{"skeleton": {"hash": "axCfTxdqPYE", "spine": "4.1.24", "images": "./images/", "audio": "C:/Users/<USER>/Desktop/咩咩启示录（Cult of the Lamb）/Floater"}, "bones": [{"name": "root"}, {"name": "Main", "parent": "root", "x": -0.37, "y": 1.23}, {"name": "Body", "parent": "Main", "length": 46.47, "rotation": 91.19, "x": 0.97, "y": 122.94, "color": "ff0000ff"}, {"name": "BodyTop", "parent": "Body", "length": 40.68, "rotation": -179.15, "x": -10.98, "y": 0.24}, {"name": "BodyBtm", "parent": "BodyTop", "length": 40.68, "x": 44.33, "y": 0.02}, {"name": "Head", "parent": "Body", "x": 0.1, "y": -0.57, "color": "ff0000ff"}, {"name": "Eye1", "parent": "Head", "rotation": 180, "x": 45.51, "y": -1.44}, {"name": "DangleHandle", "parent": "Body", "x": -59.61, "y": 1.62, "color": "28ff00ff"}, {"name": "AntlerRight", "parent": "Head", "length": 55.59, "rotation": -50, "x": 90.12, "y": -74.78, "color": "ff0000ff"}, {"name": "AntlerLeft", "parent": "Head", "length": 68.96, "rotation": 50, "x": 91.87, "y": 65.89, "color": "ff0000ff"}, {"name": "GruntEyeOffset", "parent": "Body", "x": 20.83, "y": -2.46, "skin": true, "color": "ff0000ff"}, {"name": "EyeExploder", "parent": "Head", "rotation": 180, "x": 47.87, "y": -0.58, "skin": true}, {"name": "Eyeball1", "parent": "Eye1"}, {"name": "Eye2", "parent": "Head", "rotation": 180, "x": 80.04, "y": -33.08, "scaleX": 0.5678, "scaleY": 0.5678, "skin": true}, {"name": "Eyeball2", "parent": "Eye2", "skin": true}, {"name": "Eye3", "parent": "Head", "rotation": 180, "x": 81.39, "y": 31.93, "scaleX": 0.5903, "scaleY": 0.5903, "skin": true}, {"name": "Eyeball3", "parent": "Eye3", "skin": true}, {"name": "EyeExploder2", "parent": "Head", "rotation": 180, "x": 42.36, "y": -55.44, "skin": true}, {"name": "Spike1", "parent": "Body", "length": 88.12, "rotation": 0.38, "x": 167.94, "y": 0.14, "skin": true}, {"name": "Spike2", "parent": "Body", "length": 88.12, "rotation": -40.03, "x": 132.68, "y": -65.45, "skin": true}, {"name": "Spike3", "parent": "Body", "length": 88.12, "rotation": 36.71, "x": 136.88, "y": 66.14, "skin": true}, {"name": "Spike4", "parent": "Body", "length": 88.12, "rotation": 114.83, "x": 51.12, "y": 86.04, "skin": true}, {"name": "Spike5", "parent": "Body", "length": 88.12, "rotation": -121.07, "x": 45.2, "y": -82.02, "skin": true}, {"name": "Spike6", "parent": "Body", "length": 88.12, "rotation": -91.6, "x": 95.97, "y": -61.78, "skin": true}, {"name": "Spike7", "parent": "Body", "length": 88.12, "rotation": 89.07, "x": 101.67, "y": 72.69, "skin": true}, {"name": "Spike8", "parent": "Body", "length": 88.12, "rotation": -23.82, "x": 131.13, "y": -23.78, "skin": true}, {"name": "Spike9", "parent": "Body", "length": 88.12, "rotation": 19.94, "x": 130.24, "y": 26.59, "skin": true}, {"name": "Spike10", "parent": "Body", "length": 88.12, "rotation": 142.46, "x": 39.14, "y": 45.43, "skin": true}, {"name": "Spike11", "parent": "Body", "length": 88.12, "rotation": -140.77, "x": 37.94, "y": -35.39, "skin": true}, {"name": "HornLeft", "parent": "Head", "length": 81.86, "rotation": 32.21, "x": 130.01, "y": 65.02, "scaleX": 0.8646, "scaleY": 0.8646, "skin": true, "color": "ff0000ff"}, {"name": "HornLeft2", "parent": "Head", "length": 81.86, "rotation": -30.23, "x": 131.08, "y": -64.2, "scaleX": 0.8646, "scaleY": 0.8646, "skin": true, "color": "ff0000ff"}], "slots": [{"name": "Spike6", "bone": "Spike6", "attachment": "SwampFloaters/SpikerSpike1"}, {"name": "Spike8", "bone": "Spike8", "attachment": "SwampFloaters/SpikerSpike1"}, {"name": "Spike9", "bone": "Spike9", "attachment": "SwampFloaters/SpikerSpike1"}, {"name": "Spike7", "bone": "Spike7", "attachment": "SwampFloaters/SpikerSpike1"}, {"name": "Spike10", "bone": "Spike10", "attachment": "SwampFloaters/SpikerSpike2"}, {"name": "Spike11", "bone": "Spike11", "attachment": "SwampFloaters/SpikerSpike2"}, {"name": "BodyBtm", "bone": "BodyBtm", "attachment": "BodyBtm"}, {"name": "BodyTop", "bone": "BodyTop", "attachment": "BodyTop"}, {"name": "<PERSON><PERSON>s", "bone": "Head", "attachment": "<PERSON><PERSON>s"}, {"name": "Skirt", "bone": "Head", "attachment": "Skirt"}, {"name": "Drips2", "bone": "Head", "attachment": "<PERSON><PERSON>s"}, {"name": "Head", "bone": "Head", "attachment": "Head"}, {"name": "Eye1Back", "bone": "Eye1"}, {"name": "Eye1Back3", "bone": "Eye3"}, {"name": "Eye1Back2", "bone": "Eye2"}, {"name": "SwampFloaters/Blob2", "bone": "EyeExploder", "attachment": "Blob1"}, {"name": "SwampFloaters/Blob3", "bone": "EyeExploder2", "attachment": "Blob1"}, {"name": "Eye1", "bone": "Eyeball1", "attachment": "Eye_Closed"}, {"name": "Eye3", "bone": "Eyeball3", "attachment": "Eye_Closed"}, {"name": "Eye2", "bone": "Eyeball2", "attachment": "Eye_Closed"}, {"name": "EyeExploder", "bone": "EyeExploder", "attachment": "EyeMiddle"}, {"name": "EyeExploder2", "bone": "EyeExploder2", "attachment": "EyeMiddle"}, {"name": "Spike1", "bone": "Spike1", "attachment": "SwampFloaters/SpikerSpike1"}, {"name": "Spike2", "bone": "Spike2", "attachment": "SwampFloaters/SpikerSpike2"}, {"name": "Spike3", "bone": "Spike3", "attachment": "SwampFloaters/SpikerSpike2"}, {"name": "Spike4", "bone": "Spike4", "attachment": "SwampFloaters/SpikerSpike2"}, {"name": "Spike5", "bone": "Spike5", "attachment": "SwampFloaters/SpikerSpike1"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "bone": "Eye1", "attachment": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "SwampFloaters/SpikerHorn", "bone": "HornLeft", "attachment": "SwampFloaters/SpikerHorn"}, {"name": "SwampFloaters/SpikerHorn2", "bone": "HornLeft2", "attachment": "SwampFloaters/SpikerHorn"}], "transform": [{"name": "BigExploderOffset", "order": 2, "skin": true, "bones": ["EyeExploder"], "target": "Head", "x": 37.9, "y": 550, "mixRotate": 0, "mixX": 0.099, "mixScaleX": 0, "mixShearY": 0}, {"name": "GruntEyeOffset", "bones": ["Eye1"], "target": "GruntEyeOffset", "mixRotate": 0, "mixX": 0.403, "mixScaleX": 0, "mixShearY": 0}, {"name": "RingshotOffset", "order": 1, "skin": true, "bones": ["Eye1"], "target": "Head", "x": -47, "scaleX": -2, "mixRotate": 0, "mixX": 0.163, "mixScaleX": 0.084, "mixShearY": 0}], "skins": [{"name": "default", "attachments": {"Spike2": {"SwampFloaters/SpikerSpike2": {"x": 64.35, "y": 1.14, "rotation": -90.38, "width": 58, "height": 145}}, "Spike3": {"SwampFloaters/SpikerSpike2": {"x": 64.35, "y": 1.14, "rotation": -90.38, "width": 58, "height": 145}}, "Spike6": {"SwampFloaters/SpikerSpike1": {"x": 64.35, "y": 1.14, "rotation": -90.38, "width": 62, "height": 147}}, "Spike7": {"SwampFloaters/SpikerSpike1": {"x": 64.35, "y": 1.14, "rotation": -90.38, "width": 62, "height": 147}}, "Spike8": {"SwampFloaters/SpikerSpike1": {"x": 64.35, "y": 1.14, "rotation": -90.38, "width": 62, "height": 147}}, "Spike9": {"SwampFloaters/SpikerSpike1": {"x": 64.35, "y": 1.14, "rotation": -90.38, "width": 62, "height": 147}}, "Spike10": {"SwampFloaters/SpikerSpike2": {"x": 64.35, "y": 1.14, "rotation": -90.38, "width": 58, "height": 145}}, "Spike11": {"SwampFloaters/SpikerSpike2": {"x": 64.35, "y": 1.14, "rotation": -90.38, "width": 58, "height": 145}}, "SpikerEyes": {"SwampFloaters/SpikerEyes": {"x": -31.21, "y": -4.33, "scaleX": 0.9786, "scaleY": 0.9786, "rotation": 90, "width": 181, "height": 104}}}}, {"name": "<PERSON><PERSON><PERSON>", "bones": ["EyeExploder", "EyeExploder2"], "transform": ["BigExploderOffset"], "attachments": {"BodyBtm": {"BodyBtm": {"name": "SwampFloaters/Tail_BackgroundJelly_Btm", "type": "<PERSON><PERSON><PERSON>", "skin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parent": "BodyBtm", "width": 54, "height": 62}}, "BodyTop": {"BodyTop": {"name": "SwampFloaters/<PERSON>l_<PERSON><PERSON><PERSON>_Top", "type": "<PERSON><PERSON><PERSON>", "skin": "ScuttleBigExploder", "parent": "BodyTop", "width": 114, "height": 78}}, "Head": {"Head": {"name": "SwampFloaters/BackgroundJellyHead", "type": "<PERSON><PERSON><PERSON>", "skin": "ScuttleBigExploder", "parent": "Head", "width": 264, "height": 174}, "Head2": {"name": "SwampFloaters/ExploderBigHead", "type": "<PERSON><PERSON><PERSON>", "skin": "ScuttleBigExploder", "parent": "Head2", "width": 266, "height": 190}}, "Skirt": {"Skirt": {"name": "SwampFloaters/BackgroundJellySkirt", "type": "<PERSON><PERSON><PERSON>", "skin": "ScuttleBigExploder", "parent": "Skirt", "width": 179, "height": 55}}, "Spike1": {"SwampFloaters/SpikerSpike1": {"x": 64.35, "y": 1.14, "rotation": -90.38, "width": 62, "height": 147}}, "Spike4": {"SwampFloaters/SpikerSpike2": {"x": 64.35, "y": 1.14, "rotation": -90.38, "width": 58, "height": 145}}, "Spike5": {"SwampFloaters/SpikerSpike1": {"x": 64.35, "y": 1.14, "rotation": -90.38, "width": 62, "height": 147}}}}, {"name": "<PERSON><PERSON><PERSON>_<PERSON>", "bones": ["EyeExploder"], "attachments": {"BodyTop": {"BodyTop": {"name": "SwampFloaters/Tail_Background<PERSON>elly_Top_Medium", "type": "mesh", "uvs": [0.9688, 0.15368, 0.97804, 0.41481, 0.98574, 0.63299, 1, 0.79276, 1, 0.90958, 1, 1, 0.83172, 1, 0.75009, 0.91302, 0.73622, 0.79276, 0.70388, 0.9405, 0.51751, 0.92848, 0.49595, 0.82196, 0.43896, 0.92332, 0.29418, 0.91645, 0.25105, 0.80478, 0.23719, 0.89068, 0.17712, 1, 0.04159, 0.98969, 0, 0.90099, 1e-05, 0.79276, 0, 0.67422, 0, 0.45948, 0, 0.20522, 0, 0, 0.22949, 0, 0.51597, 0, 0.79321, 0, 0.25105, 0.65876, 0.24643, 0.46807, 0.06315, 0.09699, 0.49595, 0.64673, 0.50057, 0.44573, 0.74701, 0.63127, 0.75779, 0.42168], "triangles": [18, 19, 15, 15, 16, 18, 4, 7, 3, 9, 10, 11, 6, 7, 4, 12, 13, 14, 3, 7, 8, 8, 9, 11, 11, 12, 14, 6, 4, 5, 16, 17, 18, 29, 23, 24, 22, 23, 29, 33, 25, 26, 33, 26, 0, 33, 0, 1, 31, 24, 25, 31, 25, 33, 28, 21, 22, 28, 24, 31, 29, 24, 28, 28, 22, 29, 32, 31, 33, 30, 28, 31, 30, 31, 32, 33, 1, 2, 8, 30, 32, 11, 30, 8, 11, 14, 30, 2, 3, 8, 32, 2, 8, 2, 32, 33, 27, 28, 30, 20, 21, 28, 20, 28, 27, 14, 19, 20, 27, 14, 20, 30, 14, 27, 15, 19, 14], "vertices": [1, 3, 4.4, 42.52, 1, 1, 3, 24.78, 42.6, 1, 3, 3, 41.81, 42.66, 0.65056, 4, 1.78, 42.64, 0.20544, 7, 7.46, -44.65, 0.144, 3, 3, 54.31, 43.45, 0.39878, 4, 14.28, 43.43, 0.34522, 7, -5.03, -45.63, 0.256, 3, 3, 63.42, 43.13, 0.2448, 4, 23.38, 43.11, 0.3552, 7, -14.14, -45.44, 0.4, 3, 3, 70.46, 42.88, 0.1776, 4, 30.43, 42.85, 0.4224, 7, -21.19, -45.29, 0.4, 3, 3, 69.94, 28.25, 0.1776, 4, 29.91, 28.22, 0.4224, 7, -20.88, -30.65, 0.4, 3, 3, 62.91, 21.39, 0.2448, 4, 22.88, 21.37, 0.3552, 7, -13.95, -23.7, 0.4, 3, 3, 53.49, 20.52, 0.39878, 4, 13.46, 20.5, 0.34522, 7, -4.55, -22.69, 0.256, 3, 3, 64.91, 17.3, 0.2448, 4, 24.87, 17.27, 0.3552, 7, -16.01, -19.63, 0.4, 3, 3, 63.39, 1.13, 0.2448, 4, 23.36, 1.1, 0.3552, 7, -14.74, -3.44, 0.4, 3, 3, 55.02, -0.45, 0.39878, 4, 14.99, -0.48, 0.34522, 7, -6.39, -1.74, 0.256, 3, 3, 62.75, -5.69, 0.2448, 4, 22.71, -5.71, 0.3552, 7, -14.19, 3.38, 0.4, 3, 3, 61.76, -18.26, 0.2448, 4, 21.73, -18.28, 0.3552, 7, -13.39, 15.97, 0.4, 3, 3, 52.92, -21.7, 0.39878, 4, 12.89, -21.72, 0.34522, 7, -4.61, 19.53, 0.256, 3, 3, 59.57, -23.14, 0.3216, 4, 19.54, -23.16, 0.2784, 7, -11.28, 20.88, 0.4, 3, 3, 67.91, -28.67, 0.1776, 4, 27.88, -28.69, 0.4224, 7, -19.7, 26.28, 0.4, 3, 3, 66.68, -40.42, 0.1776, 4, 26.65, -40.45, 0.4224, 7, -18.65, 38.05, 0.4, 3, 3, 59.64, -43.79, 0.2448, 4, 19.61, -43.82, 0.3552, 7, -11.66, 41.53, 0.4, 3, 3, 51.2, -43.49, 0.39878, 4, 11.17, -43.51, 0.34522, 7, -3.21, 41.35, 0.256, 3, 3, 41.96, -43.16, 0.65056, 4, 1.93, -43.18, 0.20544, 7, 6.03, 41.16, 0.144, 1, 3, 25.23, -42.56, 1, 1, 3, 5.41, -41.86, 1, 1, 3, -10.59, -41.28, 1, 1, 3, -9.88, -21.33, 1, 1, 3, -8.99, 3.58, 1, 1, 3, -8.13, 27.68, 1, 3, 3, 41.54, -21.29, 0.65056, 4, 1.51, -21.31, 0.20544, 7, 6.78, 19.3, 0.144, 1, 3, 26.66, -21.16, 1, 1, 3, -2.83, -36.06, 1, 3, 3, 41.36, 0.04, 0.65056, 4, 1.33, 0.01, 0.20544, 7, 7.27, -2.02, 0.144, 1, 3, 25.71, 1, 1, 3, 3, 40.94, 21.91, 0.65056, 4, 0.9, 21.88, 0.20544, 7, 8.02, -23.89, 0.144, 1, 3, 24.63, 23.43, 1], "hull": 27, "edges": [32, 34, 32, 30, 30, 28, 28, 54, 54, 56, 38, 40, 40, 42, 42, 44, 44, 46, 44, 58, 46, 48, 58, 48, 28, 26, 26, 24, 24, 22, 22, 60, 60, 62, 22, 20, 20, 18, 18, 16, 16, 64, 64, 66, 16, 14, 10, 12, 14, 12, 10, 8, 8, 6, 6, 4, 4, 2, 2, 0, 0, 52, 48, 50, 50, 52, 62, 50, 34, 36, 36, 38], "width": 87, "height": 78}}, "EyeExploder2": {"EyeMiddle": {"name": "SwampFloaters/ExploderEye", "x": 0.5, "y": 1.62, "rotation": 90, "width": 71, "height": 61}, "EyeMiddle_Closed": {"name": "SwampFloaters/ExploderEye", "x": 0.5, "y": 1.62, "rotation": 90, "width": 71, "height": 61}}, "Head": {"Head2": {"name": "SwampFloaters/BackgroundJellyHead_Medium", "type": "mesh", "uvs": [0.78626, 0.12195, 0.94403, 0.2374, 1, 0.47362, 0.97325, 0.55446, 1, 0.73604, 1, 0.87982, 0.9675, 0.91757, 0.89673, 0.89404, 0.88318, 0.9528, 0.78199, 0.97992, 0.71276, 0.95398, 0.65329, 1, 0.54056, 1, 0.50061, 0.95516, 0.45337, 1, 0.32999, 1, 0.29004, 0.94447, 0.25454, 0.97984, 0.12139, 0.9539, 0.10453, 0.89259, 0.04949, 0.92679, 0, 0.86076, 0, 0.73568, 0.03154, 0.52108, 0, 0.36883, 0.18992, 0, 0.69133, 0, 0.30555, 0.82901, 0.49728, 0.79599, 0.70659, 0.77694, 0.10965, 0.78991, 0.70573, 0.58922, 0.49449, 0.62058, 0.30587, 0.58977, 0.89456, 0.82885, 0.932, 0.80931, 0.91729, 0.57664, 0.08697, 0.72939, 0.10034, 0.55533], "triangles": [20, 21, 19, 17, 18, 19, 7, 9, 10, 8, 9, 7, 19, 21, 30, 6, 7, 35, 5, 6, 35, 27, 17, 19, 10, 34, 7, 27, 16, 17, 10, 11, 29, 12, 13, 28, 15, 16, 27, 27, 14, 15, 28, 13, 14, 29, 11, 12, 38, 23, 24, 2, 36, 1, 1, 31, 0, 3, 36, 2, 36, 31, 1, 0, 31, 32, 38, 22, 23, 37, 38, 33, 37, 22, 38, 36, 3, 4, 29, 31, 36, 36, 34, 29, 32, 31, 29, 30, 37, 33, 28, 32, 29, 4, 35, 36, 35, 34, 36, 27, 30, 33, 27, 33, 32, 27, 32, 28, 30, 21, 22, 30, 22, 37, 35, 4, 5, 19, 30, 27, 7, 34, 35, 34, 10, 29, 28, 29, 12, 14, 27, 28, 26, 32, 25, 25, 38, 24, 0, 32, 26, 32, 33, 25, 38, 25, 33], "vertices": [1, 5, 96.85, -57.14, 1, 1, 5, 80.76, -86.71, 1, 1, 5, 48.89, -96.66, 1, 2, 5, 38.17, -91.37, 0.856, 3, -47.87, 92.9, 0.144, 2, 5, 13.74, -95.93, 0.68, 3, -23.37, 97.1, 0.32, 2, 5, -5.52, -95.53, 0.408, 3, -4.12, 96.41, 0.592, 2, 5, -10.45, -89.26, 0.408, 3, 0.72, 90.08, 0.592, 2, 5, -7.02, -75.92, 0.408, 3, -2.91, 76.78, 0.592, 2, 5, -14.84, -73.19, 0.408, 3, 4.86, 73.94, 0.592, 2, 5, -18.07, -53.94, 0.408, 3, 7.81, 54.64, 0.592, 2, 5, -14.33, -40.89, 0.408, 3, 3.87, 41.65, 0.592, 2, 5, -20.26, -29.49, 0.408, 3, 9.63, 30.17, 0.592, 2, 5, -19.81, -8.13, 0.408, 3, 8.87, 8.81, 0.592, 2, 5, -13.65, -0.69, 0.408, 3, 2.59, 1.46, 0.592, 2, 5, -19.47, 8.39, 0.408, 3, 8.28, -7.7, 0.592, 2, 5, -18.98, 31.77, 0.408, 3, 7.44, -31.07, 0.592, 2, 5, -11.38, 39.19, 0.408, 3, -0.26, -38.37, 0.592, 2, 5, -15.98, 46.01, 0.408, 3, 4.23, -45.27, 0.592, 2, 5, -11.98, 71.17, 0.408, 3, -0.14, -70.36, 0.592, 2, 5, -3.7, 74.19, 0.408, 3, -8.47, -73.26, 0.592, 2, 5, -8.06, 84.72, 0.408, 3, -4.26, -83.85, 0.592, 2, 5, 0.98, 93.91, 0.408, 3, -13.44, -92.91, 0.592, 2, 5, 17.73, 93.56, 0.68, 3, -30.19, -92.31, 0.32, 2, 5, 46.36, 86.99, 0.856, 3, -58.71, -85.31, 0.144, 1, 5, 66.88, 92.54, 1, 2, 5, 115.54, 55.52, 0.9931, 3, -127.42, -52.82, 0.0069, 2, 5, 113.56, -39.49, 0.99999, 3, -124.03, 42.15, 1e-05, 2, 5, 4.02, 35.93, 0.68, 3, -15.62, -34.88, 0.32, 2, 5, 7.69, -0.5, 0.68, 3, -18.74, 1.59, 0.32, 2, 5, 9.42, -40.22, 0.68, 3, -19.88, 41.33, 0.32, 2, 5, 10.03, 72.94, 0.68, 3, -22.18, -71.8, 0.32, 2, 5, 34.57, -40.58, 0.856, 3, -45.02, 42.07, 0.144, 2, 5, 31.2, -0.46, 0.856, 3, -42.25, 1.9, 0.144, 2, 5, 36.07, 35.2, 0.856, 3, -47.66, -33.68, 0.144, 2, 5, 1.72, -75.69, 0.68, 3, -11.66, 76.69, 0.32, 2, 5, 4.19, -82.84, 0.68, 3, -14.02, 83.87, 0.32, 2, 5, 35.42, -80.7, 0.856, 3, -45.28, 82.2, 0.144, 2, 5, 18.23, 77.07, 0.68, 3, -30.44, -75.81, 0.32, 2, 5, 41.5, 74.05, 0.856, 3, -53.66, -72.44, 0.144], "hull": 27, "edges": [22, 20, 20, 18, 18, 16, 16, 14, 22, 24, 26, 24, 28, 26, 28, 30, 32, 30, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 12, 14, 12, 10, 32, 54, 26, 56, 20, 58, 38, 60, 42, 44, 44, 46, 10, 8, 6, 8, 58, 62, 56, 64, 54, 66, 6, 4, 48, 50, 50, 52, 52, 0, 0, 2, 2, 4, 46, 48, 14, 68, 68, 70, 70, 72, 60, 74, 74, 76], "width": 193, "height": 122}, "Head": {"name": "SwampFloaters/BackgroundJellyHead_Medium", "type": "mesh", "uvs": [0.78626, 0.12195, 0.94403, 0.2374, 1, 0.47362, 0.97325, 0.55446, 1, 0.73604, 1, 0.87982, 0.9675, 0.91757, 0.89673, 0.89404, 0.88318, 0.9528, 0.78199, 0.97992, 0.71276, 0.95398, 0.65329, 1, 0.54056, 1, 0.50061, 0.95516, 0.45337, 1, 0.32999, 1, 0.29004, 0.94447, 0.25454, 0.97984, 0.12139, 0.9539, 0.10453, 0.89259, 0.04949, 0.92679, 0, 0.86076, 0, 0.73568, 0.03154, 0.52108, 0, 0.36883, 0.18992, 0, 0.69133, 0, 0.30555, 0.82901, 0.49728, 0.79599, 0.70659, 0.77694, 0.10965, 0.78991, 0.70573, 0.58922, 0.49449, 0.62058, 0.30587, 0.58977, 0.89456, 0.82885, 0.932, 0.80931, 0.91729, 0.57664, 0.08697, 0.72939, 0.10034, 0.55533], "triangles": [20, 21, 19, 17, 18, 19, 7, 9, 10, 8, 9, 7, 19, 21, 30, 6, 7, 35, 5, 6, 35, 27, 17, 19, 10, 34, 7, 27, 16, 17, 10, 11, 29, 12, 13, 28, 15, 16, 27, 27, 14, 15, 28, 13, 14, 29, 11, 12, 38, 23, 24, 2, 36, 1, 1, 31, 0, 3, 36, 2, 36, 31, 1, 0, 31, 32, 38, 22, 23, 37, 38, 33, 37, 22, 38, 36, 3, 4, 29, 31, 36, 36, 34, 29, 32, 31, 29, 30, 37, 33, 28, 32, 29, 4, 35, 36, 35, 34, 36, 27, 30, 33, 27, 33, 32, 27, 32, 28, 30, 21, 22, 30, 22, 37, 35, 4, 5, 19, 30, 27, 7, 34, 35, 34, 10, 29, 28, 29, 12, 14, 27, 28, 26, 32, 25, 25, 38, 24, 0, 32, 26, 32, 33, 25, 38, 25, 33], "vertices": [1, 5, 96.85, -57.14, 1, 1, 5, 80.76, -86.71, 1, 1, 5, 48.89, -96.66, 1, 2, 5, 38.17, -91.37, 0.856, 3, -47.87, 92.9, 0.144, 2, 5, 13.74, -95.93, 0.68, 3, -23.37, 97.1, 0.32, 2, 5, -5.52, -95.53, 0.408, 3, -4.12, 96.41, 0.592, 2, 5, -10.45, -89.26, 0.408, 3, 0.72, 90.08, 0.592, 2, 5, -7.02, -75.92, 0.408, 3, -2.91, 76.78, 0.592, 2, 5, -14.84, -73.19, 0.408, 3, 4.86, 73.94, 0.592, 2, 5, -18.07, -53.94, 0.408, 3, 7.81, 54.64, 0.592, 2, 5, -14.33, -40.89, 0.408, 3, 3.87, 41.65, 0.592, 2, 5, -20.26, -29.49, 0.408, 3, 9.63, 30.17, 0.592, 2, 5, -19.81, -8.13, 0.408, 3, 8.87, 8.81, 0.592, 2, 5, -13.65, -0.69, 0.408, 3, 2.59, 1.46, 0.592, 2, 5, -19.47, 8.39, 0.408, 3, 8.28, -7.7, 0.592, 2, 5, -18.98, 31.77, 0.408, 3, 7.44, -31.07, 0.592, 2, 5, -11.38, 39.19, 0.408, 3, -0.26, -38.37, 0.592, 2, 5, -15.98, 46.01, 0.408, 3, 4.23, -45.27, 0.592, 2, 5, -11.98, 71.17, 0.408, 3, -0.14, -70.36, 0.592, 2, 5, -3.7, 74.19, 0.408, 3, -8.47, -73.26, 0.592, 2, 5, -8.06, 84.72, 0.408, 3, -4.26, -83.85, 0.592, 2, 5, 0.98, 93.91, 0.408, 3, -13.44, -92.91, 0.592, 2, 5, 17.73, 93.56, 0.68, 3, -30.19, -92.31, 0.32, 2, 5, 46.36, 86.99, 0.856, 3, -58.71, -85.31, 0.144, 1, 5, 66.88, 92.54, 1, 2, 5, 115.54, 55.52, 0.9931, 3, -127.42, -52.82, 0.0069, 2, 5, 113.56, -39.49, 0.99999, 3, -124.03, 42.15, 1e-05, 2, 5, 4.02, 35.93, 0.68, 3, -15.62, -34.88, 0.32, 2, 5, 7.69, -0.5, 0.68, 3, -18.74, 1.59, 0.32, 2, 5, 9.42, -40.22, 0.68, 3, -19.88, 41.33, 0.32, 2, 5, 10.03, 72.94, 0.68, 3, -22.18, -71.8, 0.32, 2, 5, 34.57, -40.58, 0.856, 3, -45.02, 42.07, 0.144, 2, 5, 31.2, -0.46, 0.856, 3, -42.25, 1.9, 0.144, 2, 5, 36.07, 35.2, 0.856, 3, -47.66, -33.68, 0.144, 2, 5, 1.72, -75.69, 0.68, 3, -11.66, 76.69, 0.32, 2, 5, 4.19, -82.84, 0.68, 3, -14.02, 83.87, 0.32, 2, 5, 35.42, -80.7, 0.856, 3, -45.28, 82.2, 0.144, 2, 5, 18.23, 77.07, 0.68, 3, -30.44, -75.81, 0.32, 2, 5, 41.5, 74.05, 0.856, 3, -53.66, -72.44, 0.144], "hull": 27, "edges": [22, 20, 20, 18, 18, 16, 16, 14, 22, 24, 26, 24, 28, 26, 28, 30, 32, 30, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 12, 14, 12, 10, 32, 54, 26, 56, 20, 58, 38, 60, 42, 44, 44, 46, 10, 8, 6, 8, 58, 62, 56, 64, 54, 66, 6, 4, 48, 50, 50, 52, 52, 0, 0, 2, 2, 4, 46, 48, 14, 68, 68, 70, 70, 72, 60, 74, 74, 76], "width": 193, "height": 122}}, "Skirt": {"Skirt": {"name": "SwampFloaters/BackgroundJellySkirt_Medium", "type": "mesh", "uvs": [1, 0.34697, 1, 0.61804, 1, 0.79748, 0.95694, 1, 0.8238, 1, 0.74455, 0.81032, 0.67225, 1, 0.55788, 1, 0.49763, 0.8366, 0.4378, 1, 0.33772, 1, 0.24337, 0.79367, 0.16331, 1, 0.04026, 1, 0, 0.83144, 0.00184, 0.61228, 0.00102, 0.3409, 0, 0, 0.23866, 0, 0.49033, 0, 0.74384, 0, 1, 0, 0.24233, 0.61804, 0.49217, 0.60074, 0.74752, 0.61804, 0.74736, 0.36516, 0.24083, 0.36426, 0.49138, 0.3414], "triangles": [27, 18, 19, 27, 19, 20, 25, 20, 21, 16, 17, 18, 27, 20, 25, 0, 25, 21, 16, 18, 26, 27, 26, 18, 3, 4, 2, 1, 2, 4, 5, 6, 24, 24, 6, 7, 8, 9, 23, 23, 9, 10, 11, 12, 22, 14, 15, 13, 22, 12, 15, 12, 13, 15, 23, 10, 22, 23, 24, 7, 4, 5, 24, 23, 7, 8, 4, 24, 1, 10, 11, 22, 15, 26, 22, 22, 26, 23, 23, 25, 24, 24, 25, 1, 25, 0, 1, 15, 16, 26, 26, 27, 23, 23, 27, 25], "vertices": [2, 3, -0.82, 68.95, 0.552, 5, -9.23, -68.12, 0.448, 2, 3, 10.83, 68.54, 0.744, 5, -20.88, -67.87, 0.256, 2, 3, 18.54, 68.26, 0.888, 5, -28.6, -67.71, 0.112, 1, 3, 27.03, 62.14, 1, 1, 3, 26.39, 44.18, 1, 2, 3, 17.86, 33.78, 0.888, 5, -28.43, -33.22, 0.112, 1, 3, 25.66, 23.73, 1, 1, 3, 25.11, 8.3, 1, 1, 3, 17.8, 0.42, 1, 1, 3, 24.53, -7.9, 1, 1, 3, 24.05, -21.4, 1, 2, 3, 14.73, -33.81, 0.888, 5, -26.31, 34.41, 0.112, 1, 3, 23.21, -44.93, 1, 1, 3, 22.62, -61.53, 1, 2, 3, 15.18, -66.71, 0.888, 5, -27.25, 67.29, 0.112, 2, 3, 5.77, -66.12, 0.744, 5, -17.83, 66.84, 0.256, 2, 3, -5.89, -65.82, 0.552, 5, -6.16, 66.71, 0.448, 1, 5, 8.5, 66.54, 1, 1, 5, 7.83, 34.33, 1, 1, 5, 7.12, 0.36, 1, 1, 5, 6.41, -33.85, 1, 1, 5, 5.69, -68.43, 1, 2, 3, 7.18, -33.68, 0.744, 5, -18.75, 34.39, 0.256, 2, 3, 7.64, 0.05, 0.744, 5, -18.71, 0.65, 0.256, 2, 3, 9.61, 34.47, 0.744, 5, -20.17, -33.8, 0.256, 2, 3, -1.26, 34.84, 0.552, 5, -9.3, -34, 0.448, 2, 3, -3.74, -33.5, 0.552, 5, -7.84, 34.36, 0.448, 2, 3, -3.51, 0.34, 0.552, 5, -7.56, 0.53, 0.448], "hull": 22, "edges": [24, 22, 22, 20, 18, 20, 18, 16, 16, 14, 12, 14, 12, 10, 10, 8, 6, 8, 4, 6, 24, 26, 28, 26, 28, 30, 22, 44, 16, 46, 10, 48, 38, 40, 40, 42, 4, 2, 34, 36, 36, 38, 40, 50, 50, 48, 2, 0, 0, 42, 36, 52, 52, 44, 38, 54, 54, 46, 30, 32, 32, 34, 32, 52, 52, 54, 54, 50, 50, 0], "width": 135, "height": 43}}, "Spike1": {"SwampFloaters/SpikerSpike1": {"x": 64.35, "y": 1.14, "rotation": -90.38, "width": 62, "height": 147}}, "Spike4": {"SwampFloaters/SpikerSpike2": {"x": 64.35, "y": 1.14, "rotation": -90.38, "width": 58, "height": 145}}, "Spike5": {"SwampFloaters/SpikerSpike1": {"x": 64.35, "y": 1.14, "rotation": -90.38, "width": 62, "height": 147}}}}, {"name": "<PERSON><PERSON><PERSON>_<PERSON>", "bones": ["EyeExploder"], "attachments": {"BodyTop": {"BodyTop": {"name": "SwampFloaters/Tail_BackgroundJelly_Btm_Medium", "type": "<PERSON><PERSON><PERSON>", "skin": "ScuttleExploderBaby", "parent": "BodyTop", "width": 45, "height": 70}}, "EyeExploder2": {"EyeMiddle": {"name": "SwampFloaters/ExploderEye", "x": 0.5, "y": 1.62, "rotation": 90, "width": 71, "height": 61}, "EyeMiddle_Closed": {"name": "SwampFloaters/ExploderEye", "x": 0.5, "y": 1.62, "rotation": 90, "width": 71, "height": 61}}, "Head": {"Head2": {"name": "SwampFloaters/BackgroundJellyHead_Small", "type": "<PERSON><PERSON><PERSON>", "skin": "ScuttleExploderBaby", "parent": "Head", "width": 127, "height": 80}, "Head": {"name": "SwampFloaters/BackgroundJellyHead_Small", "type": "<PERSON><PERSON><PERSON>", "skin": "ScuttleExploderBaby", "parent": "Head", "width": 127, "height": 80}}, "Skirt": {"Skirt": {"name": "SwampFloaters/BackgroundJellySkirt_Small", "type": "<PERSON><PERSON><PERSON>", "skin": "ScuttleExploderBaby", "parent": "Skirt", "width": 102, "height": 54}}, "Spike1": {"SwampFloaters/SpikerSpike1": {"x": 64.35, "y": 1.14, "rotation": -90.38, "width": 62, "height": 147}}, "Spike4": {"SwampFloaters/SpikerSpike2": {"x": 64.35, "y": 1.14, "rotation": -90.38, "width": 58, "height": 145}}, "Spike5": {"SwampFloaters/SpikerSpike1": {"x": 64.35, "y": 1.14, "rotation": -90.38, "width": 62, "height": 147}}}}, {"name": "ScuttleBigExploder", "bones": ["EyeExploder", "EyeExploder2"], "transform": ["BigExploderOffset"], "attachments": {"BodyBtm": {"BodyBtm": {"name": "SwampFloaters/Tail_ExploderBig_Btm", "type": "<PERSON><PERSON><PERSON>", "skin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parent": "BodyBtm", "width": 54, "height": 62}}, "BodyTop": {"BodyTop": {"name": "SwampFloaters/Tail_ExploderBig_Top", "type": "mesh", "uvs": [0.9688, 0.15368, 0.97804, 0.41481, 0.98574, 0.63299, 1, 0.79276, 1, 0.90958, 1, 1, 0.83172, 1, 0.7302, 0.97656, 0.68985, 0.84872, 0.65759, 0.95369, 0.51751, 0.92848, 0.50913, 0.85009, 0.47199, 0.97454, 0.33605, 1, 0.31936, 0.82371, 0.3054, 1, 0.17712, 1, 0.04159, 0.98969, 0, 0.90099, 1e-05, 0.79276, 0, 0.67422, 0, 0.45948, 0, 0.20522, 0, 0, 0.22949, 0, 0.51597, 0, 0.79321, 0, 0.25105, 0.65876, 0.24643, 0.46807, 0.06315, 0.09699, 0.49595, 0.64673, 0.50057, 0.44573, 0.74701, 0.63127, 0.75779, 0.42168], "triangles": [12, 13, 14, 15, 16, 14, 9, 10, 11, 6, 4, 5, 4, 6, 3, 17, 18, 16, 19, 16, 18, 6, 7, 8, 12, 14, 11, 8, 9, 11, 6, 8, 32, 3, 6, 32, 14, 16, 27, 27, 16, 19, 2, 3, 32, 14, 30, 11, 11, 30, 8, 32, 33, 2, 33, 1, 2, 8, 30, 32, 14, 27, 30, 19, 20, 27, 20, 28, 27, 20, 21, 28, 27, 28, 30, 30, 31, 32, 30, 28, 31, 32, 31, 33, 28, 22, 29, 29, 24, 28, 28, 24, 31, 28, 21, 22, 31, 25, 33, 31, 24, 25, 33, 0, 1, 33, 26, 0, 33, 25, 26, 22, 23, 29, 29, 23, 24], "vertices": [1, 3, 5.8, 62.48, 1, 1, 3, 25.71, 62.96, 1, 3, 3, 42.32, 63.37, 0.65056, 4, 2.28, 63.35, 0.20544, 7, 7.26, -65.37, 0.144, 3, 3, 54.52, 64.78, 0.39878, 4, 14.49, 64.76, 0.34522, 7, -4.92, -66.96, 0.256, 3, 3, 63.41, 64.47, 0.2448, 4, 23.37, 64.44, 0.3552, 7, -13.81, -66.78, 0.4, 3, 3, 70.28, 64.23, 0.1776, 4, 30.24, 64.2, 0.4224, 7, -20.68, -66.64, 0.4, 3, 3, 69.5, 42.59, 0.1776, 4, 29.47, 42.57, 0.4224, 7, -20.23, -44.99, 0.4, 3, 3, 67.39, 29.6, 0.2448, 4, 27.35, 29.57, 0.3552, 7, -18.31, -31.97, 0.4, 3, 3, 57.56, 24.76, 0.39878, 4, 17.53, 24.73, 0.34522, 7, -8.55, -26.98, 0.256, 3, 3, 65.18, 20.32, 0.2448, 4, 25.15, 20.3, 0.3552, 7, -16.24, -22.66, 0.4, 3, 3, 62.63, 2.38, 0.2448, 4, 22.6, 2.36, 0.3552, 7, -13.96, -4.68, 0.4, 3, 3, 56.72, 1.52, 0.39878, 4, 16.69, 1.49, 0.34522, 7, -8.06, -3.73, 0.256, 3, 3, 65.99, -3.6, 0.2448, 4, 25.96, -3.62, 0.3552, 7, -17.41, 1.24, 0.4, 3, 3, 67.27, -21.15, 0.2448, 4, 27.24, -21.17, 0.3552, 7, -18.95, 18.77, 0.4, 3, 3, 53.8, -22.81, 0.39878, 4, 13.77, -22.84, 0.34522, 7, -5.5, 20.64, 0.256, 3, 3, 67.12, -25.09, 0.3216, 4, 27.09, -25.12, 0.2784, 7, -18.86, 22.72, 0.4, 3, 3, 66.5, -41.58, 0.1776, 4, 26.47, -41.6, 0.4224, 7, -18.48, 39.21, 0.4, 3, 3, 65.09, -58.97, 0.1776, 4, 25.06, -59, 0.4224, 7, -17.33, 56.63, 0.4, 3, 3, 58.16, -64.09, 0.2448, 4, 18.13, -64.11, 0.3552, 7, -10.48, 61.84, 0.4, 3, 3, 49.93, -63.79, 0.39878, 4, 9.9, -63.81, 0.34522, 7, -2.24, 61.67, 0.256, 3, 3, 40.93, -63.48, 0.65056, 4, 0.89, -63.5, 0.20544, 7, 6.76, 61.49, 0.144, 1, 3, 24.61, -62.91, 1, 1, 3, 5.27, -62.22, 1, 1, 3, -10.34, -61.67, 1, 1, 3, -9.28, -32.16, 1, 1, 3, -7.97, 4.68, 1, 1, 3, -6.7, 40.32, 1, 3, 3, 40.9, -31.16, 0.65056, 4, 0.87, -31.18, 0.20544, 7, 7.27, 29.18, 0.144, 1, 3, 26.4, -31.25, 1, 1, 3, -2.67, -53.81, 1, 3, 3, 41.11, 0.36, 0.65056, 4, 1.08, 0.34, 0.20544, 7, 7.53, -2.34, 0.144, 1, 3, 25.87, 1.48, 1, 3, 3, 41.09, 32.68, 0.65056, 4, 1.06, 32.66, 0.20544, 7, 8.03, -34.66, 0.144, 1, 3, 25.22, 34.62, 1], "hull": 27, "edges": [32, 34, 32, 30, 30, 28, 28, 54, 54, 56, 38, 40, 40, 42, 42, 44, 44, 46, 44, 58, 46, 48, 58, 48, 28, 26, 26, 24, 24, 22, 22, 60, 60, 62, 22, 20, 20, 18, 18, 16, 16, 64, 64, 66, 16, 14, 10, 12, 14, 12, 10, 8, 8, 6, 6, 4, 4, 2, 2, 0, 0, 52, 48, 50, 50, 52, 62, 50, 34, 36, 36, 38], "width": 114, "height": 78}}, "EyeExploder": {"EyeMiddle": {"name": "SwampFloaters/ExploderBigEye1", "x": 0.5, "y": 1.62, "rotation": 90, "width": 71, "height": 61}, "EyeMiddle_Closed": {"name": "SwampFloaters/ExploderBigEye1_Closed", "x": 0.5, "y": 1.62, "rotation": 90, "width": 71, "height": 61}}, "EyeExploder2": {"EyeMiddle": {"name": "SwampFloaters/ExploderBigEye2", "x": 0.5, "y": 1.62, "rotation": 90, "width": 71, "height": 61}, "EyeMiddle_Closed": {"name": "SwampFloaters/ExploderBigEye2_Closed", "x": 0.5, "y": 1.62, "rotation": 90, "width": 71, "height": 61}}, "Head": {"Head": {"name": "SwampFloaters/ExploderBigHead", "type": "mesh", "uvs": [0.81637, 0.08714, 0.90773, 0.26711, 0.98533, 0.4814, 1, 0.56066, 0.98533, 0.66647, 0.9952, 0.91529, 0.93231, 0.96734, 0.87634, 0.93485, 0.81565, 0.98583, 0.72604, 0.96045, 0.68043, 1, 0.62109, 1, 0.58323, 0.96186, 0.53345, 1, 0.47373, 0.99999, 0.41658, 0.98093, 0.35523, 0.99903, 0.28553, 0.97311, 0.18893, 1, 0.13507, 0.92112, 0.04579, 0.95159, 0, 0.85416, 0.02543, 0.72979, 0, 0.61659, 0, 0.41067, 0.20625, 0, 0.64264, 0, 0.36879, 0.87945, 0.5079, 0.85617, 0.65977, 0.84273, 0.22665, 0.85188, 0.64927, 0.56292, 0.496, 0.58504, 0.35915, 0.56331, 0.84471, 0.88251, 0.83257, 0.81151, 0.80277, 0.55405, 0.22033, 0.67449, 0.21002, 0.53902], "triangles": [10, 11, 29, 12, 13, 28, 18, 30, 17, 18, 19, 30, 9, 10, 29, 13, 14, 28, 14, 15, 28, 15, 16, 27, 8, 34, 7, 8, 9, 34, 16, 17, 27, 6, 7, 5, 29, 11, 12, 20, 21, 19, 7, 34, 5, 19, 21, 22, 15, 27, 28, 17, 30, 27, 12, 28, 29, 9, 35, 34, 9, 29, 35, 37, 30, 19, 5, 35, 4, 5, 34, 35, 27, 37, 33, 27, 32, 28, 27, 33, 32, 28, 32, 29, 19, 22, 37, 27, 30, 37, 32, 31, 29, 29, 36, 35, 29, 31, 36, 35, 36, 4, 22, 23, 37, 23, 38, 37, 37, 38, 33, 4, 36, 3, 23, 24, 38, 31, 32, 26, 26, 32, 33, 0, 36, 31, 36, 2, 3, 25, 26, 33, 36, 1, 2, 36, 0, 1, 0, 31, 26, 24, 25, 38, 33, 38, 25], "vertices": [1, 5, 147.93, -86.6, 1, 1, 5, 113.25, -109.74, 1, 1, 5, 72.12, -129.16, 1, 2, 5, 56.99, -132.68, 0.856, 3, -66.07, 134.49, 0.144, 2, 5, 36.97, -128.43, 0.68, 3, -46.11, 129.95, 0.32, 2, 5, -10.35, -130.03, 0.408, 3, 1.22, 130.84, 0.592, 2, 5, -19.9, -113.4, 0.408, 3, 10.52, 114.07, 0.592, 2, 5, -13.42, -98.91, 0.408, 3, 3.83, 99.67, 0.592, 2, 5, -22.78, -82.85, 0.408, 3, 12.94, 83.48, 0.592, 2, 5, -17.47, -59.55, 0.408, 3, 7.29, 60.26, 0.592, 2, 5, -24.73, -47.48, 0.408, 3, 14.37, 48.09, 0.592, 2, 5, -24.42, -31.99, 0.408, 3, 13.83, 32.6, 0.592, 2, 5, -16.97, -22.25, 0.408, 3, 6.23, 22.97, 0.592, 2, 5, -23.93, -9.1, 0.408, 3, 13, 9.72, 0.592, 2, 5, -23.61, 6.5, 0.408, 3, 12.45, -5.87, 0.592, 2, 5, -19.68, 21.35, 0.408, 3, 8.3, -20.66, 0.592, 2, 5, -22.78, 37.45, 0.408, 3, 11.16, -36.8, 0.592, 2, 5, -17.48, 55.55, 0.408, 3, 5.59, -54.82, 0.592, 2, 5, -22.06, 80.88, 0.408, 3, 9.79, -80.22, 0.592, 2, 5, -6.78, 94.64, 0.408, 3, -5.69, -93.75, 0.592, 2, 5, -12.08, 118.07, 0.408, 3, -0.74, -117.26, 0.592, 2, 5, 6.67, 129.65, 0.408, 3, -19.66, -128.55, 0.592, 2, 5, 30.16, 122.51, 0.68, 3, -43.04, -121.07, 0.32, 2, 5, 51.8, 128.71, 0.856, 3, -64.77, -126.94, 0.144, 1, 5, 90.92, 127.89, 1, 2, 5, 167.81, 72.4, 0.9931, 3, -179.93, -68.92, 0.0069, 2, 5, 165.43, -41.57, 0.99999, 3, -175.86, 45, 1e-05, 2, 5, -0.14, 33.43, 0.68, 3, -11.42, -32.45, 0.32, 2, 5, 3.53, -2.99, 0.68, 3, -14.55, 4.02, 0.32, 2, 5, 5.25, -42.71, 0.68, 3, -15.68, 43.76, 0.32, 2, 5, 5.87, 70.44, 0.68, 3, -17.98, -69.37, 0.32, 2, 5, 58.46, -41.07, 0.856, 3, -68.91, 42.92, 0.144, 2, 5, 55.1, -0.96, 0.856, 3, -66.14, 2.76, 0.144, 2, 5, 59.97, 34.7, 0.856, 3, -71.54, -32.82, 0.144, 2, 5, -3.31, -90.85, 0.68, 3, -6.4, 91.77, 0.32, 2, 5, 10.24, -87.96, 0.68, 3, -20, 89.08, 0.32, 2, 5, 59.31, -81.2, 0.856, 3, -69.16, 83.05, 0.144, 2, 5, 39.6, 71.39, 0.68, 3, -51.72, -69.82, 0.32, 2, 5, 65.39, 73.55, 0.856, 3, -77.54, -71.59, 0.144], "hull": 27, "edges": [22, 20, 20, 18, 18, 16, 16, 14, 22, 24, 26, 24, 28, 26, 28, 30, 32, 30, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 12, 14, 12, 10, 32, 54, 26, 56, 20, 58, 38, 60, 42, 44, 44, 46, 10, 8, 6, 8, 58, 62, 56, 64, 54, 66, 6, 4, 48, 50, 50, 52, 52, 0, 0, 2, 2, 4, 46, 48, 14, 68, 68, 70, 70, 72, 60, 74, 74, 76], "width": 266, "height": 190}, "Head2": {"name": "SwampFloaters/ExploderBigHead", "type": "mesh", "uvs": [0.81637, 0.08714, 0.90773, 0.26711, 0.98533, 0.4814, 1, 0.56066, 0.98533, 0.66647, 0.9952, 0.91529, 0.93231, 0.96734, 0.87634, 0.93485, 0.81565, 0.98583, 0.72604, 0.96045, 0.68043, 1, 0.62109, 1, 0.58323, 0.96186, 0.53345, 1, 0.47373, 0.99999, 0.41658, 0.98093, 0.35523, 0.99903, 0.28553, 0.97311, 0.18893, 1, 0.13507, 0.92112, 0.04579, 0.95159, 0, 0.85416, 0.02543, 0.72979, 0, 0.61659, 0, 0.41067, 0.20625, 0, 0.64264, 0, 0.36879, 0.87945, 0.5079, 0.85617, 0.65977, 0.84273, 0.22665, 0.85188, 0.64927, 0.56292, 0.496, 0.58504, 0.35915, 0.56331, 0.84471, 0.88251, 0.83257, 0.81151, 0.80277, 0.55405, 0.22033, 0.67449, 0.21002, 0.53902], "triangles": [10, 11, 29, 12, 13, 28, 18, 30, 17, 18, 19, 30, 9, 10, 29, 13, 14, 28, 14, 15, 28, 15, 16, 27, 8, 34, 7, 8, 9, 34, 16, 17, 27, 6, 7, 5, 29, 11, 12, 20, 21, 19, 7, 34, 5, 19, 21, 22, 15, 27, 28, 17, 30, 27, 12, 28, 29, 9, 35, 34, 9, 29, 35, 37, 30, 19, 5, 35, 4, 5, 34, 35, 27, 37, 33, 27, 32, 28, 27, 33, 32, 28, 32, 29, 19, 22, 37, 27, 30, 37, 32, 31, 29, 29, 36, 35, 29, 31, 36, 35, 36, 4, 22, 23, 37, 23, 38, 37, 37, 38, 33, 4, 36, 3, 23, 24, 38, 31, 32, 26, 26, 32, 33, 0, 36, 31, 36, 2, 3, 25, 26, 33, 36, 1, 2, 36, 0, 1, 0, 31, 26, 24, 25, 38, 33, 38, 25], "vertices": [1, 5, 147.93, -86.6, 1, 1, 5, 113.25, -109.74, 1, 1, 5, 72.12, -129.16, 1, 2, 5, 56.99, -132.68, 0.856, 3, -66.07, 134.49, 0.144, 2, 5, 36.97, -128.43, 0.68, 3, -46.11, 129.95, 0.32, 2, 5, -10.35, -130.03, 0.408, 3, 1.22, 130.84, 0.592, 2, 5, -19.9, -113.4, 0.408, 3, 10.52, 114.07, 0.592, 2, 5, -13.42, -98.91, 0.408, 3, 3.83, 99.67, 0.592, 2, 5, -22.78, -82.85, 0.408, 3, 12.94, 83.48, 0.592, 2, 5, -17.47, -59.55, 0.408, 3, 7.29, 60.26, 0.592, 2, 5, -24.73, -47.48, 0.408, 3, 14.37, 48.09, 0.592, 2, 5, -24.42, -31.99, 0.408, 3, 13.83, 32.6, 0.592, 2, 5, -16.97, -22.25, 0.408, 3, 6.23, 22.97, 0.592, 2, 5, -23.93, -9.1, 0.408, 3, 13, 9.72, 0.592, 2, 5, -23.61, 6.5, 0.408, 3, 12.45, -5.87, 0.592, 2, 5, -19.68, 21.35, 0.408, 3, 8.3, -20.66, 0.592, 2, 5, -22.78, 37.45, 0.408, 3, 11.16, -36.8, 0.592, 2, 5, -17.48, 55.55, 0.408, 3, 5.59, -54.82, 0.592, 2, 5, -22.06, 80.88, 0.408, 3, 9.79, -80.22, 0.592, 2, 5, -6.78, 94.64, 0.408, 3, -5.69, -93.75, 0.592, 2, 5, -12.08, 118.07, 0.408, 3, -0.74, -117.26, 0.592, 2, 5, 6.67, 129.65, 0.408, 3, -19.66, -128.55, 0.592, 2, 5, 30.16, 122.51, 0.68, 3, -43.04, -121.07, 0.32, 2, 5, 51.8, 128.71, 0.856, 3, -64.77, -126.94, 0.144, 1, 5, 90.92, 127.89, 1, 2, 5, 167.81, 72.4, 0.9931, 3, -179.93, -68.92, 0.0069, 2, 5, 165.43, -41.57, 0.99999, 3, -175.86, 45, 1e-05, 2, 5, -0.14, 33.43, 0.68, 3, -11.42, -32.45, 0.32, 2, 5, 3.53, -2.99, 0.68, 3, -14.55, 4.02, 0.32, 2, 5, 5.25, -42.71, 0.68, 3, -15.68, 43.76, 0.32, 2, 5, 5.87, 70.44, 0.68, 3, -17.98, -69.37, 0.32, 2, 5, 58.46, -41.07, 0.856, 3, -68.91, 42.92, 0.144, 2, 5, 55.1, -0.96, 0.856, 3, -66.14, 2.76, 0.144, 2, 5, 59.97, 34.7, 0.856, 3, -71.54, -32.82, 0.144, 2, 5, -3.31, -90.85, 0.68, 3, -6.4, 91.77, 0.32, 2, 5, 10.24, -87.96, 0.68, 3, -20, 89.08, 0.32, 2, 5, 59.31, -81.2, 0.856, 3, -69.16, 83.05, 0.144, 2, 5, 39.6, 71.39, 0.68, 3, -51.72, -69.82, 0.32, 2, 5, 65.39, 73.55, 0.856, 3, -77.54, -71.59, 0.144], "hull": 27, "edges": [22, 20, 20, 18, 18, 16, 16, 14, 22, 24, 26, 24, 28, 26, 28, 30, 32, 30, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 12, 14, 12, 10, 32, 54, 26, 56, 20, 58, 38, 60, 42, 44, 44, 46, 10, 8, 6, 8, 58, 62, 56, 64, 54, 66, 6, 4, 48, 50, 50, 52, 52, 0, 0, 2, 2, 4, 46, 48, 14, 68, 68, 70, 70, 72, 60, 74, 74, 76], "width": 266, "height": 190}}, "Skirt": {"Skirt": {"name": "SwampFloaters/ExploderBigSkirt", "type": "mesh", "uvs": [0.99726, 0.38036, 0.99726, 0.59229, 0.99726, 0.73258, 0.98348, 1, 0.8617, 1, 0.80461, 0.82953, 0.75542, 1, 0.61569, 0.99967, 0.50089, 1, 0.39968, 1, 0.23608, 1, 0.1756, 0.84258, 0.11532, 0.99952, 0.01975, 1, 0, 0.75913, 0, 0.57909, 0, 0.34085, 0.00808, 0.10909, 0.21211, 0.10909, 0.48737, 0, 0.79873, 0.10909, 0.99726, 0.10909, 0.21488, 0.59229, 0.4941, 0.57876, 0.8015, 0.59229, 0.80139, 0.39458, 0.21374, 0.39388, 0.4935, 0.376], "triangles": [27, 20, 25, 25, 21, 0, 25, 20, 21, 16, 17, 26, 17, 18, 26, 26, 18, 27, 18, 19, 27, 27, 19, 20, 3, 4, 2, 5, 6, 24, 7, 8, 23, 8, 9, 23, 23, 9, 22, 24, 2, 4, 13, 14, 12, 11, 22, 10, 9, 10, 22, 6, 7, 24, 7, 23, 24, 12, 14, 11, 22, 11, 15, 2, 24, 1, 24, 4, 5, 11, 14, 15, 15, 26, 22, 22, 26, 23, 23, 25, 24, 24, 25, 1, 1, 25, 0, 15, 16, 26, 26, 27, 23, 23, 27, 25], "vertices": [2, 3, -0.05, 90.45, 0.552, 5, -9.68, -89.62, 0.448, 2, 3, 11.6, 90.03, 0.744, 5, -21.33, -89.38, 0.256, 2, 3, 19.31, 89.76, 0.888, 5, -29.05, -89.22, 0.112, 1, 3, 33.92, 86.77, 1, 1, 3, 33.14, 64.98, 1, 2, 3, 23.4, 55.1, 0.888, 5, -33.66, -54.63, 0.112, 1, 3, 32.46, 45.97, 1, 1, 3, 31.55, 20.97, 1, 1, 3, 30.83, 0.44, 1, 1, 3, 30.19, -17.67, 1, 1, 3, 29.14, -46.93, 1, 2, 3, 20.1, -57.44, 0.888, 5, -32.03, 57.95, 0.112, 1, 3, 28.34, -68.53, 1, 1, 3, 27.76, -85.63, 1, 2, 3, 14.39, -88.69, 0.888, 5, -26.79, 89.28, 0.112, 2, 3, 4.5, -88.34, 0.744, 5, -16.89, 89.08, 0.256, 2, 3, -8.6, -87.87, 0.552, 5, -3.79, 88.8, 0.448, 1, 5, 8.93, 87.09, 1, 1, 5, 8.17, 50.58, 1, 1, 5, 13.14, 1.19, 1, 1, 5, 5.98, -54.4, 1, 1, 5, 5.24, -89.93, 1, 2, 3, 6.6, -49.93, 0.744, 5, -18.42, 50.64, 0.256, 2, 3, 7.64, 0.05, 0.744, 5, -18.71, 0.65, 0.256, 2, 3, 10.35, 55.01, 0.744, 5, -20.6, -54.35, 0.256, 2, 3, -0.52, 55.38, 0.552, 5, -9.73, -54.55, 0.448, 2, 3, -4.32, -49.74, 0.552, 5, -7.5, 50.61, 0.448, 2, 3, -3.51, 0.34, 0.552, 5, -7.56, 0.53, 0.448], "hull": 22, "edges": [24, 22, 22, 20, 18, 20, 18, 16, 16, 14, 12, 14, 12, 10, 10, 8, 6, 8, 4, 6, 24, 26, 28, 26, 28, 30, 22, 44, 16, 46, 10, 48, 38, 40, 40, 42, 4, 2, 34, 36, 36, 38, 40, 50, 50, 48, 2, 0, 0, 42, 36, 52, 52, 44, 38, 54, 54, 46, 30, 32, 32, 34, 32, 52, 52, 54, 54, 50, 50, 0], "width": 179, "height": 55}}, "Spike1": {"SwampFloaters/SpikerSpike1": {"x": 64.35, "y": 1.14, "rotation": -90.38, "width": 62, "height": 147}}, "Spike4": {"SwampFloaters/SpikerSpike2": {"x": 64.35, "y": 1.14, "rotation": -90.38, "width": 58, "height": 145}}, "Spike5": {"SwampFloaters/SpikerSpike1": {"x": 64.35, "y": 1.14, "rotation": -90.38, "width": 62, "height": 147}}}}, {"name": "ScuttleBigExploder_Poison", "bones": ["EyeExploder", "EyeExploder2"], "transform": ["BigExploderOffset"], "attachments": {"BodyBtm": {"BodyBtm": {"name": "SwampFloaters/Tail_ExploderBig_Btm_Poison", "type": "<PERSON><PERSON><PERSON>", "skin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parent": "BodyBtm", "width": 54, "height": 62}}, "BodyTop": {"BodyTop": {"name": "SwampFloaters/Tail_ExploderBig_Top_Poison", "type": "<PERSON><PERSON><PERSON>", "skin": "ScuttleBigExploder", "parent": "BodyTop", "width": 114, "height": 78}}, "EyeExploder": {"EyeMiddle": {"name": "SwampFloaters/ExploderBigEye1", "x": 0.5, "y": 1.62, "rotation": 90, "width": 71, "height": 61}, "EyeMiddle_Closed": {"name": "SwampFloaters/ExploderBigEye1_Closed", "x": 0.5, "y": 1.62, "rotation": 90, "width": 71, "height": 61}}, "EyeExploder2": {"EyeMiddle": {"name": "SwampFloaters/ExploderBigEye2", "x": 0.5, "y": 1.62, "rotation": 90, "width": 71, "height": 61}, "EyeMiddle_Closed": {"name": "SwampFloaters/ExploderBigEye2_Closed", "x": 0.5, "y": 1.62, "rotation": 90, "width": 71, "height": 61}}, "Head": {"Head": {"name": "SwampFloaters/ExploderBigHead_Poison", "type": "<PERSON><PERSON><PERSON>", "skin": "ScuttleBigExploder", "parent": "Head", "width": 266, "height": 190}, "Head2": {"name": "SwampFloaters/ExploderBigHead_Poison", "type": "<PERSON><PERSON><PERSON>", "skin": "ScuttleBigExploder", "parent": "Head2", "width": 266, "height": 190}}, "Skirt": {"Skirt": {"name": "SwampFloaters/ExploderBigSkirt_Poison", "type": "<PERSON><PERSON><PERSON>", "skin": "ScuttleBigExploder", "parent": "Skirt", "width": 179, "height": 55}}, "Spike1": {"SwampFloaters/SpikerSpike1": {"x": 64.35, "y": 1.14, "rotation": -90.38, "width": 62, "height": 147}}, "Spike4": {"SwampFloaters/SpikerSpike2": {"x": 64.35, "y": 1.14, "rotation": -90.38, "width": 58, "height": 145}}, "Spike5": {"SwampFloaters/SpikerSpike1": {"x": 64.35, "y": 1.14, "rotation": -90.38, "width": 62, "height": 147}}}}, {"name": "ScuttleExploder", "bones": ["EyeExploder"], "attachments": {"BodyTop": {"BodyTop": {"name": "SwampFloaters/Tail_Exploder_Top", "type": "mesh", "uvs": [0.9688, 0.15368, 0.97804, 0.41481, 0.98574, 0.63299, 1, 0.79276, 1, 0.90958, 1, 1, 0.83172, 1, 0.75009, 0.91302, 0.73622, 0.79276, 0.70388, 0.9405, 0.51751, 0.92848, 0.49595, 0.82196, 0.43896, 0.92332, 0.29418, 0.91645, 0.25105, 0.80478, 0.23719, 0.89068, 0.17712, 1, 0.04159, 0.98969, 0, 0.90099, 1e-05, 0.79276, 0, 0.67422, 0, 0.45948, 0, 0.20522, 0, 0, 0.22949, 0, 0.51597, 0, 0.79321, 0, 0.25105, 0.65876, 0.24643, 0.46807, 0.06315, 0.09699, 0.49595, 0.64673, 0.50057, 0.44573, 0.74701, 0.63127, 0.75779, 0.42168], "triangles": [18, 19, 15, 15, 16, 18, 4, 7, 3, 9, 10, 11, 6, 7, 4, 12, 13, 14, 3, 7, 8, 8, 9, 11, 11, 12, 14, 6, 4, 5, 16, 17, 18, 29, 23, 24, 22, 23, 29, 33, 25, 26, 33, 26, 0, 33, 0, 1, 31, 24, 25, 31, 25, 33, 28, 21, 22, 28, 24, 31, 29, 24, 28, 28, 22, 29, 32, 31, 33, 30, 28, 31, 30, 31, 32, 33, 1, 2, 8, 30, 32, 11, 30, 8, 11, 14, 30, 2, 3, 8, 32, 2, 8, 2, 32, 33, 27, 28, 30, 20, 21, 28, 20, 28, 27, 14, 19, 20, 27, 14, 20, 30, 14, 27, 15, 19, 14], "vertices": [1, 3, 4.4, 42.52, 1, 1, 3, 24.78, 42.6, 1, 3, 3, 41.81, 42.66, 0.65056, 4, 1.78, 42.64, 0.20544, 7, 7.46, -44.65, 0.144, 3, 3, 54.31, 43.45, 0.39878, 4, 14.28, 43.43, 0.34522, 7, -5.03, -45.63, 0.256, 3, 3, 63.42, 43.13, 0.2448, 4, 23.38, 43.11, 0.3552, 7, -14.14, -45.44, 0.4, 3, 3, 70.46, 42.88, 0.1776, 4, 30.43, 42.85, 0.4224, 7, -21.19, -45.29, 0.4, 3, 3, 69.94, 28.25, 0.1776, 4, 29.91, 28.22, 0.4224, 7, -20.88, -30.65, 0.4, 3, 3, 62.91, 21.39, 0.2448, 4, 22.88, 21.37, 0.3552, 7, -13.95, -23.7, 0.4, 3, 3, 53.49, 20.52, 0.39878, 4, 13.46, 20.5, 0.34522, 7, -4.55, -22.69, 0.256, 3, 3, 64.91, 17.3, 0.2448, 4, 24.87, 17.27, 0.3552, 7, -16.01, -19.63, 0.4, 3, 3, 63.39, 1.13, 0.2448, 4, 23.36, 1.1, 0.3552, 7, -14.74, -3.44, 0.4, 3, 3, 55.02, -0.45, 0.39878, 4, 14.99, -0.48, 0.34522, 7, -6.39, -1.74, 0.256, 3, 3, 62.75, -5.69, 0.2448, 4, 22.71, -5.71, 0.3552, 7, -14.19, 3.38, 0.4, 3, 3, 61.76, -18.26, 0.2448, 4, 21.73, -18.28, 0.3552, 7, -13.39, 15.97, 0.4, 3, 3, 52.92, -21.7, 0.39878, 4, 12.89, -21.72, 0.34522, 7, -4.61, 19.53, 0.256, 3, 3, 59.57, -23.14, 0.3216, 4, 19.54, -23.16, 0.2784, 7, -11.28, 20.88, 0.4, 3, 3, 67.91, -28.67, 0.1776, 4, 27.88, -28.69, 0.4224, 7, -19.7, 26.28, 0.4, 3, 3, 66.68, -40.42, 0.1776, 4, 26.65, -40.45, 0.4224, 7, -18.65, 38.05, 0.4, 3, 3, 59.64, -43.79, 0.2448, 4, 19.61, -43.82, 0.3552, 7, -11.66, 41.53, 0.4, 3, 3, 51.2, -43.49, 0.39878, 4, 11.17, -43.51, 0.34522, 7, -3.21, 41.35, 0.256, 3, 3, 41.96, -43.16, 0.65056, 4, 1.93, -43.18, 0.20544, 7, 6.03, 41.16, 0.144, 1, 3, 25.23, -42.56, 1, 1, 3, 5.41, -41.86, 1, 1, 3, -10.59, -41.28, 1, 1, 3, -9.88, -21.33, 1, 1, 3, -8.99, 3.58, 1, 1, 3, -8.13, 27.68, 1, 3, 3, 41.54, -21.29, 0.65056, 4, 1.51, -21.31, 0.20544, 7, 6.78, 19.3, 0.144, 1, 3, 26.66, -21.16, 1, 1, 3, -2.83, -36.06, 1, 3, 3, 41.36, 0.04, 0.65056, 4, 1.33, 0.01, 0.20544, 7, 7.27, -2.02, 0.144, 1, 3, 25.71, 1, 1, 3, 3, 40.94, 21.91, 0.65056, 4, 0.9, 21.88, 0.20544, 7, 8.02, -23.89, 0.144, 1, 3, 24.63, 23.43, 1], "hull": 27, "edges": [32, 34, 32, 30, 30, 28, 28, 54, 54, 56, 38, 40, 40, 42, 42, 44, 44, 46, 44, 58, 46, 48, 58, 48, 28, 26, 26, 24, 24, 22, 22, 60, 60, 62, 22, 20, 20, 18, 18, 16, 16, 64, 64, 66, 16, 14, 10, 12, 14, 12, 10, 8, 8, 6, 6, 4, 4, 2, 2, 0, 0, 52, 48, 50, 50, 52, 62, 50, 34, 36, 36, 38], "width": 87, "height": 78}}, "EyeExploder": {"EyeMiddle": {"name": "SwampFloaters/ExploderEye", "x": 0.5, "y": 1.62, "rotation": 90, "width": 71, "height": 61}, "EyeMiddle_Closed": {"name": "SwampFloaters/ExploderEye_Closed", "x": 0.5, "y": 1.62, "rotation": 90, "width": 71, "height": 61}}, "EyeExploder2": {"EyeMiddle": {"name": "SwampFloaters/ExploderEye", "x": 0.5, "y": 1.62, "rotation": 90, "width": 71, "height": 61}, "EyeMiddle_Closed": {"name": "SwampFloaters/ExploderEye", "x": 0.5, "y": 1.62, "rotation": 90, "width": 71, "height": 61}}, "Head": {"Head": {"name": "SwampFloaters/ExploderHead", "type": "mesh", "uvs": [0.78626, 0.12195, 0.94403, 0.2374, 1, 0.47362, 0.97325, 0.55446, 1, 0.73604, 1, 0.87982, 0.9675, 0.91757, 0.89673, 0.89404, 0.88318, 0.9528, 0.78199, 0.97992, 0.71276, 0.95398, 0.65329, 1, 0.54056, 1, 0.50061, 0.95516, 0.45337, 1, 0.32999, 1, 0.29004, 0.94447, 0.25454, 0.97984, 0.12139, 0.9539, 0.10453, 0.89259, 0.04949, 0.92679, 0, 0.86076, 0, 0.73568, 0.03154, 0.52108, 0, 0.36883, 0.18992, 0, 0.69133, 0, 0.30555, 0.82901, 0.49728, 0.79599, 0.70659, 0.77694, 0.10965, 0.78991, 0.70573, 0.58922, 0.49449, 0.62058, 0.30587, 0.58977, 0.89456, 0.82885, 0.932, 0.80931, 0.91729, 0.57664, 0.08697, 0.72939, 0.10034, 0.55533], "triangles": [20, 21, 19, 17, 18, 19, 7, 9, 10, 8, 9, 7, 19, 21, 30, 6, 7, 35, 5, 6, 35, 27, 17, 19, 10, 34, 7, 27, 16, 17, 10, 11, 29, 12, 13, 28, 15, 16, 27, 27, 14, 15, 28, 13, 14, 29, 11, 12, 38, 23, 24, 2, 36, 1, 1, 31, 0, 3, 36, 2, 36, 31, 1, 0, 31, 32, 38, 22, 23, 37, 38, 33, 37, 22, 38, 36, 3, 4, 29, 31, 36, 36, 34, 29, 32, 31, 29, 30, 37, 33, 28, 32, 29, 4, 35, 36, 35, 34, 36, 27, 30, 33, 27, 33, 32, 27, 32, 28, 30, 21, 22, 30, 22, 37, 35, 4, 5, 19, 30, 27, 7, 34, 35, 34, 10, 29, 28, 29, 12, 14, 27, 28, 26, 32, 25, 25, 38, 24, 0, 32, 26, 32, 33, 25, 38, 25, 33], "vertices": [1, 5, 96.85, -57.14, 1, 1, 5, 80.76, -86.71, 1, 1, 5, 48.89, -96.66, 1, 2, 5, 38.17, -91.37, 0.856, 3, -47.87, 92.9, 0.144, 2, 5, 13.74, -95.93, 0.68, 3, -23.37, 97.1, 0.32, 2, 5, -5.52, -95.53, 0.408, 3, -4.12, 96.41, 0.592, 2, 5, -10.45, -89.26, 0.408, 3, 0.72, 90.08, 0.592, 2, 5, -7.02, -75.92, 0.408, 3, -2.91, 76.78, 0.592, 2, 5, -14.84, -73.19, 0.408, 3, 4.86, 73.94, 0.592, 2, 5, -18.07, -53.94, 0.408, 3, 7.81, 54.64, 0.592, 2, 5, -14.33, -40.89, 0.408, 3, 3.87, 41.65, 0.592, 2, 5, -20.26, -29.49, 0.408, 3, 9.63, 30.17, 0.592, 2, 5, -19.81, -8.13, 0.408, 3, 8.87, 8.81, 0.592, 2, 5, -13.65, -0.69, 0.408, 3, 2.59, 1.46, 0.592, 2, 5, -19.47, 8.39, 0.408, 3, 8.28, -7.7, 0.592, 2, 5, -18.98, 31.77, 0.408, 3, 7.44, -31.07, 0.592, 2, 5, -11.38, 39.19, 0.408, 3, -0.26, -38.37, 0.592, 2, 5, -15.98, 46.01, 0.408, 3, 4.23, -45.27, 0.592, 2, 5, -11.98, 71.17, 0.408, 3, -0.14, -70.36, 0.592, 2, 5, -3.7, 74.19, 0.408, 3, -8.47, -73.26, 0.592, 2, 5, -8.06, 84.72, 0.408, 3, -4.26, -83.85, 0.592, 2, 5, 0.98, 93.91, 0.408, 3, -13.44, -92.91, 0.592, 2, 5, 17.73, 93.56, 0.68, 3, -30.19, -92.31, 0.32, 2, 5, 46.36, 86.99, 0.856, 3, -58.71, -85.31, 0.144, 1, 5, 66.88, 92.54, 1, 2, 5, 115.54, 55.52, 0.9931, 3, -127.42, -52.82, 0.0069, 2, 5, 113.56, -39.49, 0.99999, 3, -124.03, 42.15, 1e-05, 2, 5, 4.02, 35.93, 0.68, 3, -15.62, -34.88, 0.32, 2, 5, 7.69, -0.5, 0.68, 3, -18.74, 1.59, 0.32, 2, 5, 9.42, -40.22, 0.68, 3, -19.88, 41.33, 0.32, 2, 5, 10.03, 72.94, 0.68, 3, -22.18, -71.8, 0.32, 2, 5, 34.57, -40.58, 0.856, 3, -45.02, 42.07, 0.144, 2, 5, 31.2, -0.46, 0.856, 3, -42.25, 1.9, 0.144, 2, 5, 36.07, 35.2, 0.856, 3, -47.66, -33.68, 0.144, 2, 5, 1.72, -75.69, 0.68, 3, -11.66, 76.69, 0.32, 2, 5, 4.19, -82.84, 0.68, 3, -14.02, 83.87, 0.32, 2, 5, 35.42, -80.7, 0.856, 3, -45.28, 82.2, 0.144, 2, 5, 18.23, 77.07, 0.68, 3, -30.44, -75.81, 0.32, 2, 5, 41.5, 74.05, 0.856, 3, -53.66, -72.44, 0.144], "hull": 27, "edges": [22, 20, 20, 18, 18, 16, 16, 14, 22, 24, 26, 24, 28, 26, 28, 30, 32, 30, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 12, 14, 12, 10, 32, 54, 26, 56, 20, 58, 38, 60, 42, 44, 44, 46, 10, 8, 6, 8, 58, 62, 56, 64, 54, 66, 6, 4, 48, 50, 50, 52, 52, 0, 0, 2, 2, 4, 46, 48, 14, 68, 68, 70, 70, 72, 60, 74, 74, 76], "width": 193, "height": 134}, "Head2": {"name": "SwampFloaters/ExploderHead", "type": "mesh", "uvs": [0.78626, 0.12195, 0.94403, 0.2374, 1, 0.47362, 0.97325, 0.55446, 1, 0.73604, 1, 0.87982, 0.9675, 0.91757, 0.89673, 0.89404, 0.88318, 0.9528, 0.78199, 0.97992, 0.71276, 0.95398, 0.65329, 1, 0.54056, 1, 0.50061, 0.95516, 0.45337, 1, 0.32999, 1, 0.29004, 0.94447, 0.25454, 0.97984, 0.12139, 0.9539, 0.10453, 0.89259, 0.04949, 0.92679, 0, 0.86076, 0, 0.73568, 0.03154, 0.52108, 0, 0.36883, 0.18992, 0, 0.69133, 0, 0.30555, 0.82901, 0.49728, 0.79599, 0.70659, 0.77694, 0.10965, 0.78991, 0.70573, 0.58922, 0.49449, 0.62058, 0.30587, 0.58977, 0.89456, 0.82885, 0.932, 0.80931, 0.91729, 0.57664, 0.08697, 0.72939, 0.10034, 0.55533], "triangles": [20, 21, 19, 17, 18, 19, 7, 9, 10, 8, 9, 7, 19, 21, 30, 6, 7, 35, 5, 6, 35, 27, 17, 19, 10, 34, 7, 27, 16, 17, 10, 11, 29, 12, 13, 28, 15, 16, 27, 27, 14, 15, 28, 13, 14, 29, 11, 12, 38, 23, 24, 2, 36, 1, 1, 31, 0, 3, 36, 2, 36, 31, 1, 0, 31, 32, 38, 22, 23, 37, 38, 33, 37, 22, 38, 36, 3, 4, 29, 31, 36, 36, 34, 29, 32, 31, 29, 30, 37, 33, 28, 32, 29, 4, 35, 36, 35, 34, 36, 27, 30, 33, 27, 33, 32, 27, 32, 28, 30, 21, 22, 30, 22, 37, 35, 4, 5, 19, 30, 27, 7, 34, 35, 34, 10, 29, 28, 29, 12, 14, 27, 28, 26, 32, 25, 25, 38, 24, 0, 32, 26, 32, 33, 25, 38, 25, 33], "vertices": [1, 5, 96.85, -57.14, 1, 1, 5, 80.76, -86.71, 1, 1, 5, 48.89, -96.66, 1, 2, 5, 38.17, -91.37, 0.856, 3, -47.87, 92.9, 0.144, 2, 5, 13.74, -95.93, 0.68, 3, -23.37, 97.1, 0.32, 2, 5, -5.52, -95.53, 0.408, 3, -4.12, 96.41, 0.592, 2, 5, -10.45, -89.26, 0.408, 3, 0.72, 90.08, 0.592, 2, 5, -7.02, -75.92, 0.408, 3, -2.91, 76.78, 0.592, 2, 5, -14.84, -73.19, 0.408, 3, 4.86, 73.94, 0.592, 2, 5, -18.07, -53.94, 0.408, 3, 7.81, 54.64, 0.592, 2, 5, -14.33, -40.89, 0.408, 3, 3.87, 41.65, 0.592, 2, 5, -20.26, -29.49, 0.408, 3, 9.63, 30.17, 0.592, 2, 5, -19.81, -8.13, 0.408, 3, 8.87, 8.81, 0.592, 2, 5, -13.65, -0.69, 0.408, 3, 2.59, 1.46, 0.592, 2, 5, -19.47, 8.39, 0.408, 3, 8.28, -7.7, 0.592, 2, 5, -18.98, 31.77, 0.408, 3, 7.44, -31.07, 0.592, 2, 5, -11.38, 39.19, 0.408, 3, -0.26, -38.37, 0.592, 2, 5, -15.98, 46.01, 0.408, 3, 4.23, -45.27, 0.592, 2, 5, -11.98, 71.17, 0.408, 3, -0.14, -70.36, 0.592, 2, 5, -3.7, 74.19, 0.408, 3, -8.47, -73.26, 0.592, 2, 5, -8.06, 84.72, 0.408, 3, -4.26, -83.85, 0.592, 2, 5, 0.98, 93.91, 0.408, 3, -13.44, -92.91, 0.592, 2, 5, 17.73, 93.56, 0.68, 3, -30.19, -92.31, 0.32, 2, 5, 46.36, 86.99, 0.856, 3, -58.71, -85.31, 0.144, 1, 5, 66.88, 92.54, 1, 2, 5, 115.54, 55.52, 0.9931, 3, -127.42, -52.82, 0.0069, 2, 5, 113.56, -39.49, 0.99999, 3, -124.03, 42.15, 1e-05, 2, 5, 4.02, 35.93, 0.68, 3, -15.62, -34.88, 0.32, 2, 5, 7.69, -0.5, 0.68, 3, -18.74, 1.59, 0.32, 2, 5, 9.42, -40.22, 0.68, 3, -19.88, 41.33, 0.32, 2, 5, 10.03, 72.94, 0.68, 3, -22.18, -71.8, 0.32, 2, 5, 34.57, -40.58, 0.856, 3, -45.02, 42.07, 0.144, 2, 5, 31.2, -0.46, 0.856, 3, -42.25, 1.9, 0.144, 2, 5, 36.07, 35.2, 0.856, 3, -47.66, -33.68, 0.144, 2, 5, 1.72, -75.69, 0.68, 3, -11.66, 76.69, 0.32, 2, 5, 4.19, -82.84, 0.68, 3, -14.02, 83.87, 0.32, 2, 5, 35.42, -80.7, 0.856, 3, -45.28, 82.2, 0.144, 2, 5, 18.23, 77.07, 0.68, 3, -30.44, -75.81, 0.32, 2, 5, 41.5, 74.05, 0.856, 3, -53.66, -72.44, 0.144], "hull": 27, "edges": [22, 20, 20, 18, 18, 16, 16, 14, 22, 24, 26, 24, 28, 26, 28, 30, 32, 30, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 12, 14, 12, 10, 32, 54, 26, 56, 20, 58, 38, 60, 42, 44, 44, 46, 10, 8, 6, 8, 58, 62, 56, 64, 54, 66, 6, 4, 48, 50, 50, 52, 52, 0, 0, 2, 2, 4, 46, 48, 14, 68, 68, 70, 70, 72, 60, 74, 74, 76], "width": 193, "height": 134}}, "Skirt": {"Skirt": {"name": "SwampFloaters/ExploderSkirt", "type": "mesh", "uvs": [1, 0.34697, 1, 0.61804, 1, 0.79748, 0.95694, 1, 0.8238, 1, 0.74455, 0.81032, 0.67225, 1, 0.55788, 1, 0.49763, 0.8366, 0.4378, 1, 0.33772, 1, 0.24337, 0.79367, 0.16331, 1, 0.04026, 1, 0, 0.83144, 0.00184, 0.61228, 0.00102, 0.3409, 0, 0, 0.23866, 0, 0.49033, 0, 0.74384, 0, 1, 0, 0.24233, 0.61804, 0.49217, 0.60074, 0.74752, 0.61804, 0.74736, 0.36516, 0.24083, 0.36426, 0.49138, 0.3414], "triangles": [27, 18, 19, 27, 19, 20, 25, 20, 21, 16, 17, 18, 27, 20, 25, 0, 25, 21, 16, 18, 26, 27, 26, 18, 3, 4, 2, 1, 2, 4, 5, 6, 24, 24, 6, 7, 8, 9, 23, 23, 9, 10, 11, 12, 22, 14, 15, 13, 22, 12, 15, 12, 13, 15, 23, 10, 22, 23, 24, 7, 4, 5, 24, 23, 7, 8, 4, 24, 1, 10, 11, 22, 15, 26, 22, 22, 26, 23, 23, 25, 24, 24, 25, 1, 25, 0, 1, 15, 16, 26, 26, 27, 23, 23, 27, 25], "vertices": [2, 3, -0.82, 68.95, 0.552, 5, -9.23, -68.12, 0.448, 2, 3, 10.83, 68.54, 0.744, 5, -20.88, -67.87, 0.256, 2, 3, 18.54, 68.26, 0.888, 5, -28.6, -67.71, 0.112, 1, 3, 27.03, 62.14, 1, 1, 3, 26.39, 44.18, 1, 2, 3, 17.86, 33.78, 0.888, 5, -28.43, -33.22, 0.112, 1, 3, 25.66, 23.73, 1, 1, 3, 25.11, 8.3, 1, 1, 3, 17.8, 0.42, 1, 1, 3, 24.53, -7.9, 1, 1, 3, 24.05, -21.4, 1, 2, 3, 14.73, -33.81, 0.888, 5, -26.31, 34.41, 0.112, 1, 3, 23.21, -44.93, 1, 1, 3, 22.62, -61.53, 1, 2, 3, 15.18, -66.71, 0.888, 5, -27.25, 67.29, 0.112, 2, 3, 5.77, -66.12, 0.744, 5, -17.83, 66.84, 0.256, 2, 3, -5.89, -65.82, 0.552, 5, -6.16, 66.71, 0.448, 1, 5, 8.5, 66.54, 1, 1, 5, 7.83, 34.33, 1, 1, 5, 7.12, 0.36, 1, 1, 5, 6.41, -33.85, 1, 1, 5, 5.69, -68.43, 1, 2, 3, 7.18, -33.68, 0.744, 5, -18.75, 34.39, 0.256, 2, 3, 7.64, 0.05, 0.744, 5, -18.71, 0.65, 0.256, 2, 3, 9.61, 34.47, 0.744, 5, -20.17, -33.8, 0.256, 2, 3, -1.26, 34.84, 0.552, 5, -9.3, -34, 0.448, 2, 3, -3.74, -33.5, 0.552, 5, -7.84, 34.36, 0.448, 2, 3, -3.51, 0.34, 0.552, 5, -7.56, 0.53, 0.448], "hull": 22, "edges": [24, 22, 22, 20, 18, 20, 18, 16, 16, 14, 12, 14, 12, 10, 10, 8, 6, 8, 4, 6, 24, 26, 28, 26, 28, 30, 22, 44, 16, 46, 10, 48, 38, 40, 40, 42, 4, 2, 34, 36, 36, 38, 40, 50, 50, 48, 2, 0, 0, 42, 36, 52, 52, 44, 38, 54, 54, 46, 30, 32, 32, 34, 32, 52, 52, 54, 54, 50, 50, 0], "width": 135, "height": 43}}, "Spike1": {"SwampFloaters/SpikerSpike1": {"x": 64.35, "y": 1.14, "rotation": -90.38, "width": 62, "height": 147}}, "Spike4": {"SwampFloaters/SpikerSpike2": {"x": 64.35, "y": 1.14, "rotation": -90.38, "width": 58, "height": 145}}, "Spike5": {"SwampFloaters/SpikerSpike1": {"x": 64.35, "y": 1.14, "rotation": -90.38, "width": 62, "height": 147}}}}, {"name": "ScuttleExploderBaby", "bones": ["EyeExploder"], "attachments": {"BodyTop": {"BodyTop": {"name": "SwampFloaters/Tail_ExploderBig_Btm", "type": "mesh", "uvs": [0.91559, 0.11592, 0.96865, 0.28633, 1, 0.53249, 1, 0.68642, 0.99988, 0.79166, 1, 0.92935, 0.90027, 0.99179, 0.61289, 0.99999, 0.50681, 1, 0.39157, 1, 0.1648, 1, 0.03044, 0.93977, 0, 0.83206, 1e-05, 0.70674, 0, 0.54273, 0, 0.30754, 0.09559, 0.13137, 0.29141, 1e-05, 0.65762, 0, 0.4997, 0.8291, 0.49818, 0.71547, 0.49363, 0.53507, 0.48907, 0.2961], "triangles": [21, 2, 3, 13, 14, 21, 20, 21, 3, 13, 21, 20, 4, 20, 3, 19, 20, 4, 12, 13, 20, 12, 20, 19, 6, 19, 4, 19, 10, 12, 10, 11, 12, 7, 19, 6, 8, 19, 7, 19, 9, 10, 8, 9, 19, 5, 6, 4, 22, 17, 18, 22, 18, 0, 22, 0, 1, 16, 17, 22, 15, 16, 22, 21, 22, 1, 21, 1, 2, 15, 22, 21, 14, 15, 21], "vertices": [1, 3, 2.8, 24.3, 1, 2, 3, 14.95, 26.74, 0.744, 4, -25.08, 26.72, 0.256, 3, 3, 32.23, 27.82, 0.34714, 4, -7.8, 27.79, 0.55686, 7, 16.82, -29.67, 0.096, 3, 3, 42.99, 27.43, 0.14221, 4, 2.96, 27.41, 0.66579, 7, 6.05, -29.44, 0.192, 2, 4, 10.38, 27.14, 0.728, 7, -1.37, -29.28, 0.272, 2, 4, 19.74, 26.81, 0.568, 7, -10.73, -29.09, 0.432, 2, 4, 24.21, 21.27, 0.568, 7, -15.28, -23.61, 0.432, 2, 4, 24.2, 5.74, 0.568, 7, -15.51, -8.09, 0.432, 2, 4, 23.32, 0.01, 0.568, 7, -14.72, -2.35, 0.432, 2, 4, 23.77, -6.21, 0.568, 7, -15.26, 3.86, 0.432, 2, 4, 23.34, -18.45, 0.568, 7, -15, 16.11, 0.432, 2, 4, 18.71, -25.55, 0.568, 7, -10.48, 23.27, 0.432, 2, 4, 11.2, -26.92, 0.728, 7, -3, 24.76, 0.272, 3, 3, 42.56, -26.58, 0.14221, 4, 2.53, -26.61, 0.66579, 7, 5.68, 24.57, 0.192, 3, 3, 31.15, -26.17, 0.34714, 4, -8.89, -26.2, 0.55686, 7, 17.1, 24.33, 0.096, 2, 3, 14.55, -25.59, 0.744, 4, -25.49, -25.61, 0.256, 1, 3, 2.37, -19.99, 1, 1, 3, -6.34, -9.09, 1, 1, 3, -5.64, 10.67, 1, 2, 4, 12.02, 0.05, 0.728, 7, -3.42, -2.22, 0.272, 3, 3, 44.11, 0.28, 0.14221, 4, 4.07, 0.26, 0.66579, 7, 4.53, -2.31, 0.192, 3, 3, 31.48, 0.48, 0.34714, 4, -8.56, 0.46, 0.55686, 7, 17.16, -2.33, 0.096, 2, 3, 14.75, 0.84, 0.744, 4, -25.28, 0.81, 0.256], "hull": 19, "edges": [16, 18, 16, 14, 18, 20, 22, 20, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 0, 0, 2, 2, 4, 4, 6, 6, 8, 12, 14, 8, 10, 12, 10, 16, 38, 38, 40, 40, 42, 42, 44], "width": 54, "height": 62}}, "EyeExploder2": {"EyeMiddle": {"name": "SwampFloaters/ExploderEye", "x": 0.5, "y": 1.62, "rotation": 90, "width": 71, "height": 61}, "EyeMiddle_Closed": {"name": "SwampFloaters/ExploderEye", "x": 0.5, "y": 1.62, "rotation": 90, "width": 71, "height": 61}}, "Head": {"Head2": {"name": "SwampFloaters/BabyExploderHead", "type": "<PERSON><PERSON><PERSON>", "skin": "ScuttleExploderBaby", "parent": "Head", "width": 127, "height": 87}, "Head": {"name": "SwampFloaters/BabyExploderHead", "type": "mesh", "uvs": [0.78626, 0.12195, 0.94403, 0.2374, 1, 0.47362, 0.97325, 0.55446, 1, 0.73604, 1, 0.87982, 0.9675, 0.91757, 0.89673, 0.89404, 0.88318, 0.9528, 0.78199, 0.97992, 0.71276, 0.95398, 0.65329, 1, 0.54056, 1, 0.50061, 0.95516, 0.45337, 1, 0.32999, 1, 0.29004, 0.94447, 0.25454, 0.97984, 0.12139, 0.9539, 0.10453, 0.89259, 0.04949, 0.92679, 0, 0.86076, 0, 0.73568, 0.03154, 0.52108, 0, 0.36883, 0.18992, 0, 0.69133, 0, 0.30555, 0.82901, 0.49728, 0.79599, 0.70659, 0.77694, 0.10965, 0.78991, 0.70573, 0.58922, 0.49449, 0.62058, 0.30587, 0.58977, 0.89456, 0.82885, 0.932, 0.80931, 0.91729, 0.57664, 0.08697, 0.72939, 0.10034, 0.55533], "triangles": [20, 21, 19, 17, 18, 19, 7, 9, 10, 8, 9, 7, 19, 21, 30, 6, 7, 35, 5, 6, 35, 27, 17, 19, 10, 34, 7, 27, 16, 17, 10, 11, 29, 12, 13, 28, 15, 16, 27, 27, 14, 15, 28, 13, 14, 29, 11, 12, 38, 23, 24, 2, 36, 1, 1, 31, 0, 3, 36, 2, 36, 31, 1, 0, 31, 32, 38, 22, 23, 37, 38, 33, 37, 22, 38, 36, 3, 4, 29, 31, 36, 36, 34, 29, 32, 31, 29, 30, 37, 33, 28, 32, 29, 4, 35, 36, 35, 34, 36, 27, 30, 33, 27, 33, 32, 27, 32, 28, 30, 21, 22, 30, 22, 37, 35, 4, 5, 19, 30, 27, 7, 34, 35, 34, 10, 29, 28, 29, 12, 14, 27, 28, 26, 32, 25, 25, 38, 24, 0, 32, 26, 32, 33, 25, 38, 25, 33], "vertices": [1, 5, 52.25, -36.59, 1, 1, 5, 41.73, -56.5, 1, 1, 5, 20.94, -63.21, 1, 2, 5, 13.94, -59.65, 0.856, 3, -24.11, 60.83, 0.144, 2, 5, -2.01, -62.73, 0.68, 3, -8.12, 63.67, 0.32, 2, 5, -14.57, -62.47, 0.408, 3, 4.44, 63.22, 0.592, 2, 5, -17.79, -58.25, 0.408, 3, 7.59, 58.96, 0.592, 2, 5, -15.54, -49.27, 0.408, 3, 5.21, 50.01, 0.592, 2, 5, -20.64, -47.44, 0.408, 3, 10.29, 48.1, 0.592, 2, 5, -22.75, -34.48, 0.408, 3, 12.19, 35.12, 0.592, 2, 5, -20.29, -25.7, 0.408, 3, 9.61, 26.38, 0.592, 2, 5, -24.16, -18.03, 0.408, 3, 13.36, 18.65, 0.592, 2, 5, -23.86, -3.66, 0.408, 3, 12.85, 4.28, 0.592, 2, 5, -19.83, 1.35, 0.408, 3, 8.75, -0.67, 0.592, 2, 5, -23.63, 7.46, 0.408, 3, 12.45, -6.83, 0.592, 2, 5, -23.3, 23.19, 0.408, 3, 11.89, -22.56, 0.592, 2, 5, -18.34, 28.19, 0.408, 3, 6.86, -27.48, 0.592, 2, 5, -21.34, 32.78, 0.408, 3, 9.79, -32.11, 0.592, 2, 5, -18.72, 49.71, 0.408, 3, 6.91, -49.01, 0.592, 2, 5, -13.31, 51.75, 0.408, 3, 1.48, -50.96, 0.592, 2, 5, -16.16, 58.83, 0.408, 3, 4.22, -58.09, 0.592, 2, 5, -10.25, 65.02, 0.408, 3, -1.78, -64.19, 0.592, 2, 5, 0.68, 64.79, 0.68, 3, -12.71, -63.8, 0.32, 2, 5, 19.36, 60.38, 0.856, 3, -31.32, -59.11, 0.144, 1, 5, 32.75, 64.13, 1, 2, 5, 64.49, 39.23, 0.9931, 3, -76.13, -37.29, 0.0069, 2, 5, 63.16, -24.71, 0.99999, 3, -73.85, 26.62, 1e-05, 2, 5, -8.29, 26, 0.68, 3, -3.16, -25.14, 0.32, 2, 5, -5.91, 1.49, 0.68, 3, -5.17, -0.6, 0.32, 2, 5, -4.8, -25.24, 0.68, 3, -5.89, 26.14, 0.32, 2, 5, -4.35, 50.91, 0.68, 3, -7.47, -49.99, 0.32, 2, 5, 11.61, -25.47, 0.856, 3, -22.29, 26.62, 0.144, 2, 5, 9.43, 1.53, 0.856, 3, -20.52, -0.41, 0.144, 2, 5, 12.63, 25.52, 0.856, 3, -24.07, -24.35, 0.144, 2, 5, -9.84, -49.11, 0.68, 3, -0.49, 49.94, 0.32, 2, 5, -8.23, -53.92, 0.68, 3, -2.03, 54.77, 0.32, 2, 5, 12.15, -52.47, 0.856, 3, -22.43, 53.62, 0.144, 2, 5, 1, 53.69, 0.68, 3, -12.86, -52.69, 0.32, 2, 5, 16.18, 51.67, 0.856, 3, -28.01, -50.45, 0.144], "hull": 27, "edges": [22, 20, 20, 18, 18, 16, 16, 14, 22, 24, 26, 24, 28, 26, 28, 30, 32, 30, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 12, 14, 12, 10, 32, 54, 26, 56, 20, 58, 38, 60, 42, 44, 44, 46, 10, 8, 6, 8, 58, 62, 56, 64, 54, 66, 6, 4, 48, 50, 50, 52, 52, 0, 0, 2, 2, 4, 46, 48, 14, 68, 68, 70, 70, 72, 60, 74, 74, 76], "width": 127, "height": 87}}, "Skirt": {"Skirt": {"name": "SwampFloaters/BabyExploderSkirt", "type": "mesh", "uvs": [1, 0.34697, 1, 0.61804, 1, 0.79748, 0.95694, 1, 0.79262, 1, 0.70714, 0.81626, 0.64732, 1, 0.55788, 1, 0.50075, 1, 0.45027, 1, 0.33772, 1, 0.29013, 0.79961, 0.20383, 1, 0.04026, 1, 0, 0.83144, 0.00184, 0.61228, 0.00102, 0.3409, 0, 0, 0.28542, 0.00594, 0.49033, 0, 0.70643, 0.00594, 1, 0, 0.2891, 0.62398, 0.49217, 0.60074, 0.71011, 0.62398, 0.70995, 0.3711, 0.28759, 0.3702, 0.49138, 0.3414], "triangles": [15, 26, 22, 22, 26, 23, 23, 25, 24, 24, 25, 1, 25, 0, 1, 15, 16, 26, 26, 27, 23, 23, 27, 25, 27, 20, 25, 25, 20, 0, 16, 18, 26, 26, 18, 27, 20, 21, 0, 18, 19, 27, 27, 19, 20, 16, 17, 18, 3, 4, 2, 5, 6, 7, 23, 7, 8, 10, 11, 9, 13, 14, 12, 4, 5, 2, 8, 9, 23, 9, 11, 23, 12, 14, 11, 5, 7, 23, 11, 15, 22, 11, 14, 15, 5, 24, 2, 5, 23, 24, 11, 22, 23, 24, 1, 2], "vertices": [2, 3, -2.99, 52.51, 0.344, 5, -7.31, -51.64, 0.656, 2, 3, 11.52, 51.99, 0.536, 5, -21.82, -51.34, 0.464, 2, 3, 21.12, 51.64, 0.68, 5, -31.43, -51.14, 0.32, 2, 3, 31.8, 46.87, 0.792, 5, -42.18, -46.52, 0.208, 2, 3, 31.2, 30.12, 0.792, 5, -41.83, -29.76, 0.208, 2, 3, 21.06, 21.75, 0.68, 5, -31.81, -21.25, 0.32, 2, 3, 30.67, 15.3, 0.792, 5, -41.52, -14.94, 0.208, 2, 3, 30.35, 6.19, 0.792, 5, -41.33, -5.82, 0.208, 2, 3, 30.14, 0.36, 0.792, 5, -41.21, 0, 0.208, 2, 3, 29.96, -4.78, 0.792, 5, -41.1, 5.15, 0.208, 2, 3, 29.55, -16.26, 0.792, 5, -40.86, 16.63, 0.208, 2, 3, 18.65, -20.72, 0.68, 5, -30.03, 21.26, 0.32, 2, 3, 29.06, -29.9, 0.792, 5, -40.58, 30.28, 0.208, 2, 3, 28.46, -46.58, 0.792, 5, -40.23, 46.96, 0.208, 2, 3, 19.3, -50.36, 0.68, 5, -31.12, 50.88, 0.32, 2, 3, 7.58, -49.75, 0.536, 5, -19.39, 50.45, 0.464, 2, 3, -6.95, -49.32, 0.344, 5, -4.86, 50.23, 0.656, 1, 5, 13.39, 49.96, 1, 1, 5, 12.47, 20.85, 1, 1, 5, 12.35, -0.05, 1, 1, 5, 11.57, -22.08, 1, 1, 5, 11.27, -52.03, 1, 2, 3, 9.25, -20.49, 0.536, 5, -20.63, 21.17, 0.464, 2, 3, 8.74, 0.25, 0.536, 5, -19.82, 0.43, 0.464, 2, 3, 10.78, 22.42, 0.536, 5, -21.52, -21.77, 0.464, 2, 3, -2.75, 22.89, 0.344, 5, -7.98, -22.03, 0.656, 2, 3, -4.34, -20.16, 0.344, 5, -7.04, 21.04, 0.656, 2, 3, -5.14, 0.67, 0.344, 5, -5.93, 0.22, 0.656], "hull": 22, "edges": [24, 22, 22, 20, 18, 20, 18, 16, 16, 14, 12, 14, 12, 10, 10, 8, 6, 8, 4, 6, 24, 26, 28, 26, 28, 30, 22, 44, 16, 46, 10, 48, 38, 40, 40, 42, 4, 2, 34, 36, 36, 38, 40, 50, 50, 48, 2, 0, 0, 42, 36, 52, 52, 44, 38, 54, 54, 46, 30, 32, 32, 34, 32, 52, 52, 54, 54, 50, 50, 0], "width": 102, "height": 54}}, "Spike1": {"SwampFloaters/SpikerSpike1": {"x": 64.35, "y": 1.14, "rotation": -90.38, "width": 62, "height": 147}}, "Spike4": {"SwampFloaters/SpikerSpike2": {"x": 64.35, "y": 1.14, "rotation": -90.38, "width": 58, "height": 145}}, "Spike5": {"SwampFloaters/SpikerSpike1": {"x": 64.35, "y": 1.14, "rotation": -90.38, "width": 62, "height": 147}}}}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bones": ["GruntEyeOffset"], "attachments": {"BodyBtm": {"BodyBtm": {"name": "SwampFloaters/BodyBtm3", "type": "mesh", "uvs": [0.90157, 0.08654, 0.99094, 0.26118, 0.97974, 0.42252, 0.97365, 0.57303, 0.99999, 0.73715, 0.96903, 0.78325, 0.82989, 0.591, 0.82048, 0.70715, 0.89648, 0.94196, 0.86275, 0.96821, 0.72048, 0.87152, 0.66135, 0.72478, 0.63124, 0.85261, 0.53657, 1, 0.49335, 1, 0.47135, 0.79457, 0.42735, 0.63733, 0.37801, 0.82375, 0.27195, 0.94247, 0.22335, 0.92763, 0.22735, 0.74334, 0.22068, 0.57258, 0.17256, 0.70004, 1e-05, 0.78184, 0.01917, 0.68286, 0.03062, 0.59621, 0, 0.40816, 0, 0.25941, 0.07915, 0.08777, 0.22496, 0, 0.64013, 0, 0.76422, 0, 0.16896, 0.37396, 0.18397, 0.18017, 0.31372, 0, 0.77107, 0.07717, 0.85222, 0.20113, 0.67847, 0.43382, 0.65804, 0.23215, 0.51562, 0.05092, 0.3611, 0.41059, 0.3869, 0.22046, 0.84538, 0.41299], "triangles": [34, 29, 30, 39, 34, 30, 0, 35, 31, 30, 31, 35, 33, 28, 29, 33, 29, 34, 36, 35, 0, 41, 34, 39, 33, 34, 41, 38, 30, 35, 38, 35, 36, 39, 30, 38, 41, 39, 38, 27, 28, 33, 36, 0, 1, 32, 27, 33, 32, 33, 41, 26, 27, 32, 40, 32, 41, 42, 38, 36, 42, 36, 1, 2, 42, 1, 37, 38, 42, 40, 38, 37, 38, 40, 41, 21, 32, 40, 3, 42, 2, 6, 37, 42, 6, 42, 3, 25, 26, 32, 25, 32, 21, 16, 40, 37, 21, 40, 16, 22, 25, 21, 24, 25, 22, 6, 11, 37, 11, 16, 37, 6, 7, 11, 20, 21, 16, 23, 24, 22, 3, 5, 6, 4, 5, 3, 15, 16, 11, 17, 20, 16, 12, 15, 11, 10, 11, 7, 18, 19, 20, 10, 7, 8, 17, 18, 20, 9, 10, 8, 13, 14, 15, 12, 13, 15], "vertices": [2, 4, -33.74, 39.12, 0.00054, 3, 6.29, 39.15, 0.99946, 2, 4, -17.86, 46.26, 0.08005, 3, 22.18, 46.28, 0.91995, 2, 4, -3.47, 44.78, 0.448, 3, 36.56, 44.81, 0.552, 3, 4, 9.96, 43.78, 0.64061, 3, 50, 43.8, 0.29539, 7, -0.71, -45.91, 0.064, 2, 4, 24.71, 45.52, 0.888, 7, -15.43, -47.87, 0.112, 2, 4, 28.74, 42.71, 0.888, 7, -19.5, -45.12, 0.112, 3, 4, 11.13, 31.34, 0.77126, 3, 51.16, 31.37, 0.16474, 7, -2.06, -33.49, 0.064, 3, 4, 21.48, 30.16, 0.84538, 3, 61.51, 30.18, 0.04262, 7, -12.43, -32.47, 0.112, 2, 4, 42.7, 35.96, 0.712, 7, -33.56, -38.58, 0.288, 2, 4, 44.94, 32.97, 0.712, 7, -35.85, -35.62, 0.288, 3, 4, 35.86, 21.03, 0.73875, 3, 75.9, 21.05, 0.03725, 7, -26.94, -23.55, 0.224, 3, 4, 22.57, 16.4, 0.73171, 3, 62.6, 16.43, 0.15629, 7, -13.72, -18.73, 0.112, 3, 4, 33.9, 13.4, 0.73875, 3, 73.93, 13.43, 0.03725, 7, -25.09, -15.9, 0.224, 2, 4, 46.78, 4.78, 0.712, 7, -38.1, -7.47, 0.288, 2, 4, 46.65, 1.06, 0.712, 7, -38.03, -3.74, 0.288, 3, 4, 28.22, -0.18, 0.73875, 3, 68.25, -0.16, 0.03725, 7, -19.62, -2.23, 0.224, 3, 4, 14.03, -3.47, 0.77126, 3, 54.06, -3.44, 0.16474, 7, -5.48, 1.27, 0.064, 3, 4, 30.54, -8.31, 0.73875, 3, 70.57, -8.29, 0.03725, 7, -22.06, 5.86, 0.224, 2, 4, 40.83, -17.82, 0.712, 7, -32.48, 15.22, 0.288, 2, 4, 39.35, -21.96, 0.712, 7, -31.07, 19.38, 0.288, 3, 4, 22.89, -21.02, 0.84538, 3, 62.92, -21, 0.04262, 7, -14.6, 18.69, 0.112, 3, 4, 7.61, -21.05, 0.77126, 3, 47.64, -21.03, 0.16474, 7, 0.68, 18.95, 0.064, 3, 4, 18.85, -25.6, 0.81491, 3, 58.89, -25.58, 0.04109, 7, -10.63, 23.33, 0.144, 2, 4, 25.63, -40.72, 0.856, 7, -17.63, 38.34, 0.144, 3, 4, 16.85, -38.76, 0.89107, 3, 56.88, -38.73, 0.04493, 7, -8.82, 36.51, 0.064, 3, 4, 9.14, -37.49, 0.77126, 3, 49.17, -37.47, 0.16474, 7, -1.09, 35.36, 0.064, 2, 4, -7.77, -39.53, 0.448, 3, 32.27, -39.51, 0.552, 2, 4, -21.06, -39.05, 0.048, 3, 18.97, -39.03, 0.952, 1, 3, 3.87, -31.67, 1, 1, 3, -3.52, -18.83, 1, 1, 3, -2.25, 16.91, 1, 1, 3, -1.87, 27.6, 1, 2, 4, -10.3, -24.87, 0.448, 3, 29.73, -24.85, 0.552, 2, 4, -27.58, -22.96, 0.048, 3, 12.45, -22.94, 0.952, 1, 3, -3.25, -11.19, 1, 1, 3, 5.05, 27.94, 1, 2, 4, -23.65, 34.51, 0.05689, 3, 16.38, 34.53, 0.94311, 2, 4, -3.39, 18.81, 0.448, 3, 36.65, 18.83, 0.552, 2, 4, -21.47, 17.69, 0.04863, 3, 18.56, 17.71, 0.95137, 1, 3, 1.92, 6.03, 1, 2, 4, -6.44, -8.45, 0.45118, 3, 33.59, -8.42, 0.54882, 2, 4, -23.35, -5.62, 0.048, 3, 16.68, -5.59, 0.952, 2, 4, -4.74, 33.24, 0.50917, 3, 35.3, 33.27, 0.49083], "hull": 32, "edges": [58, 56, 56, 54, 52, 54, 52, 50, 50, 48, 46, 48, 46, 44, 44, 42, 42, 40, 40, 38, 34, 32, 32, 30, 30, 28, 38, 36, 36, 34, 26, 28, 26, 24, 24, 22, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 42, 64, 64, 66, 66, 68, 58, 60, 60, 70, 70, 72, 60, 62, 62, 0, 0, 2, 10, 8, 4, 2, 22, 74, 74, 76, 76, 78, 32, 80, 80, 82, 82, 78, 12, 84, 84, 72, 4, 6, 6, 8], "width": 111, "height": 104}}, "BodyTop": {"BodyTop": {"name": "SwampFloaters/BodyTop3", "type": "mesh", "uvs": [0.96124, 0.20858, 0.96972, 0.44049, 1, 0.57093, 1, 0.71353, 0.9041, 0.66194, 0.84319, 0.55533, 0.8635, 0.68945, 0.89508, 0.88892, 0.72644, 0.81755, 0.70487, 0.66645, 0.70106, 0.80982, 0.67399, 1, 0.55442, 0.8167, 0.52284, 0.60692, 0.51382, 0.80638, 0.49126, 1, 0.4529, 1, 0.3762, 0.85109, 0.3401, 0.68945, 0.31529, 0.86828, 0.19798, 0.91987, 0.19121, 0.7548, 0.15963, 0.55189, 0.11225, 0.72385, 0, 0.81326, 0, 0.56909, 0, 0.23894, 0.12127, 0, 0.24053, 0.04367, 0.43474, 0.09591, 0.53755, 0.0814, 0.62514, 0.05818, 0.72605, 0, 0.88979, 0, 0.15295, 0.33682, 0.20245, 0.16557, 0.32811, 0.45001, 0.54707, 0.40067, 0.72986, 0.41808, 0.86695, 0.34843, 0.85171, 0.22072, 0.68226, 0.15977, 0.54707, 0.17428, 0.38714, 0.26426, 0.80983, 0.10172], "triangles": [15, 16, 14, 20, 21, 19, 0, 40, 33, 42, 31, 41, 42, 43, 29, 44, 32, 33, 41, 31, 32, 35, 27, 28, 42, 30, 31, 43, 28, 29, 29, 30, 42, 41, 32, 44, 35, 28, 43, 40, 44, 33, 26, 27, 35, 34, 26, 35, 39, 40, 0, 37, 42, 41, 37, 43, 42, 40, 38, 41, 38, 40, 39, 37, 41, 38, 39, 0, 1, 36, 35, 43, 36, 43, 37, 34, 35, 36, 22, 34, 36, 5, 38, 39, 5, 39, 1, 25, 26, 34, 25, 34, 22, 5, 1, 2, 13, 36, 37, 4, 5, 2, 9, 37, 38, 9, 38, 5, 13, 37, 9, 9, 5, 6, 18, 36, 13, 22, 36, 18, 4, 2, 3, 23, 25, 22, 18, 21, 22, 14, 18, 13, 13, 9, 12, 24, 25, 23, 10, 12, 9, 6, 8, 9, 17, 18, 14, 19, 21, 18, 40, 41, 44, 8, 6, 7, 11, 12, 10, 16, 17, 14], "vertices": [2, 3, -5.5, 53.87, 0.552, 2, -4.67, -53.54, 0.448, 3, 3, 10.86, 54.2, 0.89107, 4, -29.18, 54.18, 0.06093, 7, 35.99, -54.55, 0.048, 3, 3, 20.13, 57.14, 0.74643, 4, -19.9, 57.12, 0.10957, 7, 26.76, -57.63, 0.144, 3, 3, 30.13, 56.81, 0.4601, 4, -9.9, 56.78, 0.2679, 7, 16.75, -57.44, 0.272, 3, 3, 26.15, 46.62, 0.58925, 4, -13.88, 46.6, 0.20275, 7, 20.58, -47.2, 0.208, 3, 3, 18.42, 40.34, 0.74592, 4, -21.61, 40.32, 0.09408, 7, 28.22, -40.8, 0.16, 3, 3, 27.96, 42.18, 0.63686, 4, -12.07, 42.16, 0.21914, 7, 18.71, -42.79, 0.144, 3, 3, 42.03, 45.12, 0.34886, 4, 1.99, 45.1, 0.20314, 7, 4.69, -45.94, 0.448, 3, 3, 36.4, 27.16, 0.50592, 4, -3.64, 27.14, 0.17408, 7, 10.05, -27.9, 0.32, 3, 3, 25.72, 25.2, 0.70534, 4, -14.31, 25.18, 0.11866, 7, 20.7, -25.78, 0.176, 3, 3, 35.77, 24.45, 0.54163, 4, -4.26, 24.43, 0.18637, 7, 10.63, -25.17, 0.272, 3, 3, 49.01, 21.09, 0.34886, 4, 8.97, 21.07, 0.20314, 7, -2.65, -22.01, 0.448, 3, 3, 35.69, 8.68, 0.54163, 4, -4.34, 8.65, 0.18637, 7, 10.48, -9.4, 0.272, 3, 3, 20.83, 5.79, 0.76013, 4, -19.2, 5.77, 0.09587, 7, 25.29, -6.3, 0.144, 3, 3, 34.81, 4.34, 0.54163, 4, -5.22, 4.32, 0.18637, 7, 11.29, -5.05, 0.272, 3, 3, 48.28, 1.47, 0.30842, 4, 8.25, 1.45, 0.17958, 7, -2.22, -2.39, 0.512, 3, 3, 48.15, -2.65, 0.32864, 4, 8.11, -2.68, 0.19136, 7, -2.14, 1.74, 0.48, 3, 3, 37.4, -10.54, 0.48211, 4, -2.63, -10.56, 0.16589, 7, 8.48, 9.79, 0.352, 3, 3, 25.95, -14.05, 0.71904, 4, -14.08, -14.07, 0.13696, 7, 19.88, 13.46, 0.144, 3, 3, 38.35, -17.11, 0.34886, 4, -1.68, -17.14, 0.20314, 7, 7.44, 16.34, 0.448, 3, 3, 41.51, -29.83, 0.30842, 4, 1.48, -29.86, 0.17958, 7, 4.09, 29.02, 0.512, 3, 3, 29.94, -30.19, 0.54163, 4, -10.09, -30.21, 0.18637, 7, 15.65, 29.54, 0.272, 3, 3, 15.56, -33.09, 0.74643, 4, -24.47, -33.11, 0.10957, 7, 29.98, 32.66, 0.144, 3, 3, 27.43, -38.58, 0.41965, 4, -12.6, -38.6, 0.24435, 7, 18.04, 37.97, 0.336, 3, 3, 33.28, -50.86, 0.38931, 4, -6.75, -50.88, 0.22669, 7, 12, 50.16, 0.384, 4, 3, 16.16, -50.28, 0.65089, 2, -27.89, 50.28, 0.10957, 4, -23.87, -50.31, 0.09554, 7, 29.13, 49.84, 0.144, 2, 3, -7.05, -49.48, 0.504, 2, -4.66, 49.82, 0.496, 1, 2, 11.91, 36.43, 1, 1, 2, 8.57, 23.68, 1, 1, 2, 4.45, 2.88, 1, 1, 2, 5.24, -8.19, 1, 1, 2, 6.68, -17.63, 1, 1, 2, 10.56, -28.57, 1, 1, 2, 10.19, -46.16, 1, 2, 3, 0.43, -33.29, 0.872, 2, -11.9, 33.52, 0.128, 2, 3, -11.45, -27.54, 0.728, 2, 0.06, 27.95, 0.272, 3, 3, 9.08, -14.76, 0.85805, 2, -20.27, 14.86, 0.12595, 7, 36.74, 14.42, 0.016, 2, 3, 6.44, 8.89, 0.872, 2, -17.29, -8.74, 0.128, 2, 3, 8.37, 28.48, 0.872, 2, -18.93, -28.36, 0.128, 2, 3, 3.99, 43.38, 0.872, 2, -14.32, -43.2, 0.128, 2, 3, -5.07, 42.07, 0.744, 2, -5.28, -41.75, 0.256, 2, 3, -10.01, 24.02, 0.728, 2, -0.61, -23.62, 0.272, 2, 3, -9.51, 9.46, 0.728, 2, -1.33, -9.07, 0.272, 2, 3, -3.78, -7.95, 0.728, 2, -7.31, 8.25, 0.272, 2, 3, -13.62, 37.87, 0.728, 2, 3.2, -37.42, 0.272], "hull": 34, "edges": [52, 54, 50, 52, 48, 50, 48, 46, 46, 44, 44, 42, 42, 40, 40, 38, 38, 36, 36, 34, 34, 32, 30, 32, 30, 28, 28, 26, 26, 24, 24, 22, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 0, 0, 66, 44, 68, 68, 70, 36, 72, 26, 74, 18, 76, 10, 78, 78, 80, 76, 82, 74, 84, 72, 86, 80, 88, 86, 58, 54, 56, 56, 58, 58, 60, 60, 84, 60, 62, 64, 66, 62, 64], "width": 125, "height": 82}}, "Eye1": {"Eye_Closed": {"name": "SwampFloaters/GruntEye", "x": 0.5, "rotation": 90, "width": 76, "height": 66}, "Eye1": {"name": "SwampFloaters/GruntEye", "x": 0.5, "rotation": 90, "width": 76, "height": 66}}, "Eye2": {"Eye_Closed": {"name": "SwampFloaters/GruntEye", "x": 0.5, "rotation": 90, "width": 76, "height": 66}, "Eye1": {"name": "SwampFloaters/GruntEye", "x": 0.5, "rotation": 90, "width": 76, "height": 66}}, "Eye3": {"Eye_Closed": {"name": "SwampFloaters/GruntEye", "x": 0.5, "rotation": 90, "width": 76, "height": 66}, "Eye1": {"name": "SwampFloaters/GruntEye", "x": 0.5, "rotation": 90, "width": 76, "height": 66}}, "EyeExploder": {"EyeMiddle_Closed": {"name": "SwampFloaters/GruntEye", "x": 0.5, "rotation": 90, "width": 76, "height": 66}, "EyeMiddle": {"name": "SwampFloaters/GruntEye", "x": 0.5, "rotation": 90, "width": 76, "height": 66}}, "EyeExploder2": {"EyeMiddle": {"name": "SwampFloaters/GruntEye", "x": 0.5, "rotation": 90, "width": 76, "height": 66}, "EyeMiddle_Closed": {"name": "SwampFloaters/GruntEye", "x": 0.5, "rotation": 90, "width": 76, "height": 66}}, "Head": {"Head": {"name": "SwampFloaters/HeadRingGrunt", "type": "mesh", "uvs": [0.86693, 0.09747, 1, 0.28849, 1, 0.72601, 0.87322, 0.84617, 0.72227, 1, 0.25998, 1, 0.06815, 0.75066, 0, 0.59352, 0, 0.34704, 0.10588, 0.13752, 0.30086, 0, 0.42351, 0, 0.53358, 0, 0.68768, 0, 0.10588, 0.54423, 0.20652, 0.31006, 0.37948, 0.22996, 0.58704, 0.22687, 0.81661, 0.37168, 0.8858, 0.59044], "triangles": [17, 12, 13, 17, 13, 0, 16, 10, 11, 9, 10, 16, 16, 11, 12, 17, 16, 12, 15, 9, 16, 8, 9, 15, 18, 17, 0, 18, 0, 1, 14, 8, 15, 19, 18, 1, 7, 8, 14, 19, 1, 2, 6, 7, 14, 18, 19, 14, 3, 19, 2, 16, 14, 15, 14, 19, 5, 6, 14, 5, 14, 16, 17, 17, 18, 14, 19, 4, 5, 3, 4, 19], "vertices": [2, 2, 118.96, -57.64, 0.856, 8, 8.7, 43.86, 0.144, 2, 2, 90.29, -76.34, 0.856, 8, 4.6, 9.87, 0.144, 2, 2, 25.72, -74.93, 0.872, 8, -37.99, -38.68, 0.128, 1, 2, 9.66, -55.69, 1, 1, 2, -12.64, -33.34, 1, 1, 2, -11.25, 33.68, 1, 1, 2, 26.23, 60.72, 1, 2, 2, 48.81, 72.58, 0.856, 9, -31.37, 22.08, 0.144, 2, 2, 85.28, 71.82, 0.856, 9, -8.51, -6.35, 0.144, 2, 2, 115.96, 55.82, 0.856, 9, -1.04, -40.13, 0.144, 2, 2, 135.04, 29.05, 0.744, 9, -9.29, -71.96, 0.256, 2, 2, 134.82, 10.83, 0.76929, 9, -23.38, -83.5, 0.23071, 2, 2, 133.89, -9.8, 0.808, 8, -18.35, 86.04, 0.192, 2, 2, 133.92, -31.95, 0.856, 8, -1.36, 71.83, 0.144, 1, 2, 56.66, 54.61, 1, 1, 2, 91, 39.3, 1, 1, 2, 102.33, 13.98, 1, 1, 2, 102.16, -16.12, 1, 1, 2, 80.04, -48.95, 1, 1, 2, 47.46, -58.31, 1], "hull": 14, "edges": [12, 10, 12, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 6, 8, 10, 6, 8, 6, 4, 34, 24, 24, 26, 26, 0, 4, 2, 0, 2, 20, 18, 18, 16, 14, 16, 12, 14, 20, 22, 22, 24, 32, 22], "width": 145, "height": 148}, "Head2": {"name": "SwampFloaters/HeadRingGrunt", "type": "mesh", "uvs": [0.86693, 0.09747, 1, 0.28849, 1, 0.72601, 0.87322, 0.84617, 0.72227, 1, 0.25998, 1, 0.06815, 0.75066, 0, 0.59352, 0, 0.34704, 0.10588, 0.13752, 0.30086, 0, 0.42351, 0, 0.53358, 0, 0.68768, 0, 0.10588, 0.54423, 0.20652, 0.31006, 0.37948, 0.22996, 0.58704, 0.22687, 0.81661, 0.37168, 0.8858, 0.59044], "triangles": [17, 12, 13, 17, 13, 0, 16, 10, 11, 9, 10, 16, 16, 11, 12, 17, 16, 12, 15, 9, 16, 8, 9, 15, 18, 17, 0, 18, 0, 1, 14, 8, 15, 19, 18, 1, 7, 8, 14, 19, 1, 2, 6, 7, 14, 18, 19, 14, 3, 19, 2, 16, 14, 15, 14, 19, 5, 6, 14, 5, 14, 16, 17, 17, 18, 14, 19, 4, 5, 3, 4, 19], "vertices": [2, 2, 118.96, -57.64, 0.856, 8, 8.7, 43.86, 0.144, 2, 2, 90.29, -76.34, 0.856, 8, 4.6, 9.87, 0.144, 2, 2, 25.72, -74.93, 0.872, 8, -37.99, -38.68, 0.128, 1, 2, 9.66, -55.69, 1, 1, 2, -12.64, -33.34, 1, 1, 2, -11.25, 33.68, 1, 1, 2, 26.23, 60.72, 1, 2, 2, 48.81, 72.58, 0.856, 9, -31.37, 22.08, 0.144, 2, 2, 85.28, 71.82, 0.856, 9, -8.51, -6.35, 0.144, 2, 2, 115.96, 55.82, 0.856, 9, -1.04, -40.13, 0.144, 2, 2, 135.04, 29.05, 0.744, 9, -9.29, -71.96, 0.256, 2, 2, 134.82, 10.83, 0.76929, 9, -23.38, -83.5, 0.23071, 2, 2, 133.89, -9.8, 0.808, 8, -18.35, 86.04, 0.192, 2, 2, 133.92, -31.95, 0.856, 8, -1.36, 71.83, 0.144, 1, 2, 56.66, 54.61, 1, 1, 2, 91, 39.3, 1, 1, 2, 102.33, 13.98, 1, 1, 2, 102.16, -16.12, 1, 1, 2, 80.04, -48.95, 1, 1, 2, 47.46, -58.31, 1], "hull": 14, "edges": [12, 10, 12, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 6, 8, 10, 6, 8, 6, 4, 34, 24, 24, 26, 26, 0, 4, 2, 0, 2, 20, 18, 18, 16, 14, 16, 12, 14, 20, 22, 22, 24, 32, 22], "width": 145, "height": 148}}, "Skirt": {"Skirt": {"name": "SwampFloaters/HeadRingGrunt", "type": "mesh", "uvs": [0.86693, 0.09747, 1, 0.28849, 1, 0.72601, 0.87322, 0.84617, 0.72227, 1, 0.25998, 1, 0.06815, 0.75066, 0, 0.59352, 0, 0.34704, 0.10588, 0.13752, 0.30086, 0, 0.42351, 0, 0.53358, 0, 0.68768, 0, 0.10588, 0.54423, 0.20652, 0.31006, 0.37948, 0.22996, 0.58704, 0.22687, 0.81661, 0.37168, 0.8858, 0.59044], "triangles": [17, 12, 13, 17, 13, 0, 16, 10, 11, 9, 10, 16, 16, 11, 12, 17, 16, 12, 15, 9, 16, 8, 9, 15, 18, 17, 0, 18, 0, 1, 14, 8, 15, 19, 18, 1, 7, 8, 14, 19, 1, 2, 6, 7, 14, 18, 19, 14, 3, 19, 2, 16, 14, 15, 14, 19, 5, 6, 14, 5, 14, 16, 17, 17, 18, 14, 19, 4, 5, 3, 4, 19], "vertices": [2, 2, 118.96, -57.64, 0.856, 8, 8.7, 43.86, 0.144, 2, 2, 90.29, -76.34, 0.856, 8, 4.6, 9.87, 0.144, 2, 2, 25.72, -74.93, 0.872, 8, -37.99, -38.68, 0.128, 1, 2, 9.66, -55.69, 1, 1, 2, -12.64, -33.34, 1, 1, 2, -11.25, 33.68, 1, 1, 2, 26.23, 60.72, 1, 2, 2, 48.81, 72.58, 0.856, 9, -31.37, 22.08, 0.144, 2, 2, 85.28, 71.82, 0.856, 9, -8.51, -6.35, 0.144, 2, 2, 115.96, 55.82, 0.856, 9, -1.04, -40.13, 0.144, 2, 2, 135.04, 29.05, 0.744, 9, -9.29, -71.96, 0.256, 2, 2, 134.82, 10.83, 0.76929, 9, -23.38, -83.5, 0.23071, 2, 2, 133.89, -9.8, 0.808, 8, -18.35, 86.04, 0.192, 2, 2, 133.92, -31.95, 0.856, 8, -1.36, 71.83, 0.144, 1, 2, 56.66, 54.61, 1, 1, 2, 91, 39.3, 1, 1, 2, 102.33, 13.98, 1, 1, 2, 102.16, -16.12, 1, 1, 2, 80.04, -48.95, 1, 1, 2, 47.46, -58.31, 1], "hull": 14, "edges": [12, 10, 12, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 6, 8, 10, 6, 8, 6, 4, 34, 24, 24, 26, 26, 0, 4, 2, 0, 2, 20, 18, 18, 16, 14, 16, 12, 14, 20, 22, 22, 24, 32, 22], "width": 145, "height": 148}}, "Spike1": {"SwampFloaters/SpikerSpike1": {"x": 64.35, "y": 1.14, "rotation": -90.38, "width": 62, "height": 147}}, "Spike4": {"SwampFloaters/SpikerSpike2": {"x": 64.35, "y": 1.14, "rotation": -90.38, "width": 58, "height": 145}}, "Spike5": {"SwampFloaters/SpikerSpike1": {"x": 64.35, "y": 1.14, "rotation": -90.38, "width": 62, "height": 147}}}}, {"name": "ScuttleRingShot", "bones": ["Eye2", "Eyeball3", "Eyeball2", "Eye3"], "transform": ["RingshotOffset"], "attachments": {"BodyBtm": {"BodyBtm": {"name": "SwampFloaters/Tail_Scuttle_Btm2", "type": "<PERSON><PERSON><PERSON>", "skin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parent": "BodyBtm", "width": 55, "height": 63}}, "BodyTop": {"BodyTop": {"name": "SwampFloaters/Tail_Scuttle_Top2", "type": "<PERSON><PERSON><PERSON>", "skin": "ScuttleExploder", "parent": "BodyTop", "width": 87, "height": 78}}, "Eye1": {"Eye1": {"name": "SwampFloaters/Eye", "x": 0.5, "rotation": 90, "width": 67, "height": 52}, "Eye_Closed": {"name": "SwampFloaters/Scuttle_Eye_Closed2", "x": 0.5, "rotation": 90, "width": 77, "height": 63}}, "Eye1Back": {"Eye1Back": {"name": "SwampFloaters/Scuttle_Eye_Back2", "y": 2, "rotation": 90, "width": 122, "height": 90}}, "Eye1Back2": {"Eye1Back": {"name": "SwampFloaters/Scuttle_Eye_Back2", "y": 2, "rotation": 90, "width": 122, "height": 90}}, "Eye1Back3": {"Eye1Back": {"name": "SwampFloaters/Scuttle_Eye_Back2", "y": 2, "rotation": 90, "width": 122, "height": 90}}, "Eye2": {"Eye1": {"name": "SwampFloaters/Eye", "x": 0.5, "rotation": 90, "width": 67, "height": 52}, "Eye_Closed": {"name": "SwampFloaters/Scuttle_Eye_Closed2", "x": 0.5, "rotation": 90, "width": 77, "height": 63}}, "Eye3": {"Eye1": {"name": "SwampFloaters/Eye", "x": 0.5, "rotation": 90, "width": 67, "height": 52}, "Eye_Closed": {"name": "SwampFloaters/Scuttle_Eye_Closed2", "x": 0.5, "rotation": 90, "width": 77, "height": 63}}, "EyeExploder": {"EyeMiddle_Closed": {"name": "SwampFloaters/Eye", "x": 0.5, "rotation": 90, "width": 67, "height": 52}, "EyeMiddle": {"name": "SwampFloaters/Eye", "x": 0.5, "rotation": 90, "width": 67, "height": 52}}, "EyeExploder2": {"EyeMiddle": {"name": "SwampFloaters/Eye", "x": 0.5, "rotation": 90, "width": 67, "height": 52}, "EyeMiddle_Closed": {"name": "SwampFloaters/Eye", "x": 0.5, "rotation": 90, "width": 67, "height": 52}}, "Head": {"Head": {"name": "SwampFloaters/RingshotTurretHead", "type": "mesh", "uvs": [1, 0.35044, 0.89056, 0.35469, 0.78834, 0.51771, 0.82967, 0.65382, 0.83497, 0.49676, 0.95523, 0.397, 0.96128, 0.43945, 0.91665, 0.60713, 0.91741, 0.75251, 0.83985, 0.78848, 0.8146, 0.84408, 0.81128, 0.95273, 0.7471, 0.96122, 0.7403, 0.93681, 0.72087, 0.98004, 0.65483, 1, 0.62609, 0.95485, 0.60642, 1, 0.5202, 1, 0.49977, 0.97395, 0.48389, 1, 0.41754, 1, 0.38509, 0.95808, 0.36816, 1, 0.30325, 1, 0.26939, 0.93828, 0.25105, 0.96599, 0.19461, 0.9363, 0.17768, 0.80961, 0.09972, 0.76233, 0.085, 0.55034, 0.04224, 0.43441, 0.05964, 0.39833, 0.1625, 0.48323, 0.17536, 0.66045, 0.18519, 0.67531, 0.18292, 0.59359, 0.21242, 0.48782, 0.12563, 0.35903, 0, 0.35478, 0, 0, 0.34637, 0, 0.65912, 0, 1, 0, 0.73047, 0.3698, 0.77705, 0.30725, 0.22528, 0.31696, 0.27776, 0.37339, 0.49842, 0.79839, 0.26383, 0.78958, 0.62514, 0.79252, 0.7372, 0.83072, 0.49842, 0.66468, 0.24917, 0.6779, 0.73824, 0.64852, 0.62514, 0.65292, 0.37694, 0.78811, 0.36542, 0.66762, 0.40042, 0.28059, 0.40677, 0.17963, 0.59301, 0.18854, 0.61205, 0.28356], "triangles": [15, 16, 14, 17, 18, 16, 18, 19, 16, 20, 21, 19, 21, 22, 19, 23, 24, 22, 24, 25, 22, 14, 16, 13, 26, 27, 25, 12, 13, 11, 22, 48, 19, 19, 48, 16, 25, 56, 22, 22, 56, 48, 48, 50, 16, 16, 51, 13, 16, 50, 51, 11, 13, 10, 27, 49, 25, 25, 49, 56, 13, 51, 10, 27, 28, 49, 10, 51, 9, 9, 54, 3, 9, 51, 54, 51, 50, 54, 49, 35, 53, 49, 28, 35, 28, 29, 35, 48, 52, 50, 48, 56, 52, 52, 55, 50, 50, 55, 54, 49, 57, 56, 49, 53, 57, 9, 3, 8, 56, 57, 52, 29, 34, 35, 29, 30, 34, 3, 7, 8, 57, 53, 36, 36, 53, 35, 52, 57, 58, 57, 36, 37, 57, 37, 47, 57, 47, 58, 52, 58, 61, 52, 61, 55, 60, 58, 59, 58, 60, 61, 54, 2, 3, 3, 4, 7, 54, 55, 2, 55, 61, 44, 2, 55, 44, 6, 4, 5, 6, 7, 4, 34, 30, 33, 30, 31, 33, 33, 31, 32, 2, 44, 45, 1, 2, 45, 47, 37, 46, 46, 37, 38, 47, 46, 58, 44, 61, 45, 46, 38, 40, 38, 39, 40, 0, 1, 43, 1, 45, 43, 58, 46, 59, 46, 40, 41, 45, 60, 42, 45, 42, 43, 45, 61, 60, 59, 46, 41, 60, 59, 42, 59, 41, 42], "vertices": [2, 8, 59.39, -31.02, 0.32, 5, 104.54, -140.21, 0.68, 2, 8, 36.19, -11.82, 0.32, 5, 104.33, -110.1, 0.68, 1, 5, 72.97, -81.33, 1, 2, 5, 46.06, -92.14, 0.84, 3, -55.75, 93.79, 0.16, 2, 8, 6.35, -22.71, 0.304, 5, 76.81, -94.24, 0.696, 2, 8, 44.11, -29.77, 0.224, 5, 95.67, -127.71, 0.776, 2, 8, 39.88, -37.13, 0.144, 5, 87.32, -129.2, 0.856, 2, 8, 9, -53.78, 0.144, 5, 54.71, -116.24, 0.856, 2, 5, 26.22, -115.86, 0.664, 3, -35.56, 117.21, 0.336, 2, 5, 19.62, -94.39, 0.664, 3, -29.27, 95.65, 0.336, 2, 5, 8.87, -87.22, 0.664, 3, -18.63, 88.32, 0.336, 2, 5, -12.41, -85.86, 0.488, 3, 2.62, 86.65, 0.512, 2, 5, -13.7, -68.18, 0.488, 3, 3.65, 68.95, 0.512, 2, 5, -8.88, -66.41, 0.488, 3, -1.2, 67.25, 0.512, 2, 5, -17.24, -60.89, 0.488, 3, 7.08, 61.61, 0.512, 2, 5, -20.77, -42.65, 0.488, 3, 10.34, 43.32, 0.512, 2, 5, -11.76, -34.94, 0.488, 3, 1.22, 35.74, 0.512, 2, 5, -20.5, -29.35, 0.488, 3, 9.87, 30.02, 0.512, 2, 5, -20, -5.64, 0.488, 3, 9.02, 6.32, 0.512, 2, 5, -14.78, -0.13, 0.488, 3, 3.72, 0.89, 0.512, 2, 5, -19.79, 4.34, 0.488, 3, 8.66, -3.66, 0.512, 2, 5, -19.41, 22.59, 0.488, 3, 8.01, -21.89, 0.512, 2, 5, -11.01, 31.34, 0.488, 3, -0.52, -30.52, 0.512, 2, 5, -19.13, 36.16, 0.488, 3, 7.53, -35.47, 0.512, 2, 5, -18.76, 54.01, 0.488, 3, 6.89, -53.3, 0.512, 2, 5, -6.47, 63.07, 0.488, 3, -5.53, -62.18, 0.512, 2, 5, -11.8, 68.22, 0.488, 3, -0.28, -67.41, 0.512, 2, 5, -5.65, 83.62, 0.488, 3, -6.65, -82.71, 0.512, 2, 5, 19.27, 87.75, 0.664, 3, -31.63, -86.48, 0.336, 2, 5, 28.98, 109, 0.664, 3, -41.66, -107.57, 0.336, 2, 5, 70.6, 112.18, 0.84, 9, 21.79, 46.04, 0.16, 2, 5, 93.57, 123.46, 0.84, 9, 45.19, 35.71, 0.16, 2, 5, 100.54, 118.53, 0.84, 9, 45.89, 27.2, 0.16, 2, 5, 83.31, 90.6, 0.84, 9, 13.42, 22.44, 0.16, 2, 5, 48.51, 87.79, 0.84, 3, -60.87, -86.08, 0.16, 2, 5, 45.54, 85.14, 0.84, 3, -57.87, -83.48, 0.16, 1, 5, 61.57, 85.43, 1, 1, 5, 82.13, 76.89, 1, 2, 5, 107.86, 100.23, 0.712, 9, 36.58, 9.82, 0.288, 2, 5, 109.41, 134.75, 0.712, 9, 64.02, 30.82, 0.288, 2, 5, 178.94, 133.3, 0.712, 9, 107.6, -23.36, 0.288, 2, 5, 176.95, 38.07, 0.712, 9, 33.37, -83.06, 0.288, 2, 8, 34.08, 82.41, 0.32, 5, 175.16, -47.92, 0.68, 2, 8, 104.62, 20.67, 0.32, 5, 173.21, -141.64, 0.68, 1, 5, 102.29, -66.02, 1, 2, 8, 18.83, 15.73, 0.32, 5, 114.28, -79.09, 0.68, 2, 5, 115.53, 72.66, 0.712, 9, 20.39, -13.78, 0.288, 1, 5, 104.18, 58.46, 1, 2, 5, 19.63, -0.47, 0.664, 3, -30.68, 1.74, 0.336, 2, 5, 22.7, 63.99, 0.664, 3, -34.71, -62.66, 0.336, 2, 5, 20.05, -35.34, 0.664, 3, -30.59, 36.61, 0.336, 2, 5, 11.93, -65.99, 0.664, 3, -22.01, 67.14, 0.336, 2, 5, 45.83, -1.02, 0.84, 3, -56.87, 2.68, 0.16, 2, 5, 44.67, 67.56, 0.84, 3, -56.73, -65.91, 0.16, 2, 5, 47.63, -67.02, 0.84, 3, -57.69, 68.7, 0.16, 2, 5, 47.41, -35.91, 0.84, 3, -57.93, 37.59, 0.16, 2, 5, 22.34, 32.88, 0.664, 3, -33.89, -31.57, 0.336, 2, 5, 46.02, 35.56, 0.84, 3, -57.6, -33.89, 0.16, 1, 5, 121.66, 24.36, 1, 1, 5, 141.41, 22.2, 1, 1, 5, 138.59, -28.97, 1, 1, 5, 119.86, -33.82, 1], "hull": 44, "edges": [56, 54, 54, 52, 52, 50, 50, 48, 46, 48, 46, 44, 44, 42, 40, 42, 40, 38, 38, 36, 34, 36, 34, 32, 32, 30, 26, 24, 24, 22, 22, 20, 20, 18, 88, 90, 84, 86, 90, 84, 4, 2, 0, 86, 2, 0, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 58, 56, 64, 66, 66, 68, 68, 70, 70, 72, 62, 64, 72, 74, 92, 94, 80, 82, 82, 84, 92, 82, 78, 80, 78, 76, 76, 74, 28, 30, 26, 28, 38, 96, 50, 98, 32, 100, 26, 102, 96, 104, 98, 106, 58, 60, 60, 62, 102, 108, 100, 110, 44, 112, 112, 114, 94, 116, 116, 118, 118, 120, 120, 122, 122, 88], "width": 275, "height": 196}, "Head2": {"name": "SwampFloaters/RingshotTurretHead", "type": "mesh", "uvs": [1, 0.35044, 0.89056, 0.35469, 0.78834, 0.51771, 0.82967, 0.65382, 0.83497, 0.49676, 0.95523, 0.397, 0.96128, 0.43945, 0.91665, 0.60713, 0.91741, 0.75251, 0.83985, 0.78848, 0.8146, 0.84408, 0.81128, 0.95273, 0.7471, 0.96122, 0.7403, 0.93681, 0.72087, 0.98004, 0.65483, 1, 0.62609, 0.95485, 0.60642, 1, 0.5202, 1, 0.49977, 0.97395, 0.48389, 1, 0.41754, 1, 0.38509, 0.95808, 0.36816, 1, 0.30325, 1, 0.26939, 0.93828, 0.25105, 0.96599, 0.19461, 0.9363, 0.17768, 0.80961, 0.09972, 0.76233, 0.085, 0.55034, 0.04224, 0.43441, 0.05964, 0.39833, 0.1625, 0.48323, 0.17536, 0.66045, 0.18519, 0.67531, 0.18292, 0.59359, 0.21242, 0.48782, 0.12563, 0.35903, 0, 0.35478, 0, 0, 0.34637, 0, 0.65912, 0, 1, 0, 0.73047, 0.3698, 0.77705, 0.30725, 0.22528, 0.31696, 0.27776, 0.37339, 0.49842, 0.79839, 0.26383, 0.78958, 0.62514, 0.79252, 0.7372, 0.83072, 0.49842, 0.66468, 0.24917, 0.6779, 0.73824, 0.64852, 0.62514, 0.65292, 0.37694, 0.78811, 0.36542, 0.66762, 0.40042, 0.28059, 0.40677, 0.17963, 0.59301, 0.18854, 0.61205, 0.28356], "triangles": [15, 16, 14, 17, 18, 16, 18, 19, 16, 20, 21, 19, 21, 22, 19, 23, 24, 22, 24, 25, 22, 14, 16, 13, 26, 27, 25, 12, 13, 11, 22, 48, 19, 19, 48, 16, 25, 56, 22, 22, 56, 48, 48, 50, 16, 16, 51, 13, 16, 50, 51, 11, 13, 10, 27, 49, 25, 25, 49, 56, 13, 51, 10, 27, 28, 49, 10, 51, 9, 9, 54, 3, 9, 51, 54, 51, 50, 54, 49, 35, 53, 49, 28, 35, 28, 29, 35, 48, 52, 50, 48, 56, 52, 52, 55, 50, 50, 55, 54, 49, 57, 56, 49, 53, 57, 9, 3, 8, 56, 57, 52, 29, 34, 35, 29, 30, 34, 3, 7, 8, 57, 53, 36, 36, 53, 35, 52, 57, 58, 57, 36, 37, 57, 37, 47, 57, 47, 58, 52, 58, 61, 52, 61, 55, 60, 58, 59, 58, 60, 61, 54, 2, 3, 3, 4, 7, 54, 55, 2, 55, 61, 44, 2, 55, 44, 6, 4, 5, 6, 7, 4, 34, 30, 33, 30, 31, 33, 33, 31, 32, 2, 44, 45, 1, 2, 45, 47, 37, 46, 46, 37, 38, 47, 46, 58, 44, 61, 45, 46, 38, 40, 38, 39, 40, 0, 1, 43, 1, 45, 43, 58, 46, 59, 46, 40, 41, 45, 60, 42, 45, 42, 43, 45, 61, 60, 59, 46, 41, 60, 59, 42, 59, 41, 42], "vertices": [2, 8, 59.39, -31.02, 0.32, 5, 104.54, -140.21, 0.68, 2, 8, 36.19, -11.82, 0.32, 5, 104.33, -110.1, 0.68, 1, 5, 72.97, -81.33, 1, 2, 5, 46.06, -92.14, 0.84, 3, -55.75, 93.79, 0.16, 2, 8, 6.35, -22.71, 0.304, 5, 76.81, -94.24, 0.696, 2, 8, 44.11, -29.77, 0.224, 5, 95.67, -127.71, 0.776, 2, 8, 39.88, -37.13, 0.144, 5, 87.32, -129.2, 0.856, 2, 8, 9, -53.78, 0.144, 5, 54.71, -116.24, 0.856, 2, 5, 26.22, -115.86, 0.664, 3, -35.56, 117.21, 0.336, 2, 5, 19.62, -94.39, 0.664, 3, -29.27, 95.65, 0.336, 2, 5, 8.87, -87.22, 0.664, 3, -18.63, 88.32, 0.336, 2, 5, -12.41, -85.86, 0.488, 3, 2.62, 86.65, 0.512, 2, 5, -13.7, -68.18, 0.488, 3, 3.65, 68.95, 0.512, 2, 5, -8.88, -66.41, 0.488, 3, -1.2, 67.25, 0.512, 2, 5, -17.24, -60.89, 0.488, 3, 7.08, 61.61, 0.512, 2, 5, -20.77, -42.65, 0.488, 3, 10.34, 43.32, 0.512, 2, 5, -11.76, -34.94, 0.488, 3, 1.22, 35.74, 0.512, 2, 5, -20.5, -29.35, 0.488, 3, 9.87, 30.02, 0.512, 2, 5, -20, -5.64, 0.488, 3, 9.02, 6.32, 0.512, 2, 5, -14.78, -0.13, 0.488, 3, 3.72, 0.89, 0.512, 2, 5, -19.79, 4.34, 0.488, 3, 8.66, -3.66, 0.512, 2, 5, -19.41, 22.59, 0.488, 3, 8.01, -21.89, 0.512, 2, 5, -11.01, 31.34, 0.488, 3, -0.52, -30.52, 0.512, 2, 5, -19.13, 36.16, 0.488, 3, 7.53, -35.47, 0.512, 2, 5, -18.76, 54.01, 0.488, 3, 6.89, -53.3, 0.512, 2, 5, -6.47, 63.07, 0.488, 3, -5.53, -62.18, 0.512, 2, 5, -11.8, 68.22, 0.488, 3, -0.28, -67.41, 0.512, 2, 5, -5.65, 83.62, 0.488, 3, -6.65, -82.71, 0.512, 2, 5, 19.27, 87.75, 0.664, 3, -31.63, -86.48, 0.336, 2, 5, 28.98, 109, 0.664, 3, -41.66, -107.57, 0.336, 2, 5, 70.6, 112.18, 0.84, 9, 21.79, 46.04, 0.16, 2, 5, 93.57, 123.46, 0.84, 9, 45.19, 35.71, 0.16, 2, 5, 100.54, 118.53, 0.84, 9, 45.89, 27.2, 0.16, 2, 5, 83.31, 90.6, 0.84, 9, 13.42, 22.44, 0.16, 2, 5, 48.51, 87.79, 0.84, 3, -60.87, -86.08, 0.16, 2, 5, 45.54, 85.14, 0.84, 3, -57.87, -83.48, 0.16, 1, 5, 61.57, 85.43, 1, 1, 5, 82.13, 76.89, 1, 2, 5, 107.86, 100.23, 0.712, 9, 36.58, 9.82, 0.288, 2, 5, 109.41, 134.75, 0.712, 9, 64.02, 30.82, 0.288, 2, 5, 178.94, 133.3, 0.712, 9, 107.6, -23.36, 0.288, 2, 5, 176.95, 38.07, 0.712, 9, 33.37, -83.06, 0.288, 2, 8, 34.08, 82.41, 0.32, 5, 175.16, -47.92, 0.68, 2, 8, 104.62, 20.67, 0.32, 5, 173.21, -141.64, 0.68, 1, 5, 102.29, -66.02, 1, 2, 8, 18.83, 15.73, 0.32, 5, 114.28, -79.09, 0.68, 2, 5, 115.53, 72.66, 0.712, 9, 20.39, -13.78, 0.288, 1, 5, 104.18, 58.46, 1, 2, 5, 19.63, -0.47, 0.664, 3, -30.68, 1.74, 0.336, 2, 5, 22.7, 63.99, 0.664, 3, -34.71, -62.66, 0.336, 2, 5, 20.05, -35.34, 0.664, 3, -30.59, 36.61, 0.336, 2, 5, 11.93, -65.99, 0.664, 3, -22.01, 67.14, 0.336, 2, 5, 45.83, -1.02, 0.84, 3, -56.87, 2.68, 0.16, 2, 5, 44.67, 67.56, 0.84, 3, -56.73, -65.91, 0.16, 2, 5, 47.63, -67.02, 0.84, 3, -57.69, 68.7, 0.16, 2, 5, 47.41, -35.91, 0.84, 3, -57.93, 37.59, 0.16, 2, 5, 22.34, 32.88, 0.664, 3, -33.89, -31.57, 0.336, 2, 5, 46.02, 35.56, 0.84, 3, -57.6, -33.89, 0.16, 1, 5, 121.66, 24.36, 1, 1, 5, 141.41, 22.2, 1, 1, 5, 138.59, -28.97, 1, 1, 5, 119.86, -33.82, 1], "hull": 44, "edges": [56, 54, 54, 52, 52, 50, 50, 48, 46, 48, 46, 44, 44, 42, 40, 42, 40, 38, 38, 36, 34, 36, 34, 32, 32, 30, 26, 24, 24, 22, 22, 20, 20, 18, 88, 90, 84, 86, 90, 84, 4, 2, 0, 86, 2, 0, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 58, 56, 64, 66, 66, 68, 68, 70, 70, 72, 62, 64, 72, 74, 92, 94, 80, 82, 82, 84, 92, 82, 78, 80, 78, 76, 76, 74, 28, 30, 26, 28, 38, 96, 50, 98, 32, 100, 26, 102, 96, 104, 98, 106, 58, 60, 60, 62, 102, 108, 100, 110, 44, 112, 112, 114, 94, 116, 116, 118, 118, 120, 120, 122, 122, 88], "width": 275, "height": 196}}, "Skirt": {"Skirt": {"name": "SwampFloaters/ScuttleSkirt2", "type": "<PERSON><PERSON><PERSON>", "skin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parent": "Skirt", "width": 140, "height": 47}}, "Spike1": {"SwampFloaters/SpikerSpike1": {"x": 64.35, "y": 1.14, "rotation": -90.38, "width": 62, "height": 147}}, "Spike4": {"SwampFloaters/SpikerSpike2": {"x": 64.35, "y": 1.14, "rotation": -90.38, "width": 58, "height": 145}}, "Spike5": {"SwampFloaters/SpikerSpike1": {"x": 64.35, "y": 1.14, "rotation": -90.38, "width": 62, "height": 147}}}}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attachments": {"BodyBtm": {"BodyBtm": {"name": "SwampFloaters/Tail_Scuttle_Btm", "type": "mesh", "uvs": [1, 0.34857, 0.99999, 0.58548, 1, 0.81866, 0.89701, 0.92085, 0.72301, 0.92913, 0.64709, 0.83799, 0.60182, 1, 0.39528, 1, 0.32546, 0.93599, 0.32661, 0.82307, 0.27694, 0.89875, 0.0713, 0.87113, 0.00342, 0.7445, 1e-05, 0.58167, 0, 0.35492, 0, 0, 0.31863, 1e-05, 0.62117, 0, 1, 0, 0.32541, 0.58399, 0.64145, 0.5937, 0.32154, 0.34349, 0.62989, 0.35111], "triangles": [6, 7, 5, 5, 7, 9, 7, 8, 9, 4, 5, 3, 3, 5, 2, 10, 11, 9, 11, 12, 9, 9, 20, 5, 5, 20, 2, 12, 19, 9, 9, 19, 20, 20, 1, 2, 12, 13, 19, 19, 22, 20, 1, 20, 0, 19, 13, 14, 21, 15, 16, 22, 17, 18, 21, 16, 17, 21, 14, 15, 18, 0, 22, 22, 21, 17, 0, 20, 22, 19, 14, 21, 19, 21, 22], "vertices": [3, 3, 59.94, 31.88, 0.55411, 4, 19.91, 31.86, 0.33389, 7, -10.83, -34.14, 0.112, 3, 3, 74.86, 31.35, 0.20966, 4, 34.83, 31.33, 0.51834, 7, -25.75, -33.83, 0.272, 3, 3, 89.54, 30.83, 0.06067, 4, 49.51, 30.81, 0.57133, 7, -40.44, -33.53, 0.368, 2, 4, 55.7, 23.83, 0.552, 7, -46.74, -26.65, 0.448, 2, 4, 55.81, 12.42, 0.552, 7, -47.02, -15.24, 0.448, 3, 3, 89.93, 7.68, 0.06067, 4, 49.9, 7.66, 0.57133, 7, -41.18, -10.39, 0.368, 2, 4, 59.99, 4.33, 0.552, 7, -51.32, -7.21, 0.448, 2, 4, 59.51, -9.19, 0.552, 7, -51.04, 6.31, 0.448, 2, 4, 55.32, -13.62, 0.552, 7, -46.91, 10.8, 0.448, 3, 3, 88.24, -13.26, 0.06067, 4, 48.21, -13.29, 0.57133, 7, -39.8, 10.58, 0.368, 2, 4, 52.86, -16.71, 0.552, 7, -44.5, 13.93, 0.448, 2, 4, 50.64, -30.11, 0.632, 7, -42.48, 27.36, 0.368, 3, 3, 82.54, -34.24, 0.06989, 4, 42.51, -34.27, 0.65811, 7, -34.41, 31.64, 0.272, 3, 3, 72.28, -34.1, 0.20966, 4, 32.25, -34.13, 0.51834, 7, -24.15, 31.65, 0.272, 3, 3, 58, -33.6, 0.55411, 4, 17.97, -33.62, 0.33389, 7, -9.87, 31.36, 0.112, 1, 3, 35.66, -32.81, 1, 1, 3, 36.4, -11.95, 1, 1, 3, 37.11, 7.85, 1, 1, 3, 38, 32.65, 1, 3, 3, 73.19, -12.81, 0.20966, 4, 33.15, -12.83, 0.51834, 7, -24.74, 10.34, 0.272, 3, 3, 74.54, 7.86, 0.20966, 4, 34.5, 7.84, 0.51834, 7, -25.78, -10.34, 0.272, 3, 3, 58.04, -12.53, 0.55411, 4, 18, -12.55, 0.33389, 7, -9.58, 10.29, 0.112, 3, 3, 59.24, 7.65, 0.55411, 4, 19.2, 7.62, 0.33389, 7, -10.49, -9.9, 0.112], "hull": 19, "edges": [24, 22, 22, 20, 18, 38, 24, 26, 40, 10, 10, 8, 8, 6, 6, 4, 4, 2, 10, 12, 18, 16, 12, 14, 16, 14, 20, 18, 38, 42, 40, 44, 2, 0, 0, 36, 26, 28, 28, 30, 30, 32, 42, 32, 32, 34, 34, 36, 44, 34], "width": 55, "height": 63}}, "BodyTop": {"BodyTop": {"name": "SwampFloaters/<PERSON>l_<PERSON>uttle_Top", "type": "<PERSON><PERSON><PERSON>", "skin": "ScuttleExploder", "parent": "BodyTop", "width": 87, "height": 78}}, "Eye1": {"Eye1": {"name": "SwampFloaters/Eye", "x": 0.5, "y": -1, "rotation": 90, "width": 67, "height": 52}, "Eye_Closed": {"name": "SwampFloaters/Scuttle_Eye_Closed", "x": -0.49, "y": 0.51, "rotation": 88.81, "width": 77, "height": 63}}, "Eye1Back": {"Eye1Back": {"name": "SwampFloaters/Scuttle_Eye_Back", "rotation": 90, "width": 122, "height": 90}}, "Eye1Back2": {"Eye1Back": {"name": "SwampFloaters/Scuttle_Eye_Back", "rotation": 90, "width": 122, "height": 90}}, "Eye1Back3": {"Eye1Back": {"name": "SwampFloaters/Scuttle_Eye_Back", "rotation": 90, "width": 122, "height": 90}}, "Eye2": {"Eye1": {"name": "SwampFloaters/Eye", "x": 0.5, "y": -1, "rotation": 90, "width": 67, "height": 52}, "Eye_Closed": {"name": "SwampFloaters/Scuttle_Eye_Closed", "x": -0.49, "y": 0.51, "rotation": 88.81, "width": 77, "height": 63}}, "Eye3": {"Eye1": {"name": "SwampFloaters/Eye", "x": 0.5, "y": -1, "rotation": 90, "width": 67, "height": 52}, "Eye_Closed": {"name": "SwampFloaters/Scuttle_Eye_Closed", "x": -0.49, "y": 0.51, "rotation": 88.81, "width": 77, "height": 63}}, "EyeExploder": {"EyeMiddle_Closed": {"name": "SwampFloaters/Eye", "x": 0.5, "rotation": 90, "width": 67, "height": 52}, "EyeMiddle": {"name": "SwampFloaters/Eye", "x": 0.5, "rotation": 90, "width": 67, "height": 52}}, "EyeExploder2": {"EyeMiddle": {"name": "SwampFloaters/Eye", "x": 0.5, "rotation": 90, "width": 67, "height": 52}, "EyeMiddle_Closed": {"name": "SwampFloaters/Eye", "x": 0.5, "rotation": 90, "width": 67, "height": 52}}, "Head": {"Head": {"name": "SwampFloaters/ScuttleTurretHead", "type": "mesh", "uvs": [0.64409, 0.10617, 0.77446, 0.15891, 0.91038, 0.33119, 0.98598, 0.43276, 0.98264, 0.53594, 0.95078, 0.55538, 0.97951, 0.74513, 0.9349, 0.79992, 0.92457, 0.93952, 0.8918, 0.96236, 0.82955, 0.93537, 0.77877, 1, 0.70888, 1, 0.67053, 0.95601, 0.6253, 1, 0.53051, 1, 0.49505, 0.9661, 0.46104, 1, 0.37905, 1, 0.33646, 0.94575, 0.31025, 1, 0.21687, 1, 0.171, 0.93952, 0.07699, 0.94559, 0.05974, 0.77931, 0.00693, 0.80025, 0, 0.71034, 0.04486, 0.58653, 0.03246, 0.50875, 0.14341, 0.23275, 0.25853, 0.12903, 0.24327, 0, 0.63855, 0, 0.32649, 0.76013, 0.67183, 0.73025, 0.49708, 0.74959, 0.16422, 0.75486, 0.85491, 0.76189, 0.67045, 0.52457, 0.84797, 0.52808, 0.49015, 0.52633, 0.31817, 0.53687, 0.17393, 0.55797], "triangles": [16, 35, 13, 16, 19, 35, 13, 34, 10, 22, 33, 19, 23, 24, 22, 8, 10, 7, 10, 11, 13, 11, 12, 13, 20, 21, 19, 21, 22, 19, 9, 10, 8, 13, 14, 16, 14, 15, 16, 16, 17, 19, 17, 18, 19, 13, 35, 34, 19, 33, 35, 24, 36, 22, 22, 36, 33, 10, 37, 7, 10, 34, 37, 25, 26, 24, 7, 37, 6, 36, 24, 27, 34, 39, 37, 37, 5, 6, 37, 39, 5, 36, 42, 33, 42, 41, 33, 33, 41, 35, 24, 26, 27, 36, 27, 42, 41, 40, 35, 34, 40, 38, 34, 35, 40, 34, 38, 39, 27, 28, 42, 41, 42, 29, 4, 5, 3, 42, 28, 29, 29, 30, 41, 41, 30, 40, 3, 5, 39, 39, 2, 3, 39, 38, 2, 40, 0, 38, 40, 30, 0, 38, 1, 2, 38, 0, 1, 30, 32, 0, 30, 31, 32], "vertices": [1, 5, 119.94, -31.18, 1, 1, 5, 111.12, -56.95, 1, 1, 5, 83.51, -83.42, 1, 1, 5, 67.26, -98.13, 1, 2, 5, 51.07, -97.13, 0.904, 3, -60.69, 98.86, 0.096, 2, 5, 48.16, -90.73, 0.904, 3, -57.86, 92.41, 0.096, 2, 5, 18.25, -95.82, 0.792, 3, -27.89, 97.06, 0.208, 2, 5, 9.84, -86.77, 0.792, 3, -19.61, 87.88, 0.208, 2, 5, -12.03, -84.26, 0.536, 3, 2.22, 85.05, 0.464, 2, 5, -15.48, -77.66, 0.536, 3, 5.57, 78.4, 0.464, 2, 5, -10.99, -65.37, 0.536, 3, 0.9, 66.17, 0.464, 2, 5, -20.92, -55.05, 0.536, 3, 10.68, 55.71, 0.464, 2, 5, -20.63, -41.15, 0.536, 3, 10.18, 41.81, 0.464, 2, 5, -13.57, -33.66, 0.536, 3, 3, 34.43, 0.464, 2, 5, -20.29, -24.52, 0.536, 3, 9.59, 25.19, 0.464, 2, 5, -19.89, -5.66, 0.536, 3, 8.91, 6.34, 0.464, 2, 5, -14.42, 1.29, 0.536, 3, 3.34, -0.52, 0.464, 2, 5, -19.6, 8.16, 0.536, 3, 8.42, -7.47, 0.464, 2, 5, -19.26, 24.48, 0.536, 3, 7.84, -23.78, 0.464, 2, 5, -10.57, 32.77, 0.536, 3, -0.98, -31.95, 0.464, 2, 5, -18.98, 38.16, 0.536, 3, 7.35, -37.46, 0.464, 2, 5, -18.59, 56.74, 0.536, 3, 6.68, -56.03, 0.464, 2, 5, -8.91, 65.67, 0.536, 3, -3.13, -64.82, 0.464, 2, 5, -9.47, 84.39, 0.536, 3, -2.85, -83.55, 0.464, 2, 5, 16.7, 87.28, 0.792, 3, -29.06, -86.05, 0.208, 2, 5, 13.63, 97.86, 0.792, 3, -26.15, -96.67, 0.208, 2, 5, 27.77, 98.94, 0.792, 3, -40.3, -97.54, 0.208, 2, 5, 47.02, 89.61, 0.904, 3, -59.41, -87.93, 0.096, 2, 5, 59.28, 91.83, 0.904, 3, -71.7, -89.96, 0.096, 1, 5, 102.14, 68.85, 1, 1, 5, 117.95, 45.61, 1, 1, 5, 138.26, 48.22, 1, 1, 5, 136.63, -30.42, 1, 2, 5, 18.6, 34.15, 0.792, 3, -30.17, -32.89, 0.208, 2, 5, 21.86, -34.66, 0.792, 3, -32.41, 35.96, 0.208, 2, 5, 19.55, 0.17, 0.792, 3, -30.62, 1.1, 0.208, 2, 5, 20.1, 66.42, 0.792, 3, -32.15, -65.13, 0.208, 2, 5, 16.14, -70.98, 0.792, 3, -26.14, 72.19, 0.208, 2, 5, 54.15, -35.05, 0.904, 3, -64.69, 36.83, 0.096, 2, 5, 52.87, -70.36, 0.904, 3, -62.88, 72.12, 0.096, 2, 5, 54.63, 0.82, 0.904, 3, -65.69, 0.97, 0.096, 2, 5, 53.68, 35.07, 0.904, 3, -65.26, -33.29, 0.096, 2, 5, 50.97, 63.84, 0.904, 3, -62.98, -62.1, 0.096], "hull": 33, "edges": [50, 48, 48, 46, 46, 44, 44, 42, 40, 42, 38, 40, 38, 36, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 50, 52, 52, 54, 34, 36, 34, 32, 32, 30, 28, 30, 28, 26, 22, 24, 26, 24, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 0, 0, 2, 2, 4, 4, 6, 6, 8, 10, 8, 38, 66, 26, 68, 32, 70, 44, 72, 20, 74, 68, 76, 74, 78, 70, 80, 66, 82, 72, 84], "width": 199, "height": 157}, "Head2": {"name": "SwampFloaters/ScuttleTurretHead", "type": "mesh", "uvs": [0.64409, 0.10617, 0.77446, 0.15891, 0.91038, 0.33119, 0.98598, 0.43276, 0.98264, 0.53594, 0.95078, 0.55538, 0.97951, 0.74513, 0.9349, 0.79992, 0.92457, 0.93952, 0.8918, 0.96236, 0.82955, 0.93537, 0.77877, 1, 0.70888, 1, 0.67053, 0.95601, 0.6253, 1, 0.53051, 1, 0.49505, 0.9661, 0.46104, 1, 0.37905, 1, 0.33646, 0.94575, 0.31025, 1, 0.21687, 1, 0.171, 0.93952, 0.07699, 0.94559, 0.05974, 0.77931, 0.00693, 0.80025, 0, 0.71034, 0.04486, 0.58653, 0.03246, 0.50875, 0.14341, 0.23275, 0.25853, 0.12903, 0.24327, 0, 0.63855, 0, 0.32649, 0.76013, 0.67183, 0.73025, 0.49708, 0.74959, 0.16422, 0.75486, 0.85491, 0.76189, 0.67045, 0.52457, 0.84797, 0.52808, 0.49015, 0.52633, 0.31817, 0.53687, 0.17393, 0.55797], "triangles": [16, 35, 13, 16, 19, 35, 13, 34, 10, 22, 33, 19, 23, 24, 22, 8, 10, 7, 10, 11, 13, 11, 12, 13, 20, 21, 19, 21, 22, 19, 9, 10, 8, 13, 14, 16, 14, 15, 16, 16, 17, 19, 17, 18, 19, 13, 35, 34, 19, 33, 35, 24, 36, 22, 22, 36, 33, 10, 37, 7, 10, 34, 37, 25, 26, 24, 7, 37, 6, 36, 24, 27, 34, 39, 37, 37, 5, 6, 37, 39, 5, 36, 42, 33, 42, 41, 33, 33, 41, 35, 24, 26, 27, 36, 27, 42, 41, 40, 35, 34, 40, 38, 34, 35, 40, 34, 38, 39, 27, 28, 42, 41, 42, 29, 4, 5, 3, 42, 28, 29, 29, 30, 41, 41, 30, 40, 3, 5, 39, 39, 2, 3, 39, 38, 2, 40, 0, 38, 40, 30, 0, 38, 1, 2, 38, 0, 1, 30, 32, 0, 30, 31, 32], "vertices": [1, 5, 119.94, -31.18, 1, 1, 5, 111.12, -56.95, 1, 1, 5, 83.51, -83.42, 1, 1, 5, 67.26, -98.13, 1, 2, 5, 51.07, -97.13, 0.904, 3, -60.69, 98.86, 0.096, 2, 5, 48.16, -90.73, 0.904, 3, -57.86, 92.41, 0.096, 2, 5, 18.25, -95.82, 0.792, 3, -27.89, 97.06, 0.208, 2, 5, 9.84, -86.77, 0.792, 3, -19.61, 87.88, 0.208, 2, 5, -12.03, -84.26, 0.536, 3, 2.22, 85.05, 0.464, 2, 5, -15.48, -77.66, 0.536, 3, 5.57, 78.4, 0.464, 2, 5, -10.99, -65.37, 0.536, 3, 0.9, 66.17, 0.464, 2, 5, -20.92, -55.05, 0.536, 3, 10.68, 55.71, 0.464, 2, 5, -20.63, -41.15, 0.536, 3, 10.18, 41.81, 0.464, 2, 5, -13.57, -33.66, 0.536, 3, 3, 34.43, 0.464, 2, 5, -20.29, -24.52, 0.536, 3, 9.59, 25.19, 0.464, 2, 5, -19.89, -5.66, 0.536, 3, 8.91, 6.34, 0.464, 2, 5, -14.42, 1.29, 0.536, 3, 3.34, -0.52, 0.464, 2, 5, -19.6, 8.16, 0.536, 3, 8.42, -7.47, 0.464, 2, 5, -19.26, 24.48, 0.536, 3, 7.84, -23.78, 0.464, 2, 5, -10.57, 32.77, 0.536, 3, -0.98, -31.95, 0.464, 2, 5, -18.98, 38.16, 0.536, 3, 7.35, -37.46, 0.464, 2, 5, -18.59, 56.74, 0.536, 3, 6.68, -56.03, 0.464, 2, 5, -8.91, 65.67, 0.536, 3, -3.13, -64.82, 0.464, 2, 5, -9.47, 84.39, 0.536, 3, -2.85, -83.55, 0.464, 2, 5, 16.7, 87.28, 0.792, 3, -29.06, -86.05, 0.208, 2, 5, 13.63, 97.86, 0.792, 3, -26.15, -96.67, 0.208, 2, 5, 27.77, 98.94, 0.792, 3, -40.3, -97.54, 0.208, 2, 5, 47.02, 89.61, 0.904, 3, -59.41, -87.93, 0.096, 2, 5, 59.28, 91.83, 0.904, 3, -71.7, -89.96, 0.096, 1, 5, 102.14, 68.85, 1, 1, 5, 117.95, 45.61, 1, 1, 5, 138.26, 48.22, 1, 1, 5, 136.63, -30.42, 1, 2, 5, 18.6, 34.15, 0.792, 3, -30.17, -32.89, 0.208, 2, 5, 21.86, -34.66, 0.792, 3, -32.41, 35.96, 0.208, 2, 5, 19.55, 0.17, 0.792, 3, -30.62, 1.1, 0.208, 2, 5, 20.1, 66.42, 0.792, 3, -32.15, -65.13, 0.208, 2, 5, 16.14, -70.98, 0.792, 3, -26.14, 72.19, 0.208, 2, 5, 54.15, -35.05, 0.904, 3, -64.69, 36.83, 0.096, 2, 5, 52.87, -70.36, 0.904, 3, -62.88, 72.12, 0.096, 2, 5, 54.63, 0.82, 0.904, 3, -65.69, 0.97, 0.096, 2, 5, 53.68, 35.07, 0.904, 3, -65.26, -33.29, 0.096, 2, 5, 50.97, 63.84, 0.904, 3, -62.98, -62.1, 0.096], "hull": 33, "edges": [50, 48, 48, 46, 46, 44, 44, 42, 40, 42, 38, 40, 38, 36, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 50, 52, 52, 54, 34, 36, 34, 32, 32, 30, 28, 30, 28, 26, 22, 24, 26, 24, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 0, 0, 2, 2, 4, 4, 6, 6, 8, 10, 8, 38, 66, 26, 68, 32, 70, 44, 72, 20, 74, 68, 76, 74, 78, 70, 80, 66, 82, 72, 84], "width": 199, "height": 157}}, "Skirt": {"Skirt": {"name": "SwampFloaters/ScuttleSkirt", "type": "mesh", "uvs": [0.99999, 0.43207, 1, 0.63429, 0.97167, 0.8393, 0.85193, 0.8393, 0.81893, 0.72135, 0.77273, 0.9663, 0.64733, 0.94883, 0.60655, 0.76983, 0.55863, 1, 0.41489, 1, 0.37066, 0.76191, 0.31538, 1, 0.20665, 1, 0.17078, 0.75578, 0.12188, 0.86621, 0.03158, 0.8168, 1e-05, 0.45374, 1e-05, 0.00281, 0.16967, 0.01123, 0.36514, 0, 0.60102, 0, 0.81704, 0, 1, 0, 0.36784, 0.42259, 0.17159, 0.41902, 0.60655, 0.42502, 0.8161, 0.4068], "triangles": [0, 26, 22, 24, 19, 23, 24, 16, 17, 26, 25, 21, 23, 20, 25, 24, 17, 18, 21, 25, 20, 23, 19, 20, 22, 26, 21, 24, 18, 19, 1, 4, 26, 1, 26, 0, 25, 26, 4, 24, 15, 16, 10, 23, 25, 7, 10, 25, 7, 25, 4, 13, 15, 24, 3, 4, 1, 2, 3, 1, 14, 15, 13, 6, 7, 4, 5, 6, 4, 13, 23, 10, 23, 13, 24, 11, 12, 13, 10, 11, 13, 8, 9, 10, 7, 8, 10], "vertices": [2, 3, 4.74, 73.75, 0.552, 5, -14.72, -73, 0.448, 2, 3, 14.24, 73.42, 0.872, 5, -24.22, -72.81, 0.128, 1, 3, 23.73, 69.11, 1, 1, 3, 23.13, 52.36, 1, 2, 3, 17.42, 47.94, 0.872, 5, -27.79, -47.38, 0.128, 1, 3, 28.7, 41.06, 1, 1, 3, 27.25, 23.55, 1, 2, 3, 18.64, 18.14, 0.872, 5, -29.44, -17.6, 0.128, 1, 3, 29.21, 11.05, 1, 1, 3, 28.49, -9.06, 1, 2, 3, 17.09, -14.85, 0.872, 5, -28.38, 15.41, 0.128, 1, 3, 28, -22.98, 1, 1, 3, 27.45, -38.2, 1, 2, 3, 15.8, -42.8, 0.872, 5, -27.51, 43.38, 0.128, 1, 3, 20.75, -49.83, 1, 1, 3, 17.97, -62.38, 1, 2, 3, 0.76, -66.19, 0.552, 5, -12.82, 66.99, 0.448, 1, 5, 8.37, 66.54, 1, 1, 5, 7.48, 42.81, 1, 1, 5, 7.43, 15.43, 1, 1, 5, 6.75, -17.58, 1, 1, 5, 6.12, -47.82, 1, 1, 5, 5.58, -73.43, 1, 2, 3, 1.14, -14.67, 0.552, 5, -12.43, 15.47, 0.448, 2, 3, -0.01, -42.13, 0.552, 5, -11.69, 42.93, 0.448, 2, 3, 2.44, 18.72, 0.552, 5, -13.24, -17.94, 0.448, 2, 3, 2.64, 48.07, 0.552, 5, -13, -47.29, 0.448], "hull": 23, "edges": [20, 22, 20, 18, 20, 46, 46, 38, 22, 24, 26, 24, 26, 48, 34, 36, 36, 38, 48, 36, 26, 28, 28, 30, 32, 34, 30, 32, 16, 18, 16, 14, 14, 50, 38, 40, 50, 40, 14, 12, 12, 10, 10, 8, 8, 52, 40, 42, 42, 44, 52, 42, 8, 6, 6, 4, 4, 2, 2, 0, 0, 44], "width": 140, "height": 47}}, "Spike1": {"SwampFloaters/SpikerSpike1": {"x": 64.35, "y": 1.14, "rotation": -90.38, "width": 62, "height": 147}}, "Spike4": {"SwampFloaters/SpikerSpike2": {"x": 64.35, "y": 1.14, "rotation": -90.38, "width": 58, "height": 145}}, "Spike5": {"SwampFloaters/SpikerSpike1": {"x": 64.35, "y": 1.14, "rotation": -90.38, "width": 62, "height": 147}}, "SwampFloaters/Blob2": {"Blob1": {"name": "SwampFloaters/Scuttle_Eye_Back", "x": 45.36, "y": 10.21, "width": 122, "height": 90}}, "SwampFloaters/Blob3": {"Blob1": {"name": "SwampFloaters/Scuttle_Eye_Back", "x": 45.36, "y": 10.21, "width": 122, "height": 90}}}}, {"name": "Spiker", "bones": ["Spike10", "Spike7", "Spike6", "Spike8", "Spike1", "Spike2", "Spike5", "Spike4", "Spike9", "Spike11", "Spike3"], "attachments": {"BodyBtm": {"BodyBtm": {"name": "SwampFloaters/Tail_Spiker_Btm", "type": "<PERSON><PERSON><PERSON>", "skin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parent": "BodyBtm", "width": 87, "height": 91}}, "BodyTop": {"BodyTop": {"name": "SwampFloaters/Tail_Spiker_Top", "type": "mesh", "uvs": [0.96904, 0.24755, 0.97702, 0.4379, 0.98367, 0.59697, 0.99596, 0.71281, 0.99602, 0.79962, 0.99599, 0.86449, 0.8386, 0.864, 0.79916, 0.72536, 0.75577, 0.99212, 0.61625, 0.99999, 0.57541, 0.80822, 0.48377, 0.78017, 0.47982, 0.96659, 0.30226, 0.98911, 0.19565, 0.72187, 0.1994, 0.86318, 0.03189, 0.85654, 0.00407, 0.73183, 0, 0.63045, 0, 0.47046, 0, 0.27001, 0.02808, 0.13551, 0.20622, 0.13551, 0.51379, 0.13551, 0.80537, 0.13551, 0.19675, 0.61142, 0.19679, 0.47295, 0.08261, 0.20622, 0.4965, 0.60696, 0.50049, 0.46044, 0.76544, 0.59521, 0.77477, 0.44291], "triangles": [8, 9, 10, 6, 7, 4, 16, 17, 14, 5, 6, 4, 16, 14, 15, 12, 13, 11, 8, 10, 7, 7, 3, 4, 11, 28, 10, 10, 30, 7, 10, 28, 30, 11, 14, 28, 28, 14, 25, 17, 18, 14, 7, 30, 2, 7, 2, 3, 2, 31, 1, 31, 2, 30, 14, 18, 25, 18, 19, 25, 25, 26, 28, 25, 19, 26, 28, 29, 30, 28, 26, 29, 30, 29, 31, 19, 20, 26, 20, 27, 26, 26, 22, 29, 26, 27, 22, 29, 23, 31, 29, 22, 23, 31, 0, 1, 31, 24, 0, 31, 23, 24, 20, 21, 27, 27, 21, 22, 14, 11, 13], "vertices": [1, 3, -3.95, 62.52, 1, 1, 3, 16.44, 62.83, 1, 3, 3, 33.48, 63.1, 0.65056, 4, -6.55, 63.08, 0.20544, 7, 16.09, -64.96, 0.144, 3, 3, 45.99, 64.26, 0.39878, 4, 5.96, 64.24, 0.34522, 7, 3.6, -66.31, 0.256, 3, 3, 55.1, 63.94, 0.2448, 4, 15.06, 63.92, 0.3552, 7, -5.51, -66.12, 0.4, 3, 3, 62.15, 63.69, 0.1776, 4, 22.11, 63.67, 0.4224, 7, -12.56, -65.98, 0.4, 3, 3, 61.41, 43.19, 0.1776, 4, 21.38, 43.16, 0.4224, 7, -12.13, -45.47, 0.4, 3, 3, 46.21, 38.57, 0.5008, 4, 6.17, 38.54, 0.23479, 7, 3, -40.62, 0.26441, 3, 3, 76.09, 31.92, 0.39878, 4, 36.06, 31.89, 0.34522, 7, -26.98, -34.42, 0.256, 3, 3, 75.02, 13.7, 0.2448, 4, 34.99, 13.67, 0.3552, 7, -26.18, -16.18, 0.4, 3, 3, 53.81, 9.1, 0.2448, 4, 13.77, 9.08, 0.3552, 7, -5.03, -11.27, 0.4, 3, 3, 51, -2.73, 0.39878, 4, 10.96, -2.75, 0.34522, 7, -2.4, 0.6, 0.256, 3, 3, 70.75, -3.96, 0.2768, 4, 30.72, -3.98, 0.34015, 7, -22.17, 1.53, 0.38305, 3, 3, 72.28, -27.18, 0.3248, 4, 32.24, -27.2, 0.31757, 7, -24.04, 24.73, 0.35763, 3, 3, 43.11, -40.05, 0.51078, 4, 3.08, -40.07, 0.28091, 7, 4.93, 38.03, 0.20831, 3, 3, 58.86, -40.09, 0.18995, 4, 18.83, -40.11, 0.40658, 7, -10.82, 37.84, 0.40347, 3, 3, 56.86, -61.89, 0.1616, 4, 16.82, -61.92, 0.43062, 7, -9.14, 59.67, 0.40778, 3, 3, 43.39, -65.04, 0.27078, 4, 3.36, -65.07, 0.41871, 7, 4.27, 63.02, 0.3105, 3, 3, 32.51, -65.2, 0.65056, 4, -7.52, -65.22, 0.20544, 7, 15.15, 63.33, 0.144, 1, 3, 15.38, -64.6, 1, 1, 3, -6.06, -63.83, 1, 1, 3, -20.31, -59.66, 1, 1, 3, -19.48, -36.45, 1, 1, 3, -18.05, 3.63, 1, 1, 3, -16.69, 41.62, 1, 3, 3, 31.42, -39.49, 0.65056, 4, -8.61, -39.51, 0.20544, 7, 16.62, 37.64, 0.144, 1, 3, 16.56, -38.96, 1, 1, 3, -12.49, -52.82, 1, 3, 3, 32.28, -0.42, 0.65056, 4, -7.75, -0.44, 0.20544, 7, 16.34, -1.44, 0.144, 1, 3, 16.63, 0.65, 1, 3, 3, 32.33, 34.67, 0.65056, 4, -7.7, 34.65, 0.20544, 7, 16.82, -36.52, 0.144, 1, 3, 16.04, 36.46, 1], "hull": 25, "edges": [30, 28, 28, 50, 50, 52, 34, 36, 36, 38, 38, 40, 40, 42, 40, 54, 42, 44, 54, 44, 28, 26, 26, 24, 24, 22, 22, 56, 56, 58, 22, 20, 20, 18, 18, 16, 60, 62, 16, 14, 10, 12, 14, 12, 10, 8, 8, 6, 6, 4, 4, 2, 2, 0, 0, 48, 44, 46, 46, 48, 58, 46, 30, 32, 32, 34], "width": 132, "height": 107}}, "Drips": {"Drips": {"name": "SwampFloaters/DripsSpiker", "type": "mesh", "uvs": [1, 0.29318, 0.99999, 0.47755, 1, 0.63558, 0.84464, 0.63031, 0.83599, 0.4836, 0.80527, 0.28265, 0.78067, 0.68298, 0.78559, 1, 0.56906, 0.99999, 0.53953, 0.67245, 0.51985, 0.43541, 0.44111, 0.44067, 0.41879, 0.72349, 0.26888, 0.70445, 0.24852, 0.45427, 0.22459, 0.27211, 0.19831, 0.49008, 0.18662, 0.81234, 0.00808, 0.80879, 0.00493, 0.49862, 1e-05, 0.27162, 0, 0, 1, 0], "triangles": [19, 17, 18, 12, 13, 11, 6, 9, 10, 3, 1, 2, 8, 9, 6, 8, 6, 7, 6, 10, 5, 19, 20, 16, 1, 4, 0, 14, 15, 11, 11, 15, 10, 0, 5, 22, 5, 15, 22, 15, 20, 21, 16, 20, 15, 0, 4, 5, 10, 15, 5, 22, 15, 21, 16, 17, 19, 13, 14, 11, 3, 4, 1], "vertices": [3, 2, -22.69, -69.48, 0.54585, 7, 17.43, -72.93, 0.064, 3, 17.43, 69.91, 0.39015, 3, 2, -30.79, -68.74, 0.45029, 7, 9.33, -72.19, 0.12928, 3, 30.51, 69.44, 0.42044, 3, 2, -37.95, -68.12, 0.3256, 7, 2.17, -71.57, 0.32704, 3, 41.73, 69.05, 0.34736, 3, 2, -37.31, -55.14, 0.3256, 7, 2.81, -58.59, 0.32704, 3, 40.89, 56.07, 0.34736, 3, 2, -30.93, -55.01, 0.45029, 7, 9.19, -58.47, 0.12928, 3, 30.45, 55.72, 0.42044, 3, 2, -21.6, -53.21, 0.54585, 7, 18.52, -56.66, 0.064, 3, 16.1, 53.66, 0.39015, 3, 2, -40.93, -49.71, 0.3256, 7, -0.82, -53.16, 0.32704, 3, 44.43, 50.59, 0.34736, 3, 2, -62.82, -49.64, 0.06392, 7, -22.7, -53.09, 0.51552, 3, 71.16, 50.58, 0.42056, 3, 2, -62.44, -31.51, 0.16597, 7, -22.33, -34.97, 0.44864, 3, 68.8, 32.32, 0.38539, 3, 2, -39.77, -29.56, 0.3256, 7, 0.35, -33.01, 0.32704, 3, 42.97, 30.46, 0.34736, 3, 2, -26.96, -28.64, 0.45029, 7, 13.16, -32.1, 0.12928, 3, 26.09, 29.41, 0.42044, 3, 2, -27.2, -22.05, 0.45029, 7, 12.92, -25.51, 0.12928, 3, 26.23, 22.82, 0.42044, 3, 2, -43.18, -19.38, 0.3256, 7, -3.06, -22.84, 0.32704, 3, 46.23, 20.24, 0.34736, 3, 2, -41.57, -6.88, 0.3256, 7, -1.45, -10.33, 0.32704, 3, 44.43, 7.75, 0.34736, 3, 2, -27.83, -5.93, 0.45029, 7, 12.29, -9.38, 0.12928, 3, 26.62, 6.69, 0.42044, 3, 2, -19.84, -4.66, 0.54585, 7, 20.28, -8.12, 0.064, 3, 13.62, 5.15, 0.39015, 3, 2, -30.28, -1.67, 0.45029, 7, 9.84, -5.13, 0.12928, 3, 29.01, 2.4, 0.42044, 3, 2, -49.08, 0.16, 0.3256, 7, -8.96, -3.29, 0.32704, 3, 51.84, 0.6, 0.34736, 3, 2, -48.52, 15.09, 0.3256, 7, -8.4, 11.63, 0.32704, 3, 51.05, -14.31, 0.34736, 3, 2, -30.55, 14.51, 0.45029, 7, 9.57, 11.06, 0.12928, 3, 29.04, -13.79, 0.42044, 3, 2, -19.42, 14.12, 0.54585, 7, 20.7, 10.66, 0.064, 3, 12.92, -13.63, 0.39015, 3, 2, -4.81, 13.27, 0.60653, 7, 35.3, 9.82, 0.064, 3, -6.36, -12.94, 0.32947, 3, 2, -6.56, -70.36, 0.60653, 7, 33.56, -73.81, 0.064, 3, -3.37, 70.66, 0.32947], "hull": 23, "edges": [42, 44, 40, 42, 36, 34, 36, 38, 38, 40, 34, 32, 32, 30, 26, 24, 24, 22, 22, 20, 20, 18, 18, 16, 14, 16, 14, 12, 12, 10, 6, 4, 4, 2, 10, 8, 8, 6, 26, 28, 28, 30, 2, 0, 0, 44], "width": 76, "height": 71}}, "Drips2": {"Drips": {"name": "SwampFloaters/DripsSpiker", "type": "mesh", "uvs": [1, 0.29318, 0.99999, 0.47755, 1, 0.63558, 0.84464, 0.63031, 0.83599, 0.4836, 0.80527, 0.28265, 0.78067, 0.68298, 0.78559, 1, 0.56906, 0.99999, 0.53953, 0.67245, 0.51985, 0.43541, 0.44111, 0.44067, 0.40667, 0.60924, 0.26888, 0.60924, 0.24852, 0.45427, 0.22459, 0.27211, 0.19831, 0.49008, 0.17046, 0.74093, 0, 0.70405, 0.00493, 0.49862, 1e-05, 0.27162, 0, 0, 1, 0], "triangles": [16, 18, 19, 8, 6, 7, 12, 14, 11, 13, 14, 12, 3, 4, 1, 3, 1, 2, 6, 9, 10, 17, 18, 16, 8, 9, 6, 22, 15, 21, 11, 15, 10, 15, 20, 21, 5, 15, 22, 0, 5, 22, 10, 15, 5, 14, 15, 11, 0, 4, 5, 1, 4, 0, 16, 20, 15, 19, 20, 16, 6, 10, 5], "vertices": [3, 2, -10.78, 77.8, 0.49248, 7, 29.34, 74.35, 0.15552, 3, 3.34, -77.17, 0.352, 3, 2, -17.5, 78.51, 0.34272, 7, 22.62, 75.06, 0.33728, 3, 15.04, -77.59, 0.32, 3, 2, -23.48, 79.11, 0.22496, 7, 16.64, 75.65, 0.53504, 3, 25.07, -77.95, 0.24, 3, 2, -23.42, 66.11, 0.22496, 7, 16.7, 62.66, 0.53504, 3, 25.2, -64.96, 0.24, 3, 2, -18.17, 64.82, 0.34272, 7, 21.95, 61.36, 0.33728, 3, 15.91, -63.9, 0.32, 3, 2, -10.45, 61.51, 0.49248, 7, 29.67, 58.06, 0.15552, 3, 3.25, -60.88, 0.352, 3, 2, -26.87, 60.84, 0.22496, 7, 13.25, 57.38, 0.53504, 3, 28.73, -59.73, 0.24, 3, 2, -45.91, 62.73, 0.03648, 7, -5.79, 59.28, 0.72352, 3, 52.58, -61.53, 0.24, 3, 2, -46.47, 44.23, 0.10336, 7, -6.35, 40.77, 0.65664, 3, 51.7, -43.17, 0.24, 3, 2, -26.62, 40.67, 0.22496, 7, 13.49, 37.22, 0.53504, 3, 28.78, -39.56, 0.24, 3, 2, -15.66, 38.33, 0.34272, 7, 24.46, 34.87, 0.33728, 3, 13.8, -37.38, 0.32, 3, 2, -16.13, 31.75, 0.34272, 7, 23.99, 28.3, 0.33728, 3, 14.37, -30.82, 0.32, 3, 2, -22.84, 29.48, 0.22496, 7, 17.28, 26.03, 0.53504, 3, 25.17, -28.32, 0.24, 3, 2, -23.08, 17.96, 0.22496, 7, 17.04, 14.51, 0.53504, 3, 25.58, -16.81, 0.24, 3, 2, -17.33, 15.67, 0.34272, 7, 22.79, 12.22, 0.33728, 3, 15.8, -14.76, 0.32, 3, 2, -10.79, 12.96, 0.49248, 7, 29.32, 9.51, 0.15552, 3, 4.31, -12.34, 0.352, 3, 2, -19.69, 11.52, 0.34272, 7, 20.43, 8.07, 0.33728, 3, 18.23, -10.64, 0.32, 3, 2, -31.62, 9.91, 0.22496, 7, 8.5, 6.46, 0.53504, 3, 34.23, -8.88, 0.24, 3, 2, -29.57, -4.38, 0.22496, 7, 10.55, -7.84, 0.53504, 3, 32.4, 5.44, 0.24, 3, 2, -20.57, -4.63, 0.34272, 7, 19.55, -8.08, 0.33728, 3, 19.34, 5.49, 0.32, 3, 2, -11.15, -5.81, 0.49248, 7, 28.96, -9.26, 0.15552, 3, 4.95, 6.42, 0.352, 2, 2, 1.42, -6.61, 0.648, 3, -12.29, 7.04, 0.352, 2, 2, 3.16, 76.97, 0.648, 3, -15.27, -76.51, 0.352], "hull": 23, "edges": [42, 44, 40, 42, 36, 34, 36, 38, 38, 40, 34, 32, 32, 30, 26, 24, 24, 22, 22, 20, 20, 18, 18, 16, 14, 16, 14, 12, 12, 10, 6, 4, 4, 2, 10, 8, 8, 6, 26, 28, 28, 30, 2, 0, 0, 44], "width": 76, "height": 71}}, "EyeExploder": {"EyeMiddle_Closed": {"name": "SwampFloaters/ExploderBigEye1", "x": 0.5, "y": 1.62, "rotation": 90, "width": 71, "height": 61}, "EyeMiddle": {"name": "SwampFloaters/ExploderBigEye1", "x": 0.5, "y": 1.62, "rotation": 90, "width": 71, "height": 61}}, "EyeExploder2": {"EyeMiddle": {"name": "SwampFloaters/ExploderBigEye2", "x": 0.5, "y": 1.62, "rotation": 90, "width": 71, "height": 61}, "EyeMiddle_Closed": {"name": "SwampFloaters/ExploderBigEye2", "x": 0.5, "y": 1.62, "rotation": 90, "width": 71, "height": 61}}, "Head": {"Head": {"name": "SwampFloaters/SpikerHead", "type": "mesh", "uvs": [0.80334, 0.08714, 1, 0.27006, 0.96533, 0.4814, 1, 0.5742, 0.97744, 0.67663, 0.9748, 0.91529, 0.9145, 0.96734, 0.83643, 0.94745, 0.77636, 0.99213, 0.71218, 0.96894, 0.673, 1, 0.61611, 1, 0.56312, 0.98391, 0.53207, 1, 0.47481, 0.99999, 0.42002, 0.98093, 0.36119, 0.99903, 0.29437, 0.97311, 0.20174, 1, 0.167, 0.91902, 0.09079, 0.96209, 0.0206, 0.85416, 0.07878, 0.79069, 0, 0.61998, 0.00849, 0.41067, 0.0717, 0.00713, 0.24556, 0.00494, 0.35173, 0.0036, 0.48717, 0.00189, 0.63676, 0, 0.37419, 0.87945, 0.50757, 0.85617, 0.67947, 0.83643, 0.23791, 0.85188, 0.64312, 0.56292, 0.49617, 0.5587, 0.36495, 0.56331, 0.80798, 0.88251, 0.81887, 0.81151, 0.7903, 0.55405, 0.23185, 0.67449, 0.22197, 0.53902, 0.23676, 0.35761, 0.48994, 0.34114, 0.3663, 0.34114, 0.62831, 0.33126, 0.79023, 0.33456], "triangles": [34, 45, 46, 31, 35, 34, 31, 36, 35, 35, 45, 34, 36, 44, 35, 35, 43, 45, 44, 43, 35, 36, 42, 44, 20, 21, 22, 6, 7, 37, 19, 20, 22, 5, 6, 37, 8, 9, 37, 19, 33, 17, 16, 17, 30, 32, 11, 12, 14, 15, 31, 7, 8, 37, 15, 16, 30, 31, 13, 14, 9, 10, 32, 18, 19, 17, 12, 13, 31, 10, 11, 32, 45, 28, 29, 45, 29, 0, 46, 45, 0, 1, 46, 0, 43, 28, 45, 44, 27, 28, 43, 44, 28, 42, 25, 26, 26, 27, 44, 42, 26, 44, 24, 25, 42, 2, 46, 1, 41, 24, 42, 39, 46, 2, 34, 46, 39, 23, 24, 41, 23, 41, 40, 3, 39, 2, 4, 39, 3, 22, 23, 40, 38, 39, 4, 32, 34, 39, 32, 39, 38, 30, 33, 40, 22, 40, 33, 31, 34, 32, 31, 30, 36, 30, 40, 36, 37, 32, 38, 5, 38, 4, 37, 38, 5, 19, 22, 33, 9, 32, 37, 17, 33, 30, 15, 30, 31, 12, 31, 32, 41, 42, 36, 40, 41, 36], "vertices": [1, 5, 195.1, -85.11, 1, 1, 5, 150.46, -136.56, 1, 1, 5, 100.35, -126.28, 1, 2, 5, 78.06, -135.05, 0.856, 3, -87.11, 137.18, 0.144, 2, 5, 53.81, -128.54, 0.68, 3, -62.95, 130.3, 0.32, 2, 5, -2.99, -126.65, 0.408, 3, -6.19, 127.57, 0.592, 2, 5, -15.05, -110.34, 0.408, 3, 5.63, 111.08, 0.592, 2, 5, -9.88, -89.65, 0.408, 3, 0.15, 90.47, 0.592, 2, 5, -20.18, -73.43, 0.408, 3, 10.21, 74.11, 0.592, 2, 5, -14.31, -56.46, 0.408, 3, 4.08, 57.22, 0.592, 2, 5, -21.48, -45.88, 0.408, 3, 11.1, 46.53, 0.592, 2, 5, -21.18, -30.73, 0.408, 3, 10.57, 31.39, 0.592, 2, 5, -17.06, -16.7, 0.408, 3, 6.24, 17.42, 0.592, 2, 5, -20.71, -8.35, 0.408, 3, 9.76, 9.02, 0.592, 2, 5, -20.39, 6.89, 0.408, 3, 9.23, -6.21, 0.592, 2, 5, -15.55, 21.39, 0.408, 3, 4.17, -20.64, 0.592, 2, 5, -19.52, 37.14, 0.408, 3, 7.91, -36.44, 0.592, 2, 5, -12.98, 54.8, 0.408, 3, 1.11, -54.01, 0.592, 2, 5, -18.87, 79.59, 0.408, 3, 6.62, -78.89, 0.592, 2, 5, 0.6, 88.44, 0.408, 3, -12.98, -87.44, 0.592, 2, 5, -9.23, 108.95, 0.408, 3, -3.45, -108.09, 0.592, 2, 5, 16.85, 127.1, 0.408, 3, -29.8, -125.86, 0.592, 2, 5, 31.64, 111.29, 0.68, 3, -44.35, -109.83, 0.32, 2, 5, 72.71, 131.42, 0.856, 3, -85.72, -129.35, 0.144, 1, 5, 122.49, 128.12, 1, 2, 5, 218.2, 109.29, 0.9931, 3, -230.87, -105.06, 0.0069, 2, 5, 217.76, 62.99, 0.99522, 3, -229.74, -58.77, 0.00478, 2, 5, 217.49, 34.72, 0.99652, 3, -229.05, -30.5, 0.00348, 2, 5, 217.15, -1.35, 0.99817, 3, -228.16, 5.56, 0.00183, 2, 5, 216.77, -41.19, 0.99999, 3, -227.19, 45.39, 1e-05, 3, 5, 8.87, 33.08, 0.59296, 3, -20.42, -31.97, 0.27904, 6, 36.64, -34.52, 0.128, 3, 5, 13.67, -2.55, 0.52768, 3, -24.7, 3.73, 0.24832, 6, 31.84, 1.1, 0.224, 3, 5, 17.42, -48.41, 0.61472, 3, -27.76, 49.64, 0.28928, 6, 28.09, 46.97, 0.096, 3, 5, 16.19, 69.23, 0.49504, 3, -28.28, -68, 0.23296, 6, 29.32, -70.67, 0.272, 3, 5, 82.73, -40.09, 0.32186, 3, -93.18, 42.29, 0.05414, 6, -37.22, 38.65, 0.624, 1, 6, -39.04, -0.46, 1, 3, 5, 84.18, 33.97, 0.32186, 3, -95.74, -31.74, 0.05414, 6, -38.67, -35.42, 0.624, 2, 5, 5.73, -82.4, 0.68, 3, -15.57, 83.45, 0.32, 2, 5, 22.58, -85.65, 0.68, 3, -32.36, 86.95, 0.32, 3, 5, 84.02, -79.32, 0.63686, 3, -93.9, 81.54, 0.10714, 6, -38.51, 77.88, 0.256, 3, 5, 58.45, 69.96, 0.50592, 3, -70.55, -68.11, 0.23808, 6, -12.94, -71.41, 0.256, 3, 5, 90.75, 71.92, 0.54099, 3, -102.88, -69.59, 0.09101, 6, -45.24, -73.37, 0.368, 3, 5, 133.86, 67.08, 0.58696, 3, -145.9, -64.11, 0.06104, 6, -88.35, -68.53, 0.352, 3, 5, 136.37, -0.41, 0.34419, 3, -147.41, 3.41, 0.03181, 6, -90.86, -1.04, 0.624, 3, 5, 137.06, 32.51, 0.34317, 3, -148.59, -29.49, 0.03283, 6, -91.55, -33.96, 0.624, 3, 5, 137.96, -37.3, 0.3464, 3, -148.45, 40.32, 0.0296, 6, -92.44, 35.85, 0.624, 3, 5, 136.27, -80.39, 0.71714, 3, -146.13, 83.39, 0.04286, 6, -90.76, 78.95, 0.24], "hull": 30, "edges": [22, 20, 20, 18, 18, 16, 16, 14, 22, 24, 26, 24, 28, 26, 28, 30, 32, 30, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 12, 14, 12, 10, 32, 60, 26, 62, 20, 64, 38, 66, 42, 44, 44, 46, 10, 8, 6, 8, 64, 68, 62, 70, 60, 72, 6, 4, 48, 50, 58, 0, 0, 2, 2, 4, 46, 48, 14, 74, 74, 76, 76, 78, 66, 80, 80, 82, 82, 84, 56, 58, 70, 86, 86, 56, 72, 88, 68, 90, 90, 58, 78, 92, 92, 0, 50, 52, 84, 52, 52, 54, 54, 56], "width": 267, "height": 238}, "Head2": {"name": "SwampFloaters/SpikerHead2", "type": "mesh", "uvs": [0.80334, 0.08714, 1, 0.27006, 0.96533, 0.4814, 1, 0.5742, 0.97744, 0.67663, 0.9748, 0.91529, 0.9145, 0.96734, 0.83643, 0.94745, 0.77636, 0.99213, 0.71218, 0.96894, 0.673, 1, 0.61611, 1, 0.56312, 0.98391, 0.53207, 1, 0.47481, 0.99999, 0.42002, 0.98093, 0.36119, 0.99903, 0.29437, 0.97311, 0.20174, 1, 0.167, 0.91902, 0.09079, 0.96209, 0.0206, 0.85416, 0.07878, 0.79069, 0, 0.61998, 0.00849, 0.41067, 0.0717, 0.00713, 0.24556, 0.00494, 0.35173, 0.0036, 0.48717, 0.00189, 0.63676, 0, 0.37419, 0.87945, 0.50757, 0.85617, 0.67947, 0.83643, 0.23791, 0.85188, 0.64312, 0.56292, 0.49617, 0.5587, 0.36495, 0.56331, 0.80798, 0.88251, 0.81887, 0.81151, 0.7903, 0.55405, 0.23185, 0.67449, 0.22197, 0.53902, 0.23676, 0.35761, 0.48994, 0.34114, 0.3663, 0.34114, 0.62831, 0.33126, 0.79023, 0.33456], "triangles": [34, 45, 46, 31, 35, 34, 31, 36, 35, 35, 45, 34, 36, 44, 35, 35, 43, 45, 44, 43, 35, 36, 42, 44, 20, 21, 22, 6, 7, 37, 19, 20, 22, 5, 6, 37, 8, 9, 37, 19, 33, 17, 16, 17, 30, 32, 11, 12, 14, 15, 31, 7, 8, 37, 15, 16, 30, 31, 13, 14, 9, 10, 32, 18, 19, 17, 12, 13, 31, 10, 11, 32, 45, 28, 29, 45, 29, 0, 46, 45, 0, 1, 46, 0, 43, 28, 45, 44, 27, 28, 43, 44, 28, 42, 25, 26, 26, 27, 44, 42, 26, 44, 24, 25, 42, 2, 46, 1, 41, 24, 42, 39, 46, 2, 34, 46, 39, 23, 24, 41, 23, 41, 40, 3, 39, 2, 4, 39, 3, 22, 23, 40, 38, 39, 4, 32, 34, 39, 32, 39, 38, 30, 33, 40, 22, 40, 33, 31, 34, 32, 31, 30, 36, 30, 40, 36, 37, 32, 38, 5, 38, 4, 37, 38, 5, 19, 22, 33, 9, 32, 37, 17, 33, 30, 15, 30, 31, 12, 31, 32, 41, 42, 36, 40, 41, 36], "vertices": [1, 5, 195.1, -85.11, 1, 1, 5, 150.46, -136.56, 1, 1, 5, 100.35, -126.28, 1, 2, 5, 78.06, -135.05, 0.856, 3, -87.11, 137.18, 0.144, 2, 5, 53.81, -128.54, 0.68, 3, -62.95, 130.3, 0.32, 2, 5, -2.99, -126.65, 0.408, 3, -6.19, 127.57, 0.592, 2, 5, -15.05, -110.34, 0.408, 3, 5.63, 111.08, 0.592, 2, 5, -9.88, -89.65, 0.408, 3, 0.15, 90.47, 0.592, 2, 5, -20.18, -73.43, 0.408, 3, 10.21, 74.11, 0.592, 2, 5, -14.31, -56.46, 0.408, 3, 4.08, 57.22, 0.592, 2, 5, -21.48, -45.88, 0.408, 3, 11.1, 46.53, 0.592, 2, 5, -21.18, -30.73, 0.408, 3, 10.57, 31.39, 0.592, 2, 5, -17.06, -16.7, 0.408, 3, 6.24, 17.42, 0.592, 2, 5, -20.71, -8.35, 0.408, 3, 9.76, 9.02, 0.592, 2, 5, -20.39, 6.89, 0.408, 3, 9.23, -6.21, 0.592, 2, 5, -15.55, 21.39, 0.408, 3, 4.17, -20.64, 0.592, 2, 5, -19.52, 37.14, 0.408, 3, 7.91, -36.44, 0.592, 2, 5, -12.98, 54.8, 0.408, 3, 1.11, -54.01, 0.592, 2, 5, -18.87, 79.59, 0.408, 3, 6.62, -78.89, 0.592, 2, 5, 0.6, 88.44, 0.408, 3, -12.98, -87.44, 0.592, 2, 5, -9.23, 108.95, 0.408, 3, -3.45, -108.09, 0.592, 2, 5, 16.85, 127.1, 0.408, 3, -29.8, -125.86, 0.592, 2, 5, 31.64, 111.29, 0.68, 3, -44.35, -109.83, 0.32, 2, 5, 72.71, 131.42, 0.856, 3, -85.72, -129.35, 0.144, 1, 5, 122.49, 128.12, 1, 2, 5, 218.2, 109.29, 0.9931, 3, -230.87, -105.06, 0.0069, 2, 5, 217.76, 62.99, 0.99522, 3, -229.74, -58.77, 0.00478, 2, 5, 217.49, 34.72, 0.99652, 3, -229.05, -30.5, 0.00348, 2, 5, 217.15, -1.35, 0.99817, 3, -228.16, 5.56, 0.00183, 2, 5, 216.77, -41.19, 0.99999, 3, -227.19, 45.39, 1e-05, 3, 5, 8.87, 33.08, 0.59296, 3, -20.42, -31.97, 0.27904, 6, 36.64, -34.52, 0.128, 3, 5, 13.67, -2.55, 0.52768, 3, -24.7, 3.73, 0.24832, 6, 31.84, 1.1, 0.224, 3, 5, 17.42, -48.41, 0.61472, 3, -27.76, 49.64, 0.28928, 6, 28.09, 46.97, 0.096, 3, 5, 16.19, 69.23, 0.49504, 3, -28.28, -68, 0.23296, 6, 29.32, -70.67, 0.272, 3, 5, 82.73, -40.09, 0.32186, 3, -93.18, 42.29, 0.05414, 6, -37.22, 38.65, 0.624, 1, 6, -39.04, -0.46, 1, 3, 5, 84.18, 33.97, 0.32186, 3, -95.74, -31.74, 0.05414, 6, -38.67, -35.42, 0.624, 2, 5, 5.73, -82.4, 0.68, 3, -15.57, 83.45, 0.32, 2, 5, 22.58, -85.65, 0.68, 3, -32.36, 86.95, 0.32, 3, 5, 84.02, -79.32, 0.63686, 3, -93.9, 81.54, 0.10714, 6, -38.51, 77.88, 0.256, 3, 5, 58.45, 69.96, 0.50592, 3, -70.55, -68.11, 0.23808, 6, -12.94, -71.41, 0.256, 3, 5, 90.75, 71.92, 0.54099, 3, -102.88, -69.59, 0.09101, 6, -45.24, -73.37, 0.368, 3, 5, 133.86, 67.08, 0.58696, 3, -145.9, -64.11, 0.06104, 6, -88.35, -68.53, 0.352, 3, 5, 136.37, -0.41, 0.34419, 3, -147.41, 3.41, 0.03181, 6, -90.86, -1.04, 0.624, 3, 5, 137.06, 32.51, 0.34317, 3, -148.59, -29.49, 0.03283, 6, -91.55, -33.96, 0.624, 3, 5, 137.96, -37.3, 0.3464, 3, -148.45, 40.32, 0.0296, 6, -92.44, 35.85, 0.624, 3, 5, 136.27, -80.39, 0.71714, 3, -146.13, 83.39, 0.04286, 6, -90.76, 78.95, 0.24], "hull": 30, "edges": [22, 20, 20, 18, 18, 16, 16, 14, 22, 24, 26, 24, 28, 26, 28, 30, 32, 30, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 12, 14, 12, 10, 32, 60, 26, 62, 20, 64, 38, 66, 42, 44, 44, 46, 10, 8, 6, 8, 64, 68, 62, 70, 60, 72, 6, 4, 48, 50, 58, 0, 0, 2, 2, 4, 46, 48, 14, 74, 74, 76, 76, 78, 66, 80, 80, 82, 82, 84, 56, 58, 70, 86, 86, 56, 72, 88, 68, 90, 90, 58, 78, 92, 92, 0, 50, 52, 84, 52, 52, 54, 54, 56], "width": 267, "height": 238}}, "Skirt": {"Skirt": {"name": "SwampFloaters/SpikerSkirt", "type": "mesh", "uvs": [0.99999, 0.43314, 0.99999, 0.69863, 0.99999, 0.86303, 0.87597, 0.85189, 0.85272, 0.69188, 0.83426, 0.88191, 0.76164, 1, 0.70441, 0.86278, 0.68467, 0.7033, 0.67449, 0.88127, 0.63757, 1, 0.54957, 1, 0.5108, 0.88648, 0.4948, 0.7143, 0.48811, 0.87964, 0.44495, 1, 0.35562, 0.99852, 0.31402, 0.86994, 0.30893, 0.69074, 0.30856, 0.90512, 0.23242, 1, 0.16547, 0.89027, 0.14803, 0.70036, 0.13658, 0.85688, 0.04994, 0.87172, 0.01597, 0.7014, 0, 0.48215, 0, 0.21873, 0, 0, 0.14068, 0, 0.30129, 0, 0.49206, 0, 0.66565, 0, 0.8478, 0, 1, 0, 0.67672, 0.45582, 0.84809, 0.44377, 0.14672, 0.47982, 0.30733, 0.47461, 0.49268, 0.46418], "triangles": [19, 20, 18, 14, 15, 13, 17, 13, 15, 18, 20, 21, 8, 9, 12, 5, 7, 4, 23, 24, 22, 3, 1, 2, 13, 39, 35, 8, 35, 36, 25, 26, 37, 22, 37, 38, 4, 36, 0, 18, 38, 39, 27, 29, 37, 37, 29, 30, 38, 30, 31, 39, 31, 32, 35, 32, 33, 0, 33, 34, 27, 28, 29, 26, 27, 37, 37, 30, 38, 38, 31, 39, 39, 32, 35, 35, 33, 36, 36, 33, 0, 18, 39, 13, 13, 35, 8, 8, 36, 4, 25, 37, 22, 22, 38, 18, 1, 4, 0, 21, 22, 18, 8, 12, 13, 17, 18, 13, 7, 8, 4, 22, 24, 25, 3, 4, 1, 6, 7, 5, 11, 12, 9, 10, 11, 9, 17, 15, 16], "vertices": [3, 3, -10.15, 91.72, 0.19136, 5, 0.43, -90.75, 0.72864, 7, 57.9, -63.2, 0.08, 3, 3, 8.17, 91.25, 0.25453, 5, -17.89, -90.54, 0.52147, 7, 41.62, -63.75, 0.224, 3, 3, 19.51, 90.93, 0.31738, 5, -29.23, -90.4, 0.37862, 7, 32.85, -64.34, 0.304, 3, 3, 17.91, 67.78, 0.31738, 5, -27.98, -67.23, 0.37862, 7, 34.2, -45.88, 0.304, 3, 3, 6.72, 63.76, 0.25453, 5, -16.85, -63.04, 0.52147, 7, 42.79, -42.08, 0.224, 3, 3, 19.7, 59.92, 0.31738, 5, -29.89, -59.39, 0.37862, 7, 32.72, -39.6, 0.304, 3, 3, 27.36, 46.05, 0.45101, 5, -37.75, -45.64, 0.24499, 7, 29.02, -29.04, 0.304, 3, 3, 17.52, 35.7, 0.31738, 5, -28.06, -35.14, 0.37862, 7, 34.56, -20.28, 0.304, 3, 3, 6.39, 32.34, 0.25453, 5, -16.98, -31.62, 0.52147, 7, 42.98, -17.33, 0.224, 3, 3, 18.59, 30.06, 0.31738, 5, -29.22, -29.52, 0.37862, 7, 33.67, -15.79, 0.304, 3, 3, 26.53, 22.86, 0.45101, 5, -37.27, -22.44, 0.24499, 7, 29.83, -10.23, 0.304, 3, 3, 25.95, 6.42, 0.45101, 5, -36.92, -5.99, 0.24499, 7, 30.4, 3.12, 0.304, 3, 3, 17.86, -0.55, 0.31738, 5, -28.94, 1.1, 0.37862, 7, 34.31, 8.62, 0.304, 3, 3, 5.88, -3.15, 0.25453, 5, -17, 3.87, 0.52147, 7, 43.3, 10.64, 0.224, 3, 3, 17.24, -4.77, 0.31738, 5, -28.38, 5.33, 0.37862, 7, 34.83, 11.99, 0.304, 3, 3, 25.25, -13.13, 0.45101, 5, -36.52, 13.56, 0.24499, 7, 31.08, 18.98, 0.304, 3, 3, 24.55, -29.81, 0.45101, 5, -36.07, 30.26, 0.24499, 7, 31.74, 32.53, 0.304, 3, 3, 15.41, -37.28, 0.31738, 5, -27.03, 37.86, 0.37862, 7, 36.39, 37.92, 0.304, 3, 3, 3.02, -37.8, 0.25453, 5, -14.65, 38.57, 0.52147, 7, 45.7, 37.96, 0.224, 3, 3, 17.8, -38.39, 0.31738, 5, -29.44, 38.93, 0.37862, 7, 34.4, 38.8, 0.304, 3, 3, 23.83, -52.84, 0.45101, 5, -35.69, 53.29, 0.24499, 7, 32.46, 51.21, 0.304, 3, 3, 15.82, -65.09, 0.31738, 5, -27.86, 65.67, 0.37862, 7, 36.07, 60.1, 0.304, 3, 3, 2.61, -67.88, 0.25453, 5, -14.69, 68.65, 0.52147, 7, 45.95, 61.66, 0.224, 3, 3, 13.32, -70.41, 0.31738, 5, -25.44, 71.02, 0.37862, 7, 38.15, 64.35, 0.304, 3, 3, 13.77, -86.64, 0.31738, 5, -26.13, 87.24, 0.37862, 7, 37.8, 77.29, 0.304, 3, 3, 1.8, -92.55, 0.25453, 5, -14.25, 93.33, 0.52147, 7, 46.58, 81.1, 0.224, 3, 3, -13.43, -94.93, 0.19136, 5, 0.94, 95.93, 0.72864, 7, 59.5, 82.18, 0.08, 1, 5, 19.11, 95.51, 1, 1, 5, 34.2, 95.2, 1, 1, 5, 33.65, 68.97, 1, 1, 5, 33.03, 39.02, 1, 1, 5, 32.29, 3.45, 1, 1, 5, 31.61, -28.92, 1, 1, 5, 30.91, -62.89, 1, 1, 5, 30.32, -91.27, 1, 3, 3, -10.74, 31.37, 0.19136, 5, 0.12, -30.39, 0.72864, 7, 57.98, -16.19, 0.08, 3, 3, -10.43, 63.36, 0.19136, 5, 0.29, -62.39, 0.72864, 7, 57.94, -41.11, 0.08, 3, 3, -12.61, -67.56, 0.19136, 5, 0.53, 68.55, 0.72864, 7, 58.96, 60.85, 0.08, 3, 3, -11.9, -37.58, 0.19136, 5, 0.26, 38.57, 0.72864, 7, 58.53, 37.51, 0.08, 3, 3, -11.38, -2.98, 0.19136, 5, 0.26, 3.97, 0.72864, 7, 58.32, 10.56, 0.08], "hull": 35, "edges": [54, 56, 52, 54, 52, 50, 50, 48, 48, 46, 46, 44, 44, 42, 42, 40, 40, 38, 38, 36, 36, 34, 34, 32, 30, 28, 28, 26, 26, 24, 24, 22, 20, 22, 20, 18, 18, 16, 16, 70, 70, 64, 16, 14, 14, 12, 12, 10, 10, 8, 8, 72, 64, 66, 66, 68, 72, 66, 8, 6, 6, 4, 44, 74, 36, 76, 56, 58, 74, 58, 58, 60, 76, 60, 26, 78, 60, 62, 62, 64, 78, 62, 0, 68, 32, 30, 0, 2, 2, 4], "width": 195, "height": 69}}, "Spike1": {"SwampFloaters/SpikerSpike1": {"x": 64.35, "y": 1.14, "rotation": -90.38, "width": 62, "height": 147}}, "Spike4": {"SwampFloaters/SpikerSpike2": {"x": 64.35, "y": 1.14, "rotation": -90.38, "width": 58, "height": 145}}, "Spike5": {"SwampFloaters/SpikerSpike1": {"x": 64.35, "y": 1.14, "rotation": -90.38, "width": 62, "height": 147}}, "SpikerEyes": {"SpikerEyes": {"name": "SwampFloaters/SpikerEyes2", "x": -11.22, "y": -4.75, "scaleX": 0.9786, "scaleY": 0.9786, "rotation": 90, "width": 181, "height": 61}, "SpikerEyes_Closed": {"name": "SwampFloaters/SpikerEyes2_Closed", "x": -11.22, "y": -4.75, "scaleX": 0.9786, "scaleY": 0.9786, "rotation": 90, "width": 181, "height": 61}}}}, {"name": "SpikerBig", "bones": ["HornLeft", "Spike10", "Spike7", "Spike8", "Spike6", "Spike1", "HornLeft2", "Spike2", "Spike5", "Spike11", "Spike4", "Spike9", "Spike3"], "attachments": {"BodyBtm": {"BodyBtm": {"name": "SwampFloaters/Tail_Spiker_Btm_Big", "type": "<PERSON><PERSON><PERSON>", "skin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parent": "BodyBtm", "width": 87, "height": 91}}, "BodyTop": {"BodyTop": {"name": "SwampFloaters/Tail_Spiker_Top_Big", "type": "<PERSON><PERSON><PERSON>", "skin": "Spiker", "parent": "BodyTop", "width": 132, "height": 107}}, "Drips": {"Drips": {"name": "SwampFloaters/DripsSpiker", "type": "mesh", "uvs": [1, 0.29318, 0.99999, 0.47755, 1, 0.63558, 0.84464, 0.63031, 0.83599, 0.4836, 0.80527, 0.28265, 0.78067, 0.68298, 0.78559, 1, 0.56906, 0.99999, 0.53953, 0.67245, 0.51985, 0.43541, 0.44111, 0.44067, 0.41879, 0.72349, 0.26888, 0.70445, 0.24852, 0.45427, 0.22459, 0.27211, 0.19831, 0.49008, 0.18662, 0.81234, 0.00808, 0.80879, 0.00493, 0.49862, 1e-05, 0.27162, 0, 0, 1, 0], "triangles": [19, 17, 18, 12, 13, 11, 6, 9, 10, 3, 1, 2, 8, 9, 6, 8, 6, 7, 6, 10, 5, 19, 20, 16, 1, 4, 0, 14, 15, 11, 11, 15, 10, 0, 5, 22, 5, 15, 22, 15, 20, 21, 16, 20, 15, 0, 4, 5, 10, 15, 5, 22, 15, 21, 16, 17, 19, 13, 14, 11, 3, 4, 1], "vertices": [3, 2, -22.69, -69.48, 0.54585, 7, 17.43, -72.93, 0.064, 3, 17.43, 69.91, 0.39015, 3, 2, -30.79, -68.74, 0.45029, 7, 9.33, -72.19, 0.12928, 3, 30.51, 69.44, 0.42044, 3, 2, -37.95, -68.12, 0.3256, 7, 2.17, -71.57, 0.32704, 3, 41.73, 69.05, 0.34736, 3, 2, -37.31, -55.14, 0.3256, 7, 2.81, -58.59, 0.32704, 3, 40.89, 56.07, 0.34736, 3, 2, -30.93, -55.01, 0.45029, 7, 9.19, -58.47, 0.12928, 3, 30.45, 55.72, 0.42044, 3, 2, -21.6, -53.21, 0.54585, 7, 18.52, -56.66, 0.064, 3, 16.1, 53.66, 0.39015, 3, 2, -40.93, -49.71, 0.3256, 7, -0.82, -53.16, 0.32704, 3, 44.43, 50.59, 0.34736, 3, 2, -62.82, -49.64, 0.06392, 7, -22.7, -53.09, 0.51552, 3, 71.16, 50.58, 0.42056, 3, 2, -62.44, -31.51, 0.16597, 7, -22.33, -34.97, 0.44864, 3, 68.8, 32.32, 0.38539, 3, 2, -39.77, -29.56, 0.3256, 7, 0.35, -33.01, 0.32704, 3, 42.97, 30.46, 0.34736, 3, 2, -26.96, -28.64, 0.45029, 7, 13.16, -32.1, 0.12928, 3, 26.09, 29.41, 0.42044, 3, 2, -27.2, -22.05, 0.45029, 7, 12.92, -25.51, 0.12928, 3, 26.23, 22.82, 0.42044, 3, 2, -43.18, -19.38, 0.3256, 7, -3.06, -22.84, 0.32704, 3, 46.23, 20.24, 0.34736, 3, 2, -41.57, -6.88, 0.3256, 7, -1.45, -10.33, 0.32704, 3, 44.43, 7.75, 0.34736, 3, 2, -27.83, -5.93, 0.45029, 7, 12.29, -9.38, 0.12928, 3, 26.62, 6.69, 0.42044, 3, 2, -19.84, -4.66, 0.54585, 7, 20.28, -8.12, 0.064, 3, 13.62, 5.15, 0.39015, 3, 2, -30.28, -1.67, 0.45029, 7, 9.84, -5.13, 0.12928, 3, 29.01, 2.4, 0.42044, 3, 2, -49.08, 0.16, 0.3256, 7, -8.96, -3.29, 0.32704, 3, 51.84, 0.6, 0.34736, 3, 2, -48.52, 15.09, 0.3256, 7, -8.4, 11.63, 0.32704, 3, 51.05, -14.31, 0.34736, 3, 2, -30.55, 14.51, 0.45029, 7, 9.57, 11.06, 0.12928, 3, 29.04, -13.79, 0.42044, 3, 2, -19.42, 14.12, 0.54585, 7, 20.7, 10.66, 0.064, 3, 12.92, -13.63, 0.39015, 3, 2, -4.81, 13.27, 0.60653, 7, 35.3, 9.82, 0.064, 3, -6.36, -12.94, 0.32947, 3, 2, -6.56, -70.36, 0.60653, 7, 33.56, -73.81, 0.064, 3, -3.37, 70.66, 0.32947], "hull": 23, "edges": [42, 44, 40, 42, 36, 34, 36, 38, 38, 40, 34, 32, 32, 30, 26, 24, 24, 22, 22, 20, 20, 18, 18, 16, 14, 16, 14, 12, 12, 10, 6, 4, 4, 2, 10, 8, 8, 6, 26, 28, 28, 30, 2, 0, 0, 44], "width": 76, "height": 71}}, "Drips2": {"Drips": {"name": "SwampFloaters/DripsSpiker", "type": "<PERSON><PERSON><PERSON>", "skin": "Spiker", "parent": "<PERSON><PERSON>s", "width": 76, "height": 71}}, "EyeExploder": {"EyeMiddle_Closed": {"name": "SwampFloaters/ExploderBigEye1", "x": 0.5, "y": 1.62, "rotation": 90, "width": 71, "height": 61}, "EyeMiddle": {"name": "SwampFloaters/ExploderBigEye1", "x": 0.5, "y": 1.62, "rotation": 90, "width": 71, "height": 61}}, "EyeExploder2": {"EyeMiddle": {"name": "SwampFloaters/ExploderBigEye2", "x": 0.5, "y": 1.62, "rotation": 90, "width": 71, "height": 61}, "EyeMiddle_Closed": {"name": "SwampFloaters/ExploderBigEye2", "x": 0.5, "y": 1.62, "rotation": 90, "width": 71, "height": 61}}, "Head": {"Head2": {"name": "SwampFloaters/SpikerHead2_Big", "type": "<PERSON><PERSON><PERSON>", "skin": "Spiker", "parent": "Head2", "width": 267, "height": 238}, "Head": {"name": "SwampFloaters/SpikerHead_Big", "type": "<PERSON><PERSON><PERSON>", "skin": "Spiker", "parent": "Head", "width": 267, "height": 238}}, "Skirt": {"Skirt": {"name": "SwampFloaters/SpikerSkirtBig", "type": "<PERSON><PERSON><PERSON>", "skin": "Spiker", "parent": "Skirt", "width": 195, "height": 69}}, "Spike1": {"SwampFloaters/SpikerSpike1": {"name": "SwampFloaters/SpikerSpike1_Big", "x": 64.35, "y": 1.14, "rotation": -90.38, "width": 62, "height": 147}}, "Spike4": {"SwampFloaters/SpikerSpike2": {"name": "SwampFloaters/SpikerSpike2_Big", "x": 64.35, "y": 1.14, "rotation": -90.38, "width": 58, "height": 145}}, "Spike5": {"SwampFloaters/SpikerSpike1": {"name": "SwampFloaters/SpikerSpike1_Big", "x": 64.35, "y": 1.14, "rotation": -90.38, "width": 62, "height": 147}}, "SpikerEyes": {"SpikerEyes": {"name": "SwampFloaters/SpikerEyes", "x": -31.21, "y": -4.33, "scaleX": 0.9786, "scaleY": 0.9786, "rotation": 90, "width": 181, "height": 104}, "SpikerEyes_Closed": {"name": "SwampFloaters/<PERSON><PERSON><PERSON><PERSON>_Closed", "x": -31.21, "y": -4.33, "scaleX": 0.9786, "scaleY": 0.9786, "rotation": 90, "width": 181, "height": 104}}}}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bones": ["HornLeft", "Spike10", "Spike8", "Spike1", "HornLeft2", "Spike5", "Spike4", "Spike9", "Spike11"], "attachments": {"BodyBtm": {"BodyBtm": {"name": "SwampFloaters/Tail_Spiker_Btm_Big", "type": "<PERSON><PERSON><PERSON>", "skin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parent": "BodyBtm", "width": 87, "height": 91}}, "BodyTop": {"BodyTop": {"name": "SwampFloaters/Tail_Spiker_Top_Big", "type": "<PERSON><PERSON><PERSON>", "skin": "Spiker", "parent": "BodyTop", "width": 132, "height": 107}}, "Drips": {"Drips": {"name": "SwampFloaters/DripsSpiker", "type": "mesh", "uvs": [1, 0.29318, 0.99999, 0.47755, 1, 0.63558, 0.84464, 0.63031, 0.83599, 0.4836, 0.80527, 0.28265, 0.78067, 0.68298, 0.78559, 1, 0.56906, 0.99999, 0.53953, 0.67245, 0.51985, 0.43541, 0.44111, 0.44067, 0.41879, 0.72349, 0.26888, 0.70445, 0.24852, 0.45427, 0.22459, 0.27211, 0.19831, 0.49008, 0.18662, 0.81234, 0.00808, 0.80879, 0.00493, 0.49862, 1e-05, 0.27162, 0, 0, 1, 0], "triangles": [3, 1, 2, 6, 9, 10, 12, 13, 11, 19, 17, 18, 8, 6, 7, 8, 9, 6, 3, 4, 1, 13, 14, 11, 16, 17, 19, 22, 15, 21, 10, 15, 5, 0, 4, 5, 16, 20, 15, 15, 20, 21, 5, 15, 22, 0, 5, 22, 11, 15, 10, 14, 15, 11, 1, 4, 0, 19, 20, 16, 6, 10, 5], "vertices": [3, 2, -22.69, -69.48, 0.54585, 7, 17.43, -72.93, 0.064, 3, 17.43, 69.91, 0.39015, 3, 2, -30.79, -68.74, 0.45029, 7, 9.33, -72.19, 0.12928, 3, 30.51, 69.44, 0.42044, 3, 2, -37.95, -68.12, 0.3256, 7, 2.17, -71.57, 0.32704, 3, 41.73, 69.05, 0.34736, 3, 2, -37.31, -55.14, 0.3256, 7, 2.81, -58.59, 0.32704, 3, 40.89, 56.07, 0.34736, 3, 2, -30.93, -55.01, 0.45029, 7, 9.19, -58.47, 0.12928, 3, 30.45, 55.72, 0.42044, 3, 2, -21.6, -53.21, 0.54585, 7, 18.52, -56.66, 0.064, 3, 16.1, 53.66, 0.39015, 3, 2, -40.93, -49.71, 0.3256, 7, -0.82, -53.16, 0.32704, 3, 44.43, 50.59, 0.34736, 3, 2, -62.82, -49.64, 0.06392, 7, -22.7, -53.09, 0.51552, 3, 71.16, 50.58, 0.42056, 3, 2, -62.44, -31.51, 0.16597, 7, -22.33, -34.97, 0.44864, 3, 68.8, 32.32, 0.38539, 3, 2, -39.77, -29.56, 0.3256, 7, 0.35, -33.01, 0.32704, 3, 42.97, 30.46, 0.34736, 3, 2, -26.96, -28.64, 0.45029, 7, 13.16, -32.1, 0.12928, 3, 26.09, 29.41, 0.42044, 3, 2, -27.2, -22.05, 0.45029, 7, 12.92, -25.51, 0.12928, 3, 26.23, 22.82, 0.42044, 3, 2, -43.18, -19.38, 0.3256, 7, -3.06, -22.84, 0.32704, 3, 46.23, 20.24, 0.34736, 3, 2, -41.57, -6.88, 0.3256, 7, -1.45, -10.33, 0.32704, 3, 44.43, 7.75, 0.34736, 3, 2, -27.83, -5.93, 0.45029, 7, 12.29, -9.38, 0.12928, 3, 26.62, 6.69, 0.42044, 3, 2, -19.84, -4.66, 0.54585, 7, 20.28, -8.12, 0.064, 3, 13.62, 5.15, 0.39015, 3, 2, -30.28, -1.67, 0.45029, 7, 9.84, -5.13, 0.12928, 3, 29.01, 2.4, 0.42044, 3, 2, -49.08, 0.16, 0.3256, 7, -8.96, -3.29, 0.32704, 3, 51.84, 0.6, 0.34736, 3, 2, -48.52, 15.09, 0.3256, 7, -8.4, 11.63, 0.32704, 3, 51.05, -14.31, 0.34736, 3, 2, -30.55, 14.51, 0.45029, 7, 9.57, 11.06, 0.12928, 3, 29.04, -13.79, 0.42044, 3, 2, -19.42, 14.12, 0.54585, 7, 20.7, 10.66, 0.064, 3, 12.92, -13.63, 0.39015, 3, 2, -4.81, 13.27, 0.60653, 7, 35.3, 9.82, 0.064, 3, -6.36, -12.94, 0.32947, 3, 2, -6.56, -70.36, 0.60653, 7, 33.56, -73.81, 0.064, 3, -3.37, 70.66, 0.32947], "hull": 23, "edges": [42, 44, 40, 42, 36, 34, 36, 38, 38, 40, 34, 32, 32, 30, 26, 24, 24, 22, 22, 20, 20, 18, 18, 16, 14, 16, 14, 12, 12, 10, 6, 4, 4, 2, 10, 8, 8, 6, 26, 28, 28, 30, 2, 0, 0, 44], "width": 76, "height": 71}}, "Drips2": {"Drips": {"name": "SwampFloaters/DripsSpiker", "type": "<PERSON><PERSON><PERSON>", "skin": "Spiker", "parent": "<PERSON><PERSON>s", "width": 76, "height": 71}}, "EyeExploder": {"EyeMiddle_Closed": {"name": "SwampFloaters/ExploderBigEye1", "x": 0.5, "y": 1.62, "rotation": 90, "width": 71, "height": 61}, "EyeMiddle": {"name": "SwampFloaters/ExploderBigEye1", "x": 0.5, "y": 1.62, "rotation": 90, "width": 71, "height": 61}}, "EyeExploder2": {"EyeMiddle": {"name": "SwampFloaters/ExploderBigEye2", "x": 0.5, "y": 1.62, "rotation": 90, "width": 71, "height": 61}, "EyeMiddle_Closed": {"name": "SwampFloaters/ExploderBigEye2", "x": 0.5, "y": 1.62, "rotation": 90, "width": 71, "height": 61}}, "Head": {"Head2": {"name": "SwampFloaters/SpikerHead2_Big", "type": "<PERSON><PERSON><PERSON>", "skin": "Spiker", "parent": "Head2", "width": 267, "height": 238}, "Head": {"name": "SwampFloaters/SpikerHead_Big", "type": "<PERSON><PERSON><PERSON>", "skin": "Spiker", "parent": "Head", "width": 267, "height": 238}}, "Skirt": {"Skirt": {"name": "SwampFloaters/SpikerSkirtBig", "type": "<PERSON><PERSON><PERSON>", "skin": "Spiker", "parent": "Skirt", "width": 195, "height": 69}}, "Spike1": {"SwampFloaters/SpikerSpike1": {"name": "SwampFloaters/SpikerSpike1_Big", "x": 64.35, "y": 1.14, "rotation": -90.38, "width": 62, "height": 147}}, "Spike4": {"SwampFloaters/SpikerSpike2": {"name": "SwampFloaters/SpikerSpike2_Big", "x": 64.35, "y": 1.14, "rotation": -90.38, "width": 58, "height": 145}}, "Spike5": {"SwampFloaters/SpikerSpike1": {"name": "SwampFloaters/SpikerSpike1_Big", "x": 64.35, "y": 1.14, "rotation": -90.38, "width": 62, "height": 147}}, "SpikerEyes": {"SpikerEyes": {"name": "SwampFloaters/SpikerEyes", "x": -31.21, "y": -4.33, "scaleX": 0.9786, "scaleY": 0.9786, "rotation": 90, "width": 181, "height": 104}, "SpikerEyes_Closed": {"name": "SwampFloaters/<PERSON><PERSON><PERSON><PERSON>_Closed", "x": -31.21, "y": -4.33, "scaleX": 0.9786, "scaleY": 0.9786, "rotation": 90, "width": 181, "height": 104}}, "SwampFloaters/SpikerHorn": {"SwampFloaters/SpikerHorn": {"x": 36.27, "y": 35.91, "rotation": -123.4, "width": 172, "height": 164}}, "SwampFloaters/SpikerHorn2": {"SwampFloaters/SpikerHorn": {"x": 32.33, "y": -44.33, "scaleX": -1, "rotation": -60.97, "width": 172, "height": 164}}}}, {"name": "Spiker<PERSON><PERSON><PERSON>_attacked1", "bones": ["HornLeft", "Spike10", "Spike8", "Spike1", "HornLeft2", "Spike5", "Spike4", "Spike9", "Spike11"], "attachments": {"BodyBtm": {"BodyBtm": {"name": "SwampFloaters/Tail_Spiker_Btm_Big", "type": "<PERSON><PERSON><PERSON>", "skin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parent": "BodyBtm", "width": 87, "height": 91}}, "BodyTop": {"BodyTop": {"name": "SwampFloaters/Tail_Spiker_Top_Big", "type": "<PERSON><PERSON><PERSON>", "skin": "Spiker", "parent": "BodyTop", "width": 132, "height": 107}}, "Drips": {"Drips": {"name": "SwampFloaters/DripsSpiker", "type": "<PERSON><PERSON><PERSON>", "skin": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parent": "<PERSON><PERSON>s", "width": 76, "height": 71}}, "Drips2": {"Drips": {"name": "SwampFloaters/DripsSpiker", "type": "<PERSON><PERSON><PERSON>", "skin": "Spiker", "parent": "<PERSON><PERSON>s", "width": 76, "height": 71}}, "EyeExploder": {"EyeMiddle_Closed": {"name": "SwampFloaters/ExploderBigEye1", "x": 0.5, "y": 1.62, "rotation": 90, "width": 71, "height": 61}, "EyeMiddle": {"name": "SwampFloaters/ExploderBigEye1", "x": 0.5, "y": 1.62, "rotation": 90, "width": 71, "height": 61}}, "EyeExploder2": {"EyeMiddle": {"name": "SwampFloaters/ExploderBigEye2", "x": 0.5, "y": 1.62, "rotation": 90, "width": 71, "height": 61}, "EyeMiddle_Closed": {"name": "SwampFloaters/ExploderBigEye2", "x": 0.5, "y": 1.62, "rotation": 90, "width": 71, "height": 61}}, "Head": {"Head2": {"name": "SwampFloaters/SpikerHead2_Big", "type": "<PERSON><PERSON><PERSON>", "skin": "Spiker", "parent": "Head2", "width": 267, "height": 238}, "Head": {"name": "SwampFloaters/SpikerHead_Big", "type": "<PERSON><PERSON><PERSON>", "skin": "Spiker", "parent": "Head", "width": 267, "height": 238}}, "Skirt": {"Skirt": {"name": "SwampFloaters/SpikerSkirtBig", "type": "<PERSON><PERSON><PERSON>", "skin": "Spiker", "parent": "Skirt", "width": 195, "height": 69}}, "Spike1": {"SwampFloaters/SpikerSpike1": {"name": "SwampFloaters/SpikerSpike1_Big", "x": 64.35, "y": 1.14, "rotation": -90.38, "width": 62, "height": 147}}, "Spike4": {"SwampFloaters/SpikerSpike2": {"name": "SwampFloaters/SpikerSpike2_Big", "x": 64.35, "y": 1.14, "rotation": -90.38, "width": 58, "height": 145}}, "Spike5": {"SwampFloaters/SpikerSpike1": {"name": "SwampFloaters/SpikerSpike1_Big", "x": 64.35, "y": 1.14, "rotation": -90.38, "width": 62, "height": 147}}, "SpikerEyes": {"SpikerEyes": {"name": "SwampFloaters/SpikerEyes_attack1", "x": -31.21, "y": -4.33, "scaleX": 0.9786, "scaleY": 0.9786, "rotation": 90, "width": 181, "height": 104}, "SpikerEyes_Closed": {"name": "SwampFloaters/<PERSON><PERSON><PERSON><PERSON>_Closed_attack1", "x": -31.21, "y": -4.33, "scaleX": 0.9786, "scaleY": 0.9786, "rotation": 90, "width": 181, "height": 104}}, "SwampFloaters/SpikerHorn": {"SwampFloaters/SpikerHorn": {"x": 36.27, "y": 35.91, "rotation": -123.4, "width": 172, "height": 164}}, "SwampFloaters/SpikerHorn2": {"SwampFloaters/SpikerHorn": {"x": 32.33, "y": -44.33, "scaleX": -1, "rotation": -60.97, "width": 172, "height": 164}}}}, {"name": "Spiker<PERSON><PERSON>t_attacked2", "bones": ["HornLeft", "Spike10", "Spike8", "Spike1", "HornLeft2", "Spike5", "Spike4", "Spike9", "Spike11"], "attachments": {"BodyBtm": {"BodyBtm": {"name": "SwampFloaters/Tail_Spiker_Btm_Big", "type": "<PERSON><PERSON><PERSON>", "skin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parent": "BodyBtm", "width": 87, "height": 91}}, "BodyTop": {"BodyTop": {"name": "SwampFloaters/Tail_Spiker_Top_Big", "type": "<PERSON><PERSON><PERSON>", "skin": "Spiker", "parent": "BodyTop", "width": 132, "height": 107}}, "Drips": {"Drips": {"name": "SwampFloaters/DripsSpiker", "type": "<PERSON><PERSON><PERSON>", "skin": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parent": "<PERSON><PERSON>s", "width": 76, "height": 71}}, "Drips2": {"Drips": {"name": "SwampFloaters/DripsSpiker", "type": "<PERSON><PERSON><PERSON>", "skin": "Spiker", "parent": "<PERSON><PERSON>s", "width": 76, "height": 71}}, "EyeExploder": {"EyeMiddle_Closed": {"name": "SwampFloaters/ExploderBigEye1", "x": 0.5, "y": 1.62, "rotation": 90, "width": 71, "height": 61}, "EyeMiddle": {"name": "SwampFloaters/ExploderBigEye1", "x": 0.5, "y": 1.62, "rotation": 90, "width": 71, "height": 61}}, "EyeExploder2": {"EyeMiddle": {"name": "SwampFloaters/ExploderBigEye2", "x": 0.5, "y": 1.62, "rotation": 90, "width": 71, "height": 61}, "EyeMiddle_Closed": {"name": "SwampFloaters/ExploderBigEye2", "x": 0.5, "y": 1.62, "rotation": 90, "width": 71, "height": 61}}, "Head": {"Head2": {"name": "SwampFloaters/SpikerHead2_Big", "type": "<PERSON><PERSON><PERSON>", "skin": "Spiker", "parent": "Head2", "width": 267, "height": 238}, "Head": {"name": "SwampFloaters/SpikerHead_Big", "type": "<PERSON><PERSON><PERSON>", "skin": "Spiker", "parent": "Head", "width": 267, "height": 238}}, "Skirt": {"Skirt": {"name": "SwampFloaters/SpikerSkirtBig", "type": "<PERSON><PERSON><PERSON>", "skin": "Spiker", "parent": "Skirt", "width": 195, "height": 69}}, "Spike1": {"SwampFloaters/SpikerSpike1": {"name": "SwampFloaters/SpikerSpike1_Big", "x": 64.35, "y": 1.14, "rotation": -90.38, "width": 62, "height": 147}}, "Spike4": {"SwampFloaters/SpikerSpike2": {"name": "SwampFloaters/SpikerSpike2_Big", "x": 64.35, "y": 1.14, "rotation": -90.38, "width": 58, "height": 145}}, "Spike5": {"SwampFloaters/SpikerSpike1": {"name": "SwampFloaters/SpikerSpike1_Big", "x": 64.35, "y": 1.14, "rotation": -90.38, "width": 62, "height": 147}}, "SpikerEyes": {"SpikerEyes": {"name": "SwampFloaters/SpikerEyes_attack2", "x": -31.21, "y": -4.33, "scaleX": 0.9786, "scaleY": 0.9786, "rotation": 90, "width": 181, "height": 104}, "SpikerEyes_Closed": {"name": "SwampFloaters/<PERSON><PERSON><PERSON><PERSON>_Closed_attack2", "x": -31.21, "y": -4.33, "scaleX": 0.9786, "scaleY": 0.9786, "rotation": 90, "width": 181, "height": 104}}, "SwampFloaters/SpikerHorn": {"SwampFloaters/SpikerHorn": {"x": 36.27, "y": 35.91, "rotation": -123.4, "width": 172, "height": 164}}, "SwampFloaters/SpikerHorn2": {"SwampFloaters/SpikerHorn": {"x": 32.33, "y": -44.33, "scaleX": -1, "rotation": -60.97, "width": 172, "height": 164}}}}, {"name": "Spiker<PERSON><PERSON><PERSON>_attacked3", "bones": ["HornLeft", "Spike10", "Spike8", "Spike1", "HornLeft2", "Spike5", "Spike4", "Spike9", "Spike11"], "attachments": {"BodyBtm": {"BodyBtm": {"name": "SwampFloaters/Tail_Spiker_Btm_Big", "type": "<PERSON><PERSON><PERSON>", "skin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parent": "BodyBtm", "width": 87, "height": 91}}, "BodyTop": {"BodyTop": {"name": "SwampFloaters/Tail_Spiker_Top_Big", "type": "<PERSON><PERSON><PERSON>", "skin": "Spiker", "parent": "BodyTop", "width": 132, "height": 107}}, "Drips": {"Drips": {"name": "SwampFloaters/DripsSpiker", "type": "<PERSON><PERSON><PERSON>", "skin": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parent": "<PERSON><PERSON>s", "width": 76, "height": 71}}, "Drips2": {"Drips": {"name": "SwampFloaters/DripsSpiker", "type": "<PERSON><PERSON><PERSON>", "skin": "Spiker", "parent": "<PERSON><PERSON>s", "width": 76, "height": 71}}, "EyeExploder": {"EyeMiddle_Closed": {"name": "SwampFloaters/ExploderBigEye1", "x": 0.5, "y": 1.62, "rotation": 90, "width": 71, "height": 61}, "EyeMiddle": {"name": "SwampFloaters/ExploderBigEye1", "x": 0.5, "y": 1.62, "rotation": 90, "width": 71, "height": 61}}, "EyeExploder2": {"EyeMiddle": {"name": "SwampFloaters/ExploderBigEye2", "x": 0.5, "y": 1.62, "rotation": 90, "width": 71, "height": 61}, "EyeMiddle_Closed": {"name": "SwampFloaters/ExploderBigEye2", "x": 0.5, "y": 1.62, "rotation": 90, "width": 71, "height": 61}}, "Head": {"Head2": {"name": "SwampFloaters/SpikerHead2_Big", "type": "<PERSON><PERSON><PERSON>", "skin": "Spiker", "parent": "Head2", "width": 267, "height": 238}, "Head": {"name": "SwampFloaters/SpikerHead_Big", "type": "<PERSON><PERSON><PERSON>", "skin": "Spiker", "parent": "Head", "width": 267, "height": 238}}, "Skirt": {"Skirt": {"name": "SwampFloaters/SpikerSkirtBig", "type": "<PERSON><PERSON><PERSON>", "skin": "Spiker", "parent": "Skirt", "width": 195, "height": 69}}, "Spike1": {"SwampFloaters/SpikerSpike1": {"name": "SwampFloaters/SpikerSpike1_Big", "x": 64.35, "y": 1.14, "rotation": -90.38, "width": 62, "height": 147}}, "Spike4": {"SwampFloaters/SpikerSpike2": {"name": "SwampFloaters/SpikerSpike2_Big", "x": 64.35, "y": 1.14, "rotation": -90.38, "width": 58, "height": 145}}, "Spike5": {"SwampFloaters/SpikerSpike1": {"name": "SwampFloaters/SpikerSpike1_Big", "x": 64.35, "y": 1.14, "rotation": -90.38, "width": 62, "height": 147}}, "SpikerEyes": {"SpikerEyes_Closed": {"name": "SwampFloaters/SpikerEyes_attack3", "x": -31.21, "y": -4.33, "scaleX": 0.9786, "scaleY": 0.9786, "rotation": 90, "width": 181, "height": 104}, "SpikerEyes": {"name": "SwampFloaters/SpikerEyes_attack3", "x": -31.21, "y": -4.33, "scaleX": 0.9786, "scaleY": 0.9786, "rotation": 90, "width": 181, "height": 104}}, "SwampFloaters/SpikerHorn": {"SwampFloaters/SpikerHorn": {"x": 36.27, "y": 35.91, "rotation": -123.4, "width": 172, "height": 164}}, "SwampFloaters/SpikerHorn2": {"SwampFloaters/SpikerHorn": {"x": 32.33, "y": -44.33, "scaleX": -1, "rotation": -60.97, "width": 172, "height": 164}}}}], "animations": {"animation": {"slots": {"Spike1": {"attachment": [{}]}, "Spike2": {"attachment": [{}]}, "Spike3": {"attachment": [{}]}, "Spike4": {"attachment": [{}]}, "Spike5": {"attachment": [{}]}, "Spike6": {"attachment": [{}]}, "Spike7": {"attachment": [{}]}, "Spike8": {"attachment": [{}]}, "Spike9": {"attachment": [{}]}, "Spike10": {"attachment": [{}]}, "Spike11": {"attachment": [{}]}}, "bones": {"Spike1": {"scale": [{"x": 0.06}]}, "Spike2": {"scale": [{"x": 0.06}]}, "Spike3": {"scale": [{"x": 0.06}]}, "Spike4": {"scale": [{"x": 0.06}]}, "Spike5": {"scale": [{"x": 0.06}]}, "Spike6": {"scale": [{"x": 0.06}]}, "Spike7": {"scale": [{"x": 0.06}]}, "Spike8": {"scale": [{"x": 0.06}]}, "Spike9": {"scale": [{"x": 0.06}]}, "Spike10": {"scale": [{"x": 0.06}]}, "Spike11": {"scale": [{"x": 0.06}]}, "Body": {"translate": [{"x": 0.05, "y": -2.32, "curve": [0.05, 0.02, 0.095, 0, 0.05, -0.89, 0.095, 0]}, {"time": 0.1333, "curve": [0.3, 0, 0.633, 0.37, 0.3, 0, 0.633, -17.85]}, {"time": 0.8, "x": 0.37, "y": -17.85, "curve": [0.967, 0.37, 1.3, 0, 0.967, -17.85, 1.3, 0]}, {"time": 1.4667, "curve": [1.633, 0, 1.967, 0.37, 1.633, 0, 1.967, -17.85]}, {"time": 2.1333, "x": 0.37, "y": -17.85, "curve": [2.263, 0.37, 2.496, 0.14, 2.263, -17.85, 2.496, -6.76]}, {"time": 2.6667, "x": 0.05, "y": -2.32}], "scale": [{"x": 0.984, "y": 0.941, "curve": [0.125, 0.964, 0.25, 0.944, 0.125, 0.978, 0.25, 1.016]}, {"time": 0.3333, "x": 0.944, "y": 1.016, "curve": [0.5, 0.944, 0.833, 1.024, 0.5, 1.016, 0.833, 0.865]}, {"time": 1, "x": 1.024, "y": 0.865, "curve": [1.167, 1.024, 1.5, 0.944, 1.167, 0.865, 1.5, 1.016]}, {"time": 1.6667, "x": 0.944, "y": 1.016, "curve": [1.833, 0.944, 2.167, 1.024, 1.833, 1.016, 2.167, 0.865]}, {"time": 2.3333, "x": 1.024, "y": 0.865, "curve": [2.417, 1.024, 2.542, 1.004, 2.417, 0.865, 2.542, 0.903]}, {"time": 2.6667, "x": 0.984, "y": 0.941}]}, "BodyTop": {"rotate": [{"value": 3.05, "curve": [0.083, 3.05, 0.208, 1.83]}, {"time": 0.3333, "value": 0.6, "curve": [0.458, -0.62, 0.583, -1.84]}, {"time": 0.6667, "value": -1.84, "curve": [0.833, -1.84, 1.167, 3.05]}, {"time": 1.3333, "value": 3.05, "curve": [1.417, 3.05, 1.542, 1.83]}, {"time": 1.6667, "value": 0.6, "curve": [1.792, -0.62, 1.917, -1.84]}, {"time": 2, "value": -1.84, "curve": [2.167, -1.84, 2.5, 3.05]}, {"time": 2.6667, "value": 3.05}], "scale": [{"x": 0.928, "y": 0.798, "curve": [0.043, 0.89, 0.088, 0.845, 0.043, 0.836, 0.088, 0.881]}, {"time": 0.1333, "x": 0.8, "y": 0.926, "curve": [0.258, 0.675, 0.383, 0.551, 0.258, 1.051, 0.383, 1.175]}, {"time": 0.4667, "x": 0.551, "y": 1.175, "curve": [0.804, 0.553, 0.792, 1.049, 0.804, 1.173, 0.792, 0.677]}, {"time": 1.1333, "x": 1.049, "y": 0.677, "curve": [1.3, 1.049, 1.633, 0.551, 1.3, 0.677, 1.633, 1.175]}, {"time": 1.8, "x": 0.551, "y": 1.175, "curve": [1.967, 0.551, 2.3, 1.049, 1.967, 1.175, 2.3, 0.677]}, {"time": 2.4667, "x": 1.049, "y": 0.677, "curve": [2.52, 1.049, 2.59, 0.998, 2.52, 0.677, 2.59, 0.728]}, {"time": 2.6667, "x": 0.928, "y": 0.798}]}, "BodyBtm": {"rotate": [{"value": 0.6, "curve": [0.125, -0.62, 0.25, -1.84]}, {"time": 0.3333, "value": -1.84, "curve": [0.5, -1.84, 0.833, 3.05]}, {"time": 1, "value": 3.05, "curve": [1.083, 3.05, 1.208, 1.83]}, {"time": 1.3333, "value": 0.6, "curve": [1.458, -0.62, 1.583, -1.84]}, {"time": 1.6667, "value": -1.84, "curve": [1.833, -1.84, 2.167, 3.05]}, {"time": 2.3333, "value": 3.05, "curve": [2.417, 3.05, 2.542, 1.83]}, {"time": 2.6667, "value": 0.6}], "scale": [{"x": 1.049, "y": 0.879, "curve": [0.184, 1.049, 0.414, 0.78, 0.184, 0.879, 0.414, 1.006]}, {"time": 0.6667, "x": 0.377, "y": 1.197, "curve": [0.94, 0.377, 1.044, 1.042, 0.94, 1.197, 1.044, 0.882]}, {"time": 1.3333, "x": 1.049, "y": 0.879, "curve": [1.517, 1.049, 1.747, 0.78, 1.517, 0.879, 1.747, 1.006]}, {"time": 2, "x": 0.377, "y": 1.197, "curve": [2.274, 0.377, 2.377, 1.042, 2.274, 1.197, 2.377, 0.882]}, {"time": 2.6667, "x": 1.049, "y": 0.879}]}, "Eye1": {"translate": [{"x": -0.82, "curve": [0.125, 0.4, 0.25, 1.62, 0.125, 0, 0.25, 0]}, {"time": 0.3333, "x": 1.62, "curve": [0.5, 1.62, 0.833, -3.26, 0.5, 0, 0.833, 0]}, {"time": 1, "x": -3.26, "curve": [1.083, -3.26, 1.208, -2.04, 1.083, 0, 1.208, 0]}, {"time": 1.3333, "x": -0.82, "curve": [1.458, 0.4, 1.583, 1.62, 1.458, 0, 1.583, 0]}, {"time": 1.6667, "x": 1.62, "curve": [1.833, 1.62, 2.167, -3.26, 1.833, 0, 2.167, 0]}, {"time": 2.3333, "x": -3.26, "curve": [2.417, -3.26, 2.542, -2.04, 2.417, 0, 2.542, 0]}, {"time": 2.6667, "x": -0.82}], "scale": [{"x": 0.947, "y": 0.947, "curve": [0.176, 0.964, 0.404, 1, 0.176, 0.964, 0.404, 1]}, {"time": 0.5333, "curve": [0.692, 1, 1.008, 0.937, 0.692, 1, 1.008, 0.937]}, {"time": 1.1667, "x": 0.937, "y": 0.937, "curve": [1.213, 0.937, 1.27, 0.941, 1.213, 0.937, 1.27, 0.941]}, {"time": 1.3333, "x": 0.947, "y": 0.947, "curve": [1.51, 0.964, 1.737, 1, 1.51, 0.964, 1.737, 1]}, {"time": 1.8667, "curve": [2.025, 1, 2.342, 0.937, 2.025, 1, 2.342, 0.937]}, {"time": 2.5, "x": 0.937, "y": 0.937, "curve": [2.546, 0.937, 2.604, 0.941, 2.546, 0.937, 2.604, 0.941]}, {"time": 2.6667, "x": 0.947, "y": 0.947}]}, "DangleHandle": {"translate": [{"x": 0.11, "y": 8.62, "curve": [0.167, 0.11, 0.5, 1.95, 0.167, 8.62, 0.5, -11.9]}, {"time": 0.6667, "x": 1.95, "y": -11.9, "curve": [0.833, 1.95, 1.167, 0.11, 0.833, -11.9, 1.167, 8.62]}, {"time": 1.3333, "x": 0.11, "y": 8.62, "curve": [1.5, 0.11, 1.833, 1.95, 1.5, 8.62, 1.833, -11.9]}, {"time": 2, "x": 1.95, "y": -11.9, "curve": [2.167, 1.95, 2.5, 0.11, 2.167, -11.9, 2.5, 8.62]}, {"time": 2.6667, "x": 0.11, "y": 8.62}], "scale": [{"y": 0.253, "curve": [0.05, 1, 0.095, 1, 0.05, 0.141, 0.095, 0.07]}, {"time": 0.1333, "y": 0.07, "curve": [0.3, 1, 0.633, 1, 0.3, 0.07, 0.633, 1.476]}, {"time": 0.8, "y": 1.476, "curve": [0.967, 1, 1.3, 1, 0.967, 1.476, 1.3, 0.07]}, {"time": 1.4667, "y": 0.07, "curve": [1.633, 1, 1.967, 1, 1.633, 0.07, 1.967, 1.476]}, {"time": 2.1333, "y": 1.476, "curve": [2.263, 1, 2.496, 1, 2.263, 1.476, 2.496, 0.602]}, {"time": 2.6667, "y": 0.253}]}, "EyeExploder": {"translate": [{}, {"time": 0.0333, "x": -0.02, "y": -7.54}, {"time": 0.0667, "x": -0.04, "y": 1.46}, {"time": 0.1, "x": -7.4}, {"time": 0.1333, "x": 2.79}, {"time": 0.1667, "x": -2.22}, {"time": 0.2, "x": -0.02, "y": -3.4}, {"time": 0.2333, "x": -0.04, "y": 4.58}, {"time": 0.2667, "x": -4.23}, {"time": 0.3, "x": 5.27}, {"time": 0.3333}, {"time": 0.3667, "x": -0.02, "y": -2.68}, {"time": 0.4}, {"time": 0.4333, "x": -0.02, "y": -8.1}, {"time": 0.4667, "x": -0.04, "y": 1.46}, {"time": 0.5, "x": -7.43}, {"time": 0.5333}, {"time": 0.5667, "x": -0.02, "y": -7.54}, {"time": 0.6, "x": -0.04, "y": 1.46}, {"time": 0.6333, "x": -7.4}, {"time": 0.6667, "x": 12.93, "y": -5.09}, {"time": 0.7, "x": -2.22}, {"time": 0.7333, "x": -0.02, "y": -3.4}, {"time": 0.7667, "x": -0.04, "y": 4.58}, {"time": 0.8, "x": -4.23}, {"time": 0.8333, "x": 5.27}, {"time": 0.8667}, {"time": 0.9, "x": -0.02, "y": -2.68}, {"time": 0.9333}, {"time": 0.9667, "x": -0.02, "y": -8.1}, {"time": 1, "x": -0.04, "y": 1.46}, {"time": 1.0333, "x": -7.43}, {"time": 1.0667, "x": 2.79}, {"time": 1.1, "x": -4.23}, {"time": 1.1333, "curve": "stepped"}, {"time": 1.1667}, {"time": 1.2, "x": -0.02, "y": -7.54}, {"time": 1.2333, "x": -0.04, "y": 1.46}, {"time": 1.2667, "x": -7.4}, {"time": 1.3, "x": 2.79}, {"time": 1.3333, "x": -2.22}, {"time": 1.3667, "x": -0.02, "y": -3.4}, {"time": 1.4, "x": -0.04, "y": 4.58}, {"time": 1.4333, "x": -4.23}, {"time": 1.4667, "x": 5.27}, {"time": 1.5}, {"time": 1.5333, "x": -0.02, "y": -2.68}, {"time": 1.5667}, {"time": 1.6, "x": -0.02, "y": -8.1}, {"time": 1.6333, "x": -0.04, "y": 1.46}, {"time": 1.6667, "x": -7.43}, {"time": 1.7}, {"time": 1.7333, "x": -0.02, "y": -7.54}, {"time": 1.7667, "x": -0.04, "y": 1.46}, {"time": 1.8, "x": -7.4}, {"time": 1.8333, "x": 2.79}, {"time": 1.8667, "x": -2.22}, {"time": 1.9, "x": -0.02, "y": -3.4}, {"time": 1.9333, "x": -0.04, "y": 4.58}, {"time": 1.9667, "x": -4.23}, {"time": 2, "x": 5.27}, {"time": 2.0333}, {"time": 2.0667, "x": -0.02, "y": -2.68}, {"time": 2.1}, {"time": 2.1333, "x": -0.02, "y": -7.54}, {"time": 2.1667, "x": -0.04, "y": 1.46}, {"time": 2.2, "x": -7.4}, {"time": 2.2333, "x": 2.79}, {"time": 2.2667, "x": -2.22}, {"time": 2.3, "x": -0.02, "y": -3.4}, {"time": 2.3333, "x": -0.04, "y": 4.58}, {"time": 2.3667, "x": -4.23}, {"time": 2.4, "x": 5.27}, {"time": 2.4333}, {"time": 2.4667, "x": -0.02, "y": -2.68}, {"time": 2.5}, {"time": 2.5333, "x": -0.02, "y": -8.1}, {"time": 2.5667, "x": -0.04, "y": 1.46}, {"time": 2.6, "x": -7.43}, {"time": 2.6333, "x": 2.79}, {"time": 2.6667}], "scale": [{"x": 0.947, "y": 0.947, "curve": [0.176, 0.997, 0.404, 1.1, 0.176, 0.997, 0.404, 1.1]}, {"time": 0.5333, "x": 1.1, "y": 1.1, "curve": [0.692, 1.1, 1.008, 0.937, 0.692, 1.1, 1.008, 0.937]}, {"time": 1.1667, "x": 0.937, "y": 0.937, "curve": [1.208, 0.937, 1.292, 1.108, 1.208, 0.937, 1.292, 1.108]}, {"time": 1.3333, "x": 1.108, "y": 1.108, "curve": [1.383, 1.108, 1.483, 0.929, 1.383, 1.108, 1.483, 0.929]}, {"time": 1.5333, "x": 0.929, "y": 0.929, "curve": [1.6, 0.929, 1.733, 1.108, 1.6, 0.929, 1.733, 1.108]}, {"time": 1.8, "x": 1.108, "y": 1.108, "curve": [1.975, 1.108, 2.325, 0.937, 1.975, 1.108, 2.325, 0.937]}, {"time": 2.5, "x": 0.937, "y": 0.937, "curve": [2.546, 0.937, 2.604, 0.941, 2.546, 0.937, 2.604, 0.941]}, {"time": 2.6667, "x": 0.947, "y": 0.947}]}, "AntlerRight": {"rotate": [{"value": 6.84, "curve": [0.126, 3.34, 0.249, 0]}, {"time": 0.3333, "curve": [0.492, 0, 0.808, 14.6]}, {"time": 0.9667, "value": 14.6, "curve": [1.142, 14.6, 1.492, 0]}, {"time": 1.6667, "curve": [1.825, 0, 2.142, 14.6]}, {"time": 2.3, "value": 14.6, "curve": [2.391, 14.6, 2.53, 10.63]}, {"time": 2.6667, "value": 6.84}]}, "AntlerLeft": {"rotate": [{"value": -6.81, "curve": [0.126, -3.32, 0.249, 0]}, {"time": 0.3333, "curve": [0.492, 0, 0.808, -14.53]}, {"time": 0.9667, "value": -14.53, "curve": [1.142, -14.53, 1.492, 0]}, {"time": 1.6667, "curve": [1.825, 0, 2.142, -14.53]}, {"time": 2.3, "value": -14.53, "curve": [2.391, -14.53, 2.53, -10.57]}, {"time": 2.6667, "value": -6.81}]}, "Eye2": {"translate": [{"x": -0.82, "curve": [0.125, 0.4, 0.25, 1.62, 0.125, 0, 0.25, 0]}, {"time": 0.3333, "x": 1.62, "curve": [0.5, 1.62, 0.833, -3.26, 0.5, 0, 0.833, 0]}, {"time": 1, "x": -3.26, "curve": [1.083, -3.26, 1.208, -2.04, 1.083, 0, 1.208, 0]}, {"time": 1.3333, "x": -0.82, "curve": [1.458, 0.4, 1.583, 1.62, 1.458, 0, 1.583, 0]}, {"time": 1.6667, "x": 1.62, "curve": [1.833, 1.62, 2.167, -3.26, 1.833, 0, 2.167, 0]}, {"time": 2.3333, "x": -3.26, "curve": [2.417, -3.26, 2.542, -2.04, 2.417, 0, 2.542, 0]}, {"time": 2.6667, "x": -0.82}], "scale": [{"x": 0.947, "y": 0.947, "curve": [0.176, 0.964, 0.404, 1, 0.176, 0.964, 0.404, 1]}, {"time": 0.5333, "curve": [0.692, 1, 1.008, 0.937, 0.692, 1, 1.008, 0.937]}, {"time": 1.1667, "x": 0.937, "y": 0.937, "curve": [1.213, 0.937, 1.27, 0.941, 1.213, 0.937, 1.27, 0.941]}, {"time": 1.3333, "x": 0.947, "y": 0.947, "curve": [1.51, 0.964, 1.737, 1, 1.51, 0.964, 1.737, 1]}, {"time": 1.8667, "curve": [2.025, 1, 2.342, 0.937, 2.025, 1, 2.342, 0.937]}, {"time": 2.5, "x": 0.937, "y": 0.937, "curve": [2.546, 0.937, 2.604, 0.941, 2.546, 0.937, 2.604, 0.941]}, {"time": 2.6667, "x": 0.947, "y": 0.947}]}, "Eye3": {"translate": [{"x": -0.82, "curve": [0.125, 0.4, 0.25, 1.62, 0.125, 0, 0.25, 0]}, {"time": 0.3333, "x": 1.62, "curve": [0.5, 1.62, 0.833, -3.26, 0.5, 0, 0.833, 0]}, {"time": 1, "x": -3.26, "curve": [1.083, -3.26, 1.208, -2.04, 1.083, 0, 1.208, 0]}, {"time": 1.3333, "x": -0.82, "curve": [1.458, 0.4, 1.583, 1.62, 1.458, 0, 1.583, 0]}, {"time": 1.6667, "x": 1.62, "curve": [1.833, 1.62, 2.167, -3.26, 1.833, 0, 2.167, 0]}, {"time": 2.3333, "x": -3.26, "curve": [2.417, -3.26, 2.542, -2.04, 2.417, 0, 2.542, 0]}, {"time": 2.6667, "x": -0.82}], "scale": [{"x": 0.947, "y": 0.947, "curve": [0.176, 0.964, 0.404, 1, 0.176, 0.964, 0.404, 1]}, {"time": 0.5333, "curve": [0.692, 1, 1.008, 0.937, 0.692, 1, 1.008, 0.937]}, {"time": 1.1667, "x": 0.937, "y": 0.937, "curve": [1.213, 0.937, 1.27, 0.941, 1.213, 0.937, 1.27, 0.941]}, {"time": 1.3333, "x": 0.947, "y": 0.947, "curve": [1.51, 0.964, 1.737, 1, 1.51, 0.964, 1.737, 1]}, {"time": 1.8667, "curve": [2.025, 1, 2.342, 0.937, 2.025, 1, 2.342, 0.937]}, {"time": 2.5, "x": 0.937, "y": 0.937, "curve": [2.546, 0.937, 2.604, 0.941, 2.546, 0.937, 2.604, 0.941]}, {"time": 2.6667, "x": 0.947, "y": 0.947}]}, "EyeExploder2": {"translate": [{}, {"time": 0.0333, "x": -0.02, "y": -7.54}, {"time": 0.0667, "x": -0.04, "y": 1.46}, {"time": 0.1, "x": -7.4}, {"time": 0.1333, "x": 2.79}, {"time": 0.1667, "x": -2.22}, {"time": 0.2, "x": -0.02, "y": -3.4}, {"time": 0.2333, "x": -0.04, "y": 4.58}, {"time": 0.2667, "x": -4.23}, {"time": 0.3, "x": 5.27}, {"time": 0.3333}, {"time": 0.3667, "x": -0.02, "y": -2.68}, {"time": 0.4}, {"time": 0.4333, "x": -0.02, "y": -8.1}, {"time": 0.4667, "x": -0.04, "y": 1.46}, {"time": 0.5, "x": -7.43}, {"time": 0.5333}, {"time": 0.5667, "x": -0.02, "y": -7.54}, {"time": 0.6, "x": -0.04, "y": 1.46}, {"time": 0.6333, "x": -7.4}, {"time": 0.6667, "x": 12.93, "y": -5.09}, {"time": 0.7, "x": -2.22}, {"time": 0.7333, "x": -0.02, "y": -3.4}, {"time": 0.7667, "x": -0.04, "y": 4.58}, {"time": 0.8, "x": -4.23}, {"time": 0.8333, "x": 5.27}, {"time": 0.8667}, {"time": 0.9, "x": -0.02, "y": -2.68}, {"time": 0.9333}, {"time": 0.9667, "x": -0.02, "y": -8.1}, {"time": 1, "x": -0.04, "y": 1.46}, {"time": 1.0333, "x": -7.43}, {"time": 1.0667, "x": 2.79}, {"time": 1.1, "x": -4.23}, {"time": 1.1333, "curve": "stepped"}, {"time": 1.1667}, {"time": 1.2, "x": -0.02, "y": -7.54}, {"time": 1.2333, "x": -0.04, "y": 1.46}, {"time": 1.2667, "x": -7.4}, {"time": 1.3, "x": 2.79}, {"time": 1.3333, "x": -2.22}, {"time": 1.3667, "x": -0.02, "y": -3.4}, {"time": 1.4, "x": -0.04, "y": 4.58}, {"time": 1.4333, "x": -4.23}, {"time": 1.4667, "x": 5.27}, {"time": 1.5}, {"time": 1.5333, "x": -0.02, "y": -2.68}, {"time": 1.5667}, {"time": 1.6, "x": -0.02, "y": -8.1}, {"time": 1.6333, "x": -0.04, "y": 1.46}, {"time": 1.6667, "x": -7.43}, {"time": 1.7}, {"time": 1.7333, "x": -0.02, "y": -7.54}, {"time": 1.7667, "x": -0.04, "y": 1.46}, {"time": 1.8, "x": -7.4}, {"time": 1.8333, "x": 2.79}, {"time": 1.8667, "x": -2.22}, {"time": 1.9, "x": -0.02, "y": -3.4}, {"time": 1.9333, "x": -0.04, "y": 4.58}, {"time": 1.9667, "x": -4.23}, {"time": 2, "x": 5.27}, {"time": 2.0333}, {"time": 2.0667, "x": -0.02, "y": -2.68}, {"time": 2.1}, {"time": 2.1333, "x": -0.02, "y": -7.54}, {"time": 2.1667, "x": -0.04, "y": 1.46}, {"time": 2.2, "x": -7.4}, {"time": 2.2333, "x": 2.79}, {"time": 2.2667, "x": -2.22}, {"time": 2.3, "x": -0.02, "y": -3.4}, {"time": 2.3333, "x": -0.04, "y": 4.58}, {"time": 2.3667, "x": -4.23}, {"time": 2.4, "x": 5.27}, {"time": 2.4333}, {"time": 2.4667, "x": -0.02, "y": -2.68}, {"time": 2.5}, {"time": 2.5333, "x": -0.02, "y": -8.1}, {"time": 2.5667, "x": -0.04, "y": 1.46}, {"time": 2.6, "x": -7.43}, {"time": 2.6333, "x": 2.79}, {"time": 2.6667}], "scale": [{"x": 0.947, "y": 0.947, "curve": [0.176, 0.997, 0.404, 1.1, 0.176, 0.997, 0.404, 1.1]}, {"time": 0.5333, "x": 1.1, "y": 1.1, "curve": [0.692, 1.1, 1.008, 0.937, 0.692, 1.1, 1.008, 0.937]}, {"time": 1.1667, "x": 0.937, "y": 0.937, "curve": [1.208, 0.937, 1.292, 1.108, 1.208, 0.937, 1.292, 1.108]}, {"time": 1.3333, "x": 1.108, "y": 1.108, "curve": [1.383, 1.108, 1.483, 0.929, 1.383, 1.108, 1.483, 0.929]}, {"time": 1.5333, "x": 0.929, "y": 0.929, "curve": [1.6, 0.929, 1.733, 1.108, 1.6, 0.929, 1.733, 1.108]}, {"time": 1.8, "x": 1.108, "y": 1.108, "curve": [1.975, 1.108, 2.325, 0.937, 1.975, 1.108, 2.325, 0.937]}, {"time": 2.5, "x": 0.937, "y": 0.937, "curve": [2.546, 0.937, 2.604, 0.941, 2.546, 0.937, 2.604, 0.941]}, {"time": 2.6667, "x": 0.947, "y": 0.947}]}, "HornLeft2": {"rotate": [{"value": 2.95, "curve": [0.167, 2.95, 0.5, -3.99]}, {"time": 0.6667, "value": -3.99, "curve": [0.833, -3.99, 1.167, 2.95]}, {"time": 1.3333, "value": 2.95, "curve": [1.5, 2.95, 1.833, -3.99]}, {"time": 2, "value": -3.99, "curve": [2.167, -3.99, 2.5, 2.95]}, {"time": 2.6667, "value": 2.95}]}, "HornLeft": {"rotate": [{"value": -2.42, "curve": [0.167, -2.42, 0.5, 5.43]}, {"time": 0.6667, "value": 5.43, "curve": [0.833, 5.43, 1.167, -2.42]}, {"time": 1.3333, "value": -2.42, "curve": [1.5, -2.42, 1.833, 5.43]}, {"time": 2, "value": 5.43, "curve": [2.167, 5.43, 2.5, -2.42]}, {"time": 2.6667, "value": -2.42}]}}}, "anticipate": {"slots": {"EyeExploder": {"attachment": [{"name": "EyeMiddle_Closed"}]}, "EyeExploder2": {"attachment": [{"name": "EyeMiddle_Closed"}]}, "Spike1": {"attachment": [{}]}, "Spike2": {"attachment": [{}]}, "Spike3": {"attachment": [{}]}, "Spike4": {"attachment": [{}]}, "Spike5": {"attachment": [{}]}, "Spike6": {"attachment": [{}]}, "Spike7": {"attachment": [{}]}, "Spike8": {"attachment": [{}]}, "Spike9": {"attachment": [{}]}, "Spike10": {"attachment": [{}]}, "Spike11": {"attachment": [{}]}, "SpikerEyes": {"attachment": [{"name": "<PERSON><PERSON><PERSON><PERSON>_Closed"}]}}, "bones": {"EyeExploder": {"translate": [{"x": -0.82, "curve": [0.063, 0.4, 0.125, 1.62, 0.063, 0, 0.125, 0]}, {"time": 0.1667, "x": 1.62, "curve": [0.25, 1.62, 0.417, -3.26, 0.25, 0, 0.417, 0]}, {"time": 0.5, "x": -3.26, "curve": [0.542, -3.26, 0.604, -2.04, 0.542, 0, 0.604, 0]}, {"time": 0.6667, "x": -0.82}], "scale": [{"x": 0.947, "y": 0.947, "curve": [0.088, 0.964, 0.202, 1, 0.088, 0.964, 0.202, 1]}, {"time": 0.2667, "curve": [0.35, 1, 0.517, 0.937, 0.35, 1, 0.517, 0.937]}, {"time": 0.6, "x": 0.937, "y": 0.937, "curve": [0.619, 0.937, 0.641, 0.941, 0.619, 0.937, 0.641, 0.941]}, {"time": 0.6667, "x": 0.947, "y": 0.947}]}, "EyeExploder2": {"translate": [{"x": -0.82, "curve": [0.063, 0.4, 0.125, 1.62, 0.063, 0, 0.125, 0]}, {"time": 0.1667, "x": 1.62, "curve": [0.25, 1.62, 0.417, -3.26, 0.25, 0, 0.417, 0]}, {"time": 0.5, "x": -3.26, "curve": [0.542, -3.26, 0.604, -2.04, 0.542, 0, 0.604, 0]}, {"time": 0.6667, "x": -0.82}], "scale": [{"x": 0.947, "y": 0.947, "curve": [0.088, 0.964, 0.202, 1, 0.088, 0.964, 0.202, 1]}, {"time": 0.2667, "curve": [0.35, 1, 0.517, 0.937, 0.35, 1, 0.517, 0.937]}, {"time": 0.6, "x": 0.937, "y": 0.937, "curve": [0.619, 0.937, 0.641, 0.941, 0.619, 0.937, 0.641, 0.941]}, {"time": 0.6667, "x": 0.947, "y": 0.947}]}, "Spike1": {"scale": [{"x": 0.06}]}, "Spike2": {"scale": [{"x": 0.06}]}, "Spike3": {"scale": [{"x": 0.06}]}, "Spike4": {"scale": [{"x": 0.06}]}, "Spike5": {"scale": [{"x": 0.06}]}, "Spike6": {"scale": [{"x": 0.06}]}, "Spike7": {"scale": [{"x": 0.06}]}, "Spike8": {"scale": [{"x": 0.06}]}, "Spike9": {"scale": [{"x": 0.06}]}, "Spike10": {"scale": [{"x": 0.06}]}, "Spike11": {"scale": [{"x": 0.06}]}, "Eye1": {"translate": [{"x": -0.82, "curve": [0.063, 0.4, 0.125, 1.62, 0.063, 0, 0.125, 0]}, {"time": 0.1667, "x": 1.62, "curve": [0.25, 1.62, 0.417, -3.26, 0.25, 0, 0.417, 0]}, {"time": 0.5, "x": -3.26, "curve": [0.542, -3.26, 0.604, -2.04, 0.542, 0, 0.604, 0]}, {"time": 0.6667, "x": -0.82}], "scale": [{"x": 0.947, "y": 0.947, "curve": [0.088, 0.964, 0.202, 1, 0.088, 0.964, 0.202, 1]}, {"time": 0.2667, "curve": [0.35, 1, 0.517, 0.937, 0.35, 1, 0.517, 0.937]}, {"time": 0.6, "x": 0.937, "y": 0.937, "curve": [0.619, 0.937, 0.641, 0.941, 0.619, 0.937, 0.641, 0.941]}, {"time": 0.6667, "x": 0.947, "y": 0.947}]}, "Body": {"translate": [{"curve": [0.083, 0, 0.25, 0.37, 0.083, 0, 0.25, -17.85]}, {"time": 0.3333, "x": 0.37, "y": -17.85, "curve": [0.417, 0.37, 0.583, 0, 0.417, -17.85, 0.583, 0]}, {"time": 0.6667}], "scale": [{"x": 1.004, "y": 0.988, "curve": [0.063, 0.994, 0.125, 0.983, 0.063, 1.002, 0.125, 1.016]}, {"time": 0.1667, "x": 0.983, "y": 1.016, "curve": [0.25, 0.983, 0.417, 1.024, 0.25, 1.016, 0.417, 0.959]}, {"time": 0.5, "x": 1.024, "y": 0.959, "curve": [0.542, 1.024, 0.625, 1.004, 0.542, 0.959, 0.625, 0.988]}, {"time": 0.6667, "x": 1.004, "y": 0.988}]}, "BodyTop": {"scale": [{"x": 0.984, "y": 0.988, "curve": [0.063, 0.952, 0.125, 0.919, 0.063, 1.042, 0.125, 1.097]}, {"time": 0.1667, "x": 0.919, "y": 1.097, "curve": [0.25, 0.919, 0.417, 1.049, 0.25, 1.097, 0.417, 0.879]}, {"time": 0.5, "x": 1.049, "y": 0.879, "curve": [0.542, 1.049, 0.604, 1.016, 0.542, 0.879, 0.604, 0.933]}, {"time": 0.6667, "x": 0.984, "y": 0.988}]}, "BodyBtm": {"scale": [{"x": 1.025, "y": 0.919, "curve": [0.089, 0.99, 0.202, 0.919, 0.089, 0.978, 0.202, 1.097]}, {"time": 0.2667, "x": 0.919, "y": 1.097, "curve": [0.35, 0.919, 0.517, 1.049, 0.35, 1.097, 0.517, 0.879]}, {"time": 0.6, "x": 1.049, "y": 0.879, "curve": [0.618, 1.049, 0.641, 1.039, 0.618, 0.879, 0.641, 0.895]}, {"time": 0.6667, "x": 1.025, "y": 0.919}]}, "AntlerRight": {"rotate": [{"value": -0.9, "curve": [0.063, -0.45, 0.125, 0]}, {"time": 0.1667, "curve": [0.25, 0, 0.417, -1.8]}, {"time": 0.5, "value": -1.8, "curve": [0.542, -1.8, 0.604, -1.35]}, {"time": 0.6667, "value": -0.9}]}, "AntlerLeft": {"rotate": [{"value": 0.85, "curve": [0.063, 0.43, 0.125, 0]}, {"time": 0.1667, "curve": [0.25, 0, 0.417, 1.7]}, {"time": 0.5, "value": 1.7, "curve": [0.542, 1.7, 0.604, 1.28]}, {"time": 0.6667, "value": 0.85}]}, "DangleHandle": {"translate": [{"y": 3.22, "curve": [0.083, 0, 0.25, 0, 0.083, 3.22, 0.25, -3.22]}, {"time": 0.3333, "y": -3.22, "curve": [0.417, 0, 0.583, 0, 0.417, -3.22, 0.583, 3.22]}, {"time": 0.6667, "y": 3.22}]}, "Head": {"translate": [{"y": 3.28, "curve": [0.017, 0, 0.05, 0, 0.017, 3.28, 0.05, -4.85]}, {"time": 0.0667, "y": -4.85, "curve": [0.083, 0, 0.117, 0, 0.083, -4.85, 0.117, 3.28]}, {"time": 0.1333, "y": 3.28, "curve": [0.15, 0, 0.183, 0, 0.15, 3.28, 0.183, -4.85]}, {"time": 0.2, "y": -4.85, "curve": [0.217, 0, 0.25, 0, 0.217, -4.85, 0.25, 3.28]}, {"time": 0.2667, "y": 3.28, "curve": [0.283, 0, 0.317, 0, 0.283, 3.28, 0.317, -4.85]}, {"time": 0.3333, "y": -4.85, "curve": [0.35, 0, 0.383, 0, 0.35, -4.85, 0.383, 3.28]}, {"time": 0.4, "y": 3.28, "curve": [0.417, 0, 0.45, 0, 0.417, 3.28, 0.45, -4.85]}, {"time": 0.4667, "y": -4.85, "curve": [0.483, 0, 0.517, 0, 0.483, -4.85, 0.517, 3.28]}, {"time": 0.5333, "y": 3.28, "curve": [0.55, 0, 0.583, 0, 0.55, 3.28, 0.583, -4.85]}, {"time": 0.6, "y": -4.85, "curve": [0.617, 0, 0.65, 0, 0.617, -4.85, 0.65, 3.28]}, {"time": 0.6667, "y": 3.28}], "scale": [{"x": 0.943, "y": 1.081}]}, "Eye2": {"translate": [{"x": -0.82, "curve": [0.063, 0.4, 0.125, 1.62, 0.063, 0, 0.125, 0]}, {"time": 0.1667, "x": 1.62, "curve": [0.25, 1.62, 0.417, -3.26, 0.25, 0, 0.417, 0]}, {"time": 0.5, "x": -3.26, "curve": [0.542, -3.26, 0.604, -2.04, 0.542, 0, 0.604, 0]}, {"time": 0.6667, "x": -0.82}], "scale": [{"x": 0.947, "y": 0.947, "curve": [0.088, 0.964, 0.202, 1, 0.088, 0.964, 0.202, 1]}, {"time": 0.2667, "curve": [0.35, 1, 0.517, 0.937, 0.35, 1, 0.517, 0.937]}, {"time": 0.6, "x": 0.937, "y": 0.937, "curve": [0.619, 0.937, 0.641, 0.941, 0.619, 0.937, 0.641, 0.941]}, {"time": 0.6667, "x": 0.947, "y": 0.947}]}, "Eye3": {"translate": [{"x": -0.82, "curve": [0.063, 0.4, 0.125, 1.62, 0.063, 0, 0.125, 0]}, {"time": 0.1667, "x": 1.62, "curve": [0.25, 1.62, 0.417, -3.26, 0.25, 0, 0.417, 0]}, {"time": 0.5, "x": -3.26, "curve": [0.542, -3.26, 0.604, -2.04, 0.542, 0, 0.604, 0]}, {"time": 0.6667, "x": -0.82}], "scale": [{"x": 0.947, "y": 0.947, "curve": [0.088, 0.964, 0.202, 1, 0.088, 0.964, 0.202, 1]}, {"time": 0.2667, "curve": [0.35, 1, 0.517, 0.937, 0.35, 1, 0.517, 0.937]}, {"time": 0.6, "x": 0.937, "y": 0.937, "curve": [0.619, 0.937, 0.641, 0.941, 0.619, 0.937, 0.641, 0.941]}, {"time": 0.6667, "x": 0.947, "y": 0.947}]}, "HornLeft2": {"rotate": [{"value": 2.95, "curve": [0.083, 2.95, 0.25, -3.99]}, {"time": 0.3333, "value": -3.99, "curve": [0.417, -3.99, 0.583, 2.95]}, {"time": 0.6667, "value": 2.95}]}, "HornLeft": {"rotate": [{"value": -2.42, "curve": [0.083, -2.42, 0.25, 5.43]}, {"time": 0.3333, "value": 5.43, "curve": [0.417, 5.43, 0.583, -2.42]}, {"time": 0.6667, "value": -2.42}]}}}, "anticipate-spiker": {"slots": {"EyeExploder": {"attachment": [{"name": "EyeMiddle_Closed"}]}, "EyeExploder2": {"attachment": [{"name": "EyeMiddle_Closed"}]}, "Spike1": {"attachment": [{}]}, "Spike2": {"attachment": [{}]}, "Spike3": {"attachment": [{}]}, "Spike4": {"attachment": [{}]}, "Spike5": {"attachment": [{}]}, "Spike6": {"attachment": [{}]}, "Spike7": {"attachment": [{}]}, "Spike8": {"attachment": [{}]}, "Spike9": {"attachment": [{}]}, "Spike10": {"attachment": [{}]}, "Spike11": {"attachment": [{}]}, "SpikerEyes": {"attachment": [{"name": "<PERSON><PERSON><PERSON><PERSON>_Closed"}]}}, "bones": {"EyeExploder": {"translate": [{"x": -0.82, "curve": [0.063, 0.4, 0.125, 1.62, 0.063, 0, 0.125, 0]}, {"time": 0.1667, "x": 1.62, "curve": [0.25, 1.62, 0.417, -3.26, 0.25, 0, 0.417, 0]}, {"time": 0.5, "x": -3.26, "curve": [0.542, -3.26, 0.604, -2.04, 0.542, 0, 0.604, 0]}, {"time": 0.6667, "x": -0.82}], "scale": [{"x": 0.947, "y": 0.947, "curve": [0.088, 0.964, 0.202, 1, 0.088, 0.964, 0.202, 1]}, {"time": 0.2667, "curve": [0.35, 1, 0.517, 0.937, 0.35, 1, 0.517, 0.937]}, {"time": 0.6, "x": 0.937, "y": 0.937, "curve": [0.619, 0.937, 0.641, 0.941, 0.619, 0.937, 0.641, 0.941]}, {"time": 0.6667, "x": 0.947, "y": 0.947}]}, "EyeExploder2": {"translate": [{"x": -0.82, "curve": [0.063, 0.4, 0.125, 1.62, 0.063, 0, 0.125, 0]}, {"time": 0.1667, "x": 1.62, "curve": [0.25, 1.62, 0.417, -3.26, 0.25, 0, 0.417, 0]}, {"time": 0.5, "x": -3.26, "curve": [0.542, -3.26, 0.604, -2.04, 0.542, 0, 0.604, 0]}, {"time": 0.6667, "x": -0.82}], "scale": [{"x": 0.947, "y": 0.947, "curve": [0.088, 0.964, 0.202, 1, 0.088, 0.964, 0.202, 1]}, {"time": 0.2667, "curve": [0.35, 1, 0.517, 0.937, 0.35, 1, 0.517, 0.937]}, {"time": 0.6, "x": 0.937, "y": 0.937, "curve": [0.619, 0.937, 0.641, 0.941, 0.619, 0.937, 0.641, 0.941]}, {"time": 0.6667, "x": 0.947, "y": 0.947}]}, "Spike1": {"scale": [{"x": 0.06}]}, "Spike2": {"scale": [{"x": 0.06}]}, "Spike3": {"scale": [{"x": 0.06}]}, "Spike4": {"scale": [{"x": 0.06}]}, "Spike5": {"scale": [{"x": 0.06}]}, "Spike6": {"scale": [{"x": 0.06}]}, "Spike7": {"scale": [{"x": 0.06}]}, "Spike8": {"scale": [{"x": 0.06}]}, "Spike9": {"scale": [{"x": 0.06}]}, "Spike10": {"scale": [{"x": 0.06}]}, "Spike11": {"scale": [{"x": 0.06}]}, "Eye1": {"translate": [{"x": -0.82, "curve": [0.025, -0.82, 0.075, 22.9, 0.025, 0, 0.075, 1.45]}, {"time": 0.1, "x": 22.9, "y": 1.45, "curve": [0.492, 22.9, 0.237, -31.61, 0.492, 1.45, 0.237, 0]}, {"time": 1.6667, "x": -31.61}], "scale": [{"x": 0.947, "y": 0.947, "curve": [0.017, 0.947, 0.05, 0.854, 0.017, 0.947, 0.05, 0.854]}, {"time": 0.0667, "x": 0.854, "y": 0.854, "curve": [0.083, 0.854, 0.117, 0.947, 0.083, 0.854, 0.117, 0.947]}, {"time": 0.1333, "x": 0.947, "y": 0.947, "curve": [0.15, 0.947, 0.183, 0.854, 0.15, 0.947, 0.183, 0.854]}, {"time": 0.2, "x": 0.854, "y": 0.854, "curve": [0.217, 0.854, 0.25, 0.947, 0.217, 0.854, 0.25, 0.947]}, {"time": 0.2667, "x": 0.947, "y": 0.947, "curve": [0.283, 0.947, 0.317, 0.854, 0.283, 0.947, 0.317, 0.854]}, {"time": 0.3333, "x": 0.854, "y": 0.854, "curve": [0.35, 0.854, 0.383, 0.947, 0.35, 0.854, 0.383, 0.947]}, {"time": 0.4, "x": 0.947, "y": 0.947, "curve": [0.417, 0.947, 0.45, 0.854, 0.417, 0.947, 0.45, 0.854]}, {"time": 0.4667, "x": 0.854, "y": 0.854, "curve": [0.483, 0.854, 0.517, 0.947, 0.483, 0.854, 0.517, 0.947]}, {"time": 0.5333, "x": 0.947, "y": 0.947, "curve": [0.55, 0.947, 0.583, 0.854, 0.55, 0.947, 0.583, 0.854]}, {"time": 0.6, "x": 0.854, "y": 0.854, "curve": [0.617, 0.854, 0.65, 0.947, 0.617, 0.854, 0.65, 0.947]}, {"time": 0.6667, "x": 0.947, "y": 0.947, "curve": [0.683, 0.947, 0.717, 0.854, 0.683, 0.947, 0.717, 0.854]}, {"time": 0.7333, "x": 0.854, "y": 0.854, "curve": [0.75, 0.854, 0.783, 0.947, 0.75, 0.854, 0.783, 0.947]}, {"time": 0.8, "x": 0.947, "y": 0.947, "curve": [0.817, 0.947, 0.85, 0.854, 0.817, 0.947, 0.85, 0.854]}, {"time": 0.8667, "x": 0.854, "y": 0.854, "curve": [0.883, 0.854, 0.917, 0.947, 0.883, 0.854, 0.917, 0.947]}, {"time": 0.9333, "x": 0.947, "y": 0.947, "curve": [0.95, 0.947, 0.983, 0.854, 0.95, 0.947, 0.983, 0.854]}, {"time": 1, "x": 0.854, "y": 0.854, "curve": [1.017, 0.854, 1.05, 0.947, 1.017, 0.854, 1.05, 0.947]}, {"time": 1.0667, "x": 0.947, "y": 0.947, "curve": [1.083, 0.947, 1.117, 0.854, 1.083, 0.947, 1.117, 0.854]}, {"time": 1.1333, "x": 0.854, "y": 0.854, "curve": [1.15, 0.854, 1.183, 0.947, 1.15, 0.854, 1.183, 0.947]}, {"time": 1.2, "x": 0.947, "y": 0.947, "curve": [1.217, 0.947, 1.25, 0.854, 1.217, 0.947, 1.25, 0.854]}, {"time": 1.2667, "x": 0.854, "y": 0.854, "curve": [1.283, 0.854, 1.317, 0.947, 1.283, 0.854, 1.317, 0.947]}, {"time": 1.3333, "x": 0.947, "y": 0.947, "curve": [1.35, 0.947, 1.383, 0.854, 1.35, 0.947, 1.383, 0.854]}, {"time": 1.4, "x": 0.854, "y": 0.854, "curve": [1.417, 0.854, 1.45, 0.947, 1.417, 0.854, 1.45, 0.947]}, {"time": 1.4667, "x": 0.947, "y": 0.947, "curve": [1.483, 0.947, 1.517, 0.854, 1.483, 0.947, 1.517, 0.854]}, {"time": 1.5333, "x": 0.854, "y": 0.854, "curve": [1.55, 0.854, 1.583, 0.947, 1.55, 0.854, 1.583, 0.947]}, {"time": 1.6, "x": 0.947, "y": 0.947, "curve": [1.617, 0.947, 1.65, 0.854, 1.617, 0.947, 1.65, 0.854]}, {"time": 1.6667, "x": 0.854, "y": 0.854, "curve": [1.683, 0.854, 1.717, 0.947, 1.683, 0.854, 1.717, 0.947]}, {"time": 1.7333, "x": 0.947, "y": 0.947, "curve": [1.75, 0.947, 1.783, 0.854, 1.75, 0.947, 1.783, 0.854]}, {"time": 1.8, "x": 0.854, "y": 0.854, "curve": [1.817, 0.854, 1.85, 0.947, 1.817, 0.854, 1.85, 0.947]}, {"time": 1.8667, "x": 0.947, "y": 0.947, "curve": [1.883, 0.947, 1.917, 0.854, 1.883, 0.947, 1.917, 0.854]}, {"time": 1.9333, "x": 0.854, "y": 0.854, "curve": [1.95, 0.854, 1.983, 0.947, 1.95, 0.854, 1.983, 0.947]}, {"time": 2, "x": 0.947, "y": 0.947}]}, "Body": {"translate": [{"curve": [0.083, 0, 0.25, 0.37, 0.083, 0, 0.25, -17.85]}, {"time": 0.3333, "x": 0.37, "y": -17.85, "curve": [0.383, 0.37, 0.483, 0, 0.383, -17.85, 0.483, 0]}, {"time": 0.5333, "curve": "stepped"}, {"time": 0.6667, "curve": [0.717, 0, 0.817, 0.37, 0.717, 0, 0.817, -17.85]}, {"time": 0.8667, "x": 0.37, "y": -17.85, "curve": "stepped"}, {"time": 1, "x": 0.37, "y": -17.85, "curve": [1.05, 0.37, 1.15, 0, 1.05, -17.85, 1.15, 0]}, {"time": 1.2, "curve": "stepped"}, {"time": 1.3333, "curve": [1.417, 0, 1.583, 0.37, 1.417, 0, 1.583, -17.85]}, {"time": 1.6667, "x": 0.37, "y": -17.85, "curve": [1.75, 0.37, 1.917, 0, 1.75, -17.85, 1.917, 0]}, {"time": 2}], "scale": [{"x": 1.004, "y": 0.988, "curve": [0.05, 1.117, 0.1, 1.231, 0.05, 0.887, 0.1, 0.786]}, {"time": 0.1333, "x": 1.231, "y": 0.786, "curve": [0.192, 1.231, 0.308, 1.024, 0.192, 0.786, 0.308, 0.959]}, {"time": 0.3667, "x": 1.024, "y": 0.959, "curve": [0.408, 1.024, 0.492, 1.004, 0.408, 0.959, 0.492, 0.988]}, {"time": 0.5333, "x": 1.004, "y": 0.988, "curve": "stepped"}, {"time": 0.6667, "x": 1.004, "y": 0.988, "curve": [0.679, 0.994, 0.692, 0.983, 0.679, 1.002, 0.692, 1.016]}, {"time": 0.7, "x": 0.983, "y": 1.016, "curve": "stepped"}, {"time": 0.8333, "x": 0.983, "y": 1.016, "curve": [0.883, 0.983, 0.983, 1.024, 0.883, 1.016, 0.983, 0.959]}, {"time": 1.0333, "x": 1.024, "y": 0.959, "curve": "stepped"}, {"time": 1.1667, "x": 1.024, "y": 0.959, "curve": [1.175, 1.024, 1.192, 1.004, 1.175, 0.959, 1.192, 0.988]}, {"time": 1.2, "x": 1.004, "y": 0.988, "curve": "stepped"}, {"time": 1.3333, "x": 1.004, "y": 0.988, "curve": [1.396, 0.994, 1.458, 0.983, 1.396, 1.002, 1.458, 1.016]}, {"time": 1.5, "x": 0.983, "y": 1.016, "curve": [1.583, 0.983, 1.75, 1.024, 1.583, 1.016, 1.75, 0.959]}, {"time": 1.8333, "x": 1.024, "y": 0.959, "curve": [1.875, 1.024, 1.958, 1.004, 1.875, 0.959, 1.958, 0.988]}, {"time": 2, "x": 1.004, "y": 0.988}]}, "AntlerRight": {"rotate": [{"value": -0.9, "curve": [0.063, -0.45, 0.125, 0]}, {"time": 0.1667, "curve": [0.25, 0, 0.417, -1.8]}, {"time": 0.5, "value": -1.8, "curve": [0.508, -1.8, 0.521, -1.35]}, {"time": 0.5333, "value": -0.9, "curve": "stepped"}, {"time": 0.6667, "value": -0.9, "curve": [0.679, -0.45, 0.692, 0]}, {"time": 0.7, "curve": "stepped"}, {"time": 0.8333, "curve": [0.883, 0, 0.983, -1.8]}, {"time": 1.0333, "value": -1.8, "curve": "stepped"}, {"time": 1.1667, "value": -1.8, "curve": [1.175, -1.8, 1.188, -1.35]}, {"time": 1.2, "value": -0.9, "curve": "stepped"}, {"time": 1.3333, "value": -0.9, "curve": [1.396, -0.45, 1.458, 0]}, {"time": 1.5, "curve": [1.583, 0, 1.75, -1.8]}, {"time": 1.8333, "value": -1.8, "curve": [1.875, -1.8, 1.938, -1.35]}, {"time": 2, "value": -0.9}]}, "AntlerLeft": {"rotate": [{"value": 0.85, "curve": [0.063, 0.43, 0.125, 0]}, {"time": 0.1667, "curve": [0.25, 0, 0.417, 1.7]}, {"time": 0.5, "value": 1.7, "curve": [0.508, 1.7, 0.521, 1.28]}, {"time": 0.5333, "value": 0.85, "curve": "stepped"}, {"time": 0.6667, "value": 0.85, "curve": [0.679, 0.43, 0.692, 0]}, {"time": 0.7, "curve": "stepped"}, {"time": 0.8333, "curve": [0.883, 0, 0.983, 1.7]}, {"time": 1.0333, "value": 1.7, "curve": "stepped"}, {"time": 1.1667, "value": 1.7, "curve": [1.175, 1.7, 1.188, 1.28]}, {"time": 1.2, "value": 0.85, "curve": "stepped"}, {"time": 1.3333, "value": 0.85, "curve": [1.396, 0.43, 1.458, 0]}, {"time": 1.5, "curve": [1.583, 0, 1.75, 1.7]}, {"time": 1.8333, "value": 1.7, "curve": [1.875, 1.7, 1.938, 1.28]}, {"time": 2, "value": 0.85}]}, "Head": {"translate": [{"y": 3.28, "curve": [0.017, 0, 0.05, 0, 0.017, 3.28, 0.05, -4.85]}, {"time": 0.0667, "y": -4.85, "curve": [0.083, 0, 0.117, 0, 0.083, -4.85, 0.117, 3.28]}, {"time": 0.1333, "y": 3.28, "curve": [0.15, 0, 0.183, 0, 0.15, 3.28, 0.183, -4.85]}, {"time": 0.2, "y": -4.85, "curve": [0.217, 0, 0.25, 0, 0.217, -4.85, 0.25, 3.28]}, {"time": 0.2667, "y": 3.28, "curve": [0.283, 0, 0.317, 0, 0.283, 3.28, 0.317, -4.85]}, {"time": 0.3333, "y": -4.85, "curve": [0.35, 0, 0.383, 0, 0.35, -4.85, 0.383, 3.28]}, {"time": 0.4, "y": 3.28, "curve": [0.417, 0, 0.45, 0, 0.417, 3.28, 0.45, -4.85]}, {"time": 0.4667, "y": -4.85, "curve": [0.483, 0, 0.517, 0, 0.483, -4.85, 0.517, 3.28]}, {"time": 0.5333, "y": 3.28, "curve": [0.55, 0, 0.583, 0, 0.55, 3.28, 0.583, -4.85]}, {"time": 0.6, "y": -4.85, "curve": [0.617, 0, 0.65, 0, 0.617, -4.85, 0.65, 3.28]}, {"time": 0.6667, "y": 3.28, "curve": [0.683, 0, 0.717, 0, 0.683, 3.28, 0.717, -4.85]}, {"time": 0.7333, "y": -4.85, "curve": [0.75, 0, 0.783, 0, 0.75, -4.85, 0.783, 3.28]}, {"time": 0.8, "y": 3.28, "curve": [0.817, 0, 0.85, 0, 0.817, 3.28, 0.85, -4.85]}, {"time": 0.8667, "y": -4.85, "curve": [0.883, 0, 0.917, 0, 0.883, -4.85, 0.917, 3.28]}, {"time": 0.9333, "y": 3.28, "curve": [0.95, 0, 0.983, 0, 0.95, 3.28, 0.983, -4.85]}, {"time": 1, "y": -4.85, "curve": [1.017, 0, 1.05, 0, 1.017, -4.85, 1.05, 3.28]}, {"time": 1.0667, "y": 3.28, "curve": [1.083, 0, 1.117, 0, 1.083, 3.28, 1.117, -4.85]}, {"time": 1.1333, "y": -4.85, "curve": [1.15, 0, 1.183, 0, 1.15, -4.85, 1.183, 3.28]}, {"time": 1.2, "y": 3.28, "curve": [1.217, 0, 1.25, 0, 1.217, 3.28, 1.25, -4.85]}, {"time": 1.2667, "y": -4.85, "curve": [1.283, 0, 1.317, 0, 1.283, -4.85, 1.317, 3.28]}, {"time": 1.3333, "y": 3.28, "curve": [1.35, 0, 1.383, 0, 1.35, 3.28, 1.383, -4.85]}, {"time": 1.4, "y": -4.85, "curve": [1.417, 0, 1.45, 0, 1.417, -4.85, 1.45, 3.28]}, {"time": 1.4667, "y": 3.28, "curve": [1.483, 0, 1.517, 0, 1.483, 3.28, 1.517, -4.85]}, {"time": 1.5333, "y": -4.85, "curve": [1.55, 0, 1.583, 0, 1.55, -4.85, 1.583, 3.28]}, {"time": 1.6, "y": 3.28, "curve": [1.617, 0, 1.65, 0, 1.617, 3.28, 1.65, -4.85]}, {"time": 1.6667, "y": -4.85, "curve": [1.683, 0, 1.717, 0, 1.683, -4.85, 1.717, 3.28]}, {"time": 1.7333, "y": 3.28, "curve": [1.75, 0, 1.783, 0, 1.75, 3.28, 1.783, -4.85]}, {"time": 1.8, "y": -4.85, "curve": [1.817, 0, 1.85, 0, 1.817, -4.85, 1.85, 3.28]}, {"time": 1.8667, "y": 3.28, "curve": [1.883, 0, 1.917, 0, 1.883, 3.28, 1.917, -4.85]}, {"time": 1.9333, "y": -4.85, "curve": [1.95, 0, 1.983, 0, 1.95, -4.85, 1.983, 3.28]}, {"time": 2, "y": 3.28}], "scale": [{"x": 0.943, "y": 1.081}]}, "Eye2": {"translate": [{"x": -0.82, "curve": [0.063, 0.4, 0.125, 1.62, 0.063, 0, 0.125, 0]}, {"time": 0.1667, "x": 1.62, "curve": [0.25, 1.62, 0.417, -3.26, 0.25, 0, 0.417, 0]}, {"time": 0.5, "x": -3.26, "curve": [0.542, -3.26, 0.604, -2.04, 0.542, 0, 0.604, 0]}, {"time": 0.6667, "x": -0.82}], "scale": [{"x": 0.947, "y": 0.947, "curve": [0.088, 0.964, 0.202, 1, 0.088, 0.964, 0.202, 1]}, {"time": 0.2667, "curve": [0.35, 1, 0.517, 0.937, 0.35, 1, 0.517, 0.937]}, {"time": 0.6, "x": 0.937, "y": 0.937, "curve": [0.619, 0.937, 0.641, 0.941, 0.619, 0.937, 0.641, 0.941]}, {"time": 0.6667, "x": 0.947, "y": 0.947}]}, "Eye3": {"translate": [{"x": -0.82, "curve": [0.063, 0.4, 0.125, 1.62, 0.063, 0, 0.125, 0]}, {"time": 0.1667, "x": 1.62, "curve": [0.25, 1.62, 0.417, -3.26, 0.25, 0, 0.417, 0]}, {"time": 0.5, "x": -3.26, "curve": [0.542, -3.26, 0.604, -2.04, 0.542, 0, 0.604, 0]}, {"time": 0.6667, "x": -0.82}], "scale": [{"x": 0.947, "y": 0.947, "curve": [0.088, 0.964, 0.202, 1, 0.088, 0.964, 0.202, 1]}, {"time": 0.2667, "curve": [0.35, 1, 0.517, 0.937, 0.35, 1, 0.517, 0.937]}, {"time": 0.6, "x": 0.937, "y": 0.937, "curve": [0.619, 0.937, 0.641, 0.941, 0.619, 0.937, 0.641, 0.941]}, {"time": 0.6667, "x": 0.947, "y": 0.947}]}, "HornLeft2": {"rotate": [{"value": 2.95, "curve": [0.083, 2.95, 0.25, -3.99]}, {"time": 0.3333, "value": -3.99, "curve": [0.417, -3.99, 0.583, 2.95]}, {"time": 0.6667, "value": 2.95}]}, "HornLeft": {"rotate": [{"value": -2.42, "curve": [0.083, -2.42, 0.25, 5.43]}, {"time": 0.3333, "value": 5.43, "curve": [0.417, 5.43, 0.583, -2.42]}, {"time": 0.6667, "value": -2.42}]}, "BodyTop": {"scale": [{"x": 0.984, "y": 0.988, "curve": [0.025, 0.952, 0.05, 0.919, 0.025, 1.042, 0.05, 1.097]}, {"time": 0.0667, "x": 0.919, "y": 1.097, "curve": [0.092, 0.919, 0.142, 1.049, 0.092, 1.097, 0.142, 0.879]}, {"time": 0.1667, "x": 1.049, "y": 0.879, "curve": [0.192, 1.049, 0.229, 1.016, 0.192, 0.879, 0.229, 0.933]}, {"time": 0.2667, "x": 0.984, "y": 0.988, "curve": [0.279, 0.952, 0.292, 0.919, 0.279, 1.042, 0.292, 1.097]}, {"time": 0.3, "x": 0.919, "y": 1.097, "curve": [0.333, 0.919, 0.4, 1.049, 0.333, 1.097, 0.4, 0.879]}, {"time": 0.4333, "x": 1.049, "y": 0.879, "curve": [0.45, 1.049, 0.475, 1.016, 0.45, 0.879, 0.475, 0.933]}, {"time": 0.5, "x": 0.984, "y": 0.988, "curve": [0.525, 0.952, 0.55, 0.919, 0.525, 1.042, 0.55, 1.097]}, {"time": 0.5667, "x": 0.919, "y": 1.097, "curve": [0.592, 0.919, 0.642, 1.049, 0.592, 1.097, 0.642, 0.879]}, {"time": 0.6667, "x": 1.049, "y": 0.879, "curve": [0.692, 1.049, 0.729, 1.016, 0.692, 0.879, 0.729, 0.933]}, {"time": 0.7667, "x": 0.984, "y": 0.988, "curve": [0.779, 0.952, 0.792, 0.919, 0.779, 1.042, 0.792, 1.097]}, {"time": 0.8, "x": 0.919, "y": 1.097, "curve": [0.833, 0.919, 0.9, 1.049, 0.833, 1.097, 0.9, 0.879]}, {"time": 0.9333, "x": 1.049, "y": 0.879, "curve": [0.95, 1.049, 0.975, 1.016, 0.95, 0.879, 0.975, 0.933]}, {"time": 1, "x": 0.984, "y": 0.988, "curve": [1.025, 0.952, 1.05, 0.919, 1.025, 1.042, 1.05, 1.097]}, {"time": 1.0667, "x": 0.919, "y": 1.097, "curve": [1.092, 0.919, 1.142, 1.049, 1.092, 1.097, 1.142, 0.879]}, {"time": 1.1667, "x": 1.049, "y": 0.879, "curve": [1.192, 1.049, 1.229, 1.016, 1.192, 0.879, 1.229, 0.933]}, {"time": 1.2667, "x": 0.984, "y": 0.988, "curve": [1.279, 0.952, 1.292, 0.919, 1.279, 1.042, 1.292, 1.097]}, {"time": 1.3, "x": 0.919, "y": 1.097, "curve": [1.333, 0.919, 1.4, 1.049, 1.333, 1.097, 1.4, 0.879]}, {"time": 1.4333, "x": 1.049, "y": 0.879, "curve": [1.45, 1.049, 1.475, 1.016, 1.45, 0.879, 1.475, 0.933]}, {"time": 1.5, "x": 0.984, "y": 0.988, "curve": [1.525, 0.952, 1.55, 0.919, 1.525, 1.042, 1.55, 1.097]}, {"time": 1.5667, "x": 0.919, "y": 1.097, "curve": [1.592, 0.919, 1.642, 1.049, 1.592, 1.097, 1.642, 0.879]}, {"time": 1.6667, "x": 1.049, "y": 0.879, "curve": [1.692, 1.049, 1.729, 1.016, 1.692, 0.879, 1.729, 0.933]}, {"time": 1.7667, "x": 0.984, "y": 0.988, "curve": [1.779, 0.952, 1.792, 0.919, 1.779, 1.042, 1.792, 1.097]}, {"time": 1.8, "x": 0.919, "y": 1.097, "curve": [1.833, 0.919, 1.9, 1.049, 1.833, 1.097, 1.9, 0.879]}, {"time": 1.9333, "x": 1.049, "y": 0.879, "curve": [1.95, 1.049, 1.975, 1.016, 1.95, 0.879, 1.975, 0.933]}, {"time": 2, "x": 0.984, "y": 0.988}]}, "BodyBtm": {"scale": [{"x": 1.025, "y": 0.919, "curve": [0.033, 0.99, 0.076, 0.919, 0.033, 0.978, 0.076, 1.097]}, {"time": 0.1, "x": 0.919, "y": 1.097, "curve": [0.133, 0.919, 0.2, 1.049, 0.133, 1.097, 0.2, 0.879]}, {"time": 0.2333, "x": 1.049, "y": 0.879, "curve": [0.243, 1.049, 0.254, 1.039, 0.243, 0.879, 0.254, 0.895]}, {"time": 0.2667, "x": 1.025, "y": 0.919, "curve": [0.3, 0.99, 0.342, 0.919, 0.3, 0.978, 0.342, 1.097]}, {"time": 0.3667, "x": 0.919, "y": 1.097, "curve": [0.392, 0.919, 0.442, 1.049, 0.392, 1.097, 0.442, 0.879]}, {"time": 0.4667, "x": 1.049, "y": 0.879, "curve": [0.476, 1.049, 0.487, 1.039, 0.476, 0.879, 0.487, 0.895]}, {"time": 0.5, "x": 1.025, "y": 0.919, "curve": [0.533, 0.99, 0.576, 0.919, 0.533, 0.978, 0.576, 1.097]}, {"time": 0.6, "x": 0.919, "y": 1.097, "curve": [0.633, 0.919, 0.7, 1.049, 0.633, 1.097, 0.7, 0.879]}, {"time": 0.7333, "x": 1.049, "y": 0.879, "curve": [0.743, 1.049, 0.754, 1.039, 0.743, 0.879, 0.754, 0.895]}, {"time": 0.7667, "x": 1.025, "y": 0.919, "curve": [0.8, 0.99, 0.842, 0.919, 0.8, 0.978, 0.842, 1.097]}, {"time": 0.8667, "x": 0.919, "y": 1.097, "curve": [0.892, 0.919, 0.942, 1.049, 0.892, 1.097, 0.942, 0.879]}, {"time": 0.9667, "x": 1.049, "y": 0.879, "curve": [0.976, 1.049, 0.987, 1.039, 0.976, 0.879, 0.987, 0.895]}, {"time": 1, "x": 1.025, "y": 0.919, "curve": [1.033, 0.99, 1.076, 0.919, 1.033, 0.978, 1.076, 1.097]}, {"time": 1.1, "x": 0.919, "y": 1.097, "curve": [1.133, 0.919, 1.2, 1.049, 1.133, 1.097, 1.2, 0.879]}, {"time": 1.2333, "x": 1.049, "y": 0.879, "curve": [1.243, 1.049, 1.254, 1.039, 1.243, 0.879, 1.254, 0.895]}, {"time": 1.2667, "x": 1.025, "y": 0.919, "curve": [1.3, 0.99, 1.342, 0.919, 1.3, 0.978, 1.342, 1.097]}, {"time": 1.3667, "x": 0.919, "y": 1.097, "curve": [1.392, 0.919, 1.442, 1.049, 1.392, 1.097, 1.442, 0.879]}, {"time": 1.4667, "x": 1.049, "y": 0.879, "curve": [1.476, 1.049, 1.487, 1.039, 1.476, 0.879, 1.487, 0.895]}, {"time": 1.5, "x": 1.025, "y": 0.919, "curve": [1.533, 0.99, 1.576, 0.919, 1.533, 0.978, 1.576, 1.097]}, {"time": 1.6, "x": 0.919, "y": 1.097, "curve": [1.633, 0.919, 1.7, 1.049, 1.633, 1.097, 1.7, 0.879]}, {"time": 1.7333, "x": 1.049, "y": 0.879, "curve": [1.743, 1.049, 1.754, 1.039, 1.743, 0.879, 1.754, 0.895]}, {"time": 1.7667, "x": 1.025, "y": 0.919, "curve": [1.8, 0.99, 1.842, 0.919, 1.8, 0.978, 1.842, 1.097]}, {"time": 1.8667, "x": 0.919, "y": 1.097, "curve": [1.892, 0.919, 1.942, 1.049, 1.892, 1.097, 1.942, 0.879]}, {"time": 1.9667, "x": 1.049, "y": 0.879, "curve": [1.976, 1.049, 1.987, 1.039, 1.976, 0.879, 1.987, 0.895]}, {"time": 2, "x": 1.025, "y": 0.919}]}, "DangleHandle": {"translate": [{"x": 0.11, "y": 8.62, "curve": [0.067, 0.11, 0.2, 1.95, 0.067, 8.62, 0.2, -11.9]}, {"time": 0.2667, "x": 1.95, "y": -11.9, "curve": [0.325, 1.95, 0.442, 0.11, 0.325, -11.9, 0.442, 8.62]}, {"time": 0.5, "x": 0.11, "y": 8.62, "curve": [0.567, 0.11, 0.7, 1.95, 0.567, 8.62, 0.7, -11.9]}, {"time": 0.7667, "x": 1.95, "y": -11.9, "curve": [0.825, 1.95, 0.942, 0.11, 0.825, -11.9, 0.942, 8.62]}, {"time": 1, "x": 0.11, "y": 8.62, "curve": [1.067, 0.11, 1.2, 1.95, 1.067, 8.62, 1.2, -11.9]}, {"time": 1.2667, "x": 1.95, "y": -11.9, "curve": [1.325, 1.95, 1.442, 0.11, 1.325, -11.9, 1.442, 8.62]}, {"time": 1.5, "x": 0.11, "y": 8.62, "curve": [1.567, 0.11, 1.7, 1.95, 1.567, 8.62, 1.7, -11.9]}, {"time": 1.7667, "x": 1.95, "y": -11.9, "curve": [1.825, 1.95, 1.942, 0.11, 1.825, -11.9, 1.942, 8.62]}, {"time": 2, "x": 0.11, "y": 8.62}], "scale": [{"y": 0.253, "curve": [0.025, 1, 0.048, 1, 0.025, 0.141, 0.048, 0.07]}, {"time": 0.0667, "y": 0.07, "curve": [0.125, 1, 0.242, 1, 0.125, 0.07, 0.242, 1.476]}, {"time": 0.3, "y": 1.476, "curve": [0.367, 1, 0.5, 1, 0.367, 1.476, 0.5, 0.07]}, {"time": 0.5667, "y": 0.07, "curve": [0.625, 1, 0.742, 1, 0.625, 0.07, 0.742, 1.476]}, {"time": 0.8, "y": 1.476, "curve": [0.849, 1, 0.936, 1, 0.849, 1.476, 0.936, 0.602]}, {"time": 1, "y": 0.253, "curve": [1.025, 1, 1.048, 1, 1.025, 0.141, 1.048, 0.07]}, {"time": 1.0667, "y": 0.07, "curve": [1.125, 1, 1.242, 1, 1.125, 0.07, 1.242, 1.476]}, {"time": 1.3, "y": 1.476, "curve": [1.367, 1, 1.5, 1, 1.367, 1.476, 1.5, 0.07]}, {"time": 1.5667, "y": 0.07, "curve": [1.625, 1, 1.742, 1, 1.625, 0.07, 1.742, 1.476]}, {"time": 1.8, "y": 1.476, "curve": [1.849, 1, 1.936, 1, 1.849, 1.476, 1.936, 0.602]}, {"time": 2, "y": 0.253}]}}}, "attack": {"slots": {"Eye1": {"attachment": [{"name": "Eye1"}]}, "Eye1Back": {"attachment": [{"name": "Eye1Back"}]}, "Eye1Back2": {"attachment": [{"name": "Eye1Back"}]}, "Eye1Back3": {"attachment": [{"name": "Eye1Back"}]}, "Eye2": {"attachment": [{"name": "Eye1"}]}, "Eye3": {"attachment": [{"name": "Eye1"}]}, "Head": {"attachment": [{"time": 0.0667, "name": "Head2"}, {"time": 1, "name": "Head"}]}, "Spike1": {"attachment": [{"time": 1}]}, "Spike2": {"attachment": [{"time": 1}]}, "Spike3": {"attachment": [{"time": 1}]}, "Spike4": {"attachment": [{"time": 1}]}, "Spike5": {"attachment": [{"time": 1}]}, "Spike6": {"attachment": [{"time": 1}]}, "Spike7": {"attachment": [{"time": 1}]}, "Spike8": {"attachment": [{"time": 1}]}, "Spike9": {"attachment": [{"time": 1}]}, "Spike10": {"attachment": [{"time": 1}]}, "Spike11": {"attachment": [{"time": 1}]}}, "bones": {"Eyeball1": {"scale": [{"x": 0.563, "y": 0.947, "curve": [0.017, 0.563, 0.05, 2.141, 0.017, 0.947, 0.05, 1.621]}, {"time": 0.0667, "x": 2.141, "y": 1.621, "curve": [0.092, 2.141, 0.142, 0.89, 0.092, 1.621, 0.142, 0.89]}, {"time": 0.1667, "x": 0.89, "y": 0.89, "curve": [0.2, 0.89, 0.267, 1.196, 0.2, 0.89, 0.267, 1.196]}, {"time": 0.3, "x": 1.196, "y": 1.196}, {"time": 0.5, "x": 0.947, "y": 0.947, "curve": [0.588, 0.964, 0.702, 1, 0.588, 0.964, 0.702, 1]}, {"time": 0.7667, "curve": [0.85, 1, 1.017, 0.937, 0.85, 1, 1.017, 0.937]}, {"time": 1.1, "x": 0.937, "y": 0.937, "curve": [1.119, 0.937, 1.141, 0.941, 1.119, 0.937, 1.141, 0.941]}, {"time": 1.1667, "x": 0.947, "y": 0.947}]}, "Eye1": {"translate": [{"x": -0.82, "curve": "stepped"}, {"time": 0.5, "x": -0.82, "curve": [0.563, 0.4, 0.625, 1.62, 0.563, 0, 0.625, 0]}, {"time": 0.6667, "x": 1.62, "curve": [0.75, 1.62, 0.917, -3.26, 0.75, 0, 0.917, 0]}, {"time": 1, "x": -3.26, "curve": [1.042, -3.26, 1.104, -2.04, 1.042, 0, 1.104, 0]}, {"time": 1.1667, "x": -0.82}]}, "Eye2": {"translate": [{"x": -0.82, "curve": "stepped"}, {"time": 0.5, "x": -0.82, "curve": [0.563, 0.4, 0.625, 1.62, 0.563, 0, 0.625, 0]}, {"time": 0.6667, "x": 1.62, "curve": [0.75, 1.62, 0.917, -3.26, 0.75, 0, 0.917, 0]}, {"time": 1, "x": -3.26, "curve": [1.042, -3.26, 1.104, -2.04, 1.042, 0, 1.104, 0]}, {"time": 1.1667, "x": -0.82}]}, "Eye3": {"translate": [{"x": -0.82, "curve": "stepped"}, {"time": 0.5, "x": -0.82, "curve": [0.563, 0.4, 0.625, 1.62, 0.563, 0, 0.625, 0]}, {"time": 0.6667, "x": 1.62, "curve": [0.75, 1.62, 0.917, -3.26, 0.75, 0, 0.917, 0]}, {"time": 1, "x": -3.26, "curve": [1.042, -3.26, 1.104, -2.04, 1.042, 0, 1.104, 0]}, {"time": 1.1667, "x": -0.82}]}, "Eyeball2": {"scale": [{"x": 0.563, "y": 0.947, "curve": [0.017, 0.563, 0.05, 2.141, 0.017, 0.947, 0.05, 1.621]}, {"time": 0.0667, "x": 2.141, "y": 1.621, "curve": [0.092, 2.141, 0.142, 0.89, 0.092, 1.621, 0.142, 0.89]}, {"time": 0.1667, "x": 0.89, "y": 0.89, "curve": [0.2, 0.89, 0.267, 1.196, 0.2, 0.89, 0.267, 1.196]}, {"time": 0.3, "x": 1.196, "y": 1.196}, {"time": 0.5, "x": 0.947, "y": 0.947, "curve": [0.588, 0.964, 0.702, 1, 0.588, 0.964, 0.702, 1]}, {"time": 0.7667, "curve": [0.85, 1, 1.017, 0.937, 0.85, 1, 1.017, 0.937]}, {"time": 1.1, "x": 0.937, "y": 0.937, "curve": [1.119, 0.937, 1.141, 0.941, 1.119, 0.937, 1.141, 0.941]}, {"time": 1.1667, "x": 0.947, "y": 0.947}]}, "Eyeball3": {"scale": [{"x": 0.563, "y": 0.947, "curve": [0.017, 0.563, 0.05, 2.141, 0.017, 0.947, 0.05, 1.621]}, {"time": 0.0667, "x": 2.141, "y": 1.621, "curve": [0.092, 2.141, 0.142, 0.89, 0.092, 1.621, 0.142, 0.89]}, {"time": 0.1667, "x": 0.89, "y": 0.89, "curve": [0.2, 0.89, 0.267, 1.196, 0.2, 0.89, 0.267, 1.196]}, {"time": 0.3, "x": 1.196, "y": 1.196}, {"time": 0.5, "x": 0.947, "y": 0.947, "curve": [0.588, 0.964, 0.702, 1, 0.588, 0.964, 0.702, 1]}, {"time": 0.7667, "curve": [0.85, 1, 1.017, 0.937, 0.85, 1, 1.017, 0.937]}, {"time": 1.1, "x": 0.937, "y": 0.937, "curve": [1.119, 0.937, 1.141, 0.941, 1.119, 0.937, 1.141, 0.941]}, {"time": 1.1667, "x": 0.947, "y": 0.947}]}, "Spike1": {"scale": [{"x": 0.06, "curve": [0.008, 0.06, 0.025, 1.433, 0.008, 1, 0.025, 0.95]}, {"time": 0.0333, "x": 1.433, "y": 0.95, "curve": [0.058, 1.433, 0.108, 0.7, 0.058, 0.95, 0.108, 1.127]}, {"time": 0.1333, "x": 0.7, "y": 1.127, "curve": [0.167, 0.7, 0.233, 1.09, 0.167, 1.127, 0.233, 0.955]}, {"time": 0.2667, "x": 1.09, "y": 0.955, "curve": [0.317, 1.09, 0.417, 1, 0.317, 0.955, 0.417, 1]}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.7667}, {"time": 0.9333, "x": 1.114, "curve": [0.95, 1.114, 0.997, 0.376, 0.95, 1, 0.997, 1]}, {"time": 1, "x": 0.09}]}, "Spike2": {"scale": [{"x": 0.06, "curve": [0.008, 0.06, 0.025, 1.433, 0.008, 1, 0.025, 0.95]}, {"time": 0.0333, "x": 1.433, "y": 0.95, "curve": [0.058, 1.433, 0.108, 0.7, 0.058, 0.95, 0.108, 1.127]}, {"time": 0.1333, "x": 0.7, "y": 1.127, "curve": [0.167, 0.7, 0.233, 1.09, 0.167, 1.127, 0.233, 0.955]}, {"time": 0.2667, "x": 1.09, "y": 0.955, "curve": [0.317, 1.09, 0.417, 1, 0.317, 0.955, 0.417, 1]}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.7667}, {"time": 0.9333, "x": 1.114, "curve": [0.95, 1.114, 0.997, 0.376, 0.95, 1, 0.997, 1]}, {"time": 1, "x": 0.09}]}, "Spike3": {"scale": [{"x": 0.06, "curve": [0.008, 0.06, 0.025, 1.433, 0.008, 1, 0.025, 0.95]}, {"time": 0.0333, "x": 1.433, "y": 0.95, "curve": [0.058, 1.433, 0.108, 0.7, 0.058, 0.95, 0.108, 1.127]}, {"time": 0.1333, "x": 0.7, "y": 1.127, "curve": [0.167, 0.7, 0.233, 1.09, 0.167, 1.127, 0.233, 0.955]}, {"time": 0.2667, "x": 1.09, "y": 0.955, "curve": [0.317, 1.09, 0.417, 1, 0.317, 0.955, 0.417, 1]}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.7667}, {"time": 0.9333, "x": 1.114, "curve": [0.95, 1.114, 0.997, 0.376, 0.95, 1, 0.997, 1]}, {"time": 1, "x": 0.09}]}, "Spike4": {"scale": [{"x": 0.06, "curve": [0.008, 0.06, 0.025, 1.433, 0.008, 1, 0.025, 0.95]}, {"time": 0.0333, "x": 1.433, "y": 0.95, "curve": [0.058, 1.433, 0.108, 0.7, 0.058, 0.95, 0.108, 1.127]}, {"time": 0.1333, "x": 0.7, "y": 1.127, "curve": [0.167, 0.7, 0.233, 1.09, 0.167, 1.127, 0.233, 0.955]}, {"time": 0.2667, "x": 1.09, "y": 0.955, "curve": [0.317, 1.09, 0.417, 1, 0.317, 0.955, 0.417, 1]}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.7667}, {"time": 0.9333, "x": 1.114, "curve": [0.95, 1.114, 0.997, 0.376, 0.95, 1, 0.997, 1]}, {"time": 1, "x": 0.09}]}, "Spike5": {"scale": [{"x": 0.06, "curve": [0.008, 0.06, 0.025, 1.433, 0.008, 1, 0.025, 0.95]}, {"time": 0.0333, "x": 1.433, "y": 0.95, "curve": [0.058, 1.433, 0.108, 0.7, 0.058, 0.95, 0.108, 1.127]}, {"time": 0.1333, "x": 0.7, "y": 1.127, "curve": [0.167, 0.7, 0.233, 1.09, 0.167, 1.127, 0.233, 0.955]}, {"time": 0.2667, "x": 1.09, "y": 0.955, "curve": [0.317, 1.09, 0.417, 1, 0.317, 0.955, 0.417, 1]}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.7667}, {"time": 0.9333, "x": 1.114, "curve": [0.95, 1.114, 0.997, 0.376, 0.95, 1, 0.997, 1]}, {"time": 1, "x": 0.09}]}, "Spike6": {"scale": [{"x": 0.06, "curve": [0.008, 0.06, 0.025, 1.433, 0.008, 1, 0.025, 0.95]}, {"time": 0.0333, "x": 1.433, "y": 0.95, "curve": [0.058, 1.433, 0.108, 0.7, 0.058, 0.95, 0.108, 1.127]}, {"time": 0.1333, "x": 0.7, "y": 1.127, "curve": [0.167, 0.7, 0.233, 1.09, 0.167, 1.127, 0.233, 0.955]}, {"time": 0.2667, "x": 1.09, "y": 0.955, "curve": [0.317, 1.09, 0.417, 1, 0.317, 0.955, 0.417, 1]}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.7667}, {"time": 0.9333, "x": 1.114, "curve": [0.95, 1.114, 0.997, 0.376, 0.95, 1, 0.997, 1]}, {"time": 1, "x": 0.09}]}, "Spike7": {"scale": [{"x": 0.06, "curve": [0.008, 0.06, 0.025, 1.433, 0.008, 1, 0.025, 0.95]}, {"time": 0.0333, "x": 1.433, "y": 0.95, "curve": [0.058, 1.433, 0.108, 0.7, 0.058, 0.95, 0.108, 1.127]}, {"time": 0.1333, "x": 0.7, "y": 1.127, "curve": [0.167, 0.7, 0.233, 1.09, 0.167, 1.127, 0.233, 0.955]}, {"time": 0.2667, "x": 1.09, "y": 0.955, "curve": [0.317, 1.09, 0.417, 1, 0.317, 0.955, 0.417, 1]}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.7667}, {"time": 0.9333, "x": 1.114, "curve": [0.95, 1.114, 0.997, 0.376, 0.95, 1, 0.997, 1]}, {"time": 1, "x": 0.09}]}, "Spike8": {"scale": [{"x": 0.06, "curve": [0.008, 0.06, 0.025, 1.433, 0.008, 1, 0.025, 0.95]}, {"time": 0.0333, "x": 1.433, "y": 0.95, "curve": [0.058, 1.433, 0.108, 0.7, 0.058, 0.95, 0.108, 1.127]}, {"time": 0.1333, "x": 0.7, "y": 1.127, "curve": [0.167, 0.7, 0.233, 1.09, 0.167, 1.127, 0.233, 0.955]}, {"time": 0.2667, "x": 1.09, "y": 0.955, "curve": [0.317, 1.09, 0.417, 1, 0.317, 0.955, 0.417, 1]}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.7667}, {"time": 0.9333, "x": 1.114, "curve": [0.95, 1.114, 0.997, 0.376, 0.95, 1, 0.997, 1]}, {"time": 1, "x": 0.09}]}, "Spike9": {"scale": [{"x": 0.06, "curve": [0.008, 0.06, 0.025, 1.433, 0.008, 1, 0.025, 0.95]}, {"time": 0.0333, "x": 1.433, "y": 0.95, "curve": [0.058, 1.433, 0.108, 0.7, 0.058, 0.95, 0.108, 1.127]}, {"time": 0.1333, "x": 0.7, "y": 1.127, "curve": [0.167, 0.7, 0.233, 1.09, 0.167, 1.127, 0.233, 0.955]}, {"time": 0.2667, "x": 1.09, "y": 0.955, "curve": [0.317, 1.09, 0.417, 1, 0.317, 0.955, 0.417, 1]}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.7667}, {"time": 0.9333, "x": 1.114, "curve": [0.95, 1.114, 0.997, 0.376, 0.95, 1, 0.997, 1]}, {"time": 1, "x": 0.09}]}, "Spike10": {"scale": [{"x": 0.06, "curve": [0.008, 0.06, 0.025, 1.433, 0.008, 1, 0.025, 0.95]}, {"time": 0.0333, "x": 1.433, "y": 0.95, "curve": [0.058, 1.433, 0.108, 0.7, 0.058, 0.95, 0.108, 1.127]}, {"time": 0.1333, "x": 0.7, "y": 1.127, "curve": [0.167, 0.7, 0.233, 1.09, 0.167, 1.127, 0.233, 0.955]}, {"time": 0.2667, "x": 1.09, "y": 0.955, "curve": [0.317, 1.09, 0.417, 1, 0.317, 0.955, 0.417, 1]}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.7667}, {"time": 0.9333, "x": 1.114, "curve": [0.95, 1.114, 0.997, 0.376, 0.95, 1, 0.997, 1]}, {"time": 1, "x": 0.09}]}, "Spike11": {"scale": [{"x": 0.06, "curve": [0.008, 0.06, 0.025, 1.433, 0.008, 1, 0.025, 0.95]}, {"time": 0.0333, "x": 1.433, "y": 0.95, "curve": [0.058, 1.433, 0.108, 0.7, 0.058, 0.95, 0.108, 1.127]}, {"time": 0.1333, "x": 0.7, "y": 1.127, "curve": [0.167, 0.7, 0.233, 1.09, 0.167, 1.127, 0.233, 0.955]}, {"time": 0.2667, "x": 1.09, "y": 0.955, "curve": [0.317, 1.09, 0.417, 1, 0.317, 0.955, 0.417, 1]}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.7667}, {"time": 0.9333, "x": 1.114, "curve": [0.95, 1.114, 0.997, 0.376, 0.95, 1, 0.997, 1]}, {"time": 1, "x": 0.09}]}, "Body": {"translate": [{"x": 1.34, "y": -64.35, "curve": [0.017, 1.34, 0.05, -0.4, 0.017, -64.35, 0.05, 19.41]}, {"time": 0.0667, "x": -0.4, "y": 19.41, "curve": [0.1, -0.4, 0.167, 0.95, 0.1, 19.41, 0.167, -45.76]}, {"time": 0.2, "x": 0.95, "y": -45.76, "curve": [0.258, 0.95, 0.375, 0.19, 0.258, -45.76, 0.375, -9.07]}, {"time": 0.4333, "x": 0.19, "y": -9.07, "curve": [0.558, 0.19, 0.808, 0.73, 0.558, -9.07, 0.808, -34.92]}, {"time": 0.9333, "x": 0.73, "y": -34.92, "curve": [1.033, 0.73, 1.233, 0, 1.033, -34.92, 1.233, 0]}, {"time": 1.3333}], "scale": [{"x": 0.832, "y": 1.115, "curve": [0.017, 0.832, 0.05, 1.154, 0.017, 1.115, 0.05, 0.771]}, {"time": 0.0667, "x": 1.154, "y": 0.771, "curve": [0.1, 1.154, 0.167, 0.816, 0.1, 0.771, 0.167, 1.285]}, {"time": 0.2, "x": 0.816, "y": 1.285, "curve": [0.258, 0.816, 0.375, 1.084, 0.258, 1.285, 0.375, 0.91]}, {"time": 0.4333, "x": 1.084, "y": 0.91}, {"time": 0.7333, "x": 0.983, "y": 1.016, "curve": [0.833, 0.983, 1.033, 1.024, 0.833, 1.016, 1.033, 0.959]}, {"time": 1.1333, "x": 1.024, "y": 0.959, "curve": [1.183, 1.024, 1.283, 1.004, 1.183, 0.959, 1.283, 0.988]}, {"time": 1.3333, "x": 1.004, "y": 0.988}]}, "AntlerRight": {"rotate": [{"value": -17.11, "curve": [0.017, -17.11, 0.05, 64.15]}, {"time": 0.0667, "value": 64.15, "curve": [0.108, 64.15, 0.192, -66.36]}, {"time": 0.2333, "value": -66.36, "curve": [0.308, -66.36, 0.458, 27.66]}, {"time": 0.5333, "value": 27.66, "curve": [0.625, 27.66, 0.808, -6.81]}, {"time": 0.9, "value": -6.81}, {"time": 1.3333, "value": 6.84}]}, "AntlerLeft": {"rotate": [{"value": 18.08, "curve": [0.017, 18.08, 0.05, -69.45]}, {"time": 0.0667, "value": -69.45, "curve": [0.108, -69.45, 0.192, 46.02]}, {"time": 0.2333, "value": 46.02, "curve": [0.308, 46.02, 0.458, -21.83]}, {"time": 0.5333, "value": -21.83, "curve": [0.625, -21.83, 0.808, 4.91]}, {"time": 0.9, "value": 4.91}, {"time": 1.3333, "value": -6.81}]}, "DangleHandle": {"translate": [{"x": 1.87, "y": -11.03, "curve": [0.087, 1.58, 0.226, 0.11, 0.087, -7.75, 0.226, 8.62]}, {"time": 0.3, "x": 0.11, "y": 8.62, "curve": [0.383, 0.11, 0.55, 1.95, 0.383, 8.62, 0.55, -11.9]}, {"time": 0.6333, "x": 1.95, "y": -11.9, "curve": [0.7, 1.95, 0.833, 0, 0.7, -11.9, 0.833, 3.22]}, {"time": 0.9, "y": 3.22, "curve": [0.917, 0, 0.95, 0.11, 0.917, 3.22, 0.95, 8.62]}, {"time": 0.9667, "x": 0.11, "y": 8.62, "curve": [1.05, 0.11, 1.217, 1.95, 1.05, 8.62, 1.217, -11.9]}, {"time": 1.3, "x": 1.95, "y": -11.9, "curve": [1.31, 1.95, 1.321, 1.92, 1.31, -11.9, 1.321, -11.59]}, {"time": 1.3333, "x": 1.87, "y": -11.03}], "scale": [{"y": 1.416, "curve": [0.012, 1, 0.023, 1, 0.012, 1.454, 0.023, 1.476]}, {"time": 0.0333, "y": 1.476, "curve": [0.098, 1, 0.215, 1, 0.098, 1.476, 0.215, 0.602]}, {"time": 0.3, "y": 0.253, "curve": [0.325, 1, 0.348, 1, 0.325, 0.141, 0.348, 0.07]}, {"time": 0.3667, "y": 0.07, "curve": [0.45, 1, 0.617, 1, 0.45, 0.07, 0.617, 1.476]}, {"time": 0.7, "y": 1.476, "curve": [0.783, 1, 0.95, 1, 0.783, 1.476, 0.95, 0.07]}, {"time": 1.0333, "y": 0.07, "curve": [1.107, 1, 1.247, 1, 1.107, 0.07, 1.247, 1.192]}, {"time": 1.3333, "y": 1.416}]}, "EyeExploder": {"translate": [{"x": -0.82}], "scale": [{"x": 0.563, "y": 0.947, "curve": [0.008, 0.563, 0.025, 1.752, 0.008, 0.947, 0.025, 1.326]}, {"time": 0.0333, "x": 1.752, "y": 1.326, "curve": [0.058, 1.752, 0.108, 0.89, 0.058, 1.326, 0.108, 0.89]}, {"time": 0.1333, "x": 0.89, "y": 0.89, "curve": [0.167, 0.89, 0.233, 0.947, 0.167, 0.89, 0.233, 0.947]}, {"time": 0.2667, "x": 0.947, "y": 0.947}]}, "EyeExploder2": {"translate": [{"x": -0.82}], "scale": [{"x": 0.563, "y": 0.947, "curve": [0.008, 0.563, 0.025, 1.752, 0.008, 0.947, 0.025, 1.326]}, {"time": 0.0333, "x": 1.752, "y": 1.326, "curve": [0.058, 1.752, 0.108, 0.89, 0.058, 1.326, 0.108, 0.89]}, {"time": 0.1333, "x": 0.89, "y": 0.89, "curve": [0.167, 0.89, 0.233, 0.947, 0.167, 0.89, 0.233, 0.947]}, {"time": 0.2667, "x": 0.947, "y": 0.947}]}, "BodyTop": {"rotate": [{"value": -1.64, "curve": [0.087, -0.85, 0.226, 3.05]}, {"time": 0.3, "value": 3.05, "curve": [0.342, 3.05, 0.404, 1.83]}, {"time": 0.4667, "value": 0.6, "curve": [0.529, -0.62, 0.592, -1.84]}, {"time": 0.6333, "value": -1.84, "curve": [0.717, -1.84, 0.883, 3.05]}, {"time": 0.9667, "value": 3.05, "curve": [1.008, 3.05, 1.071, 1.83]}, {"time": 1.1333, "value": 0.6, "curve": [1.196, -0.62, 1.258, -1.84]}, {"time": 1.3, "value": -1.84, "curve": [1.31, -1.84, 1.321, -1.77]}, {"time": 1.3333, "value": -1.64}], "scale": [{"x": 0.734, "y": 0.992, "curve": [0.073, 0.874, 0.151, 1.049, 0.073, 0.852, 0.151, 0.677]}, {"time": 0.2, "x": 1.049, "y": 0.677, "curve": [0.227, 1.049, 0.262, 0.998, 0.227, 0.677, 0.262, 0.728]}, {"time": 0.3, "x": 0.928, "y": 0.798, "curve": [0.321, 0.89, 0.344, 0.845, 0.321, 0.836, 0.344, 0.881]}, {"time": 0.3667, "x": 0.8, "y": 0.926, "curve": [0.429, 0.675, 0.492, 0.551, 0.429, 1.051, 0.492, 1.175]}, {"time": 0.5333, "x": 0.551, "y": 1.175, "curve": [0.702, 0.553, 0.696, 1.049, 0.702, 1.173, 0.696, 0.677]}, {"time": 0.8667, "x": 1.049, "y": 0.677, "curve": [0.95, 1.049, 1.117, 0.551, 0.95, 0.677, 1.117, 1.175]}, {"time": 1.2, "x": 0.551, "y": 1.175, "curve": [1.234, 0.551, 1.282, 0.634, 1.234, 1.175, 1.282, 1.092]}, {"time": 1.3333, "x": 0.734, "y": 0.992}]}, "BodyBtm": {"rotate": [{"value": 1.25, "curve": [0.051, 2.23, 0.099, 3.05]}, {"time": 0.1333, "value": 3.05, "curve": [0.175, 3.05, 0.238, 1.83]}, {"time": 0.3, "value": 0.6, "curve": [0.363, -0.62, 0.425, -1.84]}, {"time": 0.4667, "value": -1.84, "curve": [0.55, -1.84, 0.717, 3.05]}, {"time": 0.8, "value": 3.05, "curve": [0.842, 3.05, 0.904, 1.83]}, {"time": 0.9667, "value": 0.6, "curve": [1.029, -0.62, 1.092, -1.84]}, {"time": 1.1333, "value": -1.84, "curve": [1.182, -1.84, 1.261, -0.12]}, {"time": 1.3333, "value": 1.25}], "scale": [{"x": 0.393, "y": 1.189, "curve": [0.111, 0.502, 0.167, 1.043, 0.111, 1.138, 0.167, 0.881]}, {"time": 0.3, "x": 1.049, "y": 0.879, "curve": [0.392, 1.049, 0.507, 0.78, 0.392, 0.879, 0.507, 1.006]}, {"time": 0.6333, "x": 0.377, "y": 1.197, "curve": [0.77, 0.377, 0.822, 1.042, 0.77, 1.197, 0.822, 0.882]}, {"time": 0.9667, "x": 1.049, "y": 0.879, "curve": [1.059, 1.049, 1.174, 0.78, 1.059, 0.879, 1.174, 1.006]}, {"time": 1.3, "x": 0.377, "y": 1.197, "curve": [1.312, 0.377, 1.323, 0.383, 1.312, 1.197, 1.323, 1.194]}, {"time": 1.3333, "x": 0.393, "y": 1.189}]}, "HornLeft2": {"rotate": [{"value": 2.95, "curve": [0.083, 2.95, 0.25, -3.99]}, {"time": 0.3333, "value": -3.99, "curve": [0.417, -3.99, 0.583, 2.95]}, {"time": 0.6667, "value": 2.95, "curve": [0.75, 2.95, 0.917, -3.99]}, {"time": 1, "value": -3.99, "curve": [1.083, -3.99, 1.25, 2.95]}, {"time": 1.3333, "value": 2.95}], "scale": [{}, {"time": 0.0333, "x": 1.545, "y": 1.217, "curve": [0.058, 1.545, 0.108, 0.81, 0.058, 1.217, 0.108, 1]}, {"time": 0.1333, "x": 0.81, "curve": [0.175, 0.81, 0.258, 1, 0.175, 1, 0.258, 1]}, {"time": 0.3}]}, "HornLeft": {"rotate": [{"value": -2.42, "curve": [0.083, -2.42, 0.25, 5.43]}, {"time": 0.3333, "value": 5.43, "curve": [0.417, 5.43, 0.583, -2.42]}, {"time": 0.6667, "value": -2.42, "curve": [0.75, -2.42, 0.917, 5.43]}, {"time": 1, "value": 5.43, "curve": [1.083, 5.43, 1.25, -2.42]}, {"time": 1.3333, "value": -2.42}], "scale": [{}, {"time": 0.0333, "x": 1.545, "y": 1.217, "curve": [0.058, 1.545, 0.108, 0.81, 0.058, 1.217, 0.108, 1]}, {"time": 0.1333, "x": 0.81, "curve": [0.175, 0.81, 0.258, 1, 0.175, 1, 0.258, 1]}, {"time": 0.3}]}}}, "attack-shoot": {"slots": {"Eye1": {"attachment": [{"name": "Eye1"}, {"time": 1.1, "name": "Eye_Closed"}]}, "Eye1Back": {"attachment": [{"name": "Eye1Back"}, {"time": 1.1}]}, "Eye1Back2": {"attachment": [{"name": "Eye1Back"}, {"time": 1}]}, "Eye1Back3": {"attachment": [{"name": "Eye1Back"}, {"time": 1.0333}]}, "Eye2": {"attachment": [{"name": "Eye1"}, {"time": 1, "name": "Eye_Closed"}]}, "Eye3": {"attachment": [{"name": "Eye1"}, {"time": 1.0333, "name": "Eye_Closed"}]}, "Head": {"attachment": [{"time": 0.0667, "name": "Head2"}, {"time": 0.9333, "name": "Head"}]}, "Spike1": {"attachment": [{"time": 0.9333}]}, "Spike2": {"attachment": [{"time": 0.9333}]}, "Spike3": {"attachment": [{"time": 0.9333}]}, "Spike4": {"attachment": [{"time": 0.9333}]}, "Spike5": {"attachment": [{"time": 0.9333}]}, "Spike6": {"attachment": [{"time": 0.9333}]}, "Spike7": {"attachment": [{"time": 0.9333}]}, "Spike8": {"attachment": [{"time": 0.9333}]}, "Spike9": {"attachment": [{"time": 0.9333}]}, "Spike10": {"attachment": [{"time": 0.9333}]}, "Spike11": {"attachment": [{"time": 0.9333}]}}, "bones": {"Eyeball1": {"scale": [{"x": 0.563, "y": 0.947, "curve": [0.017, 0.563, 0.05, 2.141, 0.017, 0.947, 0.05, 1.621]}, {"time": 0.0667, "x": 2.141, "y": 1.621, "curve": [0.092, 2.141, 0.142, 0.89, 0.092, 1.621, 0.142, 0.89]}, {"time": 0.1667, "x": 0.89, "y": 0.89, "curve": [0.2, 0.89, 0.267, 1.196, 0.2, 0.89, 0.267, 1.196]}, {"time": 0.3, "x": 1.196, "y": 1.196}, {"time": 0.5, "x": 0.947, "y": 0.947, "curve": [0.588, 0.964, 0.702, 1, 0.588, 0.964, 0.702, 1]}, {"time": 0.7667, "curve": [0.825, 1, 0.942, 0.937, 0.825, 1, 0.942, 0.937]}, {"time": 1, "x": 0.937, "y": 0.937, "curve": [1.019, 0.937, 1.041, 0.941, 1.019, 0.937, 1.041, 0.831]}, {"time": 1.0667, "x": 0.947, "y": 0.671, "curve": [1.1, 0.849, 1.142, 0.643, 1.1, 0.71, 1.142, 0.789]}, {"time": 1.1667, "x": 0.643, "y": 0.789}, {"time": 1.2333, "x": 0.713, "y": 0.876}, {"time": 1.3333, "x": 0.643, "y": 0.789}]}, "Eye1": {"translate": [{"x": -0.82, "curve": "stepped"}, {"time": 0.5, "x": -0.82, "curve": [0.563, 0.4, 0.625, 1.62, 0.563, 0, 0.625, 0]}, {"time": 0.6667, "x": 1.62, "curve": [0.75, 1.62, 0.917, -3.26, 0.75, 0, 0.917, 0]}, {"time": 1, "x": -3.26, "curve": [1.042, -3.26, 1.104, -2.04, 1.042, 0, 1.104, 0]}, {"time": 1.1667, "x": -0.82}], "scale": [{"time": 0.9, "curve": [0.917, 1, 0.95, 1.173, 0.917, 1, 0.95, 1]}, {"time": 0.9667, "x": 1.173, "curve": [0.992, 1.173, 1.054, 0.805, 0.992, 1, 1.054, 0.779]}, {"time": 1.0667, "x": 0.468, "y": 0.577, "curve": "stepped"}, {"time": 1.1, "x": 0.684, "y": 0.839, "curve": [1.133, 0.684, 1.2, 1.109, 1.133, 0.839, 1.2, 1.109]}, {"time": 1.2333, "x": 1.109, "y": 1.109, "curve": [1.258, 1.109, 1.308, 0.993, 1.258, 1.109, 1.308, 0.993]}, {"time": 1.3333, "x": 0.993, "y": 0.993}]}, "Eye2": {"translate": [{"x": -0.82, "curve": "stepped"}, {"time": 0.5, "x": -0.82, "curve": [0.563, 0.4, 0.625, 1.62, 0.563, 0, 0.625, 0]}, {"time": 0.6667, "x": 1.62, "curve": [0.725, 1.62, 0.842, -3.26, 0.725, 0, 0.842, 0]}, {"time": 0.9, "x": -3.26, "curve": [0.942, -3.26, 1.004, -2.04, 0.942, 0, 1.004, 0]}, {"time": 1.0667, "x": -0.82}], "scale": [{"time": 0.8, "curve": [0.817, 1, 0.85, 1.173, 0.817, 1, 0.85, 1]}, {"time": 0.8667, "x": 1.173, "curve": [0.892, 1.173, 0.954, 0.805, 0.892, 1, 0.954, 0.779]}, {"time": 0.9667, "x": 0.468, "y": 0.577, "curve": "stepped"}, {"time": 1, "x": 0.679, "y": 0.833, "curve": [1.033, 0.679, 1.1, 1.109, 1.033, 0.833, 1.1, 1.109]}, {"time": 1.1333, "x": 1.109, "y": 1.109, "curve": [1.158, 1.109, 1.208, 0.993, 1.158, 1.109, 1.208, 0.993]}, {"time": 1.2333, "x": 0.993, "y": 0.993}]}, "Eye3": {"translate": [{"x": -0.82, "curve": "stepped"}, {"time": 0.5, "x": -0.82, "curve": [0.563, 0.4, 0.625, 1.62, 0.563, 0, 0.625, 0]}, {"time": 0.6667, "x": 1.62, "curve": [0.733, 1.62, 0.867, -3.26, 0.733, 0, 0.867, 0]}, {"time": 0.9333, "x": -3.26, "curve": [0.975, -3.26, 1.038, -2.04, 0.975, 0, 1.038, 0]}, {"time": 1.1, "x": -0.82}], "scale": [{"time": 0.8333, "curve": [0.85, 1, 0.883, 1.173, 0.85, 1, 0.883, 1]}, {"time": 0.9, "x": 1.173, "curve": [0.925, 1.173, 0.987, 0.805, 0.925, 1, 0.987, 0.779]}, {"time": 1, "x": 0.468, "y": 0.577, "curve": "stepped"}, {"time": 1.0333, "x": 0.679, "y": 0.833, "curve": [1.058, 0.679, 1.108, 1.109, 1.058, 0.833, 1.108, 1.109]}, {"time": 1.1333, "x": 1.109, "y": 1.109, "curve": [1.167, 1.109, 1.233, 0.993, 1.167, 1.109, 1.233, 0.993]}, {"time": 1.2667, "x": 0.993, "y": 0.993}]}, "Eyeball2": {"scale": [{"x": 0.563, "y": 0.947, "curve": [0.017, 0.563, 0.05, 2.141, 0.017, 0.947, 0.05, 1.621]}, {"time": 0.0667, "x": 2.141, "y": 1.621, "curve": [0.092, 2.141, 0.142, 0.89, 0.092, 1.621, 0.142, 0.89]}, {"time": 0.1667, "x": 0.89, "y": 0.89, "curve": [0.2, 0.89, 0.267, 1.196, 0.2, 0.89, 0.267, 1.196]}, {"time": 0.3, "x": 1.196, "y": 1.196}, {"time": 0.5, "x": 0.947, "y": 0.947, "curve": [0.588, 0.964, 0.702, 1, 0.588, 0.964, 0.702, 1]}, {"time": 0.7667, "curve": [0.825, 1, 0.942, 0.937, 0.825, 1, 0.942, 0.937]}, {"time": 1, "x": 0.937, "y": 0.937, "curve": [1.019, 0.937, 1.041, 0.941, 1.019, 0.937, 1.041, 0.941]}, {"time": 1.0667, "x": 0.947, "y": 0.947}]}, "Eyeball3": {"scale": [{"x": 0.563, "y": 0.947, "curve": [0.017, 0.563, 0.05, 2.141, 0.017, 0.947, 0.05, 1.621]}, {"time": 0.0667, "x": 2.141, "y": 1.621, "curve": [0.092, 2.141, 0.142, 0.89, 0.092, 1.621, 0.142, 0.89]}, {"time": 0.1667, "x": 0.89, "y": 0.89, "curve": [0.2, 0.89, 0.267, 1.196, 0.2, 0.89, 0.267, 1.196]}, {"time": 0.3, "x": 1.196, "y": 1.196}, {"time": 0.5, "x": 0.947, "y": 0.947, "curve": [0.588, 0.964, 0.702, 1, 0.588, 0.964, 0.702, 1]}, {"time": 0.7667, "curve": [0.833, 1, 0.967, 0.937, 0.833, 1, 0.967, 0.937]}, {"time": 1.0333, "x": 0.937, "y": 0.937, "curve": [1.052, 0.937, 1.075, 0.941, 1.052, 0.937, 1.075, 0.941]}, {"time": 1.1, "x": 0.947, "y": 0.947}]}, "Spike1": {"scale": [{"x": 0.06, "curve": [0.008, 0.06, 0.025, 1.433, 0.008, 1, 0.025, 0.95]}, {"time": 0.0333, "x": 1.433, "y": 0.95, "curve": [0.058, 1.433, 0.108, 0.7, 0.058, 0.95, 0.108, 1.127]}, {"time": 0.1333, "x": 0.7, "y": 1.127, "curve": [0.167, 0.7, 0.233, 1.09, 0.167, 1.127, 0.233, 0.955]}, {"time": 0.2667, "x": 1.09, "y": 0.955, "curve": [0.317, 1.09, 0.417, 1, 0.317, 0.955, 0.417, 1]}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.7333}, {"time": 0.8333, "x": 1.114, "curve": [0.858, 1.114, 0.929, 0.376, 0.858, 1, 0.929, 1]}, {"time": 0.9333, "x": 0.09}]}, "Spike2": {"scale": [{"x": 0.06, "curve": [0.008, 0.06, 0.025, 1.433, 0.008, 1, 0.025, 0.95]}, {"time": 0.0333, "x": 1.433, "y": 0.95, "curve": [0.058, 1.433, 0.108, 0.7, 0.058, 0.95, 0.108, 1.127]}, {"time": 0.1333, "x": 0.7, "y": 1.127, "curve": [0.167, 0.7, 0.233, 1.09, 0.167, 1.127, 0.233, 0.955]}, {"time": 0.2667, "x": 1.09, "y": 0.955, "curve": [0.317, 1.09, 0.417, 1, 0.317, 0.955, 0.417, 1]}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.7333}, {"time": 0.8333, "x": 1.114, "curve": [0.858, 1.114, 0.929, 0.376, 0.858, 1, 0.929, 1]}, {"time": 0.9333, "x": 0.09}]}, "Spike3": {"scale": [{"x": 0.06, "curve": [0.008, 0.06, 0.025, 1.433, 0.008, 1, 0.025, 0.95]}, {"time": 0.0333, "x": 1.433, "y": 0.95, "curve": [0.058, 1.433, 0.108, 0.7, 0.058, 0.95, 0.108, 1.127]}, {"time": 0.1333, "x": 0.7, "y": 1.127, "curve": [0.167, 0.7, 0.233, 1.09, 0.167, 1.127, 0.233, 0.955]}, {"time": 0.2667, "x": 1.09, "y": 0.955, "curve": [0.317, 1.09, 0.417, 1, 0.317, 0.955, 0.417, 1]}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.7333}, {"time": 0.8333, "x": 1.114, "curve": [0.858, 1.114, 0.929, 0.376, 0.858, 1, 0.929, 1]}, {"time": 0.9333, "x": 0.09}]}, "Spike4": {"scale": [{"x": 0.06, "curve": [0.008, 0.06, 0.025, 1.433, 0.008, 1, 0.025, 0.95]}, {"time": 0.0333, "x": 1.433, "y": 0.95, "curve": [0.058, 1.433, 0.108, 0.7, 0.058, 0.95, 0.108, 1.127]}, {"time": 0.1333, "x": 0.7, "y": 1.127, "curve": [0.167, 0.7, 0.233, 1.09, 0.167, 1.127, 0.233, 0.955]}, {"time": 0.2667, "x": 1.09, "y": 0.955, "curve": [0.317, 1.09, 0.417, 1, 0.317, 0.955, 0.417, 1]}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.7333}, {"time": 0.8333, "x": 1.114, "curve": [0.858, 1.114, 0.929, 0.376, 0.858, 1, 0.929, 1]}, {"time": 0.9333, "x": 0.09}]}, "Spike5": {"scale": [{"x": 0.06, "curve": [0.008, 0.06, 0.025, 1.433, 0.008, 1, 0.025, 0.95]}, {"time": 0.0333, "x": 1.433, "y": 0.95, "curve": [0.058, 1.433, 0.108, 0.7, 0.058, 0.95, 0.108, 1.127]}, {"time": 0.1333, "x": 0.7, "y": 1.127, "curve": [0.167, 0.7, 0.233, 1.09, 0.167, 1.127, 0.233, 0.955]}, {"time": 0.2667, "x": 1.09, "y": 0.955, "curve": [0.317, 1.09, 0.417, 1, 0.317, 0.955, 0.417, 1]}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.7333}, {"time": 0.8333, "x": 1.114, "curve": [0.858, 1.114, 0.929, 0.376, 0.858, 1, 0.929, 1]}, {"time": 0.9333, "x": 0.09}]}, "Spike6": {"scale": [{"x": 0.06, "curve": [0.008, 0.06, 0.025, 1.433, 0.008, 1, 0.025, 0.95]}, {"time": 0.0333, "x": 1.433, "y": 0.95, "curve": [0.058, 1.433, 0.108, 0.7, 0.058, 0.95, 0.108, 1.127]}, {"time": 0.1333, "x": 0.7, "y": 1.127, "curve": [0.167, 0.7, 0.233, 1.09, 0.167, 1.127, 0.233, 0.955]}, {"time": 0.2667, "x": 1.09, "y": 0.955, "curve": [0.317, 1.09, 0.417, 1, 0.317, 0.955, 0.417, 1]}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.7333}, {"time": 0.8333, "x": 1.114, "curve": [0.858, 1.114, 0.929, 0.376, 0.858, 1, 0.929, 1]}, {"time": 0.9333, "x": 0.09}]}, "Spike7": {"scale": [{"x": 0.06, "curve": [0.008, 0.06, 0.025, 1.433, 0.008, 1, 0.025, 0.95]}, {"time": 0.0333, "x": 1.433, "y": 0.95, "curve": [0.058, 1.433, 0.108, 0.7, 0.058, 0.95, 0.108, 1.127]}, {"time": 0.1333, "x": 0.7, "y": 1.127, "curve": [0.167, 0.7, 0.233, 1.09, 0.167, 1.127, 0.233, 0.955]}, {"time": 0.2667, "x": 1.09, "y": 0.955, "curve": [0.317, 1.09, 0.417, 1, 0.317, 0.955, 0.417, 1]}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.7333}, {"time": 0.8333, "x": 1.114, "curve": [0.858, 1.114, 0.929, 0.376, 0.858, 1, 0.929, 1]}, {"time": 0.9333, "x": 0.09}]}, "Spike8": {"scale": [{"x": 0.06, "curve": [0.008, 0.06, 0.025, 1.433, 0.008, 1, 0.025, 0.95]}, {"time": 0.0333, "x": 1.433, "y": 0.95, "curve": [0.058, 1.433, 0.108, 0.7, 0.058, 0.95, 0.108, 1.127]}, {"time": 0.1333, "x": 0.7, "y": 1.127, "curve": [0.167, 0.7, 0.233, 1.09, 0.167, 1.127, 0.233, 0.955]}, {"time": 0.2667, "x": 1.09, "y": 0.955, "curve": [0.317, 1.09, 0.417, 1, 0.317, 0.955, 0.417, 1]}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.7333}, {"time": 0.8333, "x": 1.114, "curve": [0.858, 1.114, 0.929, 0.376, 0.858, 1, 0.929, 1]}, {"time": 0.9333, "x": 0.09}]}, "Spike9": {"scale": [{"x": 0.06, "curve": [0.008, 0.06, 0.025, 1.433, 0.008, 1, 0.025, 0.95]}, {"time": 0.0333, "x": 1.433, "y": 0.95, "curve": [0.058, 1.433, 0.108, 0.7, 0.058, 0.95, 0.108, 1.127]}, {"time": 0.1333, "x": 0.7, "y": 1.127, "curve": [0.167, 0.7, 0.233, 1.09, 0.167, 1.127, 0.233, 0.955]}, {"time": 0.2667, "x": 1.09, "y": 0.955, "curve": [0.317, 1.09, 0.417, 1, 0.317, 0.955, 0.417, 1]}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.7333}, {"time": 0.8333, "x": 1.114, "curve": [0.858, 1.114, 0.929, 0.376, 0.858, 1, 0.929, 1]}, {"time": 0.9333, "x": 0.09}]}, "Spike10": {"scale": [{"x": 0.06, "curve": [0.008, 0.06, 0.025, 1.433, 0.008, 1, 0.025, 0.95]}, {"time": 0.0333, "x": 1.433, "y": 0.95, "curve": [0.058, 1.433, 0.108, 0.7, 0.058, 0.95, 0.108, 1.127]}, {"time": 0.1333, "x": 0.7, "y": 1.127, "curve": [0.167, 0.7, 0.233, 1.09, 0.167, 1.127, 0.233, 0.955]}, {"time": 0.2667, "x": 1.09, "y": 0.955, "curve": [0.317, 1.09, 0.417, 1, 0.317, 0.955, 0.417, 1]}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.7333}, {"time": 0.8333, "x": 1.114, "curve": [0.858, 1.114, 0.929, 0.376, 0.858, 1, 0.929, 1]}, {"time": 0.9333, "x": 0.09}]}, "Spike11": {"scale": [{"x": 0.06, "curve": [0.008, 0.06, 0.025, 1.433, 0.008, 1, 0.025, 0.95]}, {"time": 0.0333, "x": 1.433, "y": 0.95, "curve": [0.058, 1.433, 0.108, 0.7, 0.058, 0.95, 0.108, 1.127]}, {"time": 0.1333, "x": 0.7, "y": 1.127, "curve": [0.167, 0.7, 0.233, 1.09, 0.167, 1.127, 0.233, 0.955]}, {"time": 0.2667, "x": 1.09, "y": 0.955, "curve": [0.317, 1.09, 0.417, 1, 0.317, 0.955, 0.417, 1]}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.7333}, {"time": 0.8333, "x": 1.114, "curve": [0.858, 1.114, 0.929, 0.376, 0.858, 1, 0.929, 1]}, {"time": 0.9333, "x": 0.09}]}, "Body": {"translate": [{"x": 1.34, "y": -64.35, "curve": [0.017, 1.34, 0.05, -0.4, 0.017, -64.35, 0.05, 19.41]}, {"time": 0.0667, "x": -0.4, "y": 19.41, "curve": [0.1, -0.4, 0.167, 0.95, 0.1, 19.41, 0.167, -45.76]}, {"time": 0.2, "x": 0.95, "y": -45.76, "curve": [0.258, 0.95, 0.375, 0.19, 0.258, -45.76, 0.375, -9.07]}, {"time": 0.4333, "x": 0.19, "y": -9.07, "curve": [0.558, 0.19, 0.808, 0.73, 0.558, -9.07, 0.808, -34.92]}, {"time": 0.9333, "x": 0.73, "y": -34.92, "curve": [1.033, 0.73, 1.233, 0, 1.033, -34.92, 1.233, 0]}, {"time": 1.3333}], "scale": [{"x": 0.832, "y": 1.115, "curve": [0.017, 0.832, 0.05, 1.154, 0.017, 1.115, 0.05, 0.771]}, {"time": 0.0667, "x": 1.154, "y": 0.771, "curve": [0.1, 1.154, 0.167, 0.816, 0.1, 0.771, 0.167, 1.285]}, {"time": 0.2, "x": 0.816, "y": 1.285, "curve": [0.258, 0.816, 0.375, 1.084, 0.258, 1.285, 0.375, 0.91]}, {"time": 0.4333, "x": 1.084, "y": 0.91}, {"time": 0.7333, "x": 0.983, "y": 1.016, "curve": [0.833, 0.983, 1.033, 1.024, 0.833, 1.016, 1.033, 0.959]}, {"time": 1.1333, "x": 1.024, "y": 0.959, "curve": [1.183, 1.024, 1.283, 1.004, 1.183, 0.959, 1.283, 0.988]}, {"time": 1.3333, "x": 1.004, "y": 0.988}]}, "AntlerRight": {"rotate": [{"value": -17.11, "curve": [0.017, -17.11, 0.05, 64.15]}, {"time": 0.0667, "value": 64.15, "curve": [0.108, 64.15, 0.192, -66.36]}, {"time": 0.2333, "value": -66.36, "curve": [0.308, -66.36, 0.458, 27.66]}, {"time": 0.5333, "value": 27.66, "curve": [0.625, 27.66, 0.808, -6.81]}, {"time": 0.9, "value": -6.81}, {"time": 1.3333, "value": 6.84}]}, "AntlerLeft": {"rotate": [{"value": 18.08, "curve": [0.017, 18.08, 0.05, -69.45]}, {"time": 0.0667, "value": -69.45, "curve": [0.108, -69.45, 0.192, 46.02]}, {"time": 0.2333, "value": 46.02, "curve": [0.308, 46.02, 0.458, -21.83]}, {"time": 0.5333, "value": -21.83, "curve": [0.625, -21.83, 0.808, 4.91]}, {"time": 0.9, "value": 4.91}, {"time": 1.3333, "value": -6.81}]}, "DangleHandle": {"translate": [{"x": 1.87, "y": -11.03, "curve": [0.087, 1.58, 0.226, 0.11, 0.087, -7.75, 0.226, 8.62]}, {"time": 0.3, "x": 0.11, "y": 8.62, "curve": [0.383, 0.11, 0.55, 1.95, 0.383, 8.62, 0.55, -11.9]}, {"time": 0.6333, "x": 1.95, "y": -11.9, "curve": [0.7, 1.95, 0.833, 0, 0.7, -11.9, 0.833, 3.22]}, {"time": 0.9, "y": 3.22, "curve": [0.917, 0, 0.95, 0.11, 0.917, 3.22, 0.95, 8.62]}, {"time": 0.9667, "x": 0.11, "y": 8.62, "curve": [1.05, 0.11, 1.217, 1.95, 1.05, 8.62, 1.217, -11.9]}, {"time": 1.3, "x": 1.95, "y": -11.9, "curve": [1.31, 1.95, 1.321, 1.92, 1.31, -11.9, 1.321, -11.59]}, {"time": 1.3333, "x": 1.87, "y": -11.03}], "scale": [{"y": 1.416, "curve": [0.012, 1, 0.023, 1, 0.012, 1.454, 0.023, 1.476]}, {"time": 0.0333, "y": 1.476, "curve": [0.098, 1, 0.215, 1, 0.098, 1.476, 0.215, 0.602]}, {"time": 0.3, "y": 0.253, "curve": [0.325, 1, 0.348, 1, 0.325, 0.141, 0.348, 0.07]}, {"time": 0.3667, "y": 0.07, "curve": [0.45, 1, 0.617, 1, 0.45, 0.07, 0.617, 1.476]}, {"time": 0.7, "y": 1.476, "curve": [0.783, 1, 0.95, 1, 0.783, 1.476, 0.95, 0.07]}, {"time": 1.0333, "y": 0.07, "curve": [1.107, 1, 1.247, 1, 1.107, 0.07, 1.247, 1.192]}, {"time": 1.3333, "y": 1.416}]}, "EyeExploder": {"translate": [{"x": -0.82}], "scale": [{"x": 0.563, "y": 0.947, "curve": [0.008, 0.563, 0.025, 1.752, 0.008, 0.947, 0.025, 1.326]}, {"time": 0.0333, "x": 1.752, "y": 1.326, "curve": [0.058, 1.752, 0.108, 0.89, 0.058, 1.326, 0.108, 0.89]}, {"time": 0.1333, "x": 0.89, "y": 0.89, "curve": [0.167, 0.89, 0.233, 0.947, 0.167, 0.89, 0.233, 0.947]}, {"time": 0.2667, "x": 0.947, "y": 0.947}]}, "EyeExploder2": {"translate": [{"x": -0.82}], "scale": [{"x": 0.563, "y": 0.947, "curve": [0.008, 0.563, 0.025, 1.752, 0.008, 0.947, 0.025, 1.326]}, {"time": 0.0333, "x": 1.752, "y": 1.326, "curve": [0.058, 1.752, 0.108, 0.89, 0.058, 1.326, 0.108, 0.89]}, {"time": 0.1333, "x": 0.89, "y": 0.89, "curve": [0.167, 0.89, 0.233, 0.947, 0.167, 0.89, 0.233, 0.947]}, {"time": 0.2667, "x": 0.947, "y": 0.947}]}, "BodyTop": {"rotate": [{"value": -1.64, "curve": [0.087, -0.85, 0.226, 3.05]}, {"time": 0.3, "value": 3.05, "curve": [0.342, 3.05, 0.404, 1.83]}, {"time": 0.4667, "value": 0.6, "curve": [0.529, -0.62, 0.592, -1.84]}, {"time": 0.6333, "value": -1.84, "curve": [0.717, -1.84, 0.883, 3.05]}, {"time": 0.9667, "value": 3.05, "curve": [1.008, 3.05, 1.071, 1.83]}, {"time": 1.1333, "value": 0.6, "curve": [1.196, -0.62, 1.258, -1.84]}, {"time": 1.3, "value": -1.84, "curve": [1.31, -1.84, 1.321, -1.77]}, {"time": 1.3333, "value": -1.64}], "scale": [{"x": 0.734, "y": 0.992, "curve": [0.073, 0.874, 0.151, 1.049, 0.073, 0.852, 0.151, 0.677]}, {"time": 0.2, "x": 1.049, "y": 0.677, "curve": [0.227, 1.049, 0.262, 0.998, 0.227, 0.677, 0.262, 0.728]}, {"time": 0.3, "x": 0.928, "y": 0.798, "curve": [0.321, 0.89, 0.344, 0.845, 0.321, 0.836, 0.344, 0.881]}, {"time": 0.3667, "x": 0.8, "y": 0.926, "curve": [0.429, 0.675, 0.492, 0.551, 0.429, 1.051, 0.492, 1.175]}, {"time": 0.5333, "x": 0.551, "y": 1.175, "curve": [0.702, 0.553, 0.696, 1.049, 0.702, 1.173, 0.696, 0.677]}, {"time": 0.8667, "x": 1.049, "y": 0.677, "curve": [0.95, 1.049, 1.117, 0.551, 0.95, 0.677, 1.117, 1.175]}, {"time": 1.2, "x": 0.551, "y": 1.175, "curve": [1.234, 0.551, 1.282, 0.634, 1.234, 1.175, 1.282, 1.092]}, {"time": 1.3333, "x": 0.734, "y": 0.992}]}, "BodyBtm": {"rotate": [{"value": 1.25, "curve": [0.051, 2.23, 0.099, 3.05]}, {"time": 0.1333, "value": 3.05, "curve": [0.175, 3.05, 0.238, 1.83]}, {"time": 0.3, "value": 0.6, "curve": [0.363, -0.62, 0.425, -1.84]}, {"time": 0.4667, "value": -1.84, "curve": [0.55, -1.84, 0.717, 3.05]}, {"time": 0.8, "value": 3.05, "curve": [0.842, 3.05, 0.904, 1.83]}, {"time": 0.9667, "value": 0.6, "curve": [1.029, -0.62, 1.092, -1.84]}, {"time": 1.1333, "value": -1.84, "curve": [1.182, -1.84, 1.261, -0.12]}, {"time": 1.3333, "value": 1.25}], "scale": [{"x": 0.393, "y": 1.189, "curve": [0.111, 0.502, 0.167, 1.043, 0.111, 1.138, 0.167, 0.881]}, {"time": 0.3, "x": 1.049, "y": 0.879, "curve": [0.392, 1.049, 0.507, 0.78, 0.392, 0.879, 0.507, 1.006]}, {"time": 0.6333, "x": 0.377, "y": 1.197, "curve": [0.77, 0.377, 0.822, 1.042, 0.77, 1.197, 0.822, 0.882]}, {"time": 0.9667, "x": 1.049, "y": 0.879, "curve": [1.059, 1.049, 1.174, 0.78, 1.059, 0.879, 1.174, 1.006]}, {"time": 1.3, "x": 0.377, "y": 1.197, "curve": [1.312, 0.377, 1.323, 0.383, 1.312, 1.197, 1.323, 1.194]}, {"time": 1.3333, "x": 0.393, "y": 1.189}]}}}, "background-floating": {"slots": {"Spike1": {"attachment": [{}]}, "Spike2": {"attachment": [{}]}, "Spike3": {"attachment": [{}]}, "Spike4": {"attachment": [{}]}, "Spike5": {"attachment": [{}]}, "Spike6": {"attachment": [{}]}, "Spike7": {"attachment": [{}]}, "Spike8": {"attachment": [{}]}, "Spike9": {"attachment": [{}]}, "Spike10": {"attachment": [{}]}, "Spike11": {"attachment": [{}]}}, "bones": {"Spike1": {"scale": [{"x": 0.06}]}, "Spike2": {"scale": [{"x": 0.06}]}, "Spike3": {"scale": [{"x": 0.06}]}, "Spike4": {"scale": [{"x": 0.06}]}, "Spike5": {"scale": [{"x": 0.06}]}, "Spike6": {"scale": [{"x": 0.06}]}, "Spike7": {"scale": [{"x": 0.06}]}, "Spike8": {"scale": [{"x": 0.06}]}, "Spike9": {"scale": [{"x": 0.06}]}, "Spike10": {"scale": [{"x": 0.06}]}, "Spike11": {"scale": [{"x": 0.06}]}, "Body": {"translate": [{"x": -2.95, "y": 141.62, "curve": [1, -2.95, 3, -5.37, 1, 141.62, 3, 257.95]}, {"time": 4, "x": -5.37, "y": 257.95, "curve": [5, -5.37, 7, -2.95, 5, 257.95, 7, 141.62]}, {"time": 8, "x": -2.95, "y": 141.62}], "scale": [{"x": 0.984, "y": 0.941, "curve": [0.125, 0.964, 0.25, 0.944, 0.125, 0.978, 0.25, 1.016]}, {"time": 0.3333, "x": 0.944, "y": 1.016, "curve": [0.5, 0.944, 0.833, 1.024, 0.5, 1.016, 0.833, 0.865]}, {"time": 1, "x": 1.024, "y": 0.865, "curve": [1.167, 1.024, 1.5, 0.944, 1.167, 0.865, 1.5, 1.016]}, {"time": 1.6667, "x": 0.944, "y": 1.016, "curve": [1.833, 0.944, 2.167, 1.024, 1.833, 1.016, 2.167, 0.865]}, {"time": 2.3333, "x": 1.024, "y": 0.865, "curve": [2.417, 1.024, 2.542, 1.004, 2.417, 0.865, 2.542, 0.903]}, {"time": 2.6667, "x": 0.984, "y": 0.941, "curve": [2.792, 0.964, 2.917, 0.944, 2.792, 0.978, 2.917, 1.016]}, {"time": 3, "x": 0.944, "y": 1.016, "curve": [3.167, 0.944, 3.5, 1.024, 3.167, 1.016, 3.5, 0.865]}, {"time": 3.6667, "x": 1.024, "y": 0.865, "curve": [3.833, 1.024, 4.167, 0.944, 3.833, 0.865, 4.167, 1.016]}, {"time": 4.3333, "x": 0.944, "y": 1.016, "curve": [4.5, 0.944, 4.833, 1.024, 4.5, 1.016, 4.833, 0.865]}, {"time": 5, "x": 1.024, "y": 0.865, "curve": [5.083, 1.024, 5.208, 1.004, 5.083, 0.865, 5.208, 0.903]}, {"time": 5.3333, "x": 0.984, "y": 0.941, "curve": [5.458, 0.964, 5.583, 0.944, 5.458, 0.978, 5.583, 1.016]}, {"time": 5.6667, "x": 0.944, "y": 1.016, "curve": [5.833, 0.944, 6.167, 1.024, 5.833, 1.016, 6.167, 0.865]}, {"time": 6.3333, "x": 1.024, "y": 0.865, "curve": [6.5, 1.024, 6.833, 0.944, 6.5, 0.865, 6.833, 1.016]}, {"time": 7, "x": 0.944, "y": 1.016, "curve": [7.167, 0.944, 7.5, 1.024, 7.167, 1.016, 7.5, 0.865]}, {"time": 7.6667, "x": 1.024, "y": 0.865, "curve": [7.75, 1.024, 7.875, 1.004, 7.75, 0.865, 7.875, 0.903]}, {"time": 8, "x": 0.984, "y": 0.941}]}, "BodyTop": {"rotate": [{"value": 3.05, "curve": [0.083, 3.05, 0.208, 1.83]}, {"time": 0.3333, "value": 0.6, "curve": [0.458, -0.62, 0.583, -1.84]}, {"time": 0.6667, "value": -1.84, "curve": [0.833, -1.84, 1.167, 3.05]}, {"time": 1.3333, "value": 3.05, "curve": [1.417, 3.05, 1.542, 1.83]}, {"time": 1.6667, "value": 0.6, "curve": [1.792, -0.62, 1.917, -1.84]}, {"time": 2, "value": -1.84, "curve": [2.167, -1.84, 2.5, 3.05]}, {"time": 2.6667, "value": 3.05, "curve": [2.75, 3.05, 2.875, 1.83]}, {"time": 3, "value": 0.6, "curve": [3.125, -0.62, 3.25, -1.84]}, {"time": 3.3333, "value": -1.84, "curve": [3.5, -1.84, 3.833, 3.05]}, {"time": 4, "value": 3.05, "curve": [4.083, 3.05, 4.208, 1.83]}, {"time": 4.3333, "value": 0.6, "curve": [4.458, -0.62, 4.583, -1.84]}, {"time": 4.6667, "value": -1.84, "curve": [4.833, -1.84, 5.167, 3.05]}, {"time": 5.3333, "value": 3.05, "curve": [5.417, 3.05, 5.542, 1.83]}, {"time": 5.6667, "value": 0.6, "curve": [5.792, -0.62, 5.917, -1.84]}, {"time": 6, "value": -1.84, "curve": [6.167, -1.84, 6.5, 3.05]}, {"time": 6.6667, "value": 3.05, "curve": [6.75, 3.05, 6.875, 1.83]}, {"time": 7, "value": 0.6, "curve": [7.125, -0.62, 7.25, -1.84]}, {"time": 7.3333, "value": -1.84, "curve": [7.5, -1.84, 7.833, 3.05]}, {"time": 8, "value": 3.05}], "scale": [{"x": 0.928, "y": 0.798, "curve": [0.043, 0.89, 0.088, 0.845, 0.043, 0.836, 0.088, 0.881]}, {"time": 0.1333, "x": 0.8, "y": 0.926, "curve": [0.258, 0.675, 0.383, 0.551, 0.258, 1.051, 0.383, 1.175]}, {"time": 0.4667, "x": 0.551, "y": 1.175, "curve": [0.804, 0.553, 0.792, 1.049, 0.804, 1.173, 0.792, 0.677]}, {"time": 1.1333, "x": 1.049, "y": 0.677, "curve": [1.3, 1.049, 1.633, 0.551, 1.3, 0.677, 1.633, 1.175]}, {"time": 1.8, "x": 0.551, "y": 1.175, "curve": [1.967, 0.551, 2.3, 1.049, 1.967, 1.175, 2.3, 0.677]}, {"time": 2.4667, "x": 1.049, "y": 0.677, "curve": [2.52, 1.049, 2.59, 0.998, 2.52, 0.677, 2.59, 0.728]}, {"time": 2.6667, "x": 0.928, "y": 0.798, "curve": [2.71, 0.89, 2.755, 0.845, 2.71, 0.836, 2.755, 0.881]}, {"time": 2.8, "x": 0.8, "y": 0.926, "curve": [2.925, 0.675, 3.05, 0.551, 2.925, 1.051, 3.05, 1.175]}, {"time": 3.1333, "x": 0.551, "y": 1.175, "curve": [3.471, 0.553, 3.459, 1.049, 3.471, 1.173, 3.459, 0.677]}, {"time": 3.8, "x": 1.049, "y": 0.677, "curve": [3.967, 1.049, 4.3, 0.551, 3.967, 0.677, 4.3, 1.175]}, {"time": 4.4667, "x": 0.551, "y": 1.175, "curve": [4.633, 0.551, 4.967, 1.049, 4.633, 1.175, 4.967, 0.677]}, {"time": 5.1333, "x": 1.049, "y": 0.677, "curve": [5.187, 1.049, 5.257, 0.998, 5.187, 0.677, 5.257, 0.728]}, {"time": 5.3333, "x": 0.928, "y": 0.798, "curve": [5.376, 0.89, 5.421, 0.845, 5.376, 0.836, 5.421, 0.881]}, {"time": 5.4667, "x": 0.8, "y": 0.926, "curve": [5.592, 0.675, 5.717, 0.551, 5.592, 1.051, 5.717, 1.175]}, {"time": 5.8, "x": 0.551, "y": 1.175, "curve": [6.137, 0.553, 6.125, 1.049, 6.137, 1.173, 6.125, 0.677]}, {"time": 6.4667, "x": 1.049, "y": 0.677, "curve": [6.633, 1.049, 6.967, 0.551, 6.633, 0.677, 6.967, 1.175]}, {"time": 7.1333, "x": 0.551, "y": 1.175, "curve": [7.3, 0.551, 7.633, 1.049, 7.3, 1.175, 7.633, 0.677]}, {"time": 7.8, "x": 1.049, "y": 0.677, "curve": [7.854, 1.049, 7.924, 0.998, 7.854, 0.677, 7.924, 0.728]}, {"time": 8, "x": 0.928, "y": 0.798}]}, "BodyBtm": {"rotate": [{"value": 0.6, "curve": [0.125, -0.62, 0.25, -1.84]}, {"time": 0.3333, "value": -1.84, "curve": [0.5, -1.84, 0.833, 3.05]}, {"time": 1, "value": 3.05, "curve": [1.083, 3.05, 1.208, 1.83]}, {"time": 1.3333, "value": 0.6, "curve": [1.458, -0.62, 1.583, -1.84]}, {"time": 1.6667, "value": -1.84, "curve": [1.833, -1.84, 2.167, 3.05]}, {"time": 2.3333, "value": 3.05, "curve": [2.417, 3.05, 2.542, 1.83]}, {"time": 2.6667, "value": 0.6, "curve": [2.792, -0.62, 2.917, -1.84]}, {"time": 3, "value": -1.84, "curve": [3.167, -1.84, 3.5, 3.05]}, {"time": 3.6667, "value": 3.05, "curve": [3.75, 3.05, 3.875, 1.83]}, {"time": 4, "value": 0.6, "curve": [4.125, -0.62, 4.25, -1.84]}, {"time": 4.3333, "value": -1.84, "curve": [4.5, -1.84, 4.833, 3.05]}, {"time": 5, "value": 3.05, "curve": [5.083, 3.05, 5.208, 1.83]}, {"time": 5.3333, "value": 0.6, "curve": [5.458, -0.62, 5.583, -1.84]}, {"time": 5.6667, "value": -1.84, "curve": [5.833, -1.84, 6.167, 3.05]}, {"time": 6.3333, "value": 3.05, "curve": [6.417, 3.05, 6.542, 1.83]}, {"time": 6.6667, "value": 0.6, "curve": [6.792, -0.62, 6.917, -1.84]}, {"time": 7, "value": -1.84, "curve": [7.167, -1.84, 7.5, 3.05]}, {"time": 7.6667, "value": 3.05, "curve": [7.75, 3.05, 7.875, 1.83]}, {"time": 8, "value": 0.6}], "scale": [{"x": 1.049, "y": 0.879, "curve": [0.184, 1.049, 0.414, 0.78, 0.184, 0.879, 0.414, 1.006]}, {"time": 0.6667, "x": 0.377, "y": 1.197, "curve": [0.94, 0.377, 1.044, 1.042, 0.94, 1.197, 1.044, 0.882]}, {"time": 1.3333, "x": 1.049, "y": 0.879, "curve": [1.517, 1.049, 1.747, 0.78, 1.517, 0.879, 1.747, 1.006]}, {"time": 2, "x": 0.377, "y": 1.197, "curve": [2.274, 0.377, 2.377, 1.042, 2.274, 1.197, 2.377, 0.882]}, {"time": 2.6667, "x": 1.049, "y": 0.879, "curve": [2.851, 1.049, 3.08, 0.78, 2.851, 0.879, 3.08, 1.006]}, {"time": 3.3333, "x": 0.377, "y": 1.197, "curve": [3.607, 0.377, 3.71, 1.042, 3.607, 1.197, 3.71, 0.882]}, {"time": 4, "x": 1.049, "y": 0.879, "curve": [4.184, 1.049, 4.414, 0.78, 4.184, 0.879, 4.414, 1.006]}, {"time": 4.6667, "x": 0.377, "y": 1.197, "curve": [4.94, 0.377, 5.044, 1.042, 4.94, 1.197, 5.044, 0.882]}, {"time": 5.3333, "x": 1.049, "y": 0.879, "curve": [5.517, 1.049, 5.747, 0.78, 5.517, 0.879, 5.747, 1.006]}, {"time": 6, "x": 0.377, "y": 1.197, "curve": [6.274, 0.377, 6.377, 1.042, 6.274, 1.197, 6.377, 0.882]}, {"time": 6.6667, "x": 1.049, "y": 0.879, "curve": [6.851, 1.049, 7.08, 0.78, 6.851, 0.879, 7.08, 1.006]}, {"time": 7.3333, "x": 0.377, "y": 1.197, "curve": [7.607, 0.377, 7.71, 1.042, 7.607, 1.197, 7.71, 0.882]}, {"time": 8, "x": 1.049, "y": 0.879}]}, "Eye1": {"translate": [{"x": -0.82, "curve": [0.125, 0.4, 0.25, 1.62, 0.125, 0, 0.25, 0]}, {"time": 0.3333, "x": 1.62, "curve": [0.5, 1.62, 0.833, -3.26, 0.5, 0, 0.833, 0]}, {"time": 1, "x": -3.26, "curve": [1.083, -3.26, 1.208, -2.04, 1.083, 0, 1.208, 0]}, {"time": 1.3333, "x": -0.82, "curve": [1.458, 0.4, 1.583, 1.62, 1.458, 0, 1.583, 0]}, {"time": 1.6667, "x": 1.62, "curve": [1.833, 1.62, 2.167, -3.26, 1.833, 0, 2.167, 0]}, {"time": 2.3333, "x": -3.26, "curve": [2.417, -3.26, 2.542, -2.04, 2.417, 0, 2.542, 0]}, {"time": 2.6667, "x": -0.82, "curve": [2.792, 0.4, 2.917, 1.62, 2.792, 0, 2.917, 0]}, {"time": 3, "x": 1.62, "curve": [3.167, 1.62, 3.5, -3.26, 3.167, 0, 3.5, 0]}, {"time": 3.6667, "x": -3.26, "curve": [3.75, -3.26, 3.875, -2.04, 3.75, 0, 3.875, 0]}, {"time": 4, "x": -0.82, "curve": [4.125, 0.4, 4.25, 1.62, 4.125, 0, 4.25, 0]}, {"time": 4.3333, "x": 1.62, "curve": [4.5, 1.62, 4.833, -3.26, 4.5, 0, 4.833, 0]}, {"time": 5, "x": -3.26, "curve": [5.083, -3.26, 5.208, -2.04, 5.083, 0, 5.208, 0]}, {"time": 5.3333, "x": -0.82, "curve": [5.458, 0.4, 5.583, 1.62, 5.458, 0, 5.583, 0]}, {"time": 5.6667, "x": 1.62, "curve": [5.833, 1.62, 6.167, -3.26, 5.833, 0, 6.167, 0]}, {"time": 6.3333, "x": -3.26, "curve": [6.417, -3.26, 6.542, -2.04, 6.417, 0, 6.542, 0]}, {"time": 6.6667, "x": -0.82, "curve": [6.792, 0.4, 6.917, 1.62, 6.792, 0, 6.917, 0]}, {"time": 7, "x": 1.62, "curve": [7.167, 1.62, 7.5, -3.26, 7.167, 0, 7.5, 0]}, {"time": 7.6667, "x": -3.26, "curve": [7.75, -3.26, 7.875, -2.04, 7.75, 0, 7.875, 0]}, {"time": 8, "x": -0.82}], "scale": [{"x": 0.947, "y": 0.947, "curve": [0.176, 0.964, 0.404, 1, 0.176, 0.964, 0.404, 1]}, {"time": 0.5333, "curve": [0.692, 1, 1.008, 0.937, 0.692, 1, 1.008, 0.937]}, {"time": 1.1667, "x": 0.937, "y": 0.937, "curve": [1.213, 0.937, 1.27, 0.941, 1.213, 0.937, 1.27, 0.941]}, {"time": 1.3333, "x": 0.947, "y": 0.947, "curve": [1.51, 0.964, 1.737, 1, 1.51, 0.964, 1.737, 1]}, {"time": 1.8667, "curve": [2.025, 1, 2.342, 0.937, 2.025, 1, 2.342, 0.937]}, {"time": 2.5, "x": 0.937, "y": 0.937, "curve": [2.546, 0.937, 2.604, 0.941, 2.546, 0.937, 2.604, 0.941]}, {"time": 2.6667, "x": 0.947, "y": 0.947, "curve": [2.843, 0.964, 3.071, 1, 2.843, 0.964, 3.071, 1]}, {"time": 3.2, "curve": [3.358, 1, 3.675, 0.937, 3.358, 1, 3.675, 0.937]}, {"time": 3.8333, "x": 0.937, "y": 0.937, "curve": [3.88, 0.937, 3.937, 0.941, 3.88, 0.937, 3.937, 0.941]}, {"time": 4, "x": 0.947, "y": 0.947, "curve": [4.176, 0.964, 4.404, 1, 4.176, 0.964, 4.404, 1]}, {"time": 4.5333, "curve": [4.692, 1, 5.008, 0.937, 4.692, 1, 5.008, 0.937]}, {"time": 5.1667, "x": 0.937, "y": 0.937, "curve": [5.213, 0.937, 5.27, 0.941, 5.213, 0.937, 5.27, 0.941]}, {"time": 5.3333, "x": 0.947, "y": 0.947, "curve": [5.51, 0.964, 5.737, 1, 5.51, 0.964, 5.737, 1]}, {"time": 5.8667, "curve": [6.025, 1, 6.342, 0.937, 6.025, 1, 6.342, 0.937]}, {"time": 6.5, "x": 0.937, "y": 0.937, "curve": [6.546, 0.937, 6.604, 0.941, 6.546, 0.937, 6.604, 0.941]}, {"time": 6.6667, "x": 0.947, "y": 0.947, "curve": [6.843, 0.964, 7.071, 1, 6.843, 0.964, 7.071, 1]}, {"time": 7.2, "curve": [7.358, 1, 7.675, 0.937, 7.358, 1, 7.675, 0.937]}, {"time": 7.8333, "x": 0.937, "y": 0.937, "curve": [7.88, 0.937, 7.937, 0.941, 7.88, 0.937, 7.937, 0.941]}, {"time": 8, "x": 0.947, "y": 0.947}]}, "DangleHandle": {"translate": [{"x": 0.11, "y": 8.62, "curve": [0.167, 0.11, 0.5, 1.95, 0.167, 8.62, 0.5, -11.9]}, {"time": 0.6667, "x": 1.95, "y": -11.9, "curve": [0.833, 1.95, 1.167, 0.11, 0.833, -11.9, 1.167, 8.62]}, {"time": 1.3333, "x": 0.11, "y": 8.62, "curve": [1.5, 0.11, 1.833, 1.95, 1.5, 8.62, 1.833, -11.9]}, {"time": 2, "x": 1.95, "y": -11.9, "curve": [2.167, 1.95, 2.5, 0.11, 2.167, -11.9, 2.5, 8.62]}, {"time": 2.6667, "x": 0.11, "y": 8.62, "curve": [2.833, 0.11, 3.167, 1.95, 2.833, 8.62, 3.167, -11.9]}, {"time": 3.3333, "x": 1.95, "y": -11.9, "curve": [3.5, 1.95, 3.833, 0.11, 3.5, -11.9, 3.833, 8.62]}, {"time": 4, "x": 0.11, "y": 8.62, "curve": [4.167, 0.11, 4.5, 1.95, 4.167, 8.62, 4.5, -11.9]}, {"time": 4.6667, "x": 1.95, "y": -11.9, "curve": [4.833, 1.95, 5.167, 0.11, 4.833, -11.9, 5.167, 8.62]}, {"time": 5.3333, "x": 0.11, "y": 8.62, "curve": [5.5, 0.11, 5.833, 1.95, 5.5, 8.62, 5.833, -11.9]}, {"time": 6, "x": 1.95, "y": -11.9, "curve": [6.167, 1.95, 6.5, 0.11, 6.167, -11.9, 6.5, 8.62]}, {"time": 6.6667, "x": 0.11, "y": 8.62, "curve": [6.833, 0.11, 7.167, 1.95, 6.833, 8.62, 7.167, -11.9]}, {"time": 7.3333, "x": 1.95, "y": -11.9, "curve": [7.5, 1.95, 7.833, 0.11, 7.5, -11.9, 7.833, 8.62]}, {"time": 8, "x": 0.11, "y": 8.62}], "scale": [{"y": 0.253, "curve": [0.05, 1, 0.095, 1, 0.05, 0.141, 0.095, 0.07]}, {"time": 0.1333, "y": 0.07, "curve": [0.3, 1, 0.633, 1, 0.3, 0.07, 0.633, 1.476]}, {"time": 0.8, "y": 1.476, "curve": [0.967, 1, 1.3, 1, 0.967, 1.476, 1.3, 0.07]}, {"time": 1.4667, "y": 0.07, "curve": [1.633, 1, 1.967, 1, 1.633, 0.07, 1.967, 1.476]}, {"time": 2.1333, "y": 1.476, "curve": [2.263, 1, 2.496, 1, 2.263, 1.476, 2.496, 0.602]}, {"time": 2.6667, "y": 0.253, "curve": [2.717, 1, 2.762, 1, 2.717, 0.141, 2.762, 0.07]}, {"time": 2.8, "y": 0.07, "curve": [2.967, 1, 3.3, 1, 2.967, 0.07, 3.3, 1.476]}, {"time": 3.4667, "y": 1.476, "curve": [3.633, 1, 3.967, 1, 3.633, 1.476, 3.967, 0.07]}, {"time": 4.1333, "y": 0.07, "curve": [4.3, 1, 4.633, 1, 4.3, 0.07, 4.633, 1.476]}, {"time": 4.8, "y": 1.476, "curve": [4.929, 1, 5.162, 1, 4.929, 1.476, 5.162, 0.602]}, {"time": 5.3333, "y": 0.253, "curve": [5.383, 1, 5.429, 1, 5.383, 0.141, 5.429, 0.07]}, {"time": 5.4667, "y": 0.07, "curve": [5.633, 1, 5.967, 1, 5.633, 0.07, 5.967, 1.476]}, {"time": 6.1333, "y": 1.476, "curve": [6.3, 1, 6.633, 1, 6.3, 1.476, 6.633, 0.07]}, {"time": 6.8, "y": 0.07, "curve": [6.967, 1, 7.3, 1, 6.967, 0.07, 7.3, 1.476]}, {"time": 7.4667, "y": 1.476, "curve": [7.596, 1, 7.829, 1, 7.596, 1.476, 7.829, 0.602]}, {"time": 8, "y": 0.253}]}, "EyeExploder": {"translate": [{}, {"time": 0.0333, "x": -0.02, "y": -7.54}, {"time": 0.0667, "x": -0.04, "y": 1.46}, {"time": 0.1, "x": -7.4}, {"time": 0.1333, "x": 2.79}, {"time": 0.1667, "x": -2.22}, {"time": 0.2, "x": -0.02, "y": -3.4}, {"time": 0.2333, "x": -0.04, "y": 4.58}, {"time": 0.2667, "x": -4.23}, {"time": 0.3, "x": 5.27}, {"time": 0.3333}, {"time": 0.3667, "x": -0.02, "y": -2.68}, {"time": 0.4}, {"time": 0.4333, "x": -0.02, "y": -8.1}, {"time": 0.4667, "x": -0.04, "y": 1.46}, {"time": 0.5, "x": -7.43}, {"time": 0.5333}, {"time": 0.5667, "x": -0.02, "y": -7.54}, {"time": 0.6, "x": -0.04, "y": 1.46}, {"time": 0.6333, "x": -7.4}, {"time": 0.6667, "x": 12.93, "y": -5.09}, {"time": 0.7, "x": -2.22}, {"time": 0.7333, "x": -0.02, "y": -3.4}, {"time": 0.7667, "x": -0.04, "y": 4.58}, {"time": 0.8, "x": -4.23}, {"time": 0.8333, "x": 5.27}, {"time": 0.8667}, {"time": 0.9, "x": -0.02, "y": -2.68}, {"time": 0.9333}, {"time": 0.9667, "x": -0.02, "y": -8.1}, {"time": 1, "x": -0.04, "y": 1.46}, {"time": 1.0333, "x": -7.43}, {"time": 1.0667, "x": 2.79}, {"time": 1.1, "x": -4.23}, {"time": 1.1333, "curve": "stepped"}, {"time": 1.1667}, {"time": 1.2, "x": -0.02, "y": -7.54}, {"time": 1.2333, "x": -0.04, "y": 1.46}, {"time": 1.2667, "x": -7.4}, {"time": 1.3, "x": 2.79}, {"time": 1.3333, "x": -2.22}, {"time": 1.3667, "x": -0.02, "y": -3.4}, {"time": 1.4, "x": -0.04, "y": 4.58}, {"time": 1.4333, "x": -4.23}, {"time": 1.4667, "x": 5.27}, {"time": 1.5}, {"time": 1.5333, "x": -0.02, "y": -2.68}, {"time": 1.5667}, {"time": 1.6, "x": -0.02, "y": -8.1}, {"time": 1.6333, "x": -0.04, "y": 1.46}, {"time": 1.6667, "x": -7.43}, {"time": 1.7}, {"time": 1.7333, "x": -0.02, "y": -7.54}, {"time": 1.7667, "x": -0.04, "y": 1.46}, {"time": 1.8, "x": -7.4}, {"time": 1.8333, "x": 2.79}, {"time": 1.8667, "x": -2.22}, {"time": 1.9, "x": -0.02, "y": -3.4}, {"time": 1.9333, "x": -0.04, "y": 4.58}, {"time": 1.9667, "x": -4.23}, {"time": 2, "x": 5.27}, {"time": 2.0333}, {"time": 2.0667, "x": -0.02, "y": -2.68}, {"time": 2.1}, {"time": 2.1333, "x": -0.02, "y": -7.54}, {"time": 2.1667, "x": -0.04, "y": 1.46}, {"time": 2.2, "x": -7.4}, {"time": 2.2333, "x": 2.79}, {"time": 2.2667, "x": -2.22}, {"time": 2.3, "x": -0.02, "y": -3.4}, {"time": 2.3333, "x": -0.04, "y": 4.58}, {"time": 2.3667, "x": -4.23}, {"time": 2.4, "x": 5.27}, {"time": 2.4333}, {"time": 2.4667, "x": -0.02, "y": -2.68}, {"time": 2.5}, {"time": 2.5333, "x": -0.02, "y": -8.1}, {"time": 2.5667, "x": -0.04, "y": 1.46}, {"time": 2.6, "x": -7.43}, {"time": 2.6333, "x": 2.79}, {"time": 2.6667}, {"time": 2.7, "x": -0.02, "y": -7.54}, {"time": 2.7333, "x": -0.04, "y": 1.46}, {"time": 2.7667, "x": -7.4}, {"time": 2.8, "x": 2.79}, {"time": 2.8333, "x": -2.22}, {"time": 2.8667, "x": -0.02, "y": -3.4}, {"time": 2.9, "x": -0.04, "y": 4.58}, {"time": 2.9333, "x": -4.23}, {"time": 2.9667, "x": 5.27}, {"time": 3}, {"time": 3.0333, "x": -0.02, "y": -2.68}, {"time": 3.0667}, {"time": 3.1, "x": -0.02, "y": -8.1}, {"time": 3.1333, "x": -0.04, "y": 1.46}, {"time": 3.1667, "x": -7.43}, {"time": 3.2}, {"time": 3.2333, "x": -0.02, "y": -7.54}, {"time": 3.2667, "x": -0.04, "y": 1.46}, {"time": 3.3, "x": -7.4}, {"time": 3.3333, "x": 12.93, "y": -5.09}, {"time": 3.3667, "x": -2.22}, {"time": 3.4, "x": -0.02, "y": -3.4}, {"time": 3.4333, "x": -0.04, "y": 4.58}, {"time": 3.4667, "x": -4.23}, {"time": 3.5, "x": 5.27}, {"time": 3.5333}, {"time": 3.5667, "x": -0.02, "y": -2.68}, {"time": 3.6}, {"time": 3.6333, "x": -0.02, "y": -8.1}, {"time": 3.6667, "x": -0.04, "y": 1.46}, {"time": 3.7, "x": -7.43}, {"time": 3.7333, "x": 2.79}, {"time": 3.7667, "x": -4.23}, {"time": 3.8, "curve": "stepped"}, {"time": 3.8333}, {"time": 3.8667, "x": -0.02, "y": -7.54}, {"time": 3.9, "x": -0.04, "y": 1.46}, {"time": 3.9333, "x": -7.4}, {"time": 3.9667, "x": 2.79}, {"time": 4, "x": -2.22}, {"time": 4.0333, "x": -0.02, "y": -3.4}, {"time": 4.0667, "x": -0.04, "y": 4.58}, {"time": 4.1, "x": -4.23}, {"time": 4.1333, "x": 5.27}, {"time": 4.1667}, {"time": 4.2, "x": -0.02, "y": -2.68}, {"time": 4.2333}, {"time": 4.2667, "x": -0.02, "y": -8.1}, {"time": 4.3, "x": -0.04, "y": 1.46}, {"time": 4.3333, "x": -7.43}, {"time": 4.3667}, {"time": 4.4, "x": -0.02, "y": -7.54}, {"time": 4.4333, "x": -0.04, "y": 1.46}, {"time": 4.4667, "x": -7.4}, {"time": 4.5, "x": 2.79}, {"time": 4.5333, "x": -2.22}, {"time": 4.5667, "x": -0.02, "y": -3.4}, {"time": 4.6, "x": -0.04, "y": 4.58}, {"time": 4.6333, "x": -4.23}, {"time": 4.6667, "x": 5.27}, {"time": 4.7}, {"time": 4.7333, "x": -0.02, "y": -2.68}, {"time": 4.7667}, {"time": 4.8, "x": -0.02, "y": -7.54}, {"time": 4.8333, "x": -0.04, "y": 1.46}, {"time": 4.8667, "x": -7.4}, {"time": 4.9, "x": 2.79}, {"time": 4.9333, "x": -2.22}, {"time": 4.9667, "x": -0.02, "y": -3.4}, {"time": 5, "x": -0.04, "y": 4.58}, {"time": 5.0333, "x": -4.23}, {"time": 5.0667, "x": 5.27}, {"time": 5.1}, {"time": 5.1333, "x": -0.02, "y": -2.68}, {"time": 5.1667}, {"time": 5.2, "x": -0.02, "y": -8.1}, {"time": 5.2333, "x": -0.04, "y": 1.46}, {"time": 5.2667, "x": -7.43}, {"time": 5.3, "x": 2.79}, {"time": 5.3333}, {"time": 5.3667, "x": -0.02, "y": -7.54}, {"time": 5.4, "x": -0.04, "y": 1.46}, {"time": 5.4333, "x": -7.4}, {"time": 5.4667, "x": 2.79}, {"time": 5.5, "x": -2.22}, {"time": 5.5333, "x": -0.02, "y": -3.4}, {"time": 5.5667, "x": -0.04, "y": 4.58}, {"time": 5.6, "x": -4.23}, {"time": 5.6333, "x": 5.27}, {"time": 5.6667}, {"time": 5.7, "x": -0.02, "y": -2.68}, {"time": 5.7333}, {"time": 5.7667, "x": -0.02, "y": -8.1}, {"time": 5.8, "x": -0.04, "y": 1.46}, {"time": 5.8333, "x": -7.43}, {"time": 5.8667}, {"time": 5.9, "x": -0.02, "y": -7.54}, {"time": 5.9333, "x": -0.04, "y": 1.46}, {"time": 5.9667, "x": -7.4}, {"time": 6, "x": 12.93, "y": -5.09}, {"time": 6.0333, "x": -2.22}, {"time": 6.0667, "x": -0.02, "y": -3.4}, {"time": 6.1, "x": -0.04, "y": 4.58}, {"time": 6.1333, "x": -4.23}, {"time": 6.1667, "x": 5.27}, {"time": 6.2}, {"time": 6.2333, "x": -0.02, "y": -2.68}, {"time": 6.2667}, {"time": 6.3, "x": -0.02, "y": -8.1}, {"time": 6.3333, "x": -0.04, "y": 1.46}, {"time": 6.3667, "x": -7.43}, {"time": 6.4, "x": 2.79}, {"time": 6.4333, "x": -4.23}, {"time": 6.4667, "curve": "stepped"}, {"time": 6.5}, {"time": 6.5333, "x": -0.02, "y": -7.54}, {"time": 6.5667, "x": -0.04, "y": 1.46}, {"time": 6.6, "x": -7.4}, {"time": 6.6333, "x": 2.79}, {"time": 6.6667, "x": -2.22}, {"time": 6.7, "x": -0.02, "y": -3.4}, {"time": 6.7333, "x": -0.04, "y": 4.58}, {"time": 6.7667, "x": -4.23}, {"time": 6.8, "x": 5.27}, {"time": 6.8333}, {"time": 6.8667, "x": -0.02, "y": -2.68}, {"time": 6.9}, {"time": 6.9333, "x": -0.02, "y": -8.1}, {"time": 6.9667, "x": -0.04, "y": 1.46}, {"time": 7, "x": -7.43}, {"time": 7.0333}, {"time": 7.0667, "x": -0.02, "y": -7.54}, {"time": 7.1, "x": -0.04, "y": 1.46}, {"time": 7.1333, "x": -7.4}, {"time": 7.1667, "x": 2.79}, {"time": 7.2, "x": -2.22}, {"time": 7.2333, "x": -0.02, "y": -3.4}, {"time": 7.2667, "x": -0.04, "y": 4.58}, {"time": 7.3, "x": -4.23}, {"time": 7.3333, "x": 5.27}, {"time": 7.3667}, {"time": 7.4, "x": -0.02, "y": -2.68}, {"time": 7.4333}, {"time": 7.4667, "x": -0.02, "y": -7.54}, {"time": 7.5, "x": -0.04, "y": 1.46}, {"time": 7.5333, "x": -7.4}, {"time": 7.5667, "x": 2.79}, {"time": 7.6, "x": -2.22}, {"time": 7.6333, "x": -0.02, "y": -3.4}, {"time": 7.6667, "x": -0.04, "y": 4.58}, {"time": 7.7, "x": -4.23}, {"time": 7.7333, "x": 5.27}, {"time": 7.7667}, {"time": 7.8, "x": -0.02, "y": -2.68}, {"time": 7.8333}, {"time": 7.8667, "x": -0.02, "y": -8.1}, {"time": 7.9, "x": -0.04, "y": 1.46}, {"time": 7.9333, "x": -7.43}, {"time": 7.9667, "x": 2.79}, {"time": 8}], "scale": [{"x": 0.947, "y": 0.947, "curve": [0.176, 0.997, 0.404, 1.1, 0.176, 0.997, 0.404, 1.1]}, {"time": 0.5333, "x": 1.1, "y": 1.1, "curve": [0.692, 1.1, 1.008, 0.937, 0.692, 1.1, 1.008, 0.937]}, {"time": 1.1667, "x": 0.937, "y": 0.937, "curve": [1.208, 0.937, 1.292, 1.108, 1.208, 0.937, 1.292, 1.108]}, {"time": 1.3333, "x": 1.108, "y": 1.108, "curve": [1.383, 1.108, 1.483, 0.929, 1.383, 1.108, 1.483, 0.929]}, {"time": 1.5333, "x": 0.929, "y": 0.929, "curve": [1.6, 0.929, 1.733, 1.108, 1.6, 0.929, 1.733, 1.108]}, {"time": 1.8, "x": 1.108, "y": 1.108, "curve": [1.975, 1.108, 2.325, 0.937, 1.975, 1.108, 2.325, 0.937]}, {"time": 2.5, "x": 0.937, "y": 0.937, "curve": [2.546, 0.937, 2.604, 0.941, 2.546, 0.937, 2.604, 0.941]}, {"time": 2.6667, "x": 0.947, "y": 0.947, "curve": [2.843, 0.997, 3.071, 1.1, 2.843, 0.997, 3.071, 1.1]}, {"time": 3.2, "x": 1.1, "y": 1.1, "curve": [3.358, 1.1, 3.675, 0.937, 3.358, 1.1, 3.675, 0.937]}, {"time": 3.8333, "x": 0.937, "y": 0.937, "curve": [3.875, 0.937, 3.958, 1.108, 3.875, 0.937, 3.958, 1.108]}, {"time": 4, "x": 1.108, "y": 1.108, "curve": [4.05, 1.108, 4.15, 0.929, 4.05, 1.108, 4.15, 0.929]}, {"time": 4.2, "x": 0.929, "y": 0.929, "curve": [4.267, 0.929, 4.4, 1.108, 4.267, 0.929, 4.4, 1.108]}, {"time": 4.4667, "x": 1.108, "y": 1.108, "curve": [4.642, 1.108, 4.992, 0.937, 4.642, 1.108, 4.992, 0.937]}, {"time": 5.1667, "x": 0.937, "y": 0.937, "curve": [5.213, 0.937, 5.27, 0.941, 5.213, 0.937, 5.27, 0.941]}, {"time": 5.3333, "x": 0.947, "y": 0.947, "curve": [5.51, 0.997, 5.737, 1.1, 5.51, 0.997, 5.737, 1.1]}, {"time": 5.8667, "x": 1.1, "y": 1.1, "curve": [6.025, 1.1, 6.342, 0.937, 6.025, 1.1, 6.342, 0.937]}, {"time": 6.5, "x": 0.937, "y": 0.937, "curve": [6.542, 0.937, 6.625, 1.108, 6.542, 0.937, 6.625, 1.108]}, {"time": 6.6667, "x": 1.108, "y": 1.108, "curve": [6.717, 1.108, 6.817, 0.929, 6.717, 1.108, 6.817, 0.929]}, {"time": 6.8667, "x": 0.929, "y": 0.929, "curve": [6.933, 0.929, 7.067, 1.108, 6.933, 0.929, 7.067, 1.108]}, {"time": 7.1333, "x": 1.108, "y": 1.108, "curve": [7.308, 1.108, 7.658, 0.937, 7.308, 1.108, 7.658, 0.937]}, {"time": 7.8333, "x": 0.937, "y": 0.937, "curve": [7.88, 0.937, 7.937, 0.941, 7.88, 0.937, 7.937, 0.941]}, {"time": 8, "x": 0.947, "y": 0.947}]}, "AntlerRight": {"rotate": [{"value": 6.84, "curve": [0.126, 3.34, 0.249, 0]}, {"time": 0.3333, "curve": [0.492, 0, 0.808, 14.6]}, {"time": 0.9667, "value": 14.6, "curve": [1.142, 14.6, 1.492, 0]}, {"time": 1.6667, "curve": [1.825, 0, 2.142, 14.6]}, {"time": 2.3, "value": 14.6, "curve": [2.391, 14.6, 2.53, 10.63]}, {"time": 2.6667, "value": 6.84, "curve": [2.792, 3.34, 2.916, 0]}, {"time": 3, "curve": [3.158, 0, 3.475, 14.6]}, {"time": 3.6333, "value": 14.6, "curve": [3.808, 14.6, 4.158, 0]}, {"time": 4.3333, "curve": [4.492, 0, 4.808, 14.6]}, {"time": 4.9667, "value": 14.6, "curve": [5.058, 14.6, 5.197, 10.63]}, {"time": 5.3333, "value": 6.84, "curve": [5.459, 3.34, 5.583, 0]}, {"time": 5.6667, "curve": [5.825, 0, 6.142, 14.6]}, {"time": 6.3, "value": 14.6, "curve": [6.475, 14.6, 6.825, 0]}, {"time": 7, "curve": [7.158, 0, 7.475, 14.6]}, {"time": 7.6333, "value": 14.6, "curve": [7.724, 14.6, 7.863, 10.63]}, {"time": 8, "value": 6.84}]}, "AntlerLeft": {"rotate": [{"value": -6.81, "curve": [0.126, -3.32, 0.249, 0]}, {"time": 0.3333, "curve": [0.492, 0, 0.808, -14.53]}, {"time": 0.9667, "value": -14.53, "curve": [1.142, -14.53, 1.492, 0]}, {"time": 1.6667, "curve": [1.825, 0, 2.142, -14.53]}, {"time": 2.3, "value": -14.53, "curve": [2.391, -14.53, 2.53, -10.57]}, {"time": 2.6667, "value": -6.81, "curve": [2.792, -3.32, 2.916, 0]}, {"time": 3, "curve": [3.158, 0, 3.475, -14.53]}, {"time": 3.6333, "value": -14.53, "curve": [3.808, -14.53, 4.158, 0]}, {"time": 4.3333, "curve": [4.492, 0, 4.808, -14.53]}, {"time": 4.9667, "value": -14.53, "curve": [5.058, -14.53, 5.197, -10.57]}, {"time": 5.3333, "value": -6.81, "curve": [5.459, -3.32, 5.583, 0]}, {"time": 5.6667, "curve": [5.825, 0, 6.142, -14.53]}, {"time": 6.3, "value": -14.53, "curve": [6.475, -14.53, 6.825, 0]}, {"time": 7, "curve": [7.158, 0, 7.475, -14.53]}, {"time": 7.6333, "value": -14.53, "curve": [7.724, -14.53, 7.863, -10.57]}, {"time": 8, "value": -6.81}]}, "Eye2": {"translate": [{"x": -0.82, "curve": [0.125, 0.4, 0.25, 1.62, 0.125, 0, 0.25, 0]}, {"time": 0.3333, "x": 1.62, "curve": [0.5, 1.62, 0.833, -3.26, 0.5, 0, 0.833, 0]}, {"time": 1, "x": -3.26, "curve": [1.083, -3.26, 1.208, -2.04, 1.083, 0, 1.208, 0]}, {"time": 1.3333, "x": -0.82, "curve": [1.458, 0.4, 1.583, 1.62, 1.458, 0, 1.583, 0]}, {"time": 1.6667, "x": 1.62, "curve": [1.833, 1.62, 2.167, -3.26, 1.833, 0, 2.167, 0]}, {"time": 2.3333, "x": -3.26, "curve": [2.417, -3.26, 2.542, -2.04, 2.417, 0, 2.542, 0]}, {"time": 2.6667, "x": -0.82}], "scale": [{"x": 0.947, "y": 0.947, "curve": [0.176, 0.964, 0.404, 1, 0.176, 0.964, 0.404, 1]}, {"time": 0.5333, "curve": [0.692, 1, 1.008, 0.937, 0.692, 1, 1.008, 0.937]}, {"time": 1.1667, "x": 0.937, "y": 0.937, "curve": [1.213, 0.937, 1.27, 0.941, 1.213, 0.937, 1.27, 0.941]}, {"time": 1.3333, "x": 0.947, "y": 0.947, "curve": [1.51, 0.964, 1.737, 1, 1.51, 0.964, 1.737, 1]}, {"time": 1.8667, "curve": [2.025, 1, 2.342, 0.937, 2.025, 1, 2.342, 0.937]}, {"time": 2.5, "x": 0.937, "y": 0.937, "curve": [2.546, 0.937, 2.604, 0.941, 2.546, 0.937, 2.604, 0.941]}, {"time": 2.6667, "x": 0.947, "y": 0.947}]}, "Eye3": {"translate": [{"x": -0.82, "curve": [0.125, 0.4, 0.25, 1.62, 0.125, 0, 0.25, 0]}, {"time": 0.3333, "x": 1.62, "curve": [0.5, 1.62, 0.833, -3.26, 0.5, 0, 0.833, 0]}, {"time": 1, "x": -3.26, "curve": [1.083, -3.26, 1.208, -2.04, 1.083, 0, 1.208, 0]}, {"time": 1.3333, "x": -0.82, "curve": [1.458, 0.4, 1.583, 1.62, 1.458, 0, 1.583, 0]}, {"time": 1.6667, "x": 1.62, "curve": [1.833, 1.62, 2.167, -3.26, 1.833, 0, 2.167, 0]}, {"time": 2.3333, "x": -3.26, "curve": [2.417, -3.26, 2.542, -2.04, 2.417, 0, 2.542, 0]}, {"time": 2.6667, "x": -0.82}], "scale": [{"x": 0.947, "y": 0.947, "curve": [0.176, 0.964, 0.404, 1, 0.176, 0.964, 0.404, 1]}, {"time": 0.5333, "curve": [0.692, 1, 1.008, 0.937, 0.692, 1, 1.008, 0.937]}, {"time": 1.1667, "x": 0.937, "y": 0.937, "curve": [1.213, 0.937, 1.27, 0.941, 1.213, 0.937, 1.27, 0.941]}, {"time": 1.3333, "x": 0.947, "y": 0.947, "curve": [1.51, 0.964, 1.737, 1, 1.51, 0.964, 1.737, 1]}, {"time": 1.8667, "curve": [2.025, 1, 2.342, 0.937, 2.025, 1, 2.342, 0.937]}, {"time": 2.5, "x": 0.937, "y": 0.937, "curve": [2.546, 0.937, 2.604, 0.941, 2.546, 0.937, 2.604, 0.941]}, {"time": 2.6667, "x": 0.947, "y": 0.947}]}, "EyeExploder2": {"translate": [{}, {"time": 0.0333, "x": -0.02, "y": -7.54}, {"time": 0.0667, "x": -0.04, "y": 1.46}, {"time": 0.1, "x": -7.4}, {"time": 0.1333, "x": 2.79}, {"time": 0.1667, "x": -2.22}, {"time": 0.2, "x": -0.02, "y": -3.4}, {"time": 0.2333, "x": -0.04, "y": 4.58}, {"time": 0.2667, "x": -4.23}, {"time": 0.3, "x": 5.27}, {"time": 0.3333}, {"time": 0.3667, "x": -0.02, "y": -2.68}, {"time": 0.4}, {"time": 0.4333, "x": -0.02, "y": -8.1}, {"time": 0.4667, "x": -0.04, "y": 1.46}, {"time": 0.5, "x": -7.43}, {"time": 0.5333}, {"time": 0.5667, "x": -0.02, "y": -7.54}, {"time": 0.6, "x": -0.04, "y": 1.46}, {"time": 0.6333, "x": -7.4}, {"time": 0.6667, "x": 12.93, "y": -5.09}, {"time": 0.7, "x": -2.22}, {"time": 0.7333, "x": -0.02, "y": -3.4}, {"time": 0.7667, "x": -0.04, "y": 4.58}, {"time": 0.8, "x": -4.23}, {"time": 0.8333, "x": 5.27}, {"time": 0.8667}, {"time": 0.9, "x": -0.02, "y": -2.68}, {"time": 0.9333}, {"time": 0.9667, "x": -0.02, "y": -8.1}, {"time": 1, "x": -0.04, "y": 1.46}, {"time": 1.0333, "x": -7.43}, {"time": 1.0667, "x": 2.79}, {"time": 1.1, "x": -4.23}, {"time": 1.1333, "curve": "stepped"}, {"time": 1.1667}, {"time": 1.2, "x": -0.02, "y": -7.54}, {"time": 1.2333, "x": -0.04, "y": 1.46}, {"time": 1.2667, "x": -7.4}, {"time": 1.3, "x": 2.79}, {"time": 1.3333, "x": -2.22}, {"time": 1.3667, "x": -0.02, "y": -3.4}, {"time": 1.4, "x": -0.04, "y": 4.58}, {"time": 1.4333, "x": -4.23}, {"time": 1.4667, "x": 5.27}, {"time": 1.5}, {"time": 1.5333, "x": -0.02, "y": -2.68}, {"time": 1.5667}, {"time": 1.6, "x": -0.02, "y": -8.1}, {"time": 1.6333, "x": -0.04, "y": 1.46}, {"time": 1.6667, "x": -7.43}, {"time": 1.7}, {"time": 1.7333, "x": -0.02, "y": -7.54}, {"time": 1.7667, "x": -0.04, "y": 1.46}, {"time": 1.8, "x": -7.4}, {"time": 1.8333, "x": 2.79}, {"time": 1.8667, "x": -2.22}, {"time": 1.9, "x": -0.02, "y": -3.4}, {"time": 1.9333, "x": -0.04, "y": 4.58}, {"time": 1.9667, "x": -4.23}, {"time": 2, "x": 5.27}, {"time": 2.0333}, {"time": 2.0667, "x": -0.02, "y": -2.68}, {"time": 2.1}, {"time": 2.1333, "x": -0.02, "y": -7.54}, {"time": 2.1667, "x": -0.04, "y": 1.46}, {"time": 2.2, "x": -7.4}, {"time": 2.2333, "x": 2.79}, {"time": 2.2667, "x": -2.22}, {"time": 2.3, "x": -0.02, "y": -3.4}, {"time": 2.3333, "x": -0.04, "y": 4.58}, {"time": 2.3667, "x": -4.23}, {"time": 2.4, "x": 5.27}, {"time": 2.4333}, {"time": 2.4667, "x": -0.02, "y": -2.68}, {"time": 2.5}, {"time": 2.5333, "x": -0.02, "y": -8.1}, {"time": 2.5667, "x": -0.04, "y": 1.46}, {"time": 2.6, "x": -7.43}, {"time": 2.6333, "x": 2.79}, {"time": 2.6667}], "scale": [{"x": 0.947, "y": 0.947, "curve": [0.176, 0.997, 0.404, 1.1, 0.176, 0.997, 0.404, 1.1]}, {"time": 0.5333, "x": 1.1, "y": 1.1, "curve": [0.692, 1.1, 1.008, 0.937, 0.692, 1.1, 1.008, 0.937]}, {"time": 1.1667, "x": 0.937, "y": 0.937, "curve": [1.208, 0.937, 1.292, 1.108, 1.208, 0.937, 1.292, 1.108]}, {"time": 1.3333, "x": 1.108, "y": 1.108, "curve": [1.383, 1.108, 1.483, 0.929, 1.383, 1.108, 1.483, 0.929]}, {"time": 1.5333, "x": 0.929, "y": 0.929, "curve": [1.6, 0.929, 1.733, 1.108, 1.6, 0.929, 1.733, 1.108]}, {"time": 1.8, "x": 1.108, "y": 1.108, "curve": [1.975, 1.108, 2.325, 0.937, 1.975, 1.108, 2.325, 0.937]}, {"time": 2.5, "x": 0.937, "y": 0.937, "curve": [2.546, 0.937, 2.604, 0.941, 2.546, 0.937, 2.604, 0.941]}, {"time": 2.6667, "x": 0.947, "y": 0.947}]}}}}}