{"skeleton": {"hash": "QtTvRZWq9AA", "spine": "4.1.24", "images": "./images/", "audio": "C:/Users/<USER>/Desktop/咩咩启示录（Cult of the Lamb）/Beholder"}, "bones": [{"name": "root"}, {"name": "Main", "parent": "root", "length": 165.83, "rotation": 90.86, "x": 2.25, "y": 336.57}, {"name": "Horns", "parent": "Main", "x": 113.02, "y": 4.61}, {"name": "HornTopRight", "parent": "Horns", "length": 128.7, "rotation": -33.8, "x": 12.9, "y": -123.92}, {"name": "HornTopLeft", "parent": "Horns", "length": 146.7, "rotation": 25.01, "x": 30.57, "y": 123.69}, {"name": "HornBtmRight", "parent": "Horns", "length": 143.69, "rotation": -56.06, "x": -59, "y": -185.84}, {"name": "HornBtmLeft", "parent": "Horns", "length": 142.23, "rotation": 63.3, "x": -57.03, "y": 146}, {"name": "FACEBOB", "parent": "Main", "x": -330.44, "y": 6.18}, {"name": "FACE", "parent": "FACEBOB", "x": 386.44, "y": -7.01, "color": "abe323ff"}, {"name": "Eye_Middle", "parent": "FACE", "x": 27.94, "y": -4.42}, {"name": "EyeLeft", "parent": "Main", "x": 88.97, "y": 275.62}, {"name": "EyeRight", "parent": "Main", "x": 87.86, "y": -282.65}, {"name": "Drips1", "parent": "Main", "rotation": -90.86, "x": -239.75, "y": 5.58}, {"name": "teleport", "parent": "root", "x": 1.55, "y": 0.64, "scaleX": 2.532, "scaleY": 2.532}, {"name": "Frog<PERSON><PERSON><PERSON>", "parent": "Main", "x": 447.08, "y": -11.89, "skin": true, "color": "abe323ff"}, {"name": "MiddleHorn", "parent": "Main", "length": 145.14, "rotation": -0.55, "x": 214.68, "y": -6.1}, {"name": "Drips2", "parent": "Main", "rotation": -90.86, "x": -240.26, "y": -28.42}, {"name": "TentacleLeftTop", "parent": "HornBtmLeft", "length": 73.43, "rotation": 19.73, "x": 55.5, "y": 1.47, "skin": true}, {"name": "HornBtmLeft2b", "parent": "TentacleLeftTop", "length": 73.43, "rotation": -42.38, "x": 73.43, "skin": true}, {"name": "HornBtmLeft2c", "parent": "HornBtmLeft2b", "length": 73.43, "rotation": 12.9, "x": 73.43, "skin": true}, {"name": "HornBtmLeft2d", "parent": "HornBtmLeft2c", "length": 73.43, "rotation": 58.81, "x": 73.43, "skin": true}, {"name": "TentacleLeftBtm", "parent": "HornBtmLeft", "length": 72.99, "rotation": 37.34, "x": -14.7, "y": 52.07, "skin": true}, {"name": "HornBtmLeft3b", "parent": "TentacleLeftBtm", "length": 72.99, "rotation": 84.29, "x": 72.99, "skin": true}, {"name": "HornBtmLeft3c", "parent": "HornBtmLeft3b", "length": 72.99, "rotation": -57.55, "x": 72.99, "skin": true}, {"name": "HornBtmLeft3d", "parent": "HornBtmLeft3c", "length": 72.99, "rotation": -72.56, "x": 72.99, "skin": true}, {"name": "TentacleLeftBtm2", "parent": "HornBtmRight", "length": 72.99, "rotation": 134.56, "x": -51.64, "y": -41.71, "scaleX": -1, "skin": true}, {"name": "HornBtmLeft3b2", "parent": "TentacleLeftBtm2", "length": 72.99, "rotation": 84.29, "x": 72.99, "skin": true}, {"name": "HornBtmLeft3c2", "parent": "HornBtmLeft3b2", "length": 72.99, "rotation": -57.55, "x": 72.99, "skin": true}, {"name": "HornBtmLeft3d2", "parent": "HornBtmLeft3c2", "length": 72.99, "rotation": -72.56, "x": 72.99, "skin": true}, {"name": "TentacleLeftTop2", "parent": "HornBtmRight", "length": 73.43, "rotation": 164.74, "x": 21.1, "y": 4.1, "scaleX": -1, "skin": true}, {"name": "HornBtmLeft2b2", "parent": "TentacleLeftTop2", "length": 73.43, "rotation": -42.38, "x": 73.43, "skin": true}, {"name": "HornBtmLeft2c2", "parent": "HornBtmLeft2b2", "length": 73.43, "rotation": 12.9, "x": 73.43, "skin": true}, {"name": "HornBtmLeft2d2", "parent": "HornBtmLeft2c2", "length": 73.43, "rotation": 58.81, "x": 73.43, "skin": true}], "slots": [{"name": "teleport", "bone": "teleport"}, {"name": "MASK", "bone": "root"}, {"name": "LeftLegs", "bone": "HornBtmLeft", "attachment": "LeftLegs"}, {"name": "LeftLegs2", "bone": "HornBtmRight", "attachment": "LeftLegs"}, {"name": "Horns", "bone": "Main", "attachment": "images/Beholder1_Horns"}, {"name": "Drips_Back", "bone": "Main", "attachment": "Drips_Back"}, {"name": "MiddleHorn", "bone": "MiddleHorn", "attachment": "MiddleHorn"}, {"name": "TentacleLeft1", "bone": "TentacleLeftBtm", "attachment": "images/Beholder3_Tentacle1"}, {"name": "TentacleLeft2", "bone": "TentacleLeftBtm2", "attachment": "images/Beholder3_Tentacle1"}, {"name": "TentacleRight2", "bone": "TentacleLeftTop", "attachment": "images/Beholder3_Tentacle2"}, {"name": "TentacleRight3", "bone": "TentacleLeftTop2", "attachment": "images/Beholder3_Tentacle2"}, {"name": "Head_Back", "bone": "Main", "attachment": "Head_Back"}, {"name": "Head_Front", "bone": "Main", "attachment": "Head_Front"}, {"name": "Body", "bone": "Main", "attachment": "images/Beholder1_Body"}, {"name": "images/Beholder_ExtraDrips", "bone": "Eye_Middle", "attachment": "images/Beholder_ExtraDrips"}, {"name": "images/Beholder_ExtraDrips2", "bone": "Eye_Middle", "attachment": "images/Beholder_ExtraDrips"}, {"name": "Drips_Front", "bone": "Main", "attachment": "Drips_Front"}, {"name": "EyeLeft", "bone": "EyeLeft", "attachment": "images/Beholder1_Eye_1"}, {"name": "EyeRight", "bone": "EyeRight", "attachment": "images/Beholder1_Eye_1"}, {"name": "Eye_Middle", "bone": "Eye_Middle", "attachment": "images/Beholder1_Eye_2"}, {"name": "images/Bandage", "bone": "root", "attachment": "Bandage"}, {"name": "path", "bone": "root", "attachment": "path"}], "transform": [{"name": "EyeLeft", "order": 2, "bones": ["EyeLeft"], "target": "FACE", "mixRotate": 0, "mixX": 0.4, "mixScaleX": 0, "mixShearY": 0}, {"name": "EyeRight", "order": 1, "bones": ["EyeRight"], "target": "FACE", "mixRotate": 0, "mixX": 0.4, "mixScaleX": 0, "mixShearY": 0}, {"name": "FrogEyeLeftOffset", "order": 5, "skin": true, "bones": ["EyeLeft"], "target": "Frog<PERSON><PERSON><PERSON>", "x": -162.2, "y": 400, "mixRotate": 0, "mixX": 0.1, "mixScaleX": 0, "mixShearY": 0}, {"name": "FrogEyeRightOffset", "order": 6, "skin": true, "bones": ["EyeRight"], "target": "Frog<PERSON><PERSON><PERSON>", "x": -167.6, "y": -362.2, "mixRotate": 0, "mixX": 0.1, "mixScaleX": 0, "mixShearY": 0}, {"name": "Horns", "order": 3, "bones": ["Horns"], "target": "FACE", "mixRotate": 0, "mixX": -0.277, "mixScaleX": 0, "mixShearY": 0}, {"name": "MiddleHorn", "order": 4, "bones": ["MiddleHorn"], "target": "FACE", "mixRotate": 0, "mixX": -0.168, "mixScaleX": 0, "mixShearY": 0}], "path": [{"name": "PATH", "bones": ["Main"], "target": "path", "mixRotate": 0, "mixX": 0}], "skins": [{"name": "default", "attachments": {"MASK": {"MASK": {"type": "clipping", "end": "images/Bandage", "vertexCount": 4, "vertices": [-633.72, 0.64, 659.94, -7.63, 650.09, 1311.79, -643.57, 1316.74], "color": "ce3a3aff"}}, "path": {"path": {"type": "path", "lengths": [799.79, 1372.95, 2194.67, 3965.7, 4913.82, 5454.56], "vertexCount": 18, "vertices": [255.96, -180.69, 11.23, 9.29, -177.71, 155.97, -240.09, 540.91, 9.57, 713.98, 280.47, 901.79, 136.65, 1055.89, 14.53, 1185.25, -137.66, 1346.47, 309.44, 1787.87, 36.98, 1962.63, -691.47, 2429.87, -709.76, 942.5, -655.88, 710.37, -568.31, 333.07, -22.01, 18.32, -3.73, 324.13, 14.55, 629.94]}}, "teleport": {"images/Teleport_1": {"y": 0.5, "width": 160, "height": 55}, "images/Teleport_2": {"y": 0.5, "width": 160, "height": 55}, "images/Teleport_3": {"y": 0.5, "width": 160, "height": 55}, "images/Teleport_4": {"y": 0.5, "width": 160, "height": 55}}}}, {"name": "Dungeon1", "attachments": {"Body": {"images/Beholder1_Body": {"type": "mesh", "uvs": [0.72152, 0.0315, 0.69144, 0.06607, 0.80701, 0.09764, 0.89567, 0.12018, 0.96058, 0.23649, 0.93525, 0.27106, 0.99403, 0.52642, 0.9956, 0.79831, 0.87741, 0.9061, 0.76744, 0.94412, 0.71994, 0.99932, 0.52259, 0.99933, 0.34867, 0.96817, 0.19618, 0.97432, 0.13653, 0.9321, 0.04787, 0.83439, 0.00249, 0.70962, 0.00248, 0.3387, 0.03837, 0.26298, 0.00248, 0.22841, 0.07637, 0.14423, 0.17452, 0.09313, 0.16661, 0.06156, 0.29168, 0.04803, 0.25685, 0.00068, 0.50043, 0.00067, 0.90359, 0.15776, 0.2236, 0.90654, 0.17769, 0.84491, 0.11595, 0.83439, 0.11278, 0.7923, 0.07953, 0.7442, 0.09378, 0.31465, 0.17136, 0.15381, 0.29326, 0.90504, 0.336, 0.94412, 0.51151, 0.91556, 0.66136, 0.92608, 0.72152, 0.90504, 0.7611, 0.85994, 0.81493, 0.87347, 0.86243, 0.83589, 0.86084, 0.78779, 0.90359, 0.68107, 0.87826, 0.27858, 0.79118, 0.15532, 0.66295, 0.0982, 0.26793, 0.09369, 0.27109, 0.74119, 0.22677, 0.54746, 0.22518, 0.26956, 0.4751, 0.18388, 0.72152, 0.26655, 0.72469, 0.53844, 0.67086, 0.75773, 0.06606, 0.55314, 0.02838, 0.71043, 0.02408, 0.34303, 0.08759, 0.23725, 0.0592, 0.20702, 0.0906, 0.16618, 0.23083, 0.10083, 0.2271, 0.07668, 0.34089, 0.08796, 0.69482, 0.09041, 0.79942, 0.12835, 0.91083, 0.22025, 0.88373, 0.24067, 0.95298, 0.51932, 0.94625, 0.74066, 0.90912, 0.81591], "triangles": [51, 47, 46, 52, 46, 45, 51, 46, 52, 50, 33, 47, 50, 47, 51, 50, 32, 33, 53, 52, 44, 49, 50, 51, 32, 50, 49, 55, 32, 49, 49, 53, 48, 51, 53, 49, 52, 53, 51, 31, 49, 48, 54, 48, 53, 54, 53, 43, 42, 54, 43, 39, 54, 42, 38, 54, 39, 36, 34, 48, 54, 36, 48, 28, 48, 34, 54, 37, 36, 38, 37, 54, 23, 24, 25, 1, 25, 0, 62, 22, 23, 63, 23, 25, 62, 23, 63, 2, 64, 1, 25, 1, 64, 63, 25, 64, 21, 22, 62, 47, 62, 63, 46, 63, 64, 47, 63, 46, 61, 62, 47, 21, 62, 61, 65, 64, 2, 65, 2, 3, 21, 60, 20, 61, 60, 21, 33, 61, 47, 45, 64, 65, 46, 64, 45, 4, 26, 3, 61, 33, 60, 59, 20, 60, 4, 66, 26, 65, 3, 26, 66, 65, 26, 59, 19, 20, 58, 59, 60, 66, 45, 65, 67, 45, 66, 18, 19, 59, 18, 59, 58, 44, 52, 45, 5, 66, 4, 67, 66, 5, 67, 44, 45, 68, 44, 67, 32, 58, 33, 33, 58, 60, 57, 18, 58, 57, 17, 18, 58, 32, 57, 6, 68, 5, 68, 67, 5, 43, 53, 44, 55, 57, 32, 68, 43, 44, 69, 43, 68, 16, 57, 56, 17, 57, 16, 57, 55, 56, 6, 69, 68, 31, 55, 49, 56, 55, 31, 42, 43, 69, 30, 31, 48, 69, 6, 7, 70, 42, 69, 70, 69, 7, 28, 29, 30, 15, 56, 31, 15, 31, 30, 15, 30, 29, 16, 56, 15, 41, 42, 70, 48, 28, 30, 39, 42, 41, 40, 39, 41, 8, 41, 70, 40, 41, 8, 8, 70, 7, 27, 28, 34, 14, 29, 28, 14, 28, 27, 15, 29, 14, 35, 34, 36, 9, 39, 40, 38, 39, 9, 9, 40, 8, 12, 35, 36, 13, 14, 27, 13, 27, 34, 13, 34, 35, 13, 35, 12, 10, 37, 38, 10, 38, 9, 11, 36, 37, 12, 36, 11, 11, 37, 10], "vertices": [1, 1, 282.81, -118.14, 1, 1, 1, 266.59, -104.33, 1, 1, 1, 250.82, -156.22, 1, 1, 1, 239.51, -196.04, 1, 1, 1, 183.84, -224.49, 1, 1, 1, 167.59, -212.82, 1, 1, 1, 45.91, -237.52, 1, 1, 1, -83.23, -236.3, 1, 1, 1, -133.63, -182.24, 1, 1, 1, -150.95, -132.38, 1, 1, 1, -176.85, -110.57, 1, 1, 1, -175.52, -21.57, 1, 1, 1, -159.55, 56.64, 1, 1, 1, -161.45, 125.45, 1, 1, 1, -140.99, 152.05, 1, 1, 1, -93.99, 191.33, 1, 1, 1, -34.43, 210.91, 1, 1, 1, 141.74, 208.29, 1, 1, 1, 177.46, 191.57, 1, 1, 1, 194.12, 207.51, 1, 1, 1, 233.61, 173.59, 1, 1, 1, 257.22, 128.97, 1, 1, 1, 272.26, 132.31, 1, 1, 1, 277.85, 75.82, 1, 1, 1, 300.57, 91.19, 1, 1, 1, 298.93, -18.66, 1, 1, 1, 221.61, -199.35, 1, 2, 1, -129.44, 112.6, 0.76, 8, -185.43, 113.44, 0.24, 2, 1, -99.86, 132.87, 0.728, 8, -155.85, 133.7, 0.272, 2, 1, -94.45, 160.64, 0.712, 8, -150.44, 161.47, 0.288, 2, 1, -74.44, 161.77, 0.744, 8, -130.43, 162.6, 0.256, 2, 1, -51.37, 176.42, 0.856, 8, -107.36, 177.25, 0.144, 2, 1, 152.55, 166.95, 0.696, 8, 96.56, 167.78, 0.304, 2, 1, 228.42, 130.82, 0.648, 8, 172.42, 131.66, 0.352, 2, 1, -129.2, 81.18, 0.648, 8, -185.19, 82.01, 0.352, 2, 1, -148.05, 62.18, 0.648, 8, -204.04, 63.01, 0.352, 2, 1, -135.66, -17.17, 0.648, 8, -191.66, -16.33, 0.352, 2, 1, -141.67, -84.67, 0.648, 8, -197.66, -83.83, 0.352, 2, 1, -132.08, -111.95, 0.648, 8, -188.07, -111.11, 0.352, 2, 1, -110.93, -130.12, 0.616, 8, -166.92, -129.28, 0.384, 2, 1, -117.71, -154.29, 0.648, 8, -173.71, -153.46, 0.352, 2, 1, -100.19, -175.98, 0.648, 8, -156.18, -175.14, 0.352, 2, 1, -77.33, -175.6, 0.648, 8, -133.32, -174.77, 0.352, 2, 1, -26.93, -195.64, 0.648, 8, -82.92, -194.8, 0.352, 1, 1, 164.4, -187.07, 1, 2, 1, 223.53, -148.68, 0.648, 8, 167.54, -147.84, 0.352, 2, 1, 251.52, -91.25, 0.648, 8, 195.53, -90.42, 0.352, 2, 1, 256.32, 86.85, 0.648, 8, 200.33, 87.68, 0.352, 2, 1, -51.23, 90.01, 0.096, 8, -107.22, 90.85, 0.904, 2, 1, 41.08, 108.63, 0.096, 8, -14.91, 109.46, 0.904, 2, 1, 173.08, 107.37, 0.096, 8, 117.09, 108.21, 0.904, 2, 1, 212.09, -5.94, 0.096, 8, 156.1, -5.1, 0.904, 2, 1, 171.17, -116.47, 0.112, 8, 115.18, -115.64, 0.888, 2, 1, 42.01, -115.98, 0.096, 8, -13.98, -115.14, 0.904, 2, 1, -61.77, -90.15, 0.096, 8, -117.77, -89.31, 0.904, 2, 1, 39.47, 181.14, 0.648, 8, -16.53, 181.97, 0.352, 2, 1, -34.99, 199.24, 0.94635, 8, -90.98, 200.08, 0.05365, 2, 1, 139.54, 198.58, 0.96988, 8, 83.54, 199.41, 0.03012, 1, 1, 189.35, 169.19, 1, 1, 1, 203.9, 181.78, 1, 1, 1, 223.09, 167.33, 1, 1, 1, 253.18, 103.63, 1, 1, 1, 264.68, 105.14, 1, 1, 1, 258.55, 53.9, 1, 1, 1, 255.01, -105.68, 1, 1, 1, 236.28, -152.58, 1, 1, 1, 191.89, -202.17, 1, 1, 1, 182.37, -189.8, 1, 1, 1, 49.56, -219.06, 1, 1, 1, -55.52, -214.45, 1, 1, 1, -91.01, -197.18, 1], "hull": 26, "edges": [38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 2, 0, 48, 50, 0, 50, 2, 4, 4, 6, 6, 52, 38, 36, 36, 34, 32, 34, 32, 30, 30, 28, 20, 18, 52, 8, 8, 10, 14, 12, 10, 12, 26, 28, 26, 24, 20, 22, 24, 22, 14, 16, 16, 18, 6, 8, 54, 56, 56, 58, 58, 60, 60, 62, 64, 66, 54, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 66, 94, 94, 92, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 108, 106, 62, 110, 110, 64, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 138, 136, 138, 140, 82, 140], "width": 451, "height": 475}}, "Drips_Front": {"Drips_Front": {"name": "images/Beholder1_Drips", "type": "mesh", "uvs": [1, 0.17003, 1, 0.43945, 1, 0.59268, 0.92229, 0.60612, 0.91708, 0.5416, 0.90797, 0.39912, 0.90016, 0.51472, 0.84418, 0.50934, 0.82465, 0.38568, 0.81163, 0.52816, 0.81033, 0.71366, 0.74263, 0.71634, 0.73872, 0.52279, 0.73742, 0.36955, 0.68144, 0.32654, 0.62155, 0.39106, 0.60203, 0.54429, 0.60333, 0.68408, 0.60333, 0.84538, 0.54084, 0.84269, 0.53693, 0.6814, 0.53433, 0.54698, 0.51229, 0.50744, 0.50575, 0.67627, 0.50739, 0.85861, 0.50902, 1, 0.41581, 1, 0.41418, 0.85185, 0.40764, 0.65939, 0.39456, 0.52432, 0.36676, 0.49731, 0.35268, 0.54429, 0.28758, 0.54967, 0.28888, 0.67602, 0.22639, 0.67871, 0.21988, 0.50397, 0.20556, 0.67064, 0.20296, 0.73785, 0.13656, 0.74591, 0.12745, 0.66795, 0.12485, 0.50666, 0.11183, 0.33998, 0.0923, 0.49859, 0.03502, 0.4959, 0.03111, 0.30503, 0.02721, 0.16255, 0, 0.06577, 0, 0, 0.06496, 0, 0.08319, 0.11685, 0.15739, 0.23782, 0.2277, 0.23245, 0.2061, 0.16608, 0.31416, 0.23866, 0.28551, 0.35157, 0.39748, 0.41341, 0.44825, 0.30856, 0.46257, 0.22791, 0.55837, 0.22438, 0.66773, 0.24858, 0.77449, 0.13298, 0.83047, 0, 0.86949, 0, 1, 0, 0.91896, 0.19691], "triangles": [10, 11, 9, 26, 24, 25, 9, 11, 12, 26, 27, 24, 3, 4, 2, 37, 38, 36, 18, 19, 17, 36, 38, 39, 19, 20, 17, 46, 47, 48, 45, 46, 48, 49, 45, 48, 64, 62, 63, 0, 64, 63, 44, 45, 49, 41, 44, 49, 13, 14, 59, 50, 41, 49, 60, 13, 59, 64, 8, 60, 13, 60, 8, 61, 62, 64, 64, 60, 61, 15, 58, 59, 15, 59, 14, 5, 8, 64, 64, 0, 1, 5, 64, 1, 43, 44, 41, 30, 54, 55, 42, 43, 41, 35, 50, 51, 35, 51, 54, 41, 50, 35, 35, 40, 41, 58, 56, 57, 22, 58, 15, 22, 56, 58, 55, 56, 22, 7, 8, 5, 6, 7, 5, 9, 12, 13, 8, 9, 13, 29, 30, 55, 29, 55, 22, 1, 4, 5, 30, 32, 54, 32, 35, 54, 15, 21, 22, 16, 21, 15, 30, 31, 32, 4, 1, 2, 28, 29, 22, 36, 39, 40, 35, 36, 40, 32, 34, 35, 23, 28, 22, 33, 34, 32, 20, 21, 16, 20, 16, 17, 27, 28, 23, 27, 23, 24, 53, 51, 52, 54, 51, 53], "vertices": [2, 1, -108.56, -155.15, 0.76, 8, -177.6, -146.57, 0.24, 2, 1, -150.05, -154.53, 0.808, 12, 159.16, 92.05, 0.192, 2, 1, -173.64, -154.18, 0.344, 12, 159.16, 68.45, 0.656, 2, 1, -175.34, -129.44, 0.344, 12, 134.45, 66.38, 0.656, 2, 1, -165.38, -127.93, 0.568, 12, 132.8, 76.32, 0.432, 3, 1, -143.4, -125.36, 0.65286, 12, 129.9, 98.26, 0.15514, 16, 103.41, 98.28, 0.192, 2, 1, -161.16, -122.61, 0.696, 16, 100.93, 80.48, 0.304, 2, 1, -160.07, -104.82, 0.696, 16, 83.13, 81.31, 0.304, 2, 1, -140.93, -98.9, 0.808, 16, 76.92, 100.35, 0.192, 2, 1, -162.81, -94.43, 0.536, 16, 72.78, 78.41, 0.464, 2, 1, -191.37, -93.59, 0.296, 16, 72.36, 49.84, 0.704, 2, 1, -191.46, -72.06, 0.296, 16, 50.83, 49.43, 0.704, 2, 1, -161.64, -71.26, 0.536, 16, 49.59, 79.24, 0.464, 2, 1, -138.04, -71.2, 0.808, 16, 49.18, 102.83, 0.192, 1, 1, -131.15, -53.5, 1, 1, 1, -140.8, -34.31, 1, 2, 1, -164.3, -27.75, 0.808, 12, 32.61, 75.9, 0.192, 2, 1, -185.83, -27.84, 0.52, 12, 33.02, 54.37, 0.48, 2, 1, -210.67, -27.47, 0.344, 12, 33.02, 29.53, 0.656, 2, 1, -209.96, -7.6, 0.344, 12, 13.15, 29.95, 0.656, 2, 1, -185.1, -6.73, 0.52, 12, 11.91, 54.79, 0.48, 2, 1, -164.39, -6.21, 0.808, 12, 11.08, 75.49, 0.192, 3, 1, -158.2, 0.7, 0.65286, 12, 4.07, 81.58, 0.15514, 16, -22.41, 81.6, 0.192, 2, 1, -184.17, 3.17, 0.696, 16, -24.49, 55.6, 0.304, 2, 1, -212.25, 3.07, 0.536, 16, -23.97, 27.52, 0.464, 2, 1, -234.03, 2.87, 0.296, 16, -23.45, 5.74, 0.704, 2, 1, -233.59, 32.51, 0.296, 16, -53.09, 5.74, 0.704, 2, 1, -210.77, 32.69, 0.536, 16, -53.61, 28.56, 0.464, 2, 1, -181.1, 34.33, 0.696, 16, -55.69, 58.2, 0.304, 2, 1, -160.24, 38.18, 0.808, 16, -59.85, 79, 0.192, 2, 1, -155.95, 46.95, 0.888, 16, -68.69, 83.16, 0.112, 2, 1, -163.12, 51.54, 0.888, 16, -73.17, 75.92, 0.112, 2, 1, -163.64, 72.25, 0.808, 16, -93.87, 75.1, 0.192, 2, 1, -183.1, 72.12, 0.696, 16, -93.46, 55.64, 0.304, 3, 1, -183.22, 92, 0.62918, 12, -86.84, 55.2, 0.06682, 16, -113.33, 55.22, 0.304, 3, 1, -156.28, 93.67, 0.65286, 12, -88.91, 82.11, 0.15514, 16, -115.4, 82.13, 0.192, 2, 1, -181.88, 98.61, 0.52, 12, -93.47, 56.44, 0.48, 2, 1, -192.21, 99.59, 0.344, 12, -94.3, 46.09, 0.656, 2, 1, -193.14, 120.72, 0.344, 12, -115.41, 44.85, 0.656, 2, 1, -181.09, 123.44, 0.52, 12, -118.31, 56.86, 0.48, 2, 1, -156.24, 123.89, 0.808, 12, -119.14, 81.7, 0.192, 2, 1, -130.51, 127.65, 0.808, 12, -123.28, 107.37, 0.192, 2, 1, -154.84, 134.22, 0.616, 12, -129.49, 82.94, 0.384, 2, 1, -154.16, 152.43, 0.616, 12, -147.7, 83.35, 0.384, 2, 1, -124.75, 153.23, 0.808, 12, -148.94, 112.75, 0.192, 1, 1, -102.79, 154.15, 1, 1, 1, -87.76, 162.58, 1, 1, 1, -77.63, 162.43, 1, 1, 1, -77.94, 141.77, 1, 1, 1, -96.02, 136.24, 1, 1, 1, -115, 112.93, 1, 2, 1, -114.51, 90.56, 0.52, 8, -183.55, 99.13, 0.48, 2, 1, -104.18, 97.28, 0.52, 8, -173.23, 105.85, 0.48, 2, 1, -115.87, 63.08, 0.52, 8, -184.92, 71.66, 0.48, 2, 1, -133.12, 72.45, 0.52, 8, -202.17, 81.02, 0.48, 3, 1, -143.18, 36.99, 0.67488, 8, -212.22, 45.57, 0.21312, 16, -58.92, 96.08, 0.112, 2, 1, -127.27, 20.61, 0.76, 8, -196.32, 29.18, 0.24, 2, 1, -114.92, 15.87, 0.76, 8, -183.97, 24.44, 0.24, 2, 1, -114.83, -14.6, 0.76, 8, -183.88, -6.03, 0.24, 2, 1, -119.08, -49.32, 0.76, 8, -188.12, -40.74, 0.24, 2, 1, -101.78, -83.53, 0.76, 8, -170.83, -74.95, 0.24, 2, 1, -81.57, -101.63, 0.76, 8, -150.62, -93.06, 0.24, 2, 1, -81.76, -114.04, 0.76, 8, -150.8, -105.47, 0.24, 2, 1, -82.38, -155.54, 0.76, 8, -151.42, -146.97, 0.24, 2, 1, -112.31, -129.32, 0.76, 8, -181.36, -120.75, 0.24], "hull": 64, "edges": [60, 58, 58, 56, 56, 54, 54, 52, 50, 52, 50, 48, 48, 46, 46, 44, 44, 42, 42, 40, 40, 38, 38, 36, 36, 34, 34, 32, 32, 30, 30, 28, 28, 26, 26, 24, 24, 22, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 92, 94, 92, 90, 90, 88, 88, 86, 86, 84, 84, 82, 82, 80, 80, 78, 78, 76, 76, 74, 74, 72, 72, 70, 70, 68, 68, 66, 66, 64, 64, 62, 62, 60, 94, 96, 96, 98, 98, 100, 100, 102, 116, 118, 118, 120, 120, 122, 2, 0, 0, 126, 128, 0, 122, 124, 124, 126, 128, 124, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116], "width": 318, "height": 154}}, "EyeLeft": {"images/Beholder1_Eye_1": {"x": 0.73, "y": 15.49, "rotation": -90.86, "width": 115, "height": 137}, "images/Beholder1_Eye_1_Closed": {"x": 0.73, "y": 15.49, "rotation": -90.86, "width": 115, "height": 137}, "images/Beholder1_Eye_1_Anticipate": {"name": "images/Beholder1_Eye_Anticipate", "x": 0.73, "y": 15.49, "rotation": -90.86, "width": 117, "height": 136}}, "EyeRight": {"images/Beholder1_Eye_1": {"x": -3.81, "y": -20.45, "scaleX": -1, "rotation": -90.86, "width": 115, "height": 137}, "images/Beholder1_Eye_1_Closed": {"x": -3.81, "y": -20.45, "scaleX": -1, "rotation": -90.86, "width": 115, "height": 137}, "images/Beholder1_Eye_Anticipate": {"x": -3.81, "y": -20.45, "scaleX": -1, "rotation": -90.86, "width": 117, "height": 136}}, "Eye_Middle": {"images/Beholder1_Eye_2": {"x": 2.03, "y": 1.97, "rotation": -90.86, "width": 219, "height": 182}, "images/Beholder1_Eye_1_Anticipate": {"name": "images/Beholder1_Eye_2_Anticipate", "x": -16.08, "y": 2.24, "rotation": -90.86, "width": 219, "height": 144}, "images/Beholder1_Eye_2_Closed": {"x": 2.03, "y": 1.97, "rotation": -90.86, "width": 219, "height": 182}}, "Horns": {"images/Beholder1_Horns": {"type": "mesh", "uvs": [0.75285, 0.24801, 0.86017, 0.49846, 0.73191, 0.77191, 0.68698, 0.80075, 0.6759, 0.82873, 0.89359, 0.62872, 0.89218, 0.36203, 0.95579, 0.38911, 1, 0.61413, 1, 0.69747, 0.97417, 0.78081, 0.74375, 1, 0.71265, 1, 0.26454, 1, 0.06098, 0.81206, 0.00424, 0.72456, 0, 0.43287, 0.09632, 0.35578, 0.10339, 0.65997, 0.34653, 0.81206, 0.326, 0.78705, 0.24573, 0.73898, 0.18499, 0.57653, 0.16528, 0.56704, 0.14968, 0.4199, 0.25491, 0.24084, 0.16765, 0, 0.23352, 0, 0.3302, 0.12231, 0.49868, 0.25208, 0.6617, 0.13631, 0.76505, 0, 0.86586, 0, 0.33805, 0.91416, 0.66176, 0.90166, 0.32506, 0.15005, 0.41061, 0.30137, 0.36442, 0.36316, 0.33704, 0.41107, 0.34217, 0.42999, 0.30966, 0.46782, 0.32201, 0.52846, 0.3858, 0.69731, 0.60732, 0.66503, 0.69269, 0.4685, 0.67561, 0.40739, 0.65691, 0.41098, 0.66586, 0.37743, 0.62439, 0.23603, 0.64878, 0.15334, 0.62006, 0.74945, 0.50076, 0.68231, 0.49918, 0.36735, 0.66783, 0.52399], "triangles": [17, 18, 16, 15, 16, 18, 14, 15, 18, 18, 13, 14, 18, 19, 13, 33, 13, 19, 51, 33, 19, 33, 12, 13, 35, 27, 28, 25, 27, 35, 26, 27, 25, 36, 35, 28, 29, 36, 28, 37, 25, 35, 36, 37, 35, 36, 29, 52, 38, 25, 37, 40, 24, 25, 39, 38, 37, 38, 40, 25, 40, 38, 39, 41, 40, 39, 39, 52, 41, 37, 52, 39, 36, 52, 37, 22, 23, 24, 40, 22, 24, 41, 22, 40, 51, 41, 52, 42, 41, 51, 21, 22, 41, 21, 41, 42, 20, 21, 42, 19, 20, 42, 19, 42, 51, 6, 7, 5, 7, 8, 5, 5, 8, 9, 10, 5, 9, 34, 4, 11, 5, 11, 4, 12, 34, 11, 12, 33, 34, 5, 10, 11, 34, 50, 4, 51, 34, 33, 50, 34, 51, 30, 49, 29, 0, 30, 31, 0, 31, 32, 48, 29, 49, 52, 29, 48, 0, 49, 30, 0, 48, 49, 47, 48, 0, 52, 48, 47, 45, 47, 0, 46, 52, 47, 46, 47, 45, 44, 45, 0, 1, 44, 0, 44, 53, 46, 44, 46, 45, 52, 46, 53, 43, 52, 53, 43, 51, 52, 2, 50, 43, 1, 53, 44, 2, 53, 1, 2, 43, 53, 3, 50, 2, 4, 50, 3, 51, 43, 50], "vertices": [1, 3, 226.23, 73.24, 1, 1, 3, 166.77, -55.59, 1, 1, 3, 7.6, -50.84, 1, 1, 3, -21.24, -31.74, 1, 1, 5, -32.94, 50.26, 1, 1, 5, 147.94, 41.23, 1, 1, 5, 220.02, 146.7, 1, 1, 5, 249.5, 110.42, 1, 1, 5, 213.62, 4.09, 1, 1, 5, 190.84, -28.69, 1, 1, 5, 153.08, -51.06, 1, 4, 3, -79.53, -117.28, 0.05957, 5, -40.42, -44.45, 0.93119, 4, -262.21, -209.6, 0.00068, 6, -316.27, -90.32, 0.00856, 1, 5, -58.45, -31.92, 1, 1, 6, -11.78, 57.16, 1, 1, 6, 156.8, 38.8, 1, 1, 6, 211.12, 18.54, 1, 1, 6, 274.73, -105.9, 1, 1, 6, 229.62, -168.78, 1, 1, 6, 161.61, -39.82, 1, 1, 6, -24.63, -49.09, 1, 1, 4, -41.76, 11.28, 1, 1, 4, 3.68, 52.23, 1, 1, 4, 92.41, 56.87, 1, 1, 4, 102.57, 67.41, 1, 1, 4, 170.8, 46.57, 1, 2, 3, 37.9, 370.11, 0.03782, 4, 215.56, -57.7, 0.96218, 1, 4, 346.24, -52.6, 1, 1, 4, 325.95, -94.44, 1, 1, 4, 243.46, -130.3, 1, 2, 3, 126.99, 222.77, 0.48971, 4, 135.63, -210.21, 0.51029, 1, 3, 236.12, 156.35, 1, 1, 3, 330.6, 130.63, 1, 1, 3, 369.31, 70.9, 1, 1, 6, -40.56, -2.46, 1, 1, 5, -61.07, 27.27, 1, 1, 4, 233.08, -121.24, 1, 1, 4, 141.51, -143.97, 1, 1, 4, 129.11, -101.71, 1, 1, 4, 116.89, -74.3, 1, 1, 4, 107.16, -73.61, 1, 2, 3, -32.3, 278.54, 0.05263, 4, 100.87, -45.05, 0.94737, 1, 4, 70.92, -40.23, 1, 1, 4, -21.5, -45.47, 1, 1, 3, 2.72, 50.82, 1, 2, 3, 114.5, 51.44, 0.94382, 4, -17.41, -288.23, 0.05618, 1, 3, 132.51, 77.48, 1, 1, 3, 123.88, 87.62, 1, 1, 3, 140.8, 91.06, 1, 1, 3, 181.72, 152.47, 1, 1, 3, 224.32, 159.56, 1, 1, 3, -26.32, 21.28, 1, 2, 3, -45.14, 109.45, 0.504, 4, -50.44, -121.63, 0.496, 2, 3, 80.85, 192.44, 0.4994, 4, 85.8, -186.45, 0.5006, 2, 3, 82.65, 51.71, 0.93124, 4, -33.67, -260.85, 0.06876], "hull": 33, "edges": [38, 36, 36, 34, 34, 32, 30, 32, 30, 28, 28, 26, 26, 66, 66, 38, 24, 26, 68, 24, 22, 24, 22, 20, 20, 18, 18, 16, 14, 16, 14, 12, 12, 10, 10, 8, 8, 68, 48, 50, 50, 52, 52, 54, 54, 56, 56, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 40, 40, 42, 42, 44, 44, 46, 46, 48, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 62, 64, 64, 0, 0, 2, 2, 4, 86, 100, 100, 6, 6, 4, 84, 102, 56, 58, 62, 60, 60, 98, 58, 60, 102, 104, 104, 58, 82, 104, 86, 106, 106, 88, 106, 104, 102, 86, 40, 38, 6, 8], "width": 706, "height": 479}}, "images/Bandage": {"Bandage": {"name": "images/SummonDrop3", "x": -19.33, "y": 321.66, "scaleX": -0.01, "scaleY": -0.01, "width": 85, "height": 107}}}}, {"name": "Dungeon1_Beaten", "attachments": {"Body": {"images/Beholder1_Body": {"type": "<PERSON><PERSON><PERSON>", "skin": "Dungeon1", "parent": "images/Beholder1_Body", "width": 451, "height": 475}}, "Drips_Front": {"Drips_Front": {"name": "images/Beholder1_Drips", "type": "<PERSON><PERSON><PERSON>", "skin": "Dungeon1", "parent": "Drips_Front", "width": 318, "height": 154}}, "Horns": {"images/Beholder1_Horns": {"type": "<PERSON><PERSON><PERSON>", "skin": "Dungeon1", "parent": "images/Beholder1_Horns", "width": 706, "height": 479}}, "images/Bandage": {"Bandage": {"name": "images/Bandage", "type": "mesh", "uvs": [0.781, 0.10925, 0.87301, 0.19826, 0.9608, 0.28687, 1, 0.46644, 1, 0.82556, 0.96253, 1, 0.90518, 1, 0.79579, 0.73961, 0.69312, 0.67864, 0.56508, 0.63415, 0.40816, 0.646, 0.27702, 0.68871, 0.16121, 0.81037, 0.04916, 0.95626, 0.01871, 0.86514, 1e-05, 0.57047, 0.02225, 0.31173, 0.09936, 0.19963, 0.20272, 0.09013, 0.34377, 0.01226, 0.4818, 0, 0.6353, 0.01559], "triangles": [10, 11, 19, 7, 8, 0, 0, 8, 21, 11, 18, 19, 10, 20, 9, 10, 19, 20, 8, 9, 21, 9, 20, 21, 1, 7, 0, 5, 6, 4, 6, 7, 4, 13, 14, 12, 14, 15, 12, 7, 3, 4, 12, 15, 17, 15, 16, 17, 12, 17, 11, 7, 2, 3, 7, 1, 2, 17, 18, 11], "vertices": [2, 1, 155.6, -142.07, 0.46943, 8, 99.61, -141.23, 0.53057, 2, 1, 135.4, -185.11, 0.744, 8, 79.41, -184.27, 0.256, 1, 1, 118.02, -226.6, 1, 1, 1, 80.76, -244.59, 1, 1, 1, 6.79, -243.49, 1, 1, 1, -28.88, -225.23, 1, 2, 1, -28.48, -198.11, 0.832, 8, -84.47, -197.28, 0.168, 2, 1, 9.25, -144.41, 0.392, 8, -46.74, -143.58, 0.608, 2, 1, 22.96, -96.12, 0.184, 8, -33.03, -95.28, 0.816, 1, 8, -23.11, -34.84, 1, 2, 1, 31.55, 38.58, 0.072, 8, -24.45, 39.41, 0.928, 2, 1, 24.09, 100.66, 0.28, 8, -31.9, 101.5, 0.72, 2, 1, 8, 154.46, 0.58466, 8, -48, 155.3, 0.41534, 1, 1, -13.55, 206.63, 1, 1, 1, 6.69, 220.54, 1, 1, 1, 66.77, 228.61, 1, 1, 1, 119.55, 217.35, 1, 2, 1, 142.52, 180.47, 0.82706, 8, 86.52, 181.3, 0.17294, 2, 1, 164.14, 131.28, 0.488, 8, 108.15, 132.11, 0.512, 2, 1, 179.09, 64.34, 0.152, 8, 123.1, 65.18, 0.848, 2, 1, 180.51, -0.95, 0.056, 8, 124.52, -0.12, 0.944, 2, 1, 176.61, -73.56, 0.112, 8, 120.62, -72.73, 0.888], "hull": 22, "edges": [36, 38, 30, 32, 22, 20, 16, 14, 14, 12, 10, 12, 2, 4, 4, 6, 38, 40, 40, 42, 20, 18, 18, 16, 6, 8, 10, 8, 2, 0, 0, 42, 32, 34, 34, 36, 22, 24, 24, 26, 26, 28, 28, 30], "width": 473, "height": 206}}}}, {"name": "Dungeon2", "bones": ["Frog<PERSON><PERSON><PERSON>"], "transform": ["FrogEyeLeftOffset", "FrogEyeRightOffset"], "attachments": {"Body": {"images/Beholder1_Body": {"name": "images/Beholder2_Body", "type": "mesh", "uvs": [0.8251, 0.07491, 0.95868, 0.20272, 1, 0.33993, 1, 0.42828, 1, 0.53917, 0.95457, 0.61999, 0.92131, 0.66406, 0.89497, 0.69894, 0.90114, 0.75909, 0.83743, 0.776, 0.83126, 0.74029, 0.77166, 0.76097, 0.65041, 0.84931, 0.58625, 0.82337, 0.60064, 1, 0.41361, 0.9775, 0.40334, 0.83277, 0.34288, 0.80831, 0.21554, 0.7752, 0.11691, 0.70213, 0.08071, 0.67359, 0.06198, 0.69071, 0.02328, 0.66445, 0, 0.54457, 0, 0.4224, 0, 0.34701, 0.03826, 0.21228, 0.14438, 0.08783, 0.19806, 0.04673, 0.1631, 0.02389, 0.2043, 0, 0.23676, 0, 0.42505, 0, 0.51957, 0, 0.60284, 0, 0.71511, 0, 0.71261, 0.02634, 0.03077, 0.59937, 0.26922, 0.02275, 0.39016, 0.02054, 0.55107, 0.01951, 0.07164, 0.58025, 0.08527, 0.64881, 0.16978, 0.68247, 0.02394, 0.41819, 0.03075, 0.3434, 0.07573, 0.215, 0.20522, 0.09034, 0.24475, 0.72746, 0.35243, 0.7661, 0.4533, 0.78729, 0.55144, 0.78979, 0.64413, 0.76735, 0.81451, 0.70502, 0.83768, 0.67759, 0.92901, 0.57038, 0.96581, 0.42827, 0.92764, 0.22508, 0.8295, 0.11288, 0.33062, 0.06302, 0.44103, 0.04307, 0.61823, 0.06676, 0.43422, 0.10665, 0.29382, 0.11164, 0.23657, 0.24627, 0.21885, 0.43825, 0.22567, 0.58285, 0.39469, 0.72496, 0.60324, 0.72621, 0.77225, 0.59532, 0.78316, 0.2525, 0.7041, 0.10042, 0.61823, 0.10042, 0.12617, 0.53548, 0.87585, 0.54047, 0.76889, 0.44926], "triangles": [69, 68, 75, 75, 62, 70, 69, 75, 74, 67, 66, 75, 68, 67, 75, 65, 62, 75, 62, 65, 64, 64, 63, 62, 70, 62, 72, 73, 65, 66, 75, 66, 65, 70, 72, 71, 75, 70, 56, 65, 45, 64, 70, 71, 58, 63, 59, 62, 62, 61, 72, 74, 75, 56, 54, 69, 74, 50, 67, 68, 67, 48, 66, 9, 10, 8, 53, 11, 52, 11, 53, 10, 10, 7, 8, 10, 54, 7, 10, 53, 54, 53, 69, 54, 7, 54, 6, 55, 6, 54, 6, 55, 5, 55, 54, 74, 5, 55, 4, 4, 55, 56, 55, 74, 56, 56, 3, 4, 56, 2, 3, 2, 56, 57, 57, 56, 70, 57, 1, 2, 70, 58, 57, 57, 58, 1, 1, 58, 0, 14, 15, 13, 50, 13, 16, 50, 51, 13, 13, 15, 16, 13, 52, 12, 12, 52, 11, 50, 16, 49, 13, 51, 52, 18, 48, 17, 16, 17, 49, 17, 48, 49, 51, 68, 52, 51, 50, 68, 49, 67, 50, 19, 43, 18, 18, 43, 48, 49, 48, 67, 52, 68, 53, 48, 43, 66, 43, 19, 42, 21, 22, 20, 19, 20, 42, 43, 42, 66, 20, 22, 42, 22, 37, 42, 22, 23, 37, 37, 41, 42, 42, 41, 73, 37, 23, 41, 73, 41, 44, 41, 23, 44, 23, 24, 44, 44, 45, 65, 24, 25, 44, 45, 46, 64, 44, 25, 45, 45, 25, 26, 45, 26, 46, 46, 47, 64, 46, 27, 47, 46, 26, 27, 58, 36, 0, 63, 47, 59, 62, 60, 61, 62, 59, 60, 36, 58, 61, 58, 71, 61, 47, 38, 59, 27, 28, 47, 38, 47, 28, 38, 28, 31, 31, 28, 30, 36, 35, 0, 61, 60, 40, 60, 33, 40, 40, 34, 61, 61, 34, 36, 59, 39, 60, 59, 38, 39, 28, 29, 30, 39, 32, 60, 60, 32, 33, 36, 34, 35, 38, 31, 39, 39, 31, 32, 40, 33, 34, 42, 73, 66, 73, 44, 65, 68, 69, 53, 64, 47, 63, 72, 61, 71], "vertices": [1, 1, 225.88, -162.67, 1, 1, 1, 159.12, -224.6, 1, 1, 1, 88.18, -243, 1, 1, 1, 42.69, -242.32, 1, 1, 1, -14.42, -241.47, 1, 1, 1, -55.72, -219.46, 1, 1, 1, -78.18, -203.45, 1, 1, 1, -95.95, -190.78, 1, 1, 1, -126.97, -193.22, 1, 1, 1, -135.23, -163.09, 1, 1, 1, -116.8, -160.46, 1, 1, 1, -127.03, -132.23, 1, 1, 1, -171.66, -74.45, 1, 1, 1, -157.86, -44.43, 1, 1, 1, -248.91, -49.85, 1, 1, 1, -236.01, 38.05, 1, 1, 1, -161.41, 41.78, 1, 1, 1, -148.39, 70.07, 1, 1, 1, -130.45, 129.78, 1, 1, 1, -92.13, 175.67, 1, 1, 1, -77.17, 192.5, 1, 1, 1, -85.86, 201.45, 1, 1, 1, -72.07, 219.47, 1, 1, 1, -10.17, 229.52, 1, 1, 1, 52.74, 228.58, 1, 1, 1, 91.56, 228, 1, 1, 1, 160.67, 208.94, 1, 1, 1, 224.01, 158.01, 1, 1, 1, 244.8, 132.42, 1, 1, 1, 256.8, 148.7, 1, 1, 1, 268.82, 129.12, 1, 1, 1, 268.59, 113.83, 1, 1, 1, 267.26, 25.16, 1, 1, 1, 266.6, -19.36, 1, 1, 1, 266.01, -58.57, 1, 1, 1, 265.23, -111.45, 1, 1, 1, 251.68, -110.07, 1, 1, 1, -38.61, 215.45, 1, 1, 1, 256.64, 98.72, 1, 1, 1, 256.93, 41.74, 1, 1, 1, 256.33, -34.05, 1, 1, 1, -29.05, 196.05, 1, 1, 1, -64.45, 190.16, 1, 1, 1, -82.38, 150.62, 1, 1, 1, 54.74, 217.27, 1, 1, 1, 93.21, 213.49, 1, 1, 1, 159.01, 191.32, 1, 1, 1, 222.29, 129.38, 1, 1, 1, -106.07, 115.66, 1, 1, 1, -126.72, 65.24, 1, 1, 1, -138.35, 17.9, 1, 1, 1, -140.32, -28.3, 1, 1, 1, -129.42, -72.12, 1, 1, 1, -98.52, -152.84, 1, 1, 1, -84.56, -163.96, 1, 1, 1, -29.99, -207.8, 1, 1, 1, 42.93, -226.22, 1, 1, 1, 147.83, -209.81, 1, 1, 1, 206.29, -164.45, 1, 1, 1, 235.48, 70.11, 1, 1, 1, 244.97, 17.96, 1, 1, 1, 231.53, -65.31, 1, 2, 1, 212.28, 21.66, 0.424, 8, 156.29, 22.49, 0.576, 2, 1, 210.7, 87.82, 0.504, 8, 154.71, 88.65, 0.496, 2, 1, 141.77, 115.81, 0.344, 8, 85.78, 116.65, 0.656, 2, 1, 43.04, 125.63, 0.104, 8, -12.95, 126.47, 0.896, 2, 1, -31.47, 123.53, 0.584, 8, -87.46, 124.37, 0.416, 2, 1, -105.84, 45.03, 0.664, 8, -161.83, 45.86, 0.336, 2, 1, -107.94, -53.18, 0.664, 8, -163.94, -52.34, 0.336, 2, 1, -41.73, -133.78, 0.632, 8, -97.72, -132.95, 0.368, 2, 1, 134.72, -141.55, 0.328, 8, 78.73, -140.72, 0.672, 2, 1, 213.59, -105.49, 0.504, 8, 157.6, -104.66, 0.496, 2, 1, 214.2, -65.05, 0.52, 8, 158.2, -64.21, 0.48, 2, 1, -6.38, 170.03, 0.568, 8, -62.37, 170.87, 0.432, 2, 1, -14.21, -182.99, 0.552, 8, -70.21, -182.16, 0.448, 2, 1, 33.5, -133.32, 0.104, 8, -22.49, -132.49, 0.896], "hull": 36, "edges": [26, 28, 32, 30, 26, 24, 24, 22, 22, 20, 20, 18, 18, 16, 16, 14, 10, 8, 8, 6, 6, 4, 4, 2, 2, 0, 0, 72, 72, 70, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 74, 74, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 76, 76, 78, 62, 64, 78, 64, 68, 70, 80, 68, 64, 66, 66, 68, 80, 66, 82, 84, 84, 86, 82, 88, 88, 90, 90, 92, 92, 94, 86, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 14, 12, 12, 10, 108, 12, 12, 110, 110, 112, 112, 114, 114, 116, 94, 118, 118, 120, 120, 122, 122, 116, 124, 126, 126, 128, 128, 130, 132, 134, 134, 136, 134, 100, 102, 136, 136, 138, 140, 142, 142, 144, 144, 122, 132, 146, 146, 130, 138, 148, 148, 150, 140, 150, 28, 30, 44, 46, 70, 0], "width": 471, "height": 515}}, "Drips_Back": {"Drips_Back": {"name": "images/Beholder2_Drips", "type": "mesh", "uvs": [1, 0.1449, 1, 0.46504, 0.94997, 0.64069, 0.88743, 0.72639, 0.87245, 0.91449, 0.8702, 1, 0.68833, 1, 0.68383, 0.89029, 0.68083, 0.7134, 0.67859, 0.57886, 0.67606, 0.39515, 0.66332, 0.44623, 0.65958, 0.59197, 0.66482, 0.70907, 0.66108, 0.85856, 0.60939, 0.86354, 0.59965, 0.70284, 0.59751, 0.59259, 0.58927, 0.42567, 0.56829, 0.20767, 0.52934, 0.20642, 0.51361, 0.42069, 0.4784, 0.42069, 0.46866, 0.23258, 0.45955, 0.47224, 0.45909, 0.60754, 0.4558, 0.70519, 0.45667, 0.78692, 0.39899, 0.78443, 0.40072, 0.70451, 0.39985, 0.59508, 0.40135, 0.47482, 0.37576, 0.43439, 0.36902, 0.56519, 0.33606, 0.56643, 0.32587, 0.44623, 0.32313, 0.58723, 0.31903, 0.72369, 0.31766, 0.89881, 0.31766, 1, 0.13849, 1, 0.12939, 0.8985, 0.11326, 0.71569, 0.02908, 0.60315, 0, 0.40756, 0, 0.16667, 0.06819, 0, 0.9245, 0, 0.67944, 0.20553, 0.32348, 0.20553, 0.38263, 0.21761], "triangles": [29, 30, 25, 26, 29, 25, 37, 42, 36, 28, 29, 26, 27, 28, 26, 41, 42, 37, 38, 41, 37, 40, 41, 38, 39, 40, 38, 35, 42, 43, 25, 30, 24, 36, 42, 35, 5, 6, 4, 6, 7, 4, 13, 15, 16, 14, 15, 13, 7, 8, 3, 4, 7, 3, 16, 17, 12, 16, 12, 13, 3, 8, 9, 2, 3, 9, 2, 9, 10, 12, 17, 11, 46, 50, 49, 20, 50, 46, 23, 50, 20, 49, 44, 45, 49, 45, 46, 21, 23, 20, 22, 23, 21, 32, 49, 50, 32, 50, 23, 24, 32, 23, 35, 49, 32, 44, 49, 35, 31, 32, 24, 33, 35, 32, 35, 43, 44, 24, 30, 31, 46, 47, 20, 47, 19, 20, 48, 19, 47, 10, 19, 48, 18, 19, 10, 11, 18, 10, 48, 47, 0, 1, 48, 0, 10, 48, 1, 11, 17, 18, 1, 2, 10, 34, 35, 33], "vertices": [2, 1, -104.9, -149.53, 0.94133, 16, 115.74, 137.1, 0.05867, 2, 1, -160.92, -148.7, 0.84533, 16, 113.08, 81.08, 0.15467, 2, 1, -191.44, -133.68, 0.664, 16, 96.11, 50.35, 0.336, 2, 1, -206.16, -115.26, 0.45067, 16, 76.52, 35.35, 0.54933, 2, 1, -239.01, -110.41, 0.248, 16, 71.27, 2.44, 0.752, 2, 1, -253.96, -109.53, 0.13067, 16, 70.04, -12.52, 0.86933, 2, 1, -253.18, -56.61, 0.13067, 16, 17.12, -12.52, 0.86933, 2, 1, -233.96, -55.59, 0.248, 16, 16.38, 6.67, 0.752, 2, 1, -202.99, -55.18, 0.45067, 16, 16.4, 37.63, 0.54933, 2, 1, -179.44, -54.88, 0.664, 16, 17.14, 61.17, 0.336, 2, 1, -147.29, -54.62, 0.84, 16, 18.81, 93.31, 0.16, 2, 1, -156.17, -50.78, 0.78667, 16, 15.11, 84.37, 0.21333, 2, 1, -181.65, -49.31, 0.664, 16, 11.61, 58.87, 0.336, 2, 1, -202.17, -50.53, 0.45067, 16, 11.74, 38.38, 0.54933, 2, 1, -228.31, -49.05, 0.27467, 16, 9.76, 12.23, 0.72533, 2, 1, -228.95, -34, 0.27467, 16, -5.28, 11.35, 0.72533, 2, 1, -200.79, -31.58, 0.45067, 16, -7.23, 39.47, 0.54933, 2, 1, -181.49, -31.25, 0.664, 16, -6.46, 58.76, 0.336, 2, 1, -152.25, -29.29, 0.84533, 16, -6.45, 87.97, 0.15467, 2, 1, -114.01, -23.75, 0.94133, 16, -9.89, 126.12, 0.05867, 2, 1, -113.62, -12.42, 0.888, 12, 16.12, 126.38, 0.112, 2, 1, -151.05, -7.29, 0.776, 12, 11.54, 88.89, 0.224, 2, 1, -150.89, 2.96, 0.776, 12, 1.29, 88.89, 0.224, 2, 1, -117.94, 5.3, 0.776, 12, -1.54, 121.81, 0.224, 2, 1, -159.83, 8.58, 0.67467, 12, -4.19, 79.87, 0.32533, 2, 1, -183.51, 9.06, 0.40267, 12, -4.32, 56.19, 0.59733, 2, 1, -200.58, 10.27, 0.24267, 12, -5.28, 39.1, 0.75733, 2, 1, -214.89, 10.24, 0.184, 12, -5.03, 24.8, 0.816, 2, 1, -214.2, 27.01, 0.184, 12, -21.81, 25.23, 0.816, 2, 1, -200.22, 26.3, 0.24267, 12, -21.31, 39.22, 0.75733, 2, 1, -181.07, 26.27, 0.40267, 12, -21.56, 58.37, 0.59733, 2, 1, -160.03, 25.52, 0.56267, 12, -21.13, 79.41, 0.43733, 2, 1, -152.85, 32.86, 0.672, 12, -28.57, 86.49, 0.328, 2, 1, -175.71, 35.16, 0.46133, 12, -30.53, 63.6, 0.53867, 2, 1, -175.78, 44.75, 0.46133, 12, -40.13, 63.38, 0.53867, 2, 1, -154.7, 47.41, 0.596, 12, -43.09, 84.42, 0.404, 2, 1, -179.36, 48.57, 0.40267, 12, -43.89, 59.74, 0.59733, 2, 1, -203.22, 50.12, 0.20533, 12, -45.08, 35.86, 0.79467, 2, 1, -233.86, 50.98, 0.08533, 12, -45.48, 5.22, 0.91467, 2, 1, -251.57, 51.24, 0.024, 12, -45.48, -12.49, 0.976, 2, 1, -250.79, 103.37, 0.024, 12, -97.62, -12.49, 0.976, 2, 1, -232.99, 105.75, 0.08533, 12, -100.27, 5.27, 0.91467, 2, 1, -200.93, 109.97, 0.20533, 12, -104.96, 37.26, 0.79467, 2, 1, -180.87, 134.17, 0.40267, 12, -129.46, 56.96, 0.59733, 2, 1, -146.52, 142.12, 0.67467, 12, -137.92, 91.18, 0.32533, 2, 1, -104.37, 141.49, 0.888, 12, -137.92, 133.34, 0.112, 1, 1, -75.5, 121.22, 1, 1, 1, -79.22, -127.94, 1, 2, 1, -114.12, -56.1, 0.912, 16, 22.46, 126.49, 0.088, 2, 1, -112.57, 47.47, 0.832, 12, -43.79, 126.54, 0.168, 2, 1, -114.94, 30.29, 0.832, 12, -26.57, 124.43, 0.168], "hull": 48, "edges": [88, 86, 86, 84, 84, 82, 82, 80, 78, 80, 78, 76, 76, 74, 74, 72, 72, 70, 70, 68, 68, 66, 66, 64, 64, 62, 62, 60, 60, 58, 58, 56, 56, 54, 54, 52, 52, 50, 50, 48, 48, 46, 46, 44, 44, 42, 42, 40, 40, 38, 38, 36, 36, 34, 34, 32, 32, 30, 30, 28, 28, 26, 26, 24, 24, 22, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 10, 12, 10, 8, 8, 6, 6, 4, 4, 2, 2, 0, 94, 0, 88, 90, 92, 94, 90, 92, 20, 96, 70, 98, 64, 100], "width": 291, "height": 171}}, "EyeLeft": {"images/Beholder1_Eye_1_Anticipate": {"name": "images/Beholder2_Eye_1", "x": 15.43, "y": 9.73, "rotation": -90.86, "width": 158, "height": 204}, "images/Beholder1_Eye_1": {"name": "images/Beholder2_Eye_1", "x": 15.43, "y": 9.73, "rotation": -90.86, "width": 158, "height": 204}, "images/Beholder1_Eye_1_Closed": {"name": "images/Beholder2_Eye_1_Closed", "x": 15.43, "y": 9.73, "rotation": -90.86, "width": 158, "height": 204}}, "EyeRight": {"images/Beholder1_Eye_1": {"name": "images/Beholder2_Eye_1", "x": 8.25, "y": -17.85, "scaleX": -1, "rotation": -90.86, "width": 158, "height": 204}, "images/Beholder1_Eye_Anticipate": {"name": "images/Beholder2_Eye_1", "x": 8.25, "y": -17.85, "scaleX": -1, "rotation": -90.86, "width": 158, "height": 204}, "images/Beholder1_Eye_1_Closed": {"name": "images/Beholder2_Eye_1_Closed", "x": 8.25, "y": -17.85, "scaleX": -1, "rotation": -90.86, "width": 158, "height": 204}}, "Eye_Middle": {"images/Beholder1_Eye_2": {"x": 2.03, "y": 1.97, "rotation": -90.86, "width": 219, "height": 182}, "images/Beholder1_Eye_1_Anticipate": {"name": "images/Beholder1_Eye_2_Anticipate", "x": -13.5, "y": 2.2, "rotation": -90.86, "width": 219, "height": 144}, "images/Beholder1_Eye_2_Closed": {"x": 2.03, "y": 1.97, "rotation": -90.86, "width": 219, "height": 182}}, "Horns": {"images/Beholder1_Horns": {"name": "images/Beholder2_Horns", "type": "mesh", "uvs": [1, 0.93809, 0.67293, 1, 0.39532, 1, 0.17288, 1, 0.10313, 1, 0.0025, 0.7363, 0, 0, 0.26929, 0, 0.46837, 0, 0.53594, 0, 0.99996, 0, 0.18562, 0.28212, 0.48517, 0.59082, 0.54216, 0.62298], "triangles": [2, 12, 13, 3, 11, 2, 11, 7, 2, 12, 7, 8, 12, 2, 7, 11, 3, 5, 3, 4, 5, 5, 6, 11, 12, 8, 9, 11, 6, 7, 2, 13, 1, 0, 1, 10, 10, 1, 9, 12, 9, 13, 1, 13, 9], "vertices": [1, 3, 138.18, -209.71, 1, 1, 3, -1.25, -13.6, 1, 2, 3, -83.89, 90.11, 0.07427, 4, -40.52, -34.55, 0.92573, 1, 4, 27.36, 107.82, 1, 1, 4, 49.72, 153.95, 1, 1, 4, 121.62, 201.3, 1, 1, 4, 233.06, 149.3, 1, 1, 4, 146.71, -28.79, 1, 1, 4, 82.87, -160.46, 1, 1, 3, 84.12, 161.72, 1, 1, 3, 269.62, -124.48, 1, 1, 4, 131.15, 47.09, 1, 1, 4, -11.29, -128.53, 1, 1, 3, -0.69, 101.3, 1], "hull": 11, "edges": [0, 20, 12, 22, 12, 14, 22, 14, 8, 10, 10, 12, 6, 8, 10, 6, 4, 6, 4, 24, 14, 16, 24, 16, 16, 18, 18, 20, 18, 26, 0, 2, 2, 4, 26, 2], "width": 735, "height": 167}}, "images/Bandage": {"Bandage": {"name": "images/SummonDrop3", "x": 2.38, "y": 547.95, "scaleX": -0.01, "scaleY": -0.01, "width": 85, "height": 107}}, "MiddleHorn": {"MiddleHorn": {"name": "images/Beholder2_Cross", "x": 125.17, "y": -1.38, "rotation": -90.3, "width": 283, "height": 359}}}}, {"name": "Dungeon2_Beaten", "bones": ["Frog<PERSON><PERSON><PERSON>"], "transform": ["FrogEyeLeftOffset", "FrogEyeRightOffset"], "attachments": {"Body": {"images/Beholder1_Body": {"name": "images/Beholder2_Body", "type": "<PERSON><PERSON><PERSON>", "skin": "Dungeon2", "parent": "images/Beholder1_Body", "width": 471, "height": 515}}, "Drips_Back": {"Drips_Back": {"name": "images/Beholder2_Drips", "type": "<PERSON><PERSON><PERSON>", "skin": "Dungeon2", "parent": "Drips_Back", "width": 291, "height": 171}}, "Horns": {"images/Beholder1_Horns": {"name": "images/Beholder2_Horns", "type": "<PERSON><PERSON><PERSON>", "skin": "Dungeon2", "parent": "images/Beholder1_Horns", "width": 735, "height": 167}}, "images/Bandage": {"Bandage": {"name": "images/Bandage", "type": "mesh", "uvs": [0.781, 0.10925, 0.87301, 0.19826, 0.9608, 0.28687, 1, 0.46644, 1, 0.82556, 0.96253, 1, 0.90518, 1, 0.79579, 0.73961, 0.69312, 0.67864, 0.56508, 0.63415, 0.40816, 0.646, 0.27702, 0.68871, 0.16121, 0.81037, 0.04916, 0.95626, 0.01871, 0.86514, 1e-05, 0.57047, 0.02225, 0.31173, 0.09936, 0.19963, 0.20272, 0.09013, 0.34377, 0.01226, 0.4818, 0, 0.6353, 0.01559], "triangles": [10, 11, 19, 7, 8, 0, 0, 8, 21, 11, 18, 19, 10, 20, 9, 10, 19, 20, 8, 9, 21, 9, 20, 21, 1, 7, 0, 5, 6, 4, 6, 7, 4, 13, 14, 12, 14, 15, 12, 7, 3, 4, 12, 15, 17, 15, 16, 17, 12, 17, 11, 7, 2, 3, 7, 1, 2, 17, 18, 11], "vertices": [2, 1, 155.41, -154.68, 0.46943, 8, 99.42, -153.85, 0.53057, 2, 1, 135.15, -201.89, 0.744, 8, 79.16, -201.06, 0.256, 1, 1, 117.71, -247.4, 1, 1, 1, 80.42, -267.17, 1, 1, 1, 6.45, -266.07, 1, 1, 1, -29.19, -246.11, 1, 2, 1, -28.75, -216.38, 0.832, 8, -84.74, -215.54, 0.168, 2, 1, 9.06, -157.46, 0.392, 8, -46.94, -156.63, 0.608, 2, 1, 22.84, -104.5, 0.184, 8, -33.16, -103.67, 0.816, 1, 8, -23.15, -37.4, 1, 2, 1, 31.62, 43.15, 0.072, 8, -24.38, 43.99, 0.928, 2, 1, 24.25, 111.2, 0.28, 8, -31.74, 112.03, 0.72, 2, 1, 8.23, 170.14, 0.58466, 8, -47.76, 170.98, 0.41534, 1, 1, -13.24, 227.3, 1, 1, 1, 7.02, 242.58, 1, 1, 1, 67.11, 251.5, 1, 1, 1, 119.88, 239.24, 1, 2, 1, 142.79, 198.84, 0.82706, 8, 86.8, 199.68, 0.17294, 2, 1, 164.35, 144.96, 0.488, 8, 108.35, 145.79, 0.512, 2, 1, 179.2, 71.61, 0.152, 8, 123.21, 72.44, 0.848, 2, 1, 180.53, 0.04, 0.056, 8, 124.53, 0.87, 0.944, 2, 1, 176.52, -79.56, 0.112, 8, 120.53, -78.73, 0.888], "hull": 22, "edges": [36, 38, 30, 32, 22, 20, 16, 14, 14, 12, 10, 12, 2, 4, 4, 6, 38, 40, 40, 42, 20, 18, 18, 16, 6, 8, 10, 8, 2, 0, 0, 42, 32, 34, 34, 36, 22, 24, 24, 26, 26, 28, 28, 30], "width": 473, "height": 206}}, "images/Beholder_ExtraDrips": {"images/Beholder_ExtraDrips": {"name": "images/Beholder_ExtraDrips2", "type": "mesh", "path": "images/Beholder_ExtraDrips", "uvs": [1, 1, 0.49573, 1, 0, 1, 0, 0, 0.49196, 0, 1, 0], "triangles": [1, 4, 5, 1, 5, 0, 2, 3, 4, 2, 4, 1], "vertices": [1, 1, -43.99, -94.96, 1, 1, 1, -42.46, 7.9, 1, 1, 1, -40.95, 109.02, 1, 1, 1, 90.04, 107.07, 1, 1, 1, 88.54, 6.72, 1, 1, 1, 86.99, -96.91, 1], "hull": 6, "edges": [4, 6, 0, 10, 0, 2, 2, 4, 6, 8, 8, 10], "width": 204, "height": 131}}, "MiddleHorn": {"MiddleHorn": {"name": "images/Beholder2_Cross", "x": 125.17, "y": -1.38, "rotation": -90.3, "width": 283, "height": 359}}}}, {"name": "Dungeon3", "bones": ["HornBtmLeft3c2", "HornBtmLeft3d", "HornBtmLeft3d2", "TentacleLeftBtm2", "TentacleLeftBtm", "HornBtmLeft3b", "HornBtmLeft3c", "HornBtmLeft2c", "HornBtmLeft2c2", "HornBtmLeft2b", "HornBtmLeft2b2", "HornBtmLeft3b2", "TentacleLeftTop", "HornBtmLeft2d", "TentacleLeftTop2", "HornBtmLeft2d2"], "attachments": {"Body": {"images/Beholder1_Body": {"name": "images/Beholder3_Body", "type": "mesh", "uvs": [0.87747, 0.08852, 0.96412, 0.19534, 1, 0.39271, 1, 0.49493, 0.92557, 0.90683, 0.85785, 0.94323, 0.73953, 0.98569, 0.52835, 1, 0.4798, 1, 0.26754, 0.98486, 0.09228, 0.92799, 0.06605, 0.8461, 0.01195, 0.56168, 0, 0.46182, 0, 0.35595, 0.01929, 0.21793, 0.09203, 0.13073, 0.25464, 0.0566, 0.40013, 0, 0.71486, 0, 0.03985, 0.66405, 0.07781, 0.76642, 0.08614, 0.80401, 0.17321, 0.93026, 0.59605, 0.94639, 0.65291, 0.98348, 0.79385, 0.91517, 0.93525, 0.82721, 0.91292, 0.78323, 0.96452, 0.63956, 0.9795, 0.30005, 0.93681, 0.22512, 0.03313, 0.27629, 0.12306, 0.14708, 0.73091, 0.04819, 0.83253, 0.11032, 0.35563, 0.98664, 0.11002, 0.77063, 0.03451, 0.55689, 0.02535, 0.35636, 0.14893, 0.15894, 0.25711, 0.08374, 0.40814, 0.02701, 0.5012, 0.01768, 0.65299, 0.04488, 0.83519, 0.13965, 0.92291, 0.24069, 0.9786, 0.39304, 0.94274, 0.63279, 0.88325, 0.79446, 0.85795, 0.87314, 0.7626, 0.84982, 0.68708, 0.92599, 0.58411, 0.90811, 0.49334, 0.96174, 0.38731, 0.90966, 0.12288, 0.89617, 0.23501, 0.87752, 0.29527, 0.95524, 0.28379, 0.28299, 0.16632, 0.36052, 0.15697, 0.5455, 0.19969, 0.65296, 0.31315, 0.71553, 0.34519, 0.68288, 0.39591, 0.69377, 0.44664, 0.74953, 0.50537, 0.72777, 0.5828, 0.7781, 0.64954, 0.78218, 0.67757, 0.71825, 0.36121, 0.20002, 0.51339, 0.25715, 0.62418, 0.15785, 0.72697, 0.29115, 0.84444, 0.34148, 0.87113, 0.54687, 0.75366, 0.67609, 0.85111, 0.62304], "triangles": [68, 66, 67, 66, 63, 65, 65, 63, 64, 49, 77, 78, 69, 68, 70, 68, 67, 70, 66, 65, 67, 70, 67, 72, 74, 77, 70, 63, 62, 64, 74, 70, 72, 65, 72, 67, 72, 65, 59, 72, 73, 74, 59, 65, 64, 78, 77, 76, 76, 77, 74, 59, 64, 61, 59, 71, 72, 64, 62, 61, 59, 61, 60, 75, 76, 74, 7, 25, 6, 7, 24, 25, 8, 54, 7, 24, 7, 53, 9, 36, 8, 36, 55, 8, 8, 55, 54, 9, 58, 36, 36, 58, 55, 6, 26, 5, 25, 52, 6, 6, 52, 26, 10, 23, 9, 23, 57, 9, 9, 57, 58, 25, 24, 52, 55, 66, 54, 7, 54, 53, 53, 66, 68, 66, 53, 54, 58, 57, 55, 24, 53, 52, 5, 50, 4, 5, 26, 50, 10, 56, 23, 23, 56, 57, 10, 11, 56, 53, 69, 52, 52, 51, 26, 52, 69, 51, 26, 51, 50, 57, 63, 55, 66, 55, 63, 53, 68, 69, 3, 27, 29, 3, 4, 27, 4, 50, 27, 27, 50, 49, 11, 22, 56, 22, 37, 56, 56, 37, 57, 37, 62, 57, 57, 62, 63, 49, 28, 27, 50, 51, 49, 69, 70, 51, 70, 77, 51, 51, 77, 49, 12, 20, 11, 11, 21, 22, 11, 20, 21, 27, 28, 29, 22, 21, 37, 48, 28, 49, 28, 48, 29, 48, 49, 78, 38, 61, 62, 37, 21, 20, 12, 38, 20, 38, 62, 37, 37, 20, 38, 29, 48, 3, 78, 76, 48, 3, 48, 47, 12, 13, 38, 60, 61, 39, 48, 76, 47, 76, 75, 47, 38, 39, 61, 39, 38, 13, 47, 2, 3, 13, 14, 39, 2, 47, 30, 30, 47, 46, 39, 40, 60, 60, 40, 59, 40, 32, 33, 40, 39, 32, 39, 14, 32, 14, 15, 32, 47, 75, 46, 46, 75, 45, 46, 31, 30, 31, 1, 30, 2, 30, 1, 75, 74, 45, 74, 73, 45, 40, 41, 59, 59, 41, 71, 33, 15, 16, 33, 32, 15, 71, 42, 72, 42, 43, 72, 72, 43, 73, 46, 45, 31, 1, 31, 0, 71, 41, 42, 31, 45, 0, 41, 40, 17, 73, 44, 45, 73, 43, 44, 17, 40, 33, 44, 34, 45, 34, 35, 45, 45, 35, 0, 33, 16, 17, 35, 34, 0, 41, 17, 42, 17, 18, 42, 44, 19, 34, 0, 34, 19, 44, 43, 19, 42, 18, 43, 43, 18, 19], "vertices": [1, 1, 266.5, -162.63, 1, 1, 1, 220.87, -199.22, 1, 1, 1, 137.36, -213.4, 1, 1, 1, 94.23, -212.76, 1, 1, 1, -79.1, -178.17, 1, 1, 1, -94.02, -148.82, 1, 1, 1, -111.18, -97.68, 1, 1, 1, -115.86, -6.8, 1, 1, 1, -115.55, 14.08, 1, 1, 1, -107.8, 105.25, 1, 1, 1, -82.68, 180.24, 1, 1, 1, -47.96, 191.01, 1, 1, 1, 72.4, 212.48, 1, 1, 1, 114.61, 216.98, 1, 1, 1, 159.29, 216.32, 1, 1, 1, 217.4, 207.16, 1, 1, 1, 253.73, 175.33, 1, 1, 1, 283.96, 104.95, 1, 1, 1, 306.91, 42.04, 1, 1, 1, 304.89, -93.28, 1, 1, 1, 29.03, 201.12, 1, 1, 1, -14.41, 185.45, 1, 1, 1, -30.33, 182.1, 1, 1, 1, -84.16, 145.46, 1, 1, 1, -93.68, -36.24, 1, 1, 1, -109.69, -60.45, 1, 1, 1, -81.77, -121.48, 1, 1, 1, -45.56, -182.83, 1, 1, 1, -26.86, -173.5, 1, 1, 1, 33.43, -196.6, 1, 1, 1, 176.59, -205.17, 1, 1, 1, 208.48, -187.29, 1, 1, 1, 192.69, 201.57, 1, 1, 1, 246.63, 162.09, 1, 1, 1, 284.46, -99.88, 1, 1, 1, 257.59, -143.18, 1, 1, 1, -109.12, 67.38, 1, 1, 1, -16.4, 171.62, 1, 1, 1, 74.28, 202.75, 1, 1, 1, 158.95, 205.42, 1, 1, 1, 241.46, 151.05, 1, 1, 1, 272.49, 104.06, 1, 1, 1, 295.47, 38.77, 1, 1, 1, 298.81, -1.3, 1, 1, 1, 286.35, -66.4, 1, 1, 1, 245.2, -144.14, 1, 1, 1, 202, -181.22, 1, 1, 1, 137.36, -204.2, 1, 1, 1, 36.42, -187.27, 1, 2, 1, -31.41, -160.68, 0.296, 8, -87.4, -159.84, 0.704, 1, 1, -64.45, -149.3, 1, 1, 1, -54, -108.45, 1, 1, 1, -85.65, -75.51, 1, 1, 1, -77.45, -31.34, 1, 1, 1, -99.49, 8.02, 1, 1, 1, -76.84, 53.28, 1, 1, 1, -69.45, 166.88, 1, 1, 1, -62.3, 118.56, 1, 1, 1, -95.48, 93.14, 1, 2, 1, 188.25, 93.84, 0.296, 8, 132.26, 94.68, 0.704, 2, 1, 156.29, 144.84, 0.296, 8, 100.3, 145.67, 0.704, 2, 1, 78.3, 150.02, 0.296, 8, 22.3, 150.86, 0.704, 2, 1, 32.68, 132.33, 0.296, 8, -23.31, 133.17, 0.704, 2, 1, 5.55, 83.94, 0.296, 8, -50.44, 84.78, 0.704, 2, 1, 19.12, 69.96, 0.296, 8, -36.87, 70.8, 0.704, 2, 1, 14.2, 48.22, 0.296, 8, -41.79, 49.06, 0.704, 2, 1, -9.65, 26.76, 0.296, 8, -65.65, 27.6, 0.704, 2, 1, -0.85, 1.37, 0.296, 8, -56.84, 2.21, 0.704, 2, 1, -22.58, -31.6, 0.296, 8, -78.57, -30.76, 0.704, 2, 1, -24.73, -60.27, 0.296, 8, -80.72, -59.44, 0.704, 2, 1, 2.07, -72.73, 0.296, 8, -53.93, -71.89, 0.704, 2, 1, 222.76, 60.03, 0.296, 8, 166.77, 60.87, 0.704, 2, 1, 197.68, -5.04, 0.296, 8, 141.69, -4.2, 0.704, 2, 1, 238.87, -53.3, 0.296, 8, 182.88, -52.46, 0.704, 2, 1, 181.96, -96.65, 0.296, 8, 125.97, -95.82, 0.704, 2, 1, 159.97, -146.84, 0.296, 8, 103.98, -146.01, 0.704, 2, 1, 73.14, -157.03, 0.296, 8, 17.15, -156.19, 0.704, 2, 1, 19.37, -105.71, 0.296, 8, -36.62, -104.87, 0.704, 2, 1, 41.13, -147.94, 0.296, 8, -14.87, -147.1, 0.704], "hull": 20, "edges": [26, 24, 24, 40, 40, 42, 42, 44, 44, 22, 22, 20, 20, 46, 14, 16, 48, 50, 50, 12, 12, 52, 52, 10, 10, 8, 8, 54, 54, 56, 56, 58, 58, 6, 6, 4, 4, 60, 60, 62, 26, 28, 28, 64, 64, 30, 30, 32, 32, 66, 66, 34, 34, 36, 36, 38, 38, 68, 68, 70, 70, 0, 0, 2, 2, 62, 14, 48, 18, 46, 18, 72, 2, 4, 38, 0, 28, 30, 32, 34, 16, 18, 18, 20, 22, 24, 6, 8, 12, 14, 10, 12, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 112, 114, 114, 116, 116, 110, 112, 74, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 118, 142, 142, 144, 144, 146, 146, 148, 148, 150, 150, 152, 140, 154, 154, 156, 156, 152], "width": 430, "height": 384}}, "Drips_Back": {"Drips_Back": {"name": "images/Beholder3_Drips", "type": "mesh", "uvs": [1, 0.18294, 0.97888, 0.26858, 1, 0.43534, 0.92912, 0.50496, 0.93079, 0.64778, 0.92744, 0.73347, 0.92576, 0.77521, 0.87292, 0.77192, 0.8704, 0.71919, 0.8704, 0.64997, 0.86118, 0.59944, 0.85028, 0.64558, 0.84608, 0.75214, 0.83769, 0.82245, 0.76557, 0.82245, 0.76137, 0.75214, 0.76221, 0.64009, 0.75215, 0.56099, 0.74124, 0.6324, 0.7404, 0.69282, 0.68854, 0.69414, 0.68997, 0.63419, 0.67555, 0.58078, 0.6336, 0.59077, 0.62052, 0.67498, 0.61998, 0.73278, 0.61781, 0.80058, 0.55733, 0.80129, 0.55351, 0.73992, 0.54502, 0.67733, 0.53573, 0.73057, 0.53457, 0.87964, 0.53805, 1, 0.43702, 1, 0.43238, 0.86747, 0.43764, 0.71688, 0.44328, 0.62146, 0.41616, 0.62065, 0.41163, 0.6565, 0.41055, 0.72523, 0.34589, 0.72138, 0.34381, 0.65293, 0.32786, 0.5739, 0.30347, 0.65756, 0.29999, 0.78077, 0.30115, 0.84161, 0.22335, 0.83857, 0.22121, 0.78343, 0.21773, 0.6587, 0.21199, 0.51419, 0.18637, 0.65718, 0.11902, 0.66935, 0.10966, 0.52264, 0.02265, 0.43325, 0.02862, 0.28576, 0, 0.13877, 0, 0, 0.19575, 0, 0.32066, 0, 0.43087, 0, 0.60869, 0, 0.75858, 0, 0.84381, 0, 1, 0, 0.20363, 0.27821, 0.43328, 0.27526, 0.61482, 0.30739, 0.75736, 0.30354, 0.85366, 0.28834, 0.31935, 0.27672], "triangles": [46, 44, 45, 12, 13, 15, 13, 14, 15, 39, 40, 38, 46, 47, 44, 47, 48, 44, 34, 35, 30, 6, 7, 5, 34, 30, 31, 33, 34, 31, 33, 31, 32, 7, 8, 5, 27, 28, 26, 26, 28, 25, 11, 12, 16, 12, 15, 16, 24, 25, 28, 5, 8, 4, 4, 8, 9, 24, 28, 29, 30, 35, 29, 40, 41, 38, 35, 36, 29, 20, 21, 19, 19, 21, 18, 24, 29, 23, 23, 29, 36, 38, 41, 37, 41, 42, 37, 4, 9, 10, 10, 3, 4, 11, 16, 10, 10, 16, 17, 21, 22, 18, 18, 22, 17, 23, 36, 66, 66, 36, 65, 36, 37, 65, 37, 42, 65, 10, 17, 3, 23, 66, 22, 17, 22, 67, 42, 69, 65, 3, 17, 68, 17, 67, 68, 67, 22, 66, 2, 68, 1, 2, 3, 68, 61, 67, 66, 67, 61, 68, 61, 62, 68, 66, 65, 60, 61, 66, 60, 1, 68, 0, 54, 55, 64, 55, 57, 64, 69, 64, 58, 69, 58, 65, 58, 59, 65, 58, 64, 57, 65, 59, 60, 0, 68, 62, 62, 63, 0, 55, 56, 57, 44, 48, 43, 51, 52, 50, 42, 43, 48, 49, 42, 48, 50, 52, 49, 49, 69, 42, 52, 53, 64, 52, 64, 49, 64, 53, 54, 49, 64, 69], "vertices": [1, 1, -69.24, -133.77, 1, 1, 1, -87.39, -127.61, 1, 1, 1, -122.99, -132.97, 1, 2, 1, -137.52, -112.98, 0.664, 16, 83.02, 103.99, 0.336, 2, 1, -167.95, -112.99, 0.552, 16, 83.48, 73.57, 0.448, 2, 1, -186.18, -111.78, 0.52, 16, 82.55, 55.32, 0.48, 2, 1, -195.07, -111.18, 0.104, 16, 82.08, 46.42, 0.896, 2, 1, -194.15, -96.45, 0.168, 16, 67.34, 47.13, 0.832, 2, 1, -182.9, -95.92, 0.6, 16, 66.64, 58.36, 0.4, 2, 1, -168.16, -96.14, 0.648, 16, 66.64, 73.1, 0.352, 3, 1, -157.36, -93.72, 0.61408, 16, 64.06, 83.86, 0.14592, 12, 98.06, 83.86, 0.24, 2, 1, -167.14, -90.54, 0.664, 12, 95.02, 74.04, 0.336, 2, 1, -189.82, -89.03, 0.456, 12, 93.85, 51.34, 0.544, 1, 12, 91.51, 36.36, 1, 1, 12, 71.39, 36.36, 1, 2, 1, -189.47, -65.4, 0.456, 12, 70.22, 51.34, 0.544, 2, 1, -165.61, -65.99, 0.712, 12, 70.45, 75.21, 0.288, 2, 1, -148.72, -63.43, 0.776, 12, 67.64, 92.05, 0.224, 2, 1, -163.88, -60.16, 0.648, 16, 30.6, 76.84, 0.352, 2, 1, -176.75, -59.73, 0.616, 16, 30.37, 63.97, 0.384, 2, 1, -176.81, -45.26, 0.632, 16, 15.9, 63.69, 0.368, 2, 1, -164.05, -45.85, 0.632, 16, 16.29, 76.46, 0.368, 2, 1, -152.62, -42, 0.632, 16, 12.27, 87.84, 0.368, 2, 1, -154.57, -30.26, 0.632, 16, 0.57, 85.71, 0.368, 2, 1, -172.45, -26.35, 0.632, 16, -3.08, 67.77, 0.368, 2, 1, -184.76, -26.01, 0.632, 16, -3.23, 55.46, 0.368, 2, 1, -199.19, -25.19, 0.632, 16, -3.84, 41.02, 0.368, 2, 1, -199.09, -8.32, 0.632, 16, -20.71, 40.87, 0.368, 2, 1, -186, -7.45, 0.632, 16, -21.78, 53.94, 0.368, 2, 1, -172.64, -5.28, 0.632, 16, -24.15, 67.27, 0.368, 2, 1, -183.94, -2.52, 0.568, 16, -26.74, 55.93, 0.432, 2, 1, -215.68, -1.72, 0.216, 16, -27.06, 24.18, 0.784, 1, 16, -26.09, -1.46, 1, 1, 16, -54.28, -1.46, 1, 2, 1, -212.66, 26.75, 0.328, 16, -55.57, 26.77, 0.672, 2, 1, -180.61, 24.8, 0.488, 16, -54.11, 58.85, 0.512, 2, 1, -160.31, 22.93, 0.632, 16, -52.53, 79.17, 0.368, 2, 1, -160.03, 30.49, 0.696, 12, -26.1, 79.34, 0.304, 2, 1, -167.64, 31.87, 0.616, 12, -27.36, 71.71, 0.384, 2, 1, -182.28, 32.39, 0.296, 12, -27.66, 57.07, 0.704, 2, 1, -181.19, 50.41, 0.392, 12, -45.7, 57.89, 0.608, 2, 1, -166.6, 50.78, 0.616, 12, -46.28, 72.47, 0.384, 2, 1, -149.7, 54.97, 0.616, 12, -50.73, 89.3, 0.384, 2, 1, -167.42, 62.04, 0.616, 12, -57.54, 71.48, 0.384, 2, 1, -193.65, 63.41, 0.328, 12, -58.51, 45.24, 0.672, 1, 12, -58.19, 32.28, 1, 1, 12, -79.89, 32.93, 1, 2, 1, -193.89, 85.39, 0.392, 12, -80.49, 44.67, 0.608, 2, 1, -167.31, 85.97, 0.616, 12, -81.46, 71.24, 0.384, 2, 1, -136.51, 87.11, 0.616, 12, -83.06, 102.02, 0.384, 3, 1, -166.85, 94.71, 0.39917, 16, -124.21, 71.56, 0.352, 12, -90.21, 71.56, 0.24883, 3, 1, -169.16, 113.54, 0.2809, 16, -143, 68.97, 0.544, 12, -109, 68.97, 0.1751, 2, 1, -137.88, 115.68, 0.616, 12, -111.61, 100.22, 0.384, 2, 1, -118.48, 139.67, 0.616, 12, -135.89, 119.26, 0.384, 2, 1, -87.09, 137.54, 0.616, 12, -134.22, 150.68, 0.384, 1, 1, -55.67, 145.06, 1, 1, 1, -26.11, 144.61, 1, 1, 1, -26.93, 90.01, 1, 1, 1, -27.45, 55.16, 1, 1, 1, -27.91, 24.41, 1, 1, 1, -28.65, -25.19, 1, 1, 1, -29.27, -67.01, 1, 1, 1, -29.62, -90.78, 1, 1, 1, -30.27, -134.35, 1, 2, 1, -86.21, 88.69, 0.616, 12, -85.4, 152.29, 0.384, 2, 1, -86.54, 24.62, 0.632, 16, -55.32, 152.91, 0.368, 2, 1, -94.14, -25.92, 0.632, 16, -4.67, 146.07, 0.368, 1, 1, -93.91, -65.7, 1, 1, 1, -91.07, -92.61, 1, 2, 1, -86.38, 56.4, 0.616, 12, -53.11, 152.6, 0.384], "hull": 64, "edges": [110, 112, 110, 108, 108, 106, 106, 104, 104, 102, 102, 100, 100, 98, 98, 96, 96, 94, 94, 92, 92, 90, 90, 88, 88, 86, 86, 84, 84, 82, 108, 128, 72, 70, 70, 68, 68, 66, 64, 66, 64, 62, 62, 60, 60, 58, 58, 56, 56, 54, 54, 52, 52, 50, 50, 48, 48, 46, 46, 44, 44, 42, 42, 40, 40, 38, 38, 36, 36, 34, 34, 32, 32, 30, 30, 28, 28, 26, 26, 24, 24, 22, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 0, 126, 2, 0, 130, 132, 132, 134, 2, 136, 136, 134, 128, 138, 138, 130, 82, 80, 80, 78, 76, 78, 138, 116, 116, 118, 130, 118, 118, 120, 132, 120, 120, 122, 134, 122, 112, 114, 114, 116, 128, 114, 122, 124, 124, 126, 136, 124, 72, 74, 74, 76], "width": 279, "height": 213}}, "EyeLeft": {"images/Beholder1_Eye_1": {"x": 0.73, "y": 15.49, "rotation": -90.86, "width": 115, "height": 137}, "images/Beholder1_Eye_1_Closed": {"x": 0.73, "y": 15.49, "rotation": -90.86, "width": 115, "height": 137}, "images/Beholder1_Eye_1_Anticipate": {"name": "images/Beholder1_Eye_Anticipate", "x": 0.73, "y": 15.49, "rotation": -90.86, "width": 117, "height": 136}}, "EyeRight": {"images/Beholder1_Eye_1": {"x": -3.81, "y": -20.45, "scaleX": -1, "rotation": -90.86, "width": 115, "height": 137}, "images/Beholder1_Eye_1_Closed": {"x": -3.81, "y": -20.45, "scaleX": -1, "rotation": -90.86, "width": 115, "height": 137}, "images/Beholder1_Eye_Anticipate": {"x": -3.81, "y": -20.45, "scaleX": -1, "rotation": -90.86, "width": 117, "height": 136}}, "Eye_Middle": {"images/Beholder1_Eye_2": {"x": 2.03, "y": 1.97, "rotation": -90.86, "width": 219, "height": 182}, "images/Beholder1_Eye_1_Anticipate": {"name": "images/Beholder1_Eye_2_Anticipate", "x": -14.73, "y": 2.22, "rotation": -90.86, "width": 219, "height": 144}, "images/Beholder1_Eye_2_Closed": {"x": 2.03, "y": 1.97, "rotation": -90.86, "width": 219, "height": 182}}, "Horns": {"images/Beholder1_Horns": {"name": "images/Beholder3_Horns", "type": "mesh", "uvs": [0.68837, 0.34588, 1, 0.50854, 1, 1, 0.69736, 1, 0.28416, 1, 0, 1, 0, 0.31905, 0.32881, 0.31291, 0.31979, 0, 0.45231, 0, 0.47156, 0.47754, 0.29521, 0.5874, 0.25112, 0.66274, 0.37785, 0.86367, 0.64316, 0.84202, 0.72991, 0.62586, 0.70852, 0.59058, 0.53494, 0.47754, 0.53497, 0, 0.69382, 0], "triangles": [4, 13, 3, 13, 4, 12, 4, 5, 12, 5, 6, 12, 12, 6, 11, 11, 7, 10, 11, 6, 7, 9, 10, 7, 7, 8, 9, 13, 14, 3, 15, 1, 2, 17, 0, 16, 0, 17, 18, 0, 18, 19, 16, 0, 1, 15, 16, 1, 2, 3, 14, 2, 14, 15], "vertices": [2, 1, 414.56, -89.96, 0.672, 3, 211.23, 174.94, 0.328, 2, 1, 345.88, -234.18, 0.672, 3, 234.4, 16.89, 0.328, 2, 1, 144.9, -231.19, 0.67203, 3, 65.73, -92.45, 0.32797, 2, 1, 146.99, -90.18, 0.672, 4, -104.24, -194.12, 0.328, 2, 1, 149.85, 102.35, 0.672, 4, -20.24, -20.85, 0.328, 2, 1, 151.82, 234.75, 0.672, 4, 37.52, 98.31, 0.328, 2, 1, 430.29, 230.62, 0.672, 4, 288.13, -23.18, 0.328, 2, 1, 430.53, 77.38, 0.672, 4, 223.56, -162.15, 0.328, 2, 1, 558.56, 79.68, 0.672, 4, 340.55, -214.2, 0.328, 2, 1, 557.64, 17.93, 0.672, 4, 313.61, -269.77, 0.328, 2, 1, 362.22, 11.86, 0.672, 4, 133.95, -192.64, 0.328, 2, 1, 318.51, 94.7, 0.67214, 4, 129.36, -99.09, 0.32786, 2, 1, 288.01, 115.7, 0.67987, 4, 110.6, -67.16, 0.32013, 2, 1, 204.96, 57.87, 0.672, 4, 10.89, -84.46, 0.328, 2, 1, 211.98, -65.89, 0.672, 4, -35.07, -199.58, 0.328, 2, 1, 299.77, -107.62, 0.68008, 3, 125.67, 96.4, 0.31992, 2, 1, 314.35, -97.86, 0.67372, 3, 132.36, 112.62, 0.32628, 2, 1, 361.78, -17.67, 0.672, 3, 127.15, 205.64, 0.328, 2, 1, 557.07, -20.59, 0.672, 3, 291.05, 311.87, 0.328, 2, 1, 555.97, -94.6, 0.672, 3, 331.32, 249.76, 0.328], "hull": 20, "edges": [24, 26, 8, 10, 26, 8, 24, 22, 22, 20, 20, 18, 36, 34, 34, 32, 32, 30, 30, 28, 4, 6, 6, 8, 28, 6, 36, 38, 38, 0, 4, 2, 0, 2, 16, 18, 16, 14, 10, 12, 14, 12, 26, 28], "width": 466, "height": 409}}, "images/Bandage": {"Bandage": {"name": "images/SummonDrop4", "path": "images/SummonDrop3", "x": -19.33, "y": 321.66, "scaleX": -0.01, "scaleY": -0.01, "width": 85, "height": 107}}, "TentacleLeft1": {"images/Beholder3_Tentacle1": {"type": "mesh", "uvs": [0.29313, 0.13053, 0.37253, 0.15847, 0.42753, 0.17782, 0.49886, 0.18288, 0.57985, 0.18863, 0.64469, 0.19297, 0.72275, 0.19819, 0.80917, 0.20467, 0.88653, 0.21046, 1, 0.23324, 1, 0.81508, 0.87713, 0.78054, 0.72383, 0.79077, 0.65292, 0.7876, 0.58024, 0.78434, 0.50197, 0.80457, 0.42393, 0.82473, 0.36663, 0.84175, 0.28278, 0.86664, 0.16794, 1, 0, 1, 0, 0, 0.17844, 0], "triangles": [11, 7, 8, 11, 8, 9, 10, 11, 9, 18, 22, 0, 19, 20, 21, 22, 19, 21, 18, 19, 22, 18, 0, 17, 16, 1, 2, 15, 16, 2, 15, 2, 3, 17, 0, 1, 17, 1, 16, 14, 4, 5, 13, 5, 6, 14, 5, 13, 12, 6, 7, 13, 6, 12, 14, 15, 3, 14, 3, 4, 12, 7, 11], "vertices": [3, 22, 140.62, -28.43, 0.01033, 23, 68.42, -26.47, 0.51558, 24, -4.88, -26.42, 0.47409, 3, 22, 116.67, -26.22, 0.06551, 23, 44.42, -24.95, 0.69672, 24, -28.86, -24.61, 0.23777, 3, 22, 100.08, -24.68, 0.20234, 23, 27.79, -23.89, 0.71514, 24, -45.48, -23.37, 0.08252, 4, 22, 78.55, -24.03, 0.42302, 23, 6.24, -23.86, 0.55667, 24, -67.02, -23.09, 0.01545, 21, 151.62, -23.77, 0.00486, 4, 22, 54.1, -23.29, 0.62573, 23, -18.22, -23.83, 0.32513, 24, -91.48, -22.77, 0.00024, 21, 127.17, -23.12, 0.04889, 3, 22, 34.52, -22.72, 0.69971, 23, -37.8, -23.82, 0.13117, 21, 107.59, -22.61, 0.16912, 3, 22, 10.96, -22.03, 0.60065, 23, -61.38, -23.81, 0.03489, 21, 84.02, -21.99, 0.36446, 3, 22, -15.13, -21.22, 0.42044, 23, -87.48, -23.75, 0.00257, 21, 57.93, -21.27, 0.57699, 2, 22, -38.49, -20.5, 0.22496, 21, 34.57, -20.62, 0.77504, 2, 22, -72.73, -18.48, 0.14373, 21, 0.32, -18.71, 0.85627, 2, 22, -72.17, 20.5, 0.27425, 21, 0.76, 20.27, 0.72575, 3, 22, -35.1, 17.65, 0.52915, 23, -108.56, 14.53, 0.00036, 21, 37.84, 17.54, 0.47049, 3, 22, 11.2, 17.67, 0.68154, 23, -62.27, 15.88, 0.03381, 21, 84.14, 17.71, 0.28465, 3, 22, 32.61, 17.15, 0.73914, 23, -40.86, 15.98, 0.12744, 21, 105.55, 17.26, 0.13342, 4, 22, 54.56, 16.62, 0.63595, 23, -18.91, 16.08, 0.31815, 24, -91.71, 17.14, 0.00018, 21, 127.5, 16.79, 0.04572, 4, 22, 78.21, 17.63, 0.4287, 23, 4.71, 17.77, 0.54978, 24, -68.08, 18.56, 0.01862, 21, 151.15, 17.89, 0.00289, 3, 22, 101.8, 18.64, 0.20113, 23, 28.25, 19.46, 0.70649, 24, -44.52, 19.98, 0.09237, 3, 22, 119.12, 19.53, 0.06365, 23, 45.54, 20.85, 0.67843, 24, -27.21, 21.17, 0.25793, 3, 22, 144.46, 20.83, 0.00895, 23, 70.83, 22.89, 0.493, 24, -1.9, 22.91, 0.49806, 2, 23, 105.38, 32.32, 0.26143, 24, 32.76, 31.95, 0.73857, 2, 23, 156.1, 33.05, 0.1164, 24, 83.48, 32.09, 0.8836, 2, 23, 157.06, -33.94, 0.12346, 24, 83.67, -34.91, 0.87654, 3, 22, 175.13, -37.68, 0.00011, 23, 103.18, -34.72, 0.28211, 24, 29.78, -35.06, 0.71778], "hull": 23, "edges": [40, 42, 42, 44, 44, 0, 16, 18, 38, 40, 38, 36, 24, 22, 18, 20, 22, 20, 12, 14, 14, 16, 4, 6, 6, 8, 0, 2, 2, 4, 32, 34, 34, 36, 28, 30, 30, 32, 24, 26, 26, 28, 8, 10, 10, 12], "width": 302, "height": 67}}, "TentacleLeft2": {"images/Beholder3_Tentacle1": {"type": "mesh", "uvs": [0.29313, 0.13053, 0.37253, 0.15847, 0.42753, 0.17782, 0.49886, 0.18288, 0.57985, 0.18863, 0.64469, 0.19297, 0.72275, 0.19819, 0.80917, 0.20467, 0.88653, 0.21046, 1, 0.23324, 1, 0.81508, 0.87713, 0.78054, 0.72383, 0.79077, 0.65292, 0.7876, 0.58024, 0.78434, 0.50197, 0.80457, 0.42393, 0.82473, 0.36663, 0.84175, 0.28278, 0.86664, 0.16794, 1, 0, 1, 0, 0, 0.17844, 0], "triangles": [11, 7, 8, 11, 8, 9, 10, 11, 9, 18, 22, 0, 19, 20, 21, 22, 19, 21, 18, 19, 22, 18, 0, 17, 16, 1, 2, 15, 16, 2, 15, 2, 3, 17, 0, 1, 17, 1, 16, 14, 4, 5, 13, 5, 6, 14, 5, 13, 12, 6, 7, 13, 6, 12, 14, 15, 3, 14, 3, 4, 12, 7, 11], "vertices": [3, 26, 140.62, -28.43, 0.01033, 27, 68.42, -26.47, 0.51558, 28, -4.88, -26.42, 0.47409, 3, 26, 116.67, -26.22, 0.06551, 27, 44.42, -24.95, 0.69672, 28, -28.86, -24.61, 0.23777, 3, 26, 100.08, -24.68, 0.20234, 27, 27.79, -23.89, 0.71514, 28, -45.48, -23.37, 0.08252, 4, 26, 78.55, -24.03, 0.42302, 27, 6.24, -23.86, 0.55667, 28, -67.02, -23.09, 0.01545, 25, 151.62, -23.77, 0.00486, 4, 26, 54.1, -23.29, 0.62573, 27, -18.22, -23.83, 0.32513, 28, -91.48, -22.77, 0.00024, 25, 127.17, -23.12, 0.04889, 3, 26, 34.52, -22.72, 0.69971, 27, -37.8, -23.82, 0.13117, 25, 107.59, -22.61, 0.16912, 3, 26, 10.96, -22.03, 0.60065, 27, -61.38, -23.81, 0.03489, 25, 84.02, -21.99, 0.36446, 3, 26, -15.13, -21.22, 0.42044, 27, -87.48, -23.75, 0.00257, 25, 57.93, -21.27, 0.57699, 2, 26, -38.49, -20.5, 0.22496, 25, 34.57, -20.62, 0.77504, 2, 26, -72.73, -18.48, 0.14373, 25, 0.32, -18.71, 0.85627, 2, 26, -72.17, 20.5, 0.27425, 25, 0.76, 20.27, 0.72575, 3, 26, -35.1, 17.65, 0.52915, 27, -108.56, 14.53, 0.00036, 25, 37.84, 17.54, 0.47049, 3, 26, 11.2, 17.67, 0.68154, 27, -62.27, 15.88, 0.03381, 25, 84.14, 17.71, 0.28465, 3, 26, 32.61, 17.15, 0.73914, 27, -40.86, 15.98, 0.12744, 25, 105.55, 17.26, 0.13342, 4, 26, 54.56, 16.62, 0.63595, 27, -18.91, 16.08, 0.31815, 28, -91.71, 17.14, 0.00018, 25, 127.5, 16.79, 0.04572, 4, 26, 78.21, 17.63, 0.4287, 27, 4.71, 17.77, 0.54978, 28, -68.08, 18.56, 0.01862, 25, 151.15, 17.89, 0.00289, 3, 26, 101.8, 18.64, 0.20113, 27, 28.25, 19.46, 0.70649, 28, -44.52, 19.98, 0.09237, 3, 26, 119.12, 19.53, 0.06365, 27, 45.54, 20.85, 0.67843, 28, -27.21, 21.17, 0.25793, 3, 26, 144.46, 20.83, 0.00895, 27, 70.83, 22.89, 0.493, 28, -1.9, 22.91, 0.49806, 2, 27, 105.38, 32.32, 0.26143, 28, 32.76, 31.95, 0.73857, 2, 27, 156.1, 33.05, 0.1164, 28, 83.48, 32.09, 0.8836, 2, 27, 157.06, -33.94, 0.12346, 28, 83.67, -34.91, 0.87654, 3, 26, 175.13, -37.68, 0.00011, 27, 103.18, -34.72, 0.28211, 28, 29.78, -35.06, 0.71778], "hull": 23, "edges": [40, 42, 42, 44, 44, 0, 16, 18, 38, 40, 38, 36, 24, 22, 18, 20, 22, 20, 12, 14, 14, 16, 4, 6, 6, 8, 0, 2, 2, 4, 32, 34, 34, 36, 28, 30, 30, 32, 24, 26, 26, 28, 8, 10, 10, 12], "width": 302, "height": 67}}, "TentacleRight2": {"images/Beholder3_Tentacle2": {"type": "mesh", "uvs": [0.26247, 0.07086, 0.33535, 0.09414, 0.42063, 0.10224, 0.51225, 0.10946, 0.59626, 0.11774, 0.68021, 0.12268, 0.7783, 0.1357, 0.88005, 0.14148, 0.95492, 0.13764, 1, 0.13647, 1, 0.40907, 1, 0.46294, 1, 0.72285, 0.9383, 0.76069, 0.84856, 0.78341, 0.75262, 0.81371, 0.65281, 0.8412, 0.56174, 0.86075, 0.48407, 0.91532, 0.40315, 1, 0.27541, 1, 0.23208, 0.83006, 0.27655, 0.57886, 0.35005, 0.51656, 0.43796, 0.51505, 0.51592, 0.53128, 0.60715, 0.52386, 0.69066, 0.50846, 0.78745, 0.49469, 0.88459, 0.47374, 0.96543, 0.46234, 0.96814, 0.41468, 0.8846, 0.42939, 0.78425, 0.43011, 0.6915, 0.42977, 0.60402, 0.43875, 0.51141, 0.44509, 0.43287, 0.45617, 0.34946, 0.47799, 0.27146, 0.51583, 0.17781, 0.59614, 0.08987, 0.64545, 0, 0.47782, 0, 0.1429, 0.05508, 0, 0.17436, 0], "triangles": [13, 29, 30, 10, 31, 9, 31, 8, 9, 30, 11, 12, 30, 31, 11, 31, 10, 11, 13, 30, 12, 32, 8, 31, 34, 6, 33, 15, 28, 14, 33, 6, 7, 14, 29, 13, 14, 28, 29, 33, 7, 32, 32, 7, 8, 17, 26, 16, 16, 27, 15, 16, 26, 27, 15, 27, 28, 36, 4, 35, 35, 5, 34, 35, 4, 5, 34, 5, 6, 41, 42, 40, 40, 44, 45, 43, 44, 40, 40, 0, 39, 40, 45, 0, 39, 0, 1, 40, 42, 43, 20, 21, 22, 22, 23, 19, 39, 1, 38, 38, 2, 37, 36, 37, 3, 3, 37, 2, 36, 3, 4, 19, 24, 18, 18, 25, 17, 18, 24, 25, 17, 25, 26, 19, 23, 24, 38, 1, 2, 20, 22, 19], "vertices": [2, 19, 80.79, -52.03, 0.33655, 20, 6.52, -52.14, 0.66345, 2, 19, 58.48, -47.96, 0.67093, 20, -15.73, -47.71, 0.32907, 3, 19, 32.23, -45.43, 0.78022, 20, -41.93, -44.75, 0.10832, 18, 106.2, -45.04, 0.11146, 2, 19, 4.02, -42.89, 0.66667, 18, 77.96, -42.83, 0.33333, 3, 19, -21.84, -40.36, 0.33333, 18, 52.08, -40.62, 0.55556, 17, 124.5, -41.88, 0.11111, 3, 19, -47.69, -38.23, 0.11111, 18, 26.19, -38.8, 0.55556, 17, 98.67, -39.42, 0.33333, 2, 18, -4.01, -35.82, 0.375, 17, 68.55, -35.71, 0.625, 2, 18, -35.38, -33.63, 0.16667, 17, 37.24, -32.76, 0.83333, 1, 17, 14.13, -31.53, 1, 1, 17, 0.23, -30.66, 1, 1, 17, 2.54, 1.15, 1, 1, 17, 2.99, 7.44, 1, 1, 17, 5.19, 37.77, 1, 2, 18, -49.88, 39.6, 0.16667, 17, 24.53, 40.8, 0.83333, 2, 18, -22.05, 40.92, 0.375, 17, 52.38, 41.44, 0.625, 3, 19, -65.17, 43.82, 0.11111, 18, 7.73, 43.04, 0.55556, 17, 82.2, 42.84, 0.33333, 3, 19, -34.2, 45.18, 0.33333, 18, 38.69, 44.77, 0.55556, 17, 113.2, 43.81, 0.11111, 3, 19, -5.97, 45.77, 0.64533, 20, -78.63, 47.07, 0.032, 18, 66.91, 45.7, 0.32267, 3, 19, 18.37, 50.71, 0.847, 20, -54.22, 51.6, 0.032, 18, 91.19, 50.92, 0.121, 1, 19, 43.92, 59.1, 1, 2, 19, 83.33, 56.73, 0.70133, 20, 10.83, 56.56, 0.29867, 2, 19, 95.5, 36.08, 0.59022, 20, 22.66, 35.71, 0.40978, 2, 19, 80.02, 7.56, 0.70133, 20, 6.71, 7.45, 0.29867, 1, 19, 56.91, 1.65, 1, 3, 19, 29.78, 3.11, 0.847, 20, -43.59, 3.82, 0.032, 18, 103.17, 3.46, 0.121, 3, 19, 5.85, 6.45, 0.64533, 20, -67.46, 7.55, 0.032, 18, 79.2, 6.52, 0.32267, 3, 19, -22.34, 7.27, 0.33333, 18, 51, 7, 0.55556, 17, 124.58, 5.76, 0.11111, 3, 19, -48.21, 7.02, 0.11111, 18, 25.14, 6.44, 0.55556, 17, 98.71, 5.83, 0.33333, 2, 18, -4.81, 6.27, 0.375, 17, 68.77, 6.39, 0.625, 2, 18, -34.91, 5.27, 0.17391, 17, 38.65, 6.11, 0.82609, 1, 17, 13.64, 6.59, 1, 1, 17, 12.4, 1.09, 1, 2, 18, -35.16, 0.08, 0.17391, 17, 38.27, 0.94, 0.82609, 2, 18, -4.19, -1.32, 0.375, 17, 69.21, -1.22, 0.625, 3, 19, -49.02, -2.15, 0.11111, 18, 24.44, -2.74, 0.55556, 17, 97.79, -3.34, 0.33333, 3, 19, -21.97, -2.73, 0.33333, 18, 51.49, -2.99, 0.55556, 17, 124.82, -4.24, 0.11111, 2, 19, 6.64, -3.7, 0.66667, 18, 80.11, -3.62, 0.33333, 3, 19, 30.94, -3.87, 0.7779, 20, -42.54, -3.17, 0.11097, 18, 104.41, -3.5, 0.11113, 2, 19, 56.82, -2.86, 0.66688, 20, -16.65, -2.59, 0.33312, 2, 19, 81.14, 0.11, 0.3335, 20, 7.72, -0.02, 0.6665, 2, 19, 110.6, 7.75, 0.11115, 20, 37.29, 7.14, 0.88885, 1, 20, 64.82, 10.82, 1, 1, 20, 91.02, -10.86, 1, 1, 20, 88.03, -49.93, 1, 1, 20, 69.78, -65.3, 1, 2, 19, 107.47, -61.94, 0.11184, 20, 33.03, -62.48, 0.88816], "hull": 46, "edges": [90, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 38, 36, 36, 34, 34, 32, 32, 30, 30, 28, 28, 26, 26, 24, 84, 82, 84, 86, 88, 90, 86, 88, 82, 80, 80, 78, 78, 76, 76, 74, 74, 72, 72, 70, 70, 68, 68, 66, 66, 64, 18, 20, 38, 40, 42, 40, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 20, 22, 22, 24, 60, 22, 20, 62, 62, 64, 62, 60], "width": 309, "height": 117}}, "TentacleRight3": {"images/Beholder3_Tentacle2": {"type": "mesh", "uvs": [0.26247, 0.07086, 0.33535, 0.09414, 0.42063, 0.10224, 0.51225, 0.10946, 0.59626, 0.11774, 0.68021, 0.12268, 0.7783, 0.1357, 0.88005, 0.14148, 0.95492, 0.13764, 1, 0.13647, 1, 0.40907, 1, 0.46294, 1, 0.72285, 0.9383, 0.76069, 0.84856, 0.78341, 0.75262, 0.81371, 0.65281, 0.8412, 0.56174, 0.86075, 0.48407, 0.91532, 0.40315, 1, 0.27541, 1, 0.23208, 0.83006, 0.27655, 0.57886, 0.35005, 0.51656, 0.43796, 0.51505, 0.51592, 0.53128, 0.60715, 0.52386, 0.69066, 0.50846, 0.78745, 0.49469, 0.88459, 0.47374, 0.96543, 0.46234, 0.96814, 0.41468, 0.8846, 0.42939, 0.78425, 0.43011, 0.6915, 0.42977, 0.60402, 0.43875, 0.51141, 0.44509, 0.43287, 0.45617, 0.34946, 0.47799, 0.27146, 0.51583, 0.17781, 0.59614, 0.08987, 0.64545, 0, 0.47782, 0, 0.1429, 0.05508, 0, 0.17436, 0], "triangles": [13, 29, 30, 10, 31, 9, 31, 8, 9, 30, 11, 12, 30, 31, 11, 31, 10, 11, 13, 30, 12, 32, 8, 31, 34, 6, 33, 15, 28, 14, 33, 6, 7, 14, 29, 13, 14, 28, 29, 33, 7, 32, 32, 7, 8, 17, 26, 16, 16, 27, 15, 16, 26, 27, 15, 27, 28, 36, 4, 35, 35, 5, 34, 35, 4, 5, 34, 5, 6, 41, 42, 40, 40, 44, 45, 43, 44, 40, 40, 0, 39, 40, 45, 0, 39, 0, 1, 40, 42, 43, 20, 21, 22, 22, 23, 19, 39, 1, 38, 38, 2, 37, 36, 37, 3, 3, 37, 2, 36, 3, 4, 19, 24, 18, 18, 25, 17, 18, 24, 25, 17, 25, 26, 19, 23, 24, 38, 1, 2, 20, 22, 19], "vertices": [2, 31, 80.79, -52.03, 0.33655, 32, 6.52, -52.14, 0.66345, 2, 31, 58.48, -47.96, 0.67093, 32, -15.73, -47.71, 0.32907, 3, 31, 32.23, -45.43, 0.78022, 32, -41.93, -44.75, 0.10832, 30, 106.2, -45.04, 0.11146, 2, 31, 4.02, -42.89, 0.66667, 30, 77.96, -42.83, 0.33333, 3, 31, -21.84, -40.36, 0.33333, 30, 52.08, -40.62, 0.55556, 29, 124.5, -41.88, 0.11111, 3, 31, -47.69, -38.23, 0.11111, 30, 26.19, -38.8, 0.55556, 29, 98.67, -39.42, 0.33333, 2, 30, -4.01, -35.82, 0.375, 29, 68.55, -35.71, 0.625, 2, 30, -35.38, -33.63, 0.16667, 29, 37.24, -32.76, 0.83333, 1, 29, 14.13, -31.53, 1, 1, 29, 0.23, -30.66, 1, 1, 29, 2.54, 1.15, 1, 1, 29, 2.99, 7.44, 1, 1, 29, 5.19, 37.77, 1, 2, 30, -49.88, 39.6, 0.16667, 29, 24.53, 40.8, 0.83333, 2, 30, -22.05, 40.92, 0.375, 29, 52.38, 41.44, 0.625, 3, 31, -65.17, 43.82, 0.11111, 30, 7.73, 43.04, 0.55556, 29, 82.2, 42.84, 0.33333, 3, 31, -34.2, 45.18, 0.33333, 30, 38.69, 44.77, 0.55556, 29, 113.2, 43.81, 0.11111, 3, 31, -5.97, 45.77, 0.64533, 32, -78.63, 47.07, 0.032, 30, 66.91, 45.7, 0.32267, 3, 31, 18.37, 50.71, 0.847, 32, -54.22, 51.6, 0.032, 30, 91.19, 50.92, 0.121, 1, 31, 43.92, 59.1, 1, 2, 31, 83.33, 56.73, 0.70133, 32, 10.83, 56.56, 0.29867, 2, 31, 95.5, 36.08, 0.59022, 32, 22.66, 35.71, 0.40978, 2, 31, 80.02, 7.56, 0.70133, 32, 6.71, 7.45, 0.29867, 1, 31, 56.91, 1.65, 1, 3, 31, 29.78, 3.11, 0.847, 32, -43.59, 3.82, 0.032, 30, 103.17, 3.46, 0.121, 3, 31, 5.85, 6.45, 0.64533, 32, -67.46, 7.55, 0.032, 30, 79.2, 6.52, 0.32267, 3, 31, -22.34, 7.27, 0.33333, 30, 51, 7, 0.55556, 29, 124.58, 5.76, 0.11111, 3, 31, -48.21, 7.02, 0.11111, 30, 25.14, 6.44, 0.55556, 29, 98.71, 5.83, 0.33333, 2, 30, -4.81, 6.27, 0.375, 29, 68.77, 6.39, 0.625, 2, 30, -34.91, 5.27, 0.17391, 29, 38.65, 6.11, 0.82609, 1, 29, 13.64, 6.59, 1, 1, 29, 12.4, 1.09, 1, 2, 30, -35.16, 0.08, 0.17391, 29, 38.27, 0.94, 0.82609, 2, 30, -4.19, -1.32, 0.375, 29, 69.21, -1.22, 0.625, 3, 31, -49.02, -2.15, 0.11111, 30, 24.44, -2.74, 0.55556, 29, 97.79, -3.34, 0.33333, 3, 31, -21.97, -2.73, 0.33333, 30, 51.49, -2.99, 0.55556, 29, 124.82, -4.24, 0.11111, 2, 31, 6.64, -3.7, 0.66667, 30, 80.11, -3.62, 0.33333, 3, 31, 30.94, -3.87, 0.7779, 32, -42.54, -3.17, 0.11097, 30, 104.41, -3.5, 0.11113, 2, 31, 56.82, -2.86, 0.66688, 32, -16.65, -2.59, 0.33312, 2, 31, 81.14, 0.11, 0.3335, 32, 7.72, -0.02, 0.6665, 2, 31, 110.6, 7.75, 0.11115, 32, 37.29, 7.14, 0.88885, 1, 32, 64.82, 10.82, 1, 1, 32, 91.02, -10.86, 1, 1, 32, 88.03, -49.93, 1, 1, 32, 69.78, -65.3, 1, 2, 31, 107.47, -61.94, 0.11184, 32, 33.03, -62.48, 0.88816], "hull": 46, "edges": [90, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 38, 36, 36, 34, 34, 32, 32, 30, 30, 28, 28, 26, 26, 24, 84, 82, 84, 86, 88, 90, 86, 88, 82, 80, 80, 78, 78, 76, 76, 74, 74, 72, 72, 70, 70, 68, 68, 66, 66, 64, 18, 20, 38, 40, 42, 40, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 20, 22, 22, 24, 60, 22, 20, 62, 62, 64, 62, 60], "width": 309, "height": 117}}}}, {"name": "Dungeon3_Beaten", "bones": ["HornBtmLeft3c2", "HornBtmLeft3d", "HornBtmLeft3d2", "TentacleLeftBtm2", "TentacleLeftBtm", "HornBtmLeft3b", "HornBtmLeft3c", "HornBtmLeft2c", "HornBtmLeft2c2", "HornBtmLeft2b", "HornBtmLeft2b2", "HornBtmLeft3b2", "TentacleLeftTop", "HornBtmLeft2d", "TentacleLeftTop2", "HornBtmLeft2d2"], "attachments": {"Body": {"images/Beholder1_Body": {"name": "images/Beholder3_Body", "type": "<PERSON><PERSON><PERSON>", "skin": "Dungeon3", "parent": "images/Beholder1_Body", "width": 430, "height": 384}}, "Drips_Back": {"Drips_Back": {"name": "images/Beholder3_Drips", "type": "<PERSON><PERSON><PERSON>", "skin": "Dungeon3", "parent": "Drips_Back", "width": 279, "height": 213}}, "Horns": {"images/Beholder1_Horns": {"name": "images/Beholder3_Horns", "type": "<PERSON><PERSON><PERSON>", "skin": "Dungeon3", "parent": "images/Beholder1_Horns", "width": 466, "height": 409}}, "images/Bandage": {"Bandage": {"name": "images/Bandage", "type": "mesh", "uvs": [0.781, 0.10925, 0.87301, 0.19826, 0.9608, 0.28687, 1, 0.46644, 1, 0.82556, 0.96253, 1, 0.90518, 1, 0.79579, 0.73961, 0.69312, 0.67864, 0.56508, 0.63415, 0.40816, 0.646, 0.27702, 0.68871, 0.16121, 0.81037, 0.04916, 0.95626, 0.01871, 0.86514, 1e-05, 0.57047, 0.02225, 0.31173, 0.09936, 0.19963, 0.20272, 0.09013, 0.34377, 0.01226, 0.4818, 0, 0.6353, 0.01559], "triangles": [10, 11, 19, 7, 8, 0, 0, 8, 21, 11, 18, 19, 10, 20, 9, 10, 19, 20, 8, 9, 21, 9, 20, 21, 1, 7, 0, 5, 6, 4, 6, 7, 4, 13, 14, 12, 14, 15, 12, 7, 3, 4, 12, 15, 17, 15, 16, 17, 12, 17, 11, 7, 2, 3, 7, 1, 2, 17, 18, 11], "vertices": [2, 1, 155.87, -124.38, 0.46943, 8, 99.87, -123.55, 0.53057, 2, 1, 135.71, -164.56, 0.744, 8, 79.71, -163.72, 0.256, 1, 1, 118.37, -203.29, 1, 1, 1, 81.12, -220.06, 1, 1, 1, 7.15, -218.95, 1, 1, 1, -28.53, -201.87, 1, 2, 1, -28.15, -176.54, 0.832, 8, -84.15, -175.7, 0.168, 2, 1, 9.52, -126.43, 0.392, 8, -46.47, -125.59, 0.608, 2, 1, 23.18, -81.34, 0.184, 8, -32.81, -80.5, 0.816, 1, 8, -22.95, -24.06, 1, 2, 1, 31.64, 44.45, 0.072, 8, -24.36, 45.28, 0.928, 2, 1, 24.12, 102.44, 0.28, 8, -31.87, 103.27, 0.72, 2, 1, 7.97, 152.7, 0.58466, 8, -48.02, 153.53, 0.41534, 1, 1, -13.63, 201.44, 1, 1, 1, 6.6, 214.41, 1, 1, 1, 66.67, 221.88, 1, 1, 1, 119.46, 211.32, 1, 2, 1, 142.46, 176.85, 0.82706, 8, 86.47, 177.68, 0.17294, 2, 1, 164.14, 130.89, 0.488, 8, 108.14, 131.73, 0.512, 2, 1, 179.15, 68.37, 0.152, 8, 123.16, 69.2, 0.848, 2, 1, 180.64, 7.38, 0.056, 8, 124.64, 8.22, 0.944, 2, 1, 176.81, -60.43, 0.112, 8, 120.81, -59.59, 0.888], "hull": 22, "edges": [36, 38, 30, 32, 22, 20, 16, 14, 14, 12, 10, 12, 2, 4, 4, 6, 38, 40, 40, 42, 20, 18, 18, 16, 6, 8, 10, 8, 2, 0, 0, 42, 32, 34, 34, 36, 22, 24, 24, 26, 26, 28, 28, 30], "width": 473, "height": 206}}, "TentacleLeft1": {"images/Beholder3_Tentacle1": {"type": "<PERSON><PERSON><PERSON>", "skin": "Dungeon3", "parent": "images/Beholder3_Tentacle1", "width": 302, "height": 67}}, "TentacleLeft2": {"images/Beholder3_Tentacle1": {"type": "<PERSON><PERSON><PERSON>", "skin": "Dungeon3", "parent": "images/Beholder3_Tentacle1", "width": 302, "height": 67}}, "TentacleRight2": {"images/Beholder3_Tentacle2": {"type": "<PERSON><PERSON><PERSON>", "skin": "Dungeon3", "parent": "images/Beholder3_Tentacle2", "width": 309, "height": 117}}, "TentacleRight3": {"images/Beholder3_Tentacle2": {"type": "<PERSON><PERSON><PERSON>", "skin": "Dungeon3", "parent": "images/Beholder3_Tentacle2", "width": 309, "height": 117}}}}, {"name": "Dungeon4", "bones": ["TentacleLeftBtm", "TentacleLeftBtm2", "TentacleLeftTop", "TentacleLeftTop2"], "attachments": {"Body": {"images/Beholder1_Body": {"name": "images/Beholder4_Body", "type": "mesh", "uvs": [0.79902, 0.08222, 0.91916, 0.24746, 1, 0.34537, 1, 0.52066, 0.97598, 0.61726, 1, 0.69752, 0.90467, 0.84801, 0.78607, 0.87176, 0.70601, 0.95304, 0.63863, 1, 0.51786, 1, 0.48379, 1, 0.37414, 1, 0.30497, 0.96314, 0.23074, 0.87665, 0.15652, 0.86833, 0.0941, 0.75356, 0.04686, 0.75689, 0.03506, 0.67206, 0, 0.5024, 0, 0.34212, 0.12274, 0.1704, 0.27023, 0.04199, 0.42401, 0, 0.60602, 0, 0.39438, 0.85669, 0.45343, 0.89495, 0.49729, 0.87998, 0.43825, 0.81511, 0.4956, 0.83174, 0.46186, 0.74026, 0.49729, 0.75856, 0.48042, 0.65044, 0.50067, 0.58058, 0.52428, 0.6521, 0.50717, 0.75945, 0.5423, 0.73204, 0.50769, 0.82769, 0.55908, 0.82046, 0.51, 0.88159, 0.57765, 0.88159, 0.61487, 0.86143, 0.17431, 0.80649, 0.27787, 0.80494, 0.41752, 0.70902, 0.50225, 0.49088, 0.59169, 0.70748, 0.7282, 0.82506, 0.85059, 0.79876, 0.94788, 0.67344, 0.95572, 0.42591, 0.90551, 0.26965, 0.77842, 0.14279, 0.58542, 0.07162, 0.44106, 0.0577, 0.0629, 0.61619, 0.04721, 0.35628, 0.10841, 0.37949, 0.20569, 0.18146, 0.32495, 0.08864], "triangles": [6, 49, 5, 6, 48, 49, 48, 46, 49, 49, 4, 5, 4, 49, 50, 45, 52, 51, 50, 51, 2, 51, 1, 2, 51, 52, 1, 45, 53, 52, 10, 41, 9, 9, 41, 8, 10, 40, 41, 40, 10, 39, 10, 11, 39, 11, 25, 26, 11, 27, 39, 11, 26, 27, 11, 12, 25, 12, 13, 25, 13, 14, 25, 25, 14, 43, 8, 47, 7, 8, 41, 47, 25, 28, 26, 26, 28, 27, 39, 38, 40, 40, 38, 41, 39, 29, 37, 39, 37, 38, 28, 29, 27, 39, 27, 29, 15, 42, 14, 14, 42, 43, 15, 16, 42, 47, 41, 46, 25, 43, 28, 37, 31, 35, 37, 29, 31, 31, 29, 30, 38, 37, 36, 48, 47, 46, 37, 35, 36, 46, 41, 38, 38, 36, 46, 43, 44, 28, 29, 28, 30, 28, 44, 30, 43, 42, 55, 44, 43, 55, 34, 36, 35, 31, 34, 35, 17, 18, 16, 18, 55, 16, 55, 42, 16, 32, 34, 31, 32, 31, 30, 30, 44, 32, 36, 34, 46, 32, 44, 33, 18, 19, 55, 32, 33, 34, 44, 45, 33, 33, 46, 34, 46, 33, 45, 44, 55, 57, 57, 55, 56, 45, 44, 57, 55, 19, 56, 19, 20, 56, 57, 58, 45, 58, 59, 45, 59, 54, 45, 45, 54, 53, 58, 57, 21, 57, 56, 21, 56, 20, 21, 58, 22, 59, 58, 21, 22, 52, 53, 0, 59, 23, 54, 59, 22, 23, 53, 24, 0, 53, 54, 24, 54, 23, 24, 50, 2, 3, 52, 0, 1, 49, 46, 50, 50, 46, 45, 4, 50, 3, 45, 51, 50, 7, 48, 6, 7, 47, 48], "vertices": [1, 1, 259.55, -157.26, 1, 1, 1, 176.22, -215.13, 1, 1, 1, 126.77, -254.17, 1, 1, 1, 39.32, -252.86, 1, 1, 1, -8.71, -240.33, 1, 1, 1, -48.93, -251.55, 1, 1, 1, -123.32, -203.53, 1, 1, 1, -134.29, -145.01, 1, 1, 1, -174.26, -105.02, 1, 1, 1, -197.2, -71.52, 1, 1, 1, -196.31, -12.11, 1, 1, 1, -196.06, 4.65, 1, 1, 1, -195.26, 58.59, 1, 1, 1, -176.36, 92.34, 1, 1, 1, -132.66, 128.22, 1, 1, 1, -127.96, 164.67, 1, 1, 1, -70.24, 194.52, 1, 1, 1, -71.56, 217.79, 1, 1, 1, -29.14, 222.96, 1, 1, 1, 55.76, 238.95, 1, 1, 1, 135.74, 237.75, 1, 1, 1, 220.52, 176.09, 1, 1, 1, 283.5, 102.58, 1, 2, 1, 303.32, 26.62, 0.99249, 8, 247.33, 27.45, 0.00751, 1, 1, 301.99, -62.92, 1, 1, 1, -123.9, 47.57, 1, 1, 1, -143.42, 18.81, 1, 1, 1, -136.28, -2.88, 1, 1, 1, -103.48, 25.68, 1, 1, 1, -112.2, -2.41, 1, 1, 1, -66.3, 13.5, 1, 1, 1, -75.69, -3.79, 1, 2, 1, -21.62, 3.71, 0.69476, 8, -77.62, 4.54, 0.30524, 2, 1, 13.08, -6.77, 0.65428, 8, -42.91, -5.94, 0.34572, 2, 1, -22.78, -17.86, 0.56638, 8, -78.77, -17.02, 0.43362, 2, 1, -76.21, -8.64, 0.91399, 8, -132.2, -7.81, 0.08601, 1, 1, -62.79, -26.13, 1, 1, 1, -110.26, -8.39, 1, 1, 1, -107.03, -33.73, 1, 1, 1, -137.17, -9.12, 1, 1, 1, -137.67, -42.4, 1, 1, 1, -127.88, -60.87, 1, 1, 1, -97.24, 155.46, 1, 1, 1, -97.23, 104.5, 1, 2, 1, -50.39, 35.09, 0.88387, 8, -106.39, 35.92, 0.11613, 1, 8, 1.83, -7.39, 1, 2, 1, -50.9, -50.61, 0.888, 8, -106.89, -49.77, 0.112, 1, 1, -110.57, -116.89, 1, 1, 1, -98.34, -177.29, 1, 1, 1, -36.53, -226.08, 1, 1, 1, 86.92, -231.79, 1, 1, 1, 165.25, -208.25, 1, 1, 1, 229.48, -146.67, 1, 1, 1, 266.4, -52.25, 1, 2, 1, 274.41, 18.66, 0.98981, 8, 218.42, 19.49, 0.01019, 1, 1, -1.47, 208.85, 1, 1, 1, 128.32, 214.63, 1, 1, 1, 116.3, 184.7, 1, 1, 1, 214.39, 135.37, 1, 1, 1, 259.83, 76.01, 1], "hull": 25, "edges": [38, 36, 36, 34, 34, 32, 32, 30, 30, 28, 28, 26, 26, 24, 22, 24, 50, 22, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 20, 22, 82, 20, 18, 20, 18, 16, 16, 14, 14, 12, 12, 10, 2, 4, 4, 6, 10, 8, 8, 6, 2, 0, 0, 48, 38, 40, 40, 42, 42, 44, 46, 48, 44, 46, 28, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 84, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 108], "width": 492, "height": 499}}, "Drips_Back": {"Drips_Back": {"name": "images/Beholder4_Drips", "type": "mesh", "uvs": [0.70428, 0.20337, 1, 0.37154, 1, 0.43901, 0.95429, 0.52784, 0.95791, 0.68582, 0.96245, 0.7732, 0.89762, 0.77403, 0.88942, 0.68412, 0.87849, 0.57068, 0.86659, 0.69168, 0.87112, 0.84713, 0.87293, 0.92192, 0.80261, 0.91854, 0.79532, 0.83955, 0.78896, 0.70175, 0.78091, 0.62862, 0.76731, 0.69953, 0.77271, 0.83828, 0.77767, 0.94287, 0.78083, 0.97953, 0.70611, 0.97869, 0.70114, 0.94202, 0.69664, 0.8341, 0.68444, 0.74076, 0.67131, 0.71326, 0.655, 0.73784, 0.65198, 0.81153, 0.6601, 0.92153, 0.58403, 0.92027, 0.58033, 0.809, 0.56359, 0.72775, 0.54501, 0.80483, 0.55428, 0.92733, 0.55381, 1, 0.48453, 1, 0.48547, 0.92152, 0.48052, 0.79444, 0.47148, 0.70735, 0.44794, 0.67694, 0.42982, 0.70943, 0.43614, 0.7911, 0.44743, 0.9186, 0.38675, 0.91234, 0.38769, 0.79025, 0.37728, 0.754, 0.36142, 0.78608, 0.36186, 0.86817, 0.29071, 0.86815, 0.28348, 0.77648, 0.27174, 0.66856, 0.25136, 0.67189, 0.25043, 0.77106, 0.2581, 0.8594, 0.26216, 0.93273, 0.18971, 0.93063, 0.19109, 0.85396, 0.19167, 0.76904, 0.18441, 0.71316, 0.18439, 0.76806, 0.18259, 0.8541, 0.10957, 0.84917, 0.10781, 0.76641, 0.11139, 0.67791, 0.09451, 0.58755, 0.0945, 0.65505, 0.09936, 0.75405, 0.0309, 0.75516, 0.02725, 0.65391, 0.02605, 0.56054, 0, 0.52396, 0, 0.46281, 0.24211, 0.37386, 0.32006, 0.22554, 0.4464, 0, 0.52163, 0], "triangles": [28, 29, 26, 47, 48, 45, 47, 45, 46, 12, 13, 10, 12, 10, 11, 28, 26, 27, 42, 40, 41, 66, 64, 65, 66, 67, 64, 33, 34, 32, 34, 35, 32, 20, 18, 19, 20, 21, 18, 54, 52, 53, 54, 55, 52, 21, 17, 18, 21, 22, 17, 31, 32, 35, 35, 36, 31, 43, 40, 42, 55, 51, 52, 59, 60, 58, 55, 56, 51, 60, 61, 58, 13, 9, 10, 9, 13, 14, 16, 17, 23, 17, 22, 23, 26, 29, 25, 29, 30, 25, 31, 36, 30, 36, 37, 30, 40, 44, 39, 40, 43, 44, 45, 48, 44, 48, 49, 44, 6, 4, 5, 6, 7, 4, 50, 51, 57, 51, 56, 57, 58, 61, 57, 57, 61, 62, 44, 49, 39, 23, 24, 16, 25, 30, 24, 24, 30, 38, 16, 24, 15, 57, 62, 50, 39, 49, 38, 38, 15, 24, 9, 14, 15, 37, 38, 30, 4, 8, 3, 9, 15, 8, 4, 7, 8, 50, 62, 63, 0, 38, 71, 38, 0, 15, 0, 71, 72, 49, 50, 63, 63, 71, 49, 38, 49, 71, 0, 72, 74, 64, 67, 63, 67, 68, 63, 15, 0, 8, 71, 63, 70, 2, 3, 8, 70, 63, 68, 1, 2, 8, 68, 69, 70, 1, 8, 0, 72, 73, 74], "vertices": [1, 1, -21.97, -61.25, 1, 1, 1, -71.56, -138.89, 1, 1, 1, -90.98, -138.61, 1, 2, 1, -116.39, -126.12, 0.872, 12, 129.84, 125.32, 0.128, 2, 1, -161.89, -126.41, 0.744, 12, 130.81, 79.82, 0.256, 2, 1, -187.08, -127.24, 0.744, 12, 132.02, 54.65, 0.256, 2, 1, -187.06, -110.06, 0.744, 12, 114.84, 54.41, 0.256, 2, 1, -161.14, -108.27, 0.744, 12, 112.66, 80.31, 0.256, 2, 1, -128.43, -105.85, 0.824, 12, 109.76, 112.98, 0.176, 2, 1, -163.22, -102.19, 0.696, 16, 72.61, 78.13, 0.304, 2, 1, -208.01, -102.73, 0.52, 16, 73.82, 33.36, 0.48, 2, 1, -229.55, -102.89, 0.216, 16, 74.3, 11.82, 0.784, 2, 1, -228.3, -84.27, 0.216, 16, 55.67, 12.79, 0.784, 2, 1, -205.53, -82.68, 0.424, 16, 53.73, 35.54, 0.576, 2, 1, -165.82, -81.57, 0.696, 16, 52.04, 75.22, 0.304, 2, 1, -144.73, -79.75, 0.744, 12, 83.9, 96.28, 0.256, 2, 1, -165.1, -75.85, 0.744, 12, 80.3, 75.86, 0.256, 2, 1, -205.08, -76.69, 0.648, 12, 81.74, 35.9, 0.352, 2, 1, -235.21, -77.56, 0.52, 12, 83.06, 5.78, 0.48, 2, 1, -245.78, -78.24, 0.344, 12, 83.9, -4.78, 0.656, 2, 1, -245.25, -58.45, 0.216, 12, 64.1, -4.54, 0.784, 2, 1, -234.67, -57.29, 0.44, 12, 62.78, 6.02, 0.56, 2, 1, -203.58, -56.55, 0.648, 12, 61.58, 37.1, 0.352, 2, 1, -176.65, -53.71, 0.744, 12, 58.34, 63.98, 0.256, 2, 1, -168.68, -50.35, 0.744, 16, 20.86, 71.9, 0.256, 2, 1, -175.69, -45.93, 0.712, 16, 16.54, 64.82, 0.288, 2, 1, -196.9, -44.81, 0.424, 16, 15.75, 43.6, 0.576, 1, 16, 17.91, 11.92, 1, 1, 16, -2.25, 12.28, 1, 2, 1, -195.9, -25.84, 0.44, 16, -3.24, 44.32, 0.56, 3, 1, -172.43, -21.75, 0.71635, 12, 26.32, 67.72, 0.01165, 16, -7.68, 67.72, 0.272, 2, 1, -194.56, -16.5, 0.744, 12, 21.4, 45.52, 0.256, 2, 1, -229.87, -18.43, 0.536, 12, 23.86, 10.24, 0.464, 2, 1, -250.79, -18, 0.312, 12, 23.74, -10.69, 0.688, 2, 1, -250.52, 0.36, 0.344, 12, 5.38, -10.69, 0.656, 2, 1, -227.93, -0.23, 0.504, 12, 5.63, 11.91, 0.496, 2, 1, -191.31, 0.55, 0.744, 12, 4.31, 48.51, 0.256, 2, 1, -166.2, 2.57, 0.744, 12, 1.91, 73.59, 0.256, 2, 1, -157.35, 8.68, 0.776, 12, -4.33, 82.35, 0.224, 2, 1, -166.63, 13.62, 0.792, 16, -43.13, 72.99, 0.208, 2, 1, -190.18, 12.29, 0.696, 16, -41.45, 49.47, 0.304, 2, 1, -226.94, 9.84, 0.216, 16, -38.45, 12.75, 0.784, 2, 1, -224.9, 25.89, 0.216, 16, -54.53, 14.55, 0.784, 2, 1, -189.75, 25.13, 0.696, 16, -54.29, 49.71, 0.304, 2, 1, -179.26, 27.73, 0.696, 16, -57.05, 60.15, 0.304, 2, 1, -188.44, 32.07, 0.392, 16, -61.25, 50.91, 0.608, 2, 1, -212.08, 32.3, 0.104, 16, -61.13, 27.27, 0.896, 2, 1, -211.8, 51.15, 0.088, 16, -79.99, 27.27, 0.912, 2, 1, -185.37, 52.68, 0.424, 16, -81.91, 53.67, 0.576, 2, 1, -154.25, 55.34, 0.776, 16, -85.03, 84.75, 0.224, 2, 1, -155.13, 60.75, 0.76, 12, -56.43, 83.79, 0.24, 2, 1, -183.68, 61.42, 0.744, 12, -56.67, 55.23, 0.256, 2, 1, -209.15, 59.76, 0.536, 12, -54.63, 29.79, 0.464, 2, 1, -230.28, 58.99, 0.264, 12, -53.55, 8.67, 0.736, 2, 1, -229.4, 78.18, 0.264, 12, -72.75, 9.27, 0.736, 2, 1, -207.33, 77.49, 0.552, 12, -72.39, 31.35, 0.448, 2, 1, -182.87, 76.98, 0.744, 12, -72.24, 55.81, 0.256, 2, 1, -166.75, 78.66, 0.744, 12, -74.17, 71.9, 0.256, 2, 1, -182.56, 78.9, 0.744, 12, -74.17, 56.09, 0.256, 2, 1, -207.33, 79.74, 0.504, 12, -74.64, 31.31, 0.496, 2, 1, -205.63, 99.07, 0.504, 12, -93.99, 32.73, 0.496, 2, 1, -181.79, 99.19, 0.744, 12, -94.46, 56.56, 0.256, 2, 1, -156.32, 97.86, 0.744, 12, -93.52, 82.05, 0.256, 2, 1, -130.23, 101.95, 0.6, 16, -132, 108.07, 0.4, 2, 1, -149.67, 102.24, 0.488, 16, -132, 88.63, 0.512, 2, 1, -178.2, 101.37, 0.216, 16, -130.7, 60.12, 0.784, 2, 1, -178.25, 119.52, 0.216, 16, -148.84, 59.8, 0.784, 2, 1, -149.08, 120.06, 0.536, 16, -149.82, 88.96, 0.464, 2, 1, -122.18, 119.98, 0.696, 16, -150.14, 115.85, 0.304, 1, 1, -111.55, 126.73, 1, 1, 1, -93.94, 126.47, 1, 1, 1, -69.27, 61.94, 1, 1, 1, -26.86, 40.65, 1, 1, 8, -18.4, 7.06, 1, 1, 1, 37.3, -13.71, 1], "hull": 75, "edges": [140, 142, 142, 144, 144, 146, 146, 148, 148, 0, 0, 2, 138, 140, 138, 136, 136, 134, 134, 132, 132, 130, 130, 128, 128, 126, 126, 124, 124, 122, 122, 120, 120, 118, 118, 116, 116, 114, 114, 112, 112, 110, 110, 108, 108, 106, 106, 104, 104, 102, 102, 100, 100, 98, 98, 96, 96, 94, 94, 92, 92, 90, 90, 88, 88, 86, 86, 84, 84, 82, 82, 80, 80, 78, 78, 76, 76, 74, 74, 72, 72, 70, 70, 68, 66, 68, 66, 64, 64, 62, 62, 60, 60, 58, 58, 56, 56, 54, 54, 52, 52, 50, 50, 48, 48, 46, 46, 44, 44, 42, 42, 40, 40, 38, 38, 36, 36, 34, 34, 32, 32, 30, 30, 28, 28, 26, 26, 24, 24, 22, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 2, 4, 6, 4], "width": 265, "height": 288}}, "EyeLeft": {"images/Beholder1_Eye_1": {"name": "images/Beholder4_Eyes", "x": -54.56, "y": -54.88, "rotation": -90.86, "width": 187, "height": 190}, "images/Beholder1_Eye_1_Anticipate": {"name": "images/Beholder4_Eyes_Anticipate", "x": -56.64, "y": -59.85, "rotation": -90.86, "width": 196, "height": 186}, "images/Beholder1_Eye_1_Closed": {"name": "images/Beholder4_Eyes_Closed", "x": -54.56, "y": -54.88, "rotation": -90.86, "width": 187, "height": 190}}, "EyeRight": {"images/Beholder1_Eye_1": {"name": "images/Beholder4_Eyes", "x": -57.37, "y": 47.33, "scaleX": -1, "rotation": -90.86, "width": 187, "height": 190}, "images/Beholder1_Eye_Anticipate": {"name": "images/Beholder4_Eyes_Anticipate", "x": -59.3, "y": 52.36, "scaleX": -1, "rotation": -90.86, "width": 196, "height": 186}, "images/Beholder1_Eye_1_Closed": {"name": "images/Beholder4_Eyes_Closed", "x": -57.37, "y": 47.33, "scaleX": -1, "rotation": -90.86, "width": 187, "height": 190}}, "Head_Back": {"Head_Back": {"name": "images/Beholder4_Nests2", "type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 15, -79.45, -153.23, 1, 1, 15, -82.3, 142.76, 1, 1, 15, 136.69, 144.86, 1, 1, 15, 139.54, -151.12, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 296, "height": 219}}, "Head_Front": {"Head_Front": {"name": "images/Beholder4_Nests1", "type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 3, 21.69, -100.88, 1, 1, 4, -12.1, 91.11, 1, 1, 4, 168.76, 3.42, 1, 2, 3, 190.36, 8.44, 0.9994, 4, -14.91, -375.4, 0.0006], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 429, "height": 213}}, "Horns": {"images/Beholder1_Horns": {"name": "images/Beholder4_Horns", "type": "mesh", "uvs": [0.56859, 0.22805, 0.71781, 0.31073, 0.92609, 0.35434, 0.89804, 0.11122, 1, 0.27538, 1, 0.52256, 0.941, 1, 0.79851, 1, 0.23439, 1, 0.08209, 1, 0.09485, 0.63182, 0, 0.4541, 0, 0.34929, 0.11288, 0.14905, 0.11844, 0.32056, 0.24599, 0.41846, 0.38399, 0.31395, 0.45578, 0.19719, 0.49974, 0, 0.52883, 0, 0.23961, 0.60757, 0.16161, 0.45264, 0.11116, 0.42952, 0.08847, 0.37348, 0.43979, 0.82246, 0.45587, 0.48881, 0.44716, 0.40852, 0.47609, 0.28658, 0.55158, 0.33461, 0.56734, 0.4729, 0.56649, 0.83575, 0.79633, 0.57475, 0.83087, 0.47071, 0.88185, 0.45377, 0.9184, 0.64361, 0.23771, 0.75001, 0.34775, 0.69623, 0.4504, 0.60243, 0.56702, 0.60864, 0.6682, 0.67052, 0.78033, 0.68454, 0.67184, 0.54146, 0.35005, 0.5204], "triangles": [24, 35, 36, 36, 20, 42, 12, 13, 14, 23, 12, 14, 22, 23, 14, 14, 21, 22, 15, 21, 14, 11, 12, 23, 11, 23, 22, 20, 21, 15, 10, 11, 22, 10, 22, 21, 10, 21, 20, 35, 10, 20, 35, 20, 36, 9, 10, 35, 8, 9, 35, 24, 8, 35, 20, 15, 42, 4, 2, 3, 33, 32, 2, 2, 4, 5, 33, 2, 5, 34, 33, 5, 31, 32, 33, 34, 31, 33, 40, 31, 34, 6, 7, 40, 7, 30, 40, 6, 34, 5, 6, 40, 34, 2, 32, 1, 31, 41, 32, 40, 39, 31, 32, 41, 1, 39, 41, 31, 30, 39, 40, 7, 8, 30, 0, 27, 17, 17, 18, 19, 19, 0, 17, 16, 17, 27, 28, 27, 0, 28, 0, 1, 26, 16, 27, 26, 27, 28, 29, 28, 1, 41, 29, 1, 26, 28, 29, 25, 26, 29, 42, 15, 16, 42, 16, 26, 42, 26, 25, 37, 42, 25, 38, 25, 29, 38, 29, 41, 37, 25, 38, 39, 38, 41, 36, 42, 37, 24, 36, 37, 38, 24, 37, 30, 38, 39, 30, 24, 38, 8, 24, 30], "vertices": [1, 15, 207.62, -17.98, 1, 2, 15, 178.2, -80.04, 0.53466, 3, 214.04, 178.3, 0.46534, 1, 3, 248.86, 97.84, 1, 1, 3, 315.73, 156.59, 1, 1, 3, 289.7, 88.36, 1, 1, 3, 215.14, 38.44, 1, 1, 3, 57.54, -37.69, 1, 2, 15, -71.68, -115.86, 0.07194, 3, 24.72, 11.33, 0.92806, 1, 4, 0.53, -20.56, 1, 1, 4, 27.19, 36.58, 1, 1, 4, 146.08, -24.71, 1, 1, 4, 221.14, -16.4, 1, 1, 4, 255.62, -32.48, 1, 1, 4, 301.73, -105.57, 1, 1, 4, 244.34, -81.33, 1, 2, 15, 137.22, 114.91, 0.4, 4, 189.81, -114.16, 0.6, 2, 15, 175.7, 58.14, 0.68, 4, 200.03, -181.97, 0.32, 1, 15, 218.37, 28.83, 1, 1, 15, 290.12, 11.32, 1, 1, 15, 290.23, -0.73, 1, 1, 4, 128.72, -82.74, 1, 1, 4, 193.33, -77.25, 1, 1, 4, 209.77, -61.88, 1, 1, 4, 232.18, -61.96, 1, 1, 15, -8.66, 33.27, 1, 1, 15, 112.51, 27.77, 1, 1, 15, 141.62, 31.66, 1, 1, 15, 186, 20.11, 1, 1, 15, 168.87, -11.31, 1, 1, 15, 118.73, -18.32, 1, 1, 15, -12.98, -19.23, 1, 1, 3, 152.49, 97.96, 1, 1, 3, 191.83, 107.09, 1, 1, 3, 208.68, 92.98, 1, 1, 3, 159.83, 42.06, 1, 1, 4, 82.19, -60.17, 1, 2, 15, 36.79, 71.81, 0.4958, 4, 80.62, -109.71, 0.5042, 1, 15, 71.25, 29.64, 1, 1, 15, 69.46, -18.66, 1, 2, 15, 47.4, -60.76, 0.49382, 3, 94.09, 122.7, 0.50618, 1, 3, 115.69, 81.29, 1, 2, 15, 94.26, -61.82, 0.48449, 3, 133.85, 147.51, 0.51551, 2, 15, 100.63, 71.47, 0.49645, 4, 138.06, -137.56, 0.50355], "hull": 20, "edges": [40, 42, 42, 44, 44, 46, 46, 28, 28, 26, 26, 24, 22, 24, 22, 20, 16, 18, 20, 18, 50, 52, 52, 54, 54, 34, 34, 36, 36, 38, 38, 0, 0, 56, 56, 58, 60, 48, 14, 16, 62, 64, 64, 66, 66, 4, 4, 6, 6, 8, 12, 14, 12, 68, 8, 10, 68, 10, 60, 14, 16, 48, 10, 12, 28, 30, 30, 32, 32, 34, 16, 70, 70, 40, 70, 72, 48, 74, 74, 50, 72, 74, 58, 76, 76, 60, 76, 78, 14, 80, 80, 62, 78, 80, 0, 2, 2, 4, 58, 82, 82, 62, 40, 84, 84, 52], "width": 414, "height": 363}}, "images/Bandage": {"Bandage": {"name": "images/SummonDrop3", "x": 9.9, "y": 192.24, "scaleX": -0.01, "scaleY": -0.01, "width": 85, "height": 107}}, "LeftLegs": {"LeftLegs": {"name": "images/Beholder4_Legs", "type": "mesh", "uvs": [0.95661, 0.12865, 0.87941, 0.19098, 0.79945, 0.2235, 0.93731, 0.25331, 1, 0.30074, 1, 0.643, 0.90747, 0.6954, 0.77009, 0.74377, 0.81725, 0.92819, 0.76804, 1, 0.65526, 1, 0.70242, 0.93121, 0.60605, 0.75788, 0.51788, 0.7347, 0.401, 0.72765, 0.38143, 0.80395, 0.48344, 0.92048, 0.24908, 0.83918, 0.23976, 0.72893, 0.3335, 0.67473, 0.26714, 0.64871, 0.19013, 0.61782, 0.18462, 0.82243, 0.02476, 0.61282, 0.08782, 0.52795, 0.19594, 0.47408, 0.20704, 0.44369, 0.22165, 0.36796, 0.11701, 0.35317, 0.12378, 0.44808, 0, 0.54299, 0, 0.29489, 0.28641, 0.20664, 0.58794, 0.11006, 0.59811, 0.14669, 0.7133, 0.08675, 0.77428, 0, 0.28302, 0.29988, 0.47614, 0.24493, 0.55745, 0.28656, 0.66925, 0.27824, 0.92335, 0.36815, 0.49341, 0.35765, 0.68641, 0.41998, 0.24252, 0.55413, 0.35004, 0.4918, 0.44103, 0.44573, 0.72225, 0.49993, 0.90422, 0.47147, 0.40794, 0.56633, 0.51823, 0.53109, 0.50169, 0.58394, 0.5844, 0.5731, 0.65333, 0.52161, 0.84072, 0.4814, 0.65333, 0.63543, 0.85735, 0.546, 0.92903, 0.59749, 0.73328, 0.63272, 0.65608, 0.70047, 0.62024, 0.67337, 0.27311, 0.43114, 0.34218, 0.35513, 0.49084, 0.31528, 0.33174, 0.5604, 0.47509, 0.49591, 0.62969, 0.48209, 0.64167, 0.50212, 0.69961, 0.66228, 0.4947, 0.6483, 0.5859, 0.69899], "triangles": [10, 11, 9, 9, 11, 8, 7, 8, 11, 11, 12, 7, 12, 59, 7, 12, 70, 59, 59, 68, 7, 7, 68, 6, 68, 58, 6, 6, 57, 5, 6, 58, 57, 17, 15, 16, 15, 17, 18, 21, 22, 23, 15, 18, 14, 70, 13, 69, 18, 19, 14, 13, 14, 69, 14, 19, 69, 70, 69, 60, 19, 20, 49, 19, 49, 69, 49, 20, 64, 60, 55, 68, 60, 69, 55, 20, 21, 64, 64, 21, 44, 69, 52, 55, 69, 51, 52, 69, 49, 51, 56, 58, 55, 55, 52, 53, 56, 55, 53, 23, 24, 21, 21, 24, 44, 56, 48, 57, 4, 48, 41, 51, 50, 52, 51, 49, 50, 52, 50, 53, 49, 65, 50, 49, 64, 65, 64, 45, 65, 64, 44, 45, 24, 25, 44, 44, 25, 45, 61, 25, 26, 25, 61, 45, 53, 54, 56, 56, 54, 48, 50, 67, 53, 50, 65, 67, 29, 31, 28, 31, 29, 30, 53, 47, 54, 53, 67, 47, 65, 66, 67, 67, 66, 47, 54, 47, 43, 45, 46, 65, 65, 46, 66, 45, 61, 46, 47, 66, 43, 66, 46, 43, 48, 54, 41, 41, 54, 43, 61, 62, 46, 46, 42, 43, 46, 62, 42, 26, 27, 61, 61, 27, 62, 42, 39, 40, 43, 40, 41, 62, 27, 37, 62, 63, 42, 42, 63, 39, 27, 28, 37, 62, 37, 63, 37, 38, 63, 63, 38, 39, 37, 32, 38, 37, 31, 32, 39, 38, 40, 38, 32, 34, 43, 42, 40, 13, 70, 12, 70, 60, 59, 59, 60, 68, 68, 55, 58, 58, 56, 57, 5, 57, 48, 4, 5, 48, 41, 40, 3, 41, 3, 4, 3, 40, 2, 2, 40, 34, 40, 38, 34, 1, 2, 35, 32, 33, 34, 2, 34, 35, 1, 35, 0, 35, 36, 0, 28, 31, 37], "vertices": [2, 4, 119.8, -70.17, 0.9997, 6, 69.97, -217.59, 0.0003, 2, 4, 95.89, -29.63, 0.9995, 6, 76.32, -170.95, 0.0005, 2, 4, 89.34, 2.61, 0.99934, 6, 91.16, -141.59, 0.00066, 1, 4, 51.78, -26.85, 1, 1, 4, 15.43, -29.95, 1, 2, 6, -75.31, 80.99, 0.68, 21, -30.64, 59.75, 0.32, 2, 6, -61.94, 123.7, 0.68, 21, 5.89, 85.6, 0.32, 2, 6, -34.5, 169.71, 0.68, 21, 55.62, 105.54, 0.32, 2, 6, -95.77, 272.75, 0.68, 21, 69.41, 224.62, 0.32, 2, 6, -99.99, 321.45, 0.68, 21, 95.59, 265.9, 0.32, 2, 6, -67.23, 335.74, 0.68, 21, 130.31, 257.39, 0.32, 2, 6, -63.19, 289.1, 0.68, 21, 105.23, 217.85, 0.32, 2, 6, 9.52, 198.84, 0.68, 21, 108.29, 102, 0.32, 2, 6, 41.12, 196.32, 0.856, 17, 52.25, 188.26, 0.144, 2, 6, 76.9, 206.96, 0.872, 17, 89.52, 186.21, 0.128, 2, 6, 62.91, 254.55, 0.872, 17, 92.41, 235.72, 0.128, 2, 6, 3.21, 310.51, 0.872, 17, 55.11, 308.55, 0.128, 2, 6, 92.27, 292.16, 0.872, 17, 132.75, 261.21, 0.128, 2, 6, 123.42, 228.16, 0.872, 17, 140.46, 190.45, 0.128, 2, 6, 110.16, 184.24, 0.872, 17, 113.15, 153.58, 0.128, 1, 6, 136.15, 177.27, 1, 2, 6, 166.49, 168.77, 0.792, 17, 160.96, 120, 0.208, 2, 6, 115.32, 290.43, 0.792, 17, 153.86, 251.8, 0.208, 2, 6, 215.83, 186.78, 0.792, 17, 213.48, 120.3, 0.208, 2, 6, 219.4, 128.61, 0.792, 17, 197.2, 64.34, 0.208, 2, 6, 201.88, 83.06, 0.792, 17, 165.33, 27.38, 0.208, 2, 6, 206.5, 63.68, 0.792, 17, 163.13, 7.58, 0.208, 1, 6, 221.79, 17.06, 1, 2, 4, 117.62, 232.82, 0.48, 6, 256, 21.58, 0.52, 1, 6, 229.55, 76.83, 1, 2, 4, 27.15, 323.27, 0.48, 6, 241.04, 148.63, 0.52, 2, 4, 168.26, 247.8, 0.51071, 6, 305.03, 1.96, 0.48929, 2, 4, 175.64, 140.9, 0.26144, 6, 244.58, -86.52, 0.73856, 2, 4, 185.49, 27.23, 0.77966, 6, 181.88, -181.84, 0.22034, 2, 4, 163.13, 35.53, 0.82221, 6, 169.48, -161.47, 0.17779, 1, 4, 180, -14.9, 1, 1, 4, 220.23, -58.34, 1, 2, 4, 123.11, 170.21, 0.59954, 6, 221.51, -30.97, 0.40046, 2, 4, 125.49, 99.51, 0.41397, 6, 179.58, -87.93, 0.58603, 2, 4, 89.66, 89.44, 0.40009, 6, 145.21, -73.63, 0.59991, 2, 4, 77.68, 55.66, 0.6383, 6, 114.88, -92.72, 0.3617, 1, 6, 17.85, -71.78, 1, 2, 6, 145.48, -23.49, 0.792, 17, 76.27, -53.87, 0.208, 2, 6, 73.33, -11.1, 0.792, 17, 12.54, -17.85, 0.208, 2, 6, 167.7, 124.48, 0.792, 17, 147.14, 77.9, 0.208, 2, 6, 152.54, 73.99, 0.792, 17, 115.82, 35.51, 0.208, 2, 6, 137.98, 35.22, 0.792, 17, 89.04, 3.93, 0.208, 1, 6, 42.3, 31.62, 1, 2, 6, -3.24, -8.27, 0.872, 17, -58.58, 10.66, 0.128, 2, 6, 116.49, 110.71, 0.872, 17, 94.29, 82.24, 0.128, 2, 6, 93.54, 75.91, 0.872, 17, 60.93, 57.22, 0.128, 2, 6, 84.71, 109.24, 0.888, 17, 63.88, 91.59, 0.112, 2, 6, 63.48, 92.35, 0.872, 17, 38.19, 82.85, 0.128, 2, 6, 56.73, 53.17, 0.872, 17, 18.61, 48.25, 0.128, 1, 6, 12.65, 5.65, 1, 2, 6, 27.37, 120.46, 0.872, 17, 13.69, 121.5, 0.128, 2, 6, -8.84, 41.73, 0.872, 17, -46.97, 59.62, 0.128, 2, 6, -42.95, 63.08, 0.68, 21, -15.78, 25.89, 0.32, 2, 6, 4.84, 108.72, 0.68, 21, 49.9, 33.19, 0.32, 2, 6, 9.79, 158.56, 0.68, 21, 84.07, 69.81, 0.32, 1, 6, 27.2, 147.09, 1, 2, 6, 190.54, 47.89, 0.792, 17, 142.78, -1.9, 0.208, 2, 6, 190.07, -5.8, 0.792, 17, 124.21, -52.28, 0.208, 2, 6, 157.16, -48.21, 0.792, 17, 78.92, -81.08, 0.208, 1, 6, 140.16, 116.87, 1, 1, 6, 115.15, 60.57, 1, 1, 6, 73.79, 32.8, 1, 1, 6, 65.14, 43.13, 1, 2, 6, 7, 130.47, 0.68, 21, 64.8, 49.16, 0.32, 2, 6, 70.14, 148.18, 0.88546, 17, 63.31, 133.15, 0.11454, 2, 6, 30.57, 166.59, 0.68, 21, 105.45, 63.58, 0.32], "hull": 37, "edges": [10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 62, 64, 64, 66, 60, 62, 60, 58, 58, 56, 56, 74, 74, 76, 76, 78, 78, 80, 80, 82, 66, 68, 68, 70, 70, 72, 72, 0, 0, 2, 2, 4, 4, 6, 10, 8, 6, 8, 84, 86, 86, 82, 44, 42, 42, 88, 88, 90, 90, 92, 36, 38, 38, 98, 98, 100, 100, 102, 102, 104, 104, 106, 94, 108, 108, 96, 106, 108, 110, 112, 114, 116, 118, 120, 32, 30, 30, 28, 32, 34, 34, 36, 44, 46, 46, 48, 48, 50, 50, 52, 52, 122, 122, 124, 124, 126, 126, 84, 42, 40, 40, 38, 40, 128, 128, 130, 92, 132, 132, 94, 106, 134, 134, 132, 130, 134, 116, 136, 136, 118, 110, 136, 112, 114, 56, 54, 54, 52, 28, 138, 138, 110, 24, 140, 140, 120], "width": 317, "height": 645}}, "LeftLegs2": {"LeftLegs": {"name": "images/Beholder4_Legs", "type": "mesh", "uvs": [0.95661, 0.12865, 0.87941, 0.19098, 0.79945, 0.2235, 0.93731, 0.25331, 1, 0.30074, 1, 0.643, 0.90747, 0.6954, 0.77009, 0.74377, 0.81725, 0.92819, 0.76804, 1, 0.65526, 1, 0.70242, 0.93121, 0.60605, 0.75788, 0.51788, 0.7347, 0.401, 0.72765, 0.38143, 0.80395, 0.48344, 0.92048, 0.24908, 0.83918, 0.23976, 0.72893, 0.3335, 0.67473, 0.26714, 0.64871, 0.19013, 0.61782, 0.18462, 0.82243, 0.02476, 0.61282, 0.08782, 0.52795, 0.19594, 0.47408, 0.20704, 0.44369, 0.22165, 0.36796, 0.11701, 0.35317, 0.12378, 0.44808, 0, 0.54299, 0, 0.29489, 0.28641, 0.20664, 0.58794, 0.11006, 0.59811, 0.14669, 0.7133, 0.08675, 0.77428, 0, 0.28302, 0.29988, 0.47614, 0.24493, 0.55745, 0.28656, 0.66925, 0.27824, 0.92335, 0.36815, 0.49341, 0.35765, 0.68641, 0.41998, 0.24252, 0.55413, 0.35004, 0.4918, 0.44103, 0.44573, 0.72225, 0.49993, 0.90422, 0.47147, 0.40794, 0.56633, 0.51823, 0.53109, 0.50169, 0.58394, 0.5844, 0.5731, 0.65333, 0.52161, 0.84072, 0.4814, 0.65333, 0.63543, 0.85735, 0.546, 0.92903, 0.59749, 0.73328, 0.63272, 0.65608, 0.70047, 0.62024, 0.67337, 0.27311, 0.43114, 0.34218, 0.35513, 0.49084, 0.31528, 0.33174, 0.5604, 0.47509, 0.49591, 0.62969, 0.48209, 0.64167, 0.50212, 0.69961, 0.66228, 0.49897, 0.64168], "triangles": [51, 50, 52, 69, 49, 51, 19, 49, 69, 19, 20, 49, 5, 57, 48, 56, 55, 53, 58, 56, 57, 55, 52, 53, 56, 58, 55, 69, 51, 52, 69, 52, 55, 68, 55, 58, 60, 69, 55, 60, 55, 68, 6, 58, 57, 6, 57, 5, 68, 58, 6, 59, 60, 68, 14, 19, 69, 18, 19, 14, 13, 69, 60, 13, 60, 59, 14, 69, 13, 7, 68, 6, 59, 68, 7, 12, 13, 59, 12, 59, 7, 15, 18, 14, 15, 17, 18, 17, 15, 16, 11, 12, 7, 7, 8, 11, 9, 11, 8, 10, 11, 9, 65, 46, 66, 45, 46, 65, 67, 66, 47, 65, 66, 67, 50, 65, 67, 50, 67, 53, 44, 25, 45, 24, 25, 44, 64, 44, 45, 64, 45, 65, 49, 64, 65, 49, 65, 50, 52, 50, 53, 51, 49, 50, 21, 24, 44, 23, 24, 21, 64, 21, 44, 20, 21, 64, 49, 20, 64, 21, 22, 23, 38, 32, 34, 39, 38, 40, 37, 31, 32, 37, 32, 38, 63, 38, 39, 37, 38, 63, 28, 31, 37, 62, 37, 63, 27, 28, 37, 42, 63, 39, 62, 63, 42, 62, 27, 37, 43, 40, 41, 42, 39, 40, 43, 42, 40, 61, 27, 62, 26, 27, 61, 46, 62, 42, 46, 42, 43, 61, 62, 46, 41, 54, 43, 48, 54, 41, 54, 47, 43, 53, 47, 54, 31, 29, 30, 29, 31, 28, 56, 54, 48, 53, 54, 56, 47, 66, 43, 66, 46, 43, 45, 61, 46, 53, 67, 47, 25, 61, 45, 61, 25, 26, 4, 5, 48, 56, 48, 57, 4, 48, 41, 35, 36, 0, 1, 35, 0, 2, 34, 35, 32, 33, 34, 1, 2, 35, 40, 38, 34, 2, 40, 34, 3, 40, 2, 41, 3, 4, 41, 40, 3], "vertices": [2, 29, 4.06, -212.84, 4e-05, 3, 128.62, 70.71, 0.99996, 3, 29, 18.56, -168.06, 3e-05, 3, 112.85, 26.36, 0.99981, 5, 83.24, 161.35, 0.00016, 3, 29, 38.35, -141.78, 0.00017, 3, 112.55, -6.54, 0.98567, 5, 95.42, 130.79, 0.01416, 1, 3, 70.07, 15.26, 1, 1, 3, 33.8, 11.39, 1, 2, 5, -90.12, -76.16, 0.712, 25, -2.46, 51.59, 0.288, 2, 5, -80.58, -119.89, 0.712, 25, 35.38, 75.48, 0.288, 2, 5, -57.33, -168.15, 0.712, 25, 86.09, 92.77, 0.288, 2, 5, -127.49, -265.36, 0.712, 25, 106.12, 210.97, 0.288, 2, 5, -136.01, -313.49, 0.712, 25, 134.43, 250.81, 0.288, 2, 5, -104.64, -330.63, 0.712, 25, 168.66, 240.49, 0.288, 2, 5, -96.48, -284.53, 0.712, 25, 141.54, 202.33, 0.288, 2, 5, -16.06, -201.07, 0.712, 25, 138.5, 86.47, 0.288, 1, 5, 15.64, -201.35, 1, 2, 29, 85.91, 203.8, 0.144, 5, 50.33, -215.13, 0.856, 2, 29, 80.55, 253.11, 0.144, 5, 32.18, -261.29, 0.856, 2, 29, 31.68, 318.74, 0.144, 5, -32.24, -311.74, 0.856, 2, 29, 116.1, 284.94, 0.144, 5, 58.09, -301.35, 0.856, 2, 29, 135.44, 216.45, 0.144, 5, 94.79, -240.36, 0.856, 2, 29, 114.63, 175.55, 0.144, 5, 85.47, -195.43, 0.856, 2, 29, 138.99, 164.1, 0.16, 5, 111.98, -190.8, 0.84, 2, 29, 167.35, 150.37, 0.24, 5, 142.96, -185.01, 0.76, 2, 29, 138.48, 279.16, 0.24, 5, 81.21, -301.66, 0.76, 2, 29, 219.09, 159.38, 0.24, 5, 190.51, -207.33, 0.76, 2, 29, 212.32, 101.5, 0.24, 5, 199.21, -149.7, 0.76, 2, 29, 187.03, 59.76, 0.24, 5, 185.8, -102.78, 0.76, 2, 29, 188.15, 39.87, 0.24, 5, 192.11, -83.89, 0.76, 4, 29, 194.96, -8.72, 0.1188, 3, 155.73, -207.45, 0.00608, 5, 211.47, -38.8, 0.8506, 25, 182.54, -189.5, 0.02453, 4, 29, 229.43, -10.32, 0.00819, 3, 184.05, -227.18, 0.24385, 5, 245.15, -46.34, 0.74682, 25, 211.55, -208.22, 0.00114, 1, 5, 213.91, -99.03, 1, 2, 3, 112.41, -333.17, 0.24, 5, 218.99, -171.56, 0.76, 2, 3, 236.61, -232.26, 0.24464, 5, 295.73, -31.14, 0.75536, 2, 3, 223.54, -125.9, 0.19336, 5, 243.35, 62.35, 0.80664, 2, 3, 211.62, -12.44, 0.698, 5, 189.34, 162.85, 0.302, 2, 3, 191.25, -24.84, 0.74807, 5, 175.19, 143.66, 0.25193, 3, 29, 85.35, -221.27, 2e-05, 3, 198.23, 27.89, 0.98817, 5, 161.68, 195.1, 0.0118, 2, 29, 79.5, -280.18, 3e-05, 3, 229.47, 78.17, 0.99997, 4, 29, 186.2, -55.94, 0.00781, 3, 177.54, -164.66, 0.302, 5, 215.45, 9.06, 0.68927, 25, 151.24, -225.92, 0.00093, 2, 3, 166.45, -94.8, 0.33714, 5, 178.73, 69.51, 0.66286, 2, 3, 129.36, -91.72, 0.31634, 5, 143.23, 58.31, 0.68366, 3, 29, 70.33, -97.88, 0.00427, 3, 111.18, -60.83, 0.55846, 5, 114.71, 80.02, 0.43726, 1, 5, 16.21, 67.75, 1, 2, 29, 112.69, -35.14, 0.24, 5, 139.06, 8.34, 0.76, 2, 29, 43.86, -10.2, 0.24, 5, 66.09, 2.4, 0.76, 2, 29, 160.71, 106.56, 0.24, 5, 148.08, -141, 0.76, 2, 29, 136.86, 59.56, 0.24, 5, 137.45, -89.38, 0.76, 2, 29, 115.69, 23.97, 0.24, 5, 126.39, -49.47, 0.76, 2, 29, 20.86, 37.33, 0.24, 5, 31.4, -37.4, 0.76, 1, 5, -10.42, 6.36, 1, 2, 29, 107.87, 102.07, 0.144, 5, 98.29, -122.76, 0.856, 2, 29, 79.13, 71.87, 0.144, 5, 78.51, -86.06, 0.856, 2, 29, 76.33, 106.24, 0.144, 5, 66.77, -118.48, 0.856, 2, 29, 52.44, 93.36, 0.144, 5, 47.11, -99.77, 0.856, 2, 29, 38.88, 55.99, 0.096, 5, 43.87, -60.15, 0.904, 1, 5, 4.17, -8.91, 1, 2, 29, 21.88, 127.41, 0.144, 5, 8.66, -124.58, 0.856, 1, 5, -20.43, -42.94, 1, 2, 5, -56.3, -61.19, 0.712, 25, 10.6, 16.99, 0.288, 2, 5, -12.74, -110.89, 0.712, 25, 76.58, 20.83, 0.288, 2, 5, -12.22, -160.97, 0.712, 25, 112.62, 55.6, 0.288, 2, 5, 6.13, -151.08, 0.712, 25, 118.45, 35.58, 0.288, 2, 29, 169.65, 27.15, 0.24, 5, 177.61, -66.74, 0.76, 4, 29, 159.7, -25.62, 0.31519, 3, 138.06, -172.58, 0.01006, 5, 181.91, -13.22, 0.6651, 25, 143.57, -186.39, 0.00965, 3, 29, 119.81, -61.54, 0.24, 3, 128.29, -119.79, 0.10291, 5, 152.88, 31.93, 0.65709, 2, 29, 132.26, 103.95, 0.16, 5, 121.32, -130.99, 0.84, 2, 29, 97.69, 52.95, 0.16, 5, 101.4, -72.7, 0.84, 2, 29, 52.07, 32.93, 0.24, 5, 62.66, -41.38, 0.76, 2, 29, 45.39, 44.62, 0.096, 5, 53.14, -50.89, 0.904, 2, 5, -12.52, -132.74, 0.712, 25, 92.3, 36, 0.288, 2, 29, 68.54, 142.67, 0.144, 5, 49.67, -151.57, 0.856], "hull": 37, "edges": [10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 62, 64, 64, 66, 60, 62, 60, 58, 58, 56, 56, 74, 74, 76, 76, 78, 78, 80, 80, 82, 66, 68, 68, 70, 70, 72, 72, 0, 0, 2, 2, 4, 4, 6, 10, 8, 6, 8, 84, 86, 86, 82, 44, 42, 42, 88, 88, 90, 90, 92, 36, 38, 38, 98, 98, 100, 100, 102, 102, 104, 104, 106, 94, 108, 108, 96, 106, 108, 110, 112, 114, 116, 118, 120, 32, 30, 30, 28, 32, 34, 34, 36, 44, 46, 46, 48, 48, 50, 50, 52, 52, 122, 122, 124, 124, 126, 126, 84, 42, 40, 40, 38, 40, 128, 128, 130, 92, 132, 132, 94, 106, 134, 134, 132, 130, 134, 116, 136, 136, 118, 110, 136, 112, 114, 56, 54, 54, 52, 110, 138, 138, 28], "width": 317, "height": 645}}}}, {"name": "Dungeon4_Beaten", "bones": ["TentacleLeftBtm", "TentacleLeftBtm2", "TentacleLeftTop", "TentacleLeftTop2"], "attachments": {"Body": {"images/Beholder1_Body": {"name": "images/Beholder4_Body", "type": "<PERSON><PERSON><PERSON>", "skin": "Dungeon4", "parent": "images/Beholder1_Body", "width": 492, "height": 499}}, "Drips_Back": {"Drips_Back": {"name": "images/Beholder4_Drips", "type": "<PERSON><PERSON><PERSON>", "skin": "Dungeon4", "parent": "Drips_Back", "width": 265, "height": 288}}, "Head_Back": {"Head_Back": {"name": "images/Beholder4_Nests2", "type": "<PERSON><PERSON><PERSON>", "skin": "Dungeon4", "parent": "Head_Back", "width": 296, "height": 219}}, "Head_Front": {"Head_Front": {"name": "images/Beholder4_Nests1", "type": "<PERSON><PERSON><PERSON>", "skin": "Dungeon4", "parent": "Head_Front", "width": 429, "height": 213}}, "Horns": {"images/Beholder1_Horns": {"name": "images/Beholder4_Horns", "type": "<PERSON><PERSON><PERSON>", "skin": "Dungeon4", "parent": "images/Beholder1_Horns", "width": 414, "height": 363}}, "images/Bandage": {"Bandage": {"name": "images/Bandage", "type": "mesh", "uvs": [0.781, 0.10925, 0.87301, 0.19826, 0.9608, 0.28687, 1, 0.46644, 1, 0.82556, 0.96253, 1, 0.90518, 1, 0.79579, 0.73961, 0.69312, 0.67864, 0.56508, 0.63415, 0.40816, 0.646, 0.27702, 0.68871, 0.16121, 0.81037, 0.04916, 0.95626, 0.01871, 0.86514, 1e-05, 0.57047, 0.02225, 0.31173, 0.09936, 0.19963, 0.20272, 0.09013, 0.34377, 0.01226, 0.4818, 0, 0.6353, 0.01559], "triangles": [10, 11, 19, 7, 8, 0, 0, 8, 21, 11, 18, 19, 10, 20, 9, 10, 19, 20, 8, 9, 21, 9, 20, 21, 1, 7, 0, 5, 6, 4, 6, 7, 4, 13, 14, 12, 14, 15, 12, 7, 3, 4, 12, 15, 17, 15, 16, 17, 12, 17, 11, 7, 2, 3, 7, 1, 2, 17, 18, 11], "vertices": [2, 1, 155.43, -153.89, 0.46943, 8, 99.43, -153.06, 0.53057, 2, 1, 135.17, -200.84, 0.744, 8, 79.17, -200.01, 0.256, 1, 1, 117.73, -246.1, 1, 1, 1, 80.44, -265.76, 1, 1, 1, 6.47, -264.66, 1, 1, 1, -29.17, -244.8, 1, 2, 1, -28.73, -215.24, 0.832, 8, -84.72, -214.4, 0.168, 2, 1, 9.07, -156.65, 0.392, 8, -46.92, -155.81, 0.608, 2, 1, 22.84, -103.98, 0.184, 8, -33.15, -103.14, 0.816, 1, 8, -23.15, -37.24, 1, 2, 1, 31.61, 42.87, 0.072, 8, -24.38, 43.7, 0.928, 2, 1, 24.24, 110.54, 0.28, 8, -31.75, 111.37, 0.72, 2, 1, 8.21, 169.16, 0.58466, 8, -47.78, 170, 0.41534, 1, 1, -13.26, 226.01, 1, 1, 1, 7, 241.2, 1, 1, 1, 67.09, 250.07, 1, 1, 1, 119.86, 237.87, 1, 2, 1, 142.77, 197.69, 0.82706, 8, 86.78, 198.53, 0.17294, 2, 1, 164.34, 144.1, 0.488, 8, 108.34, 144.94, 0.512, 2, 1, 179.19, 71.15, 0.152, 8, 123.2, 71.99, 0.848, 2, 1, 180.53, -0.02, 0.056, 8, 124.53, 0.81, 0.944, 2, 1, 176.53, -79.19, 0.112, 8, 120.53, -78.35, 0.888], "hull": 22, "edges": [36, 38, 30, 32, 22, 20, 16, 14, 14, 12, 10, 12, 2, 4, 4, 6, 38, 40, 40, 42, 20, 18, 18, 16, 6, 8, 10, 8, 2, 0, 0, 42, 32, 34, 34, 36, 22, 24, 24, 26, 26, 28, 28, 30], "width": 473, "height": 206}}, "images/Beholder_ExtraDrips": {"images/Beholder_ExtraDrips": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 1, -67.85, -228.86, 1, 2, 1, -64.8, -24.88, 0.248, 8, -120.8, -24.05, 0.752, 2, 1, 66.18, -26.84, 0.248, 8, 10.19, -26, 0.752, 1, 1, 63.14, -230.82, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 204, "height": 131}}, "images/Beholder_ExtraDrips2": {"images/Beholder_ExtraDrips": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [2, 1, -56.9, 7.11, 0.344, 8, -112.9, 7.94, 0.656, 1, 1, -53.86, 211.08, 1, 1, 1, 77.13, 209.13, 1, 2, 1, 74.08, 5.15, 0.344, 8, 18.09, 5.99, 0.656], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 204, "height": 131}}, "LeftLegs": {"LeftLegs": {"name": "images/Beholder4_Legs", "type": "<PERSON><PERSON><PERSON>", "skin": "Dungeon4", "parent": "LeftLegs", "width": 317, "height": 645}}, "LeftLegs2": {"LeftLegs": {"name": "images/Beholder4_Legs", "type": "<PERSON><PERSON><PERSON>", "skin": "Dungeon4", "parent": "LeftLegs", "width": 317, "height": 645}}}}], "events": {"teleport": {}}, "animations": {"animation": {"bones": {"Main": {"rotate": [{"value": 0.13, "curve": [0.088, 0.91, 0.174, 1.64]}, {"time": 0.2333, "value": 1.64, "curve": [0.358, 1.64, 0.608, -1.67]}, {"time": 0.7333, "value": -1.67, "curve": [0.799, -1.67, 0.901, -0.74]}, {"time": 1, "value": 0.13}], "translate": [{"curve": [0.125, 0, 0.375, 0, 0.125, 0, 0.375, -84.21]}, {"time": 0.5, "y": -84.21, "curve": [0.625, 0, 0.875, 0, 0.625, -84.21, 0.875, 0]}, {"time": 1}], "scale": [{"x": 1.004, "y": 0.987, "curve": [0.088, 1.024, 0.174, 1.043, 0.088, 0.972, 0.174, 0.958]}, {"time": 0.2333, "x": 1.043, "y": 0.958, "curve": [0.358, 1.043, 0.608, 0.958, 0.358, 0.958, 0.608, 1.021]}, {"time": 0.7333, "x": 0.958, "y": 1.021, "curve": [0.799, 0.958, 0.901, 0.982, 0.799, 1.021, 0.901, 1.003]}, {"time": 1, "x": 1.004, "y": 0.987}]}, "FACE": {"translate": [{"x": 0.13, "y": 6.71, "curve": [0.076, 0.21, 0.148, 0.28, 0.076, 11.82, 0.148, 16.08]}, {"time": 0.2, "x": 0.28, "y": 16.08, "curve": [0.325, 0.28, 0.575, -0.11, 0.325, 16.08, 0.575, -9.38]}, {"time": 0.7, "x": -0.11, "y": -9.38, "curve": [0.774, -0.11, 0.891, 0.02, 0.774, -9.38, 0.891, -0.44]}, {"time": 1, "x": 0.13, "y": 6.71}]}, "HornBtmLeft": {"rotate": [{"value": 3.6, "curve": [0.012, 3.71, 0.023, 3.76]}, {"time": 0.0333, "value": 3.76, "curve": [0.062, 3.76, 0.096, 3.42]}, {"time": 0.1333, "value": 2.87, "curve": [0.261, 1.16, 0.436, -3.1]}, {"time": 0.5333, "value": -3.1, "curve": [0.648, -3.1, 0.871, 2.82]}, {"time": 1, "value": 3.6}]}, "HornTopLeft": {"rotate": [{"value": 2.87, "curve": [0.128, 1.16, 0.303, -3.1]}, {"time": 0.4, "value": -3.1, "curve": [0.525, -3.1, 0.775, 3.76]}, {"time": 0.9, "value": 3.76, "curve": [0.928, 3.76, 0.963, 3.42]}, {"time": 1, "value": 2.87}]}, "HornTopRight": {"rotate": [{"value": -2.67, "curve": [0.128, -1.1, 0.303, 2.82]}, {"time": 0.4, "value": 2.82, "curve": [0.525, 2.82, 0.775, -3.49]}, {"time": 0.9, "value": -3.49, "curve": [0.928, -3.49, 0.963, -3.17]}, {"time": 1, "value": -2.67}]}, "HornBtmRight": {"rotate": [{"value": -3.34, "curve": [0.012, -3.44, 0.023, -3.49]}, {"time": 0.0333, "value": -3.49, "curve": [0.062, -3.49, 0.096, -3.17]}, {"time": 0.1333, "value": -2.67, "curve": [0.261, -1.1, 0.436, 2.82]}, {"time": 0.5333, "value": 2.82, "curve": [0.648, 2.82, 0.871, -2.62]}, {"time": 1, "value": -3.34}]}, "Drips1": {"translate": [{"x": 0.05, "y": 0.41, "curve": [0.109, -0.03, 0.226, -0.12, 0.109, -5.71, 0.226, -13.35]}, {"time": 0.3, "x": -0.12, "y": -13.35, "curve": [0.425, -0.12, 0.675, 0.14, 0.425, -13.35, 0.675, 8.42]}, {"time": 0.8, "x": 0.14, "y": 8.42, "curve": [0.852, 0.14, 0.924, 0.1, 0.852, 8.42, 0.924, 4.78]}, {"time": 1, "x": 0.05, "y": 0.41}]}, "FACEBOB": {"translate": [{"x": -6, "y": 0.1, "curve": [0.125, -6, 0.375, 18, 0.125, 0.1, 0.375, -0.19]}, {"time": 0.5, "x": 18, "y": -0.19, "curve": [0.625, 18, 0.875, -6, 0.625, -0.19, 0.875, 0.1]}, {"time": 1, "x": -6, "y": 0.1}]}, "Drips2": {"translate": [{"x": 0.14, "y": 7.93, "curve": [0.012, 0.14, 0.023, 0.14, 0.012, 8.25, 0.023, 8.42]}, {"time": 0.0333, "x": 0.14, "y": 8.42, "curve": [0.085, 0.14, 0.157, 0.1, 0.085, 8.42, 0.157, 4.78]}, {"time": 0.2333, "x": 0.05, "y": 0.41, "curve": [0.342, -0.03, 0.46, -0.12, 0.342, -5.71, 0.46, -13.35]}, {"time": 0.5333, "x": -0.12, "y": -13.35, "curve": [0.648, -0.12, 0.871, 0.11, 0.648, -13.35, 0.871, 5.42]}, {"time": 1, "x": 0.14, "y": 7.93}]}, "TentacleLeftTop": {"rotate": [{"value": 20.88, "curve": [0.125, 20.88, 0.375, -20.66]}, {"time": 0.5, "value": -20.66, "curve": [0.625, -20.66, 0.875, 20.88]}, {"time": 1, "value": 20.88}]}, "HornBtmLeft2b": {"rotate": [{"value": -1.5, "curve": [0.064, -0.64, 0.123, 0]}, {"time": 0.1667, "curve": [0.292, 0, 0.542, -5.3]}, {"time": 0.6667, "value": -5.3, "curve": [0.748, -5.3, 0.883, -3.02]}, {"time": 1, "value": -1.5}]}, "HornBtmLeft2c": {"rotate": [{"value": -14.58, "curve": [0.099, -7.54, 0.201, 0]}, {"time": 0.2667, "curve": [0.392, 0, 0.642, -26.8]}, {"time": 0.7667, "value": -26.8, "curve": [0.826, -26.8, 0.912, -20.89]}, {"time": 1, "value": -14.58}]}, "HornBtmLeft2d": {"rotate": [{"value": -32.04, "curve": [0.117, -11.54, 0.252, 19.21]}, {"time": 0.3333, "value": 19.21, "curve": [0.458, 19.21, 0.708, -52.35]}, {"time": 0.8333, "value": -52.35, "curve": [0.877, -52.35, 0.936, -43.64]}, {"time": 1, "value": -32.04}]}, "TentacleLeftBtm": {"rotate": [{"curve": [0.125, 0, 0.375, 33.59]}, {"time": 0.5, "value": 33.59, "curve": [0.625, 33.59, 0.875, 0]}, {"time": 1}]}, "HornBtmLeft3b": {"rotate": [{"value": -37.17, "curve": [0.064, -42.43, 0.123, -46.37]}, {"time": 0.1667, "value": -46.37, "curve": [0.292, -46.37, 0.542, -13.95]}, {"time": 0.6667, "value": -13.95, "curve": [0.748, -13.95, 0.883, -27.88]}, {"time": 1, "value": -37.17}]}, "HornBtmLeft3c": {"rotate": [{"value": 50.29, "curve": [0.099, 35.4, 0.201, 19.45]}, {"time": 0.2667, "value": 19.45, "curve": [0.392, 19.45, 0.642, 76.14]}, {"time": 0.7667, "value": 76.14, "curve": [0.826, 76.14, 0.912, 63.64]}, {"time": 1, "value": 50.29}]}, "HornBtmLeft3d": {"rotate": [{"value": 89.42, "curve": [0.117, 60.75, 0.252, 17.74]}, {"time": 0.3333, "value": 17.74, "curve": [0.458, 17.74, 0.708, 117.82]}, {"time": 0.8333, "value": 117.82, "curve": [0.877, 117.82, 0.936, 105.65]}, {"time": 1, "value": 89.42}]}, "HornBtmLeft3d2": {"rotate": [{"value": 89.42, "curve": [0.117, 60.75, 0.252, 17.74]}, {"time": 0.3333, "value": 17.74, "curve": [0.458, 17.74, 0.708, 117.82]}, {"time": 0.8333, "value": 117.82, "curve": [0.877, 117.82, 0.936, 105.65]}, {"time": 1, "value": 89.42}]}, "HornBtmLeft3c2": {"rotate": [{"value": 50.29, "curve": [0.099, 35.4, 0.201, 19.45]}, {"time": 0.2667, "value": 19.45, "curve": [0.392, 19.45, 0.642, 76.14]}, {"time": 0.7667, "value": 76.14, "curve": [0.826, 76.14, 0.912, 63.64]}, {"time": 1, "value": 50.29}]}, "HornBtmLeft3b2": {"rotate": [{"value": -37.17, "curve": [0.064, -42.43, 0.123, -46.37]}, {"time": 0.1667, "value": -46.37, "curve": [0.292, -46.37, 0.542, -13.95]}, {"time": 0.6667, "value": -13.95, "curve": [0.748, -13.95, 0.883, -27.88]}, {"time": 1, "value": -37.17}]}, "TentacleLeftBtm2": {"rotate": [{"curve": [0.125, 0, 0.375, -26.24]}, {"time": 0.5, "value": -26.24, "curve": [0.625, -26.24, 0.875, 0]}, {"time": 1}]}, "HornBtmLeft2d2": {"rotate": [{"value": -32.04, "curve": [0.117, -11.54, 0.252, 19.21]}, {"time": 0.3333, "value": 19.21, "curve": [0.458, 19.21, 0.708, -52.35]}, {"time": 0.8333, "value": -52.35, "curve": [0.877, -52.35, 0.936, -43.64]}, {"time": 1, "value": -32.04}]}, "HornBtmLeft2c2": {"rotate": [{"value": -14.58, "curve": [0.099, -7.54, 0.201, 0]}, {"time": 0.2667, "curve": [0.392, 0, 0.642, -26.8]}, {"time": 0.7667, "value": -26.8, "curve": [0.826, -26.8, 0.912, -20.89]}, {"time": 1, "value": -14.58}]}, "HornBtmLeft2b2": {"rotate": [{"value": -1.5, "curve": [0.064, -0.64, 0.123, 0]}, {"time": 0.1667, "curve": [0.292, 0, 0.542, -5.3]}, {"time": 0.6667, "value": -5.3, "curve": [0.748, -5.3, 0.883, -3.02]}, {"time": 1, "value": -1.5}]}, "TentacleLeftTop2": {"rotate": [{"value": -32.65, "curve": [0.125, -32.65, 0.375, 8.41]}, {"time": 0.5, "value": 8.41, "curve": [0.625, 8.41, 0.875, -32.65]}, {"time": 1, "value": -32.65}]}}}, "anticipate": {"slots": {"EyeLeft": {"attachment": [{"name": "images/Beholder1_Eye_1_Closed"}]}, "EyeRight": {"attachment": [{"name": "images/Beholder1_Eye_1_Closed"}]}, "Eye_Middle": {"attachment": [{"name": "images/Beholder1_Eye_2_Closed"}]}}, "bones": {"FACE": {"translate": [{"x": 0.39, "y": 19.18, "curve": [0.017, 0.39, 0.05, -0.1, 0.017, 19.18, 0.05, 0.44]}, {"time": 0.0667, "x": -0.1, "y": 0.44, "curve": [0.083, -0.1, 0.117, 0.39, 0.083, 0.44, 0.117, 19.18]}, {"time": 0.1333, "x": 0.39, "y": 19.18, "curve": [0.15, 0.39, 0.183, -0.1, 0.15, 19.18, 0.183, 0.44]}, {"time": 0.2, "x": -0.1, "y": 0.44, "curve": [0.217, -0.1, 0.25, 0.39, 0.217, 0.44, 0.25, 19.18]}, {"time": 0.2667, "x": 0.39, "y": 19.18, "curve": [0.283, 0.39, 0.317, -0.1, 0.283, 19.18, 0.317, 0.44]}, {"time": 0.3333, "x": -0.1, "y": 0.44, "curve": [0.35, -0.1, 0.383, 0.39, 0.35, 0.44, 0.383, 19.18]}, {"time": 0.4, "x": 0.39, "y": 19.18, "curve": [0.417, 0.39, 0.45, -0.1, 0.417, 19.18, 0.45, 0.44]}, {"time": 0.4667, "x": -0.1, "y": 0.44, "curve": [0.483, -0.1, 0.517, 0.39, 0.483, 0.44, 0.517, 19.18]}, {"time": 0.5333, "x": 0.39, "y": 19.18, "curve": [0.55, 0.39, 0.583, -0.1, 0.55, 19.18, 0.583, 0.44]}, {"time": 0.6, "x": -0.1, "y": 0.44, "curve": [0.617, -0.1, 0.65, 0.39, 0.617, 0.44, 0.65, 19.18]}, {"time": 0.6667, "x": 0.39, "y": 19.18}]}, "HornTopRight": {"rotate": [{"value": -2.67, "curve": [0.085, -1.1, 0.202, 2.82]}, {"time": 0.2667, "value": 2.82, "curve": [0.35, 2.82, 0.517, -3.49]}, {"time": 0.6, "value": -3.49, "curve": [0.619, -3.49, 0.642, -3.17]}, {"time": 0.6667, "value": -2.67}]}, "HornBtmRight": {"rotate": [{"value": -3.34, "curve": [0.012, -3.44, 0.023, -3.49]}, {"time": 0.0333, "value": -3.49, "curve": [0.052, -3.49, 0.075, -3.17]}, {"time": 0.1, "value": -2.67, "curve": [0.185, -1.1, 0.302, 2.82]}, {"time": 0.3667, "value": 2.82, "curve": [0.441, 2.82, 0.584, -2.62]}, {"time": 0.6667, "value": -3.34}]}, "Main": {"rotate": [{"value": 0.13}], "translate": [{"curve": [0.083, 0, 0.25, 0, 0.083, 0, 0.25, -52.82]}, {"time": 0.3333, "y": -52.82, "curve": [0.417, 0, 0.583, 0, 0.417, -52.82, 0.583, 0]}, {"time": 0.6667}], "scale": [{"x": 0.883, "y": 1.067, "curve": [0.017, 0.883, 0.05, 0.795, 0.017, 1.067, 0.05, 1.137]}, {"time": 0.0667, "x": 0.795, "y": 1.137, "curve": [0.083, 0.795, 0.117, 0.883, 0.083, 1.137, 0.117, 1.067]}, {"time": 0.1333, "x": 0.883, "y": 1.067, "curve": [0.15, 0.883, 0.183, 0.795, 0.15, 1.067, 0.183, 1.137]}, {"time": 0.2, "x": 0.795, "y": 1.137, "curve": [0.217, 0.795, 0.25, 0.883, 0.217, 1.137, 0.25, 1.067]}, {"time": 0.2667, "x": 0.883, "y": 1.067, "curve": [0.283, 0.883, 0.317, 0.795, 0.283, 1.067, 0.317, 1.137]}, {"time": 0.3333, "x": 0.795, "y": 1.137, "curve": [0.35, 0.795, 0.383, 0.883, 0.35, 1.137, 0.383, 1.067]}, {"time": 0.4, "x": 0.883, "y": 1.067, "curve": [0.417, 0.883, 0.45, 0.795, 0.417, 1.067, 0.45, 1.137]}, {"time": 0.4667, "x": 0.795, "y": 1.137, "curve": [0.483, 0.795, 0.517, 0.883, 0.483, 1.137, 0.517, 1.067]}, {"time": 0.5333, "x": 0.883, "y": 1.067, "curve": [0.55, 0.883, 0.583, 0.795, 0.55, 1.067, 0.583, 1.137]}, {"time": 0.6, "x": 0.795, "y": 1.137, "curve": [0.617, 0.795, 0.65, 0.883, 0.617, 1.137, 0.65, 1.067]}, {"time": 0.6667, "x": 0.883, "y": 1.067}]}, "Drips2": {"translate": [{"x": 0.05, "y": 0.41, "curve": [0.073, -0.03, 0.151, -0.12, 0.073, -5.71, 0.151, -13.35]}, {"time": 0.2, "x": -0.12, "y": -13.35, "curve": [0.283, -0.12, 0.45, 0.14, 0.283, -13.35, 0.45, 8.42]}, {"time": 0.5333, "x": 0.14, "y": 8.42, "curve": [0.568, 0.14, 0.616, 0.1, 0.568, 8.42, 0.616, 4.78]}, {"time": 0.6667, "x": 0.05, "y": 0.41}]}, "HornTopLeft": {"rotate": [{"value": 2.87, "curve": [0.085, 1.16, 0.202, -3.1]}, {"time": 0.2667, "value": -3.1, "curve": [0.35, -3.1, 0.517, 3.76]}, {"time": 0.6, "value": 3.76, "curve": [0.619, 3.76, 0.642, 3.42]}, {"time": 0.6667, "value": 2.87}]}, "HornBtmLeft": {"rotate": [{"value": 3.6, "curve": [0.012, 3.71, 0.023, 3.76]}, {"time": 0.0333, "value": 3.76, "curve": [0.052, 3.76, 0.075, 3.42]}, {"time": 0.1, "value": 2.87, "curve": [0.185, 1.16, 0.302, -3.1]}, {"time": 0.3667, "value": -3.1, "curve": [0.441, -3.1, 0.584, 2.82]}, {"time": 0.6667, "value": 3.6}]}, "FACEBOB": {"translate": [{"x": -50.88, "y": -10.91}]}, "Drips1": {"translate": [{"x": 0.05, "y": 0.41, "curve": [0.073, -0.03, 0.151, -0.12, 0.073, -5.71, 0.151, -13.35]}, {"time": 0.2, "x": -0.12, "y": -13.35, "curve": [0.283, -0.12, 0.45, 0.14, 0.283, -13.35, 0.45, 8.42]}, {"time": 0.5333, "x": 0.14, "y": 8.42, "curve": [0.568, 0.14, 0.616, 0.1, 0.568, 8.42, 0.616, 4.78]}, {"time": 0.6667, "x": 0.05, "y": 0.41}]}}}, "anticipate-charge": {"slots": {"EyeLeft": {"attachment": [{"name": "images/Beholder1_Eye_1_Anticipate"}]}, "EyeRight": {"attachment": [{"name": "images/Beholder1_Eye_Anticipate"}]}, "Eye_Middle": {"attachment": [{"name": "images/Beholder1_Eye_1_Anticipate"}]}}, "bones": {"HornBtmLeft3b": {"rotate": [{"value": -37.17, "curve": [0.025, -42.43, 0.049, -46.37]}, {"time": 0.0667, "value": -46.37, "curve": [0.108, -46.37, 0.192, -13.95]}, {"time": 0.2333, "value": -13.95, "curve": [0.258, -13.95, 0.298, -27.88]}, {"time": 0.3333, "value": -37.17, "curve": [0.359, -42.43, 0.382, -46.37]}, {"time": 0.4, "value": -46.37, "curve": [0.442, -46.37, 0.525, -13.95]}, {"time": 0.5667, "value": -13.95, "curve": [0.591, -13.95, 0.632, -27.88]}, {"time": 0.6667, "value": -37.17}]}, "HornBtmLeft2c": {"rotate": [{"value": -14.58, "curve": [0.037, -7.54, 0.075, 0]}, {"time": 0.1, "curve": [0.142, 0, 0.225, -26.8]}, {"time": 0.2667, "value": -26.8, "curve": [0.283, -26.8, 0.308, -20.89]}, {"time": 0.3333, "value": -14.58, "curve": [0.37, -7.54, 0.409, 0]}, {"time": 0.4333, "curve": [0.475, 0, 0.558, -26.8]}, {"time": 0.6, "value": -26.8, "curve": [0.617, -26.8, 0.641, -20.89]}, {"time": 0.6667, "value": -14.58}]}, "HornBtmLeft2d": {"rotate": [{"value": -32.04, "curve": [0.047, -11.54, 0.101, 19.21]}, {"time": 0.1333, "value": 19.21, "curve": [0.167, 19.21, 0.233, -86.31]}, {"time": 0.2667, "value": -86.31, "curve": [0.284, -86.31, 0.308, -63.05]}, {"time": 0.3333, "value": -32.04, "curve": [0.38, -11.54, 0.434, 19.21]}, {"time": 0.4667, "value": 19.21, "curve": [0.5, 19.21, 0.567, -86.31]}, {"time": 0.6, "value": -86.31, "curve": [0.618, -86.31, 0.641, -63.05]}, {"time": 0.6667, "value": -32.04}]}, "HornBtmLeft3c": {"rotate": [{"value": 50.29, "curve": [0.037, 35.4, 0.075, 19.45]}, {"time": 0.1, "value": 19.45, "curve": [0.142, 19.45, 0.225, 76.14]}, {"time": 0.2667, "value": 76.14, "curve": [0.283, 76.14, 0.308, 63.64]}, {"time": 0.3333, "value": 50.29, "curve": [0.37, 35.4, 0.409, 19.45]}, {"time": 0.4333, "value": 19.45, "curve": [0.475, 19.45, 0.558, 76.14]}, {"time": 0.6, "value": 76.14, "curve": [0.617, 76.14, 0.641, 63.64]}, {"time": 0.6667, "value": 50.29}]}, "TentacleLeftTop": {"rotate": [{"value": 20.88, "curve": [0.042, 20.88, 0.125, -20.66]}, {"time": 0.1667, "value": -20.66, "curve": [0.208, -20.66, 0.292, 20.88]}, {"time": 0.3333, "value": 20.88, "curve": [0.375, 20.88, 0.458, -20.66]}, {"time": 0.5, "value": -20.66, "curve": [0.542, -20.66, 0.625, 20.88]}, {"time": 0.6667, "value": 20.88}]}, "HornBtmLeft2b": {"rotate": [{"value": -1.5, "curve": [0.025, -0.64, 0.049, 0]}, {"time": 0.0667, "curve": [0.108, 0, 0.192, -5.3]}, {"time": 0.2333, "value": -5.3, "curve": [0.258, -5.3, 0.298, -3.02]}, {"time": 0.3333, "value": -1.5, "curve": [0.359, -0.64, 0.382, 0]}, {"time": 0.4, "curve": [0.442, 0, 0.525, -5.3]}, {"time": 0.5667, "value": -5.3, "curve": [0.591, -5.3, 0.632, -3.02]}, {"time": 0.6667, "value": -1.5}]}, "TentacleLeftBtm": {"rotate": [{"curve": [0.042, 0, 0.125, 33.59]}, {"time": 0.1667, "value": 33.59, "curve": [0.208, 33.59, 0.292, 0]}, {"time": 0.3333, "curve": [0.375, 0, 0.458, 33.59]}, {"time": 0.5, "value": 33.59, "curve": [0.542, 33.59, 0.625, 0]}, {"time": 0.6667}]}, "HornBtmLeft3d": {"rotate": [{"value": 89.42, "curve": [0.047, 60.75, 0.101, 17.74]}, {"time": 0.1333, "value": 17.74, "curve": [0.167, 17.74, 0.233, 117.82]}, {"time": 0.2667, "value": 117.82, "curve": [0.284, 117.82, 0.308, 105.65]}, {"time": 0.3333, "value": 89.42, "curve": [0.38, 60.75, 0.434, 17.74]}, {"time": 0.4667, "value": 17.74, "curve": [0.5, 17.74, 0.567, 117.82]}, {"time": 0.6, "value": 117.82, "curve": [0.618, 117.82, 0.641, 105.65]}, {"time": 0.6667, "value": 89.42}]}, "HornBtmLeft3d2": {"rotate": [{"value": 89.42, "curve": [0.047, 60.75, 0.101, 17.74]}, {"time": 0.1333, "value": 17.74, "curve": [0.167, 17.74, 0.233, 117.82]}, {"time": 0.2667, "value": 117.82, "curve": [0.284, 117.82, 0.308, 105.65]}, {"time": 0.3333, "value": 89.42, "curve": [0.38, 60.75, 0.434, 17.74]}, {"time": 0.4667, "value": 17.74, "curve": [0.5, 17.74, 0.567, 117.82]}, {"time": 0.6, "value": 117.82, "curve": [0.618, 117.82, 0.641, 105.65]}, {"time": 0.6667, "value": 89.42}]}, "HornBtmLeft3c2": {"rotate": [{"value": 50.29, "curve": [0.037, 35.4, 0.075, 19.45]}, {"time": 0.1, "value": 19.45, "curve": [0.142, 19.45, 0.225, 76.14]}, {"time": 0.2667, "value": 76.14, "curve": [0.283, 76.14, 0.308, 63.64]}, {"time": 0.3333, "value": 50.29, "curve": [0.37, 35.4, 0.409, 19.45]}, {"time": 0.4333, "value": 19.45, "curve": [0.475, 19.45, 0.558, 76.14]}, {"time": 0.6, "value": 76.14, "curve": [0.617, 76.14, 0.641, 63.64]}, {"time": 0.6667, "value": 50.29}]}, "HornBtmLeft3b2": {"rotate": [{"value": -37.17, "curve": [0.025, -42.43, 0.049, -46.37]}, {"time": 0.0667, "value": -46.37, "curve": [0.108, -46.37, 0.192, -13.95]}, {"time": 0.2333, "value": -13.95, "curve": [0.258, -13.95, 0.298, -27.88]}, {"time": 0.3333, "value": -37.17, "curve": [0.359, -42.43, 0.382, -46.37]}, {"time": 0.4, "value": -46.37, "curve": [0.442, -46.37, 0.525, -13.95]}, {"time": 0.5667, "value": -13.95, "curve": [0.591, -13.95, 0.632, -27.88]}, {"time": 0.6667, "value": -37.17}]}, "TentacleLeftBtm2": {"rotate": [{"curve": [0.042, 0, 0.125, -26.24]}, {"time": 0.1667, "value": -26.24, "curve": [0.208, -26.24, 0.292, 0]}, {"time": 0.3333, "curve": [0.375, 0, 0.458, -26.24]}, {"time": 0.5, "value": -26.24, "curve": [0.542, -26.24, 0.625, 0]}, {"time": 0.6667}]}, "HornBtmLeft2d2": {"rotate": [{"value": -32.04, "curve": [0.047, -11.54, 0.101, 19.21]}, {"time": 0.1333, "value": 19.21, "curve": [0.167, 19.21, 0.233, -80.52]}, {"time": 0.2667, "value": -80.52, "curve": [0.284, -80.52, 0.308, -59.74]}, {"time": 0.3333, "value": -32.04, "curve": [0.38, -11.54, 0.434, 19.21]}, {"time": 0.4667, "value": 19.21, "curve": [0.5, 19.21, 0.567, -80.52]}, {"time": 0.6, "value": -80.52, "curve": [0.618, -80.52, 0.641, -59.74]}, {"time": 0.6667, "value": -32.04}]}, "HornBtmLeft2c2": {"rotate": [{"value": -14.58, "curve": [0.037, -7.54, 0.075, 0]}, {"time": 0.1, "curve": [0.142, 0, 0.225, -26.8]}, {"time": 0.2667, "value": -26.8, "curve": [0.283, -26.8, 0.308, -20.89]}, {"time": 0.3333, "value": -14.58, "curve": [0.37, -7.54, 0.409, 0]}, {"time": 0.4333, "curve": [0.475, 0, 0.558, -26.8]}, {"time": 0.6, "value": -26.8, "curve": [0.617, -26.8, 0.641, -20.89]}, {"time": 0.6667, "value": -14.58}]}, "HornBtmLeft2b2": {"rotate": [{"value": -1.5, "curve": [0.025, -0.64, 0.049, 0]}, {"time": 0.0667, "curve": [0.108, 0, 0.192, -5.3]}, {"time": 0.2333, "value": -5.3, "curve": [0.258, -5.3, 0.298, -3.02]}, {"time": 0.3333, "value": -1.5, "curve": [0.359, -0.64, 0.382, 0]}, {"time": 0.4, "curve": [0.442, 0, 0.525, -5.3]}, {"time": 0.5667, "value": -5.3, "curve": [0.591, -5.3, 0.632, -3.02]}, {"time": 0.6667, "value": -1.5}]}, "TentacleLeftTop2": {"rotate": [{"value": -32.65, "curve": [0.042, -32.65, 0.125, 8.41]}, {"time": 0.1667, "value": 8.41, "curve": [0.208, 8.41, 0.292, -32.65]}, {"time": 0.3333, "value": -32.65, "curve": [0.375, -32.65, 0.458, 8.41]}, {"time": 0.5, "value": 8.41, "curve": [0.542, 8.41, 0.625, -32.65]}, {"time": 0.6667, "value": -32.65}]}, "HornBtmRight": {"rotate": [{"value": -3.34, "curve": [0.108, -3.34, 0.325, 12.62]}, {"time": 0.4333, "value": 12.62, "curve": [0.658, 12.62, 0.707, -4.32]}, {"time": 1.3333, "value": -4.32}]}, "Drips1": {"translate": [{"x": 0.05, "y": 0.41}]}, "Main": {"rotate": [{"value": 0.13}], "translate": [{"curve": [0.033, 0, 0.1, 0, 0.033, 0, 0.1, 46.58]}, {"time": 0.1333, "y": 46.58, "curve": [0.433, 0, 0.362, 0, 0.433, 46.58, 0.362, -98.34]}, {"time": 1.3333, "y": -98.34}], "scale": [{"x": 0.9, "y": 1.165, "curve": [0.017, 0.9, 0.05, 1.108, 0.017, 1.165, 0.05, 0.91]}, {"time": 0.0667, "x": 1.108, "y": 0.91, "curve": [0.092, 1.045, 0.115, 1.004, 0.092, 0.956, 0.115, 0.987]}, {"time": 0.1333, "x": 1.004, "y": 0.987, "curve": [0.433, 1.004, 0.362, 0.783, 0.433, 0.987, 0.362, 1.23]}, {"time": 1.3333, "x": 0.783, "y": 1.23}]}, "FACE": {"translate": [{"x": 0.13, "y": 6.71, "curve": [0.033, 0.13, 0.1, 49.09, 0.033, 6.71, 0.1, 5.86]}, {"time": 0.1333, "x": 49.09, "y": 5.86, "curve": [0.2, 49.09, 0.27, -70.46, 0.2, 5.86, 0.27, 7.95]}, {"time": 0.4, "x": -74.59, "y": 8.02, "curve": [0.425, -74.59, 0.475, -74.4, 0.425, 8.02, 0.475, 17.54]}, {"time": 0.5, "x": -74.4, "y": 17.54, "curve": [0.525, -74.4, 0.575, -74.74, 0.525, 17.54, 0.575, 0.77]}, {"time": 0.6, "x": -74.74, "y": 0.77, "curve": [0.625, -74.74, 0.675, -74.4, 0.625, 0.77, 0.675, 17.54]}, {"time": 0.7, "x": -74.4, "y": 17.54, "curve": [0.725, -74.4, 0.775, -74.74, 0.725, 17.54, 0.775, 0.77]}, {"time": 0.8, "x": -74.74, "y": 0.77, "curve": [0.817, -74.74, 0.85, -74.4, 0.817, 0.77, 0.85, 17.54]}, {"time": 0.8667, "x": -74.4, "y": 17.54, "curve": [0.883, -74.4, 0.917, -74.74, 0.883, 17.54, 0.917, 0.77]}, {"time": 0.9333, "x": -74.74, "y": 0.77, "curve": [0.95, -74.74, 0.983, -74.4, 0.95, 0.77, 0.983, 17.54]}, {"time": 1, "x": -74.4, "y": 17.54, "curve": [1.017, -74.4, 1.05, -74.74, 1.017, 17.54, 1.05, 0.77]}, {"time": 1.0667, "x": -74.74, "y": 0.77, "curve": [1.083, -74.74, 1.117, -74.4, 1.083, 0.77, 1.117, 17.54]}, {"time": 1.1333, "x": -74.4, "y": 17.54, "curve": [1.15, -74.4, 1.183, -74.74, 1.15, 17.54, 1.183, 0.77]}, {"time": 1.2, "x": -74.74, "y": 0.77, "curve": [1.217, -74.74, 1.25, -74.4, 1.217, 0.77, 1.25, 17.54]}, {"time": 1.2667, "x": -74.4, "y": 17.54, "curve": [1.283, -74.4, 1.317, -74.74, 1.283, 17.54, 1.317, 0.77]}, {"time": 1.3333, "x": -74.74, "y": 0.77, "curve": [1.35, -74.74, 1.383, -74.4, 1.35, 0.77, 1.383, 17.54]}, {"time": 1.4, "x": -74.4, "y": 17.54, "curve": [1.417, -74.4, 1.45, -74.74, 1.417, 17.54, 1.45, 0.77]}, {"time": 1.4667, "x": -74.74, "y": 0.77, "curve": [1.483, -74.74, 1.517, -74.4, 1.483, 0.77, 1.517, 17.54]}, {"time": 1.5333, "x": -74.4, "y": 17.54, "curve": [1.55, -74.4, 1.583, -74.74, 1.55, 17.54, 1.583, 0.77]}, {"time": 1.6, "x": -74.74, "y": 0.77, "curve": [1.617, -74.74, 1.65, -74.4, 1.617, 0.77, 1.65, 17.54]}, {"time": 1.6667, "x": -74.4, "y": 17.54, "curve": [1.683, -74.4, 1.717, -74.74, 1.683, 17.54, 1.717, 0.77]}, {"time": 1.7333, "x": -74.74, "y": 0.77}]}, "HornBtmLeft": {"rotate": [{"value": 3.6, "curve": [0.108, 3.6, 0.325, -9.35]}, {"time": 0.4333, "value": -9.35, "curve": [0.658, -9.35, 0.707, 4.81]}, {"time": 1.3333, "value": 4.81}]}, "HornTopLeft": {"rotate": [{"value": 2.87, "curve": [0.067, 2.87, 0.2, -10.54]}, {"time": 0.2667, "value": -10.54, "curve": [0.533, -10.54, 0.59, 4.08]}, {"time": 1.3333, "value": 4.08}]}, "HornTopRight": {"rotate": [{"value": -2.67, "curve": [0.067, -2.67, 0.2, 13.3]}, {"time": 0.2667, "value": 13.3, "curve": [0.533, 13.3, 0.59, -3.65]}, {"time": 1.3333, "value": -3.65}]}, "FACEBOB": {"translate": [{"x": -6, "y": 0.1}]}, "Drips2": {"translate": [{"x": 0.14, "y": 7.93}]}}}, "anticipate-summon": {"slots": {"EyeLeft": {"attachment": [{"name": "images/Beholder1_Eye_1_Closed"}]}, "EyeRight": {"attachment": [{"name": "images/Beholder1_Eye_1_Closed"}]}, "Eye_Middle": {"attachment": [{"name": "images/Beholder1_Eye_2_Closed"}]}}, "bones": {"Main": {"rotate": [{"value": 0.13}], "translate": [{"curve": [0.083, 0, 0.25, 0, 0.083, 0, 0.25, -52.82]}, {"time": 0.3333, "y": -52.82, "curve": [0.417, 0, 0.583, 0, 0.417, -52.82, 0.583, 0]}, {"time": 0.6667, "curve": [0.75, 0, 0.917, 0, 0.75, 0, 0.917, -52.82]}, {"time": 1, "y": -52.82, "curve": [1.083, 0, 1.25, 0, 1.083, -52.82, 1.25, 0]}, {"time": 1.3333, "curve": [1.417, 0, 1.583, 0, 1.417, 0, 1.583, -52.82]}, {"time": 1.6667, "y": -52.82, "curve": [1.75, 0, 1.917, 0, 1.75, -52.82, 1.917, 0]}, {"time": 2}], "scale": [{"x": 0.849, "y": 1.391, "curve": [0.025, 1.05, 0.05, 1.239, 0.025, 1.103, 0.05, 0.833]}, {"time": 0.0667, "x": 1.239, "y": 0.833, "curve": [0.117, 1.01, 0.166, 0.795, 0.117, 0.99, 0.166, 1.137]}, {"time": 0.2, "x": 0.795, "y": 1.137, "curve": [0.267, 0.795, 0.4, 1.004, 0.267, 1.137, 0.4, 0.987]}, {"time": 0.4667, "x": 1.004, "y": 0.987, "curve": [0.517, 0.896, 0.566, 0.795, 0.517, 1.064, 0.566, 1.137]}, {"time": 0.6, "x": 0.795, "y": 1.137, "curve": [0.617, 0.795, 0.65, 0.883, 0.617, 1.137, 0.65, 1.067]}, {"time": 0.6667, "x": 0.883, "y": 1.067, "curve": [0.683, 0.883, 0.717, 0.795, 0.683, 1.067, 0.717, 1.137]}, {"time": 0.7333, "x": 0.795, "y": 1.137, "curve": [0.75, 0.795, 0.783, 0.883, 0.75, 1.137, 0.783, 1.067]}, {"time": 0.8, "x": 0.883, "y": 1.067, "curve": [0.817, 0.883, 0.85, 0.795, 0.817, 1.067, 0.85, 1.137]}, {"time": 0.8667, "x": 0.795, "y": 1.137, "curve": [0.883, 0.795, 0.917, 0.883, 0.883, 1.137, 0.917, 1.067]}, {"time": 0.9333, "x": 0.883, "y": 1.067, "curve": [0.95, 0.883, 0.983, 0.795, 0.95, 1.067, 0.983, 1.137]}, {"time": 1, "x": 0.795, "y": 1.137, "curve": [1.017, 0.795, 1.05, 0.883, 1.017, 1.137, 1.05, 1.067]}, {"time": 1.0667, "x": 0.883, "y": 1.067, "curve": [1.083, 0.883, 1.117, 0.795, 1.083, 1.067, 1.117, 1.137]}, {"time": 1.1333, "x": 0.795, "y": 1.137, "curve": [1.15, 0.795, 1.183, 0.883, 1.15, 1.137, 1.183, 1.067]}, {"time": 1.2, "x": 0.883, "y": 1.067, "curve": [1.217, 0.883, 1.25, 0.795, 1.217, 1.067, 1.25, 1.137]}, {"time": 1.2667, "x": 0.795, "y": 1.137, "curve": [1.283, 0.795, 1.317, 0.883, 1.283, 1.137, 1.317, 1.067]}, {"time": 1.3333, "x": 0.883, "y": 1.067, "curve": [1.35, 0.883, 1.383, 0.795, 1.35, 1.067, 1.383, 1.137]}, {"time": 1.4, "x": 0.795, "y": 1.137, "curve": [1.417, 0.795, 1.45, 0.883, 1.417, 1.137, 1.45, 1.067]}, {"time": 1.4667, "x": 0.883, "y": 1.067, "curve": [1.483, 0.883, 1.517, 0.795, 1.483, 1.067, 1.517, 1.137]}, {"time": 1.5333, "x": 0.795, "y": 1.137, "curve": [1.55, 0.795, 1.583, 0.883, 1.55, 1.137, 1.583, 1.067]}, {"time": 1.6, "x": 0.883, "y": 1.067, "curve": [1.617, 0.883, 1.65, 0.795, 1.617, 1.067, 1.65, 1.137]}, {"time": 1.6667, "x": 0.795, "y": 1.137, "curve": [1.683, 0.795, 1.717, 0.883, 1.683, 1.137, 1.717, 1.067]}, {"time": 1.7333, "x": 0.883, "y": 1.067, "curve": [1.75, 0.883, 1.783, 0.795, 1.75, 1.067, 1.783, 1.137]}, {"time": 1.8, "x": 0.795, "y": 1.137, "curve": [1.817, 0.795, 1.85, 0.883, 1.817, 1.137, 1.85, 1.067]}, {"time": 1.8667, "x": 0.883, "y": 1.067, "curve": [1.883, 0.883, 1.917, 0.795, 1.883, 1.067, 1.917, 1.137]}, {"time": 1.9333, "x": 0.795, "y": 1.137, "curve": [1.95, 0.795, 1.983, 0.883, 1.95, 1.137, 1.983, 1.067]}, {"time": 2, "x": 0.883, "y": 1.067}]}, "FACE": {"translate": [{"x": 0.13, "y": 6.71, "curve": [0.025, 0.01, 0.049, -0.1, 0.025, 3.29, 0.049, 0.44]}, {"time": 0.0667, "x": -0.1, "y": 0.44, "curve": [0.083, -0.1, 0.117, 0.39, 0.083, 0.44, 0.117, 19.18]}, {"time": 0.1333, "x": 0.39, "y": 19.18, "curve": [0.15, 0.39, 0.183, -0.1, 0.15, 19.18, 0.183, 0.44]}, {"time": 0.2, "x": -0.1, "y": 0.44, "curve": [0.217, -0.1, 0.25, 0.39, 0.217, 0.44, 0.25, 19.18]}, {"time": 0.2667, "x": 0.39, "y": 19.18, "curve": [0.283, 0.39, 0.317, -0.1, 0.283, 19.18, 0.317, 0.44]}, {"time": 0.3333, "x": -0.1, "y": 0.44, "curve": [0.35, -0.1, 0.383, 0.39, 0.35, 0.44, 0.383, 19.18]}, {"time": 0.4, "x": 0.39, "y": 19.18, "curve": [0.417, 0.39, 0.45, -0.1, 0.417, 19.18, 0.45, 0.44]}, {"time": 0.4667, "x": -0.1, "y": 0.44, "curve": [0.483, -0.1, 0.517, 0.39, 0.483, 0.44, 0.517, 19.18]}, {"time": 0.5333, "x": 0.39, "y": 19.18, "curve": [0.55, 0.39, 0.583, -0.1, 0.55, 19.18, 0.583, 0.44]}, {"time": 0.6, "x": -0.1, "y": 0.44, "curve": [0.617, -0.1, 0.65, 0.39, 0.617, 0.44, 0.65, 19.18]}, {"time": 0.6667, "x": 0.39, "y": 19.18, "curve": [0.683, 0.39, 0.717, -0.1, 0.683, 19.18, 0.717, 0.44]}, {"time": 0.7333, "x": -0.1, "y": 0.44, "curve": [0.75, -0.1, 0.783, 0.39, 0.75, 0.44, 0.783, 19.18]}, {"time": 0.8, "x": 0.39, "y": 19.18, "curve": [0.817, 0.39, 0.85, -0.1, 0.817, 19.18, 0.85, 0.44]}, {"time": 0.8667, "x": -0.1, "y": 0.44, "curve": [0.883, -0.1, 0.917, 0.39, 0.883, 0.44, 0.917, 19.18]}, {"time": 0.9333, "x": 0.39, "y": 19.18, "curve": [0.95, 0.39, 0.983, -0.1, 0.95, 19.18, 0.983, 0.44]}, {"time": 1, "x": -0.1, "y": 0.44, "curve": [1.017, -0.1, 1.05, 0.39, 1.017, 0.44, 1.05, 19.18]}, {"time": 1.0667, "x": 0.39, "y": 19.18, "curve": [1.083, 0.39, 1.117, -0.1, 1.083, 19.18, 1.117, 0.44]}, {"time": 1.1333, "x": -0.1, "y": 0.44, "curve": [1.15, -0.1, 1.183, 0.39, 1.15, 0.44, 1.183, 19.18]}, {"time": 1.2, "x": 0.39, "y": 19.18, "curve": [1.217, 0.39, 1.25, -0.1, 1.217, 19.18, 1.25, 0.44]}, {"time": 1.2667, "x": -0.1, "y": 0.44, "curve": [1.283, -0.1, 1.317, 0.39, 1.283, 0.44, 1.317, 19.18]}, {"time": 1.3333, "x": 0.39, "y": 19.18, "curve": [1.35, 0.39, 1.383, -0.1, 1.35, 19.18, 1.383, 0.44]}, {"time": 1.4, "x": -0.1, "y": 0.44, "curve": [1.417, -0.1, 1.45, 0.39, 1.417, 0.44, 1.45, 19.18]}, {"time": 1.4667, "x": 0.39, "y": 19.18, "curve": [1.483, 0.39, 1.517, -0.1, 1.483, 19.18, 1.517, 0.44]}, {"time": 1.5333, "x": -0.1, "y": 0.44, "curve": [1.55, -0.1, 1.583, 0.39, 1.55, 0.44, 1.583, 19.18]}, {"time": 1.6, "x": 0.39, "y": 19.18, "curve": [1.617, 0.39, 1.65, -0.1, 1.617, 19.18, 1.65, 0.44]}, {"time": 1.6667, "x": -0.1, "y": 0.44, "curve": [1.683, -0.1, 1.717, 0.39, 1.683, 0.44, 1.717, 19.18]}, {"time": 1.7333, "x": 0.39, "y": 19.18, "curve": [1.75, 0.39, 1.783, -0.1, 1.75, 19.18, 1.783, 0.44]}, {"time": 1.8, "x": -0.1, "y": 0.44, "curve": [1.817, -0.1, 1.85, 0.39, 1.817, 0.44, 1.85, 19.18]}, {"time": 1.8667, "x": 0.39, "y": 19.18, "curve": [1.883, 0.39, 1.917, -0.1, 1.883, 19.18, 1.917, 0.44]}, {"time": 1.9333, "x": -0.1, "y": 0.44, "curve": [1.95, -0.1, 1.983, 0.39, 1.95, 0.44, 1.983, 19.18]}, {"time": 2, "x": 0.39, "y": 19.18}]}, "HornBtmLeft": {"rotate": [{"value": 3.6, "curve": [0.012, 3.71, 0.023, 3.76]}, {"time": 0.0333, "value": 3.76, "curve": [0.052, 3.76, 0.075, 3.42]}, {"time": 0.1, "value": 2.87, "curve": [0.185, 1.16, 0.302, -3.1]}, {"time": 0.3667, "value": -3.1, "curve": [0.441, -3.1, 0.584, 2.82]}, {"time": 0.6667, "value": 3.6, "curve": [0.678, 3.71, 0.69, 3.76]}, {"time": 0.7, "value": 3.76, "curve": [0.719, 3.76, 0.742, 3.42]}, {"time": 0.7667, "value": 2.87, "curve": [0.852, 1.16, 0.969, -3.1]}, {"time": 1.0333, "value": -3.1, "curve": [1.107, -3.1, 1.25, 2.82]}, {"time": 1.3333, "value": 3.6, "curve": [1.345, 3.71, 1.356, 3.76]}, {"time": 1.3667, "value": 3.76, "curve": [1.386, 3.76, 1.408, 3.42]}, {"time": 1.4333, "value": 2.87, "curve": [1.519, 1.16, 1.635, -3.1]}, {"time": 1.7, "value": -3.1, "curve": [1.774, -3.1, 1.917, 2.82]}, {"time": 2, "value": 3.6}]}, "HornTopLeft": {"rotate": [{"value": 2.87, "curve": [0.085, 1.16, 0.202, -3.1]}, {"time": 0.2667, "value": -3.1, "curve": [0.35, -3.1, 0.517, 3.76]}, {"time": 0.6, "value": 3.76, "curve": [0.619, 3.76, 0.642, 3.42]}, {"time": 0.6667, "value": 2.87, "curve": [0.752, 1.16, 0.869, -3.1]}, {"time": 0.9333, "value": -3.1, "curve": [1.017, -3.1, 1.183, 3.76]}, {"time": 1.2667, "value": 3.76, "curve": [1.286, 3.76, 1.308, 3.42]}, {"time": 1.3333, "value": 2.87, "curve": [1.419, 1.16, 1.535, -3.1]}, {"time": 1.6, "value": -3.1, "curve": [1.683, -3.1, 1.85, 3.76]}, {"time": 1.9333, "value": 3.76, "curve": [1.952, 3.76, 1.975, 3.42]}, {"time": 2, "value": 2.87}]}, "HornTopRight": {"rotate": [{"value": -2.67, "curve": [0.085, -1.1, 0.202, 2.82]}, {"time": 0.2667, "value": 2.82, "curve": [0.35, 2.82, 0.517, -3.49]}, {"time": 0.6, "value": -3.49, "curve": [0.619, -3.49, 0.642, -3.17]}, {"time": 0.6667, "value": -2.67, "curve": [0.752, -1.1, 0.869, 2.82]}, {"time": 0.9333, "value": 2.82, "curve": [1.017, 2.82, 1.183, -3.49]}, {"time": 1.2667, "value": -3.49, "curve": [1.286, -3.49, 1.308, -3.17]}, {"time": 1.3333, "value": -2.67, "curve": [1.419, -1.1, 1.535, 2.82]}, {"time": 1.6, "value": 2.82, "curve": [1.683, 2.82, 1.85, -3.49]}, {"time": 1.9333, "value": -3.49, "curve": [1.952, -3.49, 1.975, -3.17]}, {"time": 2, "value": -2.67}]}, "HornBtmRight": {"rotate": [{"value": -3.34, "curve": [0.012, -3.44, 0.023, -3.49]}, {"time": 0.0333, "value": -3.49, "curve": [0.052, -3.49, 0.075, -3.17]}, {"time": 0.1, "value": -2.67, "curve": [0.185, -1.1, 0.302, 2.82]}, {"time": 0.3667, "value": 2.82, "curve": [0.441, 2.82, 0.584, -2.62]}, {"time": 0.6667, "value": -3.34, "curve": [0.678, -3.44, 0.69, -3.49]}, {"time": 0.7, "value": -3.49, "curve": [0.719, -3.49, 0.742, -3.17]}, {"time": 0.7667, "value": -2.67, "curve": [0.852, -1.1, 0.969, 2.82]}, {"time": 1.0333, "value": 2.82, "curve": [1.107, 2.82, 1.25, -2.62]}, {"time": 1.3333, "value": -3.34, "curve": [1.345, -3.44, 1.356, -3.49]}, {"time": 1.3667, "value": -3.49, "curve": [1.386, -3.49, 1.408, -3.17]}, {"time": 1.4333, "value": -2.67, "curve": [1.519, -1.1, 1.635, 2.82]}, {"time": 1.7, "value": 2.82, "curve": [1.774, 2.82, 1.917, -2.62]}, {"time": 2, "value": -3.34}]}, "Drips1": {"translate": [{"x": 0.05, "y": 0.41, "curve": [0.073, -0.03, 0.151, -0.12, 0.073, -5.71, 0.151, -13.35]}, {"time": 0.2, "x": -0.12, "y": -13.35, "curve": [0.283, -0.12, 0.45, 0.14, 0.283, -13.35, 0.45, 8.42]}, {"time": 0.5333, "x": 0.14, "y": 8.42, "curve": [0.568, 0.14, 0.616, 0.1, 0.568, 8.42, 0.616, 4.78]}, {"time": 0.6667, "x": 0.05, "y": 0.41, "curve": [0.739, -0.03, 0.818, -0.12, 0.739, -5.71, 0.818, -13.35]}, {"time": 0.8667, "x": -0.12, "y": -13.35, "curve": [0.95, -0.12, 1.117, 0.14, 0.95, -13.35, 1.117, 8.42]}, {"time": 1.2, "x": 0.14, "y": 8.42, "curve": [1.234, 0.14, 1.282, 0.1, 1.234, 8.42, 1.282, 4.78]}, {"time": 1.3333, "x": 0.05, "y": 0.41, "curve": [1.406, -0.03, 1.484, -0.12, 1.406, -5.71, 1.484, -13.35]}, {"time": 1.5333, "x": -0.12, "y": -13.35, "curve": [1.617, -0.12, 1.783, 0.14, 1.617, -13.35, 1.783, 8.42]}, {"time": 1.8667, "x": 0.14, "y": 8.42, "curve": [1.901, 0.14, 1.949, 0.1, 1.901, 8.42, 1.949, 4.78]}, {"time": 2, "x": 0.05, "y": 0.41}]}, "FACEBOB": {"translate": [{"x": -6, "y": 0.1, "curve": [0.167, -6, 0.5, -50.88, 0.167, 0.1, 0.5, -10.91]}, {"time": 0.6667, "x": -50.88, "y": -10.91}]}, "Drips2": {"translate": [{"x": 0.14, "y": 7.93, "curve": [0.07, -0.03, 0.137, -0.12, 0.07, -5.93, 0.137, -13.35]}, {"time": 0.2, "x": -0.12, "y": -13.35, "curve": [0.283, -0.12, 0.45, 0.14, 0.283, -13.35, 0.45, 8.42]}, {"time": 0.5333, "x": 0.14, "y": 8.42, "curve": [0.568, 0.14, 0.616, 0.1, 0.568, 8.42, 0.616, 4.78]}, {"time": 0.6667, "x": 0.05, "y": 0.41, "curve": [0.739, -0.03, 0.818, -0.12, 0.739, -5.71, 0.818, -13.35]}, {"time": 0.8667, "x": -0.12, "y": -13.35, "curve": [0.95, -0.12, 1.117, 0.14, 0.95, -13.35, 1.117, 8.42]}, {"time": 1.2, "x": 0.14, "y": 8.42, "curve": [1.234, 0.14, 1.282, 0.1, 1.234, 8.42, 1.282, 4.78]}, {"time": 1.3333, "x": 0.05, "y": 0.41, "curve": [1.406, -0.03, 1.484, -0.12, 1.406, -5.71, 1.484, -13.35]}, {"time": 1.5333, "x": -0.12, "y": -13.35, "curve": [1.617, -0.12, 1.783, 0.14, 1.617, -13.35, 1.783, 8.42]}, {"time": 1.8667, "x": 0.14, "y": 8.42, "curve": [1.901, 0.14, 1.949, 0.1, 1.901, 8.42, 1.949, 4.78]}, {"time": 2, "x": 0.05, "y": 0.41}]}, "HornBtmLeft3b": {"rotate": [{"value": -37.17, "curve": [0.025, -42.43, 0.049, -46.37]}, {"time": 0.0667, "value": -46.37, "curve": [0.108, -46.37, 0.192, -13.95]}, {"time": 0.2333, "value": -13.95, "curve": [0.258, -13.95, 0.298, -27.88]}, {"time": 0.3333, "value": -37.17, "curve": [0.359, -42.43, 0.382, -46.37]}, {"time": 0.4, "value": -46.37, "curve": [0.442, -46.37, 0.525, -13.95]}, {"time": 0.5667, "value": -13.95, "curve": [0.591, -13.95, 0.632, -27.88]}, {"time": 0.6667, "value": -37.17, "curve": [0.692, -42.43, 0.716, -46.37]}, {"time": 0.7333, "value": -46.37, "curve": [0.775, -46.37, 0.858, -13.95]}, {"time": 0.9, "value": -13.95, "curve": [0.924, -13.95, 0.965, -27.88]}, {"time": 1, "value": -37.17, "curve": [1.025, -42.43, 1.049, -46.37]}, {"time": 1.0667, "value": -46.37, "curve": [1.108, -46.37, 1.192, -13.95]}, {"time": 1.2333, "value": -13.95, "curve": [1.258, -13.95, 1.298, -27.88]}, {"time": 1.3333, "value": -37.17, "curve": [1.359, -42.43, 1.382, -46.37]}, {"time": 1.4, "value": -46.37, "curve": [1.442, -46.37, 1.525, -13.95]}, {"time": 1.5667, "value": -13.95, "curve": [1.591, -13.95, 1.632, -27.88]}, {"time": 1.6667, "value": -37.17, "curve": [1.692, -42.43, 1.716, -46.37]}, {"time": 1.7333, "value": -46.37, "curve": [1.775, -46.37, 1.858, -13.95]}, {"time": 1.9, "value": -13.95, "curve": [1.924, -13.95, 1.965, -27.88]}, {"time": 2, "value": -37.17}]}, "HornBtmLeft2c": {"rotate": [{"value": -14.58, "curve": [0.037, -7.54, 0.075, 0]}, {"time": 0.1, "curve": [0.142, 0, 0.225, -26.8]}, {"time": 0.2667, "value": -26.8, "curve": [0.283, -26.8, 0.308, -20.89]}, {"time": 0.3333, "value": -14.58, "curve": [0.37, -7.54, 0.409, 0]}, {"time": 0.4333, "curve": [0.475, 0, 0.558, -26.8]}, {"time": 0.6, "value": -26.8, "curve": [0.617, -26.8, 0.641, -20.89]}, {"time": 0.6667, "value": -14.58, "curve": [0.704, -7.54, 0.742, 0]}, {"time": 0.7667, "curve": [0.808, 0, 0.892, -26.8]}, {"time": 0.9333, "value": -26.8, "curve": [0.95, -26.8, 0.975, -20.89]}, {"time": 1, "value": -14.58, "curve": [1.037, -7.54, 1.075, 0]}, {"time": 1.1, "curve": [1.142, 0, 1.225, -26.8]}, {"time": 1.2667, "value": -26.8, "curve": [1.283, -26.8, 1.308, -20.89]}, {"time": 1.3333, "value": -14.58, "curve": [1.37, -7.54, 1.409, 0]}, {"time": 1.4333, "curve": [1.475, 0, 1.558, -26.8]}, {"time": 1.6, "value": -26.8, "curve": [1.617, -26.8, 1.641, -20.89]}, {"time": 1.6667, "value": -14.58, "curve": [1.704, -7.54, 1.742, 0]}, {"time": 1.7667, "curve": [1.808, 0, 1.892, -26.8]}, {"time": 1.9333, "value": -26.8, "curve": [1.95, -26.8, 1.975, -20.89]}, {"time": 2, "value": -14.58}]}, "HornBtmLeft2d": {"rotate": [{"value": -32.04, "curve": [0.047, -11.54, 0.101, 19.21]}, {"time": 0.1333, "value": 19.21, "curve": [0.167, 19.21, 0.233, -86.31]}, {"time": 0.2667, "value": -86.31, "curve": [0.284, -86.31, 0.308, -63.05]}, {"time": 0.3333, "value": -32.04, "curve": [0.38, -11.54, 0.434, 19.21]}, {"time": 0.4667, "value": 19.21, "curve": [0.5, 19.21, 0.567, -86.31]}, {"time": 0.6, "value": -86.31, "curve": [0.618, -86.31, 0.641, -63.05]}, {"time": 0.6667, "value": -32.04, "curve": [0.714, -11.54, 0.768, 19.21]}, {"time": 0.8, "value": 19.21, "curve": [0.833, 19.21, 0.9, -86.31]}, {"time": 0.9333, "value": -86.31, "curve": [0.951, -86.31, 0.975, -63.05]}, {"time": 1, "value": -32.04, "curve": [1.047, -11.54, 1.101, 19.21]}, {"time": 1.1333, "value": 19.21, "curve": [1.167, 19.21, 1.233, -86.31]}, {"time": 1.2667, "value": -86.31, "curve": [1.284, -86.31, 1.308, -63.05]}, {"time": 1.3333, "value": -32.04, "curve": [1.38, -11.54, 1.434, 19.21]}, {"time": 1.4667, "value": 19.21, "curve": [1.5, 19.21, 1.567, -86.31]}, {"time": 1.6, "value": -86.31, "curve": [1.618, -86.31, 1.641, -63.05]}, {"time": 1.6667, "value": -32.04, "curve": [1.714, -11.54, 1.768, 19.21]}, {"time": 1.8, "value": 19.21, "curve": [1.833, 19.21, 1.9, -86.31]}, {"time": 1.9333, "value": -86.31, "curve": [1.951, -86.31, 1.975, -63.05]}, {"time": 2, "value": -32.04}]}, "HornBtmLeft3c": {"rotate": [{"value": 50.29, "curve": [0.037, 35.4, 0.075, 19.45]}, {"time": 0.1, "value": 19.45, "curve": [0.142, 19.45, 0.225, 76.14]}, {"time": 0.2667, "value": 76.14, "curve": [0.283, 76.14, 0.308, 63.64]}, {"time": 0.3333, "value": 50.29, "curve": [0.37, 35.4, 0.409, 19.45]}, {"time": 0.4333, "value": 19.45, "curve": [0.475, 19.45, 0.558, 76.14]}, {"time": 0.6, "value": 76.14, "curve": [0.617, 76.14, 0.641, 63.64]}, {"time": 0.6667, "value": 50.29, "curve": [0.704, 35.4, 0.742, 19.45]}, {"time": 0.7667, "value": 19.45, "curve": [0.808, 19.45, 0.892, 76.14]}, {"time": 0.9333, "value": 76.14, "curve": [0.95, 76.14, 0.975, 63.64]}, {"time": 1, "value": 50.29, "curve": [1.037, 35.4, 1.075, 19.45]}, {"time": 1.1, "value": 19.45, "curve": [1.142, 19.45, 1.225, 76.14]}, {"time": 1.2667, "value": 76.14, "curve": [1.283, 76.14, 1.308, 63.64]}, {"time": 1.3333, "value": 50.29, "curve": [1.37, 35.4, 1.409, 19.45]}, {"time": 1.4333, "value": 19.45, "curve": [1.475, 19.45, 1.558, 76.14]}, {"time": 1.6, "value": 76.14, "curve": [1.617, 76.14, 1.641, 63.64]}, {"time": 1.6667, "value": 50.29, "curve": [1.704, 35.4, 1.742, 19.45]}, {"time": 1.7667, "value": 19.45, "curve": [1.808, 19.45, 1.892, 76.14]}, {"time": 1.9333, "value": 76.14, "curve": [1.95, 76.14, 1.975, 63.64]}, {"time": 2, "value": 50.29}]}, "TentacleLeftTop": {"rotate": [{"value": 20.88, "curve": [0.042, 20.88, 0.125, -20.66]}, {"time": 0.1667, "value": -20.66, "curve": [0.208, -20.66, 0.292, 20.88]}, {"time": 0.3333, "value": 20.88, "curve": [0.375, 20.88, 0.458, -20.66]}, {"time": 0.5, "value": -20.66, "curve": [0.542, -20.66, 0.625, 20.88]}, {"time": 0.6667, "value": 20.88, "curve": [0.708, 20.88, 0.792, -20.66]}, {"time": 0.8333, "value": -20.66, "curve": [0.875, -20.66, 0.958, 20.88]}, {"time": 1, "value": 20.88, "curve": [1.042, 20.88, 1.125, -20.66]}, {"time": 1.1667, "value": -20.66, "curve": [1.208, -20.66, 1.292, 20.88]}, {"time": 1.3333, "value": 20.88, "curve": [1.375, 20.88, 1.458, -20.66]}, {"time": 1.5, "value": -20.66, "curve": [1.542, -20.66, 1.625, 20.88]}, {"time": 1.6667, "value": 20.88, "curve": [1.708, 20.88, 1.792, -20.66]}, {"time": 1.8333, "value": -20.66, "curve": [1.875, -20.66, 1.958, 20.88]}, {"time": 2, "value": 20.88}]}, "HornBtmLeft2b": {"rotate": [{"value": -1.5, "curve": [0.025, -0.64, 0.049, 0]}, {"time": 0.0667, "curve": [0.108, 0, 0.192, -5.3]}, {"time": 0.2333, "value": -5.3, "curve": [0.258, -5.3, 0.298, -3.02]}, {"time": 0.3333, "value": -1.5, "curve": [0.359, -0.64, 0.382, 0]}, {"time": 0.4, "curve": [0.442, 0, 0.525, -5.3]}, {"time": 0.5667, "value": -5.3, "curve": [0.591, -5.3, 0.632, -3.02]}, {"time": 0.6667, "value": -1.5, "curve": [0.692, -0.64, 0.716, 0]}, {"time": 0.7333, "curve": [0.775, 0, 0.858, -5.3]}, {"time": 0.9, "value": -5.3, "curve": [0.924, -5.3, 0.965, -3.02]}, {"time": 1, "value": -1.5, "curve": [1.025, -0.64, 1.049, 0]}, {"time": 1.0667, "curve": [1.108, 0, 1.192, -5.3]}, {"time": 1.2333, "value": -5.3, "curve": [1.258, -5.3, 1.298, -3.02]}, {"time": 1.3333, "value": -1.5, "curve": [1.359, -0.64, 1.382, 0]}, {"time": 1.4, "curve": [1.442, 0, 1.525, -5.3]}, {"time": 1.5667, "value": -5.3, "curve": [1.591, -5.3, 1.632, -3.02]}, {"time": 1.6667, "value": -1.5, "curve": [1.692, -0.64, 1.716, 0]}, {"time": 1.7333, "curve": [1.775, 0, 1.858, -5.3]}, {"time": 1.9, "value": -5.3, "curve": [1.924, -5.3, 1.965, -3.02]}, {"time": 2, "value": -1.5}]}, "TentacleLeftBtm": {"rotate": [{"curve": [0.042, 0, 0.125, 33.59]}, {"time": 0.1667, "value": 33.59, "curve": [0.208, 33.59, 0.292, 0]}, {"time": 0.3333, "curve": [0.375, 0, 0.458, 33.59]}, {"time": 0.5, "value": 33.59, "curve": [0.542, 33.59, 0.625, 0]}, {"time": 0.6667, "curve": [0.708, 0, 0.792, 33.59]}, {"time": 0.8333, "value": 33.59, "curve": [0.875, 33.59, 0.958, 0]}, {"time": 1, "curve": [1.042, 0, 1.125, 33.59]}, {"time": 1.1667, "value": 33.59, "curve": [1.208, 33.59, 1.292, 0]}, {"time": 1.3333, "curve": [1.375, 0, 1.458, 33.59]}, {"time": 1.5, "value": 33.59, "curve": [1.542, 33.59, 1.625, 0]}, {"time": 1.6667, "curve": [1.708, 0, 1.792, 33.59]}, {"time": 1.8333, "value": 33.59, "curve": [1.875, 33.59, 1.958, 0]}, {"time": 2}]}, "HornBtmLeft3d": {"rotate": [{"value": 89.42, "curve": [0.047, 60.75, 0.101, 17.74]}, {"time": 0.1333, "value": 17.74, "curve": [0.167, 17.74, 0.233, 117.82]}, {"time": 0.2667, "value": 117.82, "curve": [0.284, 117.82, 0.308, 105.65]}, {"time": 0.3333, "value": 89.42, "curve": [0.38, 60.75, 0.434, 17.74]}, {"time": 0.4667, "value": 17.74, "curve": [0.5, 17.74, 0.567, 117.82]}, {"time": 0.6, "value": 117.82, "curve": [0.618, 117.82, 0.641, 105.65]}, {"time": 0.6667, "value": 89.42, "curve": [0.714, 60.75, 0.768, 17.74]}, {"time": 0.8, "value": 17.74, "curve": [0.833, 17.74, 0.9, 117.82]}, {"time": 0.9333, "value": 117.82, "curve": [0.951, 117.82, 0.975, 105.65]}, {"time": 1, "value": 89.42, "curve": [1.047, 60.75, 1.101, 17.74]}, {"time": 1.1333, "value": 17.74, "curve": [1.167, 17.74, 1.233, 117.82]}, {"time": 1.2667, "value": 117.82, "curve": [1.284, 117.82, 1.308, 105.65]}, {"time": 1.3333, "value": 89.42, "curve": [1.38, 60.75, 1.434, 17.74]}, {"time": 1.4667, "value": 17.74, "curve": [1.5, 17.74, 1.567, 117.82]}, {"time": 1.6, "value": 117.82, "curve": [1.618, 117.82, 1.641, 105.65]}, {"time": 1.6667, "value": 89.42, "curve": [1.714, 60.75, 1.768, 17.74]}, {"time": 1.8, "value": 17.74, "curve": [1.833, 17.74, 1.9, 117.82]}, {"time": 1.9333, "value": 117.82, "curve": [1.951, 117.82, 1.975, 105.65]}, {"time": 2, "value": 89.42}]}, "HornBtmLeft3d2": {"rotate": [{"value": 89.42, "curve": [0.047, 60.75, 0.101, 17.74]}, {"time": 0.1333, "value": 17.74, "curve": [0.167, 17.74, 0.233, 117.82]}, {"time": 0.2667, "value": 117.82, "curve": [0.284, 117.82, 0.308, 105.65]}, {"time": 0.3333, "value": 89.42, "curve": [0.38, 60.75, 0.434, 17.74]}, {"time": 0.4667, "value": 17.74, "curve": [0.5, 17.74, 0.567, 117.82]}, {"time": 0.6, "value": 117.82, "curve": [0.618, 117.82, 0.641, 105.65]}, {"time": 0.6667, "value": 89.42, "curve": [0.714, 60.75, 0.768, 17.74]}, {"time": 0.8, "value": 17.74, "curve": [0.833, 17.74, 0.9, 117.82]}, {"time": 0.9333, "value": 117.82, "curve": [0.951, 117.82, 0.975, 105.65]}, {"time": 1, "value": 89.42, "curve": [1.047, 60.75, 1.101, 17.74]}, {"time": 1.1333, "value": 17.74, "curve": [1.167, 17.74, 1.233, 117.82]}, {"time": 1.2667, "value": 117.82, "curve": [1.284, 117.82, 1.308, 105.65]}, {"time": 1.3333, "value": 89.42, "curve": [1.38, 60.75, 1.434, 17.74]}, {"time": 1.4667, "value": 17.74, "curve": [1.5, 17.74, 1.567, 117.82]}, {"time": 1.6, "value": 117.82, "curve": [1.618, 117.82, 1.641, 105.65]}, {"time": 1.6667, "value": 89.42, "curve": [1.714, 60.75, 1.768, 17.74]}, {"time": 1.8, "value": 17.74, "curve": [1.833, 17.74, 1.9, 117.82]}, {"time": 1.9333, "value": 117.82, "curve": [1.951, 117.82, 1.975, 105.65]}, {"time": 2, "value": 89.42}]}, "HornBtmLeft3c2": {"rotate": [{"value": 50.29, "curve": [0.037, 35.4, 0.075, 19.45]}, {"time": 0.1, "value": 19.45, "curve": [0.142, 19.45, 0.225, 76.14]}, {"time": 0.2667, "value": 76.14, "curve": [0.283, 76.14, 0.308, 63.64]}, {"time": 0.3333, "value": 50.29, "curve": [0.37, 35.4, 0.409, 19.45]}, {"time": 0.4333, "value": 19.45, "curve": [0.475, 19.45, 0.558, 76.14]}, {"time": 0.6, "value": 76.14, "curve": [0.617, 76.14, 0.641, 63.64]}, {"time": 0.6667, "value": 50.29, "curve": [0.704, 35.4, 0.742, 19.45]}, {"time": 0.7667, "value": 19.45, "curve": [0.808, 19.45, 0.892, 76.14]}, {"time": 0.9333, "value": 76.14, "curve": [0.95, 76.14, 0.975, 63.64]}, {"time": 1, "value": 50.29, "curve": [1.037, 35.4, 1.075, 19.45]}, {"time": 1.1, "value": 19.45, "curve": [1.142, 19.45, 1.225, 76.14]}, {"time": 1.2667, "value": 76.14, "curve": [1.283, 76.14, 1.308, 63.64]}, {"time": 1.3333, "value": 50.29, "curve": [1.37, 35.4, 1.409, 19.45]}, {"time": 1.4333, "value": 19.45, "curve": [1.475, 19.45, 1.558, 76.14]}, {"time": 1.6, "value": 76.14, "curve": [1.617, 76.14, 1.641, 63.64]}, {"time": 1.6667, "value": 50.29, "curve": [1.704, 35.4, 1.742, 19.45]}, {"time": 1.7667, "value": 19.45, "curve": [1.808, 19.45, 1.892, 76.14]}, {"time": 1.9333, "value": 76.14, "curve": [1.95, 76.14, 1.975, 63.64]}, {"time": 2, "value": 50.29}]}, "HornBtmLeft3b2": {"rotate": [{"value": -37.17, "curve": [0.025, -42.43, 0.049, -46.37]}, {"time": 0.0667, "value": -46.37, "curve": [0.108, -46.37, 0.192, -13.95]}, {"time": 0.2333, "value": -13.95, "curve": [0.258, -13.95, 0.298, -27.88]}, {"time": 0.3333, "value": -37.17, "curve": [0.359, -42.43, 0.382, -46.37]}, {"time": 0.4, "value": -46.37, "curve": [0.442, -46.37, 0.525, -13.95]}, {"time": 0.5667, "value": -13.95, "curve": [0.591, -13.95, 0.632, -27.88]}, {"time": 0.6667, "value": -37.17, "curve": [0.692, -42.43, 0.716, -46.37]}, {"time": 0.7333, "value": -46.37, "curve": [0.775, -46.37, 0.858, -13.95]}, {"time": 0.9, "value": -13.95, "curve": [0.924, -13.95, 0.965, -27.88]}, {"time": 1, "value": -37.17, "curve": [1.025, -42.43, 1.049, -46.37]}, {"time": 1.0667, "value": -46.37, "curve": [1.108, -46.37, 1.192, -13.95]}, {"time": 1.2333, "value": -13.95, "curve": [1.258, -13.95, 1.298, -27.88]}, {"time": 1.3333, "value": -37.17, "curve": [1.359, -42.43, 1.382, -46.37]}, {"time": 1.4, "value": -46.37, "curve": [1.442, -46.37, 1.525, -13.95]}, {"time": 1.5667, "value": -13.95, "curve": [1.591, -13.95, 1.632, -27.88]}, {"time": 1.6667, "value": -37.17, "curve": [1.692, -42.43, 1.716, -46.37]}, {"time": 1.7333, "value": -46.37, "curve": [1.775, -46.37, 1.858, -13.95]}, {"time": 1.9, "value": -13.95, "curve": [1.924, -13.95, 1.965, -27.88]}, {"time": 2, "value": -37.17}]}, "TentacleLeftBtm2": {"rotate": [{"curve": [0.042, 0, 0.125, -26.24]}, {"time": 0.1667, "value": -26.24, "curve": [0.208, -26.24, 0.292, 0]}, {"time": 0.3333, "curve": [0.375, 0, 0.458, -26.24]}, {"time": 0.5, "value": -26.24, "curve": [0.542, -26.24, 0.625, 0]}, {"time": 0.6667, "curve": [0.708, 0, 0.792, -26.24]}, {"time": 0.8333, "value": -26.24, "curve": [0.875, -26.24, 0.958, 0]}, {"time": 1, "curve": [1.042, 0, 1.125, -26.24]}, {"time": 1.1667, "value": -26.24, "curve": [1.208, -26.24, 1.292, 0]}, {"time": 1.3333, "curve": [1.375, 0, 1.458, -26.24]}, {"time": 1.5, "value": -26.24, "curve": [1.542, -26.24, 1.625, 0]}, {"time": 1.6667, "curve": [1.708, 0, 1.792, -26.24]}, {"time": 1.8333, "value": -26.24, "curve": [1.875, -26.24, 1.958, 0]}, {"time": 2}]}, "HornBtmLeft2d2": {"rotate": [{"value": -32.04, "curve": [0.047, -11.54, 0.101, 19.21]}, {"time": 0.1333, "value": 19.21, "curve": [0.167, 19.21, 0.233, -80.52]}, {"time": 0.2667, "value": -80.52, "curve": [0.284, -80.52, 0.308, -59.74]}, {"time": 0.3333, "value": -32.04, "curve": [0.38, -11.54, 0.434, 19.21]}, {"time": 0.4667, "value": 19.21, "curve": [0.5, 19.21, 0.567, -80.52]}, {"time": 0.6, "value": -80.52, "curve": [0.618, -80.52, 0.641, -59.74]}, {"time": 0.6667, "value": -32.04, "curve": [0.714, -11.54, 0.768, 19.21]}, {"time": 0.8, "value": 19.21, "curve": [0.833, 19.21, 0.9, -80.52]}, {"time": 0.9333, "value": -80.52, "curve": [0.951, -80.52, 0.975, -59.74]}, {"time": 1, "value": -32.04, "curve": [1.047, -11.54, 1.101, 19.21]}, {"time": 1.1333, "value": 19.21, "curve": [1.167, 19.21, 1.233, -80.52]}, {"time": 1.2667, "value": -80.52, "curve": [1.284, -80.52, 1.308, -59.74]}, {"time": 1.3333, "value": -32.04, "curve": [1.38, -11.54, 1.434, 19.21]}, {"time": 1.4667, "value": 19.21, "curve": [1.5, 19.21, 1.567, -80.52]}, {"time": 1.6, "value": -80.52, "curve": [1.618, -80.52, 1.641, -59.74]}, {"time": 1.6667, "value": -32.04, "curve": [1.714, -11.54, 1.768, 19.21]}, {"time": 1.8, "value": 19.21, "curve": [1.833, 19.21, 1.9, -80.52]}, {"time": 1.9333, "value": -80.52, "curve": [1.951, -80.52, 1.975, -59.74]}, {"time": 2, "value": -32.04}]}, "HornBtmLeft2c2": {"rotate": [{"value": -14.58, "curve": [0.037, -7.54, 0.075, 0]}, {"time": 0.1, "curve": [0.142, 0, 0.225, -26.8]}, {"time": 0.2667, "value": -26.8, "curve": [0.283, -26.8, 0.308, -20.89]}, {"time": 0.3333, "value": -14.58, "curve": [0.37, -7.54, 0.409, 0]}, {"time": 0.4333, "curve": [0.475, 0, 0.558, -26.8]}, {"time": 0.6, "value": -26.8, "curve": [0.617, -26.8, 0.641, -20.89]}, {"time": 0.6667, "value": -14.58, "curve": [0.704, -7.54, 0.742, 0]}, {"time": 0.7667, "curve": [0.808, 0, 0.892, -26.8]}, {"time": 0.9333, "value": -26.8, "curve": [0.95, -26.8, 0.975, -20.89]}, {"time": 1, "value": -14.58, "curve": [1.037, -7.54, 1.075, 0]}, {"time": 1.1, "curve": [1.142, 0, 1.225, -26.8]}, {"time": 1.2667, "value": -26.8, "curve": [1.283, -26.8, 1.308, -20.89]}, {"time": 1.3333, "value": -14.58, "curve": [1.37, -7.54, 1.409, 0]}, {"time": 1.4333, "curve": [1.475, 0, 1.558, -26.8]}, {"time": 1.6, "value": -26.8, "curve": [1.617, -26.8, 1.641, -20.89]}, {"time": 1.6667, "value": -14.58, "curve": [1.704, -7.54, 1.742, 0]}, {"time": 1.7667, "curve": [1.808, 0, 1.892, -26.8]}, {"time": 1.9333, "value": -26.8, "curve": [1.95, -26.8, 1.975, -20.89]}, {"time": 2, "value": -14.58}]}, "HornBtmLeft2b2": {"rotate": [{"value": -1.5, "curve": [0.025, -0.64, 0.049, 0]}, {"time": 0.0667, "curve": [0.108, 0, 0.192, -5.3]}, {"time": 0.2333, "value": -5.3, "curve": [0.258, -5.3, 0.298, -3.02]}, {"time": 0.3333, "value": -1.5, "curve": [0.359, -0.64, 0.382, 0]}, {"time": 0.4, "curve": [0.442, 0, 0.525, -5.3]}, {"time": 0.5667, "value": -5.3, "curve": [0.591, -5.3, 0.632, -3.02]}, {"time": 0.6667, "value": -1.5, "curve": [0.692, -0.64, 0.716, 0]}, {"time": 0.7333, "curve": [0.775, 0, 0.858, -5.3]}, {"time": 0.9, "value": -5.3, "curve": [0.924, -5.3, 0.965, -3.02]}, {"time": 1, "value": -1.5, "curve": [1.025, -0.64, 1.049, 0]}, {"time": 1.0667, "curve": [1.108, 0, 1.192, -5.3]}, {"time": 1.2333, "value": -5.3, "curve": [1.258, -5.3, 1.298, -3.02]}, {"time": 1.3333, "value": -1.5, "curve": [1.359, -0.64, 1.382, 0]}, {"time": 1.4, "curve": [1.442, 0, 1.525, -5.3]}, {"time": 1.5667, "value": -5.3, "curve": [1.591, -5.3, 1.632, -3.02]}, {"time": 1.6667, "value": -1.5, "curve": [1.692, -0.64, 1.716, 0]}, {"time": 1.7333, "curve": [1.775, 0, 1.858, -5.3]}, {"time": 1.9, "value": -5.3, "curve": [1.924, -5.3, 1.965, -3.02]}, {"time": 2, "value": -1.5}]}, "TentacleLeftTop2": {"rotate": [{"value": -32.65, "curve": [0.042, -32.65, 0.125, 8.41]}, {"time": 0.1667, "value": 8.41, "curve": [0.208, 8.41, 0.292, -32.65]}, {"time": 0.3333, "value": -32.65, "curve": [0.375, -32.65, 0.458, 8.41]}, {"time": 0.5, "value": 8.41, "curve": [0.542, 8.41, 0.625, -32.65]}, {"time": 0.6667, "value": -32.65, "curve": [0.708, -32.65, 0.792, 8.41]}, {"time": 0.8333, "value": 8.41, "curve": [0.875, 8.41, 0.958, -32.65]}, {"time": 1, "value": -32.65, "curve": [1.042, -32.65, 1.125, 8.41]}, {"time": 1.1667, "value": 8.41, "curve": [1.208, 8.41, 1.292, -32.65]}, {"time": 1.3333, "value": -32.65, "curve": [1.375, -32.65, 1.458, 8.41]}, {"time": 1.5, "value": 8.41, "curve": [1.542, 8.41, 1.625, -32.65]}, {"time": 1.6667, "value": -32.65, "curve": [1.708, -32.65, 1.792, 8.41]}, {"time": 1.8333, "value": 8.41, "curve": [1.875, 8.41, 1.958, -32.65]}, {"time": 2, "value": -32.65}]}}}, "anticipate-summon2": {"slots": {"EyeLeft": {"attachment": [{"name": "images/Beholder1_Eye_1_Closed"}]}, "EyeRight": {"attachment": [{"name": "images/Beholder1_Eye_1_Closed"}]}, "Eye_Middle": {"attachment": [{"name": "images/Beholder1_Eye_2_Closed"}]}}, "bones": {"Main": {"rotate": [{"value": 0.13}], "translate": [{"curve": [0.083, 0, 0.25, 0, 0.083, 0, 0.25, -52.82]}, {"time": 0.3333, "y": -52.82, "curve": [0.417, 0, 0.583, 0, 0.417, -52.82, 0.583, 0]}, {"time": 0.6667, "curve": [0.75, 0, 0.917, 0, 0.75, 0, 0.917, -52.82]}, {"time": 1, "y": -52.82, "curve": [1.083, 0, 1.25, 0, 1.083, -52.82, 1.25, 0]}, {"time": 1.3333, "curve": [1.417, 0, 1.583, 0, 1.417, 0, 1.583, -52.82]}, {"time": 1.6667, "y": -52.82, "curve": [1.75, 0, 1.917, 0, 1.75, -52.82, 1.917, 0]}, {"time": 2}], "scale": [{"x": 0.883, "y": 1.067, "curve": [0.017, 0.883, 0.05, 0.795, 0.017, 1.067, 0.05, 1.137]}, {"time": 0.0667, "x": 0.795, "y": 1.137, "curve": [0.083, 0.795, 0.117, 0.883, 0.083, 1.137, 0.117, 1.067]}, {"time": 0.1333, "x": 0.883, "y": 1.067, "curve": [0.15, 0.883, 0.183, 0.795, 0.15, 1.067, 0.183, 1.137]}, {"time": 0.2, "x": 0.795, "y": 1.137, "curve": [0.217, 0.795, 0.25, 0.883, 0.217, 1.137, 0.25, 1.067]}, {"time": 0.2667, "x": 0.883, "y": 1.067, "curve": [0.283, 0.883, 0.317, 0.795, 0.283, 1.067, 0.317, 1.137]}, {"time": 0.3333, "x": 0.795, "y": 1.137, "curve": [0.35, 0.795, 0.383, 0.883, 0.35, 1.137, 0.383, 1.067]}, {"time": 0.4, "x": 0.883, "y": 1.067, "curve": [0.417, 0.883, 0.45, 0.795, 0.417, 1.067, 0.45, 1.137]}, {"time": 0.4667, "x": 0.795, "y": 1.137, "curve": [0.483, 0.795, 0.517, 0.883, 0.483, 1.137, 0.517, 1.067]}, {"time": 0.5333, "x": 0.883, "y": 1.067, "curve": [0.55, 0.883, 0.583, 0.795, 0.55, 1.067, 0.583, 1.137]}, {"time": 0.6, "x": 0.795, "y": 1.137, "curve": [0.617, 0.795, 0.65, 0.883, 0.617, 1.137, 0.65, 1.067]}, {"time": 0.6667, "x": 0.883, "y": 1.067, "curve": [0.683, 0.883, 0.717, 0.795, 0.683, 1.067, 0.717, 1.137]}, {"time": 0.7333, "x": 0.795, "y": 1.137, "curve": [0.75, 0.795, 0.783, 0.883, 0.75, 1.137, 0.783, 1.067]}, {"time": 0.8, "x": 0.883, "y": 1.067, "curve": [0.817, 0.883, 0.85, 0.795, 0.817, 1.067, 0.85, 1.137]}, {"time": 0.8667, "x": 0.795, "y": 1.137, "curve": [0.883, 0.795, 0.917, 0.883, 0.883, 1.137, 0.917, 1.067]}, {"time": 0.9333, "x": 0.883, "y": 1.067, "curve": [0.95, 0.883, 0.983, 0.795, 0.95, 1.067, 0.983, 1.137]}, {"time": 1, "x": 0.795, "y": 1.137, "curve": [1.017, 0.795, 1.05, 0.883, 1.017, 1.137, 1.05, 1.067]}, {"time": 1.0667, "x": 0.883, "y": 1.067, "curve": [1.083, 0.883, 1.117, 0.795, 1.083, 1.067, 1.117, 1.137]}, {"time": 1.1333, "x": 0.795, "y": 1.137, "curve": [1.15, 0.795, 1.183, 0.883, 1.15, 1.137, 1.183, 1.067]}, {"time": 1.2, "x": 0.883, "y": 1.067, "curve": [1.217, 0.883, 1.25, 0.795, 1.217, 1.067, 1.25, 1.137]}, {"time": 1.2667, "x": 0.795, "y": 1.137, "curve": [1.283, 0.795, 1.317, 0.883, 1.283, 1.137, 1.317, 1.067]}, {"time": 1.3333, "x": 0.883, "y": 1.067, "curve": [1.35, 0.883, 1.383, 0.795, 1.35, 1.067, 1.383, 1.137]}, {"time": 1.4, "x": 0.795, "y": 1.137, "curve": [1.417, 0.795, 1.45, 0.883, 1.417, 1.137, 1.45, 1.067]}, {"time": 1.4667, "x": 0.883, "y": 1.067, "curve": [1.483, 0.883, 1.517, 0.795, 1.483, 1.067, 1.517, 1.137]}, {"time": 1.5333, "x": 0.795, "y": 1.137, "curve": [1.55, 0.795, 1.583, 0.883, 1.55, 1.137, 1.583, 1.067]}, {"time": 1.6, "x": 0.883, "y": 1.067, "curve": [1.617, 0.883, 1.65, 0.795, 1.617, 1.067, 1.65, 1.137]}, {"time": 1.6667, "x": 0.795, "y": 1.137, "curve": [1.683, 0.795, 1.717, 0.883, 1.683, 1.137, 1.717, 1.067]}, {"time": 1.7333, "x": 0.883, "y": 1.067, "curve": [1.75, 0.883, 1.783, 0.795, 1.75, 1.067, 1.783, 1.137]}, {"time": 1.8, "x": 0.795, "y": 1.137, "curve": [1.817, 0.795, 1.85, 0.883, 1.817, 1.137, 1.85, 1.067]}, {"time": 1.8667, "x": 0.883, "y": 1.067, "curve": [1.883, 0.883, 1.917, 0.795, 1.883, 1.067, 1.917, 1.137]}, {"time": 1.9333, "x": 0.795, "y": 1.137, "curve": [1.95, 0.795, 1.983, 0.883, 1.95, 1.137, 1.983, 1.067]}, {"time": 2, "x": 0.883, "y": 1.067}]}, "FACE": {"translate": [{"x": 0.39, "y": 19.18, "curve": [0.017, 0.39, 0.05, -0.1, 0.017, 19.18, 0.05, 0.44]}, {"time": 0.0667, "x": -0.1, "y": 0.44, "curve": [0.083, -0.1, 0.117, 0.39, 0.083, 0.44, 0.117, 19.18]}, {"time": 0.1333, "x": 0.39, "y": 19.18, "curve": [0.15, 0.39, 0.183, -0.1, 0.15, 19.18, 0.183, 0.44]}, {"time": 0.2, "x": -0.1, "y": 0.44, "curve": [0.217, -0.1, 0.25, 0.39, 0.217, 0.44, 0.25, 19.18]}, {"time": 0.2667, "x": 0.39, "y": 19.18, "curve": [0.283, 0.39, 0.317, -0.1, 0.283, 19.18, 0.317, 0.44]}, {"time": 0.3333, "x": -0.1, "y": 0.44, "curve": [0.35, -0.1, 0.383, 0.39, 0.35, 0.44, 0.383, 19.18]}, {"time": 0.4, "x": 0.39, "y": 19.18, "curve": [0.417, 0.39, 0.45, -0.1, 0.417, 19.18, 0.45, 0.44]}, {"time": 0.4667, "x": -0.1, "y": 0.44, "curve": [0.483, -0.1, 0.517, 0.39, 0.483, 0.44, 0.517, 19.18]}, {"time": 0.5333, "x": 0.39, "y": 19.18, "curve": [0.55, 0.39, 0.583, -0.1, 0.55, 19.18, 0.583, 0.44]}, {"time": 0.6, "x": -0.1, "y": 0.44, "curve": [0.617, -0.1, 0.65, 0.39, 0.617, 0.44, 0.65, 19.18]}, {"time": 0.6667, "x": 0.39, "y": 19.18, "curve": [0.683, 0.39, 0.717, -0.1, 0.683, 19.18, 0.717, 0.44]}, {"time": 0.7333, "x": -0.1, "y": 0.44, "curve": [0.75, -0.1, 0.783, 0.39, 0.75, 0.44, 0.783, 19.18]}, {"time": 0.8, "x": 0.39, "y": 19.18, "curve": [0.817, 0.39, 0.85, -0.1, 0.817, 19.18, 0.85, 0.44]}, {"time": 0.8667, "x": -0.1, "y": 0.44, "curve": [0.883, -0.1, 0.917, 0.39, 0.883, 0.44, 0.917, 19.18]}, {"time": 0.9333, "x": 0.39, "y": 19.18, "curve": [0.95, 0.39, 0.983, -0.1, 0.95, 19.18, 0.983, 0.44]}, {"time": 1, "x": -0.1, "y": 0.44, "curve": [1.017, -0.1, 1.05, 0.39, 1.017, 0.44, 1.05, 19.18]}, {"time": 1.0667, "x": 0.39, "y": 19.18, "curve": [1.083, 0.39, 1.117, -0.1, 1.083, 19.18, 1.117, 0.44]}, {"time": 1.1333, "x": -0.1, "y": 0.44, "curve": [1.15, -0.1, 1.183, 0.39, 1.15, 0.44, 1.183, 19.18]}, {"time": 1.2, "x": 0.39, "y": 19.18, "curve": [1.217, 0.39, 1.25, -0.1, 1.217, 19.18, 1.25, 0.44]}, {"time": 1.2667, "x": -0.1, "y": 0.44, "curve": [1.283, -0.1, 1.317, 0.39, 1.283, 0.44, 1.317, 19.18]}, {"time": 1.3333, "x": 0.39, "y": 19.18, "curve": [1.35, 0.39, 1.383, -0.1, 1.35, 19.18, 1.383, 0.44]}, {"time": 1.4, "x": -0.1, "y": 0.44, "curve": [1.417, -0.1, 1.45, 0.39, 1.417, 0.44, 1.45, 19.18]}, {"time": 1.4667, "x": 0.39, "y": 19.18, "curve": [1.483, 0.39, 1.517, -0.1, 1.483, 19.18, 1.517, 0.44]}, {"time": 1.5333, "x": -0.1, "y": 0.44, "curve": [1.55, -0.1, 1.583, 0.39, 1.55, 0.44, 1.583, 19.18]}, {"time": 1.6, "x": 0.39, "y": 19.18, "curve": [1.617, 0.39, 1.65, -0.1, 1.617, 19.18, 1.65, 0.44]}, {"time": 1.6667, "x": -0.1, "y": 0.44, "curve": [1.683, -0.1, 1.717, 0.39, 1.683, 0.44, 1.717, 19.18]}, {"time": 1.7333, "x": 0.39, "y": 19.18, "curve": [1.75, 0.39, 1.783, -0.1, 1.75, 19.18, 1.783, 0.44]}, {"time": 1.8, "x": -0.1, "y": 0.44, "curve": [1.817, -0.1, 1.85, 0.39, 1.817, 0.44, 1.85, 19.18]}, {"time": 1.8667, "x": 0.39, "y": 19.18, "curve": [1.883, 0.39, 1.917, -0.1, 1.883, 19.18, 1.917, 0.44]}, {"time": 1.9333, "x": -0.1, "y": 0.44, "curve": [1.95, -0.1, 1.983, 0.39, 1.95, 0.44, 1.983, 19.18]}, {"time": 2, "x": 0.39, "y": 19.18}]}, "HornBtmLeft": {"rotate": [{"value": 3.6, "curve": [0.012, 3.71, 0.023, 3.76]}, {"time": 0.0333, "value": 3.76, "curve": [0.052, 3.76, 0.075, 3.42]}, {"time": 0.1, "value": 2.87, "curve": [0.185, 1.16, 0.302, -3.1]}, {"time": 0.3667, "value": -3.1, "curve": [0.441, -3.1, 0.584, 2.82]}, {"time": 0.6667, "value": 3.6, "curve": [0.678, 3.71, 0.69, 3.76]}, {"time": 0.7, "value": 3.76, "curve": [0.719, 3.76, 0.742, 3.42]}, {"time": 0.7667, "value": 2.87, "curve": [0.852, 1.16, 0.969, -3.1]}, {"time": 1.0333, "value": -3.1, "curve": [1.107, -3.1, 1.25, 2.82]}, {"time": 1.3333, "value": 3.6, "curve": [1.345, 3.71, 1.356, 3.76]}, {"time": 1.3667, "value": 3.76, "curve": [1.386, 3.76, 1.408, 3.42]}, {"time": 1.4333, "value": 2.87, "curve": [1.519, 1.16, 1.635, -3.1]}, {"time": 1.7, "value": -3.1, "curve": [1.774, -3.1, 1.917, 2.82]}, {"time": 2, "value": 3.6}]}, "HornTopLeft": {"rotate": [{"value": 2.87, "curve": [0.085, 1.16, 0.202, -3.1]}, {"time": 0.2667, "value": -3.1, "curve": [0.35, -3.1, 0.517, 3.76]}, {"time": 0.6, "value": 3.76, "curve": [0.619, 3.76, 0.642, 3.42]}, {"time": 0.6667, "value": 2.87, "curve": [0.752, 1.16, 0.869, -3.1]}, {"time": 0.9333, "value": -3.1, "curve": [1.017, -3.1, 1.183, 3.76]}, {"time": 1.2667, "value": 3.76, "curve": [1.286, 3.76, 1.308, 3.42]}, {"time": 1.3333, "value": 2.87, "curve": [1.419, 1.16, 1.535, -3.1]}, {"time": 1.6, "value": -3.1, "curve": [1.683, -3.1, 1.85, 3.76]}, {"time": 1.9333, "value": 3.76, "curve": [1.952, 3.76, 1.975, 3.42]}, {"time": 2, "value": 2.87}]}, "HornTopRight": {"rotate": [{"value": -2.67, "curve": [0.085, -1.1, 0.202, 2.82]}, {"time": 0.2667, "value": 2.82, "curve": [0.35, 2.82, 0.517, -3.49]}, {"time": 0.6, "value": -3.49, "curve": [0.619, -3.49, 0.642, -3.17]}, {"time": 0.6667, "value": -2.67, "curve": [0.752, -1.1, 0.869, 2.82]}, {"time": 0.9333, "value": 2.82, "curve": [1.017, 2.82, 1.183, -3.49]}, {"time": 1.2667, "value": -3.49, "curve": [1.286, -3.49, 1.308, -3.17]}, {"time": 1.3333, "value": -2.67, "curve": [1.419, -1.1, 1.535, 2.82]}, {"time": 1.6, "value": 2.82, "curve": [1.683, 2.82, 1.85, -3.49]}, {"time": 1.9333, "value": -3.49, "curve": [1.952, -3.49, 1.975, -3.17]}, {"time": 2, "value": -2.67}]}, "HornBtmRight": {"rotate": [{"value": -3.34, "curve": [0.012, -3.44, 0.023, -3.49]}, {"time": 0.0333, "value": -3.49, "curve": [0.052, -3.49, 0.075, -3.17]}, {"time": 0.1, "value": -2.67, "curve": [0.185, -1.1, 0.302, 2.82]}, {"time": 0.3667, "value": 2.82, "curve": [0.441, 2.82, 0.584, -2.62]}, {"time": 0.6667, "value": -3.34, "curve": [0.678, -3.44, 0.69, -3.49]}, {"time": 0.7, "value": -3.49, "curve": [0.719, -3.49, 0.742, -3.17]}, {"time": 0.7667, "value": -2.67, "curve": [0.852, -1.1, 0.969, 2.82]}, {"time": 1.0333, "value": 2.82, "curve": [1.107, 2.82, 1.25, -2.62]}, {"time": 1.3333, "value": -3.34, "curve": [1.345, -3.44, 1.356, -3.49]}, {"time": 1.3667, "value": -3.49, "curve": [1.386, -3.49, 1.408, -3.17]}, {"time": 1.4333, "value": -2.67, "curve": [1.519, -1.1, 1.635, 2.82]}, {"time": 1.7, "value": 2.82, "curve": [1.774, 2.82, 1.917, -2.62]}, {"time": 2, "value": -3.34}]}, "Drips1": {"translate": [{"x": 0.05, "y": 0.41, "curve": [0.073, -0.03, 0.151, -0.12, 0.073, -5.71, 0.151, -13.35]}, {"time": 0.2, "x": -0.12, "y": -13.35, "curve": [0.283, -0.12, 0.45, 0.14, 0.283, -13.35, 0.45, 8.42]}, {"time": 0.5333, "x": 0.14, "y": 8.42, "curve": [0.568, 0.14, 0.616, 0.1, 0.568, 8.42, 0.616, 4.78]}, {"time": 0.6667, "x": 0.05, "y": 0.41, "curve": [0.739, -0.03, 0.818, -0.12, 0.739, -5.71, 0.818, -13.35]}, {"time": 0.8667, "x": -0.12, "y": -13.35, "curve": [0.95, -0.12, 1.117, 0.14, 0.95, -13.35, 1.117, 8.42]}, {"time": 1.2, "x": 0.14, "y": 8.42, "curve": [1.234, 0.14, 1.282, 0.1, 1.234, 8.42, 1.282, 4.78]}, {"time": 1.3333, "x": 0.05, "y": 0.41, "curve": [1.406, -0.03, 1.484, -0.12, 1.406, -5.71, 1.484, -13.35]}, {"time": 1.5333, "x": -0.12, "y": -13.35, "curve": [1.617, -0.12, 1.783, 0.14, 1.617, -13.35, 1.783, 8.42]}, {"time": 1.8667, "x": 0.14, "y": 8.42, "curve": [1.901, 0.14, 1.949, 0.1, 1.901, 8.42, 1.949, 4.78]}, {"time": 2, "x": 0.05, "y": 0.41}]}, "FACEBOB": {"translate": [{"x": -50.88, "y": -10.91}]}, "Drips2": {"translate": [{"x": 0.05, "y": 0.41, "curve": [0.073, -0.03, 0.151, -0.12, 0.073, -5.71, 0.151, -13.35]}, {"time": 0.2, "x": -0.12, "y": -13.35, "curve": [0.283, -0.12, 0.45, 0.14, 0.283, -13.35, 0.45, 8.42]}, {"time": 0.5333, "x": 0.14, "y": 8.42, "curve": [0.568, 0.14, 0.616, 0.1, 0.568, 8.42, 0.616, 4.78]}, {"time": 0.6667, "x": 0.05, "y": 0.41, "curve": [0.739, -0.03, 0.818, -0.12, 0.739, -5.71, 0.818, -13.35]}, {"time": 0.8667, "x": -0.12, "y": -13.35, "curve": [0.95, -0.12, 1.117, 0.14, 0.95, -13.35, 1.117, 8.42]}, {"time": 1.2, "x": 0.14, "y": 8.42, "curve": [1.234, 0.14, 1.282, 0.1, 1.234, 8.42, 1.282, 4.78]}, {"time": 1.3333, "x": 0.05, "y": 0.41, "curve": [1.406, -0.03, 1.484, -0.12, 1.406, -5.71, 1.484, -13.35]}, {"time": 1.5333, "x": -0.12, "y": -13.35, "curve": [1.617, -0.12, 1.783, 0.14, 1.617, -13.35, 1.783, 8.42]}, {"time": 1.8667, "x": 0.14, "y": 8.42, "curve": [1.901, 0.14, 1.949, 0.1, 1.901, 8.42, 1.949, 4.78]}, {"time": 2, "x": 0.05, "y": 0.41}]}, "HornBtmLeft3b": {"rotate": [{"value": -37.17, "curve": [0.025, -42.43, 0.049, -46.37]}, {"time": 0.0667, "value": -46.37, "curve": [0.108, -46.37, 0.192, -13.95]}, {"time": 0.2333, "value": -13.95, "curve": [0.258, -13.95, 0.298, -27.88]}, {"time": 0.3333, "value": -37.17, "curve": [0.359, -42.43, 0.382, -46.37]}, {"time": 0.4, "value": -46.37, "curve": [0.442, -46.37, 0.525, -13.95]}, {"time": 0.5667, "value": -13.95, "curve": [0.591, -13.95, 0.632, -27.88]}, {"time": 0.6667, "value": -37.17, "curve": [0.692, -42.43, 0.716, -46.37]}, {"time": 0.7333, "value": -46.37, "curve": [0.775, -46.37, 0.858, -13.95]}, {"time": 0.9, "value": -13.95, "curve": [0.924, -13.95, 0.965, -27.88]}, {"time": 1, "value": -37.17, "curve": [1.025, -42.43, 1.049, -46.37]}, {"time": 1.0667, "value": -46.37, "curve": [1.108, -46.37, 1.192, -13.95]}, {"time": 1.2333, "value": -13.95, "curve": [1.258, -13.95, 1.298, -27.88]}, {"time": 1.3333, "value": -37.17, "curve": [1.359, -42.43, 1.382, -46.37]}, {"time": 1.4, "value": -46.37, "curve": [1.442, -46.37, 1.525, -13.95]}, {"time": 1.5667, "value": -13.95, "curve": [1.591, -13.95, 1.632, -27.88]}, {"time": 1.6667, "value": -37.17, "curve": [1.692, -42.43, 1.716, -46.37]}, {"time": 1.7333, "value": -46.37, "curve": [1.775, -46.37, 1.858, -13.95]}, {"time": 1.9, "value": -13.95, "curve": [1.924, -13.95, 1.965, -27.88]}, {"time": 2, "value": -37.17}]}, "HornBtmLeft2c": {"rotate": [{"value": -14.58, "curve": [0.037, -7.54, 0.075, 0]}, {"time": 0.1, "curve": [0.142, 0, 0.225, -26.8]}, {"time": 0.2667, "value": -26.8, "curve": [0.283, -26.8, 0.308, -20.89]}, {"time": 0.3333, "value": -14.58, "curve": [0.37, -7.54, 0.409, 0]}, {"time": 0.4333, "curve": [0.475, 0, 0.558, -26.8]}, {"time": 0.6, "value": -26.8, "curve": [0.617, -26.8, 0.641, -20.89]}, {"time": 0.6667, "value": -14.58, "curve": [0.704, -7.54, 0.742, 0]}, {"time": 0.7667, "curve": [0.808, 0, 0.892, -26.8]}, {"time": 0.9333, "value": -26.8, "curve": [0.95, -26.8, 0.975, -20.89]}, {"time": 1, "value": -14.58, "curve": [1.037, -7.54, 1.075, 0]}, {"time": 1.1, "curve": [1.142, 0, 1.225, -26.8]}, {"time": 1.2667, "value": -26.8, "curve": [1.283, -26.8, 1.308, -20.89]}, {"time": 1.3333, "value": -14.58, "curve": [1.37, -7.54, 1.409, 0]}, {"time": 1.4333, "curve": [1.475, 0, 1.558, -26.8]}, {"time": 1.6, "value": -26.8, "curve": [1.617, -26.8, 1.641, -20.89]}, {"time": 1.6667, "value": -14.58, "curve": [1.704, -7.54, 1.742, 0]}, {"time": 1.7667, "curve": [1.808, 0, 1.892, -26.8]}, {"time": 1.9333, "value": -26.8, "curve": [1.95, -26.8, 1.975, -20.89]}, {"time": 2, "value": -14.58}]}, "HornBtmLeft2d": {"rotate": [{"value": -32.04, "curve": [0.047, -11.54, 0.101, 19.21]}, {"time": 0.1333, "value": 19.21, "curve": [0.167, 19.21, 0.233, -86.31]}, {"time": 0.2667, "value": -86.31, "curve": [0.284, -86.31, 0.308, -63.05]}, {"time": 0.3333, "value": -32.04, "curve": [0.38, -11.54, 0.434, 19.21]}, {"time": 0.4667, "value": 19.21, "curve": [0.5, 19.21, 0.567, -86.31]}, {"time": 0.6, "value": -86.31, "curve": [0.618, -86.31, 0.641, -63.05]}, {"time": 0.6667, "value": -32.04, "curve": [0.714, -11.54, 0.768, 19.21]}, {"time": 0.8, "value": 19.21, "curve": [0.833, 19.21, 0.9, -86.31]}, {"time": 0.9333, "value": -86.31, "curve": [0.951, -86.31, 0.975, -63.05]}, {"time": 1, "value": -32.04, "curve": [1.047, -11.54, 1.101, 19.21]}, {"time": 1.1333, "value": 19.21, "curve": [1.167, 19.21, 1.233, -86.31]}, {"time": 1.2667, "value": -86.31, "curve": [1.284, -86.31, 1.308, -63.05]}, {"time": 1.3333, "value": -32.04, "curve": [1.38, -11.54, 1.434, 19.21]}, {"time": 1.4667, "value": 19.21, "curve": [1.5, 19.21, 1.567, -86.31]}, {"time": 1.6, "value": -86.31, "curve": [1.618, -86.31, 1.641, -63.05]}, {"time": 1.6667, "value": -32.04, "curve": [1.714, -11.54, 1.768, 19.21]}, {"time": 1.8, "value": 19.21, "curve": [1.833, 19.21, 1.9, -86.31]}, {"time": 1.9333, "value": -86.31, "curve": [1.951, -86.31, 1.975, -63.05]}, {"time": 2, "value": -32.04}]}, "HornBtmLeft3c": {"rotate": [{"value": 50.29, "curve": [0.037, 35.4, 0.075, 19.45]}, {"time": 0.1, "value": 19.45, "curve": [0.142, 19.45, 0.225, 76.14]}, {"time": 0.2667, "value": 76.14, "curve": [0.283, 76.14, 0.308, 63.64]}, {"time": 0.3333, "value": 50.29, "curve": [0.37, 35.4, 0.409, 19.45]}, {"time": 0.4333, "value": 19.45, "curve": [0.475, 19.45, 0.558, 76.14]}, {"time": 0.6, "value": 76.14, "curve": [0.617, 76.14, 0.641, 63.64]}, {"time": 0.6667, "value": 50.29, "curve": [0.704, 35.4, 0.742, 19.45]}, {"time": 0.7667, "value": 19.45, "curve": [0.808, 19.45, 0.892, 76.14]}, {"time": 0.9333, "value": 76.14, "curve": [0.95, 76.14, 0.975, 63.64]}, {"time": 1, "value": 50.29, "curve": [1.037, 35.4, 1.075, 19.45]}, {"time": 1.1, "value": 19.45, "curve": [1.142, 19.45, 1.225, 76.14]}, {"time": 1.2667, "value": 76.14, "curve": [1.283, 76.14, 1.308, 63.64]}, {"time": 1.3333, "value": 50.29, "curve": [1.37, 35.4, 1.409, 19.45]}, {"time": 1.4333, "value": 19.45, "curve": [1.475, 19.45, 1.558, 76.14]}, {"time": 1.6, "value": 76.14, "curve": [1.617, 76.14, 1.641, 63.64]}, {"time": 1.6667, "value": 50.29, "curve": [1.704, 35.4, 1.742, 19.45]}, {"time": 1.7667, "value": 19.45, "curve": [1.808, 19.45, 1.892, 76.14]}, {"time": 1.9333, "value": 76.14, "curve": [1.95, 76.14, 1.975, 63.64]}, {"time": 2, "value": 50.29}]}, "TentacleLeftTop": {"rotate": [{"value": 20.88, "curve": [0.042, 20.88, 0.125, -20.66]}, {"time": 0.1667, "value": -20.66, "curve": [0.208, -20.66, 0.292, 20.88]}, {"time": 0.3333, "value": 20.88, "curve": [0.375, 20.88, 0.458, -20.66]}, {"time": 0.5, "value": -20.66, "curve": [0.542, -20.66, 0.625, 20.88]}, {"time": 0.6667, "value": 20.88, "curve": [0.708, 20.88, 0.792, -20.66]}, {"time": 0.8333, "value": -20.66, "curve": [0.875, -20.66, 0.958, 20.88]}, {"time": 1, "value": 20.88, "curve": [1.042, 20.88, 1.125, -20.66]}, {"time": 1.1667, "value": -20.66, "curve": [1.208, -20.66, 1.292, 20.88]}, {"time": 1.3333, "value": 20.88, "curve": [1.375, 20.88, 1.458, -20.66]}, {"time": 1.5, "value": -20.66, "curve": [1.542, -20.66, 1.625, 20.88]}, {"time": 1.6667, "value": 20.88, "curve": [1.708, 20.88, 1.792, -20.66]}, {"time": 1.8333, "value": -20.66, "curve": [1.875, -20.66, 1.958, 20.88]}, {"time": 2, "value": 20.88}]}, "HornBtmLeft2b": {"rotate": [{"value": -1.5, "curve": [0.025, -0.64, 0.049, 0]}, {"time": 0.0667, "curve": [0.108, 0, 0.192, -5.3]}, {"time": 0.2333, "value": -5.3, "curve": [0.258, -5.3, 0.298, -3.02]}, {"time": 0.3333, "value": -1.5, "curve": [0.359, -0.64, 0.382, 0]}, {"time": 0.4, "curve": [0.442, 0, 0.525, -5.3]}, {"time": 0.5667, "value": -5.3, "curve": [0.591, -5.3, 0.632, -3.02]}, {"time": 0.6667, "value": -1.5, "curve": [0.692, -0.64, 0.716, 0]}, {"time": 0.7333, "curve": [0.775, 0, 0.858, -5.3]}, {"time": 0.9, "value": -5.3, "curve": [0.924, -5.3, 0.965, -3.02]}, {"time": 1, "value": -1.5, "curve": [1.025, -0.64, 1.049, 0]}, {"time": 1.0667, "curve": [1.108, 0, 1.192, -5.3]}, {"time": 1.2333, "value": -5.3, "curve": [1.258, -5.3, 1.298, -3.02]}, {"time": 1.3333, "value": -1.5, "curve": [1.359, -0.64, 1.382, 0]}, {"time": 1.4, "curve": [1.442, 0, 1.525, -5.3]}, {"time": 1.5667, "value": -5.3, "curve": [1.591, -5.3, 1.632, -3.02]}, {"time": 1.6667, "value": -1.5, "curve": [1.692, -0.64, 1.716, 0]}, {"time": 1.7333, "curve": [1.775, 0, 1.858, -5.3]}, {"time": 1.9, "value": -5.3, "curve": [1.924, -5.3, 1.965, -3.02]}, {"time": 2, "value": -1.5}]}, "TentacleLeftBtm": {"rotate": [{"curve": [0.042, 0, 0.125, 33.59]}, {"time": 0.1667, "value": 33.59, "curve": [0.208, 33.59, 0.292, 0]}, {"time": 0.3333, "curve": [0.375, 0, 0.458, 33.59]}, {"time": 0.5, "value": 33.59, "curve": [0.542, 33.59, 0.625, 0]}, {"time": 0.6667, "curve": [0.708, 0, 0.792, 33.59]}, {"time": 0.8333, "value": 33.59, "curve": [0.875, 33.59, 0.958, 0]}, {"time": 1, "curve": [1.042, 0, 1.125, 33.59]}, {"time": 1.1667, "value": 33.59, "curve": [1.208, 33.59, 1.292, 0]}, {"time": 1.3333, "curve": [1.375, 0, 1.458, 33.59]}, {"time": 1.5, "value": 33.59, "curve": [1.542, 33.59, 1.625, 0]}, {"time": 1.6667, "curve": [1.708, 0, 1.792, 33.59]}, {"time": 1.8333, "value": 33.59, "curve": [1.875, 33.59, 1.958, 0]}, {"time": 2}]}, "HornBtmLeft3d": {"rotate": [{"value": 89.42, "curve": [0.047, 60.75, 0.101, 17.74]}, {"time": 0.1333, "value": 17.74, "curve": [0.167, 17.74, 0.233, 117.82]}, {"time": 0.2667, "value": 117.82, "curve": [0.284, 117.82, 0.308, 105.65]}, {"time": 0.3333, "value": 89.42, "curve": [0.38, 60.75, 0.434, 17.74]}, {"time": 0.4667, "value": 17.74, "curve": [0.5, 17.74, 0.567, 117.82]}, {"time": 0.6, "value": 117.82, "curve": [0.618, 117.82, 0.641, 105.65]}, {"time": 0.6667, "value": 89.42, "curve": [0.714, 60.75, 0.768, 17.74]}, {"time": 0.8, "value": 17.74, "curve": [0.833, 17.74, 0.9, 117.82]}, {"time": 0.9333, "value": 117.82, "curve": [0.951, 117.82, 0.975, 105.65]}, {"time": 1, "value": 89.42, "curve": [1.047, 60.75, 1.101, 17.74]}, {"time": 1.1333, "value": 17.74, "curve": [1.167, 17.74, 1.233, 117.82]}, {"time": 1.2667, "value": 117.82, "curve": [1.284, 117.82, 1.308, 105.65]}, {"time": 1.3333, "value": 89.42, "curve": [1.38, 60.75, 1.434, 17.74]}, {"time": 1.4667, "value": 17.74, "curve": [1.5, 17.74, 1.567, 117.82]}, {"time": 1.6, "value": 117.82, "curve": [1.618, 117.82, 1.641, 105.65]}, {"time": 1.6667, "value": 89.42, "curve": [1.714, 60.75, 1.768, 17.74]}, {"time": 1.8, "value": 17.74, "curve": [1.833, 17.74, 1.9, 117.82]}, {"time": 1.9333, "value": 117.82, "curve": [1.951, 117.82, 1.975, 105.65]}, {"time": 2, "value": 89.42}]}, "HornBtmLeft3d2": {"rotate": [{"value": 89.42, "curve": [0.047, 60.75, 0.101, 17.74]}, {"time": 0.1333, "value": 17.74, "curve": [0.167, 17.74, 0.233, 117.82]}, {"time": 0.2667, "value": 117.82, "curve": [0.284, 117.82, 0.308, 105.65]}, {"time": 0.3333, "value": 89.42, "curve": [0.38, 60.75, 0.434, 17.74]}, {"time": 0.4667, "value": 17.74, "curve": [0.5, 17.74, 0.567, 117.82]}, {"time": 0.6, "value": 117.82, "curve": [0.618, 117.82, 0.641, 105.65]}, {"time": 0.6667, "value": 89.42, "curve": [0.714, 60.75, 0.768, 17.74]}, {"time": 0.8, "value": 17.74, "curve": [0.833, 17.74, 0.9, 117.82]}, {"time": 0.9333, "value": 117.82, "curve": [0.951, 117.82, 0.975, 105.65]}, {"time": 1, "value": 89.42, "curve": [1.047, 60.75, 1.101, 17.74]}, {"time": 1.1333, "value": 17.74, "curve": [1.167, 17.74, 1.233, 117.82]}, {"time": 1.2667, "value": 117.82, "curve": [1.284, 117.82, 1.308, 105.65]}, {"time": 1.3333, "value": 89.42, "curve": [1.38, 60.75, 1.434, 17.74]}, {"time": 1.4667, "value": 17.74, "curve": [1.5, 17.74, 1.567, 117.82]}, {"time": 1.6, "value": 117.82, "curve": [1.618, 117.82, 1.641, 105.65]}, {"time": 1.6667, "value": 89.42, "curve": [1.714, 60.75, 1.768, 17.74]}, {"time": 1.8, "value": 17.74, "curve": [1.833, 17.74, 1.9, 117.82]}, {"time": 1.9333, "value": 117.82, "curve": [1.951, 117.82, 1.975, 105.65]}, {"time": 2, "value": 89.42}]}, "HornBtmLeft3c2": {"rotate": [{"value": 50.29, "curve": [0.037, 35.4, 0.075, 19.45]}, {"time": 0.1, "value": 19.45, "curve": [0.142, 19.45, 0.225, 76.14]}, {"time": 0.2667, "value": 76.14, "curve": [0.283, 76.14, 0.308, 63.64]}, {"time": 0.3333, "value": 50.29, "curve": [0.37, 35.4, 0.409, 19.45]}, {"time": 0.4333, "value": 19.45, "curve": [0.475, 19.45, 0.558, 76.14]}, {"time": 0.6, "value": 76.14, "curve": [0.617, 76.14, 0.641, 63.64]}, {"time": 0.6667, "value": 50.29, "curve": [0.704, 35.4, 0.742, 19.45]}, {"time": 0.7667, "value": 19.45, "curve": [0.808, 19.45, 0.892, 76.14]}, {"time": 0.9333, "value": 76.14, "curve": [0.95, 76.14, 0.975, 63.64]}, {"time": 1, "value": 50.29, "curve": [1.037, 35.4, 1.075, 19.45]}, {"time": 1.1, "value": 19.45, "curve": [1.142, 19.45, 1.225, 76.14]}, {"time": 1.2667, "value": 76.14, "curve": [1.283, 76.14, 1.308, 63.64]}, {"time": 1.3333, "value": 50.29, "curve": [1.37, 35.4, 1.409, 19.45]}, {"time": 1.4333, "value": 19.45, "curve": [1.475, 19.45, 1.558, 76.14]}, {"time": 1.6, "value": 76.14, "curve": [1.617, 76.14, 1.641, 63.64]}, {"time": 1.6667, "value": 50.29, "curve": [1.704, 35.4, 1.742, 19.45]}, {"time": 1.7667, "value": 19.45, "curve": [1.808, 19.45, 1.892, 76.14]}, {"time": 1.9333, "value": 76.14, "curve": [1.95, 76.14, 1.975, 63.64]}, {"time": 2, "value": 50.29}]}, "HornBtmLeft3b2": {"rotate": [{"value": -37.17, "curve": [0.025, -42.43, 0.049, -46.37]}, {"time": 0.0667, "value": -46.37, "curve": [0.108, -46.37, 0.192, -13.95]}, {"time": 0.2333, "value": -13.95, "curve": [0.258, -13.95, 0.298, -27.88]}, {"time": 0.3333, "value": -37.17, "curve": [0.359, -42.43, 0.382, -46.37]}, {"time": 0.4, "value": -46.37, "curve": [0.442, -46.37, 0.525, -13.95]}, {"time": 0.5667, "value": -13.95, "curve": [0.591, -13.95, 0.632, -27.88]}, {"time": 0.6667, "value": -37.17, "curve": [0.692, -42.43, 0.716, -46.37]}, {"time": 0.7333, "value": -46.37, "curve": [0.775, -46.37, 0.858, -13.95]}, {"time": 0.9, "value": -13.95, "curve": [0.924, -13.95, 0.965, -27.88]}, {"time": 1, "value": -37.17, "curve": [1.025, -42.43, 1.049, -46.37]}, {"time": 1.0667, "value": -46.37, "curve": [1.108, -46.37, 1.192, -13.95]}, {"time": 1.2333, "value": -13.95, "curve": [1.258, -13.95, 1.298, -27.88]}, {"time": 1.3333, "value": -37.17, "curve": [1.359, -42.43, 1.382, -46.37]}, {"time": 1.4, "value": -46.37, "curve": [1.442, -46.37, 1.525, -13.95]}, {"time": 1.5667, "value": -13.95, "curve": [1.591, -13.95, 1.632, -27.88]}, {"time": 1.6667, "value": -37.17, "curve": [1.692, -42.43, 1.716, -46.37]}, {"time": 1.7333, "value": -46.37, "curve": [1.775, -46.37, 1.858, -13.95]}, {"time": 1.9, "value": -13.95, "curve": [1.924, -13.95, 1.965, -27.88]}, {"time": 2, "value": -37.17}]}, "TentacleLeftBtm2": {"rotate": [{"curve": [0.042, 0, 0.125, -26.24]}, {"time": 0.1667, "value": -26.24, "curve": [0.208, -26.24, 0.292, 0]}, {"time": 0.3333, "curve": [0.375, 0, 0.458, -26.24]}, {"time": 0.5, "value": -26.24, "curve": [0.542, -26.24, 0.625, 0]}, {"time": 0.6667, "curve": [0.708, 0, 0.792, -26.24]}, {"time": 0.8333, "value": -26.24, "curve": [0.875, -26.24, 0.958, 0]}, {"time": 1, "curve": [1.042, 0, 1.125, -26.24]}, {"time": 1.1667, "value": -26.24, "curve": [1.208, -26.24, 1.292, 0]}, {"time": 1.3333, "curve": [1.375, 0, 1.458, -26.24]}, {"time": 1.5, "value": -26.24, "curve": [1.542, -26.24, 1.625, 0]}, {"time": 1.6667, "curve": [1.708, 0, 1.792, -26.24]}, {"time": 1.8333, "value": -26.24, "curve": [1.875, -26.24, 1.958, 0]}, {"time": 2}]}, "HornBtmLeft2d2": {"rotate": [{"value": -32.04, "curve": [0.047, -11.54, 0.101, 19.21]}, {"time": 0.1333, "value": 19.21, "curve": [0.167, 19.21, 0.233, -80.52]}, {"time": 0.2667, "value": -80.52, "curve": [0.284, -80.52, 0.308, -59.74]}, {"time": 0.3333, "value": -32.04, "curve": [0.38, -11.54, 0.434, 19.21]}, {"time": 0.4667, "value": 19.21, "curve": [0.5, 19.21, 0.567, -80.52]}, {"time": 0.6, "value": -80.52, "curve": [0.618, -80.52, 0.641, -59.74]}, {"time": 0.6667, "value": -32.04, "curve": [0.714, -11.54, 0.768, 19.21]}, {"time": 0.8, "value": 19.21, "curve": [0.833, 19.21, 0.9, -80.52]}, {"time": 0.9333, "value": -80.52, "curve": [0.951, -80.52, 0.975, -59.74]}, {"time": 1, "value": -32.04, "curve": [1.047, -11.54, 1.101, 19.21]}, {"time": 1.1333, "value": 19.21, "curve": [1.167, 19.21, 1.233, -80.52]}, {"time": 1.2667, "value": -80.52, "curve": [1.284, -80.52, 1.308, -59.74]}, {"time": 1.3333, "value": -32.04, "curve": [1.38, -11.54, 1.434, 19.21]}, {"time": 1.4667, "value": 19.21, "curve": [1.5, 19.21, 1.567, -80.52]}, {"time": 1.6, "value": -80.52, "curve": [1.618, -80.52, 1.641, -59.74]}, {"time": 1.6667, "value": -32.04, "curve": [1.714, -11.54, 1.768, 19.21]}, {"time": 1.8, "value": 19.21, "curve": [1.833, 19.21, 1.9, -80.52]}, {"time": 1.9333, "value": -80.52, "curve": [1.951, -80.52, 1.975, -59.74]}, {"time": 2, "value": -32.04}]}, "HornBtmLeft2c2": {"rotate": [{"value": -14.58, "curve": [0.037, -7.54, 0.075, 0]}, {"time": 0.1, "curve": [0.142, 0, 0.225, -26.8]}, {"time": 0.2667, "value": -26.8, "curve": [0.283, -26.8, 0.308, -20.89]}, {"time": 0.3333, "value": -14.58, "curve": [0.37, -7.54, 0.409, 0]}, {"time": 0.4333, "curve": [0.475, 0, 0.558, -26.8]}, {"time": 0.6, "value": -26.8, "curve": [0.617, -26.8, 0.641, -20.89]}, {"time": 0.6667, "value": -14.58, "curve": [0.704, -7.54, 0.742, 0]}, {"time": 0.7667, "curve": [0.808, 0, 0.892, -26.8]}, {"time": 0.9333, "value": -26.8, "curve": [0.95, -26.8, 0.975, -20.89]}, {"time": 1, "value": -14.58, "curve": [1.037, -7.54, 1.075, 0]}, {"time": 1.1, "curve": [1.142, 0, 1.225, -26.8]}, {"time": 1.2667, "value": -26.8, "curve": [1.283, -26.8, 1.308, -20.89]}, {"time": 1.3333, "value": -14.58, "curve": [1.37, -7.54, 1.409, 0]}, {"time": 1.4333, "curve": [1.475, 0, 1.558, -26.8]}, {"time": 1.6, "value": -26.8, "curve": [1.617, -26.8, 1.641, -20.89]}, {"time": 1.6667, "value": -14.58, "curve": [1.704, -7.54, 1.742, 0]}, {"time": 1.7667, "curve": [1.808, 0, 1.892, -26.8]}, {"time": 1.9333, "value": -26.8, "curve": [1.95, -26.8, 1.975, -20.89]}, {"time": 2, "value": -14.58}]}, "HornBtmLeft2b2": {"rotate": [{"value": -1.5, "curve": [0.025, -0.64, 0.049, 0]}, {"time": 0.0667, "curve": [0.108, 0, 0.192, -5.3]}, {"time": 0.2333, "value": -5.3, "curve": [0.258, -5.3, 0.298, -3.02]}, {"time": 0.3333, "value": -1.5, "curve": [0.359, -0.64, 0.382, 0]}, {"time": 0.4, "curve": [0.442, 0, 0.525, -5.3]}, {"time": 0.5667, "value": -5.3, "curve": [0.591, -5.3, 0.632, -3.02]}, {"time": 0.6667, "value": -1.5, "curve": [0.692, -0.64, 0.716, 0]}, {"time": 0.7333, "curve": [0.775, 0, 0.858, -5.3]}, {"time": 0.9, "value": -5.3, "curve": [0.924, -5.3, 0.965, -3.02]}, {"time": 1, "value": -1.5, "curve": [1.025, -0.64, 1.049, 0]}, {"time": 1.0667, "curve": [1.108, 0, 1.192, -5.3]}, {"time": 1.2333, "value": -5.3, "curve": [1.258, -5.3, 1.298, -3.02]}, {"time": 1.3333, "value": -1.5, "curve": [1.359, -0.64, 1.382, 0]}, {"time": 1.4, "curve": [1.442, 0, 1.525, -5.3]}, {"time": 1.5667, "value": -5.3, "curve": [1.591, -5.3, 1.632, -3.02]}, {"time": 1.6667, "value": -1.5, "curve": [1.692, -0.64, 1.716, 0]}, {"time": 1.7333, "curve": [1.775, 0, 1.858, -5.3]}, {"time": 1.9, "value": -5.3, "curve": [1.924, -5.3, 1.965, -3.02]}, {"time": 2, "value": -1.5}]}, "TentacleLeftTop2": {"rotate": [{"value": -32.65, "curve": [0.042, -32.65, 0.125, 8.41]}, {"time": 0.1667, "value": 8.41, "curve": [0.208, 8.41, 0.292, -32.65]}, {"time": 0.3333, "value": -32.65, "curve": [0.375, -32.65, 0.458, 8.41]}, {"time": 0.5, "value": 8.41, "curve": [0.542, 8.41, 0.625, -32.65]}, {"time": 0.6667, "value": -32.65, "curve": [0.708, -32.65, 0.792, 8.41]}, {"time": 0.8333, "value": 8.41, "curve": [0.875, 8.41, 0.958, -32.65]}, {"time": 1, "value": -32.65, "curve": [1.042, -32.65, 1.125, 8.41]}, {"time": 1.1667, "value": 8.41, "curve": [1.208, 8.41, 1.292, -32.65]}, {"time": 1.3333, "value": -32.65, "curve": [1.375, -32.65, 1.458, 8.41]}, {"time": 1.5, "value": 8.41, "curve": [1.542, 8.41, 1.625, -32.65]}, {"time": 1.6667, "value": -32.65, "curve": [1.708, -32.65, 1.792, 8.41]}, {"time": 1.8333, "value": 8.41, "curve": [1.875, 8.41, 1.958, -32.65]}, {"time": 2, "value": -32.65}]}}}, "attack-charge": {"slots": {"EyeLeft": {"attachment": [{"name": "images/Beholder1_Eye_1_Anticipate"}, {"time": 0.1, "name": "images/Beholder1_Eye_1"}]}, "EyeRight": {"attachment": [{"name": "images/Beholder1_Eye_Anticipate"}, {"time": 0.1, "name": "images/Beholder1_Eye_1"}]}, "Eye_Middle": {"attachment": [{"name": "images/Beholder1_Eye_1_Anticipate"}, {"time": 0.1, "name": "images/Beholder1_Eye_2"}]}}, "bones": {"EyeLeft": {"translate": [{"x": 0.06, "y": 3.12}], "scale": [{"x": 1.725, "y": 1.725, "curve": [0.017, 1.725, 0.05, 0.897, 0.017, 1.725, 0.05, 0.897]}, {"time": 0.0667, "x": 0.897, "y": 0.897, "curve": [0.083, 0.897, 0.117, 1, 0.083, 0.897, 0.117, 1]}, {"time": 0.1333}]}, "EyeRight": {"translate": [{"x": 0.06, "y": 3.12}], "scale": [{"x": 1.725, "y": 1.725, "curve": [0.017, 1.725, 0.05, 0.897, 0.017, 1.725, 0.05, 0.897]}, {"time": 0.0667, "x": 0.897, "y": 0.897, "curve": [0.083, 0.897, 0.117, 1, 0.083, 0.897, 0.117, 1]}, {"time": 0.1333}]}, "Eye_Middle": {"translate": [{"x": 0.06, "y": 3.12}], "scale": [{"x": 1.725, "y": 1.725, "curve": [0.017, 1.725, 0.05, 0.897, 0.017, 1.725, 0.05, 0.897]}, {"time": 0.0667, "x": 0.897, "y": 0.897, "curve": [0.083, 0.897, 0.117, 1, 0.083, 0.897, 0.117, 1]}, {"time": 0.1333}]}, "HornTopRight": {"rotate": [{"value": -47.97, "curve": [0.025, -47.97, 0.075, 16.13]}, {"time": 0.1, "value": 16.13, "curve": [0.142, 16.13, 0.225, -21.43]}, {"time": 0.2667, "value": -21.43, "curve": [0.317, -21.43, 0.417, -2.67]}, {"time": 0.4667, "value": -2.67}], "scale": [{}, {"time": 0.0333, "x": 1.373, "y": 1.373}, {"time": 0.1333}]}, "Main": {"rotate": [{"value": 0.13}], "translate": [{"y": -106.72, "curve": [0.017, 0, 0.05, 0, 0.017, -106.72, 0.05, 2.82]}, {"time": 0.0667, "y": 2.82, "curve": [0.108, 0, 0.192, 0, 0.108, 2.82, 0.192, -123.91]}, {"time": 0.2333, "y": -123.91, "curve": [0.292, 0, 0.408, 0, 0.292, -123.91, 0.408, 0]}, {"time": 0.4667}], "scale": [{"x": 0.867, "y": 1.216, "curve": [0.025, 0.867, 0.075, 1.432, 0.025, 1.216, 0.075, 0.802]}, {"time": 0.1, "x": 1.432, "y": 0.802, "curve": [0.14, 1.432, 0.211, 1.125, 0.14, 0.802, 0.211, 1.07]}, {"time": 0.2667, "x": 0.971, "y": 1.204, "curve": [0.343, 0.991, 0.411, 1.004, 0.343, 1.073, 0.411, 0.987]}, {"time": 0.4667, "x": 1.004, "y": 0.987}]}, "Drips2": {"rotate": [{"curve": [0.017, 0, 0.028, 23.29]}, {"time": 0.0667, "value": 23.29, "curve": [0.117, 23.29, 0.217, -12.21]}, {"time": 0.2667, "value": -12.21, "curve": [0.317, -12.21, 0.417, 0]}, {"time": 0.4667}], "translate": [{"x": 0.05, "y": 0.41, "curve": [0.017, 0.05, 0.028, 15.52, 0.017, 0.41, 0.028, -98.09]}, {"time": 0.0667, "x": 15.52, "y": -98.09, "curve": [0.117, 15.52, 0.217, 0.33, 0.117, -98.09, 0.217, 28.88]}, {"time": 0.2667, "x": 0.33, "y": 28.88, "curve": [0.317, 0.33, 0.417, 0.05, 0.317, 28.88, 0.417, 0.41]}, {"time": 0.4667, "x": 0.05, "y": 0.41}]}, "FACE": {"translate": [{"x": -63.03, "y": 16, "curve": [0.017, -63.03, 0.05, 93.27, 0.017, 16, 0.05, -11.25]}, {"time": 0.0667, "x": 93.27, "y": -11.25, "curve": [0.117, 93.27, 0.217, -79.18, 0.117, -11.25, 0.217, 2.67]}, {"time": 0.2667, "x": -79.18, "y": 2.67, "curve": [0.317, -79.18, 0.417, 0.13, 0.317, 2.67, 0.417, 6.71]}, {"time": 0.4667, "x": 0.13, "y": 6.71}]}, "Drips1": {"rotate": [{"curve": [0.017, 0, 0.028, -28.87]}, {"time": 0.0667, "value": -28.87, "curve": [0.117, -28.87, 0.217, 13.71]}, {"time": 0.2667, "value": 13.71, "curve": [0.317, 13.71, 0.417, 0]}, {"time": 0.4667}], "translate": [{"x": 0.05, "y": 0.41, "curve": [0.017, 0.05, 0.028, 0.4, 0.017, 0.41, 0.028, 75.19]}, {"time": 0.0667, "x": 0.4, "y": 75.19, "curve": [0.117, 0.4, 0.217, 13.59, 0.117, 75.19, 0.217, -27.83]}, {"time": 0.2667, "x": 13.59, "y": -27.83, "curve": [0.317, 13.59, 0.417, 0.05, 0.317, -27.83, 0.417, 0.41]}, {"time": 0.4667, "x": 0.05, "y": 0.41}]}, "HornBtmRight": {"rotate": [{"value": -21.28, "curve": [0.033, -21.28, 0.1, 18.82]}, {"time": 0.1333, "value": 18.82, "curve": [0.175, 18.82, 0.258, -22.25]}, {"time": 0.3, "value": -22.25, "curve": [0.342, -22.25, 0.425, -3.34]}, {"time": 0.4667, "value": -3.34}], "scale": [{}, {"time": 0.0333, "x": 1.373, "y": 1.373}, {"time": 0.1333}]}, "HornTopLeft": {"rotate": [{"value": 45.81, "curve": [0.025, 45.81, 0.075, -17.43]}, {"time": 0.1, "value": -17.43, "curve": [0.142, -17.43, 0.225, 22.4]}, {"time": 0.2667, "value": 22.4, "curve": [0.317, 22.4, 0.417, 2.87]}, {"time": 0.4667, "value": 2.87}], "scale": [{}, {"time": 0.0333, "x": 1.373, "y": 1.373}, {"time": 0.1333}]}, "HornBtmLeft": {"rotate": [{"value": 24.12, "curve": [0.033, 24.12, 0.1, -21.32]}, {"time": 0.1333, "value": -21.32, "curve": [0.175, -21.32, 0.258, 23.3]}, {"time": 0.3, "value": 23.3, "curve": [0.342, 23.3, 0.425, 3.6]}, {"time": 0.4667, "value": 3.6}], "scale": [{}, {"time": 0.0333, "x": 1.373, "y": 1.373}, {"time": 0.1333}]}, "FACEBOB": {"translate": [{"time": 0.4667, "x": -6, "y": 0.1}]}}}, "attack-shoot": {"bones": {"HornTopRight": {"rotate": [{"value": -30.19, "curve": [0.042, -30.19, 0.125, 16.13]}, {"time": 0.1667, "value": 16.13, "curve": [0.192, 16.13, 0.242, -21.43]}, {"time": 0.2667, "value": -21.43, "curve": [0.317, -21.43, 0.417, -2.67]}, {"time": 0.4667, "value": -2.67}]}, "Main": {"rotate": [{"value": 0.13}], "translate": [{"y": -106.72, "curve": [0.025, 0, 0.075, 0, 0.025, -106.72, 0.075, 178.8]}, {"time": 0.1, "y": 178.8, "curve": [0.15, 0, 0.25, 0, 0.15, 178.8, 0.25, -121.33]}, {"time": 0.3, "y": -121.33, "curve": [0.342, 0, 0.425, 0, 0.342, -121.33, 0.425, 0]}, {"time": 0.4667}], "scale": [{"x": 0.683, "y": 1.596, "curve": [0.042, 0.683, 0.125, 1.432, 0.042, 1.596, 0.125, 0.802]}, {"time": 0.1667, "x": 1.432, "y": 0.802, "curve": [0.192, 1.432, 0.242, 0.867, 0.192, 0.802, 0.242, 1.216]}, {"time": 0.2667, "x": 0.867, "y": 1.216, "curve": [0.317, 0.867, 0.417, 1.004, 0.317, 1.216, 0.417, 0.987]}, {"time": 0.4667, "x": 1.004, "y": 0.987}]}, "Drips2": {"rotate": [{"curve": [0.025, 0, 0.042, 23.29]}, {"time": 0.1, "value": 23.29, "curve": [0.158, 23.29, 0.275, -12.21]}, {"time": 0.3333, "value": -12.21, "curve": [0.367, -12.21, 0.433, 0]}, {"time": 0.4667}], "translate": [{"x": 0.05, "y": 0.41, "curve": [0.025, 0.05, 0.042, 15.52, 0.025, 0.41, 0.042, -98.09]}, {"time": 0.1, "x": 15.52, "y": -98.09, "curve": [0.158, 15.52, 0.275, 0.33, 0.158, -98.09, 0.275, 28.88]}, {"time": 0.3333, "x": 0.33, "y": 28.88, "curve": [0.367, 0.33, 0.433, 0.05, 0.367, 28.88, 0.433, 0.41]}, {"time": 0.4667, "x": 0.05, "y": 0.41}]}, "FACE": {"translate": [{"x": -63.03, "y": 16, "curve": [0.025, -63.03, 0.075, 93.27, 0.025, 16, 0.075, -11.25]}, {"time": 0.1, "x": 93.27, "y": -11.25, "curve": [0.158, 93.27, 0.275, -79.18, 0.158, -11.25, 0.275, 2.67]}, {"time": 0.3333, "x": -79.18, "y": 2.67, "curve": [0.367, -79.18, 0.433, 0.13, 0.367, 2.67, 0.433, 6.71]}, {"time": 0.4667, "x": 0.13, "y": 6.71}]}, "Drips1": {"rotate": [{"curve": [0.025, 0, 0.042, -28.87]}, {"time": 0.1, "value": -28.87, "curve": [0.158, -28.87, 0.275, 13.71]}, {"time": 0.3333, "value": 13.71, "curve": [0.367, 13.71, 0.433, 0]}, {"time": 0.4667}], "translate": [{"x": 0.05, "y": 0.41, "curve": [0.025, 0.05, 0.042, 0.4, 0.025, 0.41, 0.042, 75.19]}, {"time": 0.1, "x": 0.4, "y": 75.19, "curve": [0.158, 0.4, 0.275, 13.59, 0.158, 75.19, 0.275, -27.83]}, {"time": 0.3333, "x": 13.59, "y": -27.83, "curve": [0.367, 13.59, 0.433, 0.05, 0.367, -27.83, 0.433, 0.41]}, {"time": 0.4667, "x": 0.05, "y": 0.41}]}, "HornBtmRight": {"rotate": [{"value": -3.49, "curve": [0.008, -3.49, 0.025, -31.01]}, {"time": 0.0333, "value": -31.01, "curve": [0.075, -31.01, 0.158, 18.82]}, {"time": 0.2, "value": 18.82, "curve": [0.225, 18.82, 0.275, -22.25]}, {"time": 0.3, "value": -22.25, "curve": [0.342, -22.25, 0.425, -3.34]}, {"time": 0.4667, "value": -3.34}]}, "HornTopLeft": {"rotate": [{"value": 25.45, "curve": [0.042, 25.45, 0.125, -17.43]}, {"time": 0.1667, "value": -17.43, "curve": [0.192, -17.43, 0.242, 22.4]}, {"time": 0.2667, "value": 22.4, "curve": [0.317, 22.4, 0.417, 2.87]}, {"time": 0.4667, "value": 2.87}]}, "HornBtmLeft": {"rotate": [{"value": 3.76, "curve": [0.008, 3.76, 0.025, 26.34]}, {"time": 0.0333, "value": 26.34, "curve": [0.075, 26.34, 0.158, -21.32]}, {"time": 0.2, "value": -21.32, "curve": [0.225, -21.32, 0.275, 23.3]}, {"time": 0.3, "value": 23.3, "curve": [0.342, 23.3, 0.425, 3.6]}, {"time": 0.4667, "value": 3.6}]}, "FACEBOB": {"translate": [{"time": 0.4667, "x": -6, "y": 0.1}]}, "Eye_Middle": {"translate": [{"x": 0.06, "y": 3.12}], "scale": [{"x": 1.725, "y": 1.725, "curve": [0.025, 1.725, 0.075, 0.897, 0.025, 1.725, 0.075, 0.897]}, {"time": 0.1, "x": 0.897, "y": 0.897, "curve": [0.125, 0.897, 0.175, 1, 0.125, 0.897, 0.175, 1]}, {"time": 0.2}]}, "EyeRight": {"translate": [{"x": 0.06, "y": 3.12}], "scale": [{"x": 1.725, "y": 1.725, "curve": [0.025, 1.725, 0.075, 0.897, 0.025, 1.725, 0.075, 0.897]}, {"time": 0.1, "x": 0.897, "y": 0.897, "curve": [0.125, 0.897, 0.175, 1, 0.125, 0.897, 0.175, 1]}, {"time": 0.2}]}, "EyeLeft": {"translate": [{"x": 0.06, "y": 3.12}], "scale": [{"x": 1.725, "y": 1.725, "curve": [0.025, 1.725, 0.075, 0.897, 0.025, 1.725, 0.075, 0.897]}, {"time": 0.1, "x": 0.897, "y": 0.897, "curve": [0.125, 0.897, 0.175, 1, 0.125, 0.897, 0.175, 1]}, {"time": 0.2}]}}}, "fly-in": {"slots": {"EyeLeft": {"attachment": [{"name": "images/Beholder1_Eye_1_Closed"}, {"time": 1, "name": "images/Beholder1_Eye_1"}]}, "EyeRight": {"attachment": [{"name": "images/Beholder1_Eye_1_Closed"}, {"time": 1, "name": "images/Beholder1_Eye_1"}]}, "Eye_Middle": {"attachment": [{"name": "images/Beholder1_Eye_2_Closed"}, {"time": 1, "name": "images/Beholder1_Eye_2"}]}}, "bones": {"Main": {"rotate": [{"value": 0.13, "curve": [0.139, 0.91, 0.274, 1.64]}, {"time": 0.3667, "value": 1.64}], "translate": [{"curve": [0.092, 0, 0.275, 0, 0.092, 0, 0.275, -218.26]}, {"time": 0.3667, "y": -218.26, "curve": [0.442, 0, 0.592, 0, 0.442, -218.26, 0.592, 73.73]}, {"time": 0.6667, "y": 73.73, "curve": [0.718, 0, 0.842, 0, 0.718, 193.11, 0.842, 258.02]}, {"time": 0.9, "y": 258.02, "curve": [0.95, 0, 1.05, 0, 0.95, 258.02, 1.05, 0]}, {"time": 1.1}], "scale": [{"x": 1.004, "y": 0.987, "curve": [0.139, 0.898, 0.274, 0.798, 0.139, 1.118, 0.274, 1.242]}, {"time": 0.3667, "x": 0.798, "y": 1.242, "curve": [0.4, 0.798, 0.467, 1.297, 0.4, 1.242, 0.467, 0.853]}, {"time": 0.5, "x": 1.297, "y": 0.853, "curve": [0.541, 1.297, 0.605, 1.301, 0.541, 0.853, 0.605, 0.803]}, {"time": 0.6667, "x": 1.304, "y": 0.756, "curve": [0.73, 1.149, 0.791, 1.004, 0.73, 0.875, 0.791, 0.987]}, {"time": 0.8333, "x": 1.004, "y": 0.987, "curve": [0.896, 0.883, 0.958, 0.769, 0.896, 1.139, 0.958, 1.281]}, {"time": 1, "x": 0.769, "y": 1.281}, {"time": 1.1667, "x": 1.004, "y": 0.987}]}, "FACE": {"translate": [{"x": 0.13, "y": 6.71, "curve": [0.14, 0.21, 0.272, 0.28, 0.14, 11.82, 0.272, 16.08]}, {"time": 0.3667, "x": 0.28, "y": 16.08, "curve": [0.442, 0.28, 0.592, -100.79, 0.442, 16.08, 0.592, 6.17]}, {"time": 0.6667, "x": -100.79, "y": 6.17, "curve": [0.873, -100.79, 0.867, 58.82, 0.873, 6.17, 0.867, 11.57]}, {"time": 0.9333, "x": 58.82, "y": 11.57, "curve": [0.992, 58.82, 1.108, 0.13, 0.992, 11.57, 1.108, 6.71]}, {"time": 1.1667, "x": 0.13, "y": 6.71}]}, "HornBtmLeft": {"rotate": [{"value": 3.6, "curve": [0.1, 3.6, 0.3, 37.52]}, {"time": 0.4, "value": 37.52, "curve": [0.468, 2.81, 0.534, -14.84]}, {"time": 0.6, "value": -14.84, "curve": [0.617, -14.84, 0.65, -21.04]}, {"time": 0.6667, "value": -21.04}, {"time": 1.1667, "value": 3.6}]}, "HornTopLeft": {"rotate": [{"value": 2.87, "curve": [0.1, 2.87, 0.3, 39.21]}, {"time": 0.4, "value": 39.21, "curve": [0.45, 39.21, 0.55, -15.01]}, {"time": 0.6, "value": -15.01, "curve": [0.617, -15.01, 0.65, -13.94]}, {"time": 0.6667, "value": -13.94}, {"time": 1.1667, "value": 2.87}]}, "HornTopRight": {"rotate": [{"value": -2.67, "curve": [0.1, -2.67, 0.3, -25.86]}, {"time": 0.4, "value": -25.86, "curve": [0.45, -25.86, 0.55, 15.48]}, {"time": 0.6, "value": 15.48, "curve": [0.617, 15.48, 0.65, 11.84]}, {"time": 0.6667, "value": 11.84}, {"time": 1.1667, "value": -2.67}]}, "HornBtmRight": {"rotate": [{"value": -3.34, "curve": [0.1, -3.34, 0.3, -24.05]}, {"time": 0.4, "value": -24.05, "curve": [0.468, 2.05, 0.534, 15.32]}, {"time": 0.6, "value": 15.32, "curve": [0.617, 15.32, 0.65, 9.97]}, {"time": 0.6667, "value": 9.97}, {"time": 1.1667, "value": -3.34}]}, "Drips1": {"translate": [{"x": 0.05, "y": 0.41, "curve": [0.169, -0.03, 0.352, -0.12, 0.169, -5.71, 0.352, -13.35]}, {"time": 0.4667, "x": -0.12, "y": -13.35, "curve": [0.542, -0.12, 0.692, 0.05, 0.542, -13.35, 0.692, 0.41]}, {"time": 0.7667, "x": 0.05, "y": 0.41}, {"time": 0.8333, "x": -100.94, "y": 0.8}, {"time": 1.0333, "x": 37.35, "y": 1.36}, {"time": 1.1667, "x": 0.05, "y": 0.41}]}, "FACEBOB": {"translate": [{"x": -6, "y": 0.1, "curve": [0.167, -6, 0.5, 18, 0.167, 0.1, 0.5, -0.19]}, {"time": 0.6667, "x": 18, "y": -0.19}]}, "Drips2": {"translate": [{"x": 0.14, "y": 7.93, "curve": [0.012, 0.14, 0.023, 0.14, 0.012, 8.25, 0.023, 8.42]}, {"time": 0.0333, "x": 0.14, "y": 8.42, "curve": [0.128, 0.14, 0.26, 0.1, 0.128, 8.42, 0.26, 4.78]}, {"time": 0.4, "x": 0.05, "y": 0.41, "curve": [0.473, -0.03, 0.551, -0.12, 0.473, -5.71, 0.551, -13.35]}, {"time": 0.6, "x": -0.12, "y": -13.35, "curve": [0.641, -0.12, 0.72, 0.03, 0.641, -13.35, 0.72, -1.21]}, {"time": 0.7667, "x": 0.05, "y": 0.41}, {"time": 0.8333, "x": -100.94, "y": 0.8}, {"time": 1.0333, "x": 37.35, "y": 1.36}, {"time": 1.1667, "x": 0.05, "y": 0.41}]}, "TentacleLeftTop": {"rotate": [{"value": 20.88, "curve": [0.125, 20.88, 0.375, -20.66]}, {"time": 0.5, "value": -20.66, "curve": [0.625, -20.66, 0.875, 20.88]}, {"time": 1, "value": 20.88}]}, "HornBtmLeft2b": {"rotate": [{"value": -1.5, "curve": [0.064, -0.64, 0.123, 0]}, {"time": 0.1667, "curve": [0.292, 0, 0.542, -5.3]}, {"time": 0.6667, "value": -5.3, "curve": [0.748, -5.3, 0.883, -3.02]}, {"time": 1, "value": -1.5}]}, "HornBtmLeft2c": {"rotate": [{"value": -14.58, "curve": [0.099, -7.54, 0.201, 0]}, {"time": 0.2667, "curve": [0.392, 0, 0.642, -26.8]}, {"time": 0.7667, "value": -26.8, "curve": [0.826, -26.8, 0.912, -20.89]}, {"time": 1, "value": -14.58}]}, "HornBtmLeft2d": {"rotate": [{"value": -32.04, "curve": [0.117, -11.54, 0.252, 19.21]}, {"time": 0.3333, "value": 19.21, "curve": [0.458, 19.21, 0.708, -52.35]}, {"time": 0.8333, "value": -52.35, "curve": [0.877, -52.35, 0.936, -43.64]}, {"time": 1, "value": -32.04}]}, "TentacleLeftBtm": {"rotate": [{"curve": [0.125, 0, 0.375, 33.59]}, {"time": 0.5, "value": 33.59, "curve": [0.625, 33.59, 0.875, 0]}, {"time": 1}]}, "HornBtmLeft3b": {"rotate": [{"value": -37.17, "curve": [0.064, -42.43, 0.123, -46.37]}, {"time": 0.1667, "value": -46.37, "curve": [0.292, -46.37, 0.542, -13.95]}, {"time": 0.6667, "value": -13.95, "curve": [0.748, -13.95, 0.883, -27.88]}, {"time": 1, "value": -37.17}]}, "HornBtmLeft3c": {"rotate": [{"value": 50.29, "curve": [0.099, 35.4, 0.201, 19.45]}, {"time": 0.2667, "value": 19.45, "curve": [0.392, 19.45, 0.642, 76.14]}, {"time": 0.7667, "value": 76.14, "curve": [0.826, 76.14, 0.912, 63.64]}, {"time": 1, "value": 50.29}]}, "HornBtmLeft3d": {"rotate": [{"value": 89.42, "curve": [0.117, 60.75, 0.252, 17.74]}, {"time": 0.3333, "value": 17.74, "curve": [0.458, 17.74, 0.708, 117.82]}, {"time": 0.8333, "value": 117.82, "curve": [0.877, 117.82, 0.936, 105.65]}, {"time": 1, "value": 89.42}]}, "HornBtmLeft3d2": {"rotate": [{"value": 89.42, "curve": [0.117, 60.75, 0.252, 17.74]}, {"time": 0.3333, "value": 17.74, "curve": [0.458, 17.74, 0.708, 117.82]}, {"time": 0.8333, "value": 117.82, "curve": [0.877, 117.82, 0.936, 105.65]}, {"time": 1, "value": 89.42}]}, "HornBtmLeft3c2": {"rotate": [{"value": 50.29, "curve": [0.099, 35.4, 0.201, 19.45]}, {"time": 0.2667, "value": 19.45, "curve": [0.392, 19.45, 0.642, 76.14]}, {"time": 0.7667, "value": 76.14, "curve": [0.826, 76.14, 0.912, 63.64]}, {"time": 1, "value": 50.29}]}, "HornBtmLeft3b2": {"rotate": [{"value": -37.17, "curve": [0.064, -42.43, 0.123, -46.37]}, {"time": 0.1667, "value": -46.37, "curve": [0.292, -46.37, 0.542, -13.95]}, {"time": 0.6667, "value": -13.95, "curve": [0.748, -13.95, 0.883, -27.88]}, {"time": 1, "value": -37.17}]}, "TentacleLeftBtm2": {"rotate": [{"curve": [0.125, 0, 0.375, -26.24]}, {"time": 0.5, "value": -26.24, "curve": [0.625, -26.24, 0.875, 0]}, {"time": 1}]}, "HornBtmLeft2d2": {"rotate": [{"value": -32.04, "curve": [0.117, -11.54, 0.252, 19.21]}, {"time": 0.3333, "value": 19.21, "curve": [0.458, 19.21, 0.708, -52.35]}, {"time": 0.8333, "value": -52.35, "curve": [0.877, -52.35, 0.936, -43.64]}, {"time": 1, "value": -32.04}]}, "HornBtmLeft2c2": {"rotate": [{"value": -14.58, "curve": [0.099, -7.54, 0.201, 0]}, {"time": 0.2667, "curve": [0.392, 0, 0.642, -26.8]}, {"time": 0.7667, "value": -26.8, "curve": [0.826, -26.8, 0.912, -20.89]}, {"time": 1, "value": -14.58}]}, "HornBtmLeft2b2": {"rotate": [{"value": -1.5, "curve": [0.064, -0.64, 0.123, 0]}, {"time": 0.1667, "curve": [0.292, 0, 0.542, -5.3]}, {"time": 0.6667, "value": -5.3, "curve": [0.748, -5.3, 0.883, -3.02]}, {"time": 1, "value": -1.5}]}, "TentacleLeftTop2": {"rotate": [{"value": -32.65, "curve": [0.125, -32.65, 0.375, 8.41]}, {"time": 0.5, "value": 8.41, "curve": [0.625, 8.41, 0.875, -32.65]}, {"time": 1, "value": -32.65}]}}, "path": {"PATH": {"position": [{"value": 0.5104}, {"time": 0.6667, "value": 1}], "mix": [{"curve": "stepped"}, {"time": 0.6}, {"time": 0.6667, "mixRotate": 0, "mixX": 0}]}}, "attachments": {"default": {"path": {"path": {"deform": [{"vertices": [-238.27084, -161.23645, -5.94983, -41.65036, 160.65004, 368.8997, 345.4281, 412.25732, 65.45046, 541.44995, -232.04977, 618.8001, -124.00009, 549.67395, -59.49948, 725.9005, 3.86092, 1035.7766, -142.79974, 1457.7499, -142.79974, 1457.7499, -142.79974, 1457.75]}]}}}}}, "fly-out": {"slots": {"EyeLeft": {"attachment": [{"name": "images/Beholder1_Eye_1_Closed"}]}, "EyeRight": {"attachment": [{"name": "images/Beholder1_Eye_1_Closed"}]}, "Eye_Middle": {"attachment": [{"name": "images/Beholder1_Eye_2_Closed"}]}, "MASK": {"attachment": [{"time": 0.8, "name": "MASK"}]}}, "bones": {"Main": {"rotate": [{"value": 0.13, "curve": [0.076, 0.91, 0.15, 1.64]}, {"time": 0.2, "value": 1.64, "curve": [0.333, 1.64, 0.6, -1.67]}, {"time": 0.7333, "value": -1.67, "curve": [0.799, -1.67, 0.901, -0.74]}, {"time": 1, "value": 0.13}], "translate": [{"curve": [0.05, 0, 0.15, 0, 0.05, 0, 0.15, -218.26]}, {"time": 0.2, "y": -218.26, "curve": [0.4, 0, 0.8, 0, 0.4, -218.26, 0.8, 0]}, {"time": 1}], "scale": [{"x": 1.004, "y": 0.987, "curve": [0.076, 0.898, 0.15, 0.798, 0.076, 1.118, 0.15, 1.242]}, {"time": 0.2, "x": 0.798, "y": 1.242, "curve": [0.233, 0.798, 0.3, 1.297, 0.233, 1.242, 0.3, 0.853]}, {"time": 0.3333, "x": 1.297, "y": 0.853, "curve": [0.499, 1.297, 0.752, 1.146, 0.499, 0.853, 0.752, 0.922]}, {"time": 1, "x": 1.004, "y": 0.987}]}, "FACE": {"translate": [{"x": 0.13, "y": 6.71, "curve": [0.076, 0.21, 0.148, 0.28, 0.076, 11.82, 0.148, 16.08]}, {"time": 0.2, "x": 0.28, "y": 16.08, "curve": [0.325, 0.28, 0.575, -0.11, 0.325, 16.08, 0.575, -9.38]}, {"time": 0.7, "x": -0.11, "y": -9.38, "curve": [0.774, -0.11, 0.891, 0.02, 0.774, -9.38, 0.891, -0.44]}, {"time": 1, "x": 0.13, "y": 6.71}]}, "HornBtmLeft": {"rotate": [{"value": 3.6, "curve": [0.058, 3.6, 0.175, 37.52]}, {"time": 0.2333, "value": 37.52, "curve": [0.301, 2.81, 0.368, -14.84]}, {"time": 0.4333, "value": -14.84}]}, "HornTopLeft": {"rotate": [{"value": 2.87, "curve": [0.058, 2.87, 0.175, 39.21]}, {"time": 0.2333, "value": 39.21, "curve": [0.283, 39.21, 0.383, -15.01]}, {"time": 0.4333, "value": -15.01}]}, "HornTopRight": {"rotate": [{"value": -2.67, "curve": [0.058, -2.67, 0.175, -25.86]}, {"time": 0.2333, "value": -25.86, "curve": [0.283, -25.86, 0.383, 15.48]}, {"time": 0.4333, "value": 15.48}]}, "HornBtmRight": {"rotate": [{"value": -3.34, "curve": [0.058, -3.34, 0.175, -24.05]}, {"time": 0.2333, "value": -24.05, "curve": [0.301, 2.05, 0.368, 15.32]}, {"time": 0.4333, "value": 15.32}]}, "Drips1": {"translate": [{"x": 0.05, "y": 0.41, "curve": [0.109, -0.03, 0.226, -0.12, 0.109, -5.71, 0.226, -13.35]}, {"time": 0.3, "x": -0.12, "y": -13.35, "curve": [0.425, -0.12, 0.675, 0.14, 0.425, -13.35, 0.675, 8.42]}, {"time": 0.8, "x": 0.14, "y": 8.42, "curve": [0.852, 0.14, 0.924, 0.1, 0.852, 8.42, 0.924, 4.78]}, {"time": 1, "x": 0.05, "y": 0.41}]}, "FACEBOB": {"translate": [{"x": -6, "y": 0.1, "curve": [0.125, -6, 0.375, 18, 0.125, 0.1, 0.375, -0.19]}, {"time": 0.5, "x": 18, "y": -0.19, "curve": [0.625, 18, 0.875, -6, 0.625, -0.19, 0.875, 0.1]}, {"time": 1, "x": -6, "y": 0.1}]}, "Drips2": {"translate": [{"x": 0.14, "y": 7.93, "curve": [0.012, 0.14, 0.023, 0.14, 0.012, 8.25, 0.023, 8.42]}, {"time": 0.0333, "x": 0.14, "y": 8.42, "curve": [0.085, 0.14, 0.157, 0.1, 0.085, 8.42, 0.157, 4.78]}, {"time": 0.2333, "x": 0.05, "y": 0.41, "curve": [0.342, -0.03, 0.46, -0.12, 0.342, -5.71, 0.46, -13.35]}, {"time": 0.5333, "x": -0.12, "y": -13.35, "curve": [0.648, -0.12, 0.871, 0.11, 0.648, -13.35, 0.871, 5.42]}, {"time": 1, "x": 0.14, "y": 7.93}]}, "TentacleLeftTop": {"rotate": [{"value": 20.88, "curve": [0.125, 20.88, 0.375, -20.66]}, {"time": 0.5, "value": -20.66, "curve": [0.625, -20.66, 0.875, 20.88]}, {"time": 1, "value": 20.88}]}, "HornBtmLeft2b": {"rotate": [{"value": -1.5, "curve": [0.064, -0.64, 0.123, 0]}, {"time": 0.1667, "curve": [0.292, 0, 0.542, -5.3]}, {"time": 0.6667, "value": -5.3, "curve": [0.748, -5.3, 0.883, -3.02]}, {"time": 1, "value": -1.5}]}, "HornBtmLeft2c": {"rotate": [{"value": -14.58, "curve": [0.099, -7.54, 0.201, 0]}, {"time": 0.2667, "curve": [0.392, 0, 0.642, -26.8]}, {"time": 0.7667, "value": -26.8, "curve": [0.826, -26.8, 0.912, -20.89]}, {"time": 1, "value": -14.58}]}, "HornBtmLeft2d": {"rotate": [{"value": -32.04, "curve": [0.117, -11.54, 0.252, 19.21]}, {"time": 0.3333, "value": 19.21, "curve": [0.458, 19.21, 0.708, -52.35]}, {"time": 0.8333, "value": -52.35, "curve": [0.877, -52.35, 0.936, -43.64]}, {"time": 1, "value": -32.04}]}, "TentacleLeftBtm": {"rotate": [{"curve": [0.125, 0, 0.375, 33.59]}, {"time": 0.5, "value": 33.59, "curve": [0.625, 33.59, 0.875, 0]}, {"time": 1}]}, "HornBtmLeft3b": {"rotate": [{"value": -37.17, "curve": [0.064, -42.43, 0.123, -46.37]}, {"time": 0.1667, "value": -46.37, "curve": [0.292, -46.37, 0.542, -13.95]}, {"time": 0.6667, "value": -13.95, "curve": [0.748, -13.95, 0.883, -27.88]}, {"time": 1, "value": -37.17}]}, "HornBtmLeft3c": {"rotate": [{"value": 50.29, "curve": [0.099, 35.4, 0.201, 19.45]}, {"time": 0.2667, "value": 19.45, "curve": [0.392, 19.45, 0.642, 76.14]}, {"time": 0.7667, "value": 76.14, "curve": [0.826, 76.14, 0.912, 63.64]}, {"time": 1, "value": 50.29}]}, "HornBtmLeft3d": {"rotate": [{"value": 89.42, "curve": [0.117, 60.75, 0.252, 17.74]}, {"time": 0.3333, "value": 17.74, "curve": [0.458, 17.74, 0.708, 117.82]}, {"time": 0.8333, "value": 117.82, "curve": [0.877, 117.82, 0.936, 105.65]}, {"time": 1, "value": 89.42}]}, "HornBtmLeft3d2": {"rotate": [{"value": 89.42, "curve": [0.117, 60.75, 0.252, 17.74]}, {"time": 0.3333, "value": 17.74, "curve": [0.458, 17.74, 0.708, 117.82]}, {"time": 0.8333, "value": 117.82, "curve": [0.877, 117.82, 0.936, 105.65]}, {"time": 1, "value": 89.42}]}, "HornBtmLeft3c2": {"rotate": [{"value": 50.29, "curve": [0.099, 35.4, 0.201, 19.45]}, {"time": 0.2667, "value": 19.45, "curve": [0.392, 19.45, 0.642, 76.14]}, {"time": 0.7667, "value": 76.14, "curve": [0.826, 76.14, 0.912, 63.64]}, {"time": 1, "value": 50.29}]}, "HornBtmLeft3b2": {"rotate": [{"value": -37.17, "curve": [0.064, -42.43, 0.123, -46.37]}, {"time": 0.1667, "value": -46.37, "curve": [0.292, -46.37, 0.542, -13.95]}, {"time": 0.6667, "value": -13.95, "curve": [0.748, -13.95, 0.883, -27.88]}, {"time": 1, "value": -37.17}]}, "TentacleLeftBtm2": {"rotate": [{"curve": [0.125, 0, 0.375, -26.24]}, {"time": 0.5, "value": -26.24, "curve": [0.625, -26.24, 0.875, 0]}, {"time": 1}]}, "HornBtmLeft2d2": {"rotate": [{"value": -32.04, "curve": [0.117, -11.54, 0.252, 19.21]}, {"time": 0.3333, "value": 19.21, "curve": [0.458, 19.21, 0.708, -52.35]}, {"time": 0.8333, "value": -52.35, "curve": [0.877, -52.35, 0.936, -43.64]}, {"time": 1, "value": -32.04}]}, "HornBtmLeft2c2": {"rotate": [{"value": -14.58, "curve": [0.099, -7.54, 0.201, 0]}, {"time": 0.2667, "curve": [0.392, 0, 0.642, -26.8]}, {"time": 0.7667, "value": -26.8, "curve": [0.826, -26.8, 0.912, -20.89]}, {"time": 1, "value": -14.58}]}, "HornBtmLeft2b2": {"rotate": [{"value": -1.5, "curve": [0.064, -0.64, 0.123, 0]}, {"time": 0.1667, "curve": [0.292, 0, 0.542, -5.3]}, {"time": 0.6667, "value": -5.3, "curve": [0.748, -5.3, 0.883, -3.02]}, {"time": 1, "value": -1.5}]}, "TentacleLeftTop2": {"rotate": [{"value": -32.65, "curve": [0.125, -32.65, 0.375, 8.41]}, {"time": 0.5, "value": 8.41, "curve": [0.625, 8.41, 0.875, -32.65]}, {"time": 1, "value": -32.65}]}}, "path": {"PATH": {"position": [{"time": 0.1667, "value": 0.0365, "curve": [0.317, 0.0365, 0.727, 0.3074]}, {"time": 0.7667, "value": 0.4375}], "mix": [{"time": 0.1667, "mixRotate": 0, "mixX": 0}, {"time": 0.2667}]}}, "attachments": {"default": {"path": {"path": {"deform": [{"vertices": [-238.27084, -161.23645, -5.94983, -41.65036, 160.65004, 368.8997, 345.4281, 412.25732, 65.45046, 541.44995, -232.04977, 618.8001, -124.00009, 549.67395, -59.49948, 725.9005, 3.86092, 1035.7766, -142.79974, 1457.7499, -142.79974, 1457.7499, -142.79974, 1457.75]}]}}}}}, "intro": {"bones": {"HornBtmLeft3d2": {"rotate": [{"curve": [0.042, 0, 0.125, 61.32]}, {"time": 0.1667, "value": 61.32, "curve": [0.557, 61.91, 0.767, 109.69]}, {"time": 0.9667, "value": 109.69, "curve": [1.008, 109.69, 1.092, 0]}, {"time": 1.1333, "curve": "stepped"}, {"time": 1.2333, "curve": [1.308, 0, 1.458, 89.42]}, {"time": 1.5333, "value": 89.42, "curve": [1.615, 60.75, 1.71, 17.74]}, {"time": 1.7667, "value": 17.74, "curve": [1.85, 17.74, 2.017, 117.82]}, {"time": 2.1, "value": 117.82, "curve": [2.126, 117.82, 2.162, 105.65]}, {"time": 2.2, "value": 89.42, "curve": [2.282, 60.75, 2.377, 17.74]}, {"time": 2.4333, "value": 17.74, "curve": [2.517, 17.74, 2.683, 117.82]}, {"time": 2.7667, "value": 117.82, "curve": [2.793, 117.82, 2.828, 105.65]}, {"time": 2.8667, "value": 89.42}]}, "HornBtmLeft3c2": {"rotate": [{"curve": [0.042, 0, 0.125, 36.26]}, {"time": 0.1667, "value": 36.26, "curve": [0.557, 36.87, 0.767, 86.6]}, {"time": 0.9667, "value": 86.6, "curve": [1.008, 86.6, 1.092, 0]}, {"time": 1.1333, "curve": "stepped"}, {"time": 1.2333, "curve": [1.308, 0, 1.458, 50.29]}, {"time": 1.5333, "value": 50.29, "curve": [1.595, 35.4, 1.659, 19.45]}, {"time": 1.7, "value": 19.45, "curve": [1.783, 19.45, 1.95, 76.14]}, {"time": 2.0333, "value": 76.14, "curve": [2.075, 76.14, 2.137, 63.64]}, {"time": 2.2, "value": 50.29, "curve": [2.262, 35.4, 2.325, 19.45]}, {"time": 2.3667, "value": 19.45, "curve": [2.45, 19.45, 2.617, 76.14]}, {"time": 2.7, "value": 76.14, "curve": [2.742, 76.14, 2.804, 63.64]}, {"time": 2.8667, "value": 50.29}]}, "HornBtmLeft3b2": {"rotate": [{"curve": [0.042, 0, 0.125, -130.89]}, {"time": 0.1667, "value": -130.89, "curve": [0.557, -130.06, 0.767, -62.41]}, {"time": 0.9667, "value": -62.41, "curve": [1.008, -62.41, 1.092, -39.52]}, {"time": 1.1333, "value": -39.52, "curve": [1.158, -39.52, 1.208, 0]}, {"time": 1.2333, "curve": [1.308, 0, 1.458, -37.17]}, {"time": 1.5333, "value": -37.17, "curve": [1.572, -42.43, 1.607, -46.37]}, {"time": 1.6333, "value": -46.37, "curve": [1.717, -46.37, 1.883, -13.95]}, {"time": 1.9667, "value": -13.95, "curve": [2.023, -13.95, 2.118, -27.88]}, {"time": 2.2, "value": -37.17, "curve": [2.238, -42.43, 2.274, -46.37]}, {"time": 2.3, "value": -46.37, "curve": [2.383, -46.37, 2.55, -13.95]}, {"time": 2.6333, "value": -13.95, "curve": [2.69, -13.95, 2.785, -27.88]}, {"time": 2.8667, "value": -37.17}]}, "HornBtmLeft2d2": {"rotate": [{"curve": [0.042, 0, 0.125, -74.14]}, {"time": 0.1667, "value": -74.14, "curve": [0.557, -73.49, 0.767, -20.47]}, {"time": 0.9667, "value": -20.47, "curve": [1.008, -20.47, 1.092, -26.46]}, {"time": 1.1333, "value": -26.46, "curve": [1.158, -26.46, 1.208, 0]}, {"time": 1.2333, "curve": [1.308, 0, 1.458, -32.04]}, {"time": 1.5333, "value": -32.04, "curve": [1.615, -11.54, 1.71, 19.21]}, {"time": 1.7667, "value": 19.21, "curve": [1.85, 19.21, 2.017, -52.35]}, {"time": 2.1, "value": -52.35, "curve": [2.126, -52.35, 2.162, -43.64]}, {"time": 2.2, "value": -32.04, "curve": [2.282, -11.54, 2.377, 19.21]}, {"time": 2.4333, "value": 19.21, "curve": [2.517, 19.21, 2.683, -52.35]}, {"time": 2.7667, "value": -52.35, "curve": [2.793, -52.35, 2.828, -43.64]}, {"time": 2.8667, "value": -32.04}]}, "HornBtmLeft2c2": {"rotate": [{"curve": [0.042, 0, 0.125, -17.97]}, {"time": 0.1667, "value": -17.97}, {"time": 0.9667, "value": 17.76, "curve": [1.008, 17.76, 1.092, -12.48]}, {"time": 1.1333, "value": -12.48, "curve": [1.158, -12.48, 1.208, 0]}, {"time": 1.2333, "curve": [1.308, 0, 1.458, -14.58]}, {"time": 1.5333, "value": -14.58, "curve": [1.595, -7.54, 1.659, 0]}, {"time": 1.7, "curve": [1.783, 0, 1.95, -26.8]}, {"time": 2.0333, "value": -26.8, "curve": [2.075, -26.8, 2.137, -20.89]}, {"time": 2.2, "value": -14.58, "curve": [2.262, -7.54, 2.325, 0]}, {"time": 2.3667, "curve": [2.45, 0, 2.617, -26.8]}, {"time": 2.7, "value": -26.8, "curve": [2.742, -26.8, 2.804, -20.89]}, {"time": 2.8667, "value": -14.58}]}, "HornBtmLeft2b2": {"rotate": [{"curve": [0.042, 0, 0.125, 25.14]}, {"time": 0.1667, "value": 25.14}, {"time": 0.9667, "value": 66.29, "curve": [1.008, 66.29, 1.092, -16.53]}, {"time": 1.1333, "value": -16.53, "curve": [1.158, -16.53, 1.208, 0]}, {"time": 1.2333, "curve": [1.308, 0, 1.458, -1.5]}, {"time": 1.5333, "value": -1.5, "curve": [1.572, -0.64, 1.607, 0]}, {"time": 1.6333, "curve": [1.717, 0, 1.883, -5.3]}, {"time": 1.9667, "value": -5.3, "curve": [2.023, -5.3, 2.118, -3.02]}, {"time": 2.2, "value": -1.5, "curve": [2.238, -0.64, 2.274, 0]}, {"time": 2.3, "curve": [2.383, 0, 2.55, -5.3]}, {"time": 2.6333, "value": -5.3, "curve": [2.69, -5.3, 2.785, -3.02]}, {"time": 2.8667, "value": -1.5}]}, "TentacleLeftTop": {"rotate": [{"curve": [0.042, 0, 0.125, -29.74]}, {"time": 0.1667, "value": -29.74, "curve": [0.557, -29.11, 0.767, 22.22]}, {"time": 0.9667, "value": 22.22, "curve": [1.008, 22.22, 1.092, -5.61]}, {"time": 1.1333, "value": -5.61, "curve": [1.158, -5.61, 1.208, 0]}, {"time": 1.2333, "curve": [1.308, 0, 1.458, 20.88]}, {"time": 1.5333, "value": 20.88, "curve": [1.617, 20.88, 1.783, -20.66]}, {"time": 1.8667, "value": -20.66, "curve": [1.95, -20.66, 2.117, 20.88]}, {"time": 2.2, "value": 20.88, "curve": [2.283, 20.88, 2.45, -20.66]}, {"time": 2.5333, "value": -20.66, "curve": [2.617, -20.66, 2.783, 20.88]}, {"time": 2.8667, "value": 20.88}]}, "HornBtmLeft2b": {"rotate": [{"curve": [0.042, 0, 0.125, 25.14]}, {"time": 0.1667, "value": 25.14}, {"time": 0.9667, "value": 66.29, "curve": [1.008, 66.29, 1.092, -16.53]}, {"time": 1.1333, "value": -16.53, "curve": [1.158, -16.53, 1.208, 0]}, {"time": 1.2333, "curve": [1.308, 0, 1.458, -1.5]}, {"time": 1.5333, "value": -1.5, "curve": [1.572, -0.64, 1.607, 0]}, {"time": 1.6333, "curve": [1.717, 0, 1.883, -5.3]}, {"time": 1.9667, "value": -5.3, "curve": [2.023, -5.3, 2.118, -3.02]}, {"time": 2.2, "value": -1.5, "curve": [2.238, -0.64, 2.274, 0]}, {"time": 2.3, "curve": [2.383, 0, 2.55, -5.3]}, {"time": 2.6333, "value": -5.3, "curve": [2.69, -5.3, 2.785, -3.02]}, {"time": 2.8667, "value": -1.5}]}, "HornBtmLeft2c": {"rotate": [{"curve": [0.042, 0, 0.125, -17.97]}, {"time": 0.1667, "value": -17.97}, {"time": 0.9667, "value": 17.76, "curve": [1.008, 17.76, 1.092, -12.48]}, {"time": 1.1333, "value": -12.48, "curve": [1.158, -12.48, 1.208, 0]}, {"time": 1.2333, "curve": [1.308, 0, 1.458, -14.58]}, {"time": 1.5333, "value": -14.58, "curve": [1.595, -7.54, 1.659, 0]}, {"time": 1.7, "curve": [1.783, 0, 1.95, -26.8]}, {"time": 2.0333, "value": -26.8, "curve": [2.075, -26.8, 2.137, -20.89]}, {"time": 2.2, "value": -14.58, "curve": [2.262, -7.54, 2.325, 0]}, {"time": 2.3667, "curve": [2.45, 0, 2.617, -26.8]}, {"time": 2.7, "value": -26.8, "curve": [2.742, -26.8, 2.804, -20.89]}, {"time": 2.8667, "value": -14.58}]}, "HornBtmLeft2d": {"rotate": [{"curve": [0.042, 0, 0.125, -74.14]}, {"time": 0.1667, "value": -74.14, "curve": [0.557, -73.49, 0.767, -20.47]}, {"time": 0.9667, "value": -20.47, "curve": [1.008, -20.47, 1.092, -26.46]}, {"time": 1.1333, "value": -26.46, "curve": [1.158, -26.46, 1.208, 0]}, {"time": 1.2333, "curve": [1.308, 0, 1.458, -32.04]}, {"time": 1.5333, "value": -32.04, "curve": [1.615, -11.54, 1.71, 19.21]}, {"time": 1.7667, "value": 19.21, "curve": [1.85, 19.21, 2.017, -52.35]}, {"time": 2.1, "value": -52.35, "curve": [2.126, -52.35, 2.162, -43.64]}, {"time": 2.2, "value": -32.04, "curve": [2.282, -11.54, 2.377, 19.21]}, {"time": 2.4333, "value": 19.21, "curve": [2.517, 19.21, 2.683, -52.35]}, {"time": 2.7667, "value": -52.35, "curve": [2.793, -52.35, 2.828, -43.64]}, {"time": 2.8667, "value": -32.04}]}, "TentacleLeftBtm": {"rotate": [{"curve": [0.042, 0, 0.125, 4.28]}, {"time": 0.1667, "value": 4.28, "curve": [0.557, 4.54, 0.767, 26.01]}, {"time": 0.9667, "value": 26.01, "curve": [1.008, 26.01, 1.092, -6.23]}, {"time": 1.1333, "value": -6.23, "curve": [1.158, -6.23, 1.208, 0]}, {"time": 1.2333, "curve": "stepped"}, {"time": 1.5333, "curve": [1.617, 0, 1.783, 33.59]}, {"time": 1.8667, "value": 33.59, "curve": [1.95, 33.59, 2.117, 0]}, {"time": 2.2}, {"time": 2.5333, "value": 33.59, "curve": [2.617, 33.59, 2.783, 0]}, {"time": 2.8667}]}, "HornBtmLeft3b": {"rotate": [{"curve": [0.042, 0, 0.125, -130.89]}, {"time": 0.1667, "value": -130.89, "curve": [0.557, -130.06, 0.767, -62.41]}, {"time": 0.9667, "value": -62.41, "curve": [1.008, -62.41, 1.092, -39.52]}, {"time": 1.1333, "value": -39.52, "curve": [1.158, -39.52, 1.208, 0]}, {"time": 1.2333, "curve": [1.308, 0, 1.458, -37.17]}, {"time": 1.5333, "value": -37.17, "curve": [1.572, -42.43, 1.607, -46.37]}, {"time": 1.6333, "value": -46.37, "curve": [1.717, -46.37, 1.883, -13.95]}, {"time": 1.9667, "value": -13.95, "curve": [2.023, -13.95, 2.118, -27.88]}, {"time": 2.2, "value": -37.17, "curve": [2.238, -42.43, 2.274, -46.37]}, {"time": 2.3, "value": -46.37, "curve": [2.383, -46.37, 2.55, -13.95]}, {"time": 2.6333, "value": -13.95, "curve": [2.69, -13.95, 2.785, -27.88]}, {"time": 2.8667, "value": -37.17}]}, "HornBtmLeft3c": {"rotate": [{"curve": [0.042, 0, 0.125, 36.26]}, {"time": 0.1667, "value": 36.26, "curve": [0.557, 36.87, 0.767, 86.6]}, {"time": 0.9667, "value": 86.6, "curve": [1.008, 86.6, 1.092, 0]}, {"time": 1.1333, "curve": "stepped"}, {"time": 1.2333, "curve": [1.308, 0, 1.458, 50.29]}, {"time": 1.5333, "value": 50.29, "curve": [1.595, 35.4, 1.659, 19.45]}, {"time": 1.7, "value": 19.45, "curve": [1.783, 19.45, 1.95, 76.14]}, {"time": 2.0333, "value": 76.14, "curve": [2.075, 76.14, 2.137, 63.64]}, {"time": 2.2, "value": 50.29, "curve": [2.262, 35.4, 2.325, 19.45]}, {"time": 2.3667, "value": 19.45, "curve": [2.45, 19.45, 2.617, 76.14]}, {"time": 2.7, "value": 76.14, "curve": [2.742, 76.14, 2.804, 63.64]}, {"time": 2.8667, "value": 50.29}]}, "HornBtmLeft3d": {"rotate": [{"curve": [0.042, 0, 0.125, 61.32]}, {"time": 0.1667, "value": 61.32, "curve": [0.557, 61.91, 0.767, 109.69]}, {"time": 0.9667, "value": 109.69, "curve": [1.008, 109.69, 1.092, 0]}, {"time": 1.1333, "curve": "stepped"}, {"time": 1.2333, "curve": [1.308, 0, 1.458, 89.42]}, {"time": 1.5333, "value": 89.42, "curve": [1.615, 60.75, 1.71, 17.74]}, {"time": 1.7667, "value": 17.74, "curve": [1.85, 17.74, 2.017, 117.82]}, {"time": 2.1, "value": 117.82, "curve": [2.126, 117.82, 2.162, 105.65]}, {"time": 2.2, "value": 89.42, "curve": [2.282, 60.75, 2.377, 17.74]}, {"time": 2.4333, "value": 17.74, "curve": [2.517, 17.74, 2.683, 117.82]}, {"time": 2.7667, "value": 117.82, "curve": [2.793, 117.82, 2.828, 105.65]}, {"time": 2.8667, "value": 89.42}]}, "TentacleLeftTop2": {"rotate": [{"curve": [0.042, 0, 0.125, 22.8]}, {"time": 0.1667, "value": 22.8, "curve": [0.557, 22.23, 0.767, -24.01]}, {"time": 0.9667, "value": -24.01, "curve": [1.008, -24.01, 1.092, -5.61]}, {"time": 1.1333, "value": -5.61, "curve": [1.158, -5.61, 1.208, 0]}, {"time": 1.2333, "curve": [1.308, 0, 1.458, -32.65]}, {"time": 1.5333, "value": -32.65, "curve": [1.617, -32.65, 1.783, 8.41]}, {"time": 1.8667, "value": 8.41, "curve": [1.95, 8.41, 2.117, -32.65]}, {"time": 2.2, "value": -32.65, "curve": [2.283, -32.65, 2.45, 8.41]}, {"time": 2.5333, "value": 8.41, "curve": [2.617, 8.41, 2.783, -32.65]}, {"time": 2.8667, "value": -32.65}]}, "Main": {"rotate": [{"value": 0.13, "curve": [0.088, 0.91, 0.174, 1.64]}, {"time": 0.2333, "value": 1.64, "curve": [0.35, 1.64, 0.583, -1.67]}, {"time": 0.7, "value": -1.67, "curve": [0.766, -1.67, 0.868, -0.74]}, {"time": 0.9667, "value": 0.13, "curve": [1.055, 0.91, 1.141, 1.64]}, {"time": 1.2, "value": 1.64, "curve": [1.308, 1.64, 1.525, -1.67]}, {"time": 1.6333, "value": -1.67, "curve": [1.699, -1.67, 1.801, -0.74]}, {"time": 1.9, "value": 0.13, "curve": [1.988, 0.91, 2.074, 1.64]}, {"time": 2.1333, "value": 1.64, "curve": [2.25, 1.64, 2.483, -1.67]}, {"time": 2.6, "value": -1.67, "curve": [2.666, -1.67, 2.768, -0.74]}, {"time": 2.8667, "value": 0.13}], "translate": [{"curve": [0.242, 0, 0.725, 0, 0.242, 0, 0.725, 839.61]}, {"time": 0.9667, "y": 839.61, "curve": [1.133, 0, 1.467, 0, 1.133, 839.61, 1.467, 683.3]}, {"time": 1.6333, "y": 683.3, "curve": [1.783, 0, 2.083, 0, 1.783, 683.3, 2.083, 839.61]}, {"time": 2.2333, "y": 839.61, "curve": [2.392, 0, 2.708, 0, 2.392, 839.61, 2.708, 0]}, {"time": 2.8667}], "scale": [{"x": 1.004, "y": 0.987, "curve": [0.088, 1.024, 0.174, 1.043, 0.088, 0.972, 0.174, 0.958]}, {"time": 0.2333, "x": 1.043, "y": 0.958, "curve": [0.35, 1.043, 0.583, 0.958, 0.35, 0.958, 0.583, 1.021]}, {"time": 0.7, "x": 0.958, "y": 1.021, "curve": [0.766, 0.958, 0.868, 0.982, 0.766, 1.021, 0.868, 1.003]}, {"time": 0.9667, "x": 1.004, "y": 0.987, "curve": [1.055, 1.024, 1.141, 1.043, 1.055, 0.972, 1.141, 0.958]}, {"time": 1.2, "x": 1.043, "y": 0.958, "curve": [1.308, 1.043, 1.525, 0.958, 1.308, 0.958, 1.525, 1.021]}, {"time": 1.6333, "x": 0.958, "y": 1.021, "curve": [1.699, 0.958, 1.801, 0.982, 1.699, 1.021, 1.801, 1.003]}, {"time": 1.9, "x": 1.004, "y": 0.987, "curve": [1.988, 1.024, 2.074, 1.043, 1.988, 0.972, 2.074, 0.958]}, {"time": 2.1333, "x": 1.043, "y": 0.958, "curve": [2.25, 1.043, 2.483, 0.958, 2.25, 0.958, 2.483, 1.021]}, {"time": 2.6, "x": 0.958, "y": 1.021, "curve": [2.666, 0.958, 2.768, 0.982, 2.666, 1.021, 2.768, 1.003]}, {"time": 2.8667, "x": 1.004, "y": 0.987}]}, "FACE": {"translate": [{"x": 0.13, "y": 6.71, "curve": [0.076, 0.21, 0.148, 0.28, 0.076, 11.82, 0.148, 16.08]}, {"time": 0.2, "x": 0.28, "y": 16.08, "curve": [0.317, 0.28, 0.55, -0.11, 0.317, 16.08, 0.55, -9.38]}, {"time": 0.6667, "x": -0.11, "y": -9.38, "curve": [0.74, -0.11, 0.858, 0.02, 0.74, -9.38, 0.858, -0.44]}, {"time": 0.9667, "x": 0.13, "y": 6.71, "curve": [1.043, 0.21, 1.115, 0.28, 1.043, 11.82, 1.115, 16.08]}, {"time": 1.1667, "x": 0.28, "y": 16.08, "curve": [1.275, 0.28, 1.492, -0.11, 1.275, 16.08, 1.492, -9.38]}, {"time": 1.6, "x": -0.11, "y": -9.38, "curve": [1.674, -0.11, 1.791, 0.02, 1.674, -9.38, 1.791, -0.44]}, {"time": 1.9, "x": 0.13, "y": 6.71, "curve": [1.976, 0.21, 2.048, 0.28, 1.976, 11.82, 2.048, 16.08]}, {"time": 2.1, "x": 0.28, "y": 16.08, "curve": [2.217, 0.28, 2.45, -0.11, 2.217, 16.08, 2.45, -9.38]}, {"time": 2.5667, "x": -0.11, "y": -9.38, "curve": [2.64, -0.11, 2.758, 0.02, 2.64, -9.38, 2.758, -0.44]}, {"time": 2.8667, "x": 0.13, "y": 6.71}]}, "HornBtmLeft": {"rotate": [{"value": 3.6, "curve": [0.012, 3.71, 0.023, 3.76]}, {"time": 0.0333, "value": 3.76, "curve": [0.062, 3.76, 0.096, 3.42]}, {"time": 0.1333, "value": 2.87, "curve": [0.261, 1.16, 0.436, -3.1]}, {"time": 0.5333, "value": -3.1, "curve": [0.64, -3.1, 0.847, 2.82]}, {"time": 0.9667, "value": 3.6, "curve": [0.978, 3.71, 0.99, 3.76]}, {"time": 1, "value": 3.76, "curve": [1.028, 3.76, 1.063, 3.42]}, {"time": 1.1, "value": 2.87, "curve": [1.228, 1.16, 1.403, -3.1]}, {"time": 1.5, "value": -3.1, "curve": [1.599, -3.1, 1.789, 2.82]}, {"time": 1.9, "value": 3.6, "curve": [1.912, 3.71, 1.923, 3.76]}, {"time": 1.9333, "value": 3.76, "curve": [1.962, 3.76, 1.996, 3.42]}, {"time": 2.0333, "value": 2.87, "curve": [2.161, 1.16, 2.336, -3.1]}, {"time": 2.4333, "value": -3.1, "curve": [2.54, -3.1, 2.747, 2.82]}, {"time": 2.8667, "value": 3.6}]}, "HornTopLeft": {"rotate": [{"value": 2.87, "curve": [0.128, 1.16, 0.303, -3.1]}, {"time": 0.4, "value": -3.1, "curve": [0.517, -3.1, 0.75, 3.76]}, {"time": 0.8667, "value": 3.76, "curve": [0.895, 3.76, 0.929, 3.42]}, {"time": 0.9667, "value": 2.87, "curve": [1.095, 1.16, 1.27, -3.1]}, {"time": 1.3667, "value": -3.1, "curve": [1.475, -3.1, 1.692, 3.76]}, {"time": 1.8, "value": 3.76, "curve": [1.828, 3.76, 1.863, 3.42]}, {"time": 1.9, "value": 2.87, "curve": [2.028, 1.16, 2.203, -3.1]}, {"time": 2.3, "value": -3.1, "curve": [2.417, -3.1, 2.65, 3.76]}, {"time": 2.7667, "value": 3.76, "curve": [2.795, 3.76, 2.829, 3.42]}, {"time": 2.8667, "value": 2.87}]}, "HornTopRight": {"rotate": [{"value": -2.67, "curve": [0.128, -1.1, 0.303, 2.82]}, {"time": 0.4, "value": 2.82, "curve": [0.517, 2.82, 0.75, -3.49]}, {"time": 0.8667, "value": -3.49, "curve": [0.895, -3.49, 0.929, -3.17]}, {"time": 0.9667, "value": -2.67, "curve": [1.095, -1.1, 1.27, 2.82]}, {"time": 1.3667, "value": 2.82, "curve": [1.475, 2.82, 1.692, -3.49]}, {"time": 1.8, "value": -3.49, "curve": [1.828, -3.49, 1.863, -3.17]}, {"time": 1.9, "value": -2.67, "curve": [2.028, -1.1, 2.203, 2.82]}, {"time": 2.3, "value": 2.82, "curve": [2.417, 2.82, 2.65, -3.49]}, {"time": 2.7667, "value": -3.49, "curve": [2.795, -3.49, 2.829, -3.17]}, {"time": 2.8667, "value": -2.67}]}, "HornBtmRight": {"rotate": [{"value": -3.34, "curve": [0.012, -3.44, 0.023, -3.49]}, {"time": 0.0333, "value": -3.49, "curve": [0.062, -3.49, 0.096, -3.17]}, {"time": 0.1333, "value": -2.67, "curve": [0.4, -2.86, 0.764, -3.34]}, {"time": 0.9667, "value": -3.34, "curve": [0.978, -3.44, 0.99, -3.49]}, {"time": 1, "value": -3.49, "curve": [1.028, -3.49, 1.063, -3.17]}, {"time": 1.1, "value": -2.67, "curve": [1.228, -1.1, 1.403, 2.82]}, {"time": 1.5, "value": 2.82, "curve": [1.599, 2.82, 1.789, -2.62]}, {"time": 1.9, "value": -3.34, "curve": [1.912, -3.44, 1.923, -3.49]}, {"time": 1.9333, "value": -3.49, "curve": [1.962, -3.49, 1.996, -3.17]}, {"time": 2.0333, "value": -2.67, "curve": [2.161, -1.1, 2.336, 2.82]}, {"time": 2.4333, "value": 2.82, "curve": [2.54, 2.82, 2.747, -2.62]}, {"time": 2.8667, "value": -3.34}]}, "Drips1": {"translate": [{"x": 0.05, "y": 0.41, "curve": [0.109, -0.03, 0.226, -0.12, 0.109, -5.71, 0.226, -13.35]}, {"time": 0.3, "x": -0.12, "y": -13.35, "curve": [0.417, -0.12, 0.65, 0.14, 0.417, -13.35, 0.65, 8.42]}, {"time": 0.7667, "x": 0.14, "y": 8.42, "curve": [0.818, 0.14, 0.89, 0.1, 0.818, 8.42, 0.89, 4.78]}, {"time": 0.9667, "x": 0.05, "y": 0.41, "curve": [1.075, -0.03, 1.193, -0.12, 1.075, -5.71, 1.193, -13.35]}, {"time": 1.2667, "x": -0.12, "y": -13.35, "curve": [1.375, -0.12, 1.592, 0.14, 1.375, -13.35, 1.592, 8.42]}, {"time": 1.7, "x": 0.14, "y": 8.42, "curve": [1.752, 0.14, 1.824, 0.1, 1.752, 8.42, 1.824, 4.78]}, {"time": 1.9, "x": 0.05, "y": 0.41, "curve": [2.009, -0.03, 2.126, -0.12, 2.009, -5.71, 2.126, -13.35]}, {"time": 2.2, "x": -0.12, "y": -13.35, "curve": [2.317, -0.12, 2.55, 0.14, 2.317, -13.35, 2.55, 8.42]}, {"time": 2.6667, "x": 0.14, "y": 8.42, "curve": [2.718, 0.14, 2.79, 0.1, 2.718, 8.42, 2.79, 4.78]}, {"time": 2.8667, "x": 0.05, "y": 0.41}]}, "FACEBOB": {"translate": [{"x": -6, "y": 0.1, "curve": [0.125, -6, 0.375, 18, 0.125, 0.1, 0.375, -0.19]}, {"time": 0.5, "x": 18, "y": -0.19, "curve": [0.617, 18, 0.85, -6, 0.617, -0.19, 0.85, 0.1]}, {"time": 0.9667, "x": -6, "y": 0.1, "curve": [1.092, -6, 1.342, 18, 1.092, 0.1, 1.342, -0.19]}, {"time": 1.4667, "x": 18, "y": -0.19, "curve": [1.575, 18, 1.792, -6, 1.575, -0.19, 1.792, 0.1]}, {"time": 1.9, "x": -6, "y": 0.1, "curve": [2.025, -6, 2.275, 18, 2.025, 0.1, 2.275, -0.19]}, {"time": 2.4, "x": 18, "y": -0.19, "curve": [2.517, 18, 2.75, -6, 2.517, -0.19, 2.75, 0.1]}, {"time": 2.8667, "x": -6, "y": 0.1}]}, "Drips2": {"translate": [{"x": 0.14, "y": 7.93, "curve": [0.012, 0.14, 0.023, 0.14, 0.012, 8.25, 0.023, 8.42]}, {"time": 0.0333, "x": 0.14, "y": 8.42, "curve": [0.085, 0.14, 0.157, 0.1, 0.085, 8.42, 0.157, 4.78]}, {"time": 0.2333, "x": 0.05, "y": 0.41, "curve": [0.342, -0.03, 0.46, -0.12, 0.342, -5.71, 0.46, -13.35]}, {"time": 0.5333, "x": -0.12, "y": -13.35, "curve": [0.64, -0.12, 0.847, 0.11, 0.64, -13.35, 0.847, 5.42]}, {"time": 0.9667, "x": 0.14, "y": 7.93, "curve": [0.978, 0.14, 0.99, 0.14, 0.978, 8.25, 0.99, 8.42]}, {"time": 1, "x": 0.14, "y": 8.42, "curve": [1.052, 0.14, 1.124, 0.1, 1.052, 8.42, 1.124, 4.78]}, {"time": 1.2, "x": 0.05, "y": 0.41, "curve": [1.309, -0.03, 1.426, -0.12, 1.309, -5.71, 1.426, -13.35]}, {"time": 1.5, "x": -0.12, "y": -13.35, "curve": [1.599, -0.12, 1.789, 0.11, 1.599, -13.35, 1.789, 5.42]}, {"time": 1.9, "x": 0.14, "y": 7.93, "curve": [1.912, 0.14, 1.923, 0.14, 1.912, 8.25, 1.923, 8.42]}, {"time": 1.9333, "x": 0.14, "y": 8.42, "curve": [1.985, 0.14, 2.057, 0.1, 1.985, 8.42, 2.057, 4.78]}, {"time": 2.1333, "x": 0.05, "y": 0.41, "curve": [2.242, -0.03, 2.36, -0.12, 2.242, -5.71, 2.36, -13.35]}, {"time": 2.4333, "x": -0.12, "y": -13.35, "curve": [2.54, -0.12, 2.747, 0.11, 2.54, -13.35, 2.747, 5.42]}, {"time": 2.8667, "x": 0.14, "y": 7.93}]}}, "attachments": {"default": {"MASK": {"MASK": {"deform": [{"offset": 5, "vertices": [1085.2378, 6e-05, 1085.2378]}]}}}}}, "intro2": {"slots": {"MASK": {"attachment": [{"name": "MASK"}, {"time": 1}]}, "teleport": {"attachment": [{"time": 0.2333, "name": "images/Teleport_1"}, {"time": 0.3, "name": "images/Teleport_2"}, {"time": 0.3667, "name": "images/Teleport_3"}, {"time": 0.4333, "name": "images/Teleport_4"}, {"time": 0.5}, {"time": 1, "name": "images/Teleport_1"}, {"time": 1.0667, "name": "images/Teleport_2"}, {"time": 1.1333, "name": "images/Teleport_3"}, {"time": 1.2, "name": "images/Teleport_4"}, {"time": 1.2667}]}}, "bones": {"teleport": {"scale": [{"time": 0.5, "x": 1.004, "y": 0.987}]}, "Drips1": {"rotate": [{"value": 1.88}], "translate": [{"x": 0.05, "y": 0.41, "curve": "stepped"}, {"time": 1.0667, "x": 0.05, "y": 0.41}, {"time": 1.1333, "x": -182.67, "y": 4.43}, {"time": 1.2667, "x": 37.35, "y": 1.36}, {"time": 1.3333, "x": 0.05, "y": 0.41, "curve": "stepped"}, {"time": 2.4333, "x": 0.05, "y": 0.41}, {"time": 2.5, "x": -182.67, "y": 4.43}, {"time": 2.7, "x": 37.35, "y": 1.36}, {"time": 2.8333, "x": 0.05, "y": 0.41}]}, "FACEBOB": {"translate": [{"x": -6, "y": 0.1, "curve": [0.017, -6, 0.05, -5.56, 0.017, 0.1, 0.05, 23.37]}, {"time": 0.0667, "x": -5.56, "y": 23.37, "curve": [0.083, -5.56, 0.117, -6.46, 0.083, 23.37, 0.117, -66.76]}, {"time": 0.1333, "x": -6.46, "y": -66.76, "curve": [0.158, -6.46, 0.208, -5.21, 0.158, -66.76, 0.208, 58.54]}, {"time": 0.2333, "x": -5.21, "y": 58.54, "curve": [0.25, -5.21, 0.283, -6.2, 0.25, 58.54, 0.283, -40.38]}, {"time": 0.3, "x": -6.2, "y": -40.38}, {"time": 0.3667, "x": -6, "y": 0.1}]}, "Eye_Middle": {"scale": [{}, {"time": 0.0333, "x": 1.526, "y": 1.526}, {"time": 0.2}]}, "EyeLeft": {"scale": [{}, {"time": 0.0333, "x": 1.526, "y": 1.526}, {"time": 0.2}]}, "HornTopRight": {"rotate": [{"value": -2.67, "curve": [0.043, -7.74, 0.101, -20.42]}, {"time": 0.1333, "value": -20.42}, {"time": 0.3, "value": 11.84, "curve": "stepped"}, {"time": 0.5, "value": 11.84}, {"time": 0.5667, "value": -2.67, "curve": [0.695, 1.48, 0.87, 11.84]}, {"time": 0.9667, "value": 11.84}, {"time": 1.3333, "value": -2.67, "curve": [1.654, 1.48, 2.091, 11.84]}, {"time": 2.3333, "value": 11.84}, {"time": 2.8333, "value": -2.67}]}, "Main": {"rotate": [{"value": 0.13}], "translate": [{"curve": [0.025, 0, 0.075, 0, 0.025, 0, 0.075, 59.83]}, {"time": 0.1, "y": 59.83, "curve": [0.167, 0, 0.367, 0, 0.167, 59.83, 0.367, -432.32]}, {"time": 0.3667, "y": -1055.41, "curve": "stepped"}, {"time": 0.9667, "y": -1055.41}, {"time": 1.1667, "y": 59.83, "curve": [1.2, 0, 1.267, 0, 1.2, 59.83, 1.267, 0]}, {"time": 1.3, "curve": [1.558, 0, 2.075, 0, 1.558, 0, 2.075, 73.73]}, {"time": 2.3333, "y": 73.73, "curve": [2.385, 0, 2.508, 0, 2.385, 193.11, 2.508, 258.02]}, {"time": 2.5667, "y": 258.02, "curve": [2.617, 0, 2.717, 0, 2.617, 258.02, 2.717, 0]}, {"time": 2.7667}], "scale": [{"x": 1.004, "y": 0.987, "curve": [0.008, 1.004, 0.025, 0.769, 0.008, 0.987, 0.025, 1.281]}, {"time": 0.0333, "x": 0.769, "y": 1.281, "curve": [0.075, 0.769, 0.158, 1.304, 0.075, 1.281, 0.158, 0.756]}, {"time": 0.2, "x": 1.304, "y": 0.756, "curve": "stepped"}, {"time": 0.9667, "x": 1.304, "y": 0.756, "curve": [1.03, 1.149, 1.091, 1.004, 1.03, 0.875, 1.091, 0.987]}, {"time": 1.1333, "x": 1.004, "y": 0.987, "curve": [1.171, 0.883, 1.208, 0.769, 1.171, 1.139, 1.208, 1.281]}, {"time": 1.2333, "x": 0.769, "y": 1.281}, {"time": 1.3333, "x": 1.004, "y": 0.987, "curve": [1.583, 1.004, 2.083, 1.304, 1.583, 0.987, 2.083, 0.756]}, {"time": 2.3333, "x": 1.304, "y": 0.756, "curve": [2.396, 1.149, 2.458, 1.004, 2.396, 0.875, 2.458, 0.987]}, {"time": 2.5, "x": 1.004, "y": 0.987, "curve": [2.563, 0.883, 2.625, 0.769, 2.563, 1.139, 2.625, 1.281]}, {"time": 2.6667, "x": 0.769, "y": 1.281}, {"time": 2.8333, "x": 1.004, "y": 0.987}]}, "FACE": {"translate": [{"x": 0.13, "y": 6.71, "curve": [0.008, 0.13, 0.025, -31.31, 0.008, 6.71, 0.025, 7.26]}, {"time": 0.0333, "x": -31.31, "y": 7.26, "curve": [0.117, -31.31, 0.367, 13.97, 0.117, 7.26, 0.367, 6.47]}, {"time": 0.3667, "x": 71.29, "y": 5.47, "curve": "stepped"}, {"time": 0.9667, "x": -100.79, "y": 6.17, "curve": [1.147, -100.79, 1.142, 58.82, 1.147, 6.17, 1.142, 11.57]}, {"time": 1.2, "x": 58.82, "y": 11.57, "curve": [1.233, 58.82, 1.3, 0.13, 1.233, 11.57, 1.3, 6.71]}, {"time": 1.3333, "x": 0.13, "y": 6.71, "curve": [1.583, 0.13, 2.083, -100.79, 1.583, 6.71, 2.083, 6.17]}, {"time": 2.3333, "x": -100.79, "y": 6.17, "curve": [2.54, -100.79, 2.533, 58.82, 2.54, 6.17, 2.533, 11.57]}, {"time": 2.6, "x": 58.82, "y": 11.57, "curve": [2.658, 58.82, 2.775, 0.13, 2.658, 11.57, 2.775, 6.71]}, {"time": 2.8333, "x": 0.13, "y": 6.71}]}, "HornTopLeft": {"rotate": [{"value": 2.87, "curve": [0.043, 7.69, 0.101, 19.73]}, {"time": 0.1333, "value": 19.73}, {"time": 0.3, "value": -13.94, "curve": "stepped"}, {"time": 0.5, "value": -13.94}, {"time": 0.5667, "value": 2.87, "curve": [0.695, -1.93, 0.87, -13.94]}, {"time": 0.9667, "value": -13.94}, {"time": 1.3333, "value": 2.87, "curve": [1.654, -1.93, 2.091, -13.94]}, {"time": 2.3333, "value": -13.94}, {"time": 2.8333, "value": 2.87}]}, "HornBtmLeft": {"rotate": [{"value": 3.6, "curve": [0.047, 14.58, 0.092, 20.47]}, {"time": 0.1333, "value": 20.47}, {"time": 0.3, "value": -21.04, "curve": "stepped"}, {"time": 0.5, "value": -21.04}, {"time": 0.5667, "value": 3.6, "curve": [0.708, -12.44, 0.841, -21.04]}, {"time": 0.9667, "value": -21.04}, {"time": 1.3333, "value": 3.6, "curve": [1.686, -12.44, 2.02, -21.04]}, {"time": 2.3333, "value": -21.04}, {"time": 2.8333, "value": 3.6}]}, "HornBtmRight": {"rotate": [{"value": -3.34, "curve": [0.047, -14.9, 0.092, -21.09]}, {"time": 0.1333, "value": -21.09}, {"time": 0.3, "value": 9.97, "curve": "stepped"}, {"time": 0.5, "value": 9.97}, {"time": 0.5667, "value": -3.34, "curve": [0.708, 5.33, 0.841, 9.97]}, {"time": 0.9667, "value": 9.97}, {"time": 1.3333, "value": -3.34, "curve": [1.686, 5.33, 2.02, 9.97]}, {"time": 2.3333, "value": 9.97}, {"time": 2.8333, "value": -3.34}]}, "EyeRight": {"scale": [{}, {"time": 0.0333, "x": 1.526, "y": 1.526}, {"time": 0.2}]}, "Drips2": {"rotate": [{"value": 1.88}], "translate": [{"x": 0.05, "y": 0.41, "curve": "stepped"}, {"time": 1.0667, "x": 0.05, "y": 0.41}, {"time": 1.1333, "x": -182.67, "y": 4.43}, {"time": 1.2667, "x": 37.35, "y": 1.36}, {"time": 1.3333, "x": 0.05, "y": 0.41, "curve": "stepped"}, {"time": 2.4333, "x": 0.05, "y": 0.41}, {"time": 2.5, "x": -182.67, "y": 4.43}, {"time": 2.7, "x": 37.35, "y": 1.36}, {"time": 2.8333, "x": 0.05, "y": 0.41}]}, "HornBtmLeft3d2": {"rotate": [{"curve": [0.042, 0, 0.125, 61.32]}, {"time": 0.1667, "value": 61.32, "curve": "stepped"}, {"time": 0.8333, "value": 61.32, "curve": [0.85, 61.91, 0.858, 109.69]}, {"time": 0.8667, "value": 109.69, "curve": "stepped"}, {"time": 0.9667, "value": 109.69, "curve": [1.008, 109.69, 1.092, 0]}, {"time": 1.1333, "curve": "stepped"}, {"time": 1.2333, "curve": [1.308, 0, 1.458, 89.42]}, {"time": 1.5333, "value": 89.42, "curve": [1.615, 60.75, 1.71, 17.74]}, {"time": 1.7667, "value": 17.74, "curve": [1.85, 17.74, 2.017, 117.82]}, {"time": 2.1, "value": 117.82, "curve": [2.126, 117.82, 2.162, 105.65]}, {"time": 2.2, "value": 89.42, "curve": [2.282, 60.75, 2.377, 17.74]}, {"time": 2.4333, "value": 17.74, "curve": [2.517, 17.74, 2.683, 117.82]}, {"time": 2.7667, "value": 117.82, "curve": [2.793, 117.82, 2.828, 105.65]}, {"time": 2.8667, "value": 89.42}]}, "HornBtmLeft3c2": {"rotate": [{"curve": [0.042, 0, 0.125, 36.26]}, {"time": 0.1667, "value": 36.26, "curve": "stepped"}, {"time": 0.8333, "value": 36.26, "curve": [0.85, 36.87, 0.858, 86.6]}, {"time": 0.8667, "value": 86.6, "curve": "stepped"}, {"time": 0.9667, "value": 86.6, "curve": [1.008, 86.6, 1.092, 0]}, {"time": 1.1333, "curve": "stepped"}, {"time": 1.2333, "curve": [1.308, 0, 1.458, 50.29]}, {"time": 1.5333, "value": 50.29, "curve": [1.595, 35.4, 1.659, 19.45]}, {"time": 1.7, "value": 19.45, "curve": [1.783, 19.45, 1.95, 76.14]}, {"time": 2.0333, "value": 76.14, "curve": [2.075, 76.14, 2.137, 63.64]}, {"time": 2.2, "value": 50.29, "curve": [2.262, 35.4, 2.325, 19.45]}, {"time": 2.3667, "value": 19.45, "curve": [2.45, 19.45, 2.617, 76.14]}, {"time": 2.7, "value": 76.14, "curve": [2.742, 76.14, 2.804, 63.64]}, {"time": 2.8667, "value": 50.29}]}, "HornBtmLeft3b2": {"rotate": [{"curve": [0.042, 0, 0.125, -130.89]}, {"time": 0.1667, "value": -130.89, "curve": "stepped"}, {"time": 0.8333, "value": -130.89, "curve": [0.85, -130.06, 0.858, -62.41]}, {"time": 0.8667, "value": -62.41, "curve": "stepped"}, {"time": 0.9667, "value": -62.41, "curve": [1.008, -62.41, 1.092, -39.52]}, {"time": 1.1333, "value": -39.52, "curve": [1.158, -39.52, 1.208, 0]}, {"time": 1.2333, "curve": [1.308, 0, 1.458, -37.17]}, {"time": 1.5333, "value": -37.17, "curve": [1.572, -42.43, 1.607, -46.37]}, {"time": 1.6333, "value": -46.37, "curve": [1.717, -46.37, 1.883, -13.95]}, {"time": 1.9667, "value": -13.95, "curve": [2.023, -13.95, 2.118, -27.88]}, {"time": 2.2, "value": -37.17, "curve": [2.238, -42.43, 2.274, -46.37]}, {"time": 2.3, "value": -46.37, "curve": [2.383, -46.37, 2.55, -13.95]}, {"time": 2.6333, "value": -13.95, "curve": [2.69, -13.95, 2.785, -27.88]}, {"time": 2.8667, "value": -37.17}]}, "HornBtmLeft2d2": {"rotate": [{"curve": [0.042, 0, 0.125, -74.14]}, {"time": 0.1667, "value": -74.14, "curve": "stepped"}, {"time": 0.8333, "value": -74.14, "curve": [0.85, -73.49, 0.858, -20.47]}, {"time": 0.8667, "value": -20.47, "curve": "stepped"}, {"time": 0.9667, "value": -20.47, "curve": [1.008, -20.47, 1.092, -26.46]}, {"time": 1.1333, "value": -26.46, "curve": [1.158, -26.46, 1.208, 0]}, {"time": 1.2333, "curve": [1.308, 0, 1.458, -32.04]}, {"time": 1.5333, "value": -32.04, "curve": [1.615, -11.54, 1.71, 19.21]}, {"time": 1.7667, "value": 19.21, "curve": [1.85, 19.21, 2.017, -52.35]}, {"time": 2.1, "value": -52.35, "curve": [2.126, -52.35, 2.162, -43.64]}, {"time": 2.2, "value": -32.04, "curve": [2.282, -11.54, 2.377, 19.21]}, {"time": 2.4333, "value": 19.21, "curve": [2.517, 19.21, 2.683, -52.35]}, {"time": 2.7667, "value": -52.35, "curve": [2.793, -52.35, 2.828, -43.64]}, {"time": 2.8667, "value": -32.04}]}, "HornBtmLeft2c2": {"rotate": [{"curve": [0.042, 0, 0.125, -17.97]}, {"time": 0.1667, "value": -17.97, "curve": "stepped"}, {"time": 0.8333, "value": -17.97}, {"time": 0.8667, "value": 17.76, "curve": "stepped"}, {"time": 0.9667, "value": 17.76, "curve": [1.008, 17.76, 1.092, -12.48]}, {"time": 1.1333, "value": -12.48, "curve": [1.158, -12.48, 1.208, 0]}, {"time": 1.2333, "curve": [1.308, 0, 1.458, -14.58]}, {"time": 1.5333, "value": -14.58, "curve": [1.595, -7.54, 1.659, 0]}, {"time": 1.7, "curve": [1.783, 0, 1.95, -26.8]}, {"time": 2.0333, "value": -26.8, "curve": [2.075, -26.8, 2.137, -20.89]}, {"time": 2.2, "value": -14.58, "curve": [2.262, -7.54, 2.325, 0]}, {"time": 2.3667, "curve": [2.45, 0, 2.617, -26.8]}, {"time": 2.7, "value": -26.8, "curve": [2.742, -26.8, 2.804, -20.89]}, {"time": 2.8667, "value": -14.58}]}, "HornBtmLeft2b2": {"rotate": [{"curve": [0.042, 0, 0.125, 25.14]}, {"time": 0.1667, "value": 25.14, "curve": "stepped"}, {"time": 0.8333, "value": 25.14}, {"time": 0.8667, "value": 66.29, "curve": "stepped"}, {"time": 0.9667, "value": 66.29, "curve": [1.008, 66.29, 1.092, -16.53]}, {"time": 1.1333, "value": -16.53, "curve": [1.158, -16.53, 1.208, 0]}, {"time": 1.2333, "curve": [1.308, 0, 1.458, -1.5]}, {"time": 1.5333, "value": -1.5, "curve": [1.572, -0.64, 1.607, 0]}, {"time": 1.6333, "curve": [1.717, 0, 1.883, -5.3]}, {"time": 1.9667, "value": -5.3, "curve": [2.023, -5.3, 2.118, -3.02]}, {"time": 2.2, "value": -1.5, "curve": [2.238, -0.64, 2.274, 0]}, {"time": 2.3, "curve": [2.383, 0, 2.55, -5.3]}, {"time": 2.6333, "value": -5.3, "curve": [2.69, -5.3, 2.785, -3.02]}, {"time": 2.8667, "value": -1.5}]}, "TentacleLeftTop": {"rotate": [{"curve": [0.042, 0, 0.125, -29.74]}, {"time": 0.1667, "value": -29.74, "curve": "stepped"}, {"time": 0.8333, "value": -29.74, "curve": [0.85, -29.11, 0.858, 22.22]}, {"time": 0.8667, "value": 22.22, "curve": "stepped"}, {"time": 0.9667, "value": 22.22, "curve": [1.008, 22.22, 1.092, -5.61]}, {"time": 1.1333, "value": -5.61, "curve": [1.158, -5.61, 1.208, 0]}, {"time": 1.2333, "curve": [1.308, 0, 1.458, 20.88]}, {"time": 1.5333, "value": 20.88, "curve": [1.617, 20.88, 1.783, -20.66]}, {"time": 1.8667, "value": -20.66, "curve": [1.95, -20.66, 2.117, 20.88]}, {"time": 2.2, "value": 20.88, "curve": [2.283, 20.88, 2.45, -20.66]}, {"time": 2.5333, "value": -20.66, "curve": [2.617, -20.66, 2.783, 20.88]}, {"time": 2.8667, "value": 20.88}]}, "HornBtmLeft2b": {"rotate": [{"curve": [0.042, 0, 0.125, 25.14]}, {"time": 0.1667, "value": 25.14, "curve": "stepped"}, {"time": 0.8333, "value": 25.14}, {"time": 0.8667, "value": 66.29, "curve": "stepped"}, {"time": 0.9667, "value": 66.29, "curve": [1.008, 66.29, 1.092, -16.53]}, {"time": 1.1333, "value": -16.53, "curve": [1.158, -16.53, 1.208, 0]}, {"time": 1.2333, "curve": [1.308, 0, 1.458, -1.5]}, {"time": 1.5333, "value": -1.5, "curve": [1.572, -0.64, 1.607, 0]}, {"time": 1.6333, "curve": [1.717, 0, 1.883, -5.3]}, {"time": 1.9667, "value": -5.3, "curve": [2.023, -5.3, 2.118, -3.02]}, {"time": 2.2, "value": -1.5, "curve": [2.238, -0.64, 2.274, 0]}, {"time": 2.3, "curve": [2.383, 0, 2.55, -5.3]}, {"time": 2.6333, "value": -5.3, "curve": [2.69, -5.3, 2.785, -3.02]}, {"time": 2.8667, "value": -1.5}]}, "HornBtmLeft2c": {"rotate": [{"curve": [0.042, 0, 0.125, -17.97]}, {"time": 0.1667, "value": -17.97, "curve": "stepped"}, {"time": 0.8333, "value": -17.97}, {"time": 0.8667, "value": 17.76, "curve": "stepped"}, {"time": 0.9667, "value": 17.76, "curve": [1.008, 17.76, 1.092, -12.48]}, {"time": 1.1333, "value": -12.48, "curve": [1.158, -12.48, 1.208, 0]}, {"time": 1.2333, "curve": [1.308, 0, 1.458, -14.58]}, {"time": 1.5333, "value": -14.58, "curve": [1.595, -7.54, 1.659, 0]}, {"time": 1.7, "curve": [1.783, 0, 1.95, -26.8]}, {"time": 2.0333, "value": -26.8, "curve": [2.075, -26.8, 2.137, -20.89]}, {"time": 2.2, "value": -14.58, "curve": [2.262, -7.54, 2.325, 0]}, {"time": 2.3667, "curve": [2.45, 0, 2.617, -26.8]}, {"time": 2.7, "value": -26.8, "curve": [2.742, -26.8, 2.804, -20.89]}, {"time": 2.8667, "value": -14.58}]}, "HornBtmLeft2d": {"rotate": [{"curve": [0.042, 0, 0.125, -74.14]}, {"time": 0.1667, "value": -74.14, "curve": "stepped"}, {"time": 0.8333, "value": -74.14, "curve": [0.85, -73.49, 0.858, -20.47]}, {"time": 0.8667, "value": -20.47, "curve": "stepped"}, {"time": 0.9667, "value": -20.47, "curve": [1.008, -20.47, 1.092, -26.46]}, {"time": 1.1333, "value": -26.46, "curve": [1.158, -26.46, 1.208, 0]}, {"time": 1.2333, "curve": [1.308, 0, 1.458, -32.04]}, {"time": 1.5333, "value": -32.04, "curve": [1.615, -11.54, 1.71, 19.21]}, {"time": 1.7667, "value": 19.21, "curve": [1.85, 19.21, 2.017, -52.35]}, {"time": 2.1, "value": -52.35, "curve": [2.126, -52.35, 2.162, -43.64]}, {"time": 2.2, "value": -32.04, "curve": [2.282, -11.54, 2.377, 19.21]}, {"time": 2.4333, "value": 19.21, "curve": [2.517, 19.21, 2.683, -52.35]}, {"time": 2.7667, "value": -52.35, "curve": [2.793, -52.35, 2.828, -43.64]}, {"time": 2.8667, "value": -32.04}]}, "TentacleLeftBtm": {"rotate": [{"curve": [0.042, 0, 0.125, 4.28]}, {"time": 0.1667, "value": 4.28, "curve": "stepped"}, {"time": 0.8333, "value": 4.28, "curve": [0.85, 4.54, 0.858, 26.01]}, {"time": 0.8667, "value": 26.01, "curve": "stepped"}, {"time": 0.9667, "value": 26.01, "curve": [1.008, 26.01, 1.092, -6.23]}, {"time": 1.1333, "value": -6.23, "curve": [1.158, -6.23, 1.208, 0]}, {"time": 1.2333, "curve": "stepped"}, {"time": 1.5333, "curve": [1.617, 0, 1.783, 33.59]}, {"time": 1.8667, "value": 33.59, "curve": [1.95, 33.59, 2.117, 0]}, {"time": 2.2}, {"time": 2.5333, "value": 33.59, "curve": [2.617, 33.59, 2.783, 0]}, {"time": 2.8667}]}, "HornBtmLeft3b": {"rotate": [{"curve": [0.042, 0, 0.125, -130.89]}, {"time": 0.1667, "value": -130.89, "curve": "stepped"}, {"time": 0.8333, "value": -130.89, "curve": [0.85, -130.06, 0.858, -62.41]}, {"time": 0.8667, "value": -62.41, "curve": "stepped"}, {"time": 0.9667, "value": -62.41, "curve": [1.008, -62.41, 1.092, -39.52]}, {"time": 1.1333, "value": -39.52, "curve": [1.158, -39.52, 1.208, 0]}, {"time": 1.2333, "curve": [1.308, 0, 1.458, -37.17]}, {"time": 1.5333, "value": -37.17, "curve": [1.572, -42.43, 1.607, -46.37]}, {"time": 1.6333, "value": -46.37, "curve": [1.717, -46.37, 1.883, -13.95]}, {"time": 1.9667, "value": -13.95, "curve": [2.023, -13.95, 2.118, -27.88]}, {"time": 2.2, "value": -37.17, "curve": [2.238, -42.43, 2.274, -46.37]}, {"time": 2.3, "value": -46.37, "curve": [2.383, -46.37, 2.55, -13.95]}, {"time": 2.6333, "value": -13.95, "curve": [2.69, -13.95, 2.785, -27.88]}, {"time": 2.8667, "value": -37.17}]}, "HornBtmLeft3c": {"rotate": [{"curve": [0.042, 0, 0.125, 36.26]}, {"time": 0.1667, "value": 36.26, "curve": "stepped"}, {"time": 0.8333, "value": 36.26, "curve": [0.85, 36.87, 0.858, 86.6]}, {"time": 0.8667, "value": 86.6, "curve": "stepped"}, {"time": 0.9667, "value": 86.6, "curve": [1.008, 86.6, 1.092, 0]}, {"time": 1.1333, "curve": "stepped"}, {"time": 1.2333, "curve": [1.308, 0, 1.458, 50.29]}, {"time": 1.5333, "value": 50.29, "curve": [1.595, 35.4, 1.659, 19.45]}, {"time": 1.7, "value": 19.45, "curve": [1.783, 19.45, 1.95, 76.14]}, {"time": 2.0333, "value": 76.14, "curve": [2.075, 76.14, 2.137, 63.64]}, {"time": 2.2, "value": 50.29, "curve": [2.262, 35.4, 2.325, 19.45]}, {"time": 2.3667, "value": 19.45, "curve": [2.45, 19.45, 2.617, 76.14]}, {"time": 2.7, "value": 76.14, "curve": [2.742, 76.14, 2.804, 63.64]}, {"time": 2.8667, "value": 50.29}]}, "HornBtmLeft3d": {"rotate": [{"curve": [0.042, 0, 0.125, 61.32]}, {"time": 0.1667, "value": 61.32, "curve": "stepped"}, {"time": 0.8333, "value": 61.32, "curve": [0.85, 61.91, 0.858, 109.69]}, {"time": 0.8667, "value": 109.69, "curve": "stepped"}, {"time": 0.9667, "value": 109.69, "curve": [1.008, 109.69, 1.092, 0]}, {"time": 1.1333, "curve": "stepped"}, {"time": 1.2333, "curve": [1.308, 0, 1.458, 89.42]}, {"time": 1.5333, "value": 89.42, "curve": [1.615, 60.75, 1.71, 17.74]}, {"time": 1.7667, "value": 17.74, "curve": [1.85, 17.74, 2.017, 117.82]}, {"time": 2.1, "value": 117.82, "curve": [2.126, 117.82, 2.162, 105.65]}, {"time": 2.2, "value": 89.42, "curve": [2.282, 60.75, 2.377, 17.74]}, {"time": 2.4333, "value": 17.74, "curve": [2.517, 17.74, 2.683, 117.82]}, {"time": 2.7667, "value": 117.82, "curve": [2.793, 117.82, 2.828, 105.65]}, {"time": 2.8667, "value": 89.42}]}, "TentacleLeftTop2": {"rotate": [{"curve": [0.042, 0, 0.125, 22.8]}, {"time": 0.1667, "value": 22.8, "curve": [0.492, 22.16, 0.667, -29.74]}, {"time": 0.8333, "value": -29.74, "curve": [0.85, -29.11, 0.858, 22.22]}, {"time": 0.8667, "value": 22.22, "curve": [0.892, 22.22, 0.942, -24.01]}, {"time": 0.9667, "value": -24.01, "curve": [1.008, -24.01, 1.092, -5.61]}, {"time": 1.1333, "value": -5.61, "curve": [1.158, -5.61, 1.208, 0]}, {"time": 1.2333, "curve": [1.308, 0, 1.458, -32.65]}, {"time": 1.5333, "value": -32.65, "curve": [1.617, -32.65, 1.783, 8.41]}, {"time": 1.8667, "value": 8.41, "curve": [1.95, 8.41, 2.117, -32.65]}, {"time": 2.2, "value": -32.65, "curve": [2.283, -32.65, 2.45, 8.41]}, {"time": 2.5333, "value": 8.41, "curve": [2.617, 8.41, 2.783, -32.65]}, {"time": 2.8667, "value": -32.65}]}}, "path": {"PATH": {"position": [{"time": 0.9333}, {"time": 2.3333, "value": 1}], "mix": [{"time": 0.9333, "curve": "stepped"}, {"time": 2.2667}, {"time": 2.3333, "mixRotate": 0, "mixX": 0}]}}}, "summon": {"bones": {"Main": {"rotate": [{"value": 0.13, "curve": [0.05, 6.84, 0.1, 13.13]}, {"time": 0.1333, "value": 13.13, "curve": [0.192, 13.13, 0.308, -13.06]}, {"time": 0.3667, "value": -13.06, "curve": [0.482, -13.06, 0.66, -6.24]}, {"time": 0.8333, "value": 0.13}], "translate": [{"curve": [0.008, 0, 0.025, 0, 0.008, 0, 0.025, -106.72]}, {"time": 0.0333, "y": -106.72, "curve": [0.058, 0, 0.108, 0, 0.058, -106.72, 0.108, 178.8]}, {"time": 0.1333, "y": 178.8, "curve": [0.233, 0, 0.433, 0, 0.233, 178.8, 0.433, -121.33]}, {"time": 0.5333, "y": -121.33, "curve": [0.608, 0, 0.758, 0, 0.608, -121.33, 0.758, 0]}, {"time": 0.8333}], "scale": [{"x": 0.883, "y": 1.067, "curve": [0.008, 0.883, 0.025, 0.683, 0.008, 1.067, 0.025, 1.596]}, {"time": 0.0333, "x": 0.683, "y": 1.596, "curve": [0.075, 0.683, 0.158, 1.432, 0.075, 1.596, 0.158, 0.802]}, {"time": 0.2, "x": 1.432, "y": 0.802, "curve": [0.275, 1.432, 0.425, 0.867, 0.275, 0.802, 0.425, 1.216]}, {"time": 0.5, "x": 0.867, "y": 1.216, "curve": [0.583, 0.867, 0.75, 1.004, 0.583, 1.216, 0.75, 0.987]}, {"time": 0.8333, "x": 1.004, "y": 0.987}]}, "FACE": {"translate": [{"x": 0.39, "y": 19.18, "curve": [0.008, 0.39, 0.025, -63.03, 0.008, 19.18, 0.025, 16]}, {"time": 0.0333, "x": -63.03, "y": 16, "curve": [0.058, -63.03, 0.108, 93.27, 0.058, 16, 0.108, -11.25]}, {"time": 0.1333, "x": 93.27, "y": -11.25, "curve": [0.242, 93.27, 0.458, -79.18, 0.242, -11.25, 0.458, 2.67]}, {"time": 0.5667, "x": -79.18, "y": 2.67, "curve": [0.633, -79.18, 0.767, 0.13, 0.633, 2.67, 0.767, 6.71]}, {"time": 0.8333, "x": 0.13, "y": 6.71}]}, "HornBtmLeft": {"rotate": [{"value": 3.76, "curve": [0.017, 3.76, 0.05, 26.34]}, {"time": 0.0667, "value": 26.34, "curve": [0.108, 26.34, 0.192, -21.32]}, {"time": 0.2333, "value": -21.32, "curve": [0.308, -21.32, 0.458, 23.3]}, {"time": 0.5333, "value": 23.3, "curve": [0.608, 23.3, 0.758, 3.6]}, {"time": 0.8333, "value": 3.6}]}, "HornTopLeft": {"rotate": [{"value": 2.87, "curve": [0.008, 2.87, 0.025, 25.45]}, {"time": 0.0333, "value": 25.45, "curve": [0.075, 25.45, 0.158, -17.43]}, {"time": 0.2, "value": -17.43, "curve": [0.275, -17.43, 0.425, 22.4]}, {"time": 0.5, "value": 22.4, "curve": [0.583, 22.4, 0.75, 2.87]}, {"time": 0.8333, "value": 2.87}]}, "HornTopRight": {"rotate": [{"value": -2.67, "curve": [0.008, -2.67, 0.025, -30.19]}, {"time": 0.0333, "value": -30.19, "curve": [0.075, -30.19, 0.158, 16.13]}, {"time": 0.2, "value": 16.13, "curve": [0.275, 16.13, 0.425, -21.43]}, {"time": 0.5, "value": -21.43, "curve": [0.583, -21.43, 0.75, -2.67]}, {"time": 0.8333, "value": -2.67}]}, "HornBtmRight": {"rotate": [{"value": -3.49, "curve": [0.017, -3.49, 0.05, -31.01]}, {"time": 0.0667, "value": -31.01, "curve": [0.108, -31.01, 0.192, 18.82]}, {"time": 0.2333, "value": 18.82, "curve": [0.308, 18.82, 0.458, -22.25]}, {"time": 0.5333, "value": -22.25, "curve": [0.608, -22.25, 0.758, -3.34]}, {"time": 0.8333, "value": -3.34}]}, "Drips1": {"rotate": [{"curve": [0.033, 0, 0.056, -28.87]}, {"time": 0.1333, "value": -28.87, "curve": [0.242, -28.87, 0.458, 13.71]}, {"time": 0.5667, "value": 13.71, "curve": [0.633, 13.71, 0.767, 0]}, {"time": 0.8333}], "translate": [{"x": 0.05, "y": 0.41, "curve": [0.033, 0.05, 0.056, 0.4, 0.033, 0.41, 0.056, 75.19]}, {"time": 0.1333, "x": 0.4, "y": 75.19, "curve": [0.242, 0.4, 0.458, 13.59, 0.242, 75.19, 0.458, -27.83]}, {"time": 0.5667, "x": 13.59, "y": -27.83, "curve": [0.633, 13.59, 0.767, 0.05, 0.633, -27.83, 0.767, 0.41]}, {"time": 0.8333, "x": 0.05, "y": 0.41}]}, "FACEBOB": {"translate": [{"x": -50.88, "y": -10.91, "curve": [0.017, -50.88, 0.05, -6, 0.017, -10.91, 0.05, 0.1]}, {"time": 0.0667, "x": -6, "y": 0.1, "curve": [0.083, -6, 0.117, -5.56, 0.083, 0.1, 0.117, 23.37]}, {"time": 0.1333, "x": -5.56, "y": 23.37, "curve": [0.15, -5.56, 0.183, -6.46, 0.15, 23.37, 0.183, -66.76]}, {"time": 0.2, "x": -6.46, "y": -66.76, "curve": [0.225, -6.46, 0.275, -5.21, 0.225, -66.76, 0.275, 58.54]}, {"time": 0.3, "x": -5.21, "y": 58.54, "curve": [0.317, -5.21, 0.35, -6.2, 0.317, 58.54, 0.35, -40.38]}, {"time": 0.3667, "x": -6.2, "y": -40.38}, {"time": 0.4333, "x": -6, "y": 0.1}]}, "Eye_Middle": {"translate": [{"x": 0.06, "y": 3.12}], "scale": [{"time": 0.0333, "curve": [0.042, 1, 0.058, 1.381, 0.042, 1, 0.058, 1.381]}, {"time": 0.0667, "x": 1.381, "y": 1.381, "curve": [0.083, 1.381, 0.117, 0.897, 0.083, 1.381, 0.117, 0.897]}, {"time": 0.1333, "x": 0.897, "y": 0.897, "curve": [0.158, 0.897, 0.208, 1, 0.158, 0.897, 0.208, 1]}, {"time": 0.2333}]}, "EyeRight": {"translate": [{"x": 0.06, "y": 3.12}], "scale": [{"time": 0.0333, "curve": [0.042, 1, 0.058, 1.381, 0.042, 1, 0.058, 1.381]}, {"time": 0.0667, "x": 1.381, "y": 1.381, "curve": [0.083, 1.381, 0.117, 0.897, 0.083, 1.381, 0.117, 0.897]}, {"time": 0.1333, "x": 0.897, "y": 0.897, "curve": [0.158, 0.897, 0.208, 1, 0.158, 0.897, 0.208, 1]}, {"time": 0.2333}]}, "EyeLeft": {"translate": [{"x": 0.06, "y": 3.12}], "scale": [{"time": 0.0333, "curve": [0.042, 1, 0.058, 1.381, 0.042, 1, 0.058, 1.381]}, {"time": 0.0667, "x": 1.381, "y": 1.381, "curve": [0.083, 1.381, 0.117, 0.897, 0.083, 1.381, 0.117, 0.897]}, {"time": 0.1333, "x": 0.897, "y": 0.897, "curve": [0.158, 0.897, 0.208, 1, 0.158, 0.897, 0.208, 1]}, {"time": 0.2333}]}, "Drips2": {"rotate": [{"curve": [0.033, 0, 0.056, 23.29]}, {"time": 0.1333, "value": 23.29, "curve": [0.242, 23.29, 0.458, -12.21]}, {"time": 0.5667, "value": -12.21, "curve": [0.633, -12.21, 0.767, 0]}, {"time": 0.8333}], "translate": [{"x": 0.05, "y": 0.41, "curve": [0.033, 0.05, 0.056, 15.52, 0.033, 0.41, 0.056, -98.09]}, {"time": 0.1333, "x": 15.52, "y": -98.09, "curve": [0.242, 15.52, 0.458, 0.33, 0.242, -98.09, 0.458, 28.88]}, {"time": 0.5667, "x": 0.33, "y": 28.88, "curve": [0.633, 0.33, 0.767, 0.05, 0.633, 28.88, 0.767, 0.41]}, {"time": 0.8333, "x": 0.05, "y": 0.41}]}, "TentacleLeftBtm": {"rotate": [{"curve": [0.02, 29.8, 0.075, 32.14]}, {"time": 0.1, "value": 32.14, "curve": [0.15, 32.14, 0.174, -0.16]}, {"time": 0.3, "value": -0.7, "curve": [0.398, -0.69, 0.45, 0]}, {"time": 0.5, "curve": [0.542, 0, 0.625, 33.59]}, {"time": 0.6667, "value": 33.59, "curve": [0.708, 33.59, 0.792, 0]}, {"time": 0.8333}]}, "HornBtmLeft2b": {"rotate": [{"curve": [0.02, 50, 0.075, 53.93]}, {"time": 0.1, "value": 53.93, "curve": [0.2, 53.93, 0.248, -0.61]}, {"time": 0.5, "value": -1.5, "curve": [0.525, -0.64, 0.549, 0]}, {"time": 0.5667, "curve": [0.608, 0, 0.692, -5.3]}, {"time": 0.7333, "value": -5.3, "curve": [0.758, -5.3, 0.798, -3.02]}, {"time": 0.8333, "value": -1.5}]}, "HornBtmLeft2c": {"rotate": [{"curve": [0.02, 23.98, 0.075, 25.87]}, {"time": 0.1, "value": 25.87, "curve": [0.15, 25.87, 0.174, -38.43]}, {"time": 0.3, "value": -39.49, "curve": [0.398, -39.19, 0.45, -14.58]}, {"time": 0.5, "value": -14.58, "curve": [0.537, -7.54, 0.575, 0]}, {"time": 0.6, "curve": [0.642, 0, 0.725, -26.8]}, {"time": 0.7667, "value": -26.8, "curve": [0.783, -26.8, 0.808, -20.89]}, {"time": 0.8333, "value": -14.58}]}, "HornBtmLeft3b": {"rotate": [{"curve": [0.023, -6.85, 0.059, -11.6]}, {"time": 0.1, "value": -14.88, "curve": [0.15, -14.88, 0.174, -133.91]}, {"time": 0.3, "value": -135.87, "curve": [0.43, -134.78, 0.5, -46.37]}, {"time": 0.5667, "value": -46.37, "curve": [0.608, -46.37, 0.692, -13.95]}, {"time": 0.7333, "value": -13.95, "curve": [0.758, -13.95, 0.798, -27.88]}, {"time": 0.8333, "value": -37.17}]}, "HornBtmLeft3c": {"rotate": [{"curve": [0.02, 62.45, 0.075, 67.36]}, {"time": 0.1, "value": 67.36, "curve": [0.15, 67.36, 0.174, 31.87]}, {"time": 0.3, "value": 31.29, "curve": [0.398, 31.52, 0.45, 50.29]}, {"time": 0.5, "value": 50.29, "curve": [0.537, 35.4, 0.575, 19.45]}, {"time": 0.6, "value": 19.45, "curve": [0.642, 19.45, 0.725, 76.14]}, {"time": 0.7667, "value": 76.14, "curve": [0.783, 76.14, 0.808, 63.64]}, {"time": 0.8333, "value": 50.29}]}, "TentacleLeftTop": {"rotate": [{"curve": [0.02, 48.23, 0.075, 52.02]}, {"time": 0.1, "value": 52.02, "curve": [0.15, 52.02, 0.174, -65.4]}, {"time": 0.3, "value": -67.33, "curve": [0.398, -66.26, 0.45, 20.88]}, {"time": 0.5, "value": 20.88, "curve": [0.542, 20.88, 0.625, -20.66]}, {"time": 0.6667, "value": -20.66, "curve": [0.708, -20.66, 0.792, 20.88]}, {"time": 0.8333, "value": 20.88}]}, "HornBtmLeft2d": {"rotate": [{"curve": [0.023, -25.96, 0.059, -43.94]}, {"time": 0.1, "value": -56.39, "curve": [0.15, -56.39, 0.174, -78.74]}, {"time": 0.3, "value": -79.11, "curve": [0.398, -78.54, 0.45, -32.04]}, {"time": 0.5, "value": -32.04, "curve": [0.547, -11.54, 0.601, 19.21]}, {"time": 0.6333, "value": 19.21, "curve": [0.667, 19.21, 0.733, -52.35]}, {"time": 0.7667, "value": -52.35, "curve": [0.784, -52.35, 0.808, -43.64]}, {"time": 0.8333, "value": -32.04}]}, "HornBtmLeft3d": {"rotate": [{"curve": [0.027, 16.56, 0.061, 30.59]}, {"time": 0.1, "value": 42.45, "curve": [0.15, 42.45, 0.174, 56.13]}, {"time": 0.3, "value": 56.35, "curve": [0.463, 55.88, 0.55, 17.74]}, {"time": 0.6333, "value": 17.74, "curve": [0.667, 17.74, 0.733, 117.82]}, {"time": 0.7667, "value": 117.82, "curve": [0.784, 117.82, 0.808, 105.65]}, {"time": 0.8333, "value": 89.42}]}, "HornBtmLeft3d2": {"rotate": [{"curve": [0.027, 16.56, 0.061, 30.59]}, {"time": 0.1, "value": 42.45, "curve": [0.15, 42.45, 0.174, 56.13]}, {"time": 0.3, "value": 56.35, "curve": [0.463, 55.88, 0.55, 17.74]}, {"time": 0.6333, "value": 17.74, "curve": [0.667, 17.74, 0.733, 117.82]}, {"time": 0.7667, "value": 117.82, "curve": [0.784, 117.82, 0.808, 105.65]}, {"time": 0.8333, "value": 89.42}]}, "HornBtmLeft3c2": {"rotate": [{"curve": [0.02, 62.45, 0.075, 67.36]}, {"time": 0.1, "value": 67.36, "curve": [0.15, 67.36, 0.174, 31.87]}, {"time": 0.3, "value": 31.29, "curve": [0.398, 31.52, 0.45, 50.29]}, {"time": 0.5, "value": 50.29, "curve": [0.537, 35.4, 0.575, 19.45]}, {"time": 0.6, "value": 19.45, "curve": [0.642, 19.45, 0.725, 76.14]}, {"time": 0.7667, "value": 76.14, "curve": [0.783, 76.14, 0.808, 63.64]}, {"time": 0.8333, "value": 50.29}]}, "HornBtmLeft3b2": {"rotate": [{"curve": [0.023, -6.85, 0.059, -11.6]}, {"time": 0.1, "value": -14.88, "curve": [0.15, -14.88, 0.174, -133.91]}, {"time": 0.3, "value": -135.87, "curve": [0.43, -134.78, 0.5, -46.37]}, {"time": 0.5667, "value": -46.37, "curve": [0.608, -46.37, 0.692, -13.95]}, {"time": 0.7333, "value": -13.95, "curve": [0.758, -13.95, 0.798, -27.88]}, {"time": 0.8333, "value": -37.17}]}, "TentacleLeftBtm2": {"rotate": [{"curve": [0.02, -10.77, 0.075, -11.62]}, {"time": 0.1, "value": -11.62, "curve": [0.15, -11.62, 0.174, -0.87]}, {"time": 0.3, "value": -0.7, "curve": [0.398, -0.87, 0.45, -14.72]}, {"time": 0.5, "value": -14.72, "curve": [0.542, -14.72, 0.625, -27.78]}, {"time": 0.6667, "value": -27.78, "curve": [0.708, -27.78, 0.792, 0]}, {"time": 0.8333}]}, "HornBtmLeft2d2": {"rotate": [{"curve": [0.023, -25.96, 0.059, -43.94]}, {"time": 0.1, "value": -56.39, "curve": [0.15, -56.39, 0.174, -78.74]}, {"time": 0.3, "value": -79.11, "curve": [0.398, -78.54, 0.45, -32.04]}, {"time": 0.5, "value": -32.04, "curve": [0.547, -11.54, 0.601, 19.21]}, {"time": 0.6333, "value": 19.21, "curve": [0.667, 19.21, 0.733, -52.35]}, {"time": 0.7667, "value": -52.35, "curve": [0.784, -52.35, 0.808, -43.64]}, {"time": 0.8333, "value": -32.04}]}, "HornBtmLeft2c2": {"rotate": [{"curve": [0.02, 23.98, 0.075, 25.87]}, {"time": 0.1, "value": 25.87, "curve": [0.15, 25.87, 0.174, -38.43]}, {"time": 0.3, "value": -39.49, "curve": [0.398, -39.19, 0.45, -14.58]}, {"time": 0.5, "value": -14.58, "curve": [0.537, -7.54, 0.575, 0]}, {"time": 0.6, "curve": [0.642, 0, 0.725, -26.8]}, {"time": 0.7667, "value": -26.8, "curve": [0.783, -26.8, 0.808, -20.89]}, {"time": 0.8333, "value": -14.58}]}, "HornBtmLeft2b2": {"rotate": [{"curve": [0.02, 50, 0.075, 53.93]}, {"time": 0.1, "value": 53.93, "curve": [0.2, 53.93, 0.248, -0.61]}, {"time": 0.5, "value": -1.5, "curve": [0.525, -0.64, 0.549, 0]}, {"time": 0.5667, "curve": [0.608, 0, 0.692, -5.3]}, {"time": 0.7333, "value": -5.3, "curve": [0.758, -5.3, 0.798, -3.02]}, {"time": 0.8333, "value": -1.5}]}, "TentacleLeftTop2": {"rotate": [{"curve": [0.02, -43.93, 0.075, -47.38]}, {"time": 0.1, "value": -47.38, "curve": [0.15, -47.38, 0.174, 9.41]}, {"time": 0.3, "value": 10.35, "curve": [0.398, 9.85, 0.45, -30.6]}, {"time": 0.5, "value": -30.6, "curve": [0.542, -30.6, 0.625, 0.96]}, {"time": 0.6667, "value": 0.96, "curve": [0.708, 0.96, 0.792, -32.65]}, {"time": 0.8333, "value": -32.65}]}}}, "summon2": {"bones": {"Main": {"rotate": [{"value": 0.13, "curve": [0.05, 6.84, 0.1, 13.13]}, {"time": 0.1333, "value": 13.13, "curve": [0.192, 13.13, 0.308, -13.06]}, {"time": 0.3667, "value": -13.06, "curve": [0.482, -13.06, 0.66, -6.24]}, {"time": 0.8333, "value": 0.13}], "translate": [{"curve": [0.008, 0, 0.025, 0, 0.008, 0, 0.025, -106.72]}, {"time": 0.0333, "y": -106.72, "curve": [0.058, 0, 0.108, 0, 0.058, -106.72, 0.108, 178.8]}, {"time": 0.1333, "y": 178.8, "curve": [0.233, 0, 0.433, 0, 0.233, 178.8, 0.433, -121.33]}, {"time": 0.5333, "y": -121.33, "curve": [0.608, 0, 0.758, 0, 0.608, -121.33, 0.758, 0]}, {"time": 0.8333}], "scale": [{"x": 0.883, "y": 1.067, "curve": [0.008, 0.883, 0.025, 0.683, 0.008, 1.067, 0.025, 1.596]}, {"time": 0.0333, "x": 0.683, "y": 1.596, "curve": [0.075, 0.683, 0.158, 1.432, 0.075, 1.596, 0.158, 0.802]}, {"time": 0.2, "x": 1.432, "y": 0.802, "curve": [0.275, 1.432, 0.425, 0.867, 0.275, 0.802, 0.425, 1.216]}, {"time": 0.5, "x": 0.867, "y": 1.216, "curve": [0.583, 0.867, 0.75, 1.004, 0.583, 1.216, 0.75, 0.987]}, {"time": 0.8333, "x": 1.004, "y": 0.987}]}, "FACE": {"translate": [{"x": 0.39, "y": 19.18, "curve": [0.008, 0.39, 0.025, -63.03, 0.008, 19.18, 0.025, 16]}, {"time": 0.0333, "x": -63.03, "y": 16, "curve": [0.058, -63.03, 0.108, 93.27, 0.058, 16, 0.108, -11.25]}, {"time": 0.1333, "x": 93.27, "y": -11.25, "curve": [0.242, 93.27, 0.458, -79.18, 0.242, -11.25, 0.458, 2.67]}, {"time": 0.5667, "x": -79.18, "y": 2.67, "curve": [0.633, -79.18, 0.767, 0.13, 0.633, 2.67, 0.767, 6.71]}, {"time": 0.8333, "x": 0.13, "y": 6.71}]}, "HornBtmLeft": {"rotate": [{"value": 3.76, "curve": [0.017, 3.76, 0.05, 26.34]}, {"time": 0.0667, "value": 26.34, "curve": [0.108, 26.34, 0.192, -21.32]}, {"time": 0.2333, "value": -21.32, "curve": [0.308, -21.32, 0.458, 23.3]}, {"time": 0.5333, "value": 23.3, "curve": [0.608, 23.3, 0.758, 3.6]}, {"time": 0.8333, "value": 3.6}]}, "HornTopLeft": {"rotate": [{"value": 2.87, "curve": [0.008, 2.87, 0.025, 25.45]}, {"time": 0.0333, "value": 25.45, "curve": [0.075, 25.45, 0.158, -17.43]}, {"time": 0.2, "value": -17.43, "curve": [0.275, -17.43, 0.425, 22.4]}, {"time": 0.5, "value": 22.4, "curve": [0.583, 22.4, 0.75, 2.87]}, {"time": 0.8333, "value": 2.87}]}, "HornTopRight": {"rotate": [{"value": -2.67, "curve": [0.008, -2.67, 0.025, -30.19]}, {"time": 0.0333, "value": -30.19, "curve": [0.075, -30.19, 0.158, 16.13]}, {"time": 0.2, "value": 16.13, "curve": [0.275, 16.13, 0.425, -21.43]}, {"time": 0.5, "value": -21.43, "curve": [0.583, -21.43, 0.75, -2.67]}, {"time": 0.8333, "value": -2.67}]}, "HornBtmRight": {"rotate": [{"value": -3.49, "curve": [0.017, -3.49, 0.05, -31.01]}, {"time": 0.0667, "value": -31.01, "curve": [0.108, -31.01, 0.192, 18.82]}, {"time": 0.2333, "value": 18.82, "curve": [0.308, 18.82, 0.458, -22.25]}, {"time": 0.5333, "value": -22.25, "curve": [0.608, -22.25, 0.758, -3.34]}, {"time": 0.8333, "value": -3.34}]}, "Drips1": {"rotate": [{"curve": [0.033, 0, 0.056, -28.87]}, {"time": 0.1333, "value": -28.87, "curve": [0.242, -28.87, 0.458, 13.71]}, {"time": 0.5667, "value": 13.71, "curve": [0.633, 13.71, 0.767, 0]}, {"time": 0.8333}], "translate": [{"x": 0.05, "y": 0.41, "curve": [0.033, 0.05, 0.056, 0.4, 0.033, 0.41, 0.056, 75.19]}, {"time": 0.1333, "x": 0.4, "y": 75.19, "curve": [0.242, 0.4, 0.458, 13.59, 0.242, 75.19, 0.458, -27.83]}, {"time": 0.5667, "x": 13.59, "y": -27.83, "curve": [0.633, 13.59, 0.767, 0.05, 0.633, -27.83, 0.767, 0.41]}, {"time": 0.8333, "x": 0.05, "y": 0.41}]}, "FACEBOB": {"translate": [{"x": -50.88, "y": -10.91, "curve": [0.017, -50.88, 0.05, -6, 0.017, -10.91, 0.05, 0.1]}, {"time": 0.0667, "x": -6, "y": 0.1, "curve": [0.083, -6, 0.117, -5.56, 0.083, 0.1, 0.117, 23.37]}, {"time": 0.1333, "x": -5.56, "y": 23.37, "curve": [0.15, -5.56, 0.183, -6.46, 0.15, 23.37, 0.183, -66.76]}, {"time": 0.2, "x": -6.46, "y": -66.76, "curve": [0.225, -6.46, 0.275, -5.21, 0.225, -66.76, 0.275, 58.54]}, {"time": 0.3, "x": -5.21, "y": 58.54, "curve": [0.317, -5.21, 0.35, -6.2, 0.317, 58.54, 0.35, -40.38]}, {"time": 0.3667, "x": -6.2, "y": -40.38}, {"time": 0.4333, "x": -6, "y": 0.1}]}, "Eye_Middle": {"translate": [{"x": 0.06, "y": 3.12}], "scale": [{"time": 0.0333, "curve": [0.042, 1, 0.058, 1.381, 0.042, 1, 0.058, 1.381]}, {"time": 0.0667, "x": 1.381, "y": 1.381, "curve": [0.083, 1.381, 0.117, 0.897, 0.083, 1.381, 0.117, 0.897]}, {"time": 0.1333, "x": 0.897, "y": 0.897, "curve": [0.158, 0.897, 0.208, 1, 0.158, 0.897, 0.208, 1]}, {"time": 0.2333}]}, "EyeRight": {"translate": [{"x": 0.06, "y": 3.12}], "scale": [{"time": 0.0333, "curve": [0.042, 1, 0.058, 1.381, 0.042, 1, 0.058, 1.381]}, {"time": 0.0667, "x": 1.381, "y": 1.381, "curve": [0.083, 1.381, 0.117, 0.897, 0.083, 1.381, 0.117, 0.897]}, {"time": 0.1333, "x": 0.897, "y": 0.897, "curve": [0.158, 0.897, 0.208, 1, 0.158, 0.897, 0.208, 1]}, {"time": 0.2333}]}, "EyeLeft": {"translate": [{"x": 0.06, "y": 3.12}], "scale": [{"time": 0.0333, "curve": [0.042, 1, 0.058, 1.381, 0.042, 1, 0.058, 1.381]}, {"time": 0.0667, "x": 1.381, "y": 1.381, "curve": [0.083, 1.381, 0.117, 0.897, 0.083, 1.381, 0.117, 0.897]}, {"time": 0.1333, "x": 0.897, "y": 0.897, "curve": [0.158, 0.897, 0.208, 1, 0.158, 0.897, 0.208, 1]}, {"time": 0.2333}]}, "Drips2": {"rotate": [{"curve": [0.033, 0, 0.056, 23.29]}, {"time": 0.1333, "value": 23.29, "curve": [0.242, 23.29, 0.458, -12.21]}, {"time": 0.5667, "value": -12.21, "curve": [0.633, -12.21, 0.767, 0]}, {"time": 0.8333}], "translate": [{"x": 0.05, "y": 0.41, "curve": [0.033, 0.05, 0.056, 15.52, 0.033, 0.41, 0.056, -98.09]}, {"time": 0.1333, "x": 15.52, "y": -98.09, "curve": [0.242, 15.52, 0.458, 0.33, 0.242, -98.09, 0.458, 28.88]}, {"time": 0.5667, "x": 0.33, "y": 28.88, "curve": [0.633, 0.33, 0.767, 0.05, 0.633, 28.88, 0.767, 0.41]}, {"time": 0.8333, "x": 0.05, "y": 0.41}]}, "TentacleLeftBtm": {"rotate": [{"curve": [0.02, 29.8, 0.075, 32.14]}, {"time": 0.1, "value": 32.14, "curve": [0.15, 32.14, 0.174, -0.16]}, {"time": 0.3, "value": -0.7, "curve": [0.398, -0.69, 0.45, 0]}, {"time": 0.5, "curve": [0.542, 0, 0.625, 33.59]}, {"time": 0.6667, "value": 33.59, "curve": [0.708, 33.59, 0.792, 0]}, {"time": 0.8333}]}, "HornBtmLeft2b": {"rotate": [{"curve": [0.02, 50, 0.075, 53.93]}, {"time": 0.1, "value": 53.93, "curve": [0.2, 53.93, 0.248, -0.61]}, {"time": 0.5, "value": -1.5, "curve": [0.525, -0.64, 0.549, 0]}, {"time": 0.5667, "curve": [0.608, 0, 0.692, -5.3]}, {"time": 0.7333, "value": -5.3, "curve": [0.758, -5.3, 0.798, -3.02]}, {"time": 0.8333, "value": -1.5}]}, "HornBtmLeft2c": {"rotate": [{"curve": [0.02, 23.98, 0.075, 25.87]}, {"time": 0.1, "value": 25.87, "curve": [0.15, 25.87, 0.174, -38.43]}, {"time": 0.3, "value": -39.49, "curve": [0.398, -39.19, 0.45, -14.58]}, {"time": 0.5, "value": -14.58, "curve": [0.537, -7.54, 0.575, 0]}, {"time": 0.6, "curve": [0.642, 0, 0.725, -26.8]}, {"time": 0.7667, "value": -26.8, "curve": [0.783, -26.8, 0.808, -20.89]}, {"time": 0.8333, "value": -14.58}]}, "HornBtmLeft3b": {"rotate": [{"curve": [0.023, -6.85, 0.059, -11.6]}, {"time": 0.1, "value": -14.88, "curve": [0.15, -14.88, 0.174, -133.91]}, {"time": 0.3, "value": -135.87, "curve": [0.43, -134.78, 0.5, -46.37]}, {"time": 0.5667, "value": -46.37, "curve": [0.608, -46.37, 0.692, -13.95]}, {"time": 0.7333, "value": -13.95, "curve": [0.758, -13.95, 0.798, -27.88]}, {"time": 0.8333, "value": -37.17}]}, "HornBtmLeft3c": {"rotate": [{"curve": [0.02, 62.45, 0.075, 67.36]}, {"time": 0.1, "value": 67.36, "curve": [0.15, 67.36, 0.174, 31.87]}, {"time": 0.3, "value": 31.29, "curve": [0.398, 31.52, 0.45, 50.29]}, {"time": 0.5, "value": 50.29, "curve": [0.537, 35.4, 0.575, 19.45]}, {"time": 0.6, "value": 19.45, "curve": [0.642, 19.45, 0.725, 76.14]}, {"time": 0.7667, "value": 76.14, "curve": [0.783, 76.14, 0.808, 63.64]}, {"time": 0.8333, "value": 50.29}]}, "TentacleLeftTop": {"rotate": [{"curve": [0.02, 48.23, 0.075, 52.02]}, {"time": 0.1, "value": 52.02, "curve": [0.15, 52.02, 0.174, -65.4]}, {"time": 0.3, "value": -67.33, "curve": [0.398, -66.26, 0.45, 20.88]}, {"time": 0.5, "value": 20.88, "curve": [0.542, 20.88, 0.625, -20.66]}, {"time": 0.6667, "value": -20.66, "curve": [0.708, -20.66, 0.792, 20.88]}, {"time": 0.8333, "value": 20.88}]}, "HornBtmLeft2d": {"rotate": [{"curve": [0.023, -25.96, 0.059, -43.94]}, {"time": 0.1, "value": -56.39, "curve": [0.15, -56.39, 0.174, -78.74]}, {"time": 0.3, "value": -79.11, "curve": [0.398, -78.54, 0.45, -32.04]}, {"time": 0.5, "value": -32.04, "curve": [0.547, -11.54, 0.601, 19.21]}, {"time": 0.6333, "value": 19.21, "curve": [0.667, 19.21, 0.733, -52.35]}, {"time": 0.7667, "value": -52.35, "curve": [0.784, -52.35, 0.808, -43.64]}, {"time": 0.8333, "value": -32.04}]}, "HornBtmLeft3d": {"rotate": [{"curve": [0.027, 16.56, 0.061, 30.59]}, {"time": 0.1, "value": 42.45, "curve": [0.15, 42.45, 0.174, 56.13]}, {"time": 0.3, "value": 56.35, "curve": [0.463, 55.88, 0.55, 17.74]}, {"time": 0.6333, "value": 17.74, "curve": [0.667, 17.74, 0.733, 117.82]}, {"time": 0.7667, "value": 117.82, "curve": [0.784, 117.82, 0.808, 105.65]}, {"time": 0.8333, "value": 89.42}]}, "HornBtmLeft3d2": {"rotate": [{"curve": [0.027, 16.56, 0.061, 30.59]}, {"time": 0.1, "value": 42.45, "curve": [0.15, 42.45, 0.174, 56.13]}, {"time": 0.3, "value": 56.35, "curve": [0.463, 55.88, 0.55, 17.74]}, {"time": 0.6333, "value": 17.74, "curve": [0.667, 17.74, 0.733, 117.82]}, {"time": 0.7667, "value": 117.82, "curve": [0.784, 117.82, 0.808, 105.65]}, {"time": 0.8333, "value": 89.42}]}, "HornBtmLeft3c2": {"rotate": [{"curve": [0.02, 62.45, 0.075, 67.36]}, {"time": 0.1, "value": 67.36, "curve": [0.15, 67.36, 0.174, 31.87]}, {"time": 0.3, "value": 31.29, "curve": [0.398, 31.52, 0.45, 50.29]}, {"time": 0.5, "value": 50.29, "curve": [0.537, 35.4, 0.575, 19.45]}, {"time": 0.6, "value": 19.45, "curve": [0.642, 19.45, 0.725, 76.14]}, {"time": 0.7667, "value": 76.14, "curve": [0.783, 76.14, 0.808, 63.64]}, {"time": 0.8333, "value": 50.29}]}, "HornBtmLeft3b2": {"rotate": [{"curve": [0.023, -6.85, 0.059, -11.6]}, {"time": 0.1, "value": -14.88, "curve": [0.15, -14.88, 0.174, -133.91]}, {"time": 0.3, "value": -135.87, "curve": [0.43, -134.78, 0.5, -46.37]}, {"time": 0.5667, "value": -46.37, "curve": [0.608, -46.37, 0.692, -13.95]}, {"time": 0.7333, "value": -13.95, "curve": [0.758, -13.95, 0.798, -27.88]}, {"time": 0.8333, "value": -37.17}]}, "TentacleLeftBtm2": {"rotate": [{"curve": [0.02, -10.77, 0.075, -11.62]}, {"time": 0.1, "value": -11.62, "curve": [0.15, -11.62, 0.174, -0.87]}, {"time": 0.3, "value": -0.7, "curve": [0.398, -0.87, 0.45, -14.72]}, {"time": 0.5, "value": -14.72, "curve": [0.542, -14.72, 0.625, -27.78]}, {"time": 0.6667, "value": -27.78, "curve": [0.708, -27.78, 0.792, 0]}, {"time": 0.8333}]}, "HornBtmLeft2d2": {"rotate": [{"curve": [0.023, -25.96, 0.059, -43.94]}, {"time": 0.1, "value": -56.39, "curve": [0.15, -56.39, 0.174, -78.74]}, {"time": 0.3, "value": -79.11, "curve": [0.398, -78.54, 0.45, -32.04]}, {"time": 0.5, "value": -32.04, "curve": [0.547, -11.54, 0.601, 19.21]}, {"time": 0.6333, "value": 19.21, "curve": [0.667, 19.21, 0.733, -52.35]}, {"time": 0.7667, "value": -52.35, "curve": [0.784, -52.35, 0.808, -43.64]}, {"time": 0.8333, "value": -32.04}]}, "HornBtmLeft2c2": {"rotate": [{"curve": [0.02, 23.98, 0.075, 25.87]}, {"time": 0.1, "value": 25.87, "curve": [0.15, 25.87, 0.174, -38.43]}, {"time": 0.3, "value": -39.49, "curve": [0.398, -39.19, 0.45, -14.58]}, {"time": 0.5, "value": -14.58, "curve": [0.537, -7.54, 0.575, 0]}, {"time": 0.6, "curve": [0.642, 0, 0.725, -26.8]}, {"time": 0.7667, "value": -26.8, "curve": [0.783, -26.8, 0.808, -20.89]}, {"time": 0.8333, "value": -14.58}]}, "HornBtmLeft2b2": {"rotate": [{"curve": [0.02, 50, 0.075, 53.93]}, {"time": 0.1, "value": 53.93, "curve": [0.2, 53.93, 0.248, -0.61]}, {"time": 0.5, "value": -1.5, "curve": [0.525, -0.64, 0.549, 0]}, {"time": 0.5667, "curve": [0.608, 0, 0.692, -5.3]}, {"time": 0.7333, "value": -5.3, "curve": [0.758, -5.3, 0.798, -3.02]}, {"time": 0.8333, "value": -1.5}]}, "TentacleLeftTop2": {"rotate": [{"curve": [0.02, -43.93, 0.075, -47.38]}, {"time": 0.1, "value": -47.38, "curve": [0.15, -47.38, 0.174, 9.41]}, {"time": 0.3, "value": 10.35, "curve": [0.398, 9.85, 0.45, -30.6]}, {"time": 0.5, "value": -30.6, "curve": [0.542, -30.6, 0.625, 0.96]}, {"time": 0.6667, "value": 0.96, "curve": [0.708, 0.96, 0.792, -32.65]}, {"time": 0.8333, "value": -32.65}]}}}, "teleport": {"slots": {"MASK": {"attachment": [{"name": "MASK"}]}, "teleport": {"attachment": [{"time": 0.2333, "name": "images/Teleport_1"}, {"time": 0.3, "name": "images/Teleport_2"}, {"time": 0.3667, "name": "images/Teleport_3"}, {"time": 0.4333, "name": "images/Teleport_4"}, {"time": 0.5}, {"time": 0.7, "name": "images/Teleport_1"}, {"time": 0.7667, "name": "images/Teleport_2"}, {"time": 0.8333, "name": "images/Teleport_3"}, {"time": 0.9, "name": "images/Teleport_4"}, {"time": 0.9667}]}}, "bones": {"teleport": {"scale": [{"time": 0.5, "x": 1.004, "y": 0.987}]}, "Main": {"rotate": [{"value": 0.13}], "translate": [{"curve": [0.025, 0, 0.075, 0, 0.025, 0, 0.075, 59.83]}, {"time": 0.1, "y": 59.83, "curve": [0.167, 0, 0.367, 0, 0.167, 59.83, 0.367, -436.7]}, {"time": 0.3667, "y": -1065.33, "curve": "stepped"}, {"time": 0.6667, "y": -1065.33}, {"time": 0.8667, "y": 59.83, "curve": [0.9, 0, 0.967, 0, 0.9, 59.83, 0.967, 0]}, {"time": 1}], "scale": [{"x": 1.004, "y": 0.987, "curve": [0.008, 1.004, 0.025, 0.769, 0.008, 0.987, 0.025, 1.281]}, {"time": 0.0333, "x": 0.769, "y": 1.281, "curve": [0.075, 0.769, 0.158, 1.304, 0.075, 1.281, 0.158, 0.756]}, {"time": 0.2, "x": 1.304, "y": 0.756, "curve": "stepped"}, {"time": 0.6667, "x": 1.304, "y": 0.756, "curve": [0.73, 1.149, 0.791, 1.004, 0.73, 0.875, 0.791, 0.987]}, {"time": 0.8333, "x": 1.004, "y": 0.987, "curve": [0.871, 0.883, 0.908, 0.769, 0.871, 1.139, 0.908, 1.281]}, {"time": 0.9333, "x": 0.769, "y": 1.281}, {"time": 1.0333, "x": 1.004, "y": 0.987}]}, "FACE": {"translate": [{"x": 0.13, "y": 6.71, "curve": [0.008, 0.13, 0.025, -31.31, 0.008, 6.71, 0.025, 7.26]}, {"time": 0.0333, "x": -31.31, "y": 7.26, "curve": [0.117, -31.31, 0.367, 13.97, 0.117, 7.26, 0.367, 6.47]}, {"time": 0.3667, "x": 71.29, "y": 5.47, "curve": "stepped"}, {"time": 0.6667, "x": -100.79, "y": 6.17, "curve": [0.847, -100.79, 0.842, 58.82, 0.847, 6.17, 0.842, 11.57]}, {"time": 0.9, "x": 58.82, "y": 11.57, "curve": [0.933, 58.82, 1, 0.13, 0.933, 11.57, 1, 6.71]}, {"time": 1.0333, "x": 0.13, "y": 6.71}]}, "HornBtmLeft": {"rotate": [{"value": 3.6, "curve": [0.047, 14.58, 0.092, 20.47]}, {"time": 0.1333, "value": 20.47}, {"time": 0.3, "value": -21.04, "curve": "stepped"}, {"time": 0.5, "value": -21.04}, {"time": 0.5667, "value": 3.6, "curve": [0.602, -12.44, 0.635, -21.04]}, {"time": 0.6667, "value": -21.04}, {"time": 1.0333, "value": 3.6}]}, "HornTopLeft": {"rotate": [{"value": 2.87, "curve": [0.043, 7.69, 0.101, 19.73]}, {"time": 0.1333, "value": 19.73}, {"time": 0.3, "value": -13.94, "curve": "stepped"}, {"time": 0.5, "value": -13.94}, {"time": 0.5667, "value": 2.87, "curve": [0.599, -1.93, 0.642, -13.94]}, {"time": 0.6667, "value": -13.94}, {"time": 1.0333, "value": 2.87}]}, "HornTopRight": {"rotate": [{"value": -2.67, "curve": [0.043, -7.74, 0.101, -20.42]}, {"time": 0.1333, "value": -20.42}, {"time": 0.3, "value": 11.84, "curve": "stepped"}, {"time": 0.5, "value": 11.84}, {"time": 0.5667, "value": -2.67, "curve": [0.599, 1.48, 0.642, 11.84]}, {"time": 0.6667, "value": 11.84}, {"time": 1.0333, "value": -2.67}]}, "HornBtmRight": {"rotate": [{"value": -3.34, "curve": [0.047, -14.9, 0.092, -21.09]}, {"time": 0.1333, "value": -21.09}, {"time": 0.3, "value": 9.97, "curve": "stepped"}, {"time": 0.5, "value": 9.97}, {"time": 0.5667, "value": -3.34, "curve": [0.602, 5.33, 0.635, 9.97]}, {"time": 0.6667, "value": 9.97}, {"time": 1.0333, "value": -3.34}]}, "Drips1": {"rotate": [{"value": 1.88}], "translate": [{"x": 0.05, "y": 0.41, "curve": "stepped"}, {"time": 0.7667, "x": 0.05, "y": 0.41}, {"time": 0.8333, "x": -182.67, "y": 4.43}, {"time": 0.9667, "x": 37.35, "y": 1.36}, {"time": 1.0333, "x": 0.05, "y": 0.41}]}, "FACEBOB": {"translate": [{"x": -6, "y": 0.1, "curve": [0.017, -6, 0.05, -5.56, 0.017, 0.1, 0.05, 23.37]}, {"time": 0.0667, "x": -5.56, "y": 23.37, "curve": [0.083, -5.56, 0.117, -6.46, 0.083, 23.37, 0.117, -66.76]}, {"time": 0.1333, "x": -6.46, "y": -66.76, "curve": [0.158, -6.46, 0.208, -5.21, 0.158, -66.76, 0.208, 58.54]}, {"time": 0.2333, "x": -5.21, "y": 58.54, "curve": [0.25, -5.21, 0.283, -6.2, 0.25, 58.54, 0.283, -40.38]}, {"time": 0.3, "x": -6.2, "y": -40.38}, {"time": 0.3667, "x": -6, "y": 0.1}]}, "EyeLeft": {"scale": [{}, {"time": 0.0333, "x": 1.526, "y": 1.526}, {"time": 0.2}]}, "Eye_Middle": {"scale": [{}, {"time": 0.0333, "x": 1.526, "y": 1.526}, {"time": 0.2}]}, "EyeRight": {"scale": [{}, {"time": 0.0333, "x": 1.526, "y": 1.526}, {"time": 0.2}]}, "Drips2": {"rotate": [{"value": 1.88}], "translate": [{"x": 0.05, "y": 0.41, "curve": "stepped"}, {"time": 0.7667, "x": 0.05, "y": 0.41}, {"time": 0.8333, "x": -182.67, "y": 4.43}, {"time": 0.9667, "x": 37.35, "y": 1.36}, {"time": 1.0333, "x": 0.05, "y": 0.41}]}, "TentacleLeftTop": {"rotate": [{"curve": [0.042, 0, 0.125, -29.74]}, {"time": 0.1667, "value": -29.74, "curve": "stepped"}, {"time": 0.6333, "value": -29.74, "curve": [0.65, -29.11, 0.658, 22.22]}, {"time": 0.6667, "value": 22.22, "curve": "stepped"}, {"time": 0.7667, "value": 22.22, "curve": [0.808, 22.22, 0.892, -5.61]}, {"time": 0.9333, "value": -5.61, "curve": [0.958, -5.61, 1.008, 0]}, {"time": 1.0333}]}, "HornBtmLeft2b": {"rotate": [{"curve": [0.042, 0, 0.125, 25.14]}, {"time": 0.1667, "value": 25.14, "curve": "stepped"}, {"time": 0.6333, "value": 25.14}, {"time": 0.6667, "value": 66.29, "curve": "stepped"}, {"time": 0.7667, "value": 66.29, "curve": [0.808, 66.29, 0.892, -16.53]}, {"time": 0.9333, "value": -16.53, "curve": [0.958, -16.53, 1.008, 0]}, {"time": 1.0333}]}, "HornBtmLeft2c": {"rotate": [{"curve": [0.042, 0, 0.125, -17.97]}, {"time": 0.1667, "value": -17.97, "curve": "stepped"}, {"time": 0.6333, "value": -17.97}, {"time": 0.6667, "value": 17.76, "curve": "stepped"}, {"time": 0.7667, "value": 17.76, "curve": [0.808, 17.76, 0.892, -12.48]}, {"time": 0.9333, "value": -12.48, "curve": [0.958, -12.48, 1.008, 0]}, {"time": 1.0333}]}, "HornBtmLeft2d": {"rotate": [{"curve": [0.042, 0, 0.125, -74.14]}, {"time": 0.1667, "value": -74.14, "curve": "stepped"}, {"time": 0.6333, "value": -74.14, "curve": [0.65, -73.49, 0.658, -20.47]}, {"time": 0.6667, "value": -20.47, "curve": "stepped"}, {"time": 0.7667, "value": -20.47, "curve": [0.808, -20.47, 0.892, -26.46]}, {"time": 0.9333, "value": -26.46, "curve": [0.958, -26.46, 1.008, 0]}, {"time": 1.0333}]}, "TentacleLeftBtm": {"rotate": [{"curve": [0.042, 0, 0.125, 4.28]}, {"time": 0.1667, "value": 4.28, "curve": "stepped"}, {"time": 0.6333, "value": 4.28, "curve": [0.65, 4.54, 0.658, 26.01]}, {"time": 0.6667, "value": 26.01, "curve": "stepped"}, {"time": 0.7667, "value": 26.01, "curve": [0.808, 26.01, 0.892, -6.23]}, {"time": 0.9333, "value": -6.23, "curve": [0.958, -6.23, 1.008, 0]}, {"time": 1.0333}]}, "HornBtmLeft3b": {"rotate": [{"curve": [0.042, 0, 0.125, -130.89]}, {"time": 0.1667, "value": -130.89, "curve": "stepped"}, {"time": 0.6333, "value": -130.89, "curve": [0.65, -130.06, 0.658, -62.41]}, {"time": 0.6667, "value": -62.41, "curve": "stepped"}, {"time": 0.7667, "value": -62.41, "curve": [0.808, -62.41, 0.892, -39.52]}, {"time": 0.9333, "value": -39.52, "curve": [0.958, -39.52, 1.008, 0]}, {"time": 1.0333}]}, "HornBtmLeft3c": {"rotate": [{"curve": [0.042, 0, 0.125, 36.26]}, {"time": 0.1667, "value": 36.26, "curve": "stepped"}, {"time": 0.6333, "value": 36.26, "curve": [0.65, 36.87, 0.658, 86.6]}, {"time": 0.6667, "value": 86.6, "curve": "stepped"}, {"time": 0.7667, "value": 86.6, "curve": [0.808, 86.6, 0.892, 0]}, {"time": 0.9333}]}, "HornBtmLeft3d": {"rotate": [{"curve": [0.042, 0, 0.125, 61.32]}, {"time": 0.1667, "value": 61.32, "curve": "stepped"}, {"time": 0.6333, "value": 61.32, "curve": [0.65, 61.91, 0.658, 109.69]}, {"time": 0.6667, "value": 109.69, "curve": "stepped"}, {"time": 0.7667, "value": 109.69, "curve": [0.808, 109.69, 0.892, 0]}, {"time": 0.9333}]}, "HornBtmLeft3d2": {"rotate": [{"curve": [0.042, 0, 0.125, 61.32]}, {"time": 0.1667, "value": 61.32, "curve": "stepped"}, {"time": 0.6333, "value": 61.32, "curve": [0.65, 61.91, 0.658, 109.69]}, {"time": 0.6667, "value": 109.69, "curve": "stepped"}, {"time": 0.7667, "value": 109.69, "curve": [0.808, 109.69, 0.892, 0]}, {"time": 0.9333}]}, "HornBtmLeft3c2": {"rotate": [{"curve": [0.042, 0, 0.125, 36.26]}, {"time": 0.1667, "value": 36.26, "curve": "stepped"}, {"time": 0.6333, "value": 36.26, "curve": [0.65, 36.87, 0.658, 86.6]}, {"time": 0.6667, "value": 86.6, "curve": "stepped"}, {"time": 0.7667, "value": 86.6, "curve": [0.808, 86.6, 0.892, 0]}, {"time": 0.9333}]}, "HornBtmLeft3b2": {"rotate": [{"curve": [0.042, 0, 0.125, -130.89]}, {"time": 0.1667, "value": -130.89, "curve": "stepped"}, {"time": 0.6333, "value": -130.89, "curve": [0.65, -130.06, 0.658, -62.41]}, {"time": 0.6667, "value": -62.41, "curve": "stepped"}, {"time": 0.7667, "value": -62.41, "curve": [0.808, -62.41, 0.892, -39.52]}, {"time": 0.9333, "value": -39.52, "curve": [0.958, -39.52, 1.008, 0]}, {"time": 1.0333}]}, "TentacleLeftBtm2": {"rotate": [{"curve": [0.042, 0, 0.125, 4.28]}, {"time": 0.1667, "value": 4.28, "curve": "stepped"}, {"time": 0.6333, "value": 4.28, "curve": [0.65, 4.54, 0.658, 26.01]}, {"time": 0.6667, "value": 26.01, "curve": [0.692, 26.01, 0.742, -9.04]}, {"time": 0.7667, "value": -9.04, "curve": [0.808, -9.04, 0.892, -6.23]}, {"time": 0.9333, "value": -6.23, "curve": [0.958, -6.23, 1.008, 0]}, {"time": 1.0333}]}, "HornBtmLeft2d2": {"rotate": [{"curve": [0.042, 0, 0.125, -74.14]}, {"time": 0.1667, "value": -74.14, "curve": "stepped"}, {"time": 0.6333, "value": -74.14, "curve": [0.65, -73.49, 0.658, -20.47]}, {"time": 0.6667, "value": -20.47, "curve": "stepped"}, {"time": 0.7667, "value": -20.47, "curve": [0.808, -20.47, 0.892, -26.46]}, {"time": 0.9333, "value": -26.46, "curve": [0.958, -26.46, 1.008, 0]}, {"time": 1.0333}]}, "HornBtmLeft2c2": {"rotate": [{"curve": [0.042, 0, 0.125, -17.97]}, {"time": 0.1667, "value": -17.97, "curve": "stepped"}, {"time": 0.6333, "value": -17.97}, {"time": 0.6667, "value": 17.76, "curve": "stepped"}, {"time": 0.7667, "value": 17.76, "curve": [0.808, 17.76, 0.892, -12.48]}, {"time": 0.9333, "value": -12.48, "curve": [0.958, -12.48, 1.008, 0]}, {"time": 1.0333}]}, "HornBtmLeft2b2": {"rotate": [{"curve": [0.042, 0, 0.125, 25.14]}, {"time": 0.1667, "value": 25.14, "curve": "stepped"}, {"time": 0.6333, "value": 25.14}, {"time": 0.6667, "value": 66.29, "curve": "stepped"}, {"time": 0.7667, "value": 66.29, "curve": [0.808, 66.29, 0.892, -16.53]}, {"time": 0.9333, "value": -16.53, "curve": [0.958, -16.53, 1.008, 0]}, {"time": 1.0333}]}, "TentacleLeftTop2": {"rotate": [{"curve": [0.042, 0, 0.125, 22.8]}, {"time": 0.1667, "value": 22.8, "curve": [0.394, 22.16, 0.517, -29.74]}, {"time": 0.6333, "value": -29.74, "curve": [0.65, -29.11, 0.658, 22.22]}, {"time": 0.6667, "value": 22.22, "curve": [0.692, 22.22, 0.742, -24.01]}, {"time": 0.7667, "value": -24.01, "curve": [0.808, -24.01, 0.892, -5.61]}, {"time": 0.9333, "value": -5.61, "curve": [0.958, -5.61, 1.008, 0]}, {"time": 1.0333}]}}, "events": [{"time": 0.5, "name": "teleport"}]}, "test": {"bones": {"FACE": {"translate": [{"x": -21.96, "y": 36.35}]}}}}}