{"skeleton": {"hash": "v15p0G/C9eI", "spine": "4.1.24", "x": -225.58, "y": -3.18, "width": 420.14, "height": 653.75, "images": "./images/", "audio": "C:/Users/<USER>/Desktop/咩咩启示录（Cult of the Lamb）/Haro"}, "bones": [{"name": "root"}, {"name": "MAIN", "parent": "root", "rotation": 90.8}, {"name": "BodyBtm", "parent": "MAIN", "length": 144.01, "x": 0.84, "y": -3.09, "scaleX": 0.8609}, {"name": "BodyTop", "parent": "BodyBtm", "length": 176.01, "rotation": -0.14, "x": 146.01, "y": -0.03, "scaleX": 0.8609}, {"name": "Head", "parent": "BodyTop", "length": 226.14, "rotation": 1.5, "x": 199.99, "y": -2.27, "transform": "noScale", "color": "abe323ff"}, {"name": "Eyes", "parent": "Head", "rotation": -92.66, "x": 139.96, "y": 24.96, "color": "abe323ff"}, {"name": "Neck", "parent": "BodyTop", "rotation": 1.38, "x": 199.19, "y": -2.2}, {"name": "Antlers", "parent": "Head", "x": 171.97, "y": 1.76, "color": "abe323ff"}, {"name": "Antler1", "parent": "Antlers", "length": 113.64, "rotation": -32.37, "x": 69.58, "y": -77.16, "color": "abe323ff"}, {"name": "Antler6", "parent": "Antlers", "length": 116.79, "rotation": 32.96, "x": 70.82, "y": 59.22, "color": "abe323ff"}, {"name": "Crown", "parent": "Head", "length": 69.7, "rotation": -1.43, "x": 280.71, "y": -21.79, "color": "abe323ff"}, {"name": "CrownEye", "parent": "Crown", "x": 37.2, "y": -1.46, "color": "abe323ff"}, {"name": "Hands_1", "parent": "BodyTop", "length": 55.56, "rotation": -88.5, "x": 28.77, "y": -3.52}, {"name": "Hands_0", "parent": "BodyBtm", "length": 56.32, "rotation": 85.67, "x": 117.91, "y": -0.05}, {"name": "NeckLeaf5", "parent": "Neck", "length": 53.84, "rotation": 179.63, "x": -0.64, "y": 6.71, "scaleX": 1.3918}, {"name": "Shoulder_Left", "parent": "BodyTop", "x": 149.31, "y": 108.74}, {"name": "Shoulder_Right", "parent": "BodyTop", "x": 146.86, "y": -106.63}, {"name": "Cloak_Right1", "parent": "BodyTop", "length": 85.94, "rotation": -164.77, "x": 192.91, "y": -66.69}, {"name": "Cloak_Right2", "parent": "Cloak_Right1", "length": 89.93, "rotation": -2.61, "x": 86.95, "y": 1.11}, {"name": "Cloak_Right3", "parent": "Cloak_Right2", "length": 86.62, "rotation": -6.8, "x": 89.71, "y": -1.33}, {"name": "Cloak_Right4", "parent": "Cloak_Right3", "length": 95.71, "rotation": -5.4, "x": 85.95, "y": 0.11}, {"name": "CloakWave1", "parent": "BodyTop", "x": -159.73, "y": 175.21}, {"name": "CloakWave2", "parent": "BodyTop", "x": -173.81, "y": 114.84}, {"name": "CloakWave3", "parent": "BodyTop", "x": -179, "y": 37.9}, {"name": "CloakWave4", "parent": "BodyTop", "x": -178.6, "y": -40.54}, {"name": "CloakWave5", "parent": "BodyTop", "x": -174.31, "y": -119.55}, {"name": "CloakWave6", "parent": "BodyTop", "x": -156.42, "y": -179.76}, {"name": "Cloak_Left1", "parent": "BodyTop", "length": 80.71, "rotation": 162.97, "x": 195.36, "y": 49.91}, {"name": "Cloak_Left2", "parent": "Cloak_Left1", "length": 85.78, "rotation": 4.14, "x": 89.39, "y": -0.89}, {"name": "Cloak_Left3", "parent": "Cloak_Left2", "length": 87.82, "rotation": 5.34, "x": 86.94, "y": 0.83}, {"name": "Cloak_Left4", "parent": "Cloak_Left3", "length": 93.21, "rotation": 4.94, "x": 91.54, "y": -1.87}, {"name": "CrownHolder", "parent": "root", "x": -1.99, "y": 832.9, "color": "abe323ff"}, {"name": "Beak", "parent": "Eyes", "length": 104.12, "rotation": -156.84, "x": -76.57, "y": -24.49, "color": "abe323ff"}, {"name": "Beak2", "parent": "Eyes", "length": 104.12, "rotation": -166.14, "x": -60.37, "y": -46.5, "color": "abe323ff"}, {"name": "Face", "parent": "Head", "rotation": -93.99, "x": 95.08, "y": 3.43}, {"name": "Head2", "parent": "Head", "x": 203.61, "y": 102.15, "color": "abe323ff"}, {"name": "Head3", "parent": "Head", "x": 188.18, "y": -66.19, "color": "abe323ff"}], "slots": [{"name": "MASK", "bone": "root"}, {"name": "BehindCloak", "bone": "BodyBtm", "dark": "000000", "attachment": "BehindCloak"}, {"name": "Cloak", "bone": "BodyBtm", "dark": "000000"}, {"name": "Cloak_Left", "bone": "MAIN", "dark": "000000", "attachment": "Tunic_Left"}, {"name": "Cloak_Right", "bone": "MAIN", "dark": "000000", "attachment": "Tunic_Right"}, {"name": "Symbol_1", "bone": "Antler6", "dark": "000000"}, {"name": "Symbol_2", "bone": "Antler1", "dark": "000000"}, {"name": "Neck", "bone": "Neck", "dark": "000000"}, {"name": "<PERSON><PERSON><PERSON>", "bone": "Neck", "dark": "000000", "attachment": "<PERSON><PERSON>"}, {"name": "Hands_1", "bone": "Hands_1", "dark": "000000"}, {"name": "Hands_0", "bone": "Hands_0", "dark": "000000"}, {"name": "AntlersRight", "bone": "Antlers", "dark": "000000"}, {"name": "AntlersLeft", "bone": "Antlers", "dark": "000000"}, {"name": "HEAD", "bone": "Head", "dark": "000000", "attachment": "Head"}, {"name": "CROWN", "bone": "Crown", "dark": "000000", "attachment": "Crown"}, {"name": "CrownEye", "bone": "CrownEye", "dark": "000000", "attachment": "CrownEye"}, {"name": "CrownGrass", "bone": "Crown", "dark": "000000"}, {"name": "Leaf", "bone": "NeckLeaf5", "attachment": "Leaf"}, {"name": "Inner face", "bone": "Face", "attachment": "Inner face"}, {"name": "Face", "bone": "Eyes", "attachment": "Face"}, {"name": "Nostrils", "bone": "Eyes", "attachment": "Nostrils"}, {"name": "Beak copy", "bone": "Beak", "attachment": "Beak copy"}, {"name": "Beak copy2", "bone": "Beak2", "attachment": "Beak copy"}], "transform": [{"name": "Antlers", "order": 3, "bones": ["Antlers"], "target": "Eyes", "mixRotate": 0, "mixX": -0.5, "mixScaleX": 0, "mixShearY": 0}, {"name": "Crown", "order": 4, "bones": ["Crown"], "target": "Eyes", "mixRotate": 0, "mixX": 0.157, "mixScaleX": 0, "mixShearY": 0}, {"name": "CrownHolder", "order": 2, "bones": ["Crown"], "target": "CrownHolder", "mixRotate": 0, "mixX": 0, "mixScaleX": 0, "mixShearY": 0}, {"name": "Face", "order": 1, "bones": ["Face"], "target": "Eyes", "mixRotate": 0.61, "mixX": 0.6, "mixScaleX": 0.6, "mixShearY": 0}, {"name": "Neck", "bones": ["Neck"], "target": "Head", "mixRotate": 0.7, "mixX": 0.7, "mixScaleX": 0, "mixShearY": 0}], "skins": [{"name": "default", "attachments": {"Beak copy": {"Beak copy": {"x": 53.52, "y": 9.9, "rotation": 148.42, "width": 141, "height": 62}}, "Beak copy2": {"Beak copy": {"x": 41.86, "y": -5.3, "scaleY": -1, "rotation": -154.52, "width": 141, "height": 62}}, "BehindCloak": {"BehindCloak": {"x": 183.03, "y": -5.72, "scaleX": 1.0758, "rotation": -90.8, "width": 97, "height": 363}, "BehindCloak_Floating": {"x": 164.71, "y": -5.5, "rotation": -90.8, "width": 188, "height": 471}}, "Cloak": {"Cloak": {"type": "mesh", "uvs": [0.64547, 0.01952, 0.76758, 0.04918, 0.83227, 0.13264, 0.89308, 0.20232, 0.91382, 0.32831, 0.94856, 0.45521, 0.97075, 0.56389, 0.98576, 0.67455, 0.99669, 0.76611, 1, 0.87071, 1, 1, 0.87883, 1, 0.76282, 1, 0.65622, 1, 0.54031, 1, 0.46171, 1, 0.34111, 1, 0.2298, 1, 0.1232, 1, 0, 1, 0, 0.87847, 0.00346, 0.77476, 0.01106, 0.66718, 0.02345, 0.55202, 0.05582, 0.43104, 0.08761, 0.32351, 0.11625, 0.19455, 0.17635, 0.10994, 0.2644, 0.0282, 0.35351, 0, 0.46376, 0, 0.5651, 0, 0.46171, 0.89169, 0.45994, 0.79326, 0.46105, 0.68768, 0.46328, 0.58097, 0.46551, 0.47198, 0.47108, 0.36413, 0.47666, 0.25741, 0.47889, 0.15435, 0.4878, 0.06239, 0.5392, 0.89665, 0.54142, 0.79334, 0.5392, 0.68889, 0.53808, 0.57877, 0.53474, 0.46978, 0.52916, 0.36647, 0.52582, 0.24841, 0.51802, 0.1525, 0.51468, 0.06281, 0.34111, 0.79167, 0.33954, 0.58249, 0.34581, 0.45474, 0.35678, 0.32699, 0.22823, 0.78369, 0.23764, 0.57291, 0.24547, 0.45314, 0.27212, 0.32219, 0.12476, 0.7805, 0.14828, 0.56173, 0.16082, 0.44037, 0.18589, 0.32378, 0.20648, 0.22146, 0.29522, 0.23327, 0.65465, 0.78848, 0.66248, 0.57451, 0.6562, 0.45796, 0.64365, 0.32703, 0.63266, 0.24752, 0.3717, 0.24345, 0.75655, 0.79327, 0.74714, 0.57292, 0.74869, 0.44839, 0.733, 0.32706, 0.72439, 0.23396, 0.86472, 0.79647, 0.8459, 0.57771, 0.83647, 0.45159, 0.82862, 0.33346, 0.81367, 0.2269], "triangles": [11, 9, 10, 12, 75, 11, 11, 75, 9, 13, 70, 12, 15, 41, 14, 13, 14, 41, 15, 32, 41, 15, 16, 32, 16, 17, 50, 18, 54, 17, 58, 18, 20, 20, 18, 19, 12, 70, 75, 64, 13, 41, 41, 42, 64, 13, 64, 70, 32, 50, 33, 32, 16, 50, 17, 54, 50, 18, 58, 54, 58, 20, 21, 41, 32, 42, 32, 33, 42, 43, 33, 34, 33, 43, 42, 75, 8, 9, 70, 76, 75, 70, 64, 71, 42, 43, 64, 33, 50, 34, 34, 50, 51, 64, 43, 65, 58, 59, 54, 50, 54, 51, 54, 55, 51, 54, 59, 55, 21, 22, 58, 58, 22, 59, 34, 35, 43, 31, 0, 49, 40, 29, 30, 0, 48, 49, 31, 49, 30, 49, 40, 30, 75, 7, 8, 7, 76, 6, 7, 75, 76, 64, 65, 71, 70, 71, 76, 59, 22, 23, 35, 44, 43, 43, 44, 65, 34, 51, 35, 55, 56, 51, 35, 51, 36, 51, 56, 52, 35, 36, 44, 36, 45, 44, 36, 51, 52, 44, 45, 65, 71, 77, 76, 76, 5, 6, 76, 77, 5, 45, 66, 65, 65, 66, 71, 71, 72, 77, 71, 66, 72, 55, 59, 56, 56, 59, 60, 23, 24, 59, 59, 24, 60, 36, 46, 45, 36, 37, 46, 36, 52, 37, 45, 46, 66, 46, 67, 66, 66, 73, 72, 66, 67, 73, 77, 4, 5, 56, 57, 52, 52, 53, 37, 52, 57, 53, 60, 61, 56, 56, 61, 57, 72, 78, 77, 77, 78, 4, 72, 73, 78, 24, 25, 60, 60, 25, 61, 37, 38, 46, 38, 47, 46, 67, 47, 68, 67, 46, 47, 37, 53, 38, 38, 53, 69, 73, 79, 78, 4, 78, 3, 78, 79, 3, 67, 74, 73, 73, 74, 79, 67, 68, 74, 57, 63, 53, 53, 63, 69, 25, 26, 61, 61, 62, 57, 61, 26, 62, 57, 62, 63, 38, 39, 47, 39, 48, 47, 38, 69, 39, 47, 48, 68, 68, 48, 0, 68, 0, 74, 39, 69, 29, 79, 74, 2, 74, 0, 1, 69, 63, 29, 39, 29, 40, 63, 27, 28, 63, 28, 29, 79, 2, 3, 2, 74, 1, 26, 27, 62, 63, 62, 27, 48, 40, 49, 48, 39, 40], "vertices": [3, 3, 216.69, -60.04, 0.26127, 4, 15.31, -58.15, 0.64273, 16, 69.82, 46.59, 0.096, 3, 3, 204.97, -106.8, 0.30066, 4, 2.47, -104.62, 0.36334, 16, 58.11, -0.17, 0.336, 4, 3, 173.23, -131.28, 0.47103, 4, -29.85, -128.33, 0.19278, 16, 26.36, -24.65, 0.336, 2, 318.91, -131.74, 0.00019, 4, 3, 146.71, -154.33, 0.62387, 4, -59.11, -150.77, 0.13132, 16, -0.15, -47.7, 0.24, 2, 291.19, -154.72, 0.00482, 3, 3, 99.04, -161.76, 0.88008, 4, -105.28, -157.04, 0.08376, 2, 244.38, -162.04, 0.03617, 4, 3, 50.91, -174.56, 0.81728, 4, -154.96, -168.67, 0.03426, 16, -95.95, -67.93, 0.048, 2, 195.56, -174.71, 0.10046, 3, 3, 9.96, -182.61, 0.76845, 4, -195.28, -175.73, 0.01229, 2, 155.04, -182.66, 0.21926, 3, 3, -30.92, -187.9, 0.64088, 4, -235.35, -180.02, 0.00345, 2, 114.63, -187.85, 0.35567, 3, 3, -64.62, -191.71, 0.51174, 4, -269.84, -183.04, 0.00058, 2, 80.54, -191.58, 0.48767, 4, 3, -94.36, -192.65, 0.29875, 4, -299.27, -183.27, 3e-05, 2, 50.95, -192.43, 0.36789, 1, 51.62, -195.52, 0.33333, 1, 1, 1.33, -194.82, 1, 1, 1, -16.9, -148.04, 1, 1, 1, -24.86, -103.37, 1, 1, 1, -28.58, -62.38, 1, 1, 1, -27.96, -17.88, 1, 1, 1, -27.54, 12.3, 1, 1, 1, -26.9, 58.61, 1, 1, 1, -22.01, 101.29, 1, 1, 1, -12.87, 142.1, 1, 1, 1, 6.67, 189.14, 1, 3, 3, -92.48, 191.36, 0.1597, 2, 53.73, 191.57, 0.50697, 1, 54.33, 188.48, 0.33333, 2, 3, -63.38, 189.7, 0.31549, 2, 82.82, 189.83, 0.68451, 3, 3, -22.81, 186.32, 0.57726, 4, -218.86, 193.88, 0.00056, 2, 123.27, 186.35, 0.42219, 3, 3, 18.73, 181.08, 0.67148, 4, -177.24, 187.65, 0.00074, 2, 164.9, 181.01, 0.32778, 4, 3, 64.23, 168.13, 0.69231, 4, -132.51, 173.61, 0.0054, 2, 210.16, 167.94, 0.23829, 15, -85.08, 59.39, 0.064, 4, 3, 104.59, 155.47, 0.8065, 4, -91.55, 159.98, 0.03054, 2, 250.99, 155.17, 0.05095, 15, -44.72, 46.72, 0.112, 4, 3, 152.77, 143.92, 0.70633, 4, -45.29, 147.26, 0.07558, 2, 298.25, 143.5, 0.01009, 15, 3.46, 35.17, 0.208, 4, 3, 184.65, 120.48, 0.52319, 4, -12.39, 123.08, 0.14009, 2, 330.96, 119.98, 0.00072, 15, 35.34, 11.74, 0.336, 3, 3, 215.08, 86.32, 0.44998, 4, 17.21, 88.2, 0.27802, 15, 65.77, -22.42, 0.272, 3, 3, 225.32, 51.98, 0.31754, 4, 26.63, 53.63, 0.57046, 15, 76.01, -56.76, 0.112, 2, 3, 224.84, 9.65, 0.13174, 4, 25.13, 11.32, 0.86826, 2, 3, 224.39, -29.26, 0.18123, 4, 23.75, -27.57, 0.81877, 3, 3, -133.61, 14.51, 0.00091, 2, 12.44, 14.82, 0.66576, 1, 13.29, 11.73, 0.33333, 2, 3, -96.49, 14.77, 0.00264, 2, 49.56, 14.99, 0.99736, 2, 3, -43.83, 13.74, 0.33571, 2, 102.22, 13.83, 0.66429, 2, 3, 5.83, 12.32, 0.66839, 2, 151.87, 12.28, 0.33161, 1, 3, 46.9, 11, 1, 1, 3, 87.54, 8.4, 1, 1, 3, 127.74, 5.8, 1, 2, 3, 166.59, 4.5, 0.85375, 4, -33.23, 7.58, 0.14625, 2, 3, 201.21, 0.69, 0.34745, 4, 1.3, 2.93, 0.65255, 3, 3, -135.81, -15.22, 0.01743, 2, 10.16, -14.91, 0.64924, 1, 11.01, -17.99, 0.33333, 2, 3, -96.88, -16.52, 0.07143, 2, 49.09, -16.3, 0.92857, 2, 3, -44.63, -16.26, 0.40027, 2, 101.35, -16.17, 0.59973, 3, 3, 6.33, -16.41, 0.72054, 4, -193.94, -9.48, 0.00013, 2, 152.3, -16.45, 0.27933, 2, 3, 47.43, -15.59, 0.99911, 4, -152.83, -9.65, 0.00089, 2, 3, 86.4, -13.89, 0.99574, 4, -113.83, -8.89, 0.00426, 2, 3, 130.92, -13.12, 0.97428, 4, -69.3, -9.18, 0.02572, 2, 3, 167.11, -10.53, 0.83095, 4, -33.06, -7.47, 0.16905, 2, 3, 200.94, -9.63, 0.35081, 4, 0.77, -7.38, 0.64919, 3, 3, -95.38, 60.39, 0.08467, 2, 50.79, 60.6, 0.90011, 1, 51.63, 57.52, 0.01522, 4, 3, 5.79, 59.84, 0.64594, 4, -192.5, 67.19, 0.00022, 2, 151.96, 59.8, 0.35324, 1, 152.8, 56.71, 0.00061, 5, 3, 53.92, 56.89, 0.80689, 4, -144.46, 62.97, 0.00553, 2, 200.08, 56.72, 0.16738, 1, 200.92, 53.64, 0.00027, 15, -95.39, -51.85, 0.01993, 5, 3, 102.03, 52.13, 0.86463, 4, -96.49, 56.95, 0.01725, 2, 248.18, 51.84, 0.06867, 1, 249.02, 48.76, 0.00011, 15, -47.28, -56.62, 0.04934, 4, 3, -87.59, 103.65, 0.1715, 4, -284.69, 113.43, 1e-05, 2, 58.69, 103.84, 0.81864, 1, 59.54, 100.76, 0.00985, 5, 3, 9.85, 98.93, 0.65518, 4, -187.42, 106.16, 0.00042, 2, 156.11, 98.88, 0.34371, 1, 156.96, 95.79, 0.00039, 15, -139.46, -9.81, 0.00029, 5, 3, 54.96, 95.41, 0.76162, 4, -142.41, 101.45, 0.00533, 2, 201.22, 95.24, 0.19786, 1, 202.06, 92.15, 0.00018, 15, -94.35, -13.34, 0.03501, 5, 3, 104.21, 84.61, 0.83255, 4, -93.47, 89.37, 0.02652, 2, 250.43, 84.32, 0.05799, 1, 251.28, 81.24, 7e-05, 15, -45.1, -24.13, 0.08287, 4, 3, -77.35, 143.27, 0.24153, 4, -273.42, 152.77, 1e-05, 2, 69.03, 143.43, 0.75019, 1, 69.87, 140.35, 0.00828, 5, 3, 14.45, 133.19, 0.66382, 4, -181.92, 140.29, 0.00072, 2, 160.8, 133.13, 0.33322, 1, 161.65, 130.04, 0.0002, 15, -134.85, 24.45, 0.00205, 5, 3, 60.15, 127.86, 0.73333, 4, -136.38, 133.75, 0.00674, 2, 206.48, 127.68, 0.20777, 1, 207.33, 124.59, 9e-05, 15, -89.16, 19.12, 0.05207, 5, 3, 103.98, 117.73, 0.81773, 4, -92.82, 122.48, 0.02746, 2, 250.29, 117.44, 0.06076, 1, 251.14, 114.35, 4e-05, 15, -45.33, 8.98, 0.09401, 4, 3, 142.45, 109.38, 0.76693, 4, -54.58, 113.12, 0.05998, 2, 288.74, 108.99, 0.00801, 15, -6.85, 0.64, 0.16507, 4, 3, 137.62, 75.36, 0.84349, 4, -60.31, 79.24, 0.04028, 2, 283.83, 74.98, 0.00538, 15, -11.69, -33.38, 0.11085, 4, 3, -95.54, -60.01, 0.18303, 4, -296.94, -49.97, 0.00015, 2, 50.32, -59.8, 0.81031, 1, 51.17, -62.89, 0.00651, 4, 3, 7.39, -64.19, 0.73639, 4, -194.15, -56.85, 0.00441, 16, -139.47, 42.43, 0.00128, 2, 153.25, -64.24, 0.25793, 4, 3, 51.36, -62.29, 0.85876, 4, -150.15, -56.09, 0.0185, 16, -95.5, 44.34, 0.00057, 2, 197.22, -62.44, 0.12217, 4, 3, 100.78, -58.03, 0.86152, 4, -100.64, -53.14, 0.04405, 16, -46.08, 48.6, 0.04798, 2, 246.65, -58.31, 0.04645, 4, 3, 130.8, -54.15, 0.80395, 4, -70.53, -50.05, 0.08226, 16, -16.06, 52.47, 0.11372, 2, 276.68, -54.51, 7e-05, 4, 3, 133.46, 46.04, 0.90946, 4, -65.24, 50.04, 0.0233, 2, 279.59, 45.67, 0.00311, 15, -15.85, -62.7, 0.06413, 4, 3, -93.5, -99.17, 0.26598, 4, -295.93, -89.17, 0.00026, 2, 52.26, -98.96, 0.68096, 1, 53.11, -102.05, 0.0528, 4, 3, 7.63, -96.71, 0.74651, 4, -194.77, -89.36, 0.00702, 16, -139.23, 9.92, 0.00152, 2, 153.4, -96.75, 0.24495, 4, 3, 54.57, -97.84, 0.84444, 4, -147.87, -91.72, 0.03612, 16, -92.29, 8.78, 0.00057, 2, 200.34, -98.01, 0.11886, 4, 3, 100.39, -92.34, 0.78315, 4, -101.93, -87.43, 0.07093, 16, -46.47, 14.28, 0.10311, 2, 246.17, -92.62, 0.04281, 4, 3, 135.52, -89.44, 0.66873, 4, -66.73, -85.45, 0.12715, 16, -11.34, 17.18, 0.204, 2, 281.31, -89.81, 0.00012, 4, 3, -86.6, -140.79, 0.35176, 4, -290.12, -130.95, 0.00033, 2, 59.06, -140.6, 0.57137, 1, 59.9, -143.68, 0.07653, 4, 3, 5.39, -134.61, 0.75131, 4, -198, -127.19, 0.00917, 16, -141.47, -27.98, 0.00078, 2, 151.06, -134.65, 0.23874, 4, 3, 52.99, -131.54, 0.83599, 4, -150.34, -125.37, 0.03907, 16, -93.88, -24.91, 0.02016, 2, 198.67, -131.7, 0.10478, 4, 3, 97.57, -129.04, 0.79469, 4, -105.71, -124.03, 0.08664, 16, -49.3, -22.41, 0.08517, 2, 243.25, -129.31, 0.03351, 4, 3, 137.8, -123.76, 0.54786, 4, -65.36, -119.81, 0.16727, 16, -9.06, -17.13, 0.2847, 2, 283.5, -124.13, 0.00016], "hull": 32, "edges": [38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 30, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 28, 30, 28, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 98, 62, 58, 60, 60, 62, 80, 60, 2, 0, 0, 62, 26, 28, 30, 32, 32, 34, 34, 36, 36, 38, 24, 26, 20, 22, 22, 24, 32, 100, 100, 102, 102, 104, 104, 106, 34, 108, 108, 110, 110, 112, 112, 114, 36, 116, 116, 118, 118, 120, 120, 122, 52, 124, 122, 124, 126, 124, 114, 126, 26, 128, 128, 130, 130, 132, 132, 134, 136, 94, 134, 136, 76, 138, 138, 126, 106, 138, 24, 140, 140, 142, 142, 144, 144, 146, 148, 136, 146, 148, 22, 150, 150, 152, 152, 154, 154, 156, 158, 148, 156, 158, 158, 6], "width": 354, "height": 291}}, "Cloak_Left": {"Tunic_Left": {"type": "mesh", "uvs": [1, 0.11356, 0.98611, 0.20456, 0.97818, 0.30556, 0.96628, 0.40656, 0.95834, 0.52456, 0.95636, 0.62656, 0.95072, 0.71197, 0.95041, 0.81356, 0.95041, 0.90456, 0.95834, 0.99999, 0.7296, 0.99999, 0.49505, 0.99999, 0.30857, 0.99999, 0.134, 0.99999, 0, 1, 0, 0.89356, 0.00815, 0.81024, 0.02361, 0.70105, 0.04072, 0.59653, 0.08684, 0.49755, 0.14295, 0.3901, 0.20251, 0.28871, 0.25572, 0.187, 0.35364, 0.10778, 0.47141, 0.01465, 0.54266, 0, 0.62201, 0, 0.72715, 0.001, 0.82237, 0, 1, 0, 0.12408, 0.80956, 0.11956, 0.89316, 0.14097, 0.69957, 0.1657, 0.60388, 0.19808, 0.5089, 0.24965, 0.40069, 0.31041, 0.29098, 0.38069, 0.19101, 0.4474, 0.09592, 0.49523, 0.89765, 0.49507, 0.80779, 0.50301, 0.70212, 0.51383, 0.61131, 0.536, 0.51608, 0.55781, 0.39933, 0.59392, 0.29954, 0.7368, 0.90112, 0.74043, 0.81511, 0.73597, 0.71383, 0.74097, 0.62175, 0.75062, 0.52154, 0.75958, 0.40221, 0.76648, 0.30583, 0.78234, 0.20027, 0.79863, 0.1079, 0.63636, 0.19469, 0.67706, 0.10225, 0.3052, 0.89401, 0.29868, 0.80945, 0.30853, 0.69935, 0.32675, 0.60732, 0.35994, 0.51251, 0.39987, 0.4005, 0.45933, 0.29525, 0.51322, 0.19313, 0.5644, 0.09895], "triangles": [14, 31, 13, 14, 15, 31, 10, 8, 9, 10, 46, 8, 46, 10, 39, 10, 11, 39, 11, 12, 39, 13, 57, 12, 12, 57, 39, 13, 31, 57, 46, 47, 8, 47, 7, 8, 46, 39, 47, 57, 40, 39, 39, 40, 47, 57, 31, 58, 31, 30, 58, 57, 58, 40, 15, 16, 31, 31, 16, 30, 16, 17, 30, 30, 32, 58, 30, 17, 32, 58, 59, 40, 58, 32, 59, 17, 18, 32, 32, 33, 59, 32, 18, 33, 45, 55, 52, 52, 53, 2, 52, 55, 53, 2, 53, 1, 45, 64, 55, 63, 37, 64, 53, 54, 1, 1, 54, 0, 55, 56, 53, 53, 56, 54, 64, 65, 55, 55, 65, 56, 37, 38, 64, 64, 38, 65, 22, 23, 37, 37, 23, 38, 54, 28, 0, 28, 29, 0, 56, 27, 54, 54, 27, 28, 38, 23, 24, 65, 26, 56, 56, 26, 27, 38, 25, 65, 65, 25, 26, 38, 24, 25, 33, 34, 60, 60, 34, 61, 18, 19, 33, 33, 19, 34, 4, 51, 3, 43, 44, 50, 50, 44, 51, 61, 62, 43, 43, 62, 44, 34, 35, 61, 61, 35, 62, 19, 20, 34, 34, 20, 35, 51, 52, 3, 3, 52, 2, 44, 45, 51, 51, 45, 52, 20, 21, 35, 35, 36, 62, 35, 21, 36, 62, 63, 44, 62, 36, 63, 44, 63, 45, 63, 64, 45, 36, 37, 63, 21, 22, 36, 36, 22, 37, 40, 48, 47, 47, 48, 7, 7, 48, 6, 40, 41, 48, 40, 59, 41, 48, 49, 6, 48, 41, 49, 6, 49, 5, 59, 60, 41, 41, 42, 49, 41, 60, 42, 59, 33, 60, 49, 50, 5, 5, 50, 4, 42, 43, 49, 49, 43, 50, 60, 61, 42, 42, 61, 43, 50, 51, 4], "vertices": [3, 29, -155.33, 72.7, 0.00468, 28, -74.48, 58.75, 0.03613, 27, 10.87, 52.33, 0.95919, 3, 29, -121.86, 74.15, 0.02886, 28, -41.3, 63.32, 0.16676, 27, 43.63, 59.28, 0.80437, 3, 29, -84.89, 77.15, 0.11349, 28, -4.76, 69.74, 0.39224, 27, 79.61, 68.32, 0.49427, 5, 29, -47.83, 79.41, 0.32175, 28, 31.93, 75.45, 0.44547, 27, 115.79, 76.66, 0.22262, 30, -131.85, 92.98, 0.00017, 23, 241.2, -27.39, 0.01, 5, 29, -4.66, 83.15, 0.60723, 28, 74.56, 83.19, 0.26881, 27, 157.76, 87.46, 0.06625, 30, -88.52, 92.99, 0.00771, 23, 197.91, -25.43, 0.05, 5, 29, 32.55, 87.28, 0.73566, 28, 111.22, 90.77, 0.09735, 27, 193.78, 97.66, 0.01283, 30, -51.09, 93.9, 0.04015, 23, 160.48, -24.64, 0.114, 5, 29, 75.98, 92.52, 0.65352, 28, 153.98, 100.03, 0.02044, 27, 235.75, 109.98, 0.00098, 30, -7.37, 95.38, 0.14466, 23, 116.74, -24.13, 0.1804, 5, 29, 118.67, 96.59, 0.41205, 28, 196.1, 108.05, 0.00236, 30, 35.51, 95.76, 0.31833, 23, 73.89, -22.55, 0.2557, 22, 68.69, -99.49, 0.01156, 4, 29, 150.37, 100.42, 0.22996, 30, 67.42, 96.85, 0.43821, 23, 41.96, -22.19, 0.31943, 22, 36.77, -99.13, 0.0124, 4, 29, 186.84, 106.31, 0.14031, 30, 104.26, 99.57, 0.46124, 23, 5.03, -23.24, 0.35844, 22, -0.16, -100.17, 0.04, 4, 29, 192.6, 64.38, 0.09507, 30, 106.4, 57.3, 0.5046, 23, 4.82, 19.09, 0.28243, 22, -0.37, -57.85, 0.1179, 5, 29, 194.16, 20.86, 0.03715, 30, 104.2, 13.81, 0.56425, 23, 9, 62.43, 0.1239, 22, 3.8, -14.5, 0.2422, 21, -10.27, -74.88, 0.0325, 6, 29, 191.01, -14.27, 0.007, 28, 278.45, 4.42, 0.0001, 30, 98.04, -20.92, 0.59301, 23, 16.73, 96.85, 0.0305, 22, 11.54, 19.91, 0.2422, 21, -2.54, -40.46, 0.1272, 5, 29, 183.96, -47.65, 0.00232, 28, 274.54, -29.48, 0.00074, 30, 88.14, -53.57, 0.59077, 22, 22.91, 52.08, 0.1179, 21, 8.83, -8.29, 0.28827, 5, 29, 177.46, -73.41, 0.00458, 28, 270.47, -55.73, 0.00167, 30, 79.45, -78.67, 0.59086, 22, 32.73, 76.76, 0.04, 21, 18.65, 16.38, 0.36289, 5, 29, 137.95, -78.19, 0.02603, 28, 231.58, -64.17, 0.0099, 30, 39.68, -80.03, 0.6339, 22, 72.53, 76.3, 0.0124, 21, 58.45, 15.93, 0.31777, 5, 29, 113.14, -80.12, 0.10037, 28, 207.05, -68.4, 0.04639, 30, 14.79, -79.82, 0.61924, 22, 97.38, 74.96, 0.0124, 21, 83.3, 14.59, 0.2216, 4, 29, 74.14, -82.41, 0.22105, 28, 168.43, -74.32, 0.14822, 30, -24.27, -78.75, 0.46553, 21, 122.27, 11.74, 0.1652, 5, 29, 41.94, -82.21, 0.27869, 28, 136.36, -77.11, 0.36073, 27, 230.96, -67.97, 0.00013, 30, -56.32, -75.77, 0.27195, 21, 154.15, 7.31, 0.0885, 5, 29, 4.87, -78.1, 0.1939, 28, 99.06, -76.47, 0.64514, 27, 193.71, -70.02, 0.00735, 30, -92.91, -68.49, 0.11361, 21, 190.37, -1.63, 0.04, 5, 29, -35.31, -72.5, 0.07755, 28, 58.54, -74.64, 0.82137, 27, 153.16, -71.12, 0.06239, 30, -132.45, -59.45, 0.03268, 21, 229.46, -12.46, 0.006, 4, 29, -73.56, -66.03, 0.01541, 28, 19.85, -71.76, 0.74512, 27, 114.37, -71.03, 0.23432, 30, -170, -49.71, 0.00515, 4, 29, -112.04, -60.76, 0.0007, 28, -18.95, -70.1, 0.46211, 27, 75.54, -72.18, 0.53691, 30, -207.89, -41.15, 0.00028, 2, 28, -51.21, -58.56, 0.20435, 27, 42.54, -62.99, 0.79565, 2, 28, -89.22, -44.51, 0.08117, 27, 3.61, -51.73, 0.91883, 2, 28, -97.27, -32.77, 0.05008, 27, -5.27, -40.6, 0.94992, 2, 28, -100.39, -18.43, 0.02137, 27, -9.41, -26.51, 0.97863, 2, 28, -104.15, 0.66, 0.0026, 27, -14.54, -7.75, 0.9974, 3, 29, -192.76, 35.07, 6e-05, 28, -108.24, 17.8, 0.00144, 27, -19.86, 9.05, 0.99851, 3, 29, -196.7, 67.69, 0.00041, 28, -115.21, 49.91, 0.00645, 27, -29.12, 40.58, 0.99314, 5, 29, 118.03, -57.1, 0.10711, 28, 209.78, -45.03, 0.03379, 30, 21.64, -57.31, 0.62521, 22, 89.51, 52.79, 0.0592, 21, 75.43, -7.58, 0.17468, 5, 29, 144.99, -55.05, 0.01893, 28, 236.43, -40.48, 0.00606, 30, 48.68, -57.59, 0.65669, 22, 62.51, 54.3, 0.0928, 21, 48.43, -6.07, 0.22552, 5, 29, 77.26, -59.26, 0.27788, 28, 169.39, -50.97, 0.12426, 30, -19.16, -55.95, 0.44898, 22, 130.21, 49.57, 0.04064, 21, 116.13, -10.8, 0.10824, 6, 29, 41.85, -58.93, 0.33381, 28, 134.1, -53.94, 0.33201, 27, 227.03, -45.02, 0.0001, 30, -54.42, -52.57, 0.24033, 22, 165.27, 44.59, 0.0208, 21, 151.19, -15.78, 0.07296, 6, 29, 6.52, -57.17, 0.24967, 28, 98.76, -55.48, 0.61688, 27, 191.89, -49.1, 0.00572, 30, -89.46, -47.77, 0.09093, 22, 200.06, 38.21, 0.0112, 21, 185.98, -22.16, 0.0256, 5, 29, -35.51, -52.64, 0.07345, 28, 56.49, -54.89, 0.84557, 27, 149.69, -51.56, 0.05263, 30, -130.94, -39.65, 0.02195, 21, 227.05, -32.17, 0.0064, 4, 29, -75.37, -46.14, 0.00989, 28, 16.2, -52.12, 0.74472, 27, 109.3, -51.71, 0.24251, 30, -170.1, -29.74, 0.00287, 4, 29, -113.36, -37.64, 0.00021, 28, -22.42, -47.2, 0.42266, 27, 70.43, -49.58, 0.57702, 30, -207.21, -18, 0.0001, 2, 28, -59.14, -42.53, 0.15547, 27, 33.47, -47.58, 0.84453, 6, 29, 157.59, 16.47, 0.09573, 28, 242.32, 31.91, 8e-05, 30, 67.39, 12.59, 0.58218, 23, 45.82, 61.98, 0.10065, 22, 40.63, -14.96, 0.2108, 21, 26.55, -75.33, 0.01056, 6, 29, 126.63, 13.07, 0.26745, 28, 211.81, 25.64, 0.00283, 30, 36.25, 11.86, 0.47458, 23, 76.96, 61.29, 0.06793, 22, 71.77, -15.65, 0.17725, 21, 57.69, -76.02, 0.00996, 5, 29, 78.89, 8.41, 0.57498, 28, 164.71, 16.55, 0.02093, 30, -11.71, 11.33, 0.23472, 23, 124.9, 59.64, 0.04297, 22, 119.71, -17.29, 0.1264, 6, 29, 36.82, 5.34, 0.67092, 28, 123.11, 9.57, 0.13953, 27, 211.48, 17.54, 0.00022, 30, -53.89, 11.89, 0.08181, 23, 167.01, 57.16, 0.0208, 22, 161.82, -19.77, 0.08672, 6, 29, 1.63, 5.21, 0.54871, 28, 88.08, 6.17, 0.38414, 27, 176.8, 11.62, 0.005, 30, -88.96, 14.79, 0.01095, 23, 201.91, 52.66, 0.0112, 22, 196.72, -24.27, 0.04, 5, 29, -41.39, 4.07, 0.21732, 28, 45.36, 1.04, 0.67726, 27, 134.55, 3.41, 0.09127, 30, -131.92, 17.36, 0.00135, 22, 239.51, -28.8, 0.0128, 4, 29, -78.55, 6.31, 0.05561, 28, 8.15, -0.2, 0.61111, 27, 97.53, -0.51, 0.33327, 30, -168.75, 22.79, 1e-05, 4, 29, 155.77, 61.27, 0.16523, 30, 69.43, 57.38, 0.50049, 23, 41.74, 17.33, 0.24216, 22, 36.55, -59.6, 0.09213, 5, 29, 124.37, 58.15, 0.38025, 28, 205.36, 70.32, 0.00039, 30, 37.88, 56.97, 0.36521, 23, 73.28, 16.3, 0.19241, 22, 68.09, -60.64, 0.06173, 6, 29, 80.18, 51.79, 0.63095, 28, 161.95, 59.87, 0.0135, 27, 246.6, 70.5, 0.00011, 30, -6.69, 54.44, 0.16859, 23, 117.93, 16.8, 0.14048, 22, 112.73, -60.13, 0.04637, 6, 29, 35.74, 47.52, 0.77898, 28, 118.1, 51.48, 0.08148, 27, 203.47, 58.97, 0.00365, 30, -51.33, 54.01, 0.03477, 23, 162.54, 15.2, 0.08512, 22, 157.34, -61.74, 0.016, 6, 29, -1.15, 44.87, 0.59447, 28, 81.62, 45.4, 0.31107, 27, 167.53, 50.28, 0.03792, 30, -88.31, 54.54, 0.00374, 23, 199.46, 12.99, 0.04, 22, 194.26, -63.95, 0.0128, 4, 29, -44.82, 41.26, 0.29984, 28, 38.48, 37.74, 0.5387, 27, 125.04, 39.52, 0.15025, 23, 243.23, 10.83, 0.0112, 3, 29, -80.09, 38.28, 0.0777, 28, 3.63, 31.49, 0.48234, 27, 90.74, 30.77, 0.43995, 3, 29, -118.9, 36.54, 0.01365, 28, -34.84, 26.14, 0.21254, 27, 52.75, 22.67, 0.77381, 3, 29, -152.92, 35.46, 0.0014, 28, -68.61, 21.9, 0.03283, 27, 19.37, 16, 0.96577, 3, 29, -117.69, 9.48, 0.00208, 28, -31.12, -0.68, 0.27776, 27, 58.4, -3.82, 0.72016, 3, 29, -152.28, 12.88, 9e-05, 28, -65.87, -0.52, 0.06291, 27, 23.73, -6.16, 0.937, 6, 29, 152.83, -19.51, 0.03221, 28, 240.93, -4.36, 0.0021, 30, 59.55, -22.86, 0.64756, 23, 55.26, 97.03, 0.01152, 22, 50.07, 20.09, 0.2076, 21, 35.99, -40.28, 0.099, 6, 29, 123.95, -23.85, 0.17507, 28, 212.58, -11.37, 0.01811, 30, 30.4, -24.69, 0.57245, 23, 84.47, 97.54, 0.01065, 22, 79.27, 20.6, 0.17152, 21, 65.2, -39.77, 0.0522, 5, 29, 77.42, -27.64, 0.38162, 28, 166.6, -19.48, 0.08174, 30, -16.28, -24.46, 0.36731, 22, 125.9, 18.25, 0.128, 21, 111.82, -42.12, 0.04132, 6, 29, 39.52, -29.2, 0.51856, 28, 129.01, -24.56, 0.22956, 27, 219.84, -16.08, 1e-05, 30, -54.17, -22.75, 0.15875, 22, 163.67, 14.82, 0.07872, 21, 149.59, -45.56, 0.0144, 6, 29, 4.24, -27.28, 0.34789, 28, 93.71, -25.93, 0.54341, 27, 184.72, -20, 0.00397, 30, -89.16, -17.8, 0.05354, 22, 198.4, 8.28, 0.0448, 21, 184.32, -52.09, 0.0064, 5, 29, -38.59, -24.65, 0.14558, 28, 50.82, -27.3, 0.78096, 27, 142.04, -24.46, 0.05238, 30, -131.6, -11.49, 0.00988, 22, 240.51, 0.05, 0.0112, 4, 29, -77.12, -18.6, 0.01087, 28, 11.89, -24.87, 0.70739, 27, 103.04, -24.84, 0.28088, 30, -169.47, -2.15, 0.00086, 3, 29, -115.53, -13.2, 5e-05, 28, -26.85, -23.07, 0.3612, 27, 64.27, -25.84, 0.63875, 2, 28, -62.64, -21.14, 0.0996, 27, 28.44, -26.5, 0.9004], "hull": 30, "edges": [48, 46, 46, 44, 44, 42, 42, 40, 28, 30, 34, 36, 36, 38, 38, 40, 30, 32, 32, 34, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 26, 28, 62, 30, 26, 62, 62, 60, 64, 34, 60, 64, 66, 36, 64, 66, 68, 38, 66, 68, 70, 40, 68, 70, 6, 4, 4, 2, 2, 0, 0, 58, 72, 42, 70, 72, 74, 44, 72, 74, 76, 46, 74, 76, 48, 50, 76, 50, 22, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 18, 20, 20, 22, 16, 92, 92, 78, 20, 92, 14, 94, 94, 80, 92, 94, 12, 96, 96, 82, 94, 96, 10, 98, 98, 84, 96, 98, 8, 100, 100, 86, 98, 100, 6, 102, 102, 88, 100, 102, 4, 104, 104, 90, 102, 104, 2, 106, 104, 106, 0, 108, 106, 108, 56, 58, 108, 56, 110, 106, 90, 110, 112, 108, 110, 112, 54, 56, 112, 54, 22, 24, 24, 26, 62, 114, 114, 78, 24, 114, 60, 116, 116, 80, 114, 116, 64, 118, 118, 82, 116, 118, 66, 120, 120, 84, 118, 120, 68, 122, 122, 86, 120, 122, 70, 124, 124, 88, 122, 124, 72, 126, 126, 90, 124, 126, 74, 128, 128, 110, 126, 128, 76, 130, 130, 112, 128, 130, 50, 52, 52, 54, 130, 52, 60, 32], "width": 176, "height": 291}}, "Cloak_Right": {"Tunic_Right": {"type": "mesh", "uvs": [0.65879, 0.10156, 0.77711, 0.18811, 0.73654, 0.2212, 0.79636, 0.28726, 0.86495, 0.39114, 0.92928, 0.5093, 0.95978, 0.6185, 0.98053, 0.71268, 1, 0.82075, 1, 0.90256, 1, 1, 0.86006, 0.99999, 0.68549, 0.99799, 0.47323, 1, 0.2808, 1, 0.03769, 0.99999, 0.03769, 0.89556, 0.04101, 0.80502, 0.03803, 0.70056, 0.04001, 0.60056, 0.03272, 0.50156, 0.02559, 0.39472, 0.01378, 0.29337, 0.01142, 0.18835, 0.00708, 0.09451, 0.08383, 0.021, 0.1955, 0.005, 0.31651, 0.001, 0.4157, 0, 0.50893, 0.019, 0.56952, 0.03, 0.26544, 0.79981, 0.26867, 0.89818, 0.25487, 0.70107, 0.25592, 0.60016, 0.26015, 0.50643, 0.25304, 0.39364, 0.24494, 0.28998, 0.22794, 0.19101, 0.20537, 0.09892, 0.6936, 0.89765, 0.68749, 0.81279, 0.67956, 0.70312, 0.65723, 0.60879, 0.64114, 0.50308, 0.61336, 0.39033, 0.57662, 0.29177, 0.84634, 0.90205, 0.8316, 0.80743, 0.82409, 0.71099, 0.79927, 0.6095, 0.77498, 0.50642, 0.73306, 0.3911, 0.69758, 0.29241, 0.6232, 0.19435, 0.54274, 0.10589, 0.50755, 0.1876, 0.43023, 0.10131, 0.47977, 0.89401, 0.48285, 0.80308, 0.48311, 0.69935, 0.48148, 0.59932, 0.47698, 0.50351, 0.46137, 0.3925, 0.44742, 0.29225, 0.40609, 0.19213, 0.36999, 0.09895], "triangles": [15, 32, 14, 13, 14, 58, 11, 9, 10, 13, 58, 12, 58, 14, 32, 11, 12, 47, 12, 40, 47, 11, 47, 9, 15, 16, 32, 12, 58, 40, 9, 47, 8, 40, 48, 47, 47, 48, 8, 16, 17, 32, 17, 31, 32, 32, 31, 58, 58, 41, 40, 40, 41, 48, 58, 59, 41, 58, 31, 59, 48, 7, 8, 59, 42, 41, 41, 49, 48, 41, 42, 49, 48, 49, 7, 17, 33, 31, 59, 31, 60, 17, 18, 33, 59, 60, 42, 31, 33, 60, 49, 6, 7, 42, 50, 49, 49, 50, 6, 60, 43, 42, 42, 43, 50, 60, 34, 61, 60, 33, 34, 18, 19, 33, 33, 19, 34, 60, 61, 43, 61, 44, 43, 34, 19, 35, 61, 35, 62, 61, 34, 35, 35, 19, 20, 61, 62, 44, 20, 36, 35, 20, 21, 36, 50, 5, 6, 43, 51, 50, 50, 51, 5, 43, 44, 51, 51, 4, 5, 35, 63, 62, 35, 36, 63, 44, 52, 51, 51, 52, 4, 62, 45, 44, 62, 63, 45, 44, 45, 52, 36, 64, 63, 63, 46, 45, 63, 64, 46, 52, 3, 4, 45, 53, 52, 52, 53, 3, 45, 46, 53, 3, 53, 2, 64, 56, 46, 2, 53, 54, 53, 46, 54, 46, 56, 54, 2, 54, 1, 21, 37, 36, 21, 22, 37, 36, 37, 64, 22, 38, 37, 22, 23, 38, 37, 65, 64, 64, 65, 56, 37, 38, 65, 56, 55, 54, 54, 0, 1, 54, 55, 0, 38, 66, 65, 65, 57, 56, 65, 66, 57, 23, 39, 38, 38, 39, 66, 23, 24, 39, 56, 57, 55, 57, 29, 55, 55, 30, 0, 55, 29, 30, 29, 57, 28, 39, 27, 66, 57, 66, 28, 66, 27, 28, 24, 25, 39, 25, 26, 39, 39, 26, 27], "vertices": [2, 17, 34.1, 59.73, 0.64387, 18, -55.47, 56.15, 0.35613, 2, 17, 70.64, 72.09, 0.49376, 18, -19.53, 70.17, 0.50624, 2, 17, 80.27, 61.55, 0.35457, 18, -9.43, 60.07, 0.64543, 4, 17, 106.61, 65.56, 0.11307, 18, 16.71, 65.28, 0.87904, 19, -80.37, 57.49, 0.00783, 20, -170.99, 41.47, 6e-05, 5, 17, 146.76, 67.33, 0.01559, 18, 56.73, 68.87, 0.92054, 19, -41.06, 65.8, 0.05317, 20, -132.63, 53.44, 0.00469, 26, 225.42, 9.55, 0.006, 5, 17, 191.72, 66.9, 0.00043, 18, 101.66, 70.5, 0.74524, 19, 3.37, 72.74, 0.17174, 20, -89.05, 64.53, 0.03459, 26, 181.92, -1.86, 0.048, 4, 18, 141.97, 66.79, 0.43581, 19, 43.82, 73.83, 0.3185, 20, -48.88, 69.42, 0.12419, 26, 141.78, -7.04, 0.1215, 4, 18, 176.34, 62.61, 0.17769, 19, 78.45, 73.74, 0.3211, 20, -14.39, 72.6, 0.29881, 26, 107.32, -10.47, 0.2024, 5, 18, 208.98, 57.68, 0.05198, 19, 111.45, 72.72, 0.17439, 20, 18.55, 74.68, 0.49078, 26, 74.4, -12.8, 0.2722, 25, 92.29, -73, 0.01065, 5, 18, 235.6, 51.22, 0.01079, 19, 138.64, 69.45, 0.06174, 20, 45.93, 73.99, 0.59504, 26, 47.01, -12.31, 0.32003, 25, 64.9, -72.52, 0.0124, 5, 18, 269.16, 42.55, 0.00223, 19, 173, 64.82, 0.02026, 20, 80.57, 72.6, 0.58173, 26, 12.36, -11.18, 0.35578, 25, 30.26, -71.39, 0.04, 5, 18, 274, 16.69, 0.00082, 19, 180.86, 39.72, 0.00979, 20, 90.76, 48.36, 0.58795, 26, 2, 12.99, 0.28353, 25, 19.89, -47.21, 0.1179, 6, 18, 275.41, -15.69, 9e-05, 19, 186.1, 7.73, 0.00208, 20, 98.98, 17, 0.60434, 26, -6.46, 44.29, 0.12579, 25, 11.44, -15.92, 0.2362, 24, 15.72, -94.94, 0.0315, 5, 19, 189.33, -32.16, 0.00581, 20, 105.95, -22.4, 0.59349, 26, -13.72, 83.64, 0.0325, 25, 4.17, 23.43, 0.2422, 24, 8.46, -55.58, 0.126, 4, 19, 188.11, -68.96, 0.01751, 20, 108.2, -59.15, 0.57805, 25, 1.65, 60.16, 0.1179, 24, 5.94, -18.85, 0.28653, 4, 19, 184.13, -113.77, 0.03018, 20, 108.46, -104.14, 0.56738, 25, 1.07, 105.15, 0.04, 24, 5.35, 26.13, 0.36244, 5, 17, 304.4, -135.51, 0.00039, 19, 147.64, -108.52, 0.08753, 20, 71.63, -102.35, 0.58864, 25, 37.91, 103.63, 0.0124, 24, 42.2, 24.61, 0.31103, 6, 17, 273.6, -126.84, 0.00414, 18, 192.28, -119.32, 0.00047, 19, 115.83, -105.01, 0.23304, 20, 39.63, -101.85, 0.49726, 25, 69.91, 103.37, 0.01109, 24, 74.2, 24.35, 0.254, 5, 17, 228.18, -113.75, 0.02289, 18, 146.31, -108.31, 0.00642, 19, 68.88, -99.52, 0.4839, 20, -7.63, -100.8, 0.31679, 24, 121.46, 23.65, 0.17, 5, 17, 181.03, -101.1, 0.08293, 18, 98.64, -97.81, 0.03919, 19, 20.29, -94.75, 0.626, 20, -56.44, -100.62, 0.13438, 24, 170.28, 23.83, 0.1175, 5, 17, 143.6, -91.85, 0.22632, 18, 60.82, -90.28, 0.12435, 19, -18.15, -91.75, 0.5607, 20, -95, -101.25, 0.04062, 24, 208.83, 24.74, 0.048, 5, 17, 105.52, -82.39, 0.47554, 18, 22.35, -82.56, 0.1699, 19, -57.26, -88.64, 0.3352, 20, -134.23, -101.83, 0.00737, 24, 248.05, 25.61, 0.012, 4, 17, 69.15, -74.31, 0.72325, 18, -14.35, -76.15, 0.13436, 19, -94.46, -86.62, 0.14162, 20, -171.46, -103.32, 0.00077, 3, 17, 31.96, -64.18, 0.93585, 18, -51.96, -67.72, 0.02077, 19, -132.81, -82.71, 0.04337, 3, 17, -1.39, -55.52, 0.98848, 18, -85.67, -60.6, 0.00249, 19, -167.12, -79.63, 0.00903, 3, 17, -23.45, -34.48, 0.99859, 18, -108.66, -40.59, 6e-05, 19, -192.33, -62.48, 0.00135, 3, 17, -23.44, -13.01, 0.99748, 18, -109.64, -19.13, 0.00213, 19, -195.83, -41.29, 0.00038, 3, 17, -18.73, 8.93, 0.97923, 18, -105.92, 2.99, 0.02077, 19, -194.77, -18.88, 1e-05, 2, 17, -14.06, 26.68, 0.92518, 18, -102.07, 20.94, 0.07482, 2, 17, -2.63, 41.36, 0.8656, 18, -91.32, 36.13, 0.1344, 2, 17, 4.32, 51.03, 0.80946, 18, -84.82, 46.11, 0.19054, 6, 17, 282.1, -87.04, 0.00171, 18, 198.96, -79.17, 9e-05, 19, 117.7, -64.36, 0.23197, 20, 37.67, -61.2, 0.51556, 25, 72.17, 62.73, 0.06135, 24, 76.45, -16.28, 0.18932, 5, 17, 315.13, -95.17, 8e-05, 19, 151.62, -66.97, 0.05845, 20, 71.68, -60.61, 0.60195, 25, 38.16, 61.89, 0.09175, 24, 42.45, -17.12, 0.24776, 6, 17, 238.21, -75.5, 0.01126, 18, 154.59, -69.63, 0.00821, 19, 72.52, -60.14, 0.49824, 20, -7.7, -61.25, 0.2927, 25, 117.54, 63.12, 0.04119, 24, 121.83, -15.9, 0.1484, 6, 17, 191.81, -62.63, 0.04962, 18, 107.65, -58.89, 0.07864, 19, 24.63, -55.03, 0.66067, 20, -55.86, -60.67, 0.11187, 25, 165.7, 62.89, 0.016, 24, 169.99, -16.12, 0.0832, 6, 17, 156.83, -51.87, 0.15612, 18, 72.22, -49.74, 0.23559, 19, -11.63, -50.14, 0.53414, 20, -92.42, -59.22, 0.02616, 25, 202.28, 61.7, 0.0064, 24, 206.56, -17.31, 0.0416, 5, 17, 116.66, -41.8, 0.33892, 18, 31.63, -41.52, 0.35739, 19, -52.91, -46.78, 0.29095, 20, -133.83, -59.76, 0.00315, 24, 247.97, -16.47, 0.0096, 4, 17, 79.65, -32.83, 0.63164, 18, -5.74, -34.24, 0.26904, 19, -90.88, -43.99, 0.09916, 20, -171.9, -60.54, 0.00017, 3, 17, 43.86, -25.92, 0.84817, 18, -41.82, -28.96, 0.12693, 19, -127.33, -43.02, 0.0249, 3, 17, 10.21, -20.68, 0.98505, 18, -75.67, -25.27, 0.01097, 19, -161.38, -43.36, 0.00398, 6, 18, 239.47, -6.05, 0.00219, 19, 149.27, 13.05, 0.04341, 20, 61.82, 18.83, 0.64274, 26, 30.72, 42.73, 0.09606, 25, 48.61, -17.48, 0.2044, 24, 52.9, -96.49, 0.0112, 6, 18, 210.33, -0.33, 0.02447, 19, 119.65, 15.27, 0.19963, 20, 32.13, 18.26, 0.52535, 26, 60.41, 43.52, 0.07326, 25, 78.3, -16.69, 0.16692, 24, 82.59, -95.7, 0.01036, 6, 17, 252.08, 1.7, 0.00026, 18, 164.93, 8.11, 0.1135, 19, 73.58, 18.28, 0.44593, 20, -14.03, 16.92, 0.27712, 26, 106.55, 45.2, 0.04479, 25, 124.44, -15.01, 0.1184, 6, 17, 211.76, 8.89, 0.00174, 18, 124.32, 13.45, 0.33293, 19, 32.62, 18.78, 0.49107, 20, -54.85, 13.56, 0.08274, 26, 147.35, 48.86, 0.0224, 25, 165.24, -11.35, 0.06912, 6, 17, 174.94, 16.26, 0.00567, 18, 87.2, 19.14, 0.6681, 19, -4.91, 20.03, 0.28057, 20, -92.33, 11.28, 0.00886, 26, 184.81, 51.42, 0.008, 25, 202.7, -8.79, 0.0288, 5, 17, 133.73, 22.64, 0.03845, 18, 45.75, 23.64, 0.86486, 19, -46.6, 19.59, 0.08863, 20, -133.8, 6.91, 6e-05, 25, 244.14, -4.12, 0.008, 3, 17, 97.08, 26, 0.22152, 18, 8.98, 25.33, 0.77557, 19, -83.31, 16.91, 0.00291, 5, 18, 238.43, 22.47, 0.00681, 19, 144.86, 41.25, 0.04411, 20, 54.77, 46.49, 0.62033, 26, 37.97, 15.12, 0.24216, 25, 55.86, -45.08, 0.0866, 5, 18, 207.42, 27.37, 0.03925, 19, 113.49, 42.44, 0.18537, 20, 23.43, 44.72, 0.51047, 26, 69.3, 17.12, 0.20551, 25, 87.19, -43.09, 0.0594, 5, 18, 169.24, 34.57, 0.15711, 19, 74.72, 45.06, 0.36455, 20, -15.41, 43.69, 0.28301, 26, 108.13, 18.44, 0.15608, 25, 126.02, -41.77, 0.03924, 6, 17, 220.51, 33.71, 9e-05, 18, 131.93, 38.65, 0.41861, 19, 37.19, 44.7, 0.37432, 20, -52.74, 39.8, 0.10106, 26, 145.43, 22.61, 0.09152, 25, 163.32, -37.6, 0.0144, 6, 17, 182.89, 39.74, 0.00068, 18, 94.08, 42.96, 0.73908, 19, -0.9, 44.49, 0.19657, 20, -90.64, 36.01, 0.02046, 26, 183.3, 26.67, 0.0352, 25, 201.2, -33.53, 0.008, 5, 17, 140.06, 43.86, 0.01596, 18, 51.11, 45.13, 0.93798, 19, -43.83, 41.56, 0.0367, 20, -133.11, 29.05, 0.00136, 26, 225.71, 33.95, 0.008, 3, 17, 103.43, 47.46, 0.12495, 18, 14.35, 47.06, 0.87238, 19, -80.56, 39.12, 0.00267, 2, 17, 65.05, 44.08, 0.41131, 18, -23.84, 41.93, 0.58869, 2, 17, 29.75, 38.65, 0.70571, 18, -58.85, 34.89, 0.29429, 3, 17, 56.81, 24.18, 0.51495, 18, -31.16, 21.67, 0.48479, 19, -122.74, 8.52, 0.00026, 2, 17, 22.44, 19.09, 0.80961, 18, -65.26, 15.02, 0.19039, 6, 18, 235.87, -45.84, 8e-05, 19, 150.41, -26.89, 0.04646, 20, 66.71, -20.82, 0.6367, 26, 25.53, 82.35, 0.01024, 25, 43.43, 22.14, 0.206, 24, 47.71, -56.87, 0.10052, 7, 17, 289.82, -47.59, 0.00025, 18, 204.88, -39.4, 0.00286, 19, 118.87, -24.17, 0.22143, 20, 35.05, -21.08, 0.51976, 26, 57.19, 82.84, 0.00927, 25, 75.08, 22.63, 0.17367, 24, 79.37, -56.38, 0.07276, 6, 17, 246.81, -34.59, 0.00287, 18, 161.31, -28.38, 0.0455, 19, 74.31, -18.38, 0.50665, 20, -9.85, -19.51, 0.28102, 25, 120, 21.39, 0.1216, 24, 124.29, -57.62, 0.04236, 6, 17, 201.98, -21.76, 0.01303, 18, 115.95, -17.61, 0.19994, 19, 27.99, -13.06, 0.60919, 20, -56.47, -18.57, 0.08632, 25, 166.62, 20.79, 0.06912, 24, 170.91, -58.22, 0.0224, 6, 17, 166.78, -12.99, 0.04739, 18, 80.38, -10.45, 0.45649, 19, -8.18, -10.16, 0.44414, 20, -92.74, -19.09, 0.01198, 25, 202.89, 21.58, 0.0304, 24, 207.18, -57.43, 0.0096, 5, 17, 126.8, -4.62, 0.17032, 18, 40.07, -3.91, 0.6413, 19, -48.98, -8.44, 0.18153, 20, -133.53, -21.21, 0.00045, 25, 243.66, 24.01, 0.0064, 3, 17, 90.71, 2.97, 0.37521, 18, 3.67, 2.03, 0.5726, 19, -85.83, -6.86, 0.05218, 3, 17, 53.27, 5.67, 0.7098, 18, -33.85, 3.02, 0.28454, 19, -123.2, -10.32, 0.00566, 3, 17, 18.55, 8.6, 0.89878, 18, -68.67, 4.37, 0.10069, 19, -157.93, -13.1, 0.00053], "hull": 31, "edges": [50, 48, 48, 46, 46, 44, 44, 42, 30, 32, 36, 38, 38, 40, 40, 42, 32, 34, 34, 36, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 28, 30, 64, 32, 28, 64, 64, 62, 66, 36, 62, 66, 68, 38, 66, 68, 70, 40, 68, 70, 72, 42, 70, 72, 8, 6, 6, 4, 0, 60, 74, 44, 72, 74, 76, 46, 74, 76, 78, 48, 76, 78, 50, 52, 78, 52, 24, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 20, 22, 22, 24, 18, 94, 94, 80, 22, 94, 16, 96, 96, 82, 94, 96, 14, 98, 98, 84, 96, 98, 12, 100, 100, 86, 98, 100, 10, 102, 102, 88, 100, 102, 8, 104, 104, 90, 102, 104, 6, 106, 106, 92, 104, 106, 4, 108, 106, 108, 0, 110, 108, 110, 58, 60, 110, 58, 112, 108, 92, 112, 114, 110, 112, 114, 56, 58, 114, 56, 24, 26, 26, 28, 64, 116, 116, 80, 26, 116, 62, 118, 118, 82, 116, 118, 66, 120, 120, 84, 118, 120, 68, 122, 122, 86, 120, 122, 70, 124, 124, 88, 122, 124, 72, 126, 126, 90, 124, 126, 74, 128, 128, 92, 126, 128, 76, 130, 130, 112, 128, 130, 78, 132, 132, 114, 130, 132, 52, 54, 54, 56, 132, 54, 62, 34, 0, 2, 2, 4], "width": 176, "height": 291}}, "CROWN": {"Crown": {"x": 56.25, "y": -2.92, "rotation": -90.6, "width": 83, "height": 120}}, "CrownEye": {"CrownEye": {"x": 1.01, "y": 1.26, "rotation": -90.6, "width": 35, "height": 41}}, "Face": {"Face": {"x": -2.41, "y": 15.33, "width": 119, "height": 47}, "Face2": {"x": -2.41, "y": 15.33, "width": 120, "height": 48}, "Face_Closed": {"x": -2.41, "y": 7.73, "width": 155, "height": 20}}, "HEAD": {"Head": {"type": "mesh", "uvs": [0.85337, 0.05197, 0.84217, 0.10939, 0.81317, 0.16869, 0.86664, 0.14917, 0.89704, 0.19098, 0.87389, 0.26419, 0.85096, 0.29974, 0.90174, 0.34449, 0.95413, 0.34182, 0.95572, 0.41021, 1, 0.51555, 1, 0.65836, 1, 0.74524, 0.945, 0.90153, 0.95566, 0.93727, 0.90014, 0.96866, 0.71806, 1, 0.56394, 1, 0.35643, 1, 0.23682, 1, 0.10769, 0.95121, 0.08879, 0.97291, 0.04264, 0.92111, 0.06159, 0.89148, 0, 0.7672, 0, 0.6733, 0, 0.52085, 0.07556, 0.41123, 0.17797, 0.3028, 0.1487, 0.23622, 0.1215, 0.1538, 0.19294, 0.10466, 0.22639, 0.13062, 0.22436, 0.06715, 0.22221, 0, 0.31116, 0, 0.3513, 0.09141, 0.38016, 0.15223, 0.40767, 0.22681, 0.5016, 0.21744, 0.61227, 0.21907, 0.64631, 0.14295, 0.7081, 0.05212, 0.7667, 0, 0.82993, 0, 0.87953, 0.94476, 0.4106, 0.34341, 0.62311, 0.33571, 0.25621, 0.38316, 0.15946, 0.54019, 0.15137, 0.74432, 0.1543, 0.92274, 0.2321, 0.97076, 0.61456, 0.96603, 0.70654, 0.95268, 0.81853, 0.89782, 0.7563, 0.37414, 0.81563, 0.54001, 0.82853, 0.70461, 0.22625, 0.7844, 0.3117, 0.70361, 0.43043, 0.67853, 0.5714, 0.73782, 0.61928, 0.83324, 0.2554, 0.1975, 0.28429, 0.26763, 0.754, 0.26697, 0.7869, 0.20781], "triangles": [0, 43, 44, 33, 34, 35, 63, 17, 18, 63, 18, 62, 65, 38, 46, 65, 37, 38, 65, 64, 37, 29, 32, 64, 32, 30, 31, 32, 29, 30, 48, 46, 61, 48, 28, 65, 18, 61, 62, 62, 61, 47, 61, 46, 47, 48, 65, 46, 28, 64, 65, 28, 29, 64, 16, 45, 15, 17, 53, 16, 53, 54, 16, 16, 55, 45, 16, 54, 55, 53, 17, 63, 15, 45, 14, 53, 63, 54, 54, 63, 55, 45, 13, 14, 45, 55, 13, 12, 13, 58, 13, 55, 58, 55, 63, 58, 63, 62, 58, 58, 11, 12, 62, 57, 58, 57, 47, 56, 57, 62, 47, 58, 57, 11, 57, 10, 11, 57, 9, 10, 57, 7, 9, 7, 56, 6, 7, 57, 56, 7, 8, 9, 47, 66, 56, 56, 66, 6, 47, 39, 40, 47, 46, 39, 46, 38, 39, 47, 40, 66, 5, 6, 67, 6, 66, 67, 67, 66, 41, 67, 2, 5, 5, 2, 4, 4, 2, 3, 66, 40, 41, 67, 41, 2, 64, 36, 37, 64, 32, 36, 41, 42, 2, 2, 42, 1, 0, 1, 43, 43, 1, 42, 32, 33, 36, 33, 35, 36, 60, 18, 59, 18, 60, 61, 19, 20, 52, 20, 51, 52, 21, 22, 20, 51, 59, 52, 18, 52, 59, 22, 23, 20, 20, 23, 51, 23, 50, 51, 51, 50, 59, 23, 24, 50, 59, 50, 60, 24, 25, 50, 50, 49, 60, 50, 25, 49, 61, 60, 48, 60, 49, 48, 25, 26, 49, 26, 27, 49, 49, 27, 48, 18, 19, 52, 27, 28, 48], "vertices": [2, 4, 319.49, -132.93, 0.456, 8, 83.11, -16.51, 0.544, 2, 4, 300.25, -128.35, 0.6, 8, 64.41, -22.94, 0.4, 2, 4, 280.62, -117.71, 0.6, 8, 42.13, -24.47, 0.4, 2, 4, 286.48, -136.09, 0.6, 8, 56.92, -36.86, 0.4, 2, 4, 271.94, -145.82, 0.6, 8, 49.85, -52.85, 0.4, 2, 4, 247.54, -136.97, 0.768, 8, 24.5, -58.45, 0.232, 1, 4, 235.84, -128.72, 1, 1, 4, 220.03, -145.3, 1, 1, 4, 220.21, -163.08, 1, 1, 4, 197.1, -162.68, 1, 1, 4, 160.91, -176.24, 1, 1, 4, 112.68, -174.28, 1, 1, 4, 83.34, -173.09, 1, 1, 4, 31.31, -152.31, 1, 1, 4, 19.1, -155.43, 1, 1, 4, 9.26, -136.2, 1, 1, 4, 1.18, -74.09, 1, 1, 4, 3.3, -21.89, 1, 1, 4, 6.16, 48.4, 1, 1, 4, 7.8, 88.91, 1, 1, 4, 26.06, 131.98, 1, 1, 4, 18.99, 138.68, 1, 1, 4, 37.12, 153.6, 1, 1, 4, 46.86, 146.78, 1, 1, 4, 89.68, 165.94, 1, 1, 4, 121.4, 164.65, 1, 1, 4, 172.88, 162.56, 1, 1, 4, 208.86, 135.46, 1, 1, 4, 244.07, 99.28, 1, 2, 4, 266.96, 108.28, 0.784, 9, 31.6, 34.79, 0.216, 2, 4, 295.17, 116.36, 0.664, 9, 59.67, 26.22, 0.336, 2, 4, 310.78, 91.49, 0.664, 9, 59.24, -3.14, 0.336, 2, 4, 301.56, 80.52, 0.664, 9, 45.52, -7.33, 0.336, 2, 4, 323.02, 80.34, 0.584, 9, 63.43, -19.16, 0.416, 2, 4, 345.73, 80.14, 0.584, 9, 82.38, -31.68, 0.416, 2, 4, 344.5, 50.01, 0.584, 9, 64.96, -56.29, 0.416, 2, 4, 313.08, 37.67, 0.664, 9, 31.88, -49.55, 0.336, 2, 4, 292.14, 28.73, 0.784, 9, 9.44, -45.65, 0.216, 1, 4, 266.57, 20.44, 1, 1, 4, 268.45, -11.51, 1, 1, 4, 266.37, -48.97, 1, 2, 4, 291.61, -61.55, 0.768, 8, 21.34, 28.85, 0.232, 2, 4, 321.44, -83.72, 0.6, 8, 58.41, 26.09, 0.4, 2, 4, 338.23, -104.29, 0.456, 8, 83.6, 17.72, 0.544, 2, 4, 337.36, -125.7, 0.456, 8, 94.33, -0.84, 0.544, 1, 4, 17.61, -129.54, 1, 2, 4, 227.26, 29.79, 0.752, 34, -0.53, 105.86, 0.248, 2, 4, 226.94, -42.29, 0.752, 34, 71.41, 110.55, 0.248, 2, 4, 215.97, 82.63, 0.752, 34, -52.46, 90.92, 0.248, 2, 4, 164.26, 117.56, 0.752, 34, -83.7, 36.91, 0.248, 2, 4, 95.44, 123.1, 0.752, 34, -84.44, -32.13, 0.248, 2, 4, 35.14, 124.56, 0.752, 34, -81.7, -92.39, 0.248, 1, 4, 17.74, 90.11, 1, 1, 4, 14.08, -39.5, 1, 2, 4, 17.43, -62.09, 0.752, 34, 105.72, -97.08, 0.248, 2, 4, 34.4, -101.62, 0.776, 34, 143.98, -77.41, 0.224, 2, 4, 212.13, -86.88, 0.752, 34, 116.91, 98.88, 0.248, 2, 4, 155.3, -104.7, 0.752, 34, 138.64, 43.42, 0.248, 2, 4, 99.53, -106.81, 0.752, 34, 144.63, -12.07, 0.248, 2, 4, 81.02, 110.43, 0.408, 34, -70.81, -45.46, 0.592, 2, 4, 107.12, 80.38, 0.408, 34, -42.65, -17.33, 0.592, 2, 4, 113.96, 39.82, 0.408, 34, -2.66, -7.69, 0.592, 2, 4, 92, -7.12, 0.408, 34, 45.69, -26.34, 0.592, 2, 4, 59.11, -22.03, 0.408, 34, 62.85, -58.11, 0.592, 2, 4, 278.57, 71.61, 0.784, 9, 21.39, -2.29, 0.216, 1, 4, 254.49, 62.79, 1, 1, 4, 248.25, -96.32, 1, 2, 4, 267.77, -108.28, 0.768, 8, 26.23, -23.38, 0.232], "hull": 45, "edges": [76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 74, 76, 70, 72, 72, 74, 68, 70, 68, 66, 66, 64, 64, 62, 62, 60, 60, 58, 58, 56, 56, 54, 54, 52, 12, 14, 14, 16, 16, 18, 18, 20, 50, 52, 20, 22, 22, 24, 24, 26, 32, 90, 90, 30, 30, 28, 28, 26, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 48, 50, 46, 48, 30, 32, 92, 94, 92, 96, 96, 98, 98, 100, 100, 102, 102, 104, 106, 108, 108, 110, 94, 112, 112, 114, 114, 116, 116, 110, 104, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 106, 58, 128, 128, 74, 64, 128, 56, 130, 130, 76, 128, 130, 12, 132, 132, 80, 132, 134, 134, 4], "width": 339, "height": 338}}, "Inner face": {"Inner face": {"type": "mesh", "uvs": [0.57803, 0.1635, 0.6725, 0.09168, 0.89574, 0.17152, 1, 0.40834, 1, 0.65223, 0.86713, 0.90401, 0.59846, 1, 0.48861, 0.99999, 0.3105, 0.99578, 0.07319, 0.84125, 0.00048, 0.66439, 1e-05, 0.39436, 0.08635, 0.19193, 0.27933, 0.12297, 0.41227, 0.17771, 0.429, 0, 0.5793, 0, 0.49582, 0.23235], "triangles": [0, 15, 16, 14, 15, 0, 17, 14, 0, 2, 0, 1, 14, 9, 10, 11, 12, 13, 10, 11, 14, 13, 14, 11, 17, 9, 14, 0, 2, 3, 4, 5, 3, 3, 17, 0, 5, 17, 3, 8, 9, 17, 17, 7, 8, 17, 6, 7, 5, 6, 17], "vertices": [1, 34, 5.11, 90.63, 1, 2, 34, 29.56, 105.17, 0.888, 36, 36.21, 66.01, 0.112, 2, 34, 81.67, 89.36, 0.784, 36, 16.86, 15.13, 0.216, 2, 34, 101.55, 41.88, 0.86162, 36, -32.04, -1.61, 0.13838, 2, 34, 99.21, -7, 0.928, 36, -80.56, 4.32, 0.072, 1, 34, 67.84, -57.47, 1, 1, 34, 9.54, -76.66, 1, 2, 34, -13.45, -76.63, 0.976, 35, -157.53, -58.95, 0.024, 1, 34, -52.95, -75.82, 1, 1, 34, -104.44, -44.91, 1, 2, 34, -116.66, -9.39, 0.89904, 35, -83.23, 39.33, 0.10096, 2, 34, -111.26, 44.85, 0.74289, 35, -29.42, 30.17, 0.25711, 2, 34, -89.46, 85.47, 0.656, 35, 9.63, 5.6, 0.344, 2, 34, -51.25, 99.11, 0.76, 35, 20.52, -33.47, 0.24, 2, 34, -26.66, 87.84, 0.888, 35, 7.5, -57.21, 0.112, 2, 34, -12, 123.99, 0.568, 10, -11.23, 52.21, 0.432, 2, 34, 20.61, 123.99, 0.568, 10, -12.68, 19.63, 0.432, 1, 34, -12.73, 76.87, 1], "hull": 17, "edges": [20, 22, 14, 16, 12, 14, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 20, 18, 18, 16, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 0, 0, 2, 14, 34], "width": 217, "height": 200}}, "Leaf": {"Leaf": {"x": 46.36, "y": -1.74, "rotation": 90, "width": 65, "height": 92}}, "MASK": {"MASK": {"type": "clipping", "end": "MASK", "vertexCount": 4, "vertices": [-501.26, -4.28, 670.61, -0.54, 799.84, 1215.37, -470.13, 1231.82], "color": "ce3a3aff"}}, "Nostrils": {"Nostrils": {"x": -7.16, "y": -0.82, "width": 30, "height": 9}}, "Scarf": {"Collar": {"type": "mesh", "uvs": [0.92757, 0.0876, 1, 0.204, 1, 0.37434, 0.96594, 0.45383, 1, 0.53616, 0.9808, 0.76471, 0.85476, 0.92715, 0.64422, 1, 0.50863, 0.99512, 0.33579, 1, 0.11846, 0.93488, 0.01274, 0.82282, 0, 0.54468, 0.03621, 0.45951, 0, 0.35163, 0, 0.19832, 0.09671, 0.07057, 0.33874, 1e-05, 0.52026, 0, 0.76524, 1e-05, 0.1587, 0.56739, 0.33431, 0.64404, 0.50993, 0.64972, 0.6457, 0.63553, 0.8405, 0.56739], "triangles": [6, 7, 23, 8, 22, 7, 10, 20, 9, 8, 9, 22, 6, 24, 5, 10, 11, 13, 11, 12, 13, 6, 23, 24, 7, 22, 23, 20, 21, 9, 22, 9, 21, 20, 10, 13, 24, 3, 5, 5, 3, 4, 21, 17, 22, 22, 18, 23, 22, 17, 18, 21, 20, 17, 23, 19, 24, 23, 18, 19, 24, 0, 3, 24, 19, 0, 13, 16, 20, 20, 16, 17, 16, 13, 15, 2, 3, 1, 3, 0, 1, 13, 14, 15], "vertices": [2, 4, 70.74, -99.25, 0.97858, 3, 273.09, -99.79, 0.02142, 2, 4, 56.44, -115.19, 0.87122, 3, 259.17, -116.07, 0.12878, 2, 4, 36.35, -114.48, 0.8702, 3, 239.08, -115.85, 0.1298, 2, 4, 27.25, -106.42, 0.64571, 3, 229.78, -108.01, 0.35429, 2, 4, 17.27, -113.81, 0.70513, 3, 219.98, -115.63, 0.29487, 2, 4, -11.88, -108.42, 0.28, 3, 191.2, -110.92, 0.72, 2, 4, -27.2, -79.22, 0.168, 3, 174.18, -82.14, 0.832, 2, 4, -34.58, -31.16, 0.144, 3, 166.17, -34.25, 0.856, 2, 4, -32.91, -0.42, 0.1878, 3, 167.09, -3.48, 0.8122, 2, 4, -32.1, 38.81, 0.13198, 3, 166.97, 35.76, 0.86802, 2, 4, -21.91, 87.85, 0.128, 3, 174.62, 84.98, 0.872, 2, 4, -7.41, 111.37, 0.312, 3, 187.54, 108.81, 0.688, 2, 4, 24.29, 113.09, 0.50105, 3, 221.56, 111.37, 0.49895, 2, 4, 34.05, 104.52, 0.66033, 3, 231.51, 103.04, 0.33967, 2, 4, 47.06, 112.28, 0.8132, 3, 244.33, 111.11, 0.1868, 2, 4, 65.14, 111.64, 0.83696, 3, 262.42, 110.9, 0.16304, 1, 4, 79.43, 89.17, 1, 2, 4, 85.8, 33.97, 0.9757, 3, 284.95, 33.75, 0.0243, 1, 4, 84.35, -7.21, 1, 2, 4, 82.38, -62.79, 0.99676, 3, 283.85, -63.06, 0.00324, 2, 4, 20.34, 77.18, 0.72408, 3, 218.47, 75.38, 0.27592, 2, 4, 9.89, 37.66, 0.66404, 3, 208.97, 35.62, 0.33596, 2, 4, 7.81, -2.16, 0.744, 3, 207.85, -4.24, 0.256, 2, 4, 8.39, -33.02, 0.68283, 3, 209.17, -35.07, 0.31717, 2, 4, 14.86, -77.49, 0.71973, 3, 216.71, -79.38, 0.28027], "hull": 20, "edges": [22, 20, 20, 18, 14, 12, 12, 10, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 38, 0, 0, 2, 2, 4, 6, 4, 8, 10, 6, 8, 26, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 6, 14, 16, 16, 18, 34, 36, 36, 38], "width": 240, "height": 67}}}}], "events": {"transform": {}}, "animations": {"enter": {"slots": {"BehindCloak": {"attachment": [{"name": "BehindCloak_Floating"}, {"time": 1, "name": "BehindCloak"}]}, "Face": {"attachment": [{"time": 0.0667, "name": "Face2"}, {"time": 0.4333, "name": "Face"}, {"time": 0.8333, "name": "Face2"}, {"time": 0.9667, "name": "Face_Closed"}, {"time": 1.4333, "name": "Face"}]}}, "bones": {"BodyBtm": {"scale": [{"x": 1.213, "y": 0.82, "curve": [0.384, 1.272, 0.933, 1.338, 0.384, 0.82, 0.933, 0.82]}, {"time": 0.9333, "x": 1.378, "y": 0.82, "curve": [0.941, 1.137, 1.058, 0.873, 0.941, 0.968, 1.058, 1.129]}, {"time": 1.1, "x": 0.873, "y": 1.129, "curve": [1.183, 0.873, 1.35, 1.112, 1.183, 1.129, 1.35, 0.924]}, {"time": 1.4333, "x": 1.112, "y": 0.924, "curve": [1.508, 1.112, 1.658, 1, 1.508, 0.924, 1.658, 1]}, {"time": 1.7333}]}, "Eyes": {"translate": [{"x": 61.42, "y": -2.15, "curve": "stepped"}, {"time": 0.6, "x": 61.42, "y": -2.15, "curve": [0.6, 58.23, 0.967, 51.86, 0.6, -1.97, 0.967, -1.61]}, {"time": 0.9667, "x": 48.68, "y": -1.43, "curve": [0.967, 23.97, 1.2, -25.44, 0.967, -0.3, 1.2, 1.96]}, {"time": 1.2, "x": -50.15, "y": 3.09, "curve": [1.342, -50.15, 1.625, 5.65, 1.342, 3.09, 1.625, -0.19]}, {"time": 1.7667, "x": 5.65, "y": -0.19}]}, "Antler1": {"rotate": [{"value": -2.78, "curve": [0.163, -11.96, 0.633, -24.98]}, {"time": 0.6333, "value": -32.11}, {"time": 1, "value": 46.78, "curve": [1.058, 46.78, 1.175, -63.81]}, {"time": 1.2333, "value": -63.81, "curve": [1.308, -63.81, 1.458, 43.66]}, {"time": 1.5333, "value": 43.66, "curve": [1.592, 43.66, 1.708, -12.38]}, {"time": 1.7667, "value": -12.38}]}, "Antler6": {"rotate": [{"value": 10.74, "curve": [0.163, 27.11, 0.633, 50.32]}, {"time": 0.6333, "value": 63.03, "curve": [0.633, 37.5, 1, -13.55]}, {"time": 1, "value": -39.08, "curve": [1.058, -39.08, 1.175, 71.25]}, {"time": 1.2333, "value": 71.25, "curve": [1.308, 71.25, 1.458, -27.8]}, {"time": 1.5333, "value": -27.8, "curve": [1.592, -27.8, 1.708, 17.93]}, {"time": 1.7667, "value": 17.93}]}, "Hands_1": {"rotate": [{}, {"time": 1.7667, "value": -5.24}]}, "Hands_0": {"rotate": [{}, {"time": 1.7667, "value": 1.63}]}, "NeckLeaf5": {"rotate": [{}, {"time": 1.7667, "value": 0.17}]}, "BodyTop": {"scale": [{}, {"time": 0.1667, "x": 0.92, "y": 1.085, "curve": [0.167, 0.962, 0.933, 1.048, 0.167, 1.037, 0.933, 0.942]}, {"time": 0.9333, "x": 1.09, "y": 0.894, "curve": [0.941, 1.001, 1.058, 0.903, 0.941, 0.981, 1.058, 1.076]}, {"time": 1.1, "x": 0.903, "y": 1.076, "curve": [1.183, 0.903, 1.35, 1.102, 1.183, 1.076, 1.35, 0.937]}, {"time": 1.4333, "x": 1.102, "y": 0.937, "curve": [1.508, 1.102, 1.658, 0.991, 1.508, 0.937, 1.658, 1.014]}, {"time": 1.7333, "x": 0.991, "y": 1.014}]}, "Head": {"translate": [{"x": -14.74, "y": 0.15}, {"time": 0.9667, "x": -57.49, "y": 0.58}, {"time": 1.7667}]}, "CrownEye": {"scale": [{"time": 0.3, "curve": [0.3, 0.954, 0.433, 0.861, 0.3, 1.024, 0.433, 1.073]}, {"time": 0.4333, "x": 0.815, "y": 1.097}, {"time": 0.5667, "curve": "stepped"}, {"time": 1.2, "curve": [1.2, 0.954, 1.333, 0.861, 1.2, 1.024, 1.333, 1.073]}, {"time": 1.3333, "x": 0.815, "y": 1.097}, {"time": 1.4333}]}, "MAIN": {"translate": [{"y": 1510.77, "curve": [0.242, 0, 0.928, 0, 0.242, 1510.77, 0.928, 656.68]}, {"time": 0.9667}]}, "Cloak_Right1": {"rotate": [{"value": -0.37, "curve": [0, 0.1, 0.867, 1.04]}, {"time": 0.8667, "value": 1.51, "curve": [0.89, 0.94, 1.017, 0]}, {"time": 1.0667}]}, "Cloak_Right2": {"rotate": [{"value": 6.91, "curve": [0.075, 9.63, 0.133, 12.18]}, {"time": 0.1333, "value": 14.04, "curve": [0.133, 21.98, 0.867, 37.87]}, {"time": 0.8667, "value": 45.81, "curve": [0.89, 28.46, 1.017, 0]}, {"time": 1.0667}]}, "Cloak_Right3": {"rotate": [{"value": 4.32, "curve": [0.067, 12.18, 0.133, 20.03]}, {"time": 0.1333, "value": 25.27, "curve": [0.133, 27.17, 0.767, 30.98]}, {"time": 0.7667, "value": 32.88}, {"time": 1, "value": -5.28, "curve": [1, -3.96, 1.267, -3.7]}, {"time": 1.2667}]}, "Cloak_Right4": {"rotate": [{"value": 17.5, "curve": [0, 21.23, 0.867, 28.69]}, {"time": 0.8667, "value": 32.43, "curve": [0.89, 20.15, 1.017, 0]}, {"time": 1.0667}]}, "Cloak_Left1": {"rotate": [{"value": 3.09, "curve": [0, -0.05, 0.3, -6.34]}, {"time": 0.3, "value": -9.48, "curve": [0.3, -6.33, 0.867, -0.02]}, {"time": 0.8667, "value": 3.13, "curve": [0.89, 1.94, 1.017, 0]}, {"time": 1.0667}]}, "Cloak_Left2": {"rotate": [{"value": -8.66, "curve": [0.258, -21.09, 0.867, -37.75]}, {"time": 0.8667, "value": -47.07, "curve": [0.89, -29.25, 1.017, 0]}, {"time": 1.0667}]}, "Cloak_Left3": {"rotate": [{"value": -3.71, "curve": [0.277, -15.31, 0.867, -30.36]}, {"time": 0.8667, "value": -38.9, "curve": [0.89, -24.17, 1.017, 0]}, {"time": 1.0667}]}, "Cloak_Left4": {"rotate": [{"value": -21.19, "curve": [0.182, -23.42, 0.867, -26.8]}, {"time": 0.8667, "value": -28.61, "curve": [0.89, -17.78, 1.017, 0]}, {"time": 1.0667}]}, "CloakWave6": {"translate": [{"x": 75.9, "y": -0.65, "curve": [0.1, 53.95, 0.6, 18.6, 0.1, -0.46, 0.6, -0.16]}, {"time": 0.6}]}, "CloakWave5": {"translate": [{"x": 50.11, "y": -0.43, "curve": [0.14, 31.67, 0.3, 12.36, 0.14, -0.27, 0.3, -0.11]}, {"time": 0.3, "curve": [0.3, -21.77, 0.667, -65.31, 0.3, 2.54, 0.667, 7.62]}, {"time": 0.6667, "x": -87.08, "y": 10.16, "curve": [0.667, -65.31, 1.033, -21.77, 0.667, 7.62, 1.033, 2.54]}, {"time": 1.0333}]}, "CloakWave4": {"translate": [{"x": 23.14, "y": -0.2, "curve": [0.062, 14.5, 0.1, 6.63, 0.062, -0.12, 0.1, -0.06]}, {"time": 0.1, "curve": [0.1, -32.69, 0.667, -98.08, 0.1, -0.45, 0.667, -1.35]}, {"time": 0.6667, "x": -130.77, "y": -1.8}, {"time": 0.9333}]}, "CloakWave3": {"translate": [{"x": 40.53, "y": -16.31, "curve": [0.07, 31.96, 0.144, 20.1, 0.07, -12.86, 0.144, -8.09]}, {"time": 0.2}, {"time": 0.6667, "x": -130.78, "y": -1.8}, {"time": 0.9333}]}, "CloakWave1": {"translate": [{"x": -11.67, "y": -9.57, "curve": [0.105, 21.99, 0.333, 65.93, 0.105, -17.61, 0.333, -28.1]}, {"time": 0.3333, "x": 90.76, "y": -34.03}, {"time": 0.7667, "x": -0.64, "y": -79.24, "curve": [0.767, -0.48, 1.033, -0.16, 0.767, -59.43, 1.033, -19.81]}, {"time": 1.0333}]}, "CloakWave2": {"translate": [{"x": 7.6, "y": -16.81, "curve": [0.089, 37.75, 0.2, 70.35, 0.089, -25.31, 0.2, -34.5]}, {"time": 0.2, "x": 90.73, "y": -40.25, "curve": [0.2, 68.05, 0.567, 22.68, 0.2, -30.18, 0.567, -10.06]}, {"time": 0.5667, "curve": [0.567, -21.96, 0.7, -65.88, 0.567, -20.44, 0.7, -61.32]}, {"time": 0.7, "x": -87.84, "y": -81.76, "curve": [0.7, -65.88, 1.033, -21.96, 0.7, -61.32, 1.033, -20.44]}, {"time": 1.0333}]}, "CrownHolder": {"rotate": [{"time": 1.7667, "value": 90.86}], "translate": [{"x": 2.25, "y": -39.01, "curve": [0, 2.3, 0.767, 2.39, 0, -27.73, 0.767, -5.16]}, {"time": 0.7667, "x": 2.44, "y": 6.13, "curve": [0.767, 2.39, 1.5, 2.3, 0.767, -5.16, 1.5, -27.73]}, {"time": 1.5, "x": 2.25, "y": -39.01, "curve": [1.5, 2.79, 1.767, 3.85, 1.5, -42.1, 1.767, -48.27]}, {"time": 1.7667, "x": 4.38, "y": -51.36}]}, "Beak": {"rotate": [{}, {"time": 0.2667, "value": -25.8, "curve": [0.267, -26.67, 0.967, -28.41]}, {"time": 0.9667, "value": -29.28, "curve": [1.025, -29.28, 1.142, 14.44]}, {"time": 1.2, "value": 14.44, "curve": [1.342, 14.44, 1.625, 0]}, {"time": 1.7667}]}, "Beak2": {"rotate": [{}, {"time": 0.2667, "value": -25.8, "curve": [0.267, -26.19, 0.967, -26.98]}, {"time": 0.9667, "value": -27.38, "curve": [1.025, -27.38, 1.142, 15.97]}, {"time": 1.2, "value": 15.97, "curve": [1.342, 15.97, 1.625, 1.45]}, {"time": 1.7667, "value": 1.45}]}}}, "enter-old": {"slots": {"Face": {"attachment": [{"time": 0.3333, "name": "Face2"}, {"time": 0.6667, "name": "Face"}, {"time": 1, "name": "Face2"}, {"time": 1.3333, "name": "Face"}, {"time": 1.6667, "name": "Face2"}, {"time": 2, "name": "Face"}, {"time": 2.3333, "name": "Face2"}]}, "MASK": {"attachment": [{"name": "MASK"}]}}, "bones": {"Eyes": {"translate": [{"x": -63.5, "y": -0.19, "curve": [0.342, -63.5, 1.025, -75.03, 0.342, -0.19, 1.025, 4.97]}, {"time": 1.3667, "x": -75.03, "y": 4.97, "curve": [1.567, -75.03, 1.967, 16.5, 1.567, 4.97, 1.967, -1.64]}, {"time": 2.1667, "x": 16.5, "y": -1.64, "curve": [2.292, 16.5, 2.542, 5.65, 2.292, -1.64, 2.542, -0.19]}, {"time": 2.6667, "x": 5.65, "y": -0.19}]}, "Head": {"translate": [{"curve": [0.308, 0, 0.925, -32.85, 0.308, 0, 0.925, 0.37]}, {"time": 1.2333, "x": -32.85, "y": 0.37, "curve": [1.342, -32.85, 1.558, 0, 1.342, 0.37, 1.558, 0]}, {"time": 1.6667}]}, "Antler1": {"rotate": [{"curve": [0.39, 0, 0.927, -5.34]}, {"time": 1.5, "value": -12.01, "curve": [1.625, -12.01, 1.875, 8.86]}, {"time": 2, "value": 8.86, "curve": [2.167, 8.86, 2.5, 0]}, {"time": 2.6667}]}, "Antler6": {"rotate": [{"curve": [0.375, 0, 1.125, 12.19]}, {"time": 1.5, "value": 12.19, "curve": [1.625, 12.19, 1.875, -5.82]}, {"time": 2, "value": -5.82, "curve": [2.167, -5.82, 2.5, 0]}, {"time": 2.6667}]}, "Hands_1": {"rotate": [{"value": -5.24, "curve": [0.581, -20.5, 1.119, -29.14]}, {"time": 1.6, "value": -29.14, "curve": [1.654, -2.34, 1.95, 2.92]}, {"time": 2.0667, "value": 2.92, "curve": [2.217, 2.92, 2.517, -5.24]}, {"time": 2.6667, "value": -5.24}], "translate": [{"time": 1.6, "curve": [1.654, 3.5, 1.95, 4.19, 1.654, 0.27, 1.95, 0.32]}, {"time": 2.0667, "x": 4.19, "y": 0.32, "curve": [2.217, 4.19, 2.517, 0, 2.217, 0.32, 2.517, 0]}, {"time": 2.6667}]}, "Hands_0": {"rotate": [{"value": 1.63, "curve": [0.612, 15.69, 1.18, 26.57]}, {"time": 1.6, "value": 26.57, "curve": [1.654, -0.27, 1.95, -5.53]}, {"time": 2.0667, "value": -5.53, "curve": [2.217, -5.53, 2.517, 1.63]}, {"time": 2.6667, "value": 1.63}], "translate": [{"time": 1.6, "curve": [1.654, 1.62, 1.95, 1.94, 1.654, -0.28, 1.95, -0.34]}, {"time": 2.0667, "x": 1.94, "y": -0.34, "curve": [2.217, 1.94, 2.517, 0, 2.217, -0.34, 2.517, 0]}, {"time": 2.6667}]}, "NeckLeaf5": {"rotate": [{"value": 0.17, "curve": [0.191, 1.02, 0.527, 8.75]}, {"time": 0.7, "value": 8.75, "curve": [0.85, 8.75, 1.15, 0]}, {"time": 1.3, "curve": [1.642, 0, 2.325, 0.17]}, {"time": 2.6667, "value": 0.17}]}, "CrownEye": {"scale": [{"curve": [0.017, 1, 0.05, 0.815, 0.017, 1, 0.05, 1.097]}, {"time": 0.0667, "x": 0.815, "y": 1.097, "curve": [0.092, 0.815, 0.142, 1, 0.092, 1.097, 0.142, 1]}, {"time": 0.1667, "curve": "stepped"}, {"time": 1.8667, "curve": [1.892, 1, 1.942, 0.815, 1.892, 1, 1.942, 1.097]}, {"time": 1.9667, "x": 0.815, "y": 1.097, "curve": [1.992, 0.815, 2.042, 1, 1.992, 1.097, 2.042, 1]}, {"time": 2.0667}]}, "BodyBtm": {"scale": [{"curve": [0.417, 1, 1.25, 0.967, 0.417, 1, 1.25, 1.049]}, {"time": 1.6667, "x": 0.967, "y": 1.049, "curve": [1.738, 1.005, 2.042, 1.015, 1.738, 1.001, 2.042, 0.989]}, {"time": 2.1667, "x": 1.015, "y": 0.989, "curve": [2.292, 1.015, 2.542, 1, 2.292, 0.989, 2.542, 1]}, {"time": 2.6667}]}, "BodyTop": {"scale": [{"x": 0.991, "y": 1.014, "curve": [0.166, 0.996, 0.319, 1, 0.166, 1.006, 0.319, 1]}, {"time": 0.4333, "curve": [0.742, 1, 1.358, 0.967, 0.742, 1, 1.358, 1.049]}, {"time": 1.6667, "x": 0.967, "y": 1.049, "curve": [1.738, 1.005, 2.042, 1.015, 1.738, 1.001, 2.042, 0.989]}, {"time": 2.1667, "x": 1.015, "y": 0.989, "curve": [2.292, 1.015, 2.542, 0.991, 2.292, 0.989, 2.542, 1.014]}, {"time": 2.6667, "x": 0.991, "y": 1.014}]}, "MAIN": {"translate": [{"y": -822.7}, {"time": 1.6667}], "scale": [{"time": 1.6667, "curve": [1.696, 1.055, 1.942, 1.079, 1.696, 0.935, 1.942, 0.907]}, {"time": 2.0333, "x": 1.079, "y": 0.907, "curve": [2.192, 1.079, 2.508, 1, 2.192, 0.907, 2.508, 1]}, {"time": 2.6667}]}}, "attachments": {"default": {"MASK": {"MASK": {"deform": [{"time": 1.3333}, {"time": 1.6667, "offset": 1, "vertices": [-33.17596, 0, -33.17596]}]}}}}}, "enter-oldd": {"slots": {"Face": {"attachment": [{"time": 0.3333, "name": "Face2"}, {"time": 0.6667, "name": "Face"}, {"time": 1, "name": "Face2"}, {"time": 1.3333, "name": "Face"}, {"time": 1.6667, "name": "Face2"}, {"time": 2, "name": "Face"}, {"time": 2.3333, "name": "Face2"}]}, "MASK": {"attachment": [{"name": "MASK"}]}}, "bones": {"Eyes": {"translate": [{"x": -63.5, "y": -0.19, "curve": [0.342, -63.5, 1.025, -75.03, 0.342, -0.19, 1.025, 4.97]}, {"time": 1.3667, "x": -75.03, "y": 4.97, "curve": [1.567, -75.03, 1.967, 16.5, 1.567, 4.97, 1.967, -1.64]}, {"time": 2.1667, "x": 16.5, "y": -1.64, "curve": [2.292, 16.5, 2.542, 5.65, 2.292, -1.64, 2.542, -0.19]}, {"time": 2.6667, "x": 5.65, "y": -0.19}]}, "Head": {"translate": [{"curve": [0.308, 0, 0.925, -32.85, 0.308, 0, 0.925, 0.37]}, {"time": 1.2333, "x": -32.85, "y": 0.37, "curve": [1.342, -32.85, 1.558, 0, 1.342, 0.37, 1.558, 0]}, {"time": 1.6667}]}, "Antler1": {"rotate": [{"curve": [0.39, 0, 0.927, -5.34]}, {"time": 1.5, "value": -12.01, "curve": [1.625, -12.01, 1.875, 8.86]}, {"time": 2, "value": 8.86, "curve": [2.167, 8.86, 2.5, 0]}, {"time": 2.6667}]}, "Antler6": {"rotate": [{"curve": [0.375, 0, 1.125, 12.19]}, {"time": 1.5, "value": 12.19, "curve": [1.625, 12.19, 1.875, -5.82]}, {"time": 2, "value": -5.82, "curve": [2.167, -5.82, 2.5, 0]}, {"time": 2.6667}]}, "Hands_1": {"rotate": [{"value": -5.24, "curve": [0.581, -20.5, 1.119, -29.14]}, {"time": 1.6, "value": -29.14, "curve": [1.654, -2.34, 1.95, 2.92]}, {"time": 2.0667, "value": 2.92, "curve": [2.217, 2.92, 2.517, -5.24]}, {"time": 2.6667, "value": -5.24}], "translate": [{"time": 1.6, "curve": [1.654, 3.5, 1.95, 4.19, 1.654, 0.27, 1.95, 0.32]}, {"time": 2.0667, "x": 4.19, "y": 0.32, "curve": [2.217, 4.19, 2.517, 0, 2.217, 0.32, 2.517, 0]}, {"time": 2.6667}]}, "Hands_0": {"rotate": [{"value": 1.63, "curve": [0.612, 15.69, 1.18, 26.57]}, {"time": 1.6, "value": 26.57, "curve": [1.654, -0.27, 1.95, -5.53]}, {"time": 2.0667, "value": -5.53, "curve": [2.217, -5.53, 2.517, 1.63]}, {"time": 2.6667, "value": 1.63}], "translate": [{"time": 1.6, "curve": [1.654, 1.62, 1.95, 1.94, 1.654, -0.28, 1.95, -0.34]}, {"time": 2.0667, "x": 1.94, "y": -0.34, "curve": [2.217, 1.94, 2.517, 0, 2.217, -0.34, 2.517, 0]}, {"time": 2.6667}]}, "NeckLeaf5": {"rotate": [{"value": 0.17, "curve": [0.191, 1.02, 0.527, 8.75]}, {"time": 0.7, "value": 8.75, "curve": [0.85, 8.75, 1.15, 0]}, {"time": 1.3, "curve": [1.642, 0, 2.325, 0.17]}, {"time": 2.6667, "value": 0.17}]}, "CrownEye": {"scale": [{"curve": [0.017, 1, 0.05, 0.815, 0.017, 1, 0.05, 1.097]}, {"time": 0.0667, "x": 0.815, "y": 1.097, "curve": [0.092, 0.815, 0.142, 1, 0.092, 1.097, 0.142, 1]}, {"time": 0.1667, "curve": "stepped"}, {"time": 1.8667, "curve": [1.892, 1, 1.942, 0.815, 1.892, 1, 1.942, 1.097]}, {"time": 1.9667, "x": 0.815, "y": 1.097, "curve": [1.992, 0.815, 2.042, 1, 1.992, 1.097, 2.042, 1]}, {"time": 2.0667}]}, "BodyBtm": {"scale": [{"curve": [0.417, 1, 1.25, 0.967, 0.417, 1, 1.25, 1.049]}, {"time": 1.6667, "x": 0.967, "y": 1.049, "curve": [1.738, 1.005, 2.042, 1.015, 1.738, 1.001, 2.042, 0.989]}, {"time": 2.1667, "x": 1.015, "y": 0.989, "curve": [2.292, 1.015, 2.542, 1, 2.292, 0.989, 2.542, 1]}, {"time": 2.6667}]}, "BodyTop": {"scale": [{"x": 0.991, "y": 1.014, "curve": [0.166, 0.996, 0.319, 1, 0.166, 1.006, 0.319, 1]}, {"time": 0.4333, "curve": [0.742, 1, 1.358, 0.967, 0.742, 1, 1.358, 1.049]}, {"time": 1.6667, "x": 0.967, "y": 1.049, "curve": [1.738, 1.005, 2.042, 1.015, 1.738, 1.001, 2.042, 0.989]}, {"time": 2.1667, "x": 1.015, "y": 0.989, "curve": [2.292, 1.015, 2.542, 0.991, 2.292, 0.989, 2.542, 1.014]}, {"time": 2.6667, "x": 0.991, "y": 1.014}]}, "MAIN": {"translate": [{"y": -822.7}, {"time": 1.6667}], "scale": [{"time": 1.6667, "curve": [1.696, 1.055, 1.942, 1.079, 1.696, 0.935, 1.942, 0.907]}, {"time": 2.0333, "x": 1.079, "y": 0.907, "curve": [2.192, 1.079, 2.508, 1, 2.192, 0.907, 2.508, 1]}, {"time": 2.6667}]}}, "attachments": {"default": {"MASK": {"MASK": {"deform": [{"time": 1.3333}, {"time": 1.6667, "offset": 1, "vertices": [-33.17596, 0, -33.17596]}]}}}}}, "exit": {"slots": {"BehindCloak": {"attachment": [{"time": 0.9667, "name": "BehindCloak_Floating"}]}, "Face": {"attachment": [{"time": 0.3333, "name": "Face_Closed"}, {"time": 0.8333, "name": "Face2"}, {"time": 1.1667, "name": "Face"}, {"time": 1.5, "name": "Face2"}, {"time": 1.8333, "name": "Face"}, {"time": 2.1667, "name": "Face2"}, {"time": 2.5, "name": "Face"}, {"time": 2.8333, "name": "Face2"}]}}, "bones": {"BodyBtm": {"scale": [{"curve": [0.175, 1, 0.079, 0.873, 0.175, 1, 0.079, 1.129]}, {"time": 0.7, "x": 0.873, "y": 1.129, "curve": [0.783, 0.873, 0.95, 1.378, 0.783, 1.129, 0.95, 0.82]}, {"time": 1.0333, "x": 1.378, "y": 0.82, "curve": [1.275, 1.378, 1.758, 1.11, 1.275, 0.82, 1.758, 0.82]}, {"time": 2, "x": 1.11, "y": 0.82}]}, "Eyes": {"translate": [{"x": 5.65, "y": -0.19, "curve": [0.076, 10.77, 0.147, 14.55, 0.076, -0.37, 0.147, -0.5]}, {"time": 0.2, "x": 14.55, "y": -0.5, "curve": [0.325, 14.55, 0.401, -61.39, 0.325, -0.5, 0.401, 2.95]}, {"time": 0.7, "x": -61.39, "y": 2.95, "curve": [0.783, -61.39, 0.95, 61.42, 0.783, 2.95, 0.95, -2.15]}, {"time": 1.0333, "x": 61.42, "y": -2.15, "curve": "stepped"}, {"time": 3.7333, "x": 61.42, "y": -2.15}, {"time": 3.8, "x": 14.2, "y": -55.74}]}, "Antler1": {"rotate": [{"curve": [0.167, 0, 0.5, 46.78]}, {"time": 0.6667, "value": 46.78, "curve": [0.75, 46.78, 0.917, -32.11]}, {"time": 1, "value": -32.11, "curve": [1.167, -32.11, 1.5, 6.49]}, {"time": 1.6667, "value": 6.49, "curve": [1.833, 6.49, 2.167, 0]}, {"time": 2.3333, "curve": [2.5, 0, 2.833, 6.49]}, {"time": 3, "value": 6.49, "curve": [3.133, 6.49, 3.4, 0]}, {"time": 3.5333}]}, "Antler6": {"rotate": [{"curve": [0.167, 0, 0.5, -39.08]}, {"time": 0.6667, "value": -39.08, "curve": [0.75, -39.08, 0.917, 63.03]}, {"time": 1, "value": 63.03, "curve": [1.167, 63.03, 1.5, -5.79]}, {"time": 1.6667, "value": -5.79, "curve": [1.833, -5.79, 2.167, 0]}, {"time": 2.3333, "curve": [2.5, 0, 2.833, -5.79]}, {"time": 3, "value": -5.79, "curve": [3.133, -5.79, 3.4, 0]}, {"time": 3.5333}]}, "Hands_1": {"rotate": [{"value": -5.24}]}, "Hands_0": {"rotate": [{"value": 1.63}]}, "NeckLeaf5": {"rotate": [{"value": 0.17}]}, "BodyTop": {"scale": [{"x": 0.991, "y": 1.014, "curve": [0.175, 0.991, 0.079, 0.903, 0.175, 1.014, 0.079, 1.076]}, {"time": 0.7, "x": 0.903, "y": 1.076, "curve": [0.783, 0.903, 0.95, 1.09, 0.783, 1.076, 0.95, 0.894]}, {"time": 1.0333, "x": 1.09, "y": 0.894, "curve": [1.125, 1.09, 1.308, 0.92, 1.125, 0.894, 1.308, 1.085]}, {"time": 1.4, "x": 0.92, "y": 1.085, "curve": [1.592, 0.92, 1.975, 1, 1.592, 1.085, 1.975, 0.949]}, {"time": 2.1667, "y": 0.949}]}, "Head": {"rotate": [{"time": 3.4333, "curve": [3.483, 0, 3.583, -25.28]}, {"time": 3.6333, "value": -25.28}], "translate": [{"curve": [0.175, 0, 0.072, -56.34, 0.175, 0, 0.072, 0.57]}, {"time": 0.7, "x": -57.49, "y": 0.58, "curve": [0.983, -57.49, 1.55, 0, 0.983, 0.58, 1.55, 0]}, {"time": 1.8333}], "scale": [{"time": 3.5333, "curve": [3.558, 1, 3.608, 1.183, 3.558, 1, 3.608, 0.994]}, {"time": 3.6333, "x": 1.183, "y": 0.994, "curve": [3.667, 1.183, 3.733, 0.944, 3.667, 0.994, 3.733, 1.099]}, {"time": 3.7667, "x": 0.944, "y": 1.099}]}, "CrownEye": {"scale": [{"time": 0.3, "curve": [0.317, 1, 0.35, 0.815, 0.317, 1, 0.35, 1.097]}, {"time": 0.3667, "x": 0.815, "y": 1.097, "curve": [0.4, 0.815, 0.467, 1, 0.4, 1.097, 0.467, 1]}, {"time": 0.5, "curve": "stepped"}, {"time": 1.0667, "curve": [1.092, 1, 1.142, 0.815, 1.092, 1, 1.142, 1.097]}, {"time": 1.1667, "x": 0.815, "y": 1.097, "curve": [1.2, 0.815, 1.267, 1, 1.2, 1.097, 1.267, 1]}, {"time": 1.3}]}, "MAIN": {"translate": [{"time": 0.7, "curve": [1.333, 0, 2.339, 0, 1.333, 0, 2.339, 2601.57]}, {"time": 3.2333, "y": 2601.57}]}, "Cloak_Right1": {"rotate": [{"curve": [0.167, 0, 0.141, 11.89]}, {"time": 0.6667, "value": 11.89, "curve": [0.717, 11.89, 0.709, 1.51]}, {"time": 0.8667, "value": 1.51, "curve": [0.975, 1.51, 1.192, 9.05]}, {"time": 1.3, "value": 9.05, "curve": [1.367, 9.05, 1.5, -0.37]}, {"time": 1.5667, "value": -0.37, "curve": [1.642, -0.37, 1.792, 9.05]}, {"time": 1.8667, "value": 9.05, "curve": [1.95, 9.05, 2.117, -0.37]}, {"time": 2.2, "value": -0.37, "curve": [2.283, -0.37, 2.45, 9.05]}, {"time": 2.5333, "value": 9.05, "curve": [2.625, 9.05, 2.808, -0.37]}, {"time": 2.9, "value": -0.37, "curve": [2.992, -0.37, 3.175, 9.05]}, {"time": 3.2667, "value": 9.05, "curve": [3.358, 9.05, 3.542, -0.37]}, {"time": 3.6333, "value": -0.37, "curve": [3.717, -0.37, 3.883, 9.05]}, {"time": 3.9667, "value": 9.05, "curve": [4.067, 9.05, 4.267, -0.37]}, {"time": 4.3667, "value": -0.37}], "scale": [{"curve": [0.167, 1, 0.141, 1.119, 0.167, 1, 0.141, 1]}, {"time": 0.6667, "x": 1.119, "curve": [0.717, 1.119, 0.709, 1, 0.717, 1, 0.709, 1]}, {"time": 0.8667}]}, "Cloak_Right2": {"rotate": [{"curve": [0.167, 0, 0.196, -6.12]}, {"time": 0.6667, "value": -6.12, "curve": [0.717, -6.12, 0.726, -12.16]}, {"time": 0.8667, "value": -12.16, "curve": [1.008, -12.16, 1.292, 14.04]}, {"time": 1.4333, "value": 14.04, "curve": [1.517, 14.04, 1.683, -3.95]}, {"time": 1.7667, "value": -3.95, "curve": [1.842, -3.95, 1.992, 5.47]}, {"time": 2.0667, "value": 5.47, "curve": [2.142, 5.47, 2.292, -3.95]}, {"time": 2.3667, "value": -3.95, "curve": [2.45, -3.95, 2.617, 17.47]}, {"time": 2.7, "value": 17.47, "curve": [2.792, 17.47, 2.975, -3.95]}, {"time": 3.0667, "value": -3.95, "curve": [3.15, -3.95, 3.317, 5.47]}, {"time": 3.4, "value": 5.47, "curve": [3.492, 5.47, 3.675, -3.95]}, {"time": 3.7667, "value": -3.95, "curve": [3.867, -3.95, 4.067, 5.47]}, {"time": 4.1667, "value": 5.47, "curve": [4.25, 5.47, 4.417, -3.95]}, {"time": 4.5, "value": -3.95}]}, "Cloak_Right3": {"rotate": [{"curve": [0.167, 0, 0.196, -5.28]}, {"time": 0.6667, "value": -5.28, "curve": [0.742, -5.28, 0.756, 7.88]}, {"time": 0.9667, "value": 7.88, "curve": [1.008, 7.88, 1.092, 3.53]}, {"time": 1.1333, "value": 3.53}, {"time": 1.4333, "value": 25.27, "curve": [1.492, 25.27, 1.608, -16.63]}, {"time": 1.6667, "value": -16.63, "curve": [1.725, -16.63, 1.842, 14.6]}, {"time": 1.9, "value": 14.6, "curve": [1.958, 14.6, 2.075, -16.63]}, {"time": 2.1333, "value": -16.63, "curve": [2.2, -16.63, 2.333, 24.05]}, {"time": 2.4, "value": 24.05, "curve": [2.467, 24.05, 2.6, -16.63]}, {"time": 2.6667, "value": -16.63, "curve": [2.733, -16.63, 2.867, 14.6]}, {"time": 2.9333, "value": 14.6, "curve": [3, 14.6, 3.133, -16.63]}, {"time": 3.2, "value": -16.63, "curve": [3.267, -16.63, 3.4, 14.6]}, {"time": 3.4667, "value": 14.6, "curve": [3.533, 14.6, 3.667, -16.63]}, {"time": 3.7333, "value": -16.63}]}, "Cloak_Right4": {"rotate": [{"curve": [0.167, 0, 0.196, 1.16]}, {"time": 0.6667, "value": 1.16, "curve": [0.717, 1.16, 0.726, 10.19]}, {"time": 0.8667, "value": 10.19, "curve": [0.942, 10.19, 1.092, -10.15]}, {"time": 1.1667, "value": -10.15}, {"time": 1.5667, "value": 17.5, "curve": [1.642, 17.5, 1.792, -14.64]}, {"time": 1.8667, "value": -14.64, "curve": [1.95, -14.64, 2.117, 17.5]}, {"time": 2.2, "value": 17.5, "curve": [2.219, 17.5, 2.242, 16.42]}, {"time": 2.2667, "value": 14.69, "curve": [2.352, 6.31, 2.469, -14.64]}, {"time": 2.5333, "value": -14.64, "curve": [2.625, -14.64, 2.808, 48.71]}, {"time": 2.9, "value": 48.71, "curve": [2.992, 48.71, 3.175, -14.64]}, {"time": 3.2667, "value": -14.64, "curve": [3.358, -14.64, 3.542, 17.5]}, {"time": 3.6333, "value": 17.5, "curve": [3.717, 17.5, 3.883, -14.64]}, {"time": 3.9667, "value": -14.64, "curve": [4.067, -14.64, 4.267, 17.5]}, {"time": 4.3667, "value": 17.5}]}, "Cloak_Left1": {"rotate": [{"curve": [0.167, 0, 0.141, -7.62]}, {"time": 0.6667, "value": -7.62, "curve": [0.717, -7.62, 0.709, 3.13]}, {"time": 0.8667, "value": 3.13, "curve": [0.975, 3.13, 1.192, -9.48]}, {"time": 1.3, "value": -9.48, "curve": [1.367, -9.48, 1.5, 3.09]}, {"time": 1.5667, "value": 3.09, "curve": [1.642, 3.09, 1.792, -9.48]}, {"time": 1.8667, "value": -9.48, "curve": [1.95, -9.48, 2.117, 3.09]}, {"time": 2.2, "value": 3.09, "curve": [2.283, 3.09, 2.45, -9.48]}, {"time": 2.5333, "value": -9.48, "curve": [2.625, -9.48, 2.808, 3.09]}, {"time": 2.9, "value": 3.09, "curve": [2.992, 3.09, 3.175, -9.48]}, {"time": 3.2667, "value": -9.48, "curve": [3.358, -9.48, 3.542, 3.09]}, {"time": 3.6333, "value": 3.09, "curve": [3.717, 3.09, 3.883, -9.48]}, {"time": 3.9667, "value": -9.48, "curve": [4.067, -9.48, 4.267, 3.09]}, {"time": 4.3667, "value": 3.09}], "scale": [{"curve": [0.167, 1, 0.141, 1.119, 0.167, 1, 0.141, 1]}, {"time": 0.6667, "x": 1.119, "curve": [0.717, 1.119, 0.709, 1, 0.717, 1, 0.709, 1]}, {"time": 0.8667}]}, "Cloak_Left2": {"rotate": [{"curve": [0.167, 0, 0.196, 2.33]}, {"time": 0.6667, "value": 2.33, "curve": [0.717, 2.33, 0.726, 3.62]}, {"time": 0.8667, "value": 3.62, "curve": [1.008, 3.62, 1.292, -15.32]}, {"time": 1.4333, "value": -15.32, "curve": [1.517, -15.32, 1.683, 5.94]}, {"time": 1.7667, "value": 5.94, "curve": [1.842, 5.94, 1.992, -6.63]}, {"time": 2.0667, "value": -6.63, "curve": [2.142, -6.63, 2.292, 5.94]}, {"time": 2.3667, "value": 5.94, "curve": [2.45, 5.94, 2.617, -16.77]}, {"time": 2.7, "value": -16.77, "curve": [2.792, -16.77, 2.975, 5.94]}, {"time": 3.0667, "value": 5.94, "curve": [3.15, 5.94, 3.317, -6.63]}, {"time": 3.4, "value": -6.63, "curve": [3.492, -6.63, 3.675, 5.94]}, {"time": 3.7667, "value": 5.94, "curve": [3.867, 5.94, 4.067, -6.63]}, {"time": 4.1667, "value": -6.63, "curve": [4.25, -6.63, 4.417, 5.94]}, {"time": 4.5, "value": 5.94}]}, "Cloak_Left3": {"rotate": [{"curve": [0.167, 0, 0.196, 4.81]}, {"time": 0.6667, "value": 4.81, "curve": [0.717, 4.81, 0.726, -10.23]}, {"time": 0.8667, "value": -10.23, "curve": [0.925, -10.23, 1.042, -1.56]}, {"time": 1.1, "value": -1.56}, {"time": 1.5, "value": -28.29, "curve": [1.575, -28.29, 1.725, 10.98]}, {"time": 1.8, "value": 10.98, "curve": [1.875, 10.98, 2.025, -17.68]}, {"time": 2.1, "value": -17.68, "curve": [2.175, -17.68, 2.325, 10.98]}, {"time": 2.4, "value": 10.98, "curve": [2.492, 10.98, 2.675, -28.24]}, {"time": 2.7667, "value": -28.24, "curve": [2.85, -28.24, 3.017, 10.98]}, {"time": 3.1, "value": 10.98, "curve": [3.192, 10.98, 3.375, -17.68]}, {"time": 3.4667, "value": -17.68, "curve": [3.55, -17.68, 3.717, 10.98]}, {"time": 3.8, "value": 10.98, "curve": [3.892, 10.98, 4.075, -17.68]}, {"time": 4.1667, "value": -17.68, "curve": [4.25, -17.68, 4.417, 10.98]}, {"time": 4.5, "value": 10.98}]}, "Cloak_Left4": {"rotate": [{"time": 0.6667, "curve": [0.717, 0, 0.726, 0.05]}, {"time": 0.8667, "value": 0.05, "curve": [0.967, 0.05, 1.167, 13.26]}, {"time": 1.2667, "value": 13.26}, {"time": 1.6667, "value": -19.31, "curve": [1.742, -19.31, 1.892, 15.26]}, {"time": 1.9667, "value": 15.26, "curve": [2.042, 15.26, 2.192, -19.31]}, {"time": 2.2667, "value": -19.31, "curve": [2.358, -19.31, 2.542, 15.26]}, {"time": 2.6333, "value": 15.26, "curve": [2.715, 15.26, 2.873, -14.7]}, {"time": 2.9667, "value": -19.31, "curve": [3.067, -19.31, 3.267, 15.26]}, {"time": 3.3667, "value": 15.26, "curve": [3.45, 15.26, 3.617, -19.31]}, {"time": 3.7, "value": -19.31, "curve": [3.792, -19.31, 3.975, 15.26]}, {"time": 4.0667, "value": 15.26, "curve": [4.15, 15.26, 4.317, -19.31]}, {"time": 4.4, "value": -19.31}]}, "CloakWave6": {"translate": [{"time": 1.0333, "curve": [1.175, 0, 1.458, 91.56, 1.175, 0, 1.458, -0.79]}, {"time": 1.6, "x": 91.56, "y": -0.79, "curve": [1.717, 91.56, 1.95, -53.67, 1.717, -0.79, 1.95, 0.46]}, {"time": 2.0667, "x": -53.67, "y": 0.46, "curve": [2.175, -53.67, 2.392, 91.56, 2.175, 0.46, 2.392, -0.79]}, {"time": 2.5, "x": 91.56, "y": -0.79, "curve": [2.617, 91.56, 2.85, -53.67, 2.617, -0.79, 2.85, 0.46]}, {"time": 2.9667, "x": -53.67, "y": 0.46, "curve": [3.083, -53.67, 3.317, 91.56, 3.083, 0.46, 3.317, -0.79]}, {"time": 3.4333, "x": 91.56, "y": -0.79}]}, "CloakWave5": {"translate": [{"time": 0.6333}, {"time": 0.9667, "x": -87.08, "y": 10.16}, {"time": 1.3, "curve": [1.417, 0, 1.65, 91.56, 1.417, 0, 1.65, -0.79]}, {"time": 1.7667, "x": 91.56, "y": -0.79, "curve": [1.883, 91.56, 2.117, -53.67, 1.883, -0.79, 2.117, 0.46]}, {"time": 2.2333, "x": -53.67, "y": 0.46, "curve": [2.342, -53.67, 2.558, 91.56, 2.342, 0.46, 2.558, -0.79]}, {"time": 2.6667, "x": 91.56, "y": -0.79, "curve": [2.783, 91.56, 3.017, -53.67, 2.783, -0.79, 3.017, 0.46]}, {"time": 3.1333, "x": -53.67, "y": 0.46, "curve": [3.25, -53.67, 3.483, 91.56, 3.25, 0.46, 3.483, -0.79]}, {"time": 3.6, "x": 91.56, "y": -0.79}]}, "CloakWave4": {"translate": [{"time": 0.7333}, {"time": 0.9667, "x": -130.77, "y": -1.8}, {"time": 1.4667, "curve": [1.583, 0, 1.817, 91.56, 1.583, 0, 1.817, -0.79]}, {"time": 1.9333, "x": 91.56, "y": -0.79, "curve": [2.05, 91.56, 2.283, -53.67, 2.05, -0.79, 2.283, 0.46]}, {"time": 2.4, "x": -53.67, "y": 0.46, "curve": [2.508, -53.67, 2.725, 91.56, 2.508, 0.46, 2.725, -0.79]}, {"time": 2.8333, "x": 91.56, "y": -0.79, "curve": [2.95, 91.56, 3.183, -53.67, 2.95, -0.79, 3.183, 0.46]}, {"time": 3.3, "x": -53.67, "y": 0.46, "curve": [3.408, -53.67, 3.625, 91.56, 3.408, 0.46, 3.625, -0.79]}, {"time": 3.7333, "x": 91.56, "y": -0.79}]}, "CloakWave3": {"translate": [{"time": 0.7333, "curve": [0.792, 0, 0.908, -130.78, 0.792, 0, 0.908, -1.8]}, {"time": 0.9667, "x": -130.78, "y": -1.8}, {"time": 1.3667, "curve": [1.483, 0, 1.717, 90.88, 1.483, 0, 1.717, -36.57]}, {"time": 1.8333, "x": 90.88, "y": -36.57, "curve": [1.95, 90.88, 2.183, -53.67, 1.95, -36.57, 2.183, 0.46]}, {"time": 2.3, "x": -53.67, "y": 0.46, "curve": [2.408, -53.67, 2.625, 90.88, 2.408, 0.46, 2.625, -36.57]}, {"time": 2.7333, "x": 90.88, "y": -36.57, "curve": [2.85, 90.88, 3.083, -53.67, 2.85, -36.57, 3.083, 0.46]}, {"time": 3.2, "x": -53.67, "y": 0.46, "curve": [3.308, -53.67, 3.525, 90.88, 3.308, 0.46, 3.525, -36.57]}, {"time": 3.6333, "x": 90.88, "y": -36.57}]}, "CloakWave1": {"translate": [{"time": 0.6333, "curve": [0.692, 0, 0.808, -0.64, 0.692, 0, 0.808, -79.24]}, {"time": 0.8667, "x": -0.64, "y": -79.24, "curve": [0.967, -0.64, 1.167, 90.76, 0.967, -79.24, 1.167, -34.03]}, {"time": 1.2667, "x": 90.76, "y": -34.03, "curve": [1.358, 90.76, 1.542, -53.67, 1.358, -34.03, 1.542, 0.46]}, {"time": 1.6333, "x": -53.67, "y": 0.46, "curve": [1.725, -53.67, 1.908, 90.76, 1.725, 0.46, 1.908, -34.03]}, {"time": 2, "x": 90.76, "y": -34.03, "curve": [2.1, 90.76, 2.3, -53.67, 2.1, -34.03, 2.3, 0.46]}, {"time": 2.4, "x": -53.67, "y": 0.46, "curve": [2.492, -53.67, 2.675, 90.76, 2.492, 0.46, 2.675, -34.03]}, {"time": 2.7667, "x": 90.76, "y": -34.03, "curve": [2.85, 90.76, 3.017, -53.67, 2.85, -34.03, 3.017, 0.46]}, {"time": 3.1, "x": -53.67, "y": 0.46, "curve": [3.2, -53.67, 3.4, 90.76, 3.2, 0.46, 3.4, -34.03]}, {"time": 3.5, "x": 90.76, "y": -34.03, "curve": [3.583, 90.76, 3.75, -53.67, 3.583, -34.03, 3.75, 0.46]}, {"time": 3.8333, "x": -53.67, "y": 0.46, "curve": [3.933, -53.67, 4.133, 90.76, 3.933, 0.46, 4.133, -34.03]}, {"time": 4.2333, "x": 90.76, "y": -34.03, "curve": [4.325, 90.76, 4.508, -53.67, 4.325, -34.03, 4.508, 0.46]}, {"time": 4.6, "x": -53.67, "y": 0.46}]}, "CloakWave2": {"translate": [{"time": 0.6333, "curve": [0.706, 0, 0.837, -62.74, 0.706, 0, 0.837, -58.4]}, {"time": 0.9333, "x": -87.84, "y": -81.76, "curve": [0.983, -33.78, 1.029, 0, 0.983, -31.45, 1.029, 0]}, {"time": 1.0667, "curve": [1.142, 0, 1.292, 90.73, 1.142, 0, 1.292, -40.25]}, {"time": 1.3667, "x": 90.73, "y": -40.25, "curve": [1.442, 90.73, 1.592, -53.67, 1.442, -40.25, 1.592, 0.46]}, {"time": 1.6667, "x": -53.67, "y": 0.46, "curve": [1.733, -53.67, 1.867, 90.73, 1.733, 0.46, 1.867, -40.25]}, {"time": 1.9333, "x": 90.73, "y": -40.25, "curve": [2.008, 90.73, 2.158, -53.67, 2.008, -40.25, 2.158, 0.46]}, {"time": 2.2333, "x": -53.67, "y": 0.46, "curve": [2.3, -53.67, 2.433, 90.73, 2.3, 0.46, 2.433, -40.25]}, {"time": 2.5, "x": 90.73, "y": -40.25, "curve": [2.575, 90.73, 2.725, -53.67, 2.575, -40.25, 2.725, 0.46]}, {"time": 2.8, "x": -53.67, "y": 0.46, "curve": [2.867, -53.67, 3, 90.73, 2.867, 0.46, 3, -40.25]}, {"time": 3.0667, "x": 90.73, "y": -40.25, "curve": [3.142, 90.73, 3.292, -53.67, 3.142, -40.25, 3.292, 0.46]}, {"time": 3.3667, "x": -53.67, "y": 0.46, "curve": [3.433, -53.67, 3.567, 90.73, 3.433, 0.46, 3.567, -40.25]}, {"time": 3.6333, "x": 90.73, "y": -40.25}]}, "CrownHolder": {"rotate": [{"time": 1.0667, "value": 90.86}], "translate": [{"time": 1.0667, "x": 4.38, "y": -51.36, "curve": [1.141, 2.86, 1.367, 2.44, 1.141, -6.32, 1.367, 6.13]}, {"time": 1.4667, "x": 2.44, "y": 6.13, "curve": [1.625, 2.44, 1.942, 2.25, 1.625, 6.13, 1.942, -39.01]}, {"time": 2.1, "x": 2.25, "y": -39.01, "curve": [2.267, 2.25, 2.6, 2.44, 2.267, -39.01, 2.6, 6.13]}, {"time": 2.7667, "x": 2.44, "y": 6.13, "curve": [2.933, 2.44, 3.267, 2.25, 2.933, 6.13, 3.267, -39.01]}, {"time": 3.4333, "x": 2.25, "y": -39.01}]}, "Beak": {"rotate": [{"curve": [0.167, 0, 0.5, 16.01]}, {"time": 0.6667, "value": 16.01, "curve": [0.833, 16.01, 1.167, -25.8]}, {"time": 1.3333, "value": -25.8}]}, "Beak2": {"rotate": [{"curve": [0.167, 0, 0.5, 16.01]}, {"time": 0.6667, "value": 16.01, "curve": [0.833, 16.01, 1.167, -25.8]}, {"time": 1.3333, "value": -25.8}]}}}, "exit-old": {"slots": {"MASK": {"attachment": [{"time": 0.6667, "name": "MASK"}]}}, "bones": {"Hands_1": {"rotate": [{"value": -5.24, "curve": [0.117, -5.24, 0.35, -15.57]}, {"time": 0.4667, "value": -15.57, "curve": [0.467, -7.77, 1.531, 7.59]}, {"time": 1.6, "value": 15.89, "curve": [2.303, 8.63, 2.667, 1.59]}, {"time": 2.6667, "value": -5.24}]}, "Antler1": {"rotate": [{"curve": [0.167, 0, 0.5, 8.86]}, {"time": 0.6667, "value": 8.86, "curve": [0.792, 8.86, 1.042, 4.02]}, {"time": 1.1667, "value": 4.02, "curve": [1.542, 4.02, 2.292, 16.19]}, {"time": 2.6667, "value": 16.19}]}, "Antler6": {"rotate": [{"curve": [0.167, 0, 0.5, -5.82]}, {"time": 0.6667, "value": -5.82, "curve": [0.792, -5.82, 1.042, 0.16]}, {"time": 1.1667, "value": 0.16, "curve": [1.542, 0.16, 2.292, -14.01]}, {"time": 2.6667, "value": -14.01}]}, "Eyes": {"translate": [{"x": 5.65, "y": -0.19, "curve": [0.125, 5.65, 0.375, 50.49, 0.125, -0.19, 0.375, -1.64]}, {"time": 0.5, "x": 50.49, "y": -1.64, "curve": [0.858, 50.49, 1.091, -75.03, 0.858, -1.64, 1.091, 2.48]}, {"time": 1.9333, "x": -75.03, "y": 2.48}]}, "Hands_0": {"rotate": [{"value": 1.63, "curve": [0.117, 1.63, 0.35, 16.19]}, {"time": 0.4667, "value": 16.19, "curve": [0.815, 7.29, 1.229, -8]}, {"time": 1.6, "value": -19.09, "curve": [2.008, -7.14, 2.383, 1.63]}, {"time": 2.6667, "value": 1.63}]}, "NeckLeaf5": {"rotate": [{"value": 0.17, "curve": [0, 0.12, 1.367, 0.04]}, {"time": 1.3667, "curve": [1.367, 2.19, 1.967, 6.56]}, {"time": 1.9667, "value": 8.75, "curve": [1.967, 6.6, 2.667, 2.31]}, {"time": 2.6667, "value": 0.17}]}, "CrownEye": {"scale": [{"time": 0.6, "curve": [0.6, 0.954, 0.7, 0.861, 0.6, 1.024, 0.7, 1.073]}, {"time": 0.7, "x": 0.815, "y": 1.097}, {"time": 0.8, "curve": "stepped"}, {"time": 2.5, "curve": [2.5, 0.954, 2.6, 0.861, 2.5, 1.024, 2.6, 1.073]}, {"time": 2.6, "x": 0.815, "y": 1.097, "curve": [2.637, 0.886, 2.667, 0.952, 2.637, 1.06, 2.667, 1.025]}, {"time": 2.6667}]}, "BodyBtm": {"scale": [{"curve": [0.125, 1, 0.375, 1.015, 0.125, 1, 0.375, 0.989]}, {"time": 0.5, "x": 1.015, "y": 0.989, "curve": [0.625, 1.015, 0.875, 0.967, 0.625, 0.989, 0.875, 1.049]}, {"time": 1, "x": 0.967, "y": 1.049, "curve": [2.161, 0.97, 2.667, 0.992, 2.161, 1.045, 2.667, 1.012]}, {"time": 2.6667}]}, "BodyTop": {"scale": [{"x": 0.991, "y": 1.014, "curve": [0.125, 0.991, 0.375, 1.015, 0.125, 1.014, 0.375, 0.989]}, {"time": 0.5, "x": 1.015, "y": 0.989, "curve": [0.625, 1.015, 0.875, 0.967, 0.625, 0.989, 0.875, 1.049]}, {"time": 1, "x": 0.967, "y": 1.049, "curve": [1.991, 0.972, 2.233, 0.992, 1.991, 1.042, 2.233, 1.012]}, {"time": 2.2333, "curve": [2.233, 0.998, 2.667, 0.993, 2.233, 1.003, 2.667, 1.01]}, {"time": 2.6667, "x": 0.991, "y": 1.014}]}, "Head": {"translate": [{"time": 1, "curve": [1.348, -4.71, 1.433, -24.64, 1.348, 0.05, 1.433, 0.28]}, {"time": 1.4333, "x": -32.85, "y": 0.37, "curve": [1.433, -24.64, 2.667, -8.21, 1.433, 0.28, 2.667, 0.09]}, {"time": 2.6667}]}, "MAIN": {"translate": [{"time": 0.6667, "curve": [1.167, 0, 2.433, 0.31, 1.167, 0, 2.433, -544.74]}, {"time": 2.6667, "x": 0.48, "y": -857.52}], "scale": [{"curve": [0.158, 1, 0.475, 1.079, 0.158, 1, 0.475, 0.907]}, {"time": 0.6333, "x": 1.079, "y": 0.907, "curve": [0.725, 1.079, 0.908, 1, 0.725, 0.907, 0.908, 1]}, {"time": 1}]}}, "attachments": {"default": {"MASK": {"MASK": {"deform": [{"time": 0.6667, "offset": 1, "vertices": [-33.17596, 0, -33.17596]}, {"time": 1.3}]}}}}}, "hidden": {"slots": {"MASK": {"attachment": [{"name": "MASK"}]}}, "bones": {"Head": {"translate": [{"curve": [0.417, 0, 1.25, -32.85, 0.417, 0, 1.25, 0.37]}, {"time": 1.6667, "x": -32.85, "y": 0.37}]}, "Eyes": {"translate": [{"x": 5.65, "y": -0.19, "curve": [0.217, 10.77, 0.416, 14.55, 0.217, -0.37, 0.416, -0.5]}, {"time": 0.5667, "x": 14.55, "y": -0.5, "curve": [0.967, 14.55, 1.767, -18.38, 0.967, -0.5, 1.767, 0.65]}, {"time": 2.1667, "x": -18.38, "y": 0.65}]}, "Antler1": {"rotate": [{"curve": [0.417, 0, 1.25, -11.17]}, {"time": 1.6667, "value": -11.17}]}, "Antler6": {"rotate": [{"curve": [0.417, 0, 1.25, 7.97]}, {"time": 1.6667, "value": 7.97}]}, "Hands_1": {"rotate": [{"value": -5.24, "curve": [0.036, -5.45, 0.07, -5.57]}, {"time": 0.1, "value": -5.57, "curve": [0.308, -5.57, 0.725, 4.08]}, {"time": 0.9333, "value": 4.08, "curve": [1.133, 4.08, 1.533, -11.11]}, {"time": 1.7333, "value": -11.11}]}, "Hands_0": {"rotate": [{"value": 1.63, "curve": [0.127, -0.05, 0.246, -1.34]}, {"time": 0.3333, "value": -1.34, "curve": [0.483, -1.34, 0.783, 5.53]}, {"time": 0.9333, "value": 5.53, "curve": [1.133, 5.53, 1.533, 2.01]}, {"time": 1.7333, "value": 2.01}]}, "NeckLeaf5": {"rotate": [{"value": 0.17, "curve": [0.255, 1.02, 0.703, 8.75]}, {"time": 0.9333, "value": 8.75, "curve": [1.133, 8.75, 1.533, 0]}, {"time": 1.7333}]}, "CrownEye": {"scale": [{"time": 0.7, "curve": [0.725, 1, 0.775, 0.815, 0.725, 1, 0.775, 1.097]}, {"time": 0.8, "x": 0.815, "y": 1.097, "curve": [0.833, 0.815, 0.9, 1, 0.833, 1.097, 0.9, 1]}, {"time": 0.9333, "curve": "stepped"}, {"time": 1.5333, "curve": [1.558, 1, 1.608, 0.815, 1.558, 1, 1.608, 1.097]}, {"time": 1.6333, "x": 0.815, "y": 1.097, "curve": [1.667, 0.815, 1.733, 1, 1.667, 1.097, 1.733, 1]}, {"time": 1.7667}]}, "BodyBtm": {"scale": [{"curve": [0.417, 1, 1.25, 0.967, 0.417, 1, 1.25, 1.049]}, {"time": 1.6667, "x": 0.967, "y": 1.049}]}, "BodyTop": {"scale": [{"x": 0.991, "y": 1.014, "curve": [0.217, 0.996, 0.417, 1, 0.217, 1.006, 0.417, 1]}, {"time": 0.5667, "curve": [0.983, 1, 1.817, 0.967, 0.983, 1, 1.817, 1.049]}, {"time": 2.2333, "x": 0.967, "y": 1.049}]}, "MAIN": {"translate": [{"y": -822.7}]}}}, "idle": {"slots": {"Face": {"attachment": [{"time": 0.3333, "name": "Face2"}, {"time": 0.6667, "name": "Face"}, {"time": 1, "name": "Face2"}, {"time": 1.3333, "name": "Face"}, {"time": 1.6667, "name": "Face2"}, {"time": 2, "name": "Face"}, {"time": 2.3333, "name": "Face2"}, {"time": 2.6667, "name": "Face"}, {"time": 3, "name": "Face2"}, {"time": 3.3333, "name": "Face"}, {"time": 3.6667, "name": "Face2"}, {"time": 4, "name": "Face"}, {"time": 4.3333, "name": "Face2"}, {"time": 4.6667, "name": "Face"}, {"time": 5, "name": "Face2"}, {"time": 5.3333, "name": "Face"}, {"time": 5.6667, "name": "Face2"}, {"time": 6, "name": "Face"}, {"time": 6.3333, "name": "Face2"}, {"time": 6.6667, "name": "Face"}, {"time": 7, "name": "Face2"}, {"time": 7.3333, "name": "Face"}, {"time": 7.6667, "name": "Face2"}, {"time": 8, "name": "Face"}, {"time": 8.3333, "name": "Face2"}, {"time": 8.6667, "name": "Face"}, {"time": 9, "name": "Face2"}, {"time": 9.3333, "name": "Face"}, {"time": 9.6667, "name": "Face2"}, {"time": 10, "name": "Face"}]}}, "bones": {"Eyes": {"translate": [{"x": 5.65, "y": -0.19, "curve": [0.217, 10.77, 0.416, 14.55, 0.217, -0.37, 0.416, -0.5]}, {"time": 0.5667, "x": 14.55, "y": -0.5, "curve": [0.967, 14.55, 1.767, -18.38, 0.967, -0.5, 1.767, 0.65]}, {"time": 2.1667, "x": -18.38, "y": 0.65, "curve": [2.45, -18.38, 2.926, -3.78, 2.45, 0.65, 2.926, 0.14]}, {"time": 3.3333, "x": 5.65, "y": -0.19, "curve": [3.55, 10.77, 3.749, 14.55, 3.55, -0.37, 3.749, -0.5]}, {"time": 3.9, "x": 14.55, "y": -0.5, "curve": [4.3, 14.55, 5.1, -18.38, 4.3, -0.5, 5.1, 0.65]}, {"time": 5.5, "x": -18.38, "y": 0.65, "curve": [5.784, -18.38, 6.259, -3.78, 5.784, 0.65, 6.259, 0.14]}, {"time": 6.6667, "x": 5.65, "y": -0.19, "curve": [6.883, 10.77, 7.083, 14.55, 6.883, -0.37, 7.083, -0.5]}, {"time": 7.2333, "x": 14.55, "y": -0.5, "curve": [7.633, 14.55, 8.433, -18.38, 7.633, -0.5, 8.433, 0.65]}, {"time": 8.8333, "x": -18.38, "y": 0.65, "curve": [9.117, -18.38, 9.593, -3.78, 9.117, 0.65, 9.593, 0.14]}, {"time": 10, "x": 5.65, "y": -0.19}]}, "Head": {"translate": [{"curve": [0.417, 0, 1.25, -32.85, 0.417, 0, 1.25, 0.37]}, {"time": 1.6667, "x": -32.85, "y": 0.37, "curve": [2.083, -32.85, 2.917, 0, 2.083, 0.37, 2.917, 0]}, {"time": 3.3333, "curve": [3.725, 0, 4.508, -32.85, 3.725, 0, 4.508, 0.37]}, {"time": 4.9, "x": -32.85, "y": 0.37, "curve": [5.342, -32.85, 6.225, 0, 5.342, 0.37, 6.225, 0]}, {"time": 6.6667, "curve": [7.083, 0, 7.917, -32.85, 7.083, 0, 7.917, 0.37]}, {"time": 8.3333, "x": -32.85, "y": 0.37, "curve": [8.75, -32.85, 9.583, 0, 8.75, 0.37, 9.583, 0]}, {"time": 10}]}, "Antler1": {"rotate": [{"value": -12.38, "curve": [0.363, -6.88, 0.755, 0]}, {"time": 1, "curve": [1.417, 0, 2.25, -19.58]}, {"time": 2.6667, "value": -19.58, "curve": [3.083, -19.58, 3.917, 0]}, {"time": 4.3333, "curve": [4.75, 0, 5.583, -19.58]}, {"time": 6, "value": -19.58, "curve": [6.417, -19.58, 7.25, 0]}, {"time": 7.6667, "curve": [8.083, 0, 8.917, -19.58]}, {"time": 9.3333, "value": -19.58, "curve": [9.505, -19.58, 9.746, -16.31]}, {"time": 10, "value": -12.38}]}, "Antler6": {"rotate": [{"value": 17.93, "curve": [0.363, 9.96, 0.755, 0]}, {"time": 1, "curve": [1.417, 0, 2.25, 28.37]}, {"time": 2.6667, "value": 28.37, "curve": [3.083, 28.37, 3.917, 0]}, {"time": 4.3333, "curve": [4.75, 0, 5.583, 28.37]}, {"time": 6, "value": 28.37, "curve": [6.417, 28.37, 7.25, 0]}, {"time": 7.6667, "curve": [8.083, 0, 8.917, 28.37]}, {"time": 9.3333, "value": 28.37, "curve": [9.505, 28.37, 9.746, 23.62]}, {"time": 10, "value": 17.93}]}, "Hands_1": {"rotate": [{"value": -5.24, "curve": [0.036, -5.45, 0.07, -5.57]}, {"time": 0.1, "value": -5.57, "curve": [0.308, -5.57, 0.725, 4.08]}, {"time": 0.9333, "value": 4.08, "curve": [1.133, 4.08, 1.533, -11.11]}, {"time": 1.7333, "value": -11.11, "curve": [1.925, -11.11, 2.308, 0.31]}, {"time": 2.5, "value": 0.31, "curve": [2.704, 0.31, 3.087, -4.18]}, {"time": 3.3333, "value": -5.24, "curve": [3.37, -5.45, 3.403, -5.57]}, {"time": 3.4333, "value": -5.57, "curve": [3.642, -5.57, 4.058, 10.37]}, {"time": 4.2667, "value": 10.37, "curve": [4.467, 10.37, 4.867, -2.25]}, {"time": 5.0667, "value": -2.25, "curve": [5.258, -2.25, 5.642, 0.31]}, {"time": 5.8333, "value": 0.31, "curve": [6.037, 0.31, 6.421, -4.18]}, {"time": 6.6667, "value": -5.24, "curve": [6.703, -5.45, 6.737, -5.57]}, {"time": 6.7667, "value": -5.57, "curve": [6.975, -5.57, 7.392, 4.08]}, {"time": 7.6, "value": 4.08, "curve": [7.8, 4.08, 8.2, -11.11]}, {"time": 8.4, "value": -11.11, "curve": [8.592, -11.11, 8.975, 0.31]}, {"time": 9.1667, "value": 0.31, "curve": [9.37, 0.31, 9.754, -4.18]}, {"time": 10, "value": -5.24}]}, "Hands_0": {"rotate": [{"value": 1.63, "curve": [0.127, -0.05, 0.246, -1.34]}, {"time": 0.3333, "value": -1.34, "curve": [0.483, -1.34, 0.783, 5.53]}, {"time": 0.9333, "value": 5.53, "curve": [1.133, 5.53, 1.533, 2.01]}, {"time": 1.7333, "value": 2.01, "curve": [1.983, 2.01, 2.483, 8.29]}, {"time": 2.7333, "value": 8.29, "curve": [2.88, 8.29, 3.12, 4.39]}, {"time": 3.3333, "value": 1.63, "curve": [3.461, -0.05, 3.579, -1.34]}, {"time": 3.6667, "value": -1.34, "curve": [3.817, -1.34, 4.117, 5.53]}, {"time": 4.2667, "value": 5.53, "curve": [4.467, 5.53, 4.867, 2.01]}, {"time": 5.0667, "value": 2.01, "curve": [5.317, 2.01, 5.817, 8.29]}, {"time": 6.0667, "value": 8.29, "curve": [6.213, 8.29, 6.454, 4.39]}, {"time": 6.6667, "value": 1.63, "curve": [6.794, -0.05, 6.913, -1.34]}, {"time": 7, "value": -1.34, "curve": [7.15, -1.34, 7.45, 5.53]}, {"time": 7.6, "value": 5.53, "curve": [7.8, 5.53, 8.2, 2.01]}, {"time": 8.4, "value": 2.01, "curve": [8.65, 2.01, 9.15, 8.29]}, {"time": 9.4, "value": 8.29, "curve": [9.546, 8.29, 9.787, 4.39]}, {"time": 10, "value": 1.63}]}, "NeckLeaf5": {"rotate": [{"value": 0.17, "curve": [0.255, 1.02, 0.703, 8.75]}, {"time": 0.9333, "value": 8.75, "curve": [1.133, 8.75, 1.533, 0]}, {"time": 1.7333, "curve": [1.9, 0, 2.233, 8.75]}, {"time": 2.4, "value": 8.75, "curve": [2.625, 8.75, 3.075, 0]}, {"time": 3.3, "curve": [3.311, 0, 3.322, 0.06]}, {"time": 3.3333, "value": 0.17, "curve": [3.588, 1.02, 4.036, 8.75]}, {"time": 4.2667, "value": 8.75, "curve": [4.467, 8.75, 4.867, 0]}, {"time": 5.0667, "curve": [5.233, 0, 5.567, 8.75]}, {"time": 5.7333, "value": 8.75, "curve": [5.958, 8.75, 6.408, 0]}, {"time": 6.6333, "curve": [6.644, 0, 6.655, 0.06]}, {"time": 6.6667, "value": 0.17, "curve": [6.921, 1.02, 7.37, 8.75]}, {"time": 7.6, "value": 8.75, "curve": [7.8, 8.75, 8.2, 0]}, {"time": 8.4, "curve": [8.567, 0, 8.9, 8.75]}, {"time": 9.0667, "value": 8.75, "curve": [9.292, 8.75, 9.742, 0]}, {"time": 9.9667, "curve": [9.977, 0, 9.988, 0.06]}, {"time": 10, "value": 0.17}]}, "BodyBtm": {"scale": [{"curve": [0.417, 1, 1.25, 0.967, 0.417, 1, 1.25, 1.049]}, {"time": 1.6667, "x": 0.967, "y": 1.049, "curve": [2.083, 0.967, 2.917, 1, 2.083, 1.049, 2.917, 1]}, {"time": 3.3333, "curve": [3.75, 1, 4.583, 0.967, 3.75, 1, 4.583, 1.049]}, {"time": 5, "x": 0.967, "y": 1.049, "curve": [5.417, 0.967, 6.25, 1, 5.417, 1.049, 6.25, 1]}, {"time": 6.6667, "curve": [7.083, 1, 7.917, 0.967, 7.083, 1, 7.917, 1.049]}, {"time": 8.3333, "x": 0.967, "y": 1.049, "curve": [8.75, 0.967, 9.583, 1, 8.75, 1.049, 9.583, 1]}, {"time": 10}]}, "BodyTop": {"scale": [{"x": 0.991, "y": 1.014, "curve": [0.217, 0.996, 0.417, 1, 0.217, 1.006, 0.417, 1]}, {"time": 0.5667, "curve": [0.983, 1, 1.817, 0.967, 0.983, 1, 1.817, 1.049]}, {"time": 2.2333, "x": 0.967, "y": 1.049, "curve": [2.501, 0.967, 2.947, 0.981, 2.501, 1.049, 2.947, 1.028]}, {"time": 3.3333, "x": 0.991, "y": 1.014, "curve": [3.55, 0.996, 3.75, 1, 3.55, 1.006, 3.75, 1]}, {"time": 3.9, "curve": [4.317, 1, 5.15, 0.967, 4.317, 1, 5.15, 1.049]}, {"time": 5.5667, "x": 0.967, "y": 1.049, "curve": [5.834, 0.967, 6.28, 0.981, 5.834, 1.049, 6.28, 1.028]}, {"time": 6.6667, "x": 0.991, "y": 1.014, "curve": [6.883, 0.996, 7.083, 1, 6.883, 1.006, 7.083, 1]}, {"time": 7.2333, "curve": [7.65, 1, 8.483, 0.967, 7.65, 1, 8.483, 1.049]}, {"time": 8.9, "x": 0.967, "y": 1.049, "curve": [9.168, 0.967, 9.614, 0.981, 9.168, 1.049, 9.614, 1.028]}, {"time": 10, "x": 0.991, "y": 1.014}]}, "Beak2": {"rotate": [{"value": 1.45, "curve": [0.395, 3.01, 0.858, 5.44]}, {"time": 1.1333, "value": 5.44, "curve": [1.55, 5.44, 2.383, 0]}, {"time": 2.8, "curve": [3.217, 0, 4.05, 5.44]}, {"time": 4.4667, "value": 5.44, "curve": [4.883, 5.44, 5.717, 0]}, {"time": 6.1333, "curve": [6.55, 0, 7.383, 5.44]}, {"time": 7.8, "value": 5.44, "curve": [8.217, 5.44, 9.05, 0]}, {"time": 9.4667, "curve": [9.609, 0, 9.796, 0.62]}, {"time": 10, "value": 1.45}]}}}, "talk": {"slots": {"Face": {"attachment": [{"time": 0.6667, "name": "Face_Closed"}, {"time": 2.6667, "name": "Face2"}, {"time": 3, "name": "Face"}, {"time": 3.3333, "name": "Face2"}, {"time": 3.6667, "name": "Face"}, {"time": 4, "name": "Face2"}, {"time": 4.3333, "name": "Face_Closed"}, {"time": 5.3, "name": "Face2"}, {"time": 5.6333, "name": "Face"}, {"time": 6.3, "name": "Face2"}, {"time": 6.6333, "name": "Face"}]}}, "bones": {"Eyes": {"rotate": [{"time": 3.1667, "curve": [3.45, 0, 4.017, -16.84]}, {"time": 4.3, "value": -16.84, "curve": "stepped"}, {"time": 5.3, "value": -16.84, "curve": [5.467, -16.84, 5.8, 0]}, {"time": 5.9667}], "translate": [{"x": 5.65, "y": -0.19, "curve": [0.208, 5.65, 0.625, 30.1, 0.208, -0.19, 0.625, -1.2]}, {"time": 0.8333, "x": 30.1, "y": -1.2, "curve": [1.233, 30.1, 2.033, -39.29, 1.233, -1.2, 2.033, 1.73]}, {"time": 2.4333, "x": -39.29, "y": 1.73, "curve": [2.825, -39.29, 3.608, 16.72, 2.825, 1.73, 3.608, -1.16]}, {"time": 4, "x": 16.72, "y": -1.16, "curve": [4.225, 16.72, 4.675, -37.41, 4.225, -1.16, 4.675, 1.76]}, {"time": 4.9, "x": -37.41, "y": 1.76, "curve": [5.333, -37.41, 6.2, 5.65, 5.333, 1.76, 6.2, -0.19]}, {"time": 6.6333, "x": 5.65, "y": -0.19}]}, "Head": {"translate": [{"curve": [0.417, 0, 1.25, -32.85, 0.417, 0, 1.25, 0.37]}, {"time": 1.6667, "x": -32.85, "y": 0.37, "curve": [2.075, -32.85, 2.892, 0, 2.075, 0.37, 2.892, 0]}, {"time": 3.3, "curve": [3.717, 0, 4.55, -32.85, 3.717, 0, 4.55, 0.37]}, {"time": 4.9667, "x": -32.85, "y": 0.37, "curve": [5.383, -32.85, 6.217, 0, 5.383, 0.37, 6.217, 0]}, {"time": 6.6333}]}, "Antler1": {"rotate": [{"value": -12.38, "curve": [0.363, -6.88, 0.755, 0]}, {"time": 1, "curve": [1.417, 0, 2.25, -19.58]}, {"time": 2.6667, "value": -19.58, "curve": [2.825, -19.58, 3.142, -12.38]}, {"time": 3.3, "value": -12.38, "curve": [3.663, -6.88, 4.055, 0]}, {"time": 4.3, "curve": [4.717, 0, 5.55, -19.58]}, {"time": 5.9667, "value": -19.58, "curve": [6.133, -19.58, 6.467, -12.38]}, {"time": 6.6333, "value": -12.38}]}, "Antler6": {"rotate": [{"value": 17.93, "curve": [0.363, 9.96, 0.755, 0]}, {"time": 1, "curve": [1.417, 0, 2.25, 28.37]}, {"time": 2.6667, "value": 28.37, "curve": [2.825, 28.37, 3.142, 17.93]}, {"time": 3.3, "value": 17.93, "curve": [3.663, 9.96, 4.055, 0]}, {"time": 4.3, "curve": [4.717, 0, 5.55, 28.37]}, {"time": 5.9667, "value": 28.37, "curve": [6.133, 28.37, 6.467, 17.93]}, {"time": 6.6333, "value": 17.93}]}, "Hands_1": {"rotate": [{"value": -5.24, "curve": [0.036, -5.45, 0.07, -5.57]}, {"time": 0.1, "value": -5.57, "curve": [0.308, -5.57, 0.725, 4.08]}, {"time": 0.9333, "value": 4.08, "curve": [1.133, 4.08, 1.533, -11.11]}, {"time": 1.7333, "value": -11.11, "curve": [1.925, -11.11, 2.308, 0.31]}, {"time": 2.5, "value": 0.31, "curve": [2.696, 0.31, 3.064, -4.18]}, {"time": 3.3, "value": -5.24, "curve": [3.336, -5.45, 3.37, -5.57]}, {"time": 3.4, "value": -5.57, "curve": [3.608, -5.57, 4.025, 4.08]}, {"time": 4.2333, "value": 4.08, "curve": [4.433, 4.08, 4.833, -11.11]}, {"time": 5.0333, "value": -11.11, "curve": [5.225, -11.11, 5.608, 0.31]}, {"time": 5.8, "value": 0.31, "curve": [6.004, 0.31, 6.387, -4.18]}, {"time": 6.6333, "value": -5.24}]}, "Hands_0": {"rotate": [{"value": 1.63, "curve": [0.127, -0.05, 0.246, -1.34]}, {"time": 0.3333, "value": -1.34, "curve": [0.483, -1.34, 0.783, 5.53]}, {"time": 0.9333, "value": 5.53, "curve": [1.133, 5.53, 1.533, 2.01]}, {"time": 1.7333, "value": 2.01, "curve": [1.983, 2.01, 2.483, 8.29]}, {"time": 2.7333, "value": 8.29, "curve": [2.871, 8.29, 3.099, 4.39]}, {"time": 3.3, "value": 1.63, "curve": [3.427, -0.05, 3.546, -1.34]}, {"time": 3.6333, "value": -1.34, "curve": [3.783, -1.34, 4.083, 5.53]}, {"time": 4.2333, "value": 5.53, "curve": [4.433, 5.53, 4.833, 2.01]}, {"time": 5.0333, "value": 2.01, "curve": [5.283, 2.01, 5.783, 8.29]}, {"time": 6.0333, "value": 8.29, "curve": [6.18, 8.29, 6.42, 4.39]}, {"time": 6.6333, "value": 1.63}]}, "NeckLeaf5": {"rotate": [{"value": 0.17, "curve": [0.255, 1.02, 0.703, 8.75]}, {"time": 0.9333, "value": 8.75, "curve": [1.133, 8.75, 1.533, 0]}, {"time": 1.7333, "curve": [1.9, 0, 2.233, 8.75]}, {"time": 2.4, "value": 8.75, "curve": [2.625, 8.75, 3.075, 0.17]}, {"time": 3.3, "value": 0.17, "curve": [3.555, 1.02, 4.003, 8.75]}, {"time": 4.2333, "value": 8.75, "curve": [4.433, 8.75, 4.833, 0]}, {"time": 5.0333, "curve": [5.2, 0, 5.533, 8.75]}, {"time": 5.7, "value": 8.75, "curve": [5.925, 8.75, 6.375, 0]}, {"time": 6.6, "curve": [6.611, 0, 6.622, 0.06]}, {"time": 6.6333, "value": 0.17}]}, "BodyBtm": {"scale": [{"curve": [0.417, 1, 1.25, 0.967, 0.417, 1, 1.25, 1.049]}, {"time": 1.6667, "x": 0.967, "y": 1.049, "curve": [2.075, 0.967, 2.892, 1, 2.075, 1.049, 2.892, 1]}, {"time": 3.3, "curve": [3.717, 1, 4.55, 0.967, 3.717, 1, 4.55, 1.049]}, {"time": 4.9667, "x": 0.967, "y": 1.049, "curve": [5.383, 0.967, 6.217, 1, 5.383, 1.049, 6.217, 1]}, {"time": 6.6333}]}, "BodyTop": {"scale": [{"x": 0.991, "y": 1.014, "curve": [0.217, 0.996, 0.417, 1, 0.217, 1.006, 0.417, 1]}, {"time": 0.5667, "curve": [0.983, 1, 1.817, 0.967, 0.983, 1, 1.817, 1.049]}, {"time": 2.2333, "x": 0.967, "y": 1.049, "curve": [2.493, 0.967, 2.925, 0.981, 2.493, 1.049, 2.925, 1.028]}, {"time": 3.3, "x": 0.991, "y": 1.014, "curve": [3.517, 0.996, 3.717, 1, 3.517, 1.006, 3.717, 1]}, {"time": 3.8667, "curve": [4.283, 1, 5.117, 0.967, 4.283, 1, 5.117, 1.049]}, {"time": 5.5333, "x": 0.967, "y": 1.049, "curve": [5.801, 0.967, 6.247, 0.981, 5.801, 1.049, 6.247, 1.028]}, {"time": 6.6333, "x": 0.991, "y": 1.014}]}, "Beak2": {"rotate": [{"value": 1.45, "curve": [0.142, 1.45, 0.425, 15.26]}, {"time": 0.5667, "value": 15.26, "curve": [0.633, 15.26, 0.767, 1.45]}, {"time": 0.8333, "value": 1.45, "curve": [0.875, 1.45, 0.958, 11.47]}, {"time": 1, "value": 11.47, "curve": [1.042, 11.47, 1.125, 1.45]}, {"time": 1.1667, "value": 1.45, "curve": "stepped"}, {"time": 1.3333, "value": 1.45, "curve": [1.392, 1.45, 1.508, 15.26]}, {"time": 1.5667, "value": 15.26, "curve": [1.625, 15.26, 1.742, 1.45]}, {"time": 1.8, "value": 1.45, "curve": [1.875, 1.45, 2.025, 15.26]}, {"time": 2.1, "value": 15.26, "curve": [2.175, 15.26, 2.325, 1.45]}, {"time": 2.4, "value": 1.45, "curve": [2.442, 1.45, 2.525, 11.47]}, {"time": 2.5667, "value": 11.47, "curve": [2.608, 11.47, 2.692, 1.45]}, {"time": 2.7333, "value": 1.45, "curve": "stepped"}, {"time": 2.9667, "value": 1.45, "curve": [3.008, 1.45, 3.092, 11.47]}, {"time": 3.1333, "value": 11.47, "curve": [3.175, 11.47, 3.258, 1.45]}, {"time": 3.3, "value": 1.45, "curve": [3.358, 1.45, 3.475, 15.26]}, {"time": 3.5333, "value": 15.26, "curve": [3.6, 15.26, 3.733, 1.45]}, {"time": 3.8, "value": 1.45, "curve": [3.842, 1.45, 3.925, 11.47]}, {"time": 3.9667, "value": 11.47, "curve": [4.008, 11.47, 4.092, 1.45]}, {"time": 4.1333, "value": 1.45, "curve": [4.175, 1.45, 4.258, 11.47]}, {"time": 4.3, "value": 11.47, "curve": [4.342, 11.47, 4.425, 1.45]}, {"time": 4.4667, "value": 1.45, "curve": "stepped"}, {"time": 5.2333, "value": 1.45, "curve": [5.292, 1.45, 5.408, 15.26]}, {"time": 5.4667, "value": 15.26, "curve": [5.525, 15.26, 5.642, 1.45]}, {"time": 5.7, "value": 1.45, "curve": [5.775, 1.45, 5.925, 15.26]}, {"time": 6, "value": 15.26, "curve": [6.075, 15.26, 6.225, 1.45]}, {"time": 6.3, "value": 1.45}]}, "Beak": {"rotate": [{"curve": [0.142, 0, 0.425, -11.43]}, {"time": 0.5667, "value": -11.43, "curve": [0.633, -11.43, 0.767, 0]}, {"time": 0.8333, "curve": [0.875, 0, 0.958, -5.24]}, {"time": 1, "value": -5.24, "curve": [1.042, -5.24, 1.125, 0]}, {"time": 1.1667, "curve": "stepped"}, {"time": 1.3333, "curve": [1.392, 0, 1.508, -11.43]}, {"time": 1.5667, "value": -11.43, "curve": [1.625, -11.43, 1.742, 0]}, {"time": 1.8, "curve": [1.875, 0, 2.025, -11.43]}, {"time": 2.1, "value": -11.43, "curve": [2.175, -11.43, 2.325, 0]}, {"time": 2.4, "curve": [2.442, 0, 2.525, -5.24]}, {"time": 2.5667, "value": -5.24, "curve": [2.608, -5.24, 2.692, 0]}, {"time": 2.7333, "curve": "stepped"}, {"time": 2.9667, "curve": [3.008, 0, 3.092, -5.24]}, {"time": 3.1333, "value": -5.24, "curve": [3.175, -5.24, 3.258, 0]}, {"time": 3.3, "curve": [3.358, 0, 3.475, -11.43]}, {"time": 3.5333, "value": -11.43, "curve": [3.6, -11.43, 3.733, 0]}, {"time": 3.8, "curve": [3.842, 0, 3.925, -5.24]}, {"time": 3.9667, "value": -5.24, "curve": [4.008, -5.24, 4.092, 0]}, {"time": 4.1333, "curve": [4.175, 0, 4.258, -5.24]}, {"time": 4.3, "value": -5.24, "curve": [4.342, -5.24, 4.425, 0]}, {"time": 4.4667, "curve": "stepped"}, {"time": 5.2333, "curve": [5.292, 0, 5.408, -11.43]}, {"time": 5.4667, "value": -11.43, "curve": [5.525, -11.43, 5.642, 0]}, {"time": 5.7, "curve": [5.775, 0, 5.925, -11.43]}, {"time": 6, "value": -11.43, "curve": [6.075, -11.43, 6.225, 0]}, {"time": 6.3}]}}}, "talk2": {"slots": {"Face": {"attachment": [{"time": 0.3333, "name": "Face_Closed"}, {"time": 2, "name": "Face2"}, {"time": 2.3333, "name": "Face"}, {"time": 3, "name": "Face2"}, {"time": 3.3333, "name": "Face"}]}}, "bones": {"Eyes": {"rotate": [{"time": 0.3333, "curve": [0.5, 0, 0.833, -16.84]}, {"time": 1, "value": -16.84, "curve": "stepped"}, {"time": 2, "value": -16.84, "curve": [2.167, -16.84, 2.5, 0]}, {"time": 2.6667}], "translate": [{"x": -9.89, "y": 0.35, "curve": [0.175, -9.89, 0.525, 16.72, 0.175, 0.35, 0.525, -1.16]}, {"time": 0.7, "x": 16.72, "y": -1.16, "curve": [0.925, 16.72, 1.375, -37.41, 0.925, -1.16, 1.375, 1.76]}, {"time": 1.6, "x": -37.41, "y": 1.76, "curve": [2.033, -37.41, 2.9, -9.89, 2.033, 1.76, 2.9, 0.35]}, {"time": 3.3333, "x": -9.89, "y": 0.35}]}, "Head": {"translate": [{"curve": [0.417, 0, 1.25, -32.85, 0.417, 0, 1.25, 0.37]}, {"time": 1.6667, "x": -32.85, "y": 0.37, "curve": [2.083, -32.85, 2.917, 0, 2.083, 0.37, 2.917, 0]}, {"time": 3.3333}]}, "Antler1": {"rotate": [{"value": -12.38, "curve": [0.363, -6.88, 0.755, 0]}, {"time": 1, "curve": [1.417, 0, 2.25, -19.58]}, {"time": 2.6667, "value": -19.58, "curve": [2.833, -19.58, 3.167, -12.38]}, {"time": 3.3333, "value": -12.38}]}, "Antler6": {"rotate": [{"value": 17.93, "curve": [0.363, 9.96, 0.755, 0]}, {"time": 1, "curve": [1.417, 0, 2.25, 28.37]}, {"time": 2.6667, "value": 28.37, "curve": [2.833, 28.37, 3.167, 17.93]}, {"time": 3.3333, "value": 17.93}]}, "Hands_1": {"rotate": [{"value": -5.24, "curve": [0.036, -5.45, 0.07, -5.57]}, {"time": 0.1, "value": -5.57, "curve": [0.308, -5.57, 0.725, 4.08]}, {"time": 0.9333, "value": 4.08, "curve": [1.133, 4.08, 1.533, -11.11]}, {"time": 1.7333, "value": -11.11, "curve": [1.925, -11.11, 2.308, 0.31]}, {"time": 2.5, "value": 0.31, "curve": [2.704, 0.31, 3.087, -4.18]}, {"time": 3.3333, "value": -5.24}]}, "Hands_0": {"rotate": [{"value": 1.63, "curve": [0.127, -0.05, 0.246, -1.34]}, {"time": 0.3333, "value": -1.34, "curve": [0.483, -1.34, 0.783, 5.53]}, {"time": 0.9333, "value": 5.53, "curve": [1.133, 5.53, 1.533, 2.01]}, {"time": 1.7333, "value": 2.01, "curve": [1.983, 2.01, 2.483, 8.29]}, {"time": 2.7333, "value": 8.29, "curve": [2.88, 8.29, 3.12, 4.39]}, {"time": 3.3333, "value": 1.63}]}, "NeckLeaf5": {"rotate": [{"value": 0.17, "curve": [0.255, 1.02, 0.703, 8.75]}, {"time": 0.9333, "value": 8.75, "curve": [1.133, 8.75, 1.533, 0]}, {"time": 1.7333, "curve": [1.9, 0, 2.233, 8.75]}, {"time": 2.4, "value": 8.75, "curve": [2.625, 8.75, 3.075, 0]}, {"time": 3.3, "curve": [3.311, 0, 3.322, 0.06]}, {"time": 3.3333, "value": 0.17}]}, "BodyBtm": {"scale": [{"curve": [0.417, 1, 1.25, 0.967, 0.417, 1, 1.25, 1.049]}, {"time": 1.6667, "x": 0.967, "y": 1.049, "curve": [2.083, 0.967, 2.917, 1, 2.083, 1.049, 2.917, 1]}, {"time": 3.3333}]}, "BodyTop": {"scale": [{"x": 0.991, "y": 1.014, "curve": [0.217, 0.996, 0.417, 1, 0.217, 1.006, 0.417, 1]}, {"time": 0.5667, "curve": [0.983, 1, 1.817, 0.967, 0.983, 1, 1.817, 1.049]}, {"time": 2.2333, "x": 0.967, "y": 1.049, "curve": [2.501, 0.967, 2.947, 0.981, 2.501, 1.049, 2.947, 1.028]}, {"time": 3.3333, "x": 0.991, "y": 1.014}]}, "Beak2": {"rotate": [{"value": 1.45, "curve": [0.058, 1.45, 0.175, 15.26]}, {"time": 0.2333, "value": 15.26, "curve": [0.3, 15.26, 0.433, 1.45]}, {"time": 0.5, "value": 1.45, "curve": [0.542, 1.45, 0.625, 11.47]}, {"time": 0.6667, "value": 11.47, "curve": [0.708, 11.47, 0.792, 1.45]}, {"time": 0.8333, "value": 1.45, "curve": [0.875, 1.45, 0.958, 11.47]}, {"time": 1, "value": 11.47, "curve": [1.042, 11.47, 1.125, 1.45]}, {"time": 1.1667, "value": 1.45, "curve": "stepped"}, {"time": 1.7, "value": 1.45, "curve": [1.758, 1.45, 1.875, 15.26]}, {"time": 1.9333, "value": 15.26, "curve": [1.992, 15.26, 2.108, 1.45]}, {"time": 2.1667, "value": 1.45, "curve": [2.242, 1.45, 2.392, 15.26]}, {"time": 2.4667, "value": 15.26, "curve": [2.542, 15.26, 2.692, 1.45]}, {"time": 2.7667, "value": 1.45, "curve": [2.808, 1.45, 2.892, 11.47]}, {"time": 2.9333, "value": 11.47, "curve": [2.975, 11.47, 3.058, 1.45]}, {"time": 3.1, "value": 1.45}]}, "Beak": {"rotate": [{"curve": [0.058, 0, 0.175, -11.43]}, {"time": 0.2333, "value": -11.43, "curve": [0.3, -11.43, 0.433, 0]}, {"time": 0.5, "curve": [0.542, 0, 0.625, -5.24]}, {"time": 0.6667, "value": -5.24, "curve": [0.708, -5.24, 0.792, 0]}, {"time": 0.8333, "curve": [0.875, 0, 0.958, -5.24]}, {"time": 1, "value": -5.24, "curve": [1.042, -5.24, 1.125, 0]}, {"time": 1.1667, "curve": "stepped"}, {"time": 1.7, "curve": [1.758, 0, 1.875, -11.43]}, {"time": 1.9333, "value": -11.43, "curve": [1.992, -11.43, 2.108, 0]}, {"time": 2.1667, "curve": [2.242, 0, 2.392, -11.43]}, {"time": 2.4667, "value": -11.43, "curve": [2.542, -11.43, 2.692, 0]}, {"time": 2.7667, "curve": [2.808, 0, 2.892, -5.24]}, {"time": 2.9333, "value": -5.24, "curve": [2.975, -5.24, 3.058, 0]}, {"time": 3.1}]}}}, "talk3": {"slots": {"Face": {"attachment": [{"time": 0.6667, "name": "Face_Closed"}, {"time": 3, "name": "Face"}]}}, "bones": {"Eyes": {"translate": [{"x": 5.65, "y": -0.19, "curve": [0.208, 5.65, 0.625, 30.1, 0.208, -0.19, 0.625, -1.2]}, {"time": 0.8333, "x": 30.1, "y": -1.2, "curve": [1.233, 30.1, 2.033, -39.29, 1.233, -1.2, 2.033, 1.73]}, {"time": 2.4333, "x": -39.29, "y": 1.73, "curve": [2.65, -39.29, 3.083, 5.65, 2.65, 1.73, 3.083, -0.19]}, {"time": 3.3, "x": 5.65, "y": -0.19}]}, "Head": {"translate": [{"curve": [0.417, 0, 1.25, -32.85, 0.417, 0, 1.25, 0.37]}, {"time": 1.6667, "x": -32.85, "y": 0.37, "curve": [2.075, -32.85, 2.892, 0, 2.075, 0.37, 2.892, 0]}, {"time": 3.3}]}, "Antler1": {"rotate": [{"value": -12.38, "curve": [0.363, -6.88, 0.755, 0]}, {"time": 1, "curve": [1.417, 0, 2.25, -19.58]}, {"time": 2.6667, "value": -19.58, "curve": [2.825, -19.58, 3.142, -12.38]}, {"time": 3.3, "value": -12.38}]}, "Antler6": {"rotate": [{"value": 17.93, "curve": [0.363, 9.96, 0.755, 0]}, {"time": 1, "curve": [1.417, 0, 2.25, 28.37]}, {"time": 2.6667, "value": 28.37, "curve": [2.825, 28.37, 3.142, 17.93]}, {"time": 3.3, "value": 17.93}]}, "Hands_1": {"rotate": [{"value": -5.24, "curve": [0.036, -5.45, 0.07, -5.57]}, {"time": 0.1, "value": -5.57, "curve": [0.308, -5.57, 0.725, 4.08]}, {"time": 0.9333, "value": 4.08, "curve": [1.133, 4.08, 1.533, -11.11]}, {"time": 1.7333, "value": -11.11, "curve": [1.925, -11.11, 2.308, 0.31]}, {"time": 2.5, "value": 0.31, "curve": [2.696, 0.31, 3.064, -4.18]}, {"time": 3.3, "value": -5.24}]}, "Hands_0": {"rotate": [{"value": 1.63, "curve": [0.127, -0.05, 0.246, -1.34]}, {"time": 0.3333, "value": -1.34, "curve": [0.483, -1.34, 0.783, 5.53]}, {"time": 0.9333, "value": 5.53, "curve": [1.133, 5.53, 1.533, 2.01]}, {"time": 1.7333, "value": 2.01, "curve": [1.983, 2.01, 2.483, 8.29]}, {"time": 2.7333, "value": 8.29, "curve": [2.871, 8.29, 3.099, 4.39]}, {"time": 3.3, "value": 1.63}]}, "NeckLeaf5": {"rotate": [{"value": 0.17, "curve": [0.255, 1.02, 0.703, 8.75]}, {"time": 0.9333, "value": 8.75, "curve": [1.133, 8.75, 1.533, 0]}, {"time": 1.7333, "curve": [1.9, 0, 2.233, 8.75]}, {"time": 2.4, "value": 8.75, "curve": [2.625, 8.75, 3.075, 0.17]}, {"time": 3.3, "value": 0.17}]}, "BodyBtm": {"scale": [{"curve": [0.417, 1, 1.25, 0.967, 0.417, 1, 1.25, 1.049]}, {"time": 1.6667, "x": 0.967, "y": 1.049, "curve": [2.075, 0.967, 2.892, 1, 2.075, 1.049, 2.892, 1]}, {"time": 3.3}]}, "BodyTop": {"scale": [{"x": 0.991, "y": 1.014, "curve": [0.217, 0.996, 0.417, 1, 0.217, 1.006, 0.417, 1]}, {"time": 0.5667, "curve": [0.983, 1, 1.817, 0.967, 0.983, 1, 1.817, 1.049]}, {"time": 2.2333, "x": 0.967, "y": 1.049, "curve": [2.493, 0.967, 2.925, 0.981, 2.493, 1.049, 2.925, 1.028]}, {"time": 3.3, "x": 0.991, "y": 1.014}]}, "Beak2": {"rotate": [{"value": 1.45, "curve": [0.142, 1.45, 0.425, 15.26]}, {"time": 0.5667, "value": 15.26, "curve": [0.633, 15.26, 0.767, 1.45]}, {"time": 0.8333, "value": 1.45, "curve": [0.875, 1.45, 0.958, 11.47]}, {"time": 1, "value": 11.47, "curve": [1.042, 11.47, 1.125, 1.45]}, {"time": 1.1667, "value": 1.45, "curve": "stepped"}, {"time": 1.3333, "value": 1.45, "curve": [1.392, 1.45, 1.508, 15.26]}, {"time": 1.5667, "value": 15.26, "curve": [1.625, 15.26, 1.742, 1.45]}, {"time": 1.8, "value": 1.45, "curve": [1.875, 1.45, 2.025, 15.26]}, {"time": 2.1, "value": 15.26, "curve": [2.175, 15.26, 2.325, 1.45]}, {"time": 2.4, "value": 1.45, "curve": [2.442, 1.45, 2.525, 11.47]}, {"time": 2.5667, "value": 11.47, "curve": [2.608, 11.47, 2.692, 1.45]}, {"time": 2.7333, "value": 1.45, "curve": "stepped"}, {"time": 2.9667, "value": 1.45, "curve": [3.008, 1.45, 3.092, 11.47]}, {"time": 3.1333, "value": 11.47, "curve": [3.175, 11.47, 3.258, 1.45]}, {"time": 3.3, "value": 1.45}]}, "Beak": {"rotate": [{"curve": [0.142, 0, 0.425, -11.43]}, {"time": 0.5667, "value": -11.43, "curve": [0.633, -11.43, 0.767, 0]}, {"time": 0.8333, "curve": [0.875, 0, 0.958, -5.24]}, {"time": 1, "value": -5.24, "curve": [1.042, -5.24, 1.125, 0]}, {"time": 1.1667, "curve": "stepped"}, {"time": 1.3333, "curve": [1.392, 0, 1.508, -11.43]}, {"time": 1.5667, "value": -11.43, "curve": [1.625, -11.43, 1.742, 0]}, {"time": 1.8, "curve": [1.875, 0, 2.025, -11.43]}, {"time": 2.1, "value": -11.43, "curve": [2.175, -11.43, 2.325, 0]}, {"time": 2.4, "curve": [2.442, 0, 2.525, -5.24]}, {"time": 2.5667, "value": -5.24, "curve": [2.608, -5.24, 2.692, 0]}, {"time": 2.7333, "curve": "stepped"}, {"time": 2.9667, "curve": [3.008, 0, 3.092, -5.24]}, {"time": 3.1333, "value": -5.24, "curve": [3.175, -5.24, 3.258, 0]}, {"time": 3.3}]}}}, "transform": {"slots": {"AntlersLeft": {"rgba2": [{"time": 6.0333, "light": "ffffffff", "dark": "000000"}, {"time": 7.1333, "light": "ff0000ff", "dark": "000000"}]}, "AntlersRight": {"rgba2": [{"time": 6.0333, "light": "ffffffff", "dark": "000000"}, {"time": 7.1333, "light": "ff0000ff", "dark": "000000"}]}, "BehindCloak": {"rgba2": [{"time": 6.0333, "light": "ffffffff", "dark": "000000"}, {"time": 7.1333, "light": "ff0000ff", "dark": "000000"}], "attachment": [{"time": 0.9667, "name": "BehindCloak_Floating"}, {"time": 6.0667, "name": "BehindCloak"}]}, "Cloak": {"rgba2": [{"time": 6.0333, "light": "ffffffff", "dark": "000000"}, {"time": 7.1333, "light": "ff0000ff", "dark": "000000"}]}, "Cloak_Left": {"rgba2": [{"time": 6.0333, "light": "ffffffff", "dark": "000000"}, {"time": 7.1333, "light": "ff0000ff", "dark": "000000"}]}, "Cloak_Right": {"rgba2": [{"time": 6.0333, "light": "ffffffff", "dark": "000000"}, {"time": 7.1333, "light": "ff0000ff", "dark": "000000"}]}, "CROWN": {"rgba2": [{"time": 6.0333, "light": "ffffffff", "dark": "000000"}, {"time": 7.1333, "light": "ff0000ff", "dark": "000000"}]}, "CrownEye": {"rgba2": [{"time": 6.0333, "light": "ffffffff", "dark": "000000"}, {"time": 7.1333, "light": "ff0000ff", "dark": "000000"}]}, "CrownGrass": {"rgba2": [{"time": 6.0333, "light": "ffffffff", "dark": "000000"}, {"time": 7.1333, "light": "ff0000ff", "dark": "000000"}]}, "Face": {"attachment": [{"time": 0.3333, "name": "Face2"}, {"time": 0.6667, "name": "Face"}, {"time": 1, "name": "Face2"}, {"time": 1.3333, "name": "Face"}, {"time": 1.6667, "name": "Face2"}, {"time": 2, "name": "Face"}, {"time": 2.3333, "name": "Face2"}, {"time": 2.6667, "name": "Face"}, {"time": 3, "name": "Face2"}]}, "Hands_0": {"rgba2": [{"time": 6.0333, "light": "ffffffff", "dark": "000000"}, {"time": 7.1333, "light": "ff0000ff", "dark": "000000"}]}, "Hands_1": {"rgba2": [{"time": 6.0333, "light": "ffffffff", "dark": "000000"}, {"time": 7.1333, "light": "ff0000ff", "dark": "000000"}]}, "HEAD": {"rgba2": [{"time": 6.0333, "light": "ffffffff", "dark": "000000"}, {"time": 7.1333, "light": "ff0000ff", "dark": "000000"}]}, "Neck": {"rgba2": [{"time": 6.0333, "light": "ffffffff", "dark": "000000"}, {"time": 7.1333, "light": "ff0000ff", "dark": "000000"}]}, "Scarf": {"rgba2": [{"time": 6.0333, "light": "ffffffff", "dark": "000000"}, {"time": 7.1333, "light": "ff0000ff", "dark": "000000"}]}, "Symbol_1": {"rgba2": [{"time": 6.0333, "light": "ffffffff", "dark": "000000"}, {"time": 7.1333, "light": "ff0000ff", "dark": "000000"}]}, "Symbol_2": {"rgba2": [{"time": 6.0333, "light": "ffffffff", "dark": "000000"}, {"time": 7.1333, "light": "ff0000ff", "dark": "000000"}]}}, "bones": {"BodyBtm": {"scale": [{"curve": [0.175, 1, 0.079, 0.873, 0.175, 1, 0.079, 1.129]}, {"time": 0.7, "x": 0.873, "y": 1.129, "curve": [0.783, 0.873, 0.95, 1.055, 0.783, 1.129, 0.95, 0.938]}, {"time": 1.0333, "x": 1.055, "y": 0.938, "curve": [1.125, 1.055, 1.308, 0.89, 1.125, 0.938, 1.308, 1.138]}, {"time": 1.4, "x": 0.89, "y": 1.138, "curve": [1.592, 0.89, 1.975, 0.967, 1.592, 1.138, 1.975, 0.995]}, {"time": 2.1667, "x": 0.967, "y": 0.995, "curve": "stepped"}, {"time": 6.0667, "x": 0.967, "y": 0.995, "curve": [6.117, 0.967, 6.217, 0.832, 6.117, 0.995, 6.217, 1.128]}, {"time": 6.2667, "x": 0.832, "y": 1.128, "curve": [6.35, 0.832, 6.517, 0.894, 6.35, 1.128, 6.517, 1.081]}, {"time": 6.6, "x": 0.894, "y": 1.081, "curve": [6.642, 0.894, 6.725, 0.871, 6.642, 1.081, 6.725, 1.098]}, {"time": 6.7667, "x": 0.871, "y": 1.098, "curve": [6.95, 0.871, 7.41, 0.803, 6.95, 1.098, 7.41, 1.177]}, {"time": 7.5, "x": 0.765, "y": 1.221}]}, "MAIN": {"translate": [{"time": 0.7, "curve": [0.85, 0, 0.885, 0, 0.85, 0, 0.885, 216.4]}, {"time": 1.3, "y": 216.4, "curve": [1.467, 0, 1.8, 0, 1.467, 216.4, 1.8, 87.13]}, {"time": 1.9667, "y": 87.13, "curve": [2.133, 0, 2.467, 0, 2.133, 87.13, 2.467, 152.1]}, {"time": 2.6333, "y": 152.1, "curve": [2.8, 0, 3.133, 0, 2.8, 152.1, 3.133, 108.19]}, {"time": 3.3, "y": 108.19, "curve": [3.483, 0, 3.85, 0, 3.483, 108.19, 3.85, 152.1]}, {"time": 4.0333, "y": 152.1, "curve": [4.217, 0, 4.583, 0, 4.217, 152.1, 4.583, 108.19]}, {"time": 4.7667, "y": 108.19, "curve": [5, 0, 5.467, 0, 5, 108.19, 5.467, 165.11]}, {"time": 5.7, "y": 165.11, "curve": [5.792, 0, 6.02, 0, 5.792, 165.11, 6.02, 93.83]}, {"time": 6.0667}]}, "CrownEye": {"scale": [{"time": 0.3, "curve": [0.317, 1, 0.35, 0.815, 0.317, 1, 0.35, 1.097]}, {"time": 0.3667, "x": 0.815, "y": 1.097, "curve": [0.4, 0.815, 0.467, 1, 0.4, 1.097, 0.467, 1]}, {"time": 0.5, "curve": "stepped"}, {"time": 1.0667, "curve": [1.092, 1, 1.142, 0.815, 1.092, 1, 1.142, 1.097]}, {"time": 1.1667, "x": 0.815, "y": 1.097, "curve": [1.2, 0.815, 1.267, 1, 1.2, 1.097, 1.267, 1]}, {"time": 1.3, "curve": "stepped"}, {"time": 3.7333, "curve": [3.758, 1, 3.808, 0.815, 3.758, 1, 3.808, 1.097]}, {"time": 3.8333, "x": 0.815, "y": 1.097, "curve": [3.867, 0.815, 3.933, 1, 3.867, 1.097, 3.933, 1]}, {"time": 3.9667, "curve": "stepped"}, {"time": 4.5667, "curve": [4.592, 1, 4.642, 0.815, 4.592, 1, 4.642, 1.097]}, {"time": 4.6667, "x": 0.815, "y": 1.097, "curve": [4.7, 0.815, 4.767, 1, 4.7, 1.097, 4.767, 1]}, {"time": 4.8}]}, "Eyes": {"translate": [{"x": 5.65, "y": -0.19, "curve": [0.076, 10.77, 0.147, 14.55, 0.076, -0.37, 0.147, -0.5]}, {"time": 0.2, "x": 14.55, "y": -0.5, "curve": [0.325, 14.55, 0.575, -54.93, 0.325, -0.5, 0.575, 2.53]}, {"time": 0.7, "x": -54.93, "y": 2.53, "curve": [0.825, -54.93, 1.075, 58.18, 0.825, 2.53, 1.075, -2.05]}, {"time": 1.2, "x": 58.18, "y": -2.05}, {"time": 1.3333, "x": 58.55, "y": 7.87}, {"time": 1.4, "x": 58.09, "y": -5.69}, {"time": 1.4667, "x": 58.25, "y": 1.39}, {"time": 1.5333, "x": 58.09, "y": -5.69}, {"time": 1.6, "x": 58.25, "y": 1.39}, {"time": 1.6667, "x": 58.09, "y": -5.69}, {"time": 1.7333, "x": 58.25, "y": 1.39}, {"time": 1.8, "x": 58.09, "y": -5.69}, {"time": 1.8667, "x": 58.25, "y": 1.39}, {"time": 1.9333, "x": 58.09, "y": -5.69}, {"time": 2, "x": 58.25, "y": 1.39}, {"time": 2.0667, "x": 58.09, "y": -5.69}, {"time": 2.1333, "x": 58.25, "y": 1.39}, {"time": 2.2, "x": 58.09, "y": -5.69}, {"time": 2.2667, "x": 58.55, "y": 7.87}, {"time": 2.3333, "x": 57.84, "y": -10.54}, {"time": 2.4, "x": 58.55, "y": 7.87}, {"time": 2.4667, "x": 57.84, "y": -10.54}, {"time": 2.5333, "x": 58.55, "y": 7.87}, {"time": 2.6, "x": 57.84, "y": -10.54}, {"time": 2.6667, "x": 58.55, "y": 7.87}, {"time": 2.7333, "x": 57.84, "y": -10.54}, {"time": 2.8, "x": 58.55, "y": 7.87}, {"time": 2.8667, "x": 57.84, "y": -10.54}, {"time": 2.9333, "x": 58.55, "y": 7.87}, {"time": 3, "x": 57.84, "y": -10.54}, {"time": 3.0667, "x": 58.55, "y": 7.87}, {"time": 3.1333, "x": 57.84, "y": -10.54}, {"time": 3.2, "x": 58.55, "y": 7.87}, {"time": 3.2667, "x": 57.84, "y": -10.54}, {"time": 3.3333, "x": 58.55, "y": 7.87}, {"time": 3.4, "x": 57.84, "y": -10.54}, {"time": 3.7333, "x": 14.91, "y": -37.33}, {"time": 3.8, "x": 14.2, "y": -55.74}, {"time": 3.8667, "x": 14.91, "y": -37.33}, {"time": 3.9333, "x": 14.2, "y": -55.74}, {"time": 4, "x": 14.91, "y": -37.33}, {"time": 4.0667, "x": 14.2, "y": -55.74}, {"time": 4.1333, "x": 14.91, "y": -37.33}, {"time": 4.2, "x": 14.2, "y": -55.74}, {"time": 4.2667, "x": 14.91, "y": -37.33}, {"time": 4.3333, "x": 14.2, "y": -55.74}, {"time": 4.4, "x": 14.91, "y": -37.33}, {"time": 4.4667, "x": 14.2, "y": -55.74}, {"time": 4.5333, "x": 14.91, "y": -37.33}, {"time": 4.6, "x": 14.2, "y": -55.74}, {"time": 4.6667, "x": 14.91, "y": -37.33}, {"time": 4.7333, "x": 14.2, "y": -55.74}, {"time": 4.8, "x": 14.91, "y": -37.33}, {"time": 4.8667, "x": 14.2, "y": -55.74}, {"time": 5.1333, "x": -14.85, "y": 40.98}, {"time": 5.2, "x": -14.13, "y": 59.39}, {"time": 5.2667, "x": -14.85, "y": 40.98}, {"time": 5.3333, "x": -14.13, "y": 59.39}, {"time": 5.4, "x": -14.85, "y": 40.98}, {"time": 5.4667, "x": -14.13, "y": 59.39}, {"time": 5.5333, "x": -14.85, "y": 40.98}, {"time": 5.6, "x": -14.13, "y": 59.39}, {"time": 6.0333, "x": 57.84, "y": -10.54, "curve": [6.117, 57.84, 6.283, -82.92, 6.117, -10.54, 6.283, -3.42]}, {"time": 6.3667, "x": -82.92, "y": -3.42, "curve": [6.467, -82.92, 6.667, -66.74, 6.467, -3.42, 6.667, -4.28]}, {"time": 6.7667, "x": -66.74, "y": -4.28}]}, "Head": {"rotate": [{"time": 3.4333, "curve": [3.483, 0, 3.583, -25.28]}, {"time": 3.6333, "value": -25.28, "curve": "stepped"}, {"time": 3.7667, "value": -25.28, "curve": [4.495, -25.28, 4.592, -14.05]}, {"time": 4.8667, "value": -14.05, "curve": [4.933, -14.05, 5.067, 32.92]}, {"time": 5.1333, "value": 32.92, "curve": "stepped"}, {"time": 5.6333, "value": 32.92}, {"time": 6.2}], "translate": [{"curve": [0.175, 0, 0.072, -56.34, 0.175, 0, 0.072, 0.57]}, {"time": 0.7, "x": -57.49, "y": 0.58, "curve": [0.867, -57.49, 1.2, 0, 0.867, 0.58, 1.2, 0]}, {"time": 1.3667, "curve": [2.618, 0, 2.41, 1.08, 2.618, 0, 2.41, -0.01]}, {"time": 4.6333, "x": 1.67, "y": -0.02, "curve": [4.92, 0.72, 5.321, 0.05, 4.92, -0.01, 5.321, 0]}, {"time": 5.9}, {"time": 6.2333, "x": -91.06, "y": 0.64, "curve": "stepped"}, {"time": 6.7667, "x": -91.06, "y": 0.64, "curve": [6.95, -91.06, 7.41, -131.53, 6.95, 0.64, 7.41, 0.81]}, {"time": 7.5, "x": -154.52, "y": 0.9}], "scale": [{"time": 3.5333, "curve": [3.558, 1, 3.608, 1.183, 3.558, 1, 3.608, 0.994]}, {"time": 3.6333, "x": 1.183, "y": 0.994, "curve": [3.667, 1.183, 3.733, 0.944, 3.667, 0.994, 3.733, 1.099]}, {"time": 3.7667, "x": 0.944, "y": 1.099, "curve": [3.8, 0.944, 3.867, 1, 3.8, 1.099, 3.867, 1]}, {"time": 3.9, "curve": "stepped"}, {"time": 4.8667, "curve": [4.892, 1, 4.942, 1.183, 4.892, 1, 4.942, 0.994]}, {"time": 4.9667, "x": 1.183, "y": 0.994, "curve": [5, 1.183, 5.067, 0.944, 5, 0.994, 5.067, 1.099]}, {"time": 5.1, "x": 0.944, "y": 1.099, "curve": [5.133, 0.944, 5.2, 1, 5.133, 1.099, 5.2, 1]}, {"time": 5.2333, "curve": "stepped"}, {"time": 5.6, "curve": [5.617, 1, 5.65, 0.958, 5.617, 1, 5.65, 1.077]}, {"time": 5.6667, "x": 0.958, "y": 1.077, "curve": [5.683, 0.958, 5.717, 1, 5.683, 1.077, 5.717, 1]}, {"time": 5.7333, "curve": [5.75, 1, 5.783, 0.958, 5.75, 1, 5.783, 1.077]}, {"time": 5.8, "x": 0.958, "y": 1.077, "curve": [5.817, 0.958, 5.85, 1, 5.817, 1.077, 5.85, 1]}, {"time": 5.8667, "curve": [5.883, 1, 5.917, 0.958, 5.883, 1, 5.917, 1.077]}, {"time": 5.9333, "x": 0.958, "y": 1.077, "curve": [5.95, 0.958, 5.983, 1, 5.95, 1.077, 5.983, 1]}, {"time": 6, "curve": [6.017, 1, 6.05, 0.958, 6.017, 1, 6.05, 1.077]}, {"time": 6.0667, "x": 0.958, "y": 1.077, "curve": [6.083, 0.958, 6.117, 1, 6.083, 1.077, 6.117, 1]}, {"time": 6.1333, "curve": [6.15, 1, 6.183, 0.958, 6.15, 1, 6.183, 1.077]}, {"time": 6.2, "x": 0.958, "y": 1.077, "curve": [6.217, 0.958, 6.25, 1, 6.217, 1.077, 6.25, 1]}, {"time": 6.2667, "curve": [6.283, 1, 6.317, 0.958, 6.283, 1, 6.317, 1.077]}, {"time": 6.3333, "x": 0.958, "y": 1.077, "curve": [6.35, 0.958, 6.383, 1, 6.35, 1.077, 6.383, 1]}, {"time": 6.4, "curve": [6.417, 1, 6.45, 0.958, 6.417, 1, 6.45, 1.077]}, {"time": 6.4667, "x": 0.958, "y": 1.077, "curve": [6.483, 0.958, 6.517, 1, 6.483, 1.077, 6.517, 1]}, {"time": 6.5333, "curve": [6.55, 1, 6.583, 0.958, 6.55, 1, 6.583, 1.077]}, {"time": 6.6, "x": 0.958, "y": 1.077, "curve": [6.617, 0.958, 6.65, 1, 6.617, 1.077, 6.65, 1]}, {"time": 6.6667, "curve": [6.683, 1, 6.717, 0.958, 6.683, 1, 6.717, 1.077]}, {"time": 6.7333, "x": 0.958, "y": 1.077, "curve": [6.75, 0.958, 6.783, 1, 6.75, 1.077, 6.783, 1]}, {"time": 6.8, "curve": [6.817, 1, 6.85, 0.958, 6.817, 1, 6.85, 1.077]}, {"time": 6.8667, "x": 0.958, "y": 1.077, "curve": [6.883, 0.958, 6.917, 1, 6.883, 1.077, 6.917, 1]}, {"time": 6.9333, "curve": [6.95, 1, 6.983, 0.93, 6.95, 1, 6.983, 1.135]}, {"time": 7, "x": 0.93, "y": 1.135, "curve": [7.017, 0.93, 7.05, 1, 7.017, 1.135, 7.05, 1]}, {"time": 7.0667}, {"time": 7.1333, "x": 0.93, "y": 1.135, "curve": [7.15, 0.93, 7.183, 1, 7.15, 1.135, 7.183, 1]}, {"time": 7.2, "curve": [7.217, 1, 7.25, 0.878, 7.217, 1, 7.25, 1.207]}, {"time": 7.2667, "x": 0.878, "y": 1.207, "curve": [7.283, 0.878, 7.317, 1, 7.283, 1.207, 7.317, 1]}, {"time": 7.3333, "curve": [7.35, 1, 7.383, 0.878, 7.35, 1, 7.383, 1.207]}, {"time": 7.4, "x": 0.878, "y": 1.207, "curve": [7.417, 0.878, 7.45, 1, 7.417, 1.207, 7.45, 1]}, {"time": 7.4667}]}, "Hands_0": {"rotate": [{"value": 1.63}]}, "Hands_1": {"rotate": [{"value": -5.24}]}, "Antler6": {"rotate": [{"curve": [0.167, 0, 0.5, -11.36]}, {"time": 0.6667, "value": -11.36, "curve": [0.75, -11.36, 0.917, 14.75]}, {"time": 1, "value": 14.75, "curve": [1.167, 14.75, 1.5, -5.79]}, {"time": 1.6667, "value": -5.79, "curve": [1.833, -5.79, 2.167, 0]}, {"time": 2.3333, "curve": [2.5, 0, 2.833, -5.79]}, {"time": 3, "value": -5.79, "curve": [3.133, -5.79, 3.4, 0]}, {"time": 3.5333, "curve": "stepped"}, {"time": 5.6667, "curve": [5.775, 0, 5.992, -11.36]}, {"time": 6.1, "value": -11.36, "curve": [6.183, -11.36, 6.35, 14.75]}, {"time": 6.4333, "value": 14.75, "curve": [6.92, 14.75, 7.233, -11.36]}, {"time": 7.5, "value": -11.36}]}, "Antler1": {"rotate": [{"curve": [0.167, 0, 0.5, 14.28]}, {"time": 0.6667, "value": 14.28, "curve": [0.75, 14.28, 0.917, -9.97]}, {"time": 1, "value": -9.97, "curve": [1.167, -9.97, 1.5, 6.49]}, {"time": 1.6667, "value": 6.49, "curve": [1.833, 6.49, 2.167, 0]}, {"time": 2.3333, "curve": [2.5, 0, 2.833, 6.49]}, {"time": 3, "value": 6.49, "curve": [3.133, 6.49, 3.4, 0]}, {"time": 3.5333, "curve": "stepped"}, {"time": 5.6667, "curve": [5.775, 0, 5.992, 14.28]}, {"time": 6.1, "value": 14.28, "curve": [6.183, 14.28, 6.35, -9.97]}, {"time": 6.4333, "value": -9.97, "curve": [6.92, -9.97, 7.233, 14.28]}, {"time": 7.5, "value": 14.28}]}, "NeckLeaf5": {"rotate": [{"value": 0.17}]}, "BodyTop": {"scale": [{"x": 0.991, "y": 1.014, "curve": [0.175, 0.991, 0.079, 0.903, 0.175, 1.014, 0.079, 1.076]}, {"time": 0.7, "x": 0.903, "y": 1.076, "curve": [0.783, 0.903, 0.95, 1.09, 0.783, 1.076, 0.95, 0.894]}, {"time": 1.0333, "x": 1.09, "y": 0.894, "curve": [1.125, 1.09, 1.308, 0.92, 1.125, 0.894, 1.308, 1.085]}, {"time": 1.4, "x": 0.92, "y": 1.085, "curve": [1.592, 0.92, 1.975, 1, 1.592, 1.085, 1.975, 0.949]}, {"time": 2.1667, "y": 0.949, "curve": "stepped"}, {"time": 6.0667, "y": 0.949, "curve": [6.117, 1, 6.217, 0.86, 6.117, 0.949, 6.217, 1.076]}, {"time": 6.2667, "x": 0.86, "y": 1.076, "curve": [6.35, 0.86, 6.517, 0.908, 6.35, 1.076, 6.517, 1.053]}, {"time": 6.6, "x": 0.908, "y": 1.053, "curve": [6.642, 0.908, 6.725, 0.89, 6.642, 1.053, 6.725, 1.061]}, {"time": 6.7667, "x": 0.89, "y": 1.061, "curve": [6.95, 0.89, 7.41, 0.821, 6.95, 1.061, 7.41, 1.137]}, {"time": 7.5, "x": 0.782, "y": 1.18}]}, "Cloak_Right1": {"rotate": [{"curve": [0.167, 0, 0.141, 11.89]}, {"time": 0.6667, "value": 11.89, "curve": [0.75, 11.89, 0.737, 1.51]}, {"time": 1, "value": 1.51, "curve": [1.133, 1.51, 1.4, 9.05]}, {"time": 1.5333, "value": 9.05, "curve": [1.625, 9.05, 1.808, -0.37]}, {"time": 1.9, "value": -0.37, "curve": [1.992, -0.37, 2.175, 9.05]}, {"time": 2.2667, "value": 9.05, "curve": [2.358, 9.05, 2.542, -0.37]}, {"time": 2.6333, "value": -0.37, "curve": [2.742, -0.37, 2.958, 9.05]}, {"time": 3.0667, "value": 9.05, "curve": [3.175, 9.05, 3.392, -0.37]}, {"time": 3.5, "value": -0.37, "curve": [3.617, -0.37, 3.85, 9.05]}, {"time": 3.9667, "value": 9.05, "curve": [4.075, 9.05, 4.292, -0.37]}, {"time": 4.4, "value": -0.37, "curve": [4.508, -0.37, 4.725, 9.05]}, {"time": 4.8333, "value": 9.05, "curve": [4.942, 9.05, 5.158, -0.37]}, {"time": 5.2667, "value": -0.37, "curve": [5.375, -0.37, 5.592, 9.05]}, {"time": 5.7, "value": 9.05, "curve": [5.808, 9.05, 6.025, -0.37]}, {"time": 6.1333, "value": -0.37, "curve": [6.158, -0.37, 6.208, 0]}, {"time": 6.2333}], "scale": [{"curve": [0.167, 1, 0.141, 1.119, 0.167, 1, 0.141, 1]}, {"time": 0.6667, "x": 1.119, "curve": [0.75, 1.119, 0.737, 1, 0.75, 1, 0.737, 1]}, {"time": 1, "curve": "stepped"}, {"time": 6}, {"time": 6.2333, "x": 1.119, "curve": "stepped"}, {"time": 6.6667, "x": 1.119, "curve": [6.875, 1.119, 7.292, 1.25, 6.875, 1, 7.292, 1]}, {"time": 7.5, "x": 1.25}]}, "Cloak_Right2": {"rotate": [{"curve": [0.167, 0, 0.196, -6.12]}, {"time": 0.6667, "value": -6.12, "curve": [0.75, -6.12, 0.766, -12.16]}, {"time": 1, "value": -12.16, "curve": [1.183, -12.16, 1.55, 14.04]}, {"time": 1.7333, "value": 14.04, "curve": [1.825, 14.04, 2.008, -3.95]}, {"time": 2.1, "value": -3.95, "curve": [2.192, -3.95, 2.375, 5.47]}, {"time": 2.4667, "value": 5.47, "curve": [2.558, 5.47, 2.742, -3.95]}, {"time": 2.8333, "value": -3.95, "curve": [2.942, -3.95, 3.158, 17.47]}, {"time": 3.2667, "value": 17.47, "curve": [3.375, 17.47, 3.592, -3.95]}, {"time": 3.7, "value": -3.95, "curve": [3.808, -3.95, 4.025, 5.47]}, {"time": 4.1333, "value": 5.47, "curve": [4.242, 5.47, 4.458, -3.95]}, {"time": 4.5667, "value": -3.95, "curve": [4.675, -3.95, 4.892, 5.47]}, {"time": 5, "value": 5.47, "curve": [5.108, 5.47, 5.325, -3.95]}, {"time": 5.4333, "value": -3.95, "curve": [5.542, -3.95, 5.758, 5.47]}, {"time": 5.8667, "value": 5.47, "curve": [5.932, 5.47, 6.037, 2]}, {"time": 6.1333, "value": -0.67, "curve": [6.172, -0.3, 6.207, 0]}, {"time": 6.2333}]}, "Cloak_Right3": {"rotate": [{"curve": [0.167, 0, 0.196, -5.28]}, {"time": 0.6667, "value": -5.28, "curve": [0.75, -5.28, 0.766, 7.88]}, {"time": 1, "value": 7.88, "curve": [1.075, 7.88, 1.225, 3.53]}, {"time": 1.3, "value": 3.53}, {"time": 1.7667, "value": 25.27, "curve": [1.858, 25.27, 2.042, -16.63]}, {"time": 2.1333, "value": -16.63, "curve": [2.225, -16.63, 2.408, 14.6]}, {"time": 2.5, "value": 14.6, "curve": [2.592, 14.6, 2.775, -16.63]}, {"time": 2.8667, "value": -16.63, "curve": [2.975, -16.63, 3.192, 24.05]}, {"time": 3.3, "value": 24.05, "curve": [3.408, 24.05, 3.625, -16.63]}, {"time": 3.7333, "value": -16.63, "curve": [3.842, -16.63, 4.058, 14.6]}, {"time": 4.1667, "value": 14.6, "curve": [4.275, 14.6, 4.492, -16.63]}, {"time": 4.6, "value": -16.63, "curve": [4.708, -16.63, 4.925, 14.6]}, {"time": 5.0333, "value": 14.6, "curve": [5.142, 14.6, 5.358, -16.63]}, {"time": 5.4667, "value": -16.63, "curve": [5.575, -16.63, 5.792, 40.59]}, {"time": 5.9, "value": 40.59, "curve": [5.958, 40.59, 6.047, 18.13]}, {"time": 6.1333, "value": -2.6, "curve": [6.171, -1.25, 6.208, 0]}, {"time": 6.2333}]}, "Cloak_Right4": {"rotate": [{"curve": [0.167, 0, 0.196, 1.16]}, {"time": 0.6667, "value": 1.16, "curve": [0.75, 1.16, 0.766, 10.19]}, {"time": 1, "value": 10.19, "curve": [1.1, 10.19, 1.3, -10.15]}, {"time": 1.4, "value": -10.15}, {"time": 1.9, "value": 17.5, "curve": [1.992, 17.5, 2.175, -14.64]}, {"time": 2.2667, "value": -14.64, "curve": [2.358, -14.64, 2.542, 17.5]}, {"time": 2.6333, "value": 17.5, "curve": [2.662, 17.5, 2.696, 16.42]}, {"time": 2.7333, "value": 14.69, "curve": [2.84, 6.31, 2.986, -14.64]}, {"time": 3.0667, "value": -14.64, "curve": [3.175, -14.64, 3.392, 48.71]}, {"time": 3.5, "value": 48.71, "curve": [3.617, 48.71, 3.85, -14.64]}, {"time": 3.9667, "value": -14.64, "curve": [4.075, -14.64, 4.292, 17.5]}, {"time": 4.4, "value": 17.5, "curve": [4.508, 17.5, 4.725, -14.64]}, {"time": 4.8333, "value": -14.64, "curve": [4.942, -14.64, 5.158, 17.5]}, {"time": 5.2667, "value": 17.5, "curve": [5.375, 17.5, 5.592, -14.64]}, {"time": 5.7, "value": -14.64, "curve": [5.808, -14.64, 6.025, 17.5]}, {"time": 6.1333, "value": 17.5, "curve": [6.158, 17.5, 6.208, 0]}, {"time": 6.2333}]}, "Cloak_Left1": {"rotate": [{"curve": [0.167, 0, 0.141, -7.62]}, {"time": 0.6667, "value": -7.62, "curve": [0.75, -7.62, 0.737, 3.13]}, {"time": 1, "value": 3.13, "curve": [1.133, 3.13, 1.4, -9.48]}, {"time": 1.5333, "value": -9.48, "curve": [1.625, -9.48, 1.808, 3.09]}, {"time": 1.9, "value": 3.09, "curve": [1.992, 3.09, 2.175, -9.48]}, {"time": 2.2667, "value": -9.48, "curve": [2.358, -9.48, 2.542, 3.09]}, {"time": 2.6333, "value": 3.09, "curve": [2.742, 3.09, 2.958, -9.48]}, {"time": 3.0667, "value": -9.48, "curve": [3.175, -9.48, 3.392, 3.09]}, {"time": 3.5, "value": 3.09, "curve": [3.617, 3.09, 3.85, -9.48]}, {"time": 3.9667, "value": -9.48, "curve": [4.075, -9.48, 4.292, 3.09]}, {"time": 4.4, "value": 3.09, "curve": [4.508, 3.09, 4.725, -9.48]}, {"time": 4.8333, "value": -9.48, "curve": [4.942, -9.48, 5.158, 3.09]}, {"time": 5.2667, "value": 3.09, "curve": [5.375, 3.09, 5.592, -9.48]}, {"time": 5.7, "value": -9.48, "curve": [5.808, -9.48, 6.025, 3.09]}, {"time": 6.1333, "value": 3.09, "curve": [6.158, 3.09, 6.208, 0]}, {"time": 6.2333}], "scale": [{"curve": [0.167, 1, 0.141, 1.119, 0.167, 1, 0.141, 1]}, {"time": 0.6667, "x": 1.119, "curve": [0.75, 1.119, 0.737, 1, 0.75, 1, 0.737, 1]}, {"time": 1, "curve": "stepped"}, {"time": 6}, {"time": 6.2333, "x": 1.119, "curve": "stepped"}, {"time": 6.6667, "x": 1.119, "curve": [6.875, 1.119, 7.292, 1.25, 6.875, 1, 7.292, 1]}, {"time": 7.5, "x": 1.25}]}, "Cloak_Left2": {"rotate": [{"curve": [0.167, 0, 0.196, 2.33]}, {"time": 0.6667, "value": 2.33, "curve": [0.75, 2.33, 0.766, 3.62]}, {"time": 1, "value": 3.62, "curve": [1.183, 3.62, 1.55, -15.32]}, {"time": 1.7333, "value": -15.32, "curve": [1.825, -15.32, 2.008, 5.94]}, {"time": 2.1, "value": 5.94, "curve": [2.192, 5.94, 2.375, -6.63]}, {"time": 2.4667, "value": -6.63, "curve": [2.558, -6.63, 2.742, 5.94]}, {"time": 2.8333, "value": 5.94, "curve": [2.942, 5.94, 3.158, -16.77]}, {"time": 3.2667, "value": -16.77, "curve": [3.375, -16.77, 3.592, 5.94]}, {"time": 3.7, "value": 5.94, "curve": [3.808, 5.94, 4.025, -6.63]}, {"time": 4.1333, "value": -6.63, "curve": [4.242, -6.63, 4.458, 5.94]}, {"time": 4.5667, "value": 5.94, "curve": [4.675, 5.94, 4.892, -6.63]}, {"time": 5, "value": -6.63, "curve": [5.108, -6.63, 5.325, 5.94]}, {"time": 5.4333, "value": 5.94, "curve": [5.542, 5.94, 5.758, -6.63]}, {"time": 5.8667, "value": -6.63, "curve": [5.932, -6.63, 6.037, -1.99]}, {"time": 6.1333, "value": 1.57, "curve": [6.172, 0.7, 6.207, 0]}, {"time": 6.2333}]}, "Cloak_Left3": {"rotate": [{"curve": [0.167, 0, 0.196, 4.81]}, {"time": 0.6667, "value": 4.81, "curve": [0.75, 4.81, 0.766, -10.23]}, {"time": 1, "value": -10.23, "curve": [1.075, -10.23, 1.225, -1.56]}, {"time": 1.3, "value": -1.56}, {"time": 1.7667, "value": -28.29, "curve": [1.858, -28.29, 2.042, 10.98]}, {"time": 2.1333, "value": 10.98, "curve": [2.225, 10.98, 2.408, -17.68]}, {"time": 2.5, "value": -17.68, "curve": [2.592, -17.68, 2.775, 10.98]}, {"time": 2.8667, "value": 10.98, "curve": [2.975, 10.98, 3.192, -28.24]}, {"time": 3.3, "value": -28.24, "curve": [3.408, -28.24, 3.625, 10.98]}, {"time": 3.7333, "value": 10.98, "curve": [3.842, 10.98, 4.058, -17.68]}, {"time": 4.1667, "value": -17.68, "curve": [4.275, -17.68, 4.492, 10.98]}, {"time": 4.6, "value": 10.98, "curve": [4.708, 10.98, 4.925, -17.68]}, {"time": 5.0333, "value": -17.68, "curve": [5.142, -17.68, 5.358, 10.98]}, {"time": 5.4667, "value": 10.98, "curve": [5.575, 10.98, 5.792, -38.78]}, {"time": 5.9, "value": -38.78, "curve": [5.958, -38.78, 6.047, -19.6]}, {"time": 6.1333, "value": -1.89, "curve": [6.171, -0.91, 6.208, 0]}, {"time": 6.2333}]}, "Cloak_Left4": {"rotate": [{"time": 0.6667, "curve": [0.75, 0, 0.766, 0.05]}, {"time": 1, "value": 0.05, "curve": [1.125, 0.05, 1.375, 13.26]}, {"time": 1.5, "value": 13.26}, {"time": 2, "value": -19.31, "curve": [2.092, -19.31, 2.275, 15.26]}, {"time": 2.3667, "value": 15.26, "curve": [2.458, 15.26, 2.642, -19.31]}, {"time": 2.7333, "value": -19.31, "curve": [2.842, -19.31, 3.058, 15.26]}, {"time": 3.1667, "value": 15.26, "curve": [3.265, 15.26, 3.454, -25.1]}, {"time": 3.5667, "value": -31.31, "curve": [3.578, -23.52, 3.59, -19.31]}, {"time": 3.6, "value": -19.31, "curve": [3.717, -19.31, 3.95, 15.26]}, {"time": 4.0667, "value": 15.26, "curve": [4.175, 15.26, 4.392, -19.31]}, {"time": 4.5, "value": -19.31, "curve": [4.608, -19.31, 4.825, 15.26]}, {"time": 4.9333, "value": 15.26, "curve": [5.042, 15.26, 5.258, -19.31]}, {"time": 5.3667, "value": -19.31, "curve": [5.475, -19.31, 5.692, 15.26]}, {"time": 5.8, "value": 15.26, "curve": [5.883, 15.26, 6.05, -19.31]}, {"time": 6.1333, "value": -19.31, "curve": [6.158, -19.31, 6.208, 0]}, {"time": 6.2333}]}, "CloakWave6": {"translate": [{"time": 1.0333, "curve": [1.175, 0, 1.458, 91.56, 1.175, 0, 1.458, -0.79]}, {"time": 1.6, "x": 91.56, "y": -0.79, "curve": [1.717, 91.56, 1.95, -53.67, 1.717, -0.79, 1.95, 0.46]}, {"time": 2.0667, "x": -53.67, "y": 0.46, "curve": [2.175, -53.67, 2.392, 91.56, 2.175, 0.46, 2.392, -0.79]}, {"time": 2.5, "x": 91.56, "y": -0.79, "curve": [2.617, 91.56, 2.85, -53.67, 2.617, -0.79, 2.85, 0.46]}, {"time": 2.9667, "x": -53.67, "y": 0.46, "curve": [3.083, -53.67, 3.317, 91.56, 3.083, 0.46, 3.317, -0.79]}, {"time": 3.4333, "x": 91.56, "y": -0.79, "curve": [3.542, 91.56, 3.758, -53.67, 3.542, -0.79, 3.758, 0.46]}, {"time": 3.8667, "x": -53.67, "y": 0.46, "curve": [3.983, -53.67, 4.217, 91.56, 3.983, 0.46, 4.217, -0.79]}, {"time": 4.3333, "x": 91.56, "y": -0.79, "curve": [4.442, 91.56, 4.658, -53.67, 4.442, -0.79, 4.658, 0.46]}, {"time": 4.7667, "x": -53.67, "y": 0.46, "curve": [4.883, -53.67, 5.117, 91.56, 4.883, 0.46, 5.117, -0.79]}, {"time": 5.2333, "x": 91.56, "y": -0.79, "curve": [5.342, 91.56, 5.558, -53.67, 5.342, -0.79, 5.558, 0.46]}, {"time": 5.6667, "x": -53.67, "y": 0.46, "curve": [5.783, -53.67, 6.017, 91.56, 5.783, 0.46, 6.017, -0.79]}, {"time": 6.1333, "x": 91.56, "y": -0.79, "curve": [6.183, 91.56, 6.283, 0, 6.183, -0.79, 6.283, 0]}, {"time": 6.3333}]}, "CloakWave5": {"translate": [{"time": 0.6333}, {"time": 0.9667, "x": -87.08, "y": 10.16}, {"time": 1.3, "curve": [1.417, 0, 1.65, 91.56, 1.417, 0, 1.65, -0.79]}, {"time": 1.7667, "x": 91.56, "y": -0.79, "curve": [1.883, 91.56, 2.117, -53.67, 1.883, -0.79, 2.117, 0.46]}, {"time": 2.2333, "x": -53.67, "y": 0.46, "curve": [2.342, -53.67, 2.558, 91.56, 2.342, 0.46, 2.558, -0.79]}, {"time": 2.6667, "x": 91.56, "y": -0.79, "curve": [2.783, 91.56, 3.017, -53.67, 2.783, -0.79, 3.017, 0.46]}, {"time": 3.1333, "x": -53.67, "y": 0.46, "curve": [3.25, -53.67, 3.483, 91.56, 3.25, 0.46, 3.483, -0.79]}, {"time": 3.6, "x": 91.56, "y": -0.79, "curve": [3.708, 91.56, 3.925, -53.67, 3.708, -0.79, 3.925, 0.46]}, {"time": 4.0333, "x": -53.67, "y": 0.46, "curve": [4.15, -53.67, 4.383, 91.56, 4.15, 0.46, 4.383, -0.79]}, {"time": 4.5, "x": 91.56, "y": -0.79, "curve": [4.617, 91.56, 4.85, -53.67, 4.617, -0.79, 4.85, 0.46]}, {"time": 4.9667, "x": -53.67, "y": 0.46, "curve": [5.075, -53.67, 5.292, 91.56, 5.075, 0.46, 5.292, -0.79]}, {"time": 5.4, "x": 91.56, "y": -0.79, "curve": [5.517, 91.56, 5.75, -53.67, 5.517, -0.79, 5.75, 0.46]}, {"time": 5.8667, "x": -53.67, "y": 0.46, "curve": [5.925, -53.67, 6.042, 0, 5.925, 0.46, 6.042, 0]}, {"time": 6.1}], "scale": [{"time": 4.3667}]}, "CloakWave4": {"translate": [{"time": 0.7333}, {"time": 0.9667, "x": -130.77, "y": -1.8}, {"time": 1.4667, "curve": [1.583, 0, 1.817, 91.56, 1.583, 0, 1.817, -0.79]}, {"time": 1.9333, "x": 91.56, "y": -0.79, "curve": [2.05, 91.56, 2.283, -53.67, 2.05, -0.79, 2.283, 0.46]}, {"time": 2.4, "x": -53.67, "y": 0.46, "curve": [2.508, -53.67, 2.725, 91.56, 2.508, 0.46, 2.725, -0.79]}, {"time": 2.8333, "x": 91.56, "y": -0.79, "curve": [2.95, 91.56, 3.183, -53.67, 2.95, -0.79, 3.183, 0.46]}, {"time": 3.3, "x": -53.67, "y": 0.46, "curve": [3.408, -53.67, 3.625, 91.56, 3.408, 0.46, 3.625, -0.79]}, {"time": 3.7333, "x": 91.56, "y": -0.79, "curve": [3.842, 91.56, 4.058, -53.67, 3.842, -0.79, 4.058, 0.46]}, {"time": 4.1667, "x": -53.67, "y": 0.46, "curve": [4.283, -53.67, 4.517, 91.56, 4.283, 0.46, 4.517, -0.79]}, {"time": 4.6333, "x": 91.56, "y": -0.79, "curve": [4.742, 91.56, 4.958, -53.67, 4.742, -0.79, 4.958, 0.46]}, {"time": 5.0667, "x": -53.67, "y": 0.46, "curve": [5.183, -53.67, 5.417, 91.56, 5.183, 0.46, 5.417, -0.79]}, {"time": 5.5333, "x": 91.56, "y": -0.79, "curve": [5.65, 91.56, 5.883, -53.67, 5.65, -0.79, 5.883, 0.46]}, {"time": 6, "x": -53.67, "y": 0.46, "curve": [6.025, -53.67, 6.075, 0, 6.025, 0.46, 6.075, 0]}, {"time": 6.1}]}, "CloakWave3": {"translate": [{"time": 0.7333, "curve": [0.792, 0, 0.908, -130.78, 0.792, 0, 0.908, -1.8]}, {"time": 0.9667, "x": -130.78, "y": -1.8}, {"time": 1.3667, "curve": [1.483, 0, 1.717, 90.88, 1.483, 0, 1.717, -36.57]}, {"time": 1.8333, "x": 90.88, "y": -36.57, "curve": [1.95, 90.88, 2.183, -53.67, 1.95, -36.57, 2.183, 0.46]}, {"time": 2.3, "x": -53.67, "y": 0.46, "curve": [2.408, -53.67, 2.625, 90.88, 2.408, 0.46, 2.625, -36.57]}, {"time": 2.7333, "x": 90.88, "y": -36.57, "curve": [2.85, 90.88, 3.083, -53.67, 2.85, -36.57, 3.083, 0.46]}, {"time": 3.2, "x": -53.67, "y": 0.46, "curve": [3.308, -53.67, 3.525, 90.88, 3.308, 0.46, 3.525, -36.57]}, {"time": 3.6333, "x": 90.88, "y": -36.57, "curve": [3.75, 90.88, 3.983, -53.67, 3.75, -36.57, 3.983, 0.46]}, {"time": 4.1, "x": -53.67, "y": 0.46, "curve": [4.217, -53.67, 4.45, 90.88, 4.217, 0.46, 4.45, -36.57]}, {"time": 4.5667, "x": 90.88, "y": -36.57, "curve": [4.675, 90.88, 4.892, -53.67, 4.675, -36.57, 4.892, 0.46]}, {"time": 5, "x": -53.67, "y": 0.46, "curve": [5.117, -53.67, 5.35, 90.88, 5.117, 0.46, 5.35, -36.57]}, {"time": 5.4667, "x": 90.88, "y": -36.57, "curve": [5.583, 90.88, 5.817, -53.67, 5.583, -36.57, 5.817, 0.46]}, {"time": 5.9333, "x": -53.67, "y": 0.46, "curve": [5.975, -53.67, 6.058, 0, 5.975, 0.46, 6.058, 0]}, {"time": 6.1}]}, "CloakWave1": {"translate": [{"time": 0.6333, "curve": [0.733, 0, 0.933, -0.64, 0.733, 0, 0.933, -79.24]}, {"time": 1.0333, "x": -0.64, "y": -79.24, "curve": [1.15, -0.64, 1.383, 90.76, 1.15, -79.24, 1.383, -34.03]}, {"time": 1.5, "x": 90.76, "y": -34.03, "curve": [1.608, 90.76, 1.825, -53.67, 1.608, -34.03, 1.825, 0.46]}, {"time": 1.9333, "x": -53.67, "y": 0.46, "curve": [2.05, -53.67, 2.283, 90.76, 2.05, 0.46, 2.283, -34.03]}, {"time": 2.4, "x": 90.76, "y": -34.03, "curve": [2.517, 90.76, 2.75, -53.67, 2.517, -34.03, 2.75, 0.46]}, {"time": 2.8667, "x": -53.67, "y": 0.46, "curve": [2.975, -53.67, 3.192, 90.76, 2.975, 0.46, 3.192, -34.03]}, {"time": 3.3, "x": 90.76, "y": -34.03, "curve": [3.417, 90.76, 3.65, -53.67, 3.417, -34.03, 3.65, 0.46]}, {"time": 3.7667, "x": -53.67, "y": 0.46, "curve": [3.883, -53.67, 4.117, 90.76, 3.883, 0.46, 4.117, -34.03]}, {"time": 4.2333, "x": 90.76, "y": -34.03, "curve": [4.342, 90.76, 4.558, -53.67, 4.342, -34.03, 4.558, 0.46]}, {"time": 4.6667, "x": -53.67, "y": 0.46, "curve": [4.783, -53.67, 5.017, 90.76, 4.783, 0.46, 5.017, -34.03]}, {"time": 5.1333, "x": 90.76, "y": -34.03, "curve": [5.242, 90.76, 5.458, -53.67, 5.242, -34.03, 5.458, 0.46]}, {"time": 5.5667, "x": -53.67, "y": 0.46, "curve": [5.683, -53.67, 5.917, 90.76, 5.683, 0.46, 5.917, -34.03]}, {"time": 6.0333, "x": 90.76, "y": -34.03, "curve": [6.108, 90.76, 6.258, 0, 6.108, -34.03, 6.258, 0]}, {"time": 6.3333}]}, "CloakWave2": {"translate": [{"time": 0.6333, "curve": [0.714, 0, 0.86, -62.74, 0.714, 0, 0.86, -58.4]}, {"time": 0.9667, "x": -87.84, "y": -81.76, "curve": [1.054, -33.78, 1.134, 0, 1.054, -31.45, 1.134, 0]}, {"time": 1.2, "curve": [1.317, 0, 1.55, 90.73, 1.317, 0, 1.55, -40.25]}, {"time": 1.6667, "x": 90.73, "y": -40.25, "curve": [1.783, 90.73, 2.017, -53.67, 1.783, -40.25, 2.017, 0.46]}, {"time": 2.1333, "x": -53.67, "y": 0.46, "curve": [2.242, -53.67, 2.458, 90.73, 2.242, 0.46, 2.458, -40.25]}, {"time": 2.5667, "x": 90.73, "y": -40.25, "curve": [2.683, 90.73, 2.917, -53.67, 2.683, -40.25, 2.917, 0.46]}, {"time": 3.0333, "x": -53.67, "y": 0.46, "curve": [3.15, -53.67, 3.383, 90.73, 3.15, 0.46, 3.383, -40.25]}, {"time": 3.5, "x": 90.73, "y": -40.25, "curve": [3.608, 90.73, 3.825, -53.67, 3.608, -40.25, 3.825, 0.46]}, {"time": 3.9333, "x": -53.67, "y": 0.46, "curve": [4.05, -53.67, 4.283, 90.73, 4.05, 0.46, 4.283, -40.25]}, {"time": 4.4, "x": 90.73, "y": -40.25, "curve": [4.517, 90.73, 4.75, -53.67, 4.517, -40.25, 4.75, 0.46]}, {"time": 4.8667, "x": -53.67, "y": 0.46, "curve": [4.975, -53.67, 5.192, 90.73, 4.975, 0.46, 5.192, -40.25]}, {"time": 5.3, "x": 90.73, "y": -40.25, "curve": [5.417, 90.73, 5.65, -53.67, 5.417, -40.25, 5.65, 0.46]}, {"time": 5.7667, "x": -53.67, "y": 0.46, "curve": [5.85, -53.67, 6.017, 0, 5.85, 0.46, 6.017, 0]}, {"time": 6.1}]}, "CrownHolder": {"rotate": [{"time": 1.0667, "value": 90.86}], "translate": [{"time": 1.0667, "x": 4.38, "y": -51.36, "curve": [1.141, 2.86, 1.367, 2.44, 1.141, -6.32, 1.367, 6.13]}, {"time": 1.4667, "x": 2.44, "y": 6.13, "curve": [1.625, 2.44, 1.942, 2.25, 1.625, 6.13, 1.942, -39.01]}, {"time": 2.1, "x": 2.25, "y": -39.01, "curve": [2.267, 2.25, 2.6, 2.44, 2.267, -39.01, 2.6, 6.13]}, {"time": 2.7667, "x": 2.44, "y": 6.13, "curve": [2.933, 2.44, 3.267, 2.25, 2.933, 6.13, 3.267, -39.01]}, {"time": 3.4333, "x": 2.25, "y": -39.01, "curve": [3.617, 2.25, 3.983, 2.44, 3.617, -39.01, 3.983, 6.13]}, {"time": 4.1667, "x": 2.44, "y": 6.13, "curve": [4.35, 2.44, 4.717, 2.25, 4.35, 6.13, 4.717, -39.01]}, {"time": 4.9, "x": 2.25, "y": -39.01, "curve": [5.083, 2.25, 5.45, 2.44, 5.083, -39.01, 5.45, 6.13]}, {"time": 5.6333, "x": 2.44, "y": 6.13, "curve": [5.725, 2.44, 5.908, 5.6, 5.725, 6.13, 5.908, -39.01]}, {"time": 6, "x": 5.6, "y": -39.01, "curve": [6.158, 5.6, 6.59, 3.91, 6.158, -39.01, 6.59, -201.29]}, {"time": 6.6333, "x": 2.25, "y": -360.42}]}}, "transform": {"Crown": [{"time": 6.6667, "mixRotate": 0, "mixX": 0.253, "mixScaleX": 0, "mixShearY": 0}]}, "events": [{"time": 7.5, "name": "transform"}]}}}