{"skeleton": {"hash": "ESvrl+5u1LA", "spine": "4.1.24", "x": -109.1, "y": -10.24, "width": 227.78, "height": 259.51, "images": "./images/", "audio": ""}, "bones": [{"name": "root"}, {"name": "Main", "parent": "root"}, {"name": "BODY_1", "parent": "root"}, {"name": "BODY_2", "parent": "root"}, {"name": "BODY_3", "parent": "root"}, {"name": "HEAD", "parent": "Main", "x": 5.36, "y": 165.51}, {"name": "Antler_4", "parent": "HEAD", "length": 39.51, "rotation": 108.03, "x": -46.56, "y": 38.45}, {"name": "Antler_1", "parent": "HEAD", "length": 38.69, "rotation": 64.59, "x": 37.13, "y": 39}, {"name": "Antler_0", "parent": "HEAD", "length": 44.97, "rotation": 29.05, "x": 56.27, "y": -10.86}, {"name": "Antler_3", "parent": "HEAD", "length": 47.89, "rotation": 141.67, "x": -50.2, "y": -11.06}, {"name": "Grass_1", "parent": "Main", "length": 20.11, "rotation": 92.49, "x": 10.6, "y": 317.52}, {"name": "Grass_0", "parent": "Main", "length": 26.21, "rotation": -90, "x": 18.69, "y": 305.98}, {"name": "HeadInner", "parent": "Main", "x": 1.87, "y": -65.12}, {"name": "FACE", "parent": "Main", "x": -0.88, "y": 57.85, "color": "abe323ff"}, {"name": "Twig", "parent": "Main", "length": 18.29, "rotation": 35.54, "x": 244.29, "y": 139.87}, {"name": "Twig2", "parent": "BODY_1", "length": 25.64, "rotation": 53.9, "x": 40.12, "y": 118.58}, {"name": "Twig3", "parent": "BODY_2", "length": 36.04, "rotation": 53.37, "x": 1.84, "y": 67.89}, {"name": "BtmSpike_Left", "parent": "HEAD", "length": 37.77, "rotation": -175.1, "x": -76.27, "y": -71.39}, {"name": "BtmSpike_Right_Behind", "parent": "HEAD", "length": 40.45, "rotation": 1.4, "x": 63.49, "y": -66.22}, {"name": "warning", "parent": "root", "x": -7.59, "y": 260.92}, {"name": "EYE_LEFT_TOP", "parent": "FACE", "x": -81.33, "y": 122, "scaleX": 0.8138, "scaleY": 0.8138, "skin": true, "color": "abe323ff"}, {"name": "EYE_LEFT_BTM", "parent": "FACE", "x": -112.02, "y": 51, "skin": true, "color": "abe323ff"}, {"name": "EYE_RIGHT_BTM", "parent": "FACE", "x": 113.97, "y": 51, "skin": true, "color": "abe323ff"}, {"name": "EYE_RIGHT_TOP", "parent": "FACE", "x": 75.84, "y": 122, "scaleX": 0.8138, "scaleY": 0.8138, "skin": true, "color": "abe323ff"}, {"name": "MOUTH", "parent": "FACE", "x": -0.42, "y": 26.98, "skin": true, "color": "abe323ff"}, {"name": "Fleshball_1", "parent": "BODY_2", "x": -2.24, "y": 182.96, "skin": true}, {"name": "Fleshball_2", "parent": "BODY_2", "rotation": 54.08, "x": -36.86, "y": 67.15, "scaleX": 1.0507, "scaleY": 1.0507, "skin": true}, {"name": "Fleshball_3", "parent": "BODY_1", "x": 69.13, "y": 109.9, "skin": true}, {"name": "Fleshball_4", "parent": "BODY_2", "rotation": 61.03, "x": -74.18, "y": 163.85, "skin": true}, {"name": "Fleshball_5", "parent": "BODY_1", "rotation": -131.02, "x": -69.68, "y": 95.85, "skin": true}, {"name": "Fleshball_6", "parent": "BODY_2", "x": 67.72, "y": 92.57, "skin": true}, {"name": "Fleshball_7", "parent": "BODY_2", "x": 34.32, "y": 132.26, "skin": true}, {"name": "Fleshball_8", "parent": "BODY_2", "rotation": 157.83, "x": -51.35, "y": 124.51, "skin": true}, {"name": "Fleshball_9", "parent": "BODY_3", "x": 26.44, "y": 80.6, "scaleX": 1.0507, "scaleY": 1.0507, "skin": true}, {"name": "MOUTH_BTM", "parent": "FACE", "x": -1.37, "y": -5.46, "skin": true, "color": "abe323ff"}, {"name": "HeadFleshball_1", "parent": "HEAD", "x": -72.07, "y": 77.67, "skin": true}, {"name": "HeadFleshball_2", "parent": "HEAD", "x": -24.23, "y": 76.47, "skin": true}, {"name": "HeadFleshball_3", "parent": "HEAD", "x": 59.49, "y": 72.89, "skin": true}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parent": "HEAD", "length": 61, "rotation": 90, "x": -0.31, "y": 86.04, "skin": true}, {"name": "TurretMouth", "parent": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rotation": -90.71, "x": 75.38, "y": 1.18, "scaleX": 0.7088, "scaleY": 0.7088, "skin": true}, {"name": "HeadFleshball_1_Behind", "parent": "HEAD", "x": -63.7, "y": 93.22, "skin": true}, {"name": "HeadFleshball_3_Behind", "parent": "HEAD", "x": 69.06, "y": 103.98, "skin": true}, {"name": "SpawnBall", "parent": "root", "x": -1.07, "y": 53.03, "skin": true}, {"name": "MOUTH_CHASER", "parent": "FACE", "x": -0.42, "y": 26.98, "skin": true, "color": "abe323ff"}, {"name": "MOUTH_TOP", "parent": "FACE", "x": -4.86, "y": 63.22, "color": "abe323ff"}, {"name": "EyeGoopLeft", "parent": "FACE", "x": -101.94, "y": 74.3, "color": "abe323ff"}, {"name": "EyeGoopRight", "parent": "FACE", "x": 104.52, "y": 70.58, "scaleX": -1, "color": "abe323ff"}, {"name": "CrabLeg1", "parent": "Main", "length": 75.78, "rotation": -135.3, "x": -60.53, "y": 132.42}, {"name": "CrabLeg2", "parent": "CrabLeg1", "length": 76.42, "rotation": 45.18, "x": 76.68, "y": 0.52}, {"name": "CrabLeg3", "parent": "Main", "length": 54.38, "rotation": -140.5, "x": -43.65, "y": 95.59}, {"name": "CrabLeg4", "parent": "CrabLeg3", "length": 57.88, "rotation": 48.25, "x": 55.17, "y": 0.08}, {"name": "CrabLeg5", "parent": "Main", "length": 54.38, "rotation": 138.55, "x": 45.77, "y": 94.3, "scaleX": -1}, {"name": "CrabLeg6", "parent": "CrabLeg5", "length": 57.88, "rotation": 48.25, "x": 55.17, "y": 0.08}, {"name": "CrabLeg7", "parent": "Main", "length": 75.78, "rotation": 138.73, "x": 56.55, "y": 131.13, "scaleX": -1}, {"name": "CrabLeg8", "parent": "CrabLeg7", "length": 76.42, "rotation": 45.18, "x": 76.68, "y": 0.52}], "slots": [{"name": "Patroller,Chaser/Chaser Boss/FleshBall11", "bone": "Fleshball_8", "attachment": "Patroller,Chaser/Chaser Boss/FleshBall5"}, {"name": "Patroller,Chaser/Chaser Boss/FleshBall6", "bone": "Fleshball_6", "attachment": "Patroller,Chaser/Chaser Boss/FleshBall4"}, {"name": "Patroller,Chaser/Chaser Boss/FleshBall7", "bone": "Fleshball_7", "attachment": "Patroller,Chaser/Chaser Boss/FleshBall1"}, {"name": "Patroller,Chaser/Chaser Boss/FleshBall2", "bone": "Fleshball_2", "attachment": "Patroller,Chaser/Chaser Boss/FleshBall2"}, {"name": "Patroller,Chaser/Chaser Boss/FleshBall4", "bone": "Fleshball_4", "attachment": "Patroller,Chaser/Chaser Boss/FleshBall2"}, {"name": "Patroller,Chaser/Chaser Boss/FleshBall1", "bone": "Fleshball_1", "attachment": "Patroller,Chaser/Chaser Boss/FleshBall1"}, {"name": "Patroller,Chaser/Chaser Boss/FleshBall9", "bone": "Fleshball_9", "attachment": "Patroller,Chaser/Chaser Boss/FleshBall6"}, {"name": "Body_2", "bone": "BODY_3", "attachment": "Body_2"}, {"name": "Grass_0_back", "bone": "Grass_0", "attachment": "Patroller,Chaser/Patroller_images/Grass_0"}, {"name": "Body_1", "bone": "BODY_2", "attachment": "Body_1"}, {"name": "Patroller,Chaser/Chaser Boss/FleshBall3", "bone": "Fleshball_3", "attachment": "Patroller,Chaser/Chaser Boss/FleshBall3"}, {"name": "Patroller,Chaser/Chaser Boss/FleshBall5", "bone": "Fleshball_5", "attachment": "Patroller,Chaser/Chaser Boss/FleshBall3"}, {"name": "Body_0", "bone": "BODY_1", "attachment": "Body_0"}, {"name": "Anlter_4_Behind", "bone": "Antler_4", "attachment": "Patroller,Chaser/Patroller_images/Anlter_4"}, {"name": "Anlter_3_Behind", "bone": "Antler_3", "attachment": "Patroller,Chaser/Patroller_images/Anlter_3"}, {"name": "Anlter_1_Behind", "bone": "Antler_1", "attachment": "Patroller,Chaser/Patroller_images/Anlter_1"}, {"name": "Anlter_0_Behind", "bone": "Antler_0", "attachment": "Patroller,Chaser/Patroller_images/Anlter_0"}, {"name": "BtmSpike_Right_Behind", "bone": "BtmSpike_Right_Behind", "attachment": "Head_StabbyBit__1"}, {"name": "BtmSpike_Left_Behind", "bone": "BtmSpike_Left", "attachment": "Head_StabbyBit__0"}, {"name": "<PERSON><PERSON>,<PERSON>r/Pouncer/PouncerCrab_Leg1", "bone": "CrabLeg3", "attachment": "<PERSON><PERSON>,<PERSON>r/Pouncer/PouncerCrab_Leg1"}, {"name": "<PERSON><PERSON>,<PERSON>r/Pouncer/PouncerCrab_Leg3", "bone": "CrabLeg5", "attachment": "<PERSON><PERSON>,<PERSON>r/Pouncer/PouncerCrab_Leg1"}, {"name": "<PERSON><PERSON>,<PERSON>r/Pouncer/PouncerCrab_Leg2", "bone": "CrabLeg1", "attachment": "<PERSON><PERSON>,<PERSON>r/Pouncer/PouncerCrab_Leg2"}, {"name": "<PERSON><PERSON>,<PERSON>r/Pouncer/PouncerCrab_Leg4", "bone": "CrabLeg7", "attachment": "<PERSON><PERSON>,<PERSON>r/Pouncer/PouncerCrab_Leg2"}, {"name": "Head", "bone": "HEAD", "attachment": "Patroller,Chaser/Patroller_images/Head"}, {"name": "HeadFleshball_1_Behind", "bone": "HeadFleshball_1_Behind", "attachment": "Patroller,Chaser/Chaser Boss/FleshballHead_2"}, {"name": "HeadFleshball_3_Behind", "bone": "HeadFleshball_3_Behind", "attachment": "Patroller,Chaser/Chaser Boss/FleshballHead_3"}, {"name": "BtmSpike_Left_Front", "bone": "BtmSpike_Left", "attachment": "Head_StabbyBit__0"}, {"name": "Twig_0", "bone": "Twig", "attachment": "Patroller,Chaser/Patroller_images/Twig_0"}, {"name": "HeadInner", "bone": "HeadInner", "attachment": "Patroller,Chaser/Patroller_images/HeadInner"}, {"name": "Anlter_4_Front", "bone": "Antler_4", "attachment": "Patroller,Chaser/Patroller_images/Anlter_4"}, {"name": "Anlter_3_Front", "bone": "Antler_3", "attachment": "Patroller,Chaser/Patroller_images/Anlter_3"}, {"name": "Anlter_1_Front", "bone": "Antler_1", "attachment": "Patroller,Chaser/Patroller_images/Anlter_1"}, {"name": "Anlter_0_Front", "bone": "Antler_0", "attachment": "Patroller,Chaser/Patroller_images/Anlter_0"}, {"name": "Symbol", "bone": "FACE", "attachment": "Patroller,Chaser/Patroller_images/Symbol"}, {"name": "Eyes", "bone": "FACE", "attachment": "Patroller,Chaser/Chaser_images/Eyes"}, {"name": "BtmSpike_Right_Front", "bone": "BtmSpike_Right_Behind", "attachment": "Head_StabbyBit__1"}, {"name": "Grass_1", "bone": "Grass_1", "attachment": "Patroller,Chaser/Patroller_images/Grass_1"}, {"name": "Grass_0", "bone": "Grass_0", "attachment": "Patroller,Chaser/Patroller_images/Grass_0"}, {"name": "Other/WarningIcon", "bone": "warning"}, {"name": "EyeGoop1", "bone": "EyeGoopLeft"}, {"name": "EyeGoop4", "bone": "EyeGoopRight"}, {"name": "EyeGoop2", "bone": "EyeGoopLeft"}, {"name": "EyeGoop5", "bone": "EyeGoopRight"}, {"name": "EyeGoop3", "bone": "EyeGoopLeft"}, {"name": "EyeGoop6", "bone": "EyeGoopRight"}, {"name": "Patroller,Chaser/Chaser Boss/Tunneler_Ball", "bone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "attachment": "Patroller,Chaser/Chaser Boss/Tunneler_Ball"}, {"name": "HeadFleshball_1", "bone": "HeadFleshball_1", "attachment": "Patroller,Chaser/Chaser Boss/FleshballHead_2"}, {"name": "HeadFleshball_3", "bone": "HeadFleshball_3", "attachment": "Patroller,Chaser/Chaser Boss/FleshballHead_3"}, {"name": "HeadFleshball_2", "bone": "HeadFleshball_2", "attachment": "Patroller,Chaser/Chaser Boss/FleshballHead_1"}, {"name": "TurretMouth", "bone": "TurretMouth", "attachment": "Patroller,Chaser/Chaser Boss/Tunneler_Mouth"}, {"name": "<PERSON>ler,<PERSON>r/Pouncer <PERSON>/Mouth2", "bone": "MOUTH_CHASER", "attachment": "Mouth"}, {"name": "MouthBtm", "bone": "MOUTH_CHASER", "attachment": "Mouth"}, {"name": "Patroller,Chaser/Chaser Boss/FleshBall8", "bone": "SpawnBall"}, {"name": "EYE_LEFT_TOP", "bone": "EYE_LEFT_TOP", "attachment": "<PERSON>ler,<PERSON>r/Pouncer Boss/Eye_Normal"}, {"name": "EYE_LEFT_BTM", "bone": "EYE_LEFT_BTM", "attachment": "<PERSON>ler,<PERSON>r/Pouncer Boss/Eye_Normal"}, {"name": "EYE_RIGHT_TOP", "bone": "EYE_RIGHT_TOP", "attachment": "<PERSON>ler,<PERSON>r/Pouncer Boss/Eye_Normal"}, {"name": "EYE_RIGHT_BTM", "bone": "EYE_RIGHT_BTM", "attachment": "<PERSON>ler,<PERSON>r/Pouncer Boss/Eye_Normal"}, {"name": "Patroller,<PERSON>r/Pouncer Boss/Mouth", "bone": "MOUTH", "attachment": "Mouth"}, {"name": "explode", "bone": "SpawnBall"}], "transform": [{"name": "Grass_0", "order": 2, "bones": ["Grass_0"], "target": "FACE", "mixRotate": 0, "mixX": 0.7, "mixScaleX": 0.7, "mixShearY": 0}, {"name": "Grass_1", "order": 3, "bones": ["Grass_1"], "target": "FACE", "mixRotate": 0, "mixX": 0.6, "mixScaleX": 0.6, "mixShearY": 0}, {"name": "HEAD", "order": 1, "bones": ["HEAD"], "target": "FACE", "mixRotate": 0.7, "mixX": 0.5, "mixScaleX": 0.5, "mixShearY": 0}, {"name": "HeadInner", "bones": ["HeadInner"], "target": "FACE", "mixRotate": 0.7, "mixX": 0.7, "mixScaleX": 0.7, "mixShearY": 0}, {"name": "MOUTH_BTM", "order": 5, "bones": ["MOUTH_TOP"], "target": "MOUTH_BTM", "mixRotate": 0, "mixX": -0.403, "mixScaleX": 0, "mixShearY": 0}, {"name": "Twig", "order": 4, "bones": ["Twig"], "target": "FACE", "mixRotate": 0, "mixX": 0.7, "mixScaleX": 0.7, "mixShearY": 0}], "skins": [{"name": "default", "attachments": {"explode": {"Patroller,Chaser/Mini Chaser images/explode_00": {"y": 0.5, "width": 160, "height": 143}, "Patroller,Chaser/Mini Chaser images/explode_01": {"y": 0.5, "width": 160, "height": 143}, "Patroller,Chaser/Mini Chaser images/explode_02": {"y": 0.5, "width": 160, "height": 143}, "Patroller,Chaser/Mini Chaser images/explode_03": {"y": 0.5, "width": 160, "height": 143}, "Patroller,Chaser/Mini Chaser images/explode_04": {"y": 0.5, "width": 160, "height": 143}, "Patroller,Chaser/Mini Chaser images/explode_05": {"y": 0.5, "width": 160, "height": 143}, "Patroller,Chaser/Mini Chaser images/explode_06": {"y": 0.5, "width": 160, "height": 143}, "Patroller,Chaser/Mini Chaser images/explode_07": {"y": 0.5, "width": 160, "height": 143}, "Patroller,Chaser/Mini Chaser images/explode_08": {"y": 0.5, "width": 160, "height": 143}, "Patroller,Chaser/Mini Chaser images/explode_09": {"y": 0.5, "width": 160, "height": 143}}, "HeadFleshball_1": {"Patroller,Chaser/Chaser Boss/FleshballHead_2": {"x": 0.66, "y": 15.72, "width": 97, "height": 100}}, "HeadFleshball_1_Behind": {"Patroller,Chaser/Chaser Boss/FleshballHead_2": {"x": 0.66, "y": 15.72, "width": 97, "height": 100}}, "HeadFleshball_2": {"Patroller,Chaser/Chaser Boss/FleshballHead_1": {"x": -0.92, "y": 18.02, "width": 79, "height": 74}}, "HeadFleshball_3": {"Patroller,Chaser/Chaser Boss/FleshballHead_3": {"x": -1.18, "y": 14.14, "width": 67, "height": 65}}, "HeadFleshball_3_Behind": {"Patroller,Chaser/Chaser Boss/FleshballHead_3": {"x": -1.18, "y": 14.14, "width": 67, "height": 65}}, "Other/WarningIcon": {"Patroller,Chaser/Pouncer Boss/JumpAttack": {"x": 6.21, "y": 52.8, "width": 44, "height": 126}, "Patroller,Chaser/Pouncer Boss/Sipral": {"x": 6.21, "y": 52.8, "width": 67, "height": 118}, "Patroller,Chaser/Pouncer Boss/StraightAttack": {"x": 6.21, "y": 52.8, "width": 51, "height": 125}, "WarningIcon": {"x": 1.15, "y": 33.17, "width": 61, "height": 117}}, "Patroller,Chaser/Chaser Boss/FleshBall1": {"Patroller,Chaser/Chaser Boss/FleshBall1": {"x": 1.69, "y": 19.11, "width": 96, "height": 74}}, "Patroller,Chaser/Chaser Boss/FleshBall2": {"Patroller,Chaser/Chaser Boss/FleshBall2": {"x": 4.5, "y": 7.87, "width": 56, "height": 46}}, "Patroller,Chaser/Chaser Boss/FleshBall3": {"Patroller,Chaser/Chaser Boss/FleshBall3": {"x": 5.62, "y": 1.69, "width": 62, "height": 50}}, "Patroller,Chaser/Chaser Boss/FleshBall4": {"Patroller,Chaser/Chaser Boss/FleshBall2": {"x": 4.5, "y": 7.87, "width": 56, "height": 46}}, "Patroller,Chaser/Chaser Boss/FleshBall5": {"Patroller,Chaser/Chaser Boss/FleshBall3": {"x": 5.62, "y": 1.69, "width": 62, "height": 50}}, "Patroller,Chaser/Chaser Boss/FleshBall6": {"Patroller,Chaser/Chaser Boss/FleshBall4": {"x": 7.26, "y": 0.97, "width": 36, "height": 37}}, "Patroller,Chaser/Chaser Boss/FleshBall7": {"Patroller,Chaser/Chaser Boss/FleshBall1": {"x": 5.27, "y": 9.28, "scaleX": 0.8202, "scaleY": 0.8202, "rotation": -14.98, "width": 96, "height": 74}, "Patroller,Chaser/Chaser Boss/FleshBall5": {"x": 5.81, "y": 7.26, "width": 55, "height": 53}}, "Patroller,Chaser/Chaser Boss/FleshBall8": {"Patroller,Chaser/Chaser Boss/FleshBall1": {"x": 1.2, "y": 3.59, "width": 96, "height": 74}}, "Patroller,Chaser/Chaser Boss/FleshBall9": {"Patroller,Chaser/Chaser Boss/FleshBall6": {"x": -1.22, "y": 13.38, "width": 50, "height": 62}}, "Patroller,Chaser/Chaser Boss/FleshBall11": {"Patroller,Chaser/Chaser Boss/FleshBall5": {"x": 11.23, "y": -8.64, "scaleX": 1.0964, "scaleY": 1.0964, "rotation": -100.51, "width": 55, "height": 53}}, "Patroller,Chaser/Chaser Boss/Tunneler_Ball": {"Patroller,Chaser/Chaser Boss/Tunneler_Ball": {"x": 51.93, "y": 1.7, "rotation": -84.97, "width": 147, "height": 137}}, "TurretMouth": {"Patroller,Chaser/Chaser Boss/Tunneler_Mouth": {"width": 144, "height": 98}, "Patroller,Chaser/Chaser Boss/Tunneler_Mouth_Closed": {"width": 132, "height": 86}, "Patroller,Chaser/Chaser Boss/Tunneler_Mouth_Wide": {"x": 0.5, "y": 0.5, "width": 159, "height": 101}}}}, {"name": "ALL", "attachments": {"Anlter_0_Behind": {"Patroller,Chaser/Patroller_images/Anlter_0": {"x": 37.28, "y": 9.11, "rotation": -29.05, "width": 64, "height": 65}}, "Anlter_0_Front": {"Patroller,Chaser/Patroller_images/Anlter_0": {"x": 37.28, "y": 9.11, "rotation": -29.05, "width": 64, "height": 65}}, "Anlter_1_Behind": {"Patroller,Chaser/Patroller_images/Anlter_1": {"x": 36.77, "y": 13.67, "rotation": -64.59, "width": 49, "height": 119}}, "Anlter_1_Front": {"Patroller,Chaser/Patroller_images/Anlter_1": {"x": 36.77, "y": 13.67, "rotation": -64.59, "width": 49, "height": 119}}, "Anlter_3_Behind": {"Patroller,Chaser/Patroller_images/Anlter_3": {"x": 41.48, "y": -8.7, "rotation": -141.67, "width": 68, "height": 66}}, "Anlter_3_Front": {"Patroller,Chaser/Patroller_images/Anlter_3": {"x": 41.48, "y": -8.7, "rotation": -141.67, "width": 68, "height": 66}}, "Anlter_4_Behind": {"Patroller,Chaser/Patroller_images/Anlter_4": {"x": 31.95, "y": -8.59, "rotation": -108.03, "width": 53, "height": 118}}, "Anlter_4_Front": {"Patroller,Chaser/Patroller_images/Anlter_4": {"x": 31.95, "y": -8.59, "rotation": -108.03, "width": 53, "height": 118}}, "Body_0": {"Body_0": {"path": "<PERSON>ler,Chaser/Chaser_images/Body_0", "y": 87.04, "width": 169, "height": 188}}, "Body_1": {"Body_1": {"name": "<PERSON>ler,Chaser/Chaser_images/Body_1", "y": 61.35, "width": 130, "height": 135}}, "Body_2": {"Body_2": {"name": "<PERSON>ler,Chaser/Chaser_images/Body_2", "y": 36.48, "width": 85, "height": 85}}, "Grass_0": {"Patroller,Chaser/Patroller_images/Grass_0": {"x": 22.49, "y": -6.43, "rotation": 90, "width": 75, "height": 62}}, "Grass_0_back": {"Patroller,Chaser/Patroller_images/Grass_0": {"x": 22.49, "y": -6.43, "rotation": 90, "width": 75, "height": 62}}, "Grass_1": {"Patroller,Chaser/Patroller_images/Grass_1": {"x": 6.92, "y": 0.13, "rotation": -92.49, "width": 43, "height": 29}}, "Head": {"Patroller,Chaser/Patroller_images/Head": {"x": -0.93, "y": -28.42, "width": 200, "height": 187}}, "HeadInner": {"Patroller,Chaser/Patroller_images/HeadInner": {"x": 0.95, "y": 27.58, "width": 126, "height": 96}}, "Twig_0": {"Patroller,Chaser/Patroller_images/Twig_0": {"x": 7.38, "y": 1.9, "rotation": -35.54, "width": 31, "height": 50}}}}, {"name": "BABY_BODY_0", "bones": ["SpawnBall"], "attachments": {"Anlter_0_Behind": {"Patroller,Chaser/Patroller_images/Anlter_0": {"name": "Patroller,Chaser/Mini Chaser images/Antler_2", "x": 3.65, "y": 2.75, "rotation": -29.05, "width": 48, "height": 105}}, "Anlter_3_Behind": {"Patroller,Chaser/Patroller_images/Anlter_3": {"name": "Patroller,Chaser/Mini Chaser images/Antler_1", "x": 10.16, "y": 2.46, "rotation": -141.67, "width": 53, "height": 104}}, "Eyes": {"Patroller,Chaser/Chaser_images/Eyes": {"name": "Patroller,Chaser/Chaser_images/Eyes_Normal", "x": 1.58, "y": -18.03, "scaleX": 0.75, "scaleY": 0.75, "width": 102, "height": 41}, "Patroller,Chaser/Chaser_images/Eyes_Shocked": {"x": 1.26, "y": -17.1, "scaleX": 0.75, "scaleY": 0.75, "width": 111, "height": 45}, "Patroller,Chaser/Chaser_images/Eyes_Shut": {"x": 1.35, "y": -17.95, "scaleX": 0.75, "scaleY": 0.75, "width": 95, "height": 33}}, "Head": {"Patroller,Chaser/Patroller_images/Head": {"name": "Patroller,Chaser/Mini Chaser images/Head", "x": 0.52, "y": -50.7, "width": 149, "height": 135}}, "HeadInner": {"Patroller,Chaser/Patroller_images/HeadInner": {"name": "Patroller,Chaser/Mini Chaser images/Face", "x": 0.52, "y": 23.08, "width": 97, "height": 73}}}}, {"name": "BABY_BODY_0_BACK", "bones": ["SpawnBall"], "attachments": {"Anlter_0_Front": {"Patroller,Chaser/Patroller_images/Anlter_0": {"name": "Patroller,Chaser/Mini Chaser images/Antler_2", "x": 3.08, "y": 3.06, "rotation": -29.05, "width": 48, "height": 105}}, "Anlter_3_Front": {"Patroller,Chaser/Patroller_images/Anlter_3": {"name": "Patroller,Chaser/Mini Chaser images/Antler_1", "x": 9.63, "y": 2.04, "rotation": -141.67, "width": 53, "height": 104}}, "Head": {"Patroller,Chaser/Patroller_images/Head": {"name": "Patroller,Chaser/Mini Chaser images/Head_Back", "x": 0.52, "y": -50.7, "width": 149, "height": 135}}}}, {"name": "BABY_BODY_1", "attachments": {"Body_0": {"Body_0": {"name": "Patroller,Chaser/Mini Chaser images/Body_0", "type": "mesh", "uvs": [0.59272, 0.08917, 0.7613, 0.06707, 0.81844, 0.25865, 0.93272, 0.30286, 0.93272, 0.46865, 1, 0.46496, 1, 0.7597, 0.8813, 1, 0.10415, 1, 0.04415, 0.74128, 0, 0.70123, 0, 0.49156, 0.2013, 0, 0.58415, 0, 0.0613, 0.47233, 0.12415, 0.3397, 0.33558, 0.39128, 0.6413, 0.53865, 0.74701, 0.52759, 0.82701, 0.7597, 0.48584, 0.18202, 0.56013, 0.32202, 0.66013, 0.33676], "triangles": [19, 7, 8, 8, 16, 17, 19, 8, 17, 17, 18, 19, 7, 19, 6, 8, 9, 16, 16, 14, 15, 14, 16, 9, 19, 4, 6, 4, 5, 6, 3, 4, 2, 4, 19, 18, 9, 10, 14, 10, 11, 14, 16, 21, 17, 4, 18, 2, 17, 22, 18, 17, 21, 22, 18, 22, 2, 15, 14, 11, 12, 15, 11, 16, 20, 21, 20, 16, 12, 16, 15, 12, 21, 0, 22, 22, 1, 2, 22, 0, 1, 21, 20, 0, 20, 13, 0, 20, 12, 13], "vertices": [2, 2, 13.06, 98, 0.648, 15, -32.56, 9.74, 0.352, 2, 2, 37.84, 100.52, 0.616, 15, -15.93, -8.8, 0.384, 2, 2, 46.24, 78.68, 0.648, 15, -28.63, -28.46, 0.352, 2, 2, 63.04, 73.64, 0.712, 15, -22.8, -45, 0.288, 2, 2, 63.04, 54.74, 0.776, 15, -38.07, -56.14, 0.224, 2, 2, 72.93, 55.16, 0.52, 15, -31.9, -63.88, 0.48, 2, 2, 72.93, 21.56, 0.744, 15, -59.05, -83.68, 0.256, 1, 2, 55.48, -5.83, 1, 1, 2, -58.76, -5.83, 1, 1, 2, -67.58, 23.66, 1, 1, 2, -74.07, 28.23, 1, 1, 2, -74.07, 52.13, 1, 2, 2, -44.48, 108.17, 0.68, 15, -58.25, 62.22, 0.32, 2, 2, 11.8, 108.17, 0.76, 15, -25.09, 16.74, 0.24, 1, 2, -65.06, 54.32, 1, 1, 2, -55.82, 69.44, 1, 1, 2, -24.74, 63.56, 1, 1, 2, 20.2, 46.76, 1, 1, 2, 35.74, 48.02, 1, 1, 2, 47.5, 21.56, 1, 2, 2, -2.65, 87.42, 0.76428, 15, -50.37, 16.19, 0.23572, 2, 2, 8.27, 71.46, 0.7868, 15, -56.83, -2.03, 0.2132, 2, 2, 22.97, 69.78, 0.62651, 15, -49.53, -14.9, 0.37349], "hull": 14, "edges": [20, 22, 22, 28, 28, 30, 30, 24, 24, 26, 26, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 20, 18, 18, 16, 14, 16, 10, 12, 14, 12, 32, 34, 34, 36, 36, 38, 38, 14, 22, 24, 32, 40, 40, 42, 42, 44, 44, 34], "width": 147, "height": 114}}}}, {"name": "BABY_BODY_2", "attachments": {"Body_1": {"Body_1": {"name": "Patroller,Chaser/Mini Chaser images/Body_1", "type": "mesh", "uvs": [0.78541, 0.12407, 0.88592, 0.21343, 0.8931, 0.48151, 1, 0.54407, 1, 1, 0.16439, 1, 0, 0.77194, 0, 0.28045, 0.16439, 0.25811, 0.1931, 0, 0.44439, 0, 0.62028, 0, 0.77464, 0, 0.43362, 0.44577, 0.60233, 0.39215, 0.70644, 0.63789, 0.84644, 0.6513, 0.86797, 0.90151, 0.3008, 0.41002, 0.26131, 0.62449], "triangles": [5, 17, 4, 5, 15, 17, 5, 19, 15, 19, 13, 15, 13, 14, 15, 15, 16, 17, 17, 3, 4, 19, 18, 13, 5, 6, 19, 17, 16, 3, 19, 7, 8, 19, 6, 7, 16, 2, 3, 16, 15, 2, 1, 14, 0, 14, 2, 15, 19, 8, 18, 14, 13, 10, 18, 8, 10, 13, 18, 10, 10, 8, 9, 2, 14, 1, 11, 12, 0, 14, 11, 0, 14, 10, 11], "vertices": [2, 16, 22.43, -18.55, 0.4, 3, 30.11, 74.82, 0.6, 2, 16, 22.71, -33, 0.272, 3, 41.87, 66.42, 0.728, 2, 16, 2.99, -48.71, 0.544, 3, 42.71, 41.22, 0.456, 2, 16, 5.73, -62.26, 0.128, 3, 55.22, 35.34, 0.872, 1, 3, 55.22, -7.52, 1, 1, 3, -42.55, -7.52, 1, 1, 3, -61.78, 13.92, 1, 2, 16, -44.2, 46.42, 0.288, 3, -61.78, 60.12, 0.712, 2, 16, -31.03, 32.24, 0.624, 3, -42.55, 62.22, 0.376, 2, 16, -9.56, 44.02, 0.384, 3, -39.19, 86.48, 0.616, 2, 16, 7.98, 20.43, 0.464, 3, -9.79, 86.48, 0.536, 1, 16, 20.26, 3.91, 1, 2, 16, 31.04, -10.58, 0.432, 3, 28.85, 86.48, 0.568, 2, 16, -26.39, -3.57, 0.16, 3, -11.05, 44.58, 0.84, 2, 16, -10.57, -16.4, 0.304, 3, 8.69, 49.62, 0.696, 2, 16, -21.84, -39.96, 0.176, 3, 20.87, 26.52, 0.824, 1, 3, 37.25, 25.26, 1, 1, 3, 39.77, 1.74, 1, 2, 16, -32.97, 10.91, 0.288, 3, -26.59, 47.94, 0.712, 1, 3, -31.21, 27.78, 1], "hull": 13, "edges": [8, 10, 12, 10, 24, 0, 0, 2, 2, 4, 8, 6, 4, 6, 22, 24, 18, 16, 12, 14, 16, 14, 26, 28, 28, 30, 30, 32, 32, 34, 26, 36, 36, 38, 18, 20, 20, 22], "width": 117, "height": 94}}}}, {"name": "BABY_BODY_3", "attachments": {"Body_2": {"Body_2": {"name": "Patroller,Chaser/Mini Chaser images/Body_2", "type": "mesh", "uvs": [0.68255, 0.26773, 0.81601, 0.27419, 0.84741, 0.4228, 1, 0.26126, 1, 0.61665, 0.92199, 0.74588, 0.92984, 1, 0.1919, 1, 0.12517, 0.77172, 0, 0.62957, 0.00393, 0.34526, 0.14872, 0.42926, 0.18797, 0.19665, 0.34106, 0, 0.5805, 0, 0.39209, 0.64249, 0.5962, 0.41634, 0.72573, 0.6748, 0.81993, 0.65542], "triangles": [16, 14, 0, 2, 3, 4, 9, 10, 11, 16, 15, 13, 16, 13, 14, 12, 13, 15, 11, 12, 15, 1, 2, 0, 18, 2, 4, 2, 17, 0, 18, 17, 2, 16, 0, 17, 5, 18, 4, 8, 9, 11, 8, 11, 15, 7, 8, 15, 18, 5, 6, 17, 18, 6, 15, 16, 17, 7, 15, 17, 7, 17, 6], "vertices": [2, 4, 16.67, 41.64, 0.696, 16, -12.21, -27.56, 0.304, 2, 4, 30.95, 41.22, 0.76, 16, -4.03, -39.27, 0.24, 2, 4, 34.31, 31.56, 0.632, 16, -9.78, -47.73, 0.368, 2, 4, 50.64, 42.06, 0.432, 16, 8.39, -54.57, 0.568, 2, 4, 50.64, 18.96, 0.664, 16, -10.14, -68.36, 0.336, 1, 4, 42.29, 10.56, 1, 1, 4, 43.13, -5.96, 1, 1, 4, -35.83, -5.96, 1, 1, 4, -42.97, 8.88, 1, 2, 4, -56.36, 18.12, 0.616, 16, -74.67, 17.01, 0.384, 2, 4, -55.94, 36.6, 0.36, 16, -59.59, 27.7, 0.64, 2, 4, -40.45, 31.14, 0.728, 16, -54.72, 12.01, 0.272, 2, 4, -36.25, 46.26, 0.584, 16, -40.08, 17.66, 0.416, 2, 4, -19.87, 59.04, 0.52, 16, -20.05, 12.14, 0.48, 2, 4, 5.75, 59.04, 0.408, 16, -4.76, -8.42, 0.592, 2, 4, -14.41, 17.28, 0.68, 16, -50.31, -17.16, 0.32, 2, 4, 7.43, 31.98, 0.792, 16, -25.48, -25.91, 0.208, 1, 4, 21.29, 15.18, 1, 1, 4, 31.37, 16.44, 1], "hull": 15, "edges": [28, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 26, 28, 26, 24, 24, 22, 22, 20, 18, 20, 18, 16, 12, 14, 16, 14, 30, 32, 32, 34, 34, 36, 30, 22], "width": 107, "height": 65}}}}, {"name": "BODY_0", "bones": ["SpawnBall"], "attachments": {"Anlter_0_Behind": {"Patroller,Chaser/Patroller_images/Anlter_0": {"x": 37.28, "y": 9.11, "rotation": -29.05, "width": 64, "height": 65}}, "Anlter_1_Behind": {"Patroller,Chaser/Patroller_images/Anlter_1": {"x": 30.84, "y": 9.88, "rotation": -64.59, "width": 49, "height": 119}}, "Anlter_3_Behind": {"Patroller,Chaser/Patroller_images/Anlter_3": {"name": "Patroller,Chaser/Patroller_images/Anlter_4", "path": "Patroller,Chaser/Patroller_images/Anlter_3", "x": 37.62, "y": 1.97, "rotation": -141.67, "width": 68, "height": 66}}, "Anlter_4_Behind": {"Patroller,Chaser/Patroller_images/Anlter_4": {"name": "Patroller,Chaser/Patroller_images/Anlter_5", "path": "Patroller,Chaser/Patroller_images/Anlter_4", "x": 32.35, "y": -9.88, "rotation": -108.03, "width": 53, "height": 118}}, "Eyes": {"Patroller,Chaser/Chaser_images/Eyes": {"name": "Patroller,Chaser/Chaser_images/Eyes_Normal", "x": 1.58, "y": -5.25, "width": 102, "height": 41}, "Patroller,Chaser/Chaser_images/Eyes_Shocked": {"x": 1.26, "y": -4.97, "width": 111, "height": 45}, "Patroller,Chaser/Chaser_images/Eyes_Shut": {"x": 1.35, "y": -3.77, "width": 95, "height": 33}}, "Grass_0": {"Patroller,Chaser/Patroller_images/Grass_0": {"x": 22.49, "y": -6.43, "rotation": 90, "width": 75, "height": 62}}, "Grass_0_back": {"Patroller,Chaser/Patroller_images/Grass_0": {"x": 22.49, "y": -6.43, "rotation": 90, "width": 75, "height": 62}}, "Grass_1": {"Patroller,Chaser/Patroller_images/Grass_1": {"x": 6.92, "y": 0.13, "rotation": -92.49, "width": 43, "height": 29}}, "Head": {"Patroller,Chaser/Patroller_images/Head": {"type": "mesh", "uvs": [0.86763, 0.09835, 0.99999, 0.39684, 0.99999, 0.68396, 0.93939, 0.90854, 0.81181, 1, 0.50082, 1, 0.18452, 1, 0.05162, 0.88012, 1e-05, 0.68112, 0, 0.40537, 0.10744, 0.14384, 0.27489, 0, 0.49551, 1e-05, 0.68688, 0, 0.50082, 0.16089, 0.50082, 0.39969, 0.50082, 0.89149, 0.50112, 0.68126], "triangles": [0, 15, 14, 15, 10, 14, 17, 9, 15, 17, 15, 1, 3, 4, 16, 6, 16, 5, 4, 5, 16, 6, 7, 16, 2, 3, 17, 7, 8, 17, 2, 17, 1, 9, 17, 8, 0, 14, 13, 1, 15, 0, 15, 9, 10, 14, 10, 11, 12, 14, 11, 14, 12, 13, 3, 16, 17, 16, 7, 17], "vertices": [1, 5, 72.6, 46.69, 1, 1, 5, 99.07, -9.13, 1, 1, 5, 99.07, -62.82, 1, 1, 5, 86.95, -104.82, 1, 1, 5, 61.43, -121.92, 1, 1, 5, -0.76, -121.92, 1, 1, 5, -64.02, -121.92, 1, 1, 5, -90.6, -99.5, 1, 1, 5, -100.92, -62.29, 1, 1, 5, -100.93, -10.73, 1, 1, 5, -79.44, 38.18, 1, 1, 5, -45.95, 65.08, 1, 1, 5, -1.83, 65.08, 1, 1, 5, 36.45, 65.08, 1, 2, 5, -0.76, 34.99, 0.536, 13, 0.98, 91.77, 0.464, 2, 5, -0.76, -9.66, 0.28, 13, 0.98, 47.12, 0.72, 2, 5, -0.76, -101.63, 0.472, 13, 0.98, -44.85, 0.528, 2, 5, -0.7, -62.32, 0.376, 13, 1.04, -5.53, 0.624], "hull": 14, "edges": [18, 20, 20, 22, 16, 18, 16, 14, 14, 12, 8, 6, 6, 4, 26, 0, 4, 2, 0, 2, 22, 24, 24, 26, 24, 28, 28, 30, 8, 10, 10, 12, 32, 10, 30, 34, 34, 32, 14, 32, 32, 6], "width": 200, "height": 187}}, "HeadInner": {"Patroller,Chaser/Patroller_images/HeadInner": {"type": "mesh", "uvs": [1, 0.33275, 0.99639, 0.70483, 1, 1, 0.51718, 1, 0, 1, 0, 0.71453, 1e-05, 0.31825, 0, 0, 0.50874, 0, 1, 0, 0.50917, 0.328, 0.51378, 0.71511], "triangles": [6, 7, 8, 10, 8, 9, 4, 11, 3, 3, 1, 2, 1, 10, 0, 5, 6, 10, 11, 10, 1, 5, 10, 11, 6, 8, 10, 10, 9, 0, 4, 5, 11, 3, 11, 1], "vertices": [2, 13, 66.01, 29.58, 0.656, 12, 64.92, 54.48, 0.344, 2, 13, 65.55, -11.35, 0.656, 12, 64.45, 13.55, 0.344, 2, 13, 66.02, -43.82, 0.208, 12, 64.92, -18.92, 0.792, 2, 13, 3.7, -43.82, 0.208, 12, 2.63, -18.92, 0.792, 2, 13, -63.06, -43.82, 0.208, 12, -64.09, -18.92, 0.792, 2, 13, -63.04, -12.42, 0.656, 12, -64.07, 12.48, 0.344, 2, 13, -63.04, 31.17, 0.656, 12, -64.07, 56.07, 0.344, 2, 13, -63.06, 66.18, 0.208, 12, -64.09, 91.08, 0.792, 2, 13, 2.61, 66.18, 0.208, 12, 1.54, 91.08, 0.792, 2, 13, 66.02, 66.18, 0.208, 12, 64.92, 91.08, 0.792, 2, 13, 2.67, 30.1, 0.656, 12, 1.6, 55, 0.344, 2, 13, 3.26, -12.48, 0.656, 12, 2.2, 12.42, 0.344], "hull": 10, "edges": [12, 14, 0, 18, 14, 16, 16, 18, 0, 20, 20, 12, 16, 20, 4, 6, 6, 8, 8, 10, 10, 12, 6, 22, 22, 20, 10, 22, 0, 2, 2, 4, 22, 2], "width": 126, "height": 96}}, "Symbol": {"Patroller,Chaser/Patroller_images/Symbol": {"x": -0.19, "y": 15.97, "width": 26, "height": 41}}, "Twig_0": {"Patroller,Chaser/Patroller_images/Twig_0": {"x": 7.38, "y": 1.9, "rotation": -35.54, "width": 31, "height": 50}}}}, {"name": "BODY_0_BACK", "attachments": {"Anlter_0_Front": {"Patroller,Chaser/Patroller_images/Anlter_0": {"name": "Patroller,Chaser/Patroller_images/Anlter_1", "path": "Patroller,Chaser/Patroller_images/Anlter_0", "x": 37.28, "y": 9.11, "rotation": -29.05, "width": 64, "height": 65}}, "Anlter_1_Front": {"Patroller,Chaser/Patroller_images/Anlter_1": {"name": "Patroller,Chaser/Patroller_images/Anlter_2", "path": "Patroller,Chaser/Patroller_images/Anlter_1", "x": 30.84, "y": 9.88, "rotation": -64.59, "width": 49, "height": 119}}, "Anlter_3_Front": {"Patroller,Chaser/Patroller_images/Anlter_3": {"name": "Patroller,Chaser/Patroller_images/Anlter_4", "path": "Patroller,Chaser/Patroller_images/Anlter_3", "x": 37.62, "y": 1.97, "rotation": -141.67, "width": 68, "height": 66}}, "Anlter_4_Front": {"Patroller,Chaser/Patroller_images/Anlter_4": {"name": "Patroller,Chaser/Patroller_images/Anlter_5", "path": "Patroller,Chaser/Patroller_images/Anlter_4", "x": 32.35, "y": -9.88, "rotation": -108.03, "width": 53, "height": 118}}, "Grass_1": {"Patroller,Chaser/Patroller_images/Grass_1": {"x": 6.92, "y": 0.13, "rotation": -92.49, "width": 43, "height": 29}}, "Head": {"Patroller,Chaser/Patroller_images/Head": {"x": -0.93, "y": -28.42, "width": 200, "height": 187}}}}, {"name": "BODY_1", "attachments": {"Body_0": {"Body_0": {"type": "mesh", "path": "<PERSON>ler,Chaser/Chaser_images/Body_0", "uvs": [0.80688, 0.08282, 0.71172, 0.07155, 0.7073, 0.09558, 0.78821, 0.12117, 0.82319, 0.19943, 0.91028, 0.161, 0.97836, 0.3076, 0.93244, 0.34747, 0.96773, 0.39252, 0.90914, 0.46939, 1, 0.50355, 1, 0.61457, 0.97406, 0.6402, 1, 0.67578, 1, 0.85371, 1, 1, 0.24094, 1, 0.15226, 1, 0, 0.80958, 0, 0.34413, 0.13415, 0.26746, 0.10476, 0.17474, 0.22717, 0.14189, 0.24373, 0.19391, 0.33427, 0.10947, 0.42384, 0.13188, 0.58347, 0.0884, 0.64346, 0, 0.82136, 0, 0.87069, 0.24356, 0.88391, 0.29102, 0.14593, 0.87933, 0.25519, 0.76546, 0.34702, 0.78965, 0.45153, 0.67151, 0.5307, 0.74695, 0.71239, 0.62734, 0.89647, 0.69144, 0.88539, 0.82097, 0.85724, 0.48368, 0.89467, 0.65474, 0.45768, 0.44442, 0.51227, 0.70802, 0.59961, 0.63371, 0.68852, 0.58183, 0.66356, 0.44863, 0.56686, 0.46546, 0.41868, 0.42339, 0.5731, 0.429, 0.67604, 0.4262, 0.4937, 0.89572, 0.79322, 0.93243, 0.70683, 0.59657, 0.78951, 0.41713, 0.82634, 0.63358, 0.45711, 0.26141, 0.60692, 0.18356, 0.69587, 0.25089, 0.76844, 0.25089, 0.79419, 0.37083], "triangles": [7, 53, 59, 58, 59, 57, 30, 58, 29, 58, 4, 29, 30, 59, 58, 59, 30, 7, 7, 30, 6, 6, 29, 5, 6, 30, 29, 29, 4, 5, 0, 1, 28, 4, 58, 3, 58, 57, 3, 57, 56, 2, 15, 16, 51, 16, 50, 51, 15, 51, 14, 51, 38, 14, 16, 33, 50, 33, 31, 32, 31, 33, 16, 18, 31, 17, 16, 17, 31, 50, 35, 51, 35, 36, 51, 51, 36, 38, 50, 33, 35, 31, 18, 32, 14, 38, 13, 38, 37, 13, 38, 36, 37, 18, 19, 32, 33, 34, 35, 33, 32, 34, 32, 19, 47, 32, 47, 34, 47, 19, 20, 52, 36, 44, 44, 36, 43, 43, 36, 35, 34, 42, 35, 35, 42, 43, 46, 43, 41, 36, 54, 37, 54, 40, 37, 37, 12, 13, 37, 40, 12, 41, 43, 42, 42, 34, 41, 34, 47, 41, 11, 12, 40, 40, 54, 39, 9, 11, 40, 43, 46, 44, 40, 39, 9, 53, 39, 54, 54, 36, 52, 9, 10, 11, 53, 54, 52, 46, 45, 44, 52, 44, 45, 49, 53, 52, 49, 52, 45, 39, 53, 9, 9, 53, 7, 9, 7, 8, 46, 48, 45, 46, 41, 48, 45, 48, 49, 41, 47, 48, 57, 49, 48, 53, 49, 59, 59, 49, 57, 48, 47, 55, 57, 48, 55, 57, 55, 56, 20, 23, 47, 47, 23, 55, 25, 23, 24, 23, 25, 55, 20, 21, 23, 55, 25, 56, 57, 2, 3, 21, 22, 23, 25, 26, 56, 56, 26, 2, 2, 26, 1, 26, 27, 1, 1, 27, 28], "vertices": [2, 2, 51.86, 165.21, 0.28, 15, 44.6, 17.98, 0.72, 2, 2, 35.78, 167.33, 0.696, 15, 36.84, 32.22, 0.304, 2, 2, 35.03, 162.81, 0.584, 15, 32.74, 30.17, 0.416, 2, 2, 48.71, 158, 0.584, 15, 36.91, 16.28, 0.416, 2, 2, 54.62, 143.28, 0.52, 15, 28.51, 2.84, 0.48, 2, 2, 69.34, 150.51, 0.152, 15, 43.02, -4.8, 0.848, 1, 15, 27.53, -30.33, 1, 2, 2, 73.08, 115.45, 0.52, 15, 16.9, -28.48, 0.48, 2, 2, 79.05, 106.98, 0.52, 15, 13.57, -38.29, 0.48, 1, 2, 69.14, 92.53, 1, 2, 2, 84.5, 86.11, 0.696, 15, -0.08, -54.99, 0.304, 2, 2, 84.5, 65.24, 0.712, 15, -16.95, -67.29, 0.288, 2, 2, 80.12, 60.42, 0.776, 15, -23.42, -66.58, 0.224, 2, 2, 84.5, 53.73, 0.728, 15, -26.25, -74.07, 0.272, 1, 2, 84.5, 20.28, 1, 1, 2, 84.5, -7.22, 1, 1, 2, -43.78, -7.22, 1, 1, 2, -58.77, -7.22, 1, 1, 2, -84.5, 28.58, 1, 1, 2, -84.5, 116.08, 1, 1, 2, -61.83, 130.49, 1, 2, 2, -66.8, 147.93, 0.888, 15, -39.28, 103.68, 0.112, 2, 2, -46.11, 154.1, 0.904, 15, -22.1, 90.6, 0.096, 1, 2, -43.31, 144.32, 1, 1, 2, -28.01, 160.2, 1, 2, 2, -12.87, 155.98, 0.82483, 15, -0.99, 64.85, 0.17517, 2, 2, 14.11, 164.16, 0.584, 15, 21.51, 47.87, 0.416, 2, 2, 24.24, 180.78, 0.68, 15, 40.91, 49.47, 0.32, 2, 2, 54.31, 180.78, 0.216, 15, 58.62, 25.18, 0.784, 2, 2, 62.65, 134.99, 0.52, 15, 26.53, -8.54, 0.48, 2, 2, 64.88, 126.06, 0.52, 15, 20.64, -15.6, 0.48, 1, 2, -59.84, 15.46, 1, 1, 2, -41.37, 36.87, 1, 1, 2, -25.85, 32.32, 1, 1, 2, -8.19, 54.53, 1, 1, 2, 5.19, 40.35, 1, 2, 2, 35.89, 62.84, 0.584, 15, -47.53, -29.43, 0.416, 2, 2, 67, 50.79, 0.712, 15, -38.93, -61.67, 0.288, 1, 2, 65.13, 26.44, 1, 1, 2, 60.37, 89.84, 1, 1, 2, 66.7, 57.69, 1, 1, 2, -7.15, 97.23, 1, 1, 2, 2.07, 47.67, 1, 2, 2, 16.83, 61.64, 0.776, 15, -59.72, -14.73, 0.224, 2, 2, 31.86, 71.39, 0.568, 15, -42.99, -21.13, 0.432, 2, 2, 27.64, 96.43, 0.6, 15, -25.24, -2.97, 0.4, 2, 2, 11.3, 93.27, 0.84, 15, -37.43, 8.37, 0.16, 1, 2, -13.74, 101.18, 1, 1, 2, 12.35, 100.13, 1, 1, 2, 29.75, 100.65, 1, 1, 2, -1.06, 12.38, 1, 1, 2, 49.55, 5.48, 1, 1, 2, 34.95, 68.62, 1, 2, 2, 48.93, 102.36, 0.33244, 15, -7.92, -16.68, 0.66756, 2, 2, 55.15, 61.66, 0.488, 15, -37.13, -45.68, 0.512, 2, 2, -7.25, 131.63, 0.90189, 15, -17.36, 45.96, 0.09811, 2, 2, 18.07, 146.27, 0.40142, 15, 9.39, 34.13, 0.59858, 2, 2, 33.1, 133.61, 0.48636, 15, 8.01, 14.52, 0.51364, 2, 2, 45.37, 133.61, 0.36969, 15, 15.24, 4.61, 0.63031, 2, 2, 49.72, 111.06, 0.3092, 15, -0.42, -12.19, 0.6908], "hull": 29, "edges": [52, 54, 54, 56, 56, 0, 0, 2, 8, 58, 14, 12, 12, 10, 10, 8, 58, 60, 60, 14, 30, 32, 32, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 30, 28, 76, 28, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 26, 28, 24, 26, 36, 38, 32, 34, 36, 34, 2, 4, 4, 6, 6, 8, 18, 78, 78, 80, 80, 24, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 82, 94, 68, 94, 96, 96, 98, 70, 100, 100, 102, 102, 76, 72, 104, 104, 98, 42, 44, 38, 40, 40, 42, 44, 46, 46, 48, 48, 50, 50, 52, 98, 106, 80, 108, 108, 104, 106, 108, 96, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 98], "width": 169, "height": 188}}}}, {"name": "BODY_2", "attachments": {"Body_1": {"Body_1": {"name": "<PERSON>ler,Chaser/Chaser_images/Body_1", "type": "mesh", "uvs": [0.8942, 0.17547, 0.83226, 0.25615, 0.73754, 0.27019, 0.85048, 0.32281, 0.88327, 0.39999, 1, 0.37894, 1, 0.51433, 0.89784, 0.55786, 0.90148, 0.82097, 0.85412, 1, 0.16922, 1, 0, 1, 0, 0.86306, 0, 0.31983, 0.17845, 0.11108, 0.59911, 0.11934, 0.56268, 0, 0.7339, 0, 0.15502, 0.39278, 0.4225, 0.25524, 0.60169, 0.59784, 0.77568, 0.59534], "triangles": [8, 20, 21, 12, 18, 20, 12, 13, 18, 21, 7, 8, 10, 20, 9, 9, 20, 8, 20, 10, 12, 10, 11, 12, 18, 19, 20, 20, 2, 21, 2, 19, 15, 21, 4, 7, 4, 2, 3, 4, 21, 2, 7, 4, 6, 4, 5, 6, 18, 14, 19, 18, 13, 14, 19, 14, 15, 17, 2, 15, 0, 1, 2, 17, 0, 2, 15, 16, 17, 20, 19, 2], "vertices": [2, 16, 59.39, -17.41, 0.64, 3, 51.25, 105.16, 0.36, 2, 16, 45.84, -17.44, 0.64, 3, 43.19, 94.27, 0.36, 2, 16, 36.98, -8.69, 0.272, 3, 30.88, 92.37, 0.728, 1, 3, 45.56, 85.27, 1, 2, 16, 34.22, -34.35, 0.144, 3, 49.82, 74.85, 0.856, 2, 16, 45.55, -44.83, 0.48, 3, 65, 77.69, 0.52, 2, 16, 30.89, -55.74, 0.48, 3, 65, 59.41, 0.52, 2, 16, 18.25, -48.59, 0.128, 3, 51.72, 53.54, 0.872, 1, 3, 52.19, 18.02, 1, 1, 3, 46.04, -6.15, 1, 1, 3, -43, -6.15, 1, 1, 3, -65, -6.15, 1, 1, 3, -65, 12.33, 1, 1, 3, -65, 85.67, 1, 1, 3, -41.8, 113.85, 1, 2, 16, 42.58, 17.9, 0.528, 3, 12.88, 112.74, 0.472, 2, 16, 52.68, 31.31, 0.64, 3, 8.15, 128.85, 0.36, 2, 16, 65.96, 13.45, 0.64, 3, 30.41, 128.85, 0.36, 1, 3, -44.85, 75.82, 1, 2, 16, 14.16, 25.38, 0.56, 3, -10.08, 94.39, 0.44, 2, 16, -9.06, -20.91, 0.848, 3, 13.22, 48.14, 0.152, 1, 3, 35.84, 48.48, 1], "hull": 18, "edges": [26, 28, 28, 30, 30, 32, 32, 34, 34, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 22, 24, 24, 26, 20, 22, 24, 20, 18, 20, 18, 16, 16, 14, 10, 12, 14, 12, 36, 38, 38, 40, 40, 42, 42, 16], "width": 130, "height": 135}}}}, {"name": "BODY_3", "attachments": {"Body_2": {"Body_2": {"name": "<PERSON>ler,Chaser/Chaser_images/Body_2", "type": "mesh", "uvs": [1, 0.28045, 1, 0.47321, 1, 0.7468, 1, 1, 0.15386, 1, 0, 0.73345, 0, 0.40321, 0.24799, 0.13277, 0.70107, 0.0702, 0.75729, 0.20859, 0.78324, 0, 1, 0, 0.95581, 0.43975, 0.32402, 0.59999, 0.661, 0.56663], "triangles": [3, 4, 14, 4, 13, 14, 14, 2, 3, 4, 5, 13, 14, 12, 2, 12, 1, 2, 5, 6, 13, 14, 13, 7, 13, 6, 7, 14, 9, 12, 9, 7, 8, 9, 14, 7, 12, 0, 1, 12, 9, 0, 0, 9, 11, 9, 10, 11], "vertices": [2, 16, 14.03, -40.23, 0.496, 4, 42.5, 55.14, 0.504, 2, 16, 0.88, -50.01, 0.304, 4, 42.5, 38.75, 0.696, 1, 4, 42.5, 15.5, 1, 1, 4, 42.5, -6.02, 1, 1, 4, -29.42, -6.02, 1, 1, 4, -42.5, 16.63, 1, 1, 4, -42.5, 44.7, 1, 1, 4, -21.42, 67.69, 1, 2, 16, 13.21, -9.18, 0.592, 4, 17.09, 73.01, 0.408, 2, 16, 6.62, -20.03, 0.8, 4, 21.87, 61.25, 0.2, 1, 16, 22.17, -11.22, 1, 1, 16, 33.16, -26.01, 1, 2, 16, 0.92, -45.3, 0.256, 4, 38.74, 41.6, 0.744, 1, 4, -14.96, 27.98, 1, 1, 4, 13.68, 30.81, 1], "hull": 12, "edges": [20, 22, 18, 20, 18, 16, 12, 14, 16, 14, 0, 22, 24, 0, 0, 2, 24, 2, 10, 12, 6, 8, 10, 8, 10, 26, 26, 28, 2, 4, 4, 6, 28, 4], "width": 85, "height": 85}}}}, {"name": "BODY_4", "attachments": {"Body_2": {"Body_2": {"name": "<PERSON>ler,Chaser/Chaser_images/Body_2", "type": "mesh", "uvs": [1, 0.28045, 1, 0.47321, 1, 0.7468, 1, 1, 0.15386, 1, 0, 0.73345, 0, 0.40321, 0.24799, 0.13277, 0.70107, 0.0702, 0.75729, 0.20859, 0.78324, 0, 1, 0, 0.95581, 0.43975, 0.32402, 0.59999, 0.661, 0.56663], "triangles": [3, 4, 14, 4, 13, 14, 14, 2, 3, 4, 5, 13, 14, 12, 2, 12, 1, 2, 5, 6, 13, 14, 13, 7, 13, 6, 7, 14, 9, 12, 9, 7, 8, 9, 14, 7, 12, 0, 1, 12, 9, 0, 0, 9, 11, 9, 10, 11], "vertices": [2, 16, -37.34, 8.6, 0.496, 4, -27.34, 43.05, 0.504, 2, 16, -47.86, 0.78, 0.304, 4, -27.34, 29.95, 0.696, 1, 4, -27.34, 11.34, 1, 1, 4, -27.34, -5.88, 1, 1, 4, 30.2, -5.88, 1, 1, 4, 40.66, 12.25, 1, 1, 4, 40.66, 34.71, 1, 1, 4, 23.8, 53.1, 1, 2, 16, -13.74, 0.82, 0.592, 4, -7.01, 57.35, 0.408, 2, 16, -23.57, -1.73, 0.8, 4, -10.84, 47.94, 0.2, 1, 16, -13.24, 8.15, 1, 1, 16, -22.04, 19.98, 1, 2, 16, -44.24, -0.27, 0.256, 4, -24.34, 32.22, 0.744, 1, 4, 18.63, 21.33, 1, 1, 4, -4.29, 23.59, 1], "hull": 12, "edges": [20, 22, 18, 20, 18, 16, 12, 14, 16, 14, 0, 22, 24, 0, 0, 2, 24, 2, 10, 12, 6, 8, 10, 8, 10, 26, 26, 28, 2, 4, 4, 6, 28, 4], "width": 85, "height": 85}}}}, {"name": "CHASERBOSS_BODY_0", "bones": ["MOUTH_BTM", "HeadFleshball_2", "TurretMouth", "HeadFleshball_1_Behind", "HeadFleshball_1", "SpawnBall", "MOUTH_CHASER", "EYE_LEFT_BTM", "EYE_RIGHT_BTM", "HeadFleshball_3", "HeadFleshball_3_Behind", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "attachments": {"Anlter_0_Behind": {"Patroller,Chaser/Patroller_images/Anlter_0": {"name": "Patroller,Chaser/Chaser Boss/Antler_2", "type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [2, 8, 106.96, -54.15, 0.616, 5, 176.07, -6.25, 0.384, 2, 8, 22.17, -7.04, 0.616, 5, 79.07, -6.25, 0.384, 2, 8, 120.76, 170.41, 0.616, 5, 79.07, 196.75, 0.384, 2, 8, 205.55, 123.3, 0.616, 5, 176.07, 196.75, 0.384], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 97, "height": 203}}, "Anlter_1_Behind": {"Patroller,Chaser/Patroller_images/Anlter_1": {"x": 30.84, "y": 9.88, "rotation": -64.59, "width": 49, "height": 119}}, "Anlter_3_Behind": {"Patroller,Chaser/Patroller_images/Anlter_3": {"name": "Patroller,Chaser/Chaser Boss/Antler_1", "type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 3, 0, 1, 2, 3], "vertices": [2, 9, 38.81, 8.57, 0.616, 5, -85.96, 6.29, 0.384, 2, 9, 110.97, 65.64, 0.616, 5, -177.96, 6.29, 0.384, 2, 9, 225.1, -78.7, 0.616, 5, -177.96, 190.29, 0.384, 2, 9, 152.93, -135.76, 0.616, 5, -85.96, 190.29, 0.384], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 92, "height": 184}}, "Anlter_4_Behind": {"Patroller,Chaser/Patroller_images/Anlter_4": {"name": "Patroller,Chaser/Patroller_images/Anlter_5", "path": "Patroller,Chaser/Patroller_images/Anlter_4", "x": 32.35, "y": -9.88, "rotation": -108.03, "width": 53, "height": 118}}, "EyeGoop1": {"EyeGoop1": {"name": "Patroller,Chaser/Chaser Boss/EyeGoop1", "x": -11.44, "y": -67.29, "width": 22, "height": 67}}, "EyeGoop2": {"EyeGoop2": {"name": "Patroller,Chaser/Chaser Boss/EyeGoop2", "x": 4.5, "y": -41.5, "width": 16, "height": 57}}, "EyeGoop3": {"EyeGoop3": {"name": "Patroller,Chaser/Chaser Boss/EyeGoop2", "x": 15.35, "y": -30.18, "scaleY": 0.6433, "width": 16, "height": 57}}, "EyeGoop4": {"EyeGoop4": {"name": "Patroller,Chaser/Chaser Boss/EyeGoop1", "x": 2.2, "y": -53.03, "width": 22, "height": 67}}, "EyeGoop5": {"EyeGoop5": {"name": "Patroller,Chaser/Chaser Boss/EyeGoop2", "x": -21.54, "y": -50.18, "width": 16, "height": 57}}, "EyeGoop6": {"EyeGoop6": {"name": "Patroller,Chaser/Chaser Boss/EyeGoop2", "x": 15.35, "y": -34.43, "scaleY": 1.0439, "width": 16, "height": 57}}, "EYE_LEFT_BTM": {"Patroller,Chaser/Pouncer Boss/Eye_Normal": {"name": "Patroller,Chaser/Chaser Boss/Eye", "x": 9.57, "y": 12.97, "width": 77, "height": 85}, "Patroller,Chaser/Pouncer Boss/Eye_Shocked": {"name": "Patroller,Chaser/Chaser Boss/Eye_Shocked", "x": 9.57, "y": 12.97, "width": 85, "height": 98}, "Patroller,Chaser/Pouncer Boss/Eye_Transform1": {"name": "<PERSON>ler,Chaser/Chaser Boss/Eye_Transform1", "x": 9.57, "y": 12.97, "width": 91, "height": 97}, "Patroller,Chaser/Pouncer Boss/Eye_Transform2": {"name": "<PERSON>ler,Chaser/Chaser Boss/Eye_Transform2", "x": 9.57, "y": 12.97, "width": 91, "height": 97}, "Patroller,Chaser/Pouncer Boss/Eye_Shut": {"x": 10.8, "y": 15.19, "width": 55, "height": 62}}, "EYE_RIGHT_BTM": {"Patroller,Chaser/Pouncer Boss/Eye_Normal": {"name": "Patroller,Chaser/Chaser Boss/Eye", "x": -12.3, "y": 12.97, "scaleX": -1, "width": 77, "height": 85}, "Patroller,Chaser/Pouncer Boss/Eye_Shocked": {"name": "Patroller,Chaser/Chaser Boss/Eye_Shocked", "x": -12.3, "y": 12.97, "scaleX": -1, "width": 85, "height": 98}, "Patroller,Chaser/Pouncer Boss/Eye_Transform1": {"name": "<PERSON>ler,Chaser/Chaser Boss/Eye_Transform1", "x": -12.3, "y": 12.97, "scaleX": -1, "width": 91, "height": 97}, "Patroller,Chaser/Pouncer Boss/Eye_Transform2": {"name": "<PERSON>ler,Chaser/Chaser Boss/Eye_Transform2", "x": -12.3, "y": 12.97, "scaleX": -1, "width": 91, "height": 97}, "Patroller,Chaser/Pouncer Boss/Eye_Shut": {"name": "<PERSON>ler,<PERSON>r/Pouncer Boss/Eye_Shut2", "path": "<PERSON>ler,<PERSON>r/Pouncer <PERSON>/Eye_Shut", "x": -21.27, "y": 15.19, "scaleX": -1, "width": 55, "height": 62}}, "Head": {"Patroller,Chaser/Patroller_images/Head": {"name": "Patroller,Chaser/Chaser Boss/Head", "y": 14.66, "width": 288, "height": 281}}, "MouthBtm": {"Mouth": {"name": "Patroller,Chaser/Chaser Boss/Mouth_Btm", "type": "mesh", "uvs": [0.59162, 0.15372, 0.7385, 0.12643, 0.93686, 0.41773, 1, 0.64189, 1, 1, 0.78077, 1, 0, 1, 0, 0.90216, 1e-05, 0.73113, 0, 0.52322, 0.06102, 0.29273, 0.1924, 0.36017, 0.23714, 0.16913, 0.36448, 0.20302, 0.35416, 0, 0.56409, 0, 0.06303, 0.33393, 0.16439, 0.59842, 0.21073, 0.57249, 0.25996, 0.48951, 0.3092, 0.58027, 0.35553, 0.57508, 0.40766, 0.60101, 0.47717, 0.42209, 0.56904, 0.6156, 0.61231, 0.6156, 0.62777, 0.5215, 0.67259, 0.52565, 0.77459, 0.6751, 0.88277, 0.3776, 0.84413, 0.33898, 0.76995, 0.60466, 0.67722, 0.48981, 0.60459, 0.50365, 0.58449, 0.57698, 0.48249, 0.38603, 0.39749, 0.56315, 0.32177, 0.55208, 0.26311, 0.475, 0.22684, 0.49091, 0.1913, 0.56713, 0.10265, 0.66349, 0.21777, 0.74079, 0.32929, 0.66671, 0.42642, 0.69247, 0.48757, 0.79232, 0.5919, 0.72146, 0.75738, 0.76978, 0.81493, 0.71824, 0.94444, 0.773, 0.72929, 0.29238], "triangles": [6, 42, 45, 45, 42, 44, 42, 43, 44, 5, 45, 47, 45, 46, 47, 49, 47, 48, 49, 5, 47, 42, 7, 8, 8, 41, 42, 45, 44, 46, 49, 48, 3, 47, 46, 28, 47, 28, 48, 28, 46, 25, 41, 17, 42, 42, 17, 43, 43, 18, 20, 18, 43, 17, 8, 9, 41, 25, 27, 28, 44, 24, 46, 46, 24, 25, 48, 28, 3, 43, 22, 44, 44, 22, 24, 43, 21, 22, 43, 20, 21, 41, 9, 17, 23, 24, 22, 17, 40, 18, 49, 3, 4, 5, 49, 4, 25, 26, 27, 27, 31, 28, 3, 28, 2, 2, 28, 29, 24, 34, 25, 25, 34, 26, 34, 24, 23, 27, 32, 31, 28, 31, 29, 29, 31, 30, 21, 36, 22, 40, 17, 16, 37, 20, 19, 20, 37, 21, 19, 20, 18, 34, 33, 26, 21, 37, 36, 18, 39, 19, 18, 40, 39, 39, 40, 11, 9, 16, 17, 10, 11, 40, 23, 22, 36, 19, 38, 37, 36, 37, 38, 27, 26, 32, 16, 9, 10, 26, 33, 32, 32, 33, 35, 19, 39, 38, 35, 34, 23, 31, 32, 30, 38, 39, 11, 2, 29, 30, 10, 40, 16, 6, 45, 5, 42, 6, 7, 30, 1, 2, 35, 33, 34, 35, 23, 36, 32, 35, 50, 50, 1, 30, 35, 0, 50, 0, 14, 15, 0, 13, 14, 35, 13, 0, 11, 12, 13, 50, 0, 1, 11, 13, 35, 35, 38, 11, 35, 36, 38, 32, 50, 30], "vertices": [1, 44, 18.55, -5.3, 1, 2, 44, 41.17, -0.61, 0.784, 43, 36.73, 64.25, 0.216, 2, 43, 67.28, 14.15, 0.822, 34, 68.23, 46.59, 0.178, 2, 43, 77, -24.4, 0.4245, 34, 77.95, 8.03, 0.5755, 2, 43, 77, -86, 0.7649, 34, 77.95, -53.56, 0.2351, 2, 43, 43.24, -86, 0.73918, 34, 44.19, -53.56, 0.26082, 2, 43, -77, -86, 0.86068, 34, -76.05, -53.56, 0.13932, 2, 43, -77, -69.17, 0.61313, 34, -76.05, -36.74, 0.38687, 2, 43, -77, -39.75, 0.14136, 34, -76.05, -7.32, 0.85864, 2, 43, -77, -3.99, 0.59201, 34, -76.05, 28.44, 0.40799, 2, 44, -63.16, -29.21, 0.288, 43, -67.6, 35.65, 0.712, 2, 44, -42.93, -40.81, 0.752, 43, -47.37, 24.05, 0.248, 2, 44, -36.04, -7.95, 0.984, 43, -40.48, 56.91, 0.016, 1, 44, -16.43, -13.78, 1, 1, 44, -18.02, 21.14, 1, 1, 44, 14.31, 21.14, 1, 2, 43, -67.29, 28.56, 0.87795, 34, -66.34, 61, 0.12205, 2, 43, -51.68, -16.93, 0.44685, 34, -50.73, 15.51, 0.55315, 2, 43, -44.55, -12.47, 0.51096, 34, -43.6, 19.97, 0.48904, 2, 43, -36.97, 1.8, 0.72813, 34, -36.02, 34.24, 0.27187, 2, 43, -29.38, -13.81, 0.50638, 34, -28.43, 18.63, 0.49362, 2, 43, -22.25, -12.91, 0.53609, 34, -21.3, 19.52, 0.46391, 2, 43, -14.22, -17.37, 0.43451, 34, -13.27, 15.06, 0.56549, 2, 43, -3.52, 13.4, 0.43886, 34, -2.57, 45.84, 0.56114, 2, 43, 10.63, -19.88, 0.39818, 34, 11.58, 12.55, 0.60182, 2, 43, 17.3, -19.88, 0.42641, 34, 18.25, 12.55, 0.57359, 2, 43, 19.68, -3.7, 0.78365, 34, 20.63, 28.74, 0.21635, 2, 43, 26.58, -4.41, 0.73766, 34, 27.53, 28.02, 0.26234, 2, 43, 42.29, -30.12, 0.32607, 34, 43.24, 2.32, 0.67393, 2, 43, 58.95, 21.05, 0.87923, 34, 59.9, 53.49, 0.12077, 2, 44, 57.44, -37.17, 0.768, 43, 53, 27.69, 0.232, 2, 43, 41.57, -18, 0.49457, 34, 42.52, 14.43, 0.50543, 2, 44, 31.73, -63.11, 0.208, 43, 27.29, 1.75, 0.792, 2, 44, 20.55, -65.49, 0.384, 43, 16.11, -0.63, 0.616, 2, 44, 17.45, -78.1, 0.4, 43, 13.01, -13.24, 0.6, 2, 44, 1.74, -45.26, 0.752, 43, -2.7, 19.6, 0.248, 2, 44, -11.35, -75.72, 0.512, 43, -15.79, -10.86, 0.488, 2, 44, -23.01, -73.82, 0.32, 43, -27.45, -8.96, 0.68, 2, 44, -32.04, -60.56, 0.448, 43, -36.48, 4.3, 0.552, 1, 43, -42.07, 1.56, 1, 2, 43, -47.54, -11.55, 0.52073, 34, -46.59, 20.89, 0.47927, 2, 43, -61.19, -28.12, 0.36473, 34, -60.24, 4.32, 0.63527, 2, 43, -43.46, -41.42, 0.25011, 34, -42.51, -8.98, 0.74989, 2, 43, -26.29, -28.67, 0.34681, 34, -25.34, 3.76, 0.65319, 2, 43, -11.33, -33.11, 0.32171, 34, -10.38, -0.67, 0.67829, 2, 43, -1.91, -50.28, 0.21857, 34, -0.96, -17.84, 0.78143, 2, 43, 14.15, -38.09, 0.29384, 34, 15.1, -5.66, 0.70617, 2, 43, 39.64, -46.4, 0.23784, 34, 40.59, -13.97, 0.76216, 2, 43, 48.5, -37.54, 0.30069, 34, 49.45, -5.1, 0.69931, 2, 43, 68.44, -46.96, 0.27265, 34, 69.39, -14.52, 0.72735, 2, 44, 39.75, -29.15, 0.896, 43, 35.31, 35.71, 0.104], "hull": 16, "edges": [18, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 4, 8, 6, 4, 6, 12, 14, 8, 10, 10, 12, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 20, 14, 16, 16, 18, 16, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 6, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 0, 0, 2, 2, 100, 100, 60, 2, 4, 18, 20], "width": 154, "height": 172}, "Mouth_shut": {"name": "Patroller,Chaser/Chaser Boss/Mouth_Btm", "type": "mesh", "uvs": [0.59162, 0.15372, 0.7385, 0.12643, 0.93686, 0.41773, 1, 0.64189, 1, 1, 0.78077, 1, 0, 1, 0, 0.90216, 1e-05, 0.73113, 0, 0.52322, 0.06102, 0.29273, 0.1924, 0.36017, 0.23714, 0.16913, 0.36448, 0.20302, 0.35416, 0, 0.56409, 0, 0.06303, 0.33393, 0.16439, 0.59842, 0.21073, 0.57249, 0.25996, 0.48951, 0.3092, 0.58027, 0.35553, 0.57508, 0.40766, 0.60101, 0.47717, 0.42209, 0.56904, 0.6156, 0.61231, 0.6156, 0.62777, 0.5215, 0.67259, 0.52565, 0.77459, 0.6751, 0.88277, 0.3776, 0.84413, 0.33898, 0.76995, 0.60466, 0.67722, 0.48981, 0.60459, 0.50365, 0.58449, 0.57698, 0.48249, 0.38603, 0.39749, 0.56315, 0.32177, 0.55208, 0.26311, 0.475, 0.22684, 0.49091, 0.1913, 0.56713, 0.10265, 0.66349, 0.21777, 0.74079, 0.32929, 0.66671, 0.42642, 0.69247, 0.48757, 0.79232, 0.5919, 0.72146, 0.75738, 0.76978, 0.81493, 0.71824, 0.94444, 0.773, 0.72929, 0.29238], "triangles": [6, 42, 45, 45, 42, 44, 42, 43, 44, 5, 45, 47, 45, 46, 47, 49, 47, 48, 49, 5, 47, 42, 7, 8, 8, 41, 42, 45, 44, 46, 49, 48, 3, 47, 46, 28, 47, 28, 48, 28, 46, 25, 41, 17, 42, 42, 17, 43, 43, 18, 20, 18, 43, 17, 8, 9, 41, 25, 27, 28, 44, 24, 46, 46, 24, 25, 48, 28, 3, 43, 22, 44, 44, 22, 24, 43, 21, 22, 43, 20, 21, 41, 9, 17, 23, 24, 22, 17, 40, 18, 49, 3, 4, 5, 49, 4, 25, 26, 27, 27, 31, 28, 3, 28, 2, 2, 28, 29, 24, 34, 25, 25, 34, 26, 34, 24, 23, 27, 32, 31, 28, 31, 29, 29, 31, 30, 21, 36, 22, 40, 17, 16, 37, 20, 19, 20, 37, 21, 19, 20, 18, 34, 33, 26, 21, 37, 36, 18, 39, 19, 18, 40, 39, 39, 40, 11, 9, 16, 17, 10, 11, 40, 23, 22, 36, 19, 38, 37, 36, 37, 38, 27, 26, 32, 16, 9, 10, 26, 33, 32, 32, 33, 35, 19, 39, 38, 35, 34, 23, 31, 32, 30, 38, 39, 11, 2, 29, 30, 10, 40, 16, 6, 45, 5, 42, 6, 7, 30, 1, 2, 35, 33, 34, 35, 23, 36, 32, 35, 50, 50, 1, 30, 35, 0, 50, 0, 14, 15, 0, 13, 14, 35, 13, 0, 11, 12, 13, 50, 0, 1, 11, 13, 35, 35, 38, 11, 35, 36, 38, 32, 50, 30], "vertices": [1, 44, 18.55, -5.3, 1, 2, 44, 41.17, -0.61, 0.784, 43, 36.73, 64.25, 0.216, 2, 43, 67.28, 14.15, 0.822, 34, 68.23, 46.59, 0.178, 2, 43, 77, -24.4, 0.4245, 34, 77.95, 8.03, 0.5755, 2, 43, 77, -86, 0.7649, 34, 77.95, -53.56, 0.2351, 2, 43, 43.24, -86, 0.73918, 34, 44.19, -53.56, 0.26082, 2, 43, -77, -86, 0.86068, 34, -76.05, -53.56, 0.13932, 2, 43, -77, -69.17, 0.61313, 34, -76.05, -36.74, 0.38687, 2, 43, -77, -39.75, 0.14136, 34, -76.05, -7.32, 0.85864, 2, 43, -77, -3.99, 0.59201, 34, -76.05, 28.44, 0.40799, 2, 44, -63.16, -29.21, 0.288, 43, -67.6, 35.65, 0.712, 2, 44, -42.93, -40.81, 0.752, 43, -47.37, 24.05, 0.248, 2, 44, -36.04, -7.95, 0.984, 43, -40.48, 56.91, 0.016, 1, 44, -16.43, -13.78, 1, 1, 44, -18.02, 21.14, 1, 1, 44, 14.31, 21.14, 1, 2, 43, -67.29, 28.56, 0.87795, 34, -66.34, 61, 0.12205, 2, 43, -51.68, -16.93, 0.44685, 34, -50.73, 15.51, 0.55315, 2, 43, -44.55, -12.47, 0.51096, 34, -43.6, 19.97, 0.48904, 2, 43, -36.97, 1.8, 0.72813, 34, -36.02, 34.24, 0.27187, 2, 43, -29.38, -13.81, 0.50638, 34, -28.43, 18.63, 0.49362, 2, 43, -22.25, -12.91, 0.53609, 34, -21.3, 19.52, 0.46391, 2, 43, -14.22, -17.37, 0.43451, 34, -13.27, 15.06, 0.56549, 2, 43, -3.52, 13.4, 0.43886, 34, -2.57, 45.84, 0.56114, 2, 43, 10.63, -19.88, 0.39818, 34, 11.58, 12.55, 0.60182, 2, 43, 17.3, -19.88, 0.42641, 34, 18.25, 12.55, 0.57359, 2, 43, 19.68, -3.7, 0.78365, 34, 20.63, 28.74, 0.21635, 2, 43, 26.58, -4.41, 0.73766, 34, 27.53, 28.02, 0.26234, 2, 43, 42.29, -30.12, 0.32607, 34, 43.24, 2.32, 0.67393, 2, 43, 58.95, 21.05, 0.87923, 34, 59.9, 53.49, 0.12077, 2, 44, 57.44, -37.17, 0.768, 43, 53, 27.69, 0.232, 2, 43, 41.57, -18, 0.49457, 34, 42.52, 14.43, 0.50543, 2, 44, 31.73, -63.11, 0.208, 43, 27.29, 1.75, 0.792, 2, 44, 20.55, -65.49, 0.384, 43, 16.11, -0.63, 0.616, 2, 44, 17.45, -78.1, 0.4, 43, 13.01, -13.24, 0.6, 2, 44, 1.74, -45.26, 0.752, 43, -2.7, 19.6, 0.248, 2, 44, -11.35, -75.72, 0.512, 43, -15.79, -10.86, 0.488, 2, 44, -23.01, -73.82, 0.32, 43, -27.45, -8.96, 0.68, 2, 44, -32.04, -60.56, 0.448, 43, -36.48, 4.3, 0.552, 1, 43, -42.07, 1.56, 1, 2, 43, -47.54, -11.55, 0.52073, 34, -46.59, 20.89, 0.47927, 2, 43, -61.19, -28.12, 0.36473, 34, -60.24, 4.32, 0.63527, 2, 43, -43.46, -41.42, 0.25011, 34, -42.51, -8.98, 0.74989, 2, 43, -26.29, -28.67, 0.34681, 34, -25.34, 3.76, 0.65319, 2, 43, -11.33, -33.11, 0.32171, 34, -10.38, -0.67, 0.67829, 2, 43, -1.91, -50.28, 0.21857, 34, -0.96, -17.84, 0.78143, 2, 43, 14.15, -38.09, 0.29384, 34, 15.1, -5.66, 0.70617, 2, 43, 39.64, -46.4, 0.23784, 34, 40.59, -13.97, 0.76216, 2, 43, 48.5, -37.54, 0.30069, 34, 49.45, -5.1, 0.69931, 2, 43, 68.44, -46.96, 0.27265, 34, 69.39, -14.52, 0.72735, 2, 44, 39.75, -29.15, 0.896, 43, 35.31, 35.71, 0.104], "hull": 16, "edges": [18, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 4, 8, 6, 4, 6, 12, 14, 8, 10, 10, 12, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 20, 14, 16, 16, 18, 16, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 6, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 0, 0, 2, 2, 100, 100, 60, 2, 4, 18, 20], "width": 154, "height": 172}}, "Patroller,Chaser/Pouncer Boss/Mouth2": {"Mouth": {"name": "Patroller,Chaser/Chaser Boss/Mouth", "type": "mesh", "uvs": [0.59162, 0.15372, 0.7385, 0.12643, 0.93686, 0.41773, 1, 0.64189, 1, 1, 0.78077, 1, 0, 1, 0, 0.90216, 1e-05, 0.73113, 0, 0.52322, 0.06102, 0.29273, 0.1924, 0.36017, 0.23714, 0.16913, 0.36448, 0.20302, 0.35416, 0, 0.56409, 0, 0.06303, 0.33393, 0.16439, 0.59842, 0.21073, 0.57249, 0.25996, 0.48951, 0.3092, 0.58027, 0.35553, 0.57508, 0.40766, 0.60101, 0.47717, 0.42209, 0.56904, 0.6156, 0.61231, 0.6156, 0.62777, 0.5215, 0.67259, 0.52565, 0.77459, 0.6751, 0.88277, 0.3776, 0.84413, 0.33898, 0.76995, 0.60466, 0.67722, 0.48981, 0.60459, 0.50365, 0.58449, 0.57698, 0.48249, 0.38603, 0.39749, 0.56315, 0.32177, 0.55208, 0.26311, 0.475, 0.22684, 0.49091, 0.1913, 0.56713, 0.10265, 0.66349, 0.21777, 0.74079, 0.32929, 0.66671, 0.42642, 0.69247, 0.48757, 0.79232, 0.5919, 0.72146, 0.75738, 0.76978, 0.81493, 0.71824, 0.94444, 0.773, 0.72929, 0.29238], "triangles": [6, 42, 45, 45, 42, 44, 42, 43, 44, 5, 45, 47, 45, 46, 47, 49, 47, 48, 49, 5, 47, 42, 7, 8, 8, 41, 42, 45, 44, 46, 49, 48, 3, 47, 46, 28, 47, 28, 48, 28, 46, 25, 41, 17, 42, 42, 17, 43, 8, 9, 41, 44, 24, 46, 46, 24, 25, 48, 28, 3, 43, 22, 44, 49, 3, 4, 44, 22, 24, 18, 43, 17, 43, 21, 22, 31, 32, 30, 43, 20, 21, 25, 27, 28, 19, 39, 38, 19, 20, 18, 25, 26, 27, 18, 40, 39, 5, 49, 4, 3, 28, 2, 2, 28, 29, 27, 32, 31, 28, 31, 29, 29, 31, 30, 40, 17, 16, 9, 16, 17, 16, 9, 10, 2, 29, 30, 10, 40, 16, 6, 45, 5, 42, 6, 7, 18, 39, 19, 27, 26, 32, 27, 31, 28, 17, 40, 18, 43, 18, 20, 41, 9, 17, 23, 24, 22, 25, 34, 26, 20, 37, 21, 26, 33, 32, 24, 34, 25, 34, 24, 23, 34, 33, 26, 19, 38, 37, 21, 36, 22, 23, 22, 36, 36, 37, 38, 32, 33, 35, 30, 1, 2, 35, 33, 34, 35, 23, 36, 32, 35, 50, 50, 1, 30, 35, 0, 50, 0, 14, 15, 0, 13, 14, 35, 13, 0, 11, 12, 13, 50, 0, 1, 11, 13, 35, 35, 38, 11, 35, 36, 38, 32, 50, 30, 35, 34, 23, 21, 37, 36, 38, 39, 11, 37, 20, 19, 39, 40, 11, 10, 11, 40], "vertices": [1, 44, 18.55, -5.3, 1, 2, 44, 41.17, -0.61, 0.784, 43, 36.73, 64.25, 0.216, 2, 43, 67.28, 14.15, 0.822, 34, 68.23, 46.59, 0.178, 2, 43, 77, -24.4, 0.4245, 34, 77.95, 8.03, 0.5755, 2, 43, 77, -86, 0.7649, 34, 77.95, -53.56, 0.2351, 2, 43, 43.24, -86, 0.73918, 34, 44.19, -53.56, 0.26082, 2, 43, -77, -86, 0.86068, 34, -76.05, -53.56, 0.13932, 2, 43, -77, -69.17, 0.61313, 34, -76.05, -36.74, 0.38687, 2, 43, -77, -39.75, 0.14136, 34, -76.05, -7.32, 0.85864, 2, 43, -77, -3.99, 0.59201, 34, -76.05, 28.44, 0.40799, 2, 44, -63.16, -29.21, 0.288, 43, -67.6, 35.65, 0.712, 2, 44, -42.93, -40.81, 0.752, 43, -47.37, 24.05, 0.248, 2, 44, -36.04, -7.95, 0.984, 43, -40.48, 56.91, 0.016, 1, 44, -16.43, -13.78, 1, 1, 44, -18.02, 21.14, 1, 1, 44, 14.31, 21.14, 1, 2, 43, -67.29, 28.56, 0.87795, 34, -66.34, 61, 0.12205, 2, 43, -51.68, -16.93, 0.64685, 34, -50.73, 15.51, 0.35315, 3, 44, -40.11, -77.33, 0.41589, 43, -44.55, -12.47, 0.45157, 34, -43.6, 19.97, 0.13255, 2, 44, -32.53, -63.06, 0.39258, 43, -36.97, 1.8, 0.60742, 3, 44, -24.94, -78.67, 0.41634, 43, -29.38, -13.81, 0.448, 34, -28.43, 18.63, 0.13566, 3, 44, -17.81, -77.77, 0.41349, 43, -22.25, -12.91, 0.47105, 34, -21.3, 19.52, 0.11546, 3, 44, -9.78, -82.23, 0.42399, 43, -14.22, -17.37, 0.39148, 34, -13.27, 15.06, 0.18453, 3, 44, 0.92, -51.46, 0.42349, 43, -3.52, 13.4, 0.39494, 34, -2.57, 45.84, 0.18157, 3, 44, 15.07, -84.74, 0.42833, 43, 10.63, -19.88, 0.36243, 34, 11.58, 12.55, 0.20924, 3, 44, 21.74, -84.74, 0.42493, 43, 17.3, -19.88, 0.38503, 34, 18.25, 12.55, 0.19004, 2, 44, 24.12, -68.56, 0.3752, 43, 19.68, -3.7, 0.6248, 2, 44, 31.02, -69.27, 0.38948, 43, 26.58, -4.41, 0.61052, 2, 43, 42.29, -30.12, 0.52607, 34, 43.24, 2.32, 0.47393, 2, 43, 58.95, 21.05, 0.87923, 34, 59.9, 53.49, 0.12077, 2, 44, 57.44, -37.17, 0.768, 43, 53, 27.69, 0.232, 2, 43, 41.57, -18, 0.69457, 34, 42.52, 14.43, 0.30543, 2, 44, 31.73, -63.11, 0.528, 43, 27.29, 1.75, 0.472, 2, 44, 20.55, -65.49, 0.704, 43, 16.11, -0.63, 0.296, 2, 44, 17.45, -78.1, 0.72, 43, 13.01, -13.24, 0.28, 2, 44, 1.74, -45.26, 0.752, 43, -2.7, 19.6, 0.248, 2, 44, -11.35, -75.72, 0.832, 43, -15.79, -10.86, 0.168, 2, 44, -23.01, -73.82, 0.64, 43, -27.45, -8.96, 0.36, 2, 44, -32.04, -60.56, 0.768, 43, -36.48, 4.3, 0.232, 2, 44, -37.63, -63.3, 0.32, 43, -42.07, 1.56, 0.68, 3, 44, -43.1, -76.41, 0.41494, 43, -47.54, -11.55, 0.45915, 34, -46.59, 20.89, 0.12591, 2, 43, -61.19, -28.12, 0.36473, 34, -60.24, 4.32, 0.63527, 2, 43, -43.46, -41.42, 0.25011, 34, -42.51, -8.98, 0.74989, 2, 43, -26.29, -28.67, 0.34681, 34, -25.34, 3.76, 0.65319, 2, 43, -11.33, -33.11, 0.32171, 34, -10.38, -0.67, 0.67829, 2, 43, -1.91, -50.28, 0.21857, 34, -0.96, -17.84, 0.78143, 2, 43, 14.15, -38.09, 0.29384, 34, 15.1, -5.66, 0.70617, 2, 43, 39.64, -46.4, 0.23784, 34, 40.59, -13.97, 0.76216, 2, 43, 48.5, -37.54, 0.30069, 34, 49.45, -5.1, 0.69931, 2, 43, 68.44, -46.96, 0.27265, 34, 69.39, -14.52, 0.72735, 2, 44, 39.75, -29.15, 0.896, 43, 35.31, 35.71, 0.104], "hull": 16, "edges": [18, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 4, 8, 6, 4, 6, 12, 14, 8, 10, 10, 12, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 20, 14, 16, 16, 18, 16, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 6, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 0, 0, 2, 2, 100, 100, 60, 2, 4, 18, 20], "width": 154, "height": 172}, "Mouth_shut": {"name": "Patroller,Chaser/Chaser Boss/Mouth", "type": "mesh", "uvs": [1, 0.64189, 1, 1, 0.78077, 1, 0, 1, 0, 0.90216, 1e-05, 0.73113, 0, 0.52322, 0, 0.24812, 0, 0, 0.67568, 0, 1, 0, 0.06303, 0.33393, 0.16439, 0.59842, 0.21073, 0.57249, 0.25996, 0.48951, 0.3092, 0.58027, 0.35553, 0.57508, 0.40766, 0.60101, 0.47717, 0.42209, 0.56904, 0.6156, 0.61231, 0.6156, 0.62777, 0.5215, 0.67259, 0.52565, 0.77459, 0.6751, 0.88277, 0.3776, 0.93686, 0.41773, 0.84413, 0.33898, 0.76995, 0.60466, 0.67722, 0.48981, 0.60459, 0.50365, 0.58449, 0.57698, 0.48249, 0.38603, 0.39749, 0.56315, 0.32177, 0.55208, 0.26311, 0.475, 0.22684, 0.49091, 0.1913, 0.56713, 0.06102, 0.29273, 0.10265, 0.66349, 0.21777, 0.74079, 0.32929, 0.66671, 0.42642, 0.69247, 0.48757, 0.79232, 0.5919, 0.72146, 0.75738, 0.76978, 0.81493, 0.71824, 0.94444, 0.773], "triangles": [37, 7, 8, 9, 37, 8, 37, 36, 11, 26, 9, 10, 9, 31, 37, 24, 26, 10, 9, 26, 31, 25, 24, 10, 37, 31, 34, 31, 32, 34, 37, 34, 35, 28, 31, 26, 27, 28, 26, 31, 30, 18, 14, 35, 34, 0, 25, 10, 28, 29, 31, 21, 29, 28, 11, 6, 7, 11, 7, 37, 11, 12, 6, 22, 21, 28, 32, 33, 34, 14, 34, 33, 31, 18, 32, 18, 17, 32, 37, 35, 36, 13, 36, 35, 13, 35, 14, 16, 33, 32, 31, 29, 30, 30, 29, 21, 14, 15, 13, 15, 33, 16, 33, 15, 14, 36, 12, 11, 16, 32, 17, 24, 27, 26, 23, 27, 24, 22, 28, 27, 30, 19, 18, 20, 30, 21, 19, 30, 20, 25, 23, 24, 0, 23, 25, 22, 27, 23, 20, 21, 22, 12, 36, 13, 18, 19, 17, 38, 6, 12, 40, 15, 16, 40, 16, 17, 41, 17, 19, 40, 17, 41, 45, 23, 0, 43, 19, 20, 41, 19, 43, 20, 22, 23, 5, 6, 38, 13, 40, 12, 40, 13, 15, 39, 12, 40, 38, 12, 39, 23, 43, 20, 44, 23, 45, 44, 43, 23, 46, 45, 0, 42, 41, 43, 5, 38, 39, 39, 4, 5, 46, 2, 44, 46, 44, 45, 42, 43, 44, 2, 42, 44, 39, 40, 41, 42, 39, 41, 3, 39, 42, 3, 42, 2, 46, 0, 1, 2, 46, 1, 39, 3, 4], "vertices": [2, 34, 77.95, 8.03, 0.5755, 43, 77, -24.4, 0.4245, 2, 34, 77.95, -53.56, 0.3951, 43, 77, -86, 0.6049, 2, 34, 44.19, -53.56, 0.54882, 43, 43.24, -86, 0.45118, 2, 34, -76.05, -53.56, 0.44332, 43, -77, -86, 0.55668, 2, 34, -76.05, -36.74, 0.53087, 43, -77, -69.17, 0.46913, 2, 34, -76.05, -7.32, 0.85864, 43, -77, -39.75, 0.14136, 2, 34, -76.05, 28.44, 0.40799, 43, -77, -3.99, 0.59201, 1, 43, -77, 43.32, 1, 1, 43, -77, 86, 1, 1, 43, 27.05, 86, 1, 1, 43, 77, 86, 1, 2, 34, -66.34, 61, 0.12205, 43, -67.29, 28.56, 0.87795, 2, 34, -50.73, 15.51, 0.55315, 43, -51.68, -16.93, 0.44685, 2, 34, -43.6, 19.97, 0.48904, 43, -44.55, -12.47, 0.51096, 2, 34, -36.02, 34.24, 0.27187, 43, -36.97, 1.8, 0.72813, 2, 34, -28.43, 18.63, 0.49362, 43, -29.38, -13.81, 0.50638, 2, 34, -21.3, 19.52, 0.46391, 43, -22.25, -12.91, 0.53609, 2, 34, -13.27, 15.06, 0.56549, 43, -14.22, -17.37, 0.43451, 2, 34, -2.57, 45.84, 0.56114, 43, -3.52, 13.4, 0.43886, 2, 34, 11.58, 12.55, 0.60182, 43, 10.63, -19.88, 0.39818, 2, 34, 18.25, 12.55, 0.57359, 43, 17.3, -19.88, 0.42641, 2, 34, 20.63, 28.74, 0.21635, 43, 19.68, -3.7, 0.78365, 2, 34, 27.53, 28.02, 0.26234, 43, 26.58, -4.41, 0.73766, 2, 34, 43.24, 2.32, 0.67393, 43, 42.29, -30.12, 0.32607, 2, 34, 59.9, 53.49, 0.12077, 43, 58.95, 21.05, 0.87923, 2, 34, 68.23, 46.59, 0.178, 43, 67.28, 14.15, 0.822, 1, 43, 53, 27.69, 1, 2, 34, 42.52, 14.43, 0.50543, 43, 41.57, -18, 0.49457, 1, 43, 27.29, 1.75, 1, 1, 43, 16.11, -0.63, 1, 1, 43, 13.01, -13.24, 1, 1, 43, -2.7, 19.6, 1, 1, 43, -15.79, -10.86, 1, 1, 43, -27.45, -8.96, 1, 1, 43, -36.48, 4.3, 1, 1, 43, -42.07, 1.56, 1, 2, 34, -46.59, 20.89, 0.47927, 43, -47.54, -11.55, 0.52073, 1, 43, -67.6, 35.65, 1, 2, 34, -60.24, 4.32, 0.63527, 43, -61.19, -28.12, 0.36473, 2, 34, -42.51, -8.98, 0.74989, 43, -43.46, -41.42, 0.25011, 2, 34, -25.34, 3.76, 0.65319, 43, -26.29, -28.67, 0.34681, 2, 34, -10.38, -0.67, 0.67829, 43, -11.33, -33.11, 0.32171, 2, 34, -0.96, -17.84, 0.78143, 43, -1.91, -50.28, 0.21857, 2, 34, 15.1, -5.66, 0.70617, 43, 14.15, -38.09, 0.29384, 2, 34, 40.59, -13.97, 0.76216, 43, 39.64, -46.4, 0.23784, 2, 34, 49.45, -5.1, 0.69931, 43, 48.5, -37.54, 0.30069, 2, 34, 69.39, -14.52, 0.72735, 43, 68.44, -46.96, 0.27265], "hull": 11, "edges": [12, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 2, 0, 0, 20, 50, 0, 6, 8, 2, 4, 4, 6, 16, 18, 18, 20, 18, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 12, 14, 14, 16, 74, 14, 8, 10, 10, 12, 10, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 0], "width": 154, "height": 172}}}}, {"name": "CHASERBOSS_BODY_0_BACK", "bones": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "HeadFleshball_1_Behind", "HeadFleshball_3_Behind"], "attachments": {"Anlter_0_Front": {"Patroller,Chaser/Patroller_images/Anlter_0": {"name": "Patroller,Chaser/Chaser Boss/Antler_2", "type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 3, 0, 1, 2, 3], "vertices": [2, 8, 106.96, -54.15, 0.616, 5, 176.07, -6.25, 0.384, 2, 8, 22.17, -7.04, 0.616, 5, 79.07, -6.25, 0.384, 2, 8, 120.76, 170.41, 0.616, 5, 79.07, 196.75, 0.384, 2, 8, 205.55, 123.3, 0.616, 5, 176.07, 196.75, 0.384], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 97, "height": 203}}, "Anlter_3_Front": {"Patroller,Chaser/Patroller_images/Anlter_3": {"name": "Patroller,Chaser/Chaser Boss/Antler_1", "type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [2, 9, 33.18, 4.12, 0.616, 5, -78.78, 6.29, 0.384, 2, 9, 105.35, 61.18, 0.616, 5, -170.78, 6.29, 0.384, 2, 9, 219.47, -83.15, 0.616, 5, -170.78, 190.29, 0.384, 2, 9, 147.3, -140.21, 0.616, 5, -78.78, 190.29, 0.384], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 92, "height": 184}}, "EYE_LEFT_BTM": {"Patroller,Chaser/Pouncer Boss/Eye_Transform1": {"name": "Patroller,Chaser/Chaser Boss/Eye", "x": 9.57, "y": 12.97, "width": 77, "height": 85}, "Patroller,Chaser/Pouncer Boss/Eye_Transform2": {"name": "Patroller,Chaser/Chaser Boss/Eye", "x": 9.57, "y": 12.97, "width": 77, "height": 85}, "Patroller,Chaser/Pouncer Boss/Eye_Normal": {"name": "Patroller,Chaser/Chaser Boss/Eye", "x": 9.57, "y": 12.97, "width": 77, "height": 85}, "Patroller,Chaser/Pouncer Boss/Eye_Shocked": {"name": "Patroller,Chaser/Chaser Boss/Eye_Shocked", "x": 9.57, "y": 12.97, "width": 85, "height": 98}, "Patroller,Chaser/Pouncer Boss/Eye_Shut": {"x": 10.8, "y": 15.19, "width": 55, "height": 62}}, "EYE_RIGHT_BTM": {"Patroller,Chaser/Pouncer Boss/Eye_Transform1": {"name": "Patroller,Chaser/Chaser Boss/Eye", "x": -12.3, "y": 12.97, "scaleX": -1, "width": 77, "height": 85}, "Patroller,Chaser/Pouncer Boss/Eye_Transform2": {"name": "Patroller,Chaser/Chaser Boss/Eye", "x": -12.3, "y": 12.97, "scaleX": -1, "width": 77, "height": 85}, "Patroller,Chaser/Pouncer Boss/Eye_Normal": {"name": "Patroller,Chaser/Chaser Boss/Eye", "x": -12.3, "y": 12.97, "scaleX": -1, "width": 77, "height": 85}, "Patroller,Chaser/Pouncer Boss/Eye_Shocked": {"name": "Patroller,Chaser/Chaser Boss/Eye_Shocked", "x": -12.3, "y": 12.97, "scaleX": -1, "width": 85, "height": 98}, "Patroller,Chaser/Pouncer Boss/Eye_Shut": {"name": "<PERSON>ler,<PERSON>r/Pouncer Boss/Eye_Shut2", "path": "<PERSON>ler,<PERSON>r/Pouncer <PERSON>/Eye_Shut", "x": -21.27, "y": 15.19, "scaleX": -1, "width": 55, "height": 62}}, "Head": {"Patroller,Chaser/Patroller_images/Head": {"name": "Patroller,Chaser/Chaser Boss/Head_Back", "y": 14.66, "width": 288, "height": 281}}}}, {"name": "CHASERBOSS_BODY_1", "bones": ["Fleshball_2", "Fleshball_1", "Fleshball_3", "Fleshball_4", "Fleshball_5"], "attachments": {"Body_0": {"Body_0": {"name": "<PERSON>ler,Chaser/Chaser Boss/Body_0_Front", "x": -0.37, "y": 86.69, "width": 200, "height": 199}}, "Body_1": {"Body_1": {"name": "<PERSON>ler,Chaser/Chaser Boss/Body_0_Back", "type": "mesh", "uvs": [0.5767, 0.09391, 0.67757, 0.0765, 0.71874, 0.15857, 0.78874, 0.14862, 0.85049, 0.05164, 0.93901, 0.06656, 0.89167, 0.17349, 0.91637, 0.2083, 0.89784, 0.26549, 0.94313, 0.26301, 0.9596, 0.34258, 0.8999, 0.3575, 1, 0.46692, 1, 0.57633, 0.92049, 0.62607, 0.94725, 0.68078, 0.89784, 0.77279, 0.91843, 0.89712, 0.85461, 1, 0.16086, 1, 0.08469, 0.8822, 0.11763, 0.81009, 0.04099, 0.63303, 0.06411, 0.56639, 0, 0.59125, 0, 0.50671, 0.06205, 0.48433, 0, 0.452, 0, 0.37242, 0.05175, 0.29782, 0, 0.29285, 0, 0.23565, 0.0497, 0.19835, 0.12586, 0.12126, 0.24938, 0.14116, 0.25144, 0.00995, 0.33378, 1e-05, 0.37907, 0.05164, 0.46348, 0, 0.54582, 0, 0.29879, 0.58379, 0.32349, 0.32269, 0.65493, 0.30528, 0.70639, 0.63353], "triangles": [6, 4, 5, 3, 4, 6, 8, 6, 7, 29, 30, 31, 29, 32, 33, 29, 31, 32, 2, 42, 0, 2, 0, 1, 42, 2, 3, 8, 42, 3, 8, 3, 6, 34, 36, 37, 36, 34, 35, 41, 34, 37, 37, 0, 41, 38, 0, 37, 39, 0, 38, 41, 0, 42, 11, 8, 9, 11, 9, 10, 42, 8, 11, 26, 27, 28, 29, 41, 26, 26, 28, 29, 34, 29, 33, 41, 29, 34, 40, 23, 26, 25, 26, 23, 14, 11, 12, 41, 40, 26, 24, 25, 23, 13, 14, 12, 43, 42, 11, 43, 11, 14, 40, 41, 42, 43, 40, 42, 16, 43, 14, 16, 14, 15, 21, 23, 40, 22, 23, 21, 19, 21, 40, 18, 43, 16, 18, 16, 17, 19, 40, 43, 18, 19, 43, 20, 21, 19], "vertices": [2, 3, 20.51, 192.22, 0.696, 14, 21.35, 119.63, 0.304, 1, 3, 48.05, 196.15, 1, 2, 3, 59.29, 177.6, 0.84, 16, 122.32, 19.37, 0.16, 2, 3, 78.39, 179.85, 0.872, 16, 135.53, 5.38, 0.128, 2, 3, 95.25, 201.77, 0.68, 16, 163.18, 4.93, 0.32, 2, 3, 119.42, 198.4, 0.568, 16, 174.89, -16.48, 0.432, 2, 3, 106.49, 174.23, 0.76, 16, 147.79, -20.52, 0.24, 2, 3, 113.24, 166.36, 0.936, 16, 145.5, -30.63, 0.064, 2, 3, 108.18, 153.44, 0.808, 16, 132.11, -34.28, 0.192, 2, 3, 120.54, 154, 0.68, 16, 139.93, -43.87, 0.32, 2, 3, 125.04, 136.02, 0.808, 16, 128.19, -58.21, 0.192, 1, 3, 108.74, 132.64, 1, 2, 3, 136.07, 107.92, 0.808, 14, 66.39, -16.14, 0.192, 2, 3, 136.07, 83.19, 0.664, 14, 52.02, -36.26, 0.336, 2, 3, 114.36, 71.95, 0.824, 16, 70.4, -87.87, 0.176, 2, 3, 121.67, 59.58, 0.728, 16, 64.84, -101.11, 0.272, 2, 3, 108.18, 38.79, 0.776, 16, 40.11, -102.7, 0.224, 1, 3, 113.8, 10.69, 1, 1, 3, 96.38, -12.56, 1, 1, 3, -93.01, -12.56, 1, 1, 3, -113.81, 14.06, 1, 1, 3, -104.82, 30.36, 1, 2, 3, -125.74, 70.38, 0.76, 16, -74.13, 103.86, 0.24, 2, 3, -119.43, 85.44, 0.728, 16, -58.28, 107.79, 0.272, 2, 3, -136.93, 79.82, 0.52, 16, -73.23, 118.48, 0.48, 2, 3, -136.93, 98.92, 0.584, 16, -57.9, 129.88, 0.416, 2, 3, -119.99, 103.98, 0.84, 16, -43.73, 119.3, 0.16, 2, 3, -136.93, 111.29, 0.856, 16, -47.98, 137.26, 0.144, 1, 3, -136.93, 129.27, 1, 2, 3, -122.8, 146.13, 0.744, 14, -122.05, 165.43, 0.256, 2, 3, -136.93, 147.26, 0.76, 14, -132.89, 174.55, 0.24, 2, 3, -136.93, 160.18, 0.728, 14, -125.38, 185.07, 0.272, 2, 3, -123.36, 168.61, 0.664, 14, -109.44, 184.05, 0.336, 2, 3, -102.57, 186.03, 0.888, 14, -82.39, 186.14, 0.112, 2, 3, -68.85, 181.54, 0.696, 16, 49.02, 124.54, 0.304, 2, 3, -68.29, 211.19, 0.584, 16, 73.15, 141.79, 0.416, 2, 3, -45.81, 213.44, 0.744, 16, 88.37, 125.09, 0.256, 3, 3, -33.44, 201.77, 0.71597, 14, -17, 158.76, 0.208, 16, 86.38, 108.2, 0.07603, 2, 3, -10.4, 213.44, 0.68, 14, 8.53, 154.87, 0.32, 2, 3, 12.08, 213.44, 0.776, 14, 26.83, 141.8, 0.224, 1, 3, -55.36, 81.5, 1, 1, 3, -48.62, 140.51, 1, 1, 3, 41.86, 144.45, 1, 1, 3, 55.91, 70.26, 1], "hull": 40, "edges": [38, 40, 40, 42, 46, 48, 36, 38, 36, 34, 34, 32, 32, 30, 30, 28, 28, 26, 26, 24, 22, 24, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 0, 0, 78, 76, 78, 74, 72, 74, 76, 70, 72, 70, 68, 68, 66, 66, 64, 64, 62, 60, 62, 60, 58, 58, 56, 54, 56, 54, 52, 48, 50, 52, 50, 42, 44, 44, 46, 80, 82, 82, 84, 84, 86, 80, 86, 84, 16], "width": 273, "height": 226}}}}, {"name": "CHASERBOSS_BODY_2", "bones": ["Fleshball_7", "Fleshball_8", "Fleshball_6"], "attachments": {"Body_1": {"Body_1": {"name": "Patroller,Chaser/Chaser Boss/Body_1", "type": "mesh", "uvs": [0.65327, 0.15873, 0.73616, 0.14226, 0.83632, 0.03318, 0.93315, 0.08177, 0.87284, 0.17979, 0.9167, 0.26148, 0.82897, 0.3595, 1, 0.47713, 1, 0.58169, 0.85365, 0.55555, 0.94412, 0.6209, 0.89751, 0.82674, 0.93041, 0.90516, 0.82623, 1, 0.12141, 1, 0.04198, 0.85026, 0, 0.83379, 0, 0.74529, 0.04543, 0.72883, 0, 0.67532, 0.0368, 0.59093, 0, 0.50243, 0, 0.40776, 0.03334, 0.38306, 0, 0.34396, 0, 0.24723, 0.04198, 0.25546, 0.07651, 0.15667, 0.19221, 0.15049, 0.20257, 0, 0.31654, 0, 0.3977, 0.04965, 0.46332, 0, 0.63946, 0, 0.53585, 0.20195, 0.66881, 0.21224, 0.71198, 0.34396, 0.64982, 0.4057, 0.70853, 0.47979, 0.65155, 0.56006, 0.26992, 0.5436, 0.15767, 0.42423, 0.18185, 0.35219, 0.24401, 0.34602, 0.19566, 0.21635, 0.26128, 0.15873, 0.3269, 0.17931], "triangles": [15, 16, 17, 18, 15, 17, 15, 18, 14, 13, 11, 12, 13, 39, 11, 14, 39, 13, 45, 29, 30, 46, 45, 30, 28, 29, 45, 31, 46, 30, 31, 32, 34, 46, 31, 34, 44, 28, 45, 24, 25, 26, 43, 44, 45, 43, 45, 46, 27, 44, 26, 44, 27, 28, 42, 44, 43, 42, 26, 44, 23, 24, 26, 42, 23, 26, 41, 23, 42, 38, 37, 36, 21, 22, 23, 37, 40, 43, 41, 42, 43, 40, 41, 43, 9, 6, 7, 38, 6, 9, 39, 37, 38, 9, 7, 8, 41, 21, 23, 20, 41, 40, 20, 21, 41, 18, 20, 40, 19, 20, 18, 11, 9, 10, 14, 18, 40, 39, 9, 11, 9, 39, 38, 43, 46, 34, 43, 34, 37, 39, 40, 37, 14, 40, 39, 4, 2, 3, 1, 2, 4, 34, 32, 33, 34, 33, 0, 35, 0, 1, 34, 0, 35, 36, 35, 1, 6, 36, 1, 4, 6, 1, 5, 6, 4, 37, 34, 35, 37, 35, 36, 38, 36, 6], "vertices": [3, 3, 36.72, 130.15, 0.57658, 16, 70.78, 9.16, 0.272, 15, 7.35, 9.56, 0.15142, 2, 3, 53.71, 132.98, 0.76, 15, 19.65, -2.5, 0.24, 2, 3, 74.25, 151.74, 0.504, 15, 46.91, -8.04, 0.496, 2, 3, 94.1, 143.39, 0.664, 15, 51.85, -29, 0.336, 2, 3, 81.73, 126.53, 0.856, 15, 30.94, -28.94, 0.144, 2, 3, 90.72, 112.48, 0.776, 15, 24.89, -44.49, 0.224, 2, 3, 72.74, 95.62, 0.776, 16, 64.56, -40.35, 0.224, 2, 3, 107.8, 75.38, 0.632, 16, 69.24, -80.55, 0.368, 2, 3, 107.8, 57.4, 0.568, 16, 54.81, -91.29, 0.432, 2, 3, 77.8, 61.9, 0.808, 16, 40.52, -64.53, 0.192, 2, 3, 96.34, 50.66, 0.84, 16, 42.56, -86.12, 0.16, 1, 3, 86.79, 15.25, 1, 1, 3, 93.53, 1.76, 1, 1, 3, 72.18, -14.55, 1, 1, 3, -72.31, -14.55, 1, 2, 3, -88.59, 11.21, 0.872, 16, -99.45, 38.75, 0.128, 2, 3, -97.2, 14.04, 0.728, 16, -102.31, 47.34, 0.272, 2, 3, -97.2, 29.26, 0.712, 16, -90.09, 56.43, 0.288, 2, 3, -87.89, 32.09, 0.728, 16, -82.26, 50.64, 0.272, 2, 3, -97.2, 41.3, 0.76, 16, -80.44, 63.61, 0.24, 1, 3, -89.66, 55.81, 1, 1, 3, -97.2, 71.03, 1, 2, 3, -97.2, 87.32, 0.792, 16, -43.51, 91.07, 0.208, 2, 3, -90.36, 91.56, 0.808, 16, -36.02, 88.12, 0.192, 2, 3, -97.2, 98.29, 0.472, 16, -34.7, 97.62, 0.528, 2, 3, -97.2, 114.93, 0.632, 16, -21.35, 107.55, 0.368, 2, 3, -88.59, 113.51, 0.76, 16, -17.35, 99.79, 0.24, 2, 3, -81.51, 130.5, 0.888, 16, 0.51, 104.25, 0.112, 2, 3, -57.8, 131.57, 0.792, 15, -47.19, 86.77, 0.208, 2, 3, -55.67, 157.45, 0.68, 15, -25.03, 100.3, 0.32, 2, 3, -32.31, 157.45, 0.696, 15, -11.26, 81.42, 0.304, 3, 3, -15.67, 148.91, 0.78752, 16, 54.57, 62.4, 0.144, 15, -8.36, 62.95, 0.06848, 2, 3, -2.22, 157.45, 0.664, 16, 69.45, 56.7, 0.336, 2, 3, 33.89, 157.45, 0.712, 16, 91, 27.73, 0.288, 2, 3, 12.65, 122.72, 0.824, 16, 50.45, 24.04, 0.176, 2, 3, 39.91, 120.95, 0.664, 16, 65.29, 1.11, 0.336, 3, 3, 48.76, 98.29, 0.62086, 16, 52.39, -19.51, 0.288, 15, -11.3, -18.94, 0.09114, 2, 3, 36.01, 87.67, 0.76, 15, -27.39, -14.9, 0.24, 2, 3, 48.05, 74.93, 0.472, 15, -30.6, -32.13, 0.528, 2, 3, 36.37, 61.12, 0.712, 15, -48.64, -30.82, 0.288, 2, 3, -41.87, 63.95, 0.872, 15, -92.44, 34.06, 0.128, 2, 3, -64.88, 84.48, 0.712, 15, -89.41, 64.75, 0.288, 2, 3, -59.92, 96.87, 0.68, 15, -76.48, 68.04, 0.32, 2, 3, -47.18, 97.94, 0.648, 15, -68.11, 58.37, 0.352, 1, 3, -57.09, 120.24, 1, 1, 3, -43.64, 130.15, 1, 1, 3, -30.18, 126.61, 1], "hull": 34, "edges": [26, 24, 24, 22, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 0, 0, 66, 64, 66, 64, 62, 62, 60, 58, 60, 58, 56, 56, 54, 54, 52, 52, 50, 48, 50, 48, 46, 46, 44, 42, 44, 42, 40, 40, 38, 38, 36, 36, 34, 32, 34, 32, 30, 26, 28, 30, 28, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92], "width": 205, "height": 172}}}}, {"name": "CHASERBOSS_BODY_3", "bones": ["Fleshball_2", "Fleshball_9"], "attachments": {"Body_2": {"Body_2": {"name": "Patroller,Chaser/Chaser Boss/Body_2", "type": "mesh", "uvs": [0.64624, 0.21529, 0.8482, 0.21529, 0.9457, 0.32511, 1, 0.36985, 1, 0.49593, 0.92679, 0.51355, 0.99999, 0.58005, 0.99999, 0.64862, 1, 1, 0.37813, 1, 0.06475, 0.92299, 0.09957, 0.76437, 0, 0.69523, 0, 0.52847, 0.06475, 0.5, 0, 0.39425, 0, 0.27224, 0.1135, 0.34951, 0.18314, 0.2885, 0.16921, 0.15835, 0.27367, 0.09328, 0.36768, 0.13802, 0.48259, 0, 0.62883, 0, 0.86561, 0.43899, 0.94052, 0.73736, 0.26047, 0.76847, 0.27614, 0.57121, 0.40324, 0.55494, 0.4485, 0.65256, 0.55122, 0.53664, 0.65394, 0.55698, 0.69747, 0.66476, 0.7967, 0.60375, 0.87679, 0.67289], "triangles": [4, 5, 3, 18, 19, 20, 15, 16, 17, 24, 1, 2, 24, 2, 3, 3, 5, 24, 14, 15, 17, 18, 20, 21, 22, 0, 21, 0, 22, 23, 30, 21, 0, 28, 18, 21, 30, 28, 21, 24, 31, 0, 24, 0, 1, 27, 18, 28, 17, 18, 27, 14, 17, 27, 33, 31, 24, 33, 24, 5, 6, 34, 5, 29, 28, 30, 32, 31, 33, 34, 33, 5, 12, 13, 14, 11, 14, 27, 29, 27, 28, 32, 29, 30, 7, 34, 6, 25, 34, 7, 12, 14, 11, 26, 11, 27, 27, 29, 26, 9, 26, 29, 10, 11, 26, 9, 10, 26, 25, 7, 8, 32, 34, 25, 32, 33, 34, 25, 8, 32, 9, 29, 32, 8, 9, 32, 32, 30, 31, 30, 0, 31], "vertices": [2, 4, 19.6, 85.17, 0.792, 16, 24.47, -3.94, 0.208, 1, 4, 47.67, 85.17, 1, 2, 4, 61.23, 72.1, 0.728, 16, 38.82, -45.14, 0.272, 2, 4, 68.77, 66.78, 0.28, 16, 39.05, -54.37, 0.72, 2, 4, 68.77, 51.77, 0.52, 16, 27.01, -63.33, 0.48, 2, 4, 58.6, 49.68, 0.648, 16, 19.26, -56.41, 0.352, 2, 4, 68.77, 41.76, 0.712, 16, 18.98, -69.3, 0.288, 2, 4, 68.77, 33.6, 0.92, 16, 12.43, -74.17, 0.08, 1, 4, 68.77, -8.21, 1, 1, 4, -17.67, -8.21, 1, 1, 4, -61.23, 0.95, 1, 1, 4, -56.39, 19.83, 1, 2, 4, -70.23, 28.06, 0.92, 16, -74.96, 34.06, 0.08, 2, 4, -70.23, 47.9, 0.824, 16, -59.04, 45.91, 0.176, 2, 4, -61.23, 51.29, 0.568, 16, -50.95, 40.71, 0.432, 2, 4, -70.23, 63.87, 0.552, 16, -46.22, 55.44, 0.448, 2, 4, -70.23, 78.39, 0.472, 16, -34.57, 64.1, 0.528, 2, 4, -54.45, 69.2, 0.696, 16, -32.54, 45.95, 0.304, 2, 4, -44.77, 76.46, 0.744, 16, -20.93, 42.52, 0.256, 2, 4, -46.71, 91.94, 0.872, 16, -9.66, 53.31, 0.128, 2, 4, -32.19, 99.69, 0.776, 16, 5.22, 46.28, 0.224, 2, 4, -19.12, 94.36, 0.712, 16, 8.74, 32.62, 0.288, 2, 4, -3.15, 110.79, 0.696, 16, 31.45, 29.6, 0.304, 2, 4, 17.18, 110.79, 0.792, 16, 43.58, 13.29, 0.208, 2, 4, 50.09, 58.55, 0.664, 16, 21.3, -44.29, 0.336, 2, 4, 60.51, 23.04, 0.776, 16, -0.98, -73.83, 0.224, 1, 4, -34.02, 19.34, 1, 2, 4, -31.84, 42.81, 0.664, 16, -40.22, 12.07, 0.336, 1, 4, -14.18, 44.75, 1, 1, 4, -7.88, 33.13, 1, 2, 4, 6.39, 46.93, 0.344, 16, -14.1, -16.16, 0.656, 2, 4, 20.67, 44.51, 0.424, 16, -7.52, -29.06, 0.576, 1, 4, 26.72, 31.68, 1, 1, 4, 40.52, 38.94, 1, 2, 4, 51.65, 30.71, 0.712, 16, -0.11, -62.15, 0.288], "hull": 24, "edges": [16, 18, 22, 20, 20, 18, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 0, 0, 2, 2, 4, 4, 48, 10, 8, 8, 6, 4, 6, 14, 16, 50, 14, 12, 14, 10, 12, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68], "width": 139, "height": 119}}}}, {"name": "CHASERBOSS_BODY_4", "attachments": {"Body_2": {"Body_2": {"name": "Patroller,Chaser/Chaser Boss/Body_3", "type": "mesh", "uvs": [1, 0.31018, 1, 0.49103, 1, 0.6363, 0.90591, 0.7371, 1, 0.80529, 1, 1, 0.227, 1, 0.0669, 0.90905, 0.08469, 0.75193, 0, 0.76637, 0, 0.52957, 0.05801, 0.45545, 0, 0.32501, 0.15881, 0.20938, 0.2181, 0.25682, 0.3693, 0.08783, 0.71914, 0.0908, 0.74878, 0.17084, 0.76657, 0, 1, 0, 0.94445, 0.41395, 0.33669, 0.60073, 0.39005, 0.67188, 0.55608, 0.52661, 0.66281, 0.71042], "triangles": [17, 18, 19, 0, 17, 19, 20, 17, 0, 14, 11, 12, 14, 12, 13, 20, 0, 1, 17, 23, 15, 17, 15, 16, 23, 17, 20, 14, 15, 23, 21, 14, 23, 11, 14, 21, 3, 20, 1, 24, 23, 20, 22, 21, 23, 20, 3, 24, 22, 23, 24, 1, 2, 3, 8, 11, 21, 10, 11, 8, 9, 10, 8, 22, 6, 8, 22, 8, 21, 7, 8, 6, 3, 4, 5, 24, 3, 5, 6, 22, 24, 5, 6, 24], "vertices": [2, 4, 40.99, 52.45, 0.744, 15, -52.91, -39.66, 0.256, 1, 4, 40.99, 37.08, 1, 1, 4, 40.99, 24.73, 1, 1, 4, 32.99, 16.17, 1, 1, 4, 40.99, 10.37, 1, 1, 4, 40.99, -6.18, 1, 1, 4, -24.72, -6.18, 1, 1, 4, -38.32, 1.55, 1, 1, 4, -36.81, 14.91, 1, 1, 4, -44.01, 13.68, 1, 2, 4, -44.01, 33.81, 0.888, 15, -118.06, 18.03, 0.112, 2, 4, -39.08, 40.11, 0.856, 15, -110.07, 17.76, 0.144, 2, 4, -44.01, 51.19, 0.728, 15, -104.01, 28.27, 0.272, 2, 4, -30.51, 61.02, 0.84, 15, -88.12, 23.16, 0.16, 2, 4, -25.47, 56.99, 0.872, 15, -88.41, 16.71, 0.128, 2, 4, -12.62, 71.35, 0.92, 15, -69.23, 14.79, 0.08, 1, 4, 17.12, 71.1, 1, 2, 4, 19.64, 64.3, 0.744, 15, -55.93, -15.43, 0.256, 2, 4, 21.15, 78.82, 0.6, 15, -43.3, -8.1, 0.4, 2, 4, 40.99, 78.82, 0.376, 15, -31.61, -24.13, 0.624, 2, 4, 36.27, 43.63, 0.872, 15, -62.82, -41.05, 0.128, 2, 4, -15.39, 27.76, 0.92, 15, -106.09, -8.66, 0.08, 2, 4, -10.86, 21.71, 0.856, 15, -108.3, -15.89, 0.144, 2, 4, 3.26, 34.06, 0.856, 15, -90.01, -20.01, 0.144, 1, 4, 12.33, 18.43, 1], "hull": 20, "edges": [18, 16, 16, 14, 10, 12, 14, 12, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 36, 38, 34, 36, 0, 38, 40, 0, 0, 2, 40, 2, 2, 4, 4, 6, 8, 10, 6, 8, 16, 42, 42, 44, 44, 46, 46, 48], "width": 85, "height": 85}}}}, {"name": "CHASER_HEAD", "attachments": {"Anlter_0_Behind": {"Patroller,Chaser/Patroller_images/Anlter_0": {"name": "<PERSON>ler,Chaser/Chaser_images/Head_GrassBit_1", "x": 15.36, "y": 7.96, "rotation": -29.05, "width": 65, "height": 55}}, "Anlter_1_Behind": {"Patroller,Chaser/Patroller_images/Anlter_1": {"name": "<PERSON>ler,Chaser/Chaser_images/Head_StabbyBit_Top_0", "x": 10.88, "y": 33.39, "rotation": -66.37, "width": 114, "height": 98}}, "Anlter_3_Behind": {"Patroller,Chaser/Patroller_images/Anlter_3": {"name": "<PERSON>ler,Chaser/Chaser_images/Head_GrassBit_0", "x": 19.71, "y": 1.74, "rotation": -141.67, "width": 60, "height": 67}}, "Anlter_4_Behind": {"Patroller,Chaser/Patroller_images/Anlter_4": {"name": "<PERSON>ler,Chaser/Chaser_images/Head_StabbyBit_Top_0", "x": 33.68, "y": 27.71, "rotation": -109.82, "width": 114, "height": 98}}, "BtmSpike_Left_Behind": {"Head_StabbyBit__0": {"name": "<PERSON>ler,Chaser/Chaser_images/Head_StabbyBit__0 copy", "x": 19.02, "y": -9.83, "rotation": 171.96, "width": 106, "height": 60}}, "BtmSpike_Right_Front": {"Head_StabbyBit__1": {"name": "<PERSON>ler,Chaser/Chaser_images/Head_StabbyBit__0 copy", "x": -23.13, "y": 8.81, "scaleX": 0.8382, "rotation": -1.4, "width": 106, "height": 60}}, "Eyes": {"Patroller,Chaser/Chaser_images/Eyes": {"name": "Patroller,Chaser/Chaser_images/Eyes_Normal", "x": 2.78, "y": 7.42, "width": 102, "height": 41}, "Patroller,Chaser/Chaser_images/Eyes_Shocked": {"x": 2.78, "y": 7.42, "width": 111, "height": 45}, "Patroller,Chaser/Chaser_images/Eyes_Shut": {"x": 2.78, "y": 7.42, "width": 95, "height": 33}}, "Grass_0": {"Patroller,Chaser/Patroller_images/Grass_0": {"name": "<PERSON>ler,Chaser/Chaser_images/Grass_Top", "x": -9.05, "y": -6.43, "rotation": 90, "width": 114, "height": 68}}, "Grass_0_back": {"Patroller,Chaser/Patroller_images/Grass_0": {"name": "<PERSON>ler,Chaser/Chaser_images/Grass_Top", "x": -9.05, "y": -6.43, "rotation": 90, "width": 114, "height": 68}}, "Head": {"Patroller,Chaser/Patroller_images/Head": {"name": "Patroller,Chaser/Chaser_images/Head", "x": -1.53, "y": -35.18, "rotation": 0.57, "width": 184, "height": 165}}, "HeadInner": {"Patroller,Chaser/Patroller_images/HeadInner": {"name": "<PERSON>ler,Chaser/Chaser_images/HeadInner", "type": "mesh", "uvs": [1, 0.33275, 0.99639, 0.70483, 1, 1, 0.51718, 1, 0, 1, 0, 0.71453, 1e-05, 0.31825, 0, 0, 0.50874, 0, 1, 0, 0.50917, 0.328, 0.51378, 0.71511], "triangles": [3, 1, 2, 4, 11, 3, 10, 8, 9, 6, 7, 8, 3, 11, 1, 4, 5, 11, 10, 9, 0, 6, 8, 10, 5, 10, 11, 11, 10, 1, 5, 6, 10, 1, 10, 0], "vertices": [2, 13, 72.72, 38.5, 0.656, 12, 71.49, 63.22, 0.344, 2, 13, 72.21, -8.52, 0.656, 12, 70.98, 16.33, 0.344, 2, 13, 72.75, -46.66, 0.208, 12, 71.52, -21.7, 0.792, 2, 13, 3.93, -46.66, 0.208, 12, 2.86, -21.7, 0.792, 2, 13, -69.8, -46.66, 0.208, 12, -70.69, -21.7, 0.792, 2, 13, -69.68, -9.74, 0.656, 12, -70.58, 15.1, 0.344, 2, 13, -69.68, 40.34, 0.656, 12, -70.58, 65.05, 0.344, 2, 13, -69.8, 79.86, 0.208, 12, -70.69, 104.49, 0.792, 2, 13, 2.72, 79.86, 0.208, 12, 1.66, 104.49, 0.792, 2, 13, 72.75, 79.86, 0.208, 12, 71.52, 104.49, 0.792, 2, 13, 2.82, 39.1, 0.656, 12, 1.76, 63.82, 0.344, 2, 13, 3.48, -9.82, 0.656, 12, 2.41, 15.03, 0.344], "hull": 10, "edges": [12, 14, 0, 18, 14, 16, 16, 18, 0, 20, 20, 12, 16, 20, 4, 6, 6, 8, 8, 10, 10, 12, 6, 22, 22, 20, 10, 22, 0, 2, 2, 4, 22, 2], "width": 133, "height": 127}}}}, {"name": "CHASER_HEAD_BACK", "attachments": {"Anlter_0_Behind": {"Patroller,Chaser/Patroller_images/Anlter_0": {"name": "<PERSON>ler,Chaser/Chaser_images/Head_GrassBit_1", "x": 15.36, "y": 7.96, "rotation": -29.05, "width": 65, "height": 55}}, "Anlter_1_Front": {"Patroller,Chaser/Patroller_images/Anlter_1": {"name": "<PERSON>ler,Chaser/Chaser_images/Head_StabbyBit_Top_0", "x": 10.88, "y": 33.39, "rotation": -66.37, "width": 114, "height": 98}}, "Anlter_3_Behind": {"Patroller,Chaser/Patroller_images/Anlter_3": {"name": "<PERSON>ler,Chaser/Chaser_images/Head_GrassBit_0", "x": 19.71, "y": 1.74, "rotation": -141.67, "width": 60, "height": 67}}, "Anlter_4_Front": {"Patroller,Chaser/Patroller_images/Anlter_4": {"name": "<PERSON>ler,Chaser/Chaser_images/Head_StabbyBit_Top_0", "x": 33.68, "y": 27.71, "rotation": -109.82, "width": 114, "height": 98}}, "BtmSpike_Left_Front": {"Head_StabbyBit__0": {"name": "<PERSON>ler,Chaser/Chaser_images/Head_StabbyBit__0 copy", "x": 19.02, "y": -9.83, "rotation": 171.96, "width": 106, "height": 60}}, "BtmSpike_Right_Behind": {"Head_StabbyBit__1": {"name": "<PERSON>ler,Chaser/Chaser_images/Head_StabbyBit__0 copy", "x": -23.13, "y": 8.81, "scaleX": 0.8382, "rotation": -1.4, "width": 106, "height": 60}}, "Grass_0_back": {"Patroller,Chaser/Patroller_images/Grass_0": {"name": "<PERSON>ler,Chaser/Chaser_images/Grass_Top", "x": -9.05, "y": -6.43, "rotation": 90, "width": 114, "height": 68}}, "Head": {"Patroller,Chaser/Patroller_images/Head": {"name": "Patroller,Chaser/Chaser_images/Head", "x": -1.53, "y": -35.18, "rotation": 0.57, "width": 184, "height": 165}}}}, {"name": "POUNCERBOSS_BODY_0", "bones": ["EYE_RIGHT_TOP", "SpawnBall", "EYE_LEFT_TOP", "EYE_LEFT_BTM", "EYE_RIGHT_BTM", "MOUTH"], "attachments": {"Anlter_0_Behind": {"Patroller,Chaser/Patroller_images/Anlter_0": {"name": "<PERSON>ler,<PERSON>r/<PERSON>uncer <PERSON>/Antlers2", "type": "mesh", "uvs": [1, 1, 0.92251, 1, 0.08862, 1, 0, 1, 0, 0, 0.09278, 0, 0.90167, 0, 1, 0, 0.84538, 0.9016, 0.19285, 0.8458], "triangles": [7, 8, 6, 0, 8, 7, 9, 3, 4, 9, 5, 6, 0, 1, 8, 2, 3, 9, 1, 2, 8, 2, 9, 8, 8, 9, 6, 9, 4, 5], "vertices": [2, 8, 92.12, 45.45, 0.056, 1, 116.96, 185.3, 0.944, 2, 8, 76.13, 54.33, 0.056, 1, 98.68, 185.3, 0.944, 2, 1, -98.12, 185.3, 0.944, 9, 91.86, -35.3, 0.056, 2, 1, -119.04, 185.3, 0.944, 9, 108.27, -22.33, 0.056, 2, 1, -119.04, 282.3, 0.704, 9, 168.43, -98.42, 0.296, 2, 1, -97.14, 282.3, 0.704, 9, 151.25, -112, 0.296, 2, 8, 118.94, 141.52, 0.296, 1, 93.76, 282.3, 0.704, 2, 8, 139.23, 130.25, 0.28, 1, 116.96, 282.3, 0.72, 2, 8, 64.86, 71.52, 0.056, 1, 80.47, 194.84, 0.944, 2, 1, -73.52, 200.25, 0.944, 9, 81.84, -62.29, 0.056], "hull": 8, "edges": [6, 8, 0, 14, 12, 14, 12, 16, 0, 2, 16, 2, 8, 10, 10, 12, 10, 18, 2, 4, 4, 6, 18, 4], "width": 236, "height": 97}}, "Anlter_1_Front": {"Patroller,Chaser/Patroller_images/Anlter_1": {"name": "<PERSON><PERSON>,<PERSON><PERSON>/<PERSON>uncer <PERSON>/Antlers1", "type": "mesh", "uvs": [1, 0.95093, 0.69086, 0.91979, 0.7361, 0.82948, 0.72964, 0.57129, 0.52061, 0.58245, 0.31479, 0.5866, 0.34872, 0.84194, 0.377, 0.91356, 0.04334, 0.9447, 0, 0, 0.32327, 0, 0.31883, 0.22854, 0.52401, 0.2491, 0.72485, 0.24478, 0.71631, 0, 1, 0], "triangles": [13, 14, 15, 3, 13, 15, 0, 2, 3, 0, 3, 15, 1, 2, 0, 11, 9, 10, 5, 9, 11, 8, 9, 5, 8, 5, 6, 8, 6, 7, 12, 5, 11, 4, 5, 12, 12, 13, 3, 4, 12, 3], "vertices": [2, 7, 76.02, -12.42, 0.424, 1, 97.41, 201.1, 0.576, 2, 7, 57.38, 38.28, 0.424, 1, 46.62, 219.24, 0.576, 2, 7, 73.65, 37.29, 0.424, 1, 57.78, 231.1, 0.576, 2, 7, 110.01, 55.81, 0.424, 1, 66.82, 270.88, 0.576, 3, 7, 92.82, 87.91, 0.2137, 1, 2, 278.53, 0.576, 6, 102.45, -79.75, 0.2103, 2, 1, -62.44, 270.45, 0.576, 6, 112.92, -45.49, 0.424, 2, 1, -47.96, 232.35, 0.576, 6, 72.73, -38.61, 0.424, 2, 1, -40.71, 222.38, 0.576, 6, 60.44, -39.79, 0.424, 2, 1, -96.22, 204.99, 0.576, 6, 73.74, 16.94, 0.424, 2, 1, -135.88, 349.02, 0.576, 6, 218, -22.1, 0.424, 2, 1, -81.07, 361.22, 0.576, 6, 200.59, -75.59, 0.424, 2, 1, -74.01, 325.81, 0.576, 6, 166.49, -63.67, 0.424, 3, 7, 140.64, 109.97, 0.2137, 1, 3.51, 329.72, 0.576, 6, 152.35, -96.61, 0.2103, 2, 7, 156.25, 78.7, 0.424, 1, 78.82, 321.04, 0.576, 2, 7, 190.55, 96.63, 0.424, 1, 86.99, 358.86, 0.576, 2, 7, 211.73, 52.05, 0.424, 1, 134.71, 346.58, 0.576], "hull": 16, "edges": [10, 12, 12, 14, 14, 16, 18, 20, 28, 30, 0, 30, 2, 0, 20, 22, 22, 10, 28, 26, 4, 6, 6, 26, 22, 24, 24, 26, 10, 8, 8, 6, 24, 8, 16, 18, 2, 4], "width": 174, "height": 158}}, "EyeGoop1": {"EyeGoop1": {"name": "Patroller,Chaser/Chaser Boss/EyeGoop1", "x": -11.44, "y": -67.29, "width": 22, "height": 67}}, "EyeGoop2": {"EyeGoop2": {"name": "Patroller,Chaser/Chaser Boss/EyeGoop2", "x": 4.5, "y": -41.5, "width": 16, "height": 57}}, "EyeGoop3": {"EyeGoop3": {"name": "Patroller,Chaser/Chaser Boss/EyeGoop2", "x": 15.35, "y": -30.18, "scaleY": 0.6433, "width": 16, "height": 57}}, "EyeGoop4": {"EyeGoop4": {"name": "Patroller,Chaser/Chaser Boss/EyeGoop1", "x": 2.2, "y": -53.03, "width": 22, "height": 67}}, "EyeGoop5": {"EyeGoop5": {"name": "Patroller,Chaser/Chaser Boss/EyeGoop2", "x": -21.54, "y": -50.18, "width": 16, "height": 57}}, "EyeGoop6": {"EyeGoop6": {"name": "Patroller,Chaser/Chaser Boss/EyeGoop2", "x": 15.35, "y": -34.43, "scaleY": 1.0439, "width": 16, "height": 57}}, "EYE_LEFT_BTM": {"Patroller,Chaser/Pouncer Boss/Eye_Transform1": {"name": "<PERSON>ler,Chaser/Chaser Boss/Eye_Transform1", "x": 0.5, "y": 0.5, "width": 91, "height": 97}, "Patroller,Chaser/Pouncer Boss/Eye_Transform2": {"name": "<PERSON>ler,Chaser/Chaser Boss/Eye_Transform2", "x": 0.5, "y": 0.5, "width": 91, "height": 97}, "Patroller,Chaser/Pouncer Boss/Eye_Normal": {"x": 0.5, "y": 0.5, "width": 77, "height": 85}, "Patroller,Chaser/Pouncer Boss/Eye_Shocked": {"width": 94, "height": 100}, "Patroller,Chaser/Pouncer Boss/Eye_Shut": {"x": 0.5, "width": 55, "height": 62}}, "EYE_LEFT_TOP": {"Patroller,Chaser/Pouncer Boss/Eye_Transform1": {"name": "<PERSON>ler,Chaser/Chaser Boss/Eye_Transform1", "x": 0.5, "y": 0.5, "width": 91, "height": 97}, "Patroller,Chaser/Pouncer Boss/Eye_Transform2": {"name": "<PERSON>ler,Chaser/Chaser Boss/Eye_Transform2", "x": 0.5, "y": 0.5, "width": 91, "height": 97}, "Patroller,Chaser/Pouncer Boss/Eye_Normal": {"x": 0.5, "y": 0.5, "width": 77, "height": 85}, "Patroller,Chaser/Pouncer Boss/Eye_Shocked": {"width": 94, "height": 100}, "Patroller,Chaser/Pouncer Boss/Eye_Shut": {"x": 0.5, "width": 55, "height": 62}}, "EYE_RIGHT_BTM": {"Patroller,Chaser/Pouncer Boss/Eye_Transform1": {"name": "<PERSON>ler,Chaser/Chaser Boss/Eye_Transform1", "x": -0.5, "y": 0.5, "scaleX": -1, "width": 91, "height": 97}, "Patroller,Chaser/Pouncer Boss/Eye_Transform2": {"name": "<PERSON>ler,Chaser/Chaser Boss/Eye_Transform2", "x": -0.5, "y": 0.5, "scaleX": -1, "width": 91, "height": 97}, "Patroller,Chaser/Pouncer Boss/Eye_Normal": {"x": -0.5, "y": 0.5, "scaleX": -1, "width": 77, "height": 85}, "Patroller,Chaser/Pouncer Boss/Eye_Shocked": {"scaleX": -1, "width": 94, "height": 100}, "Patroller,Chaser/Pouncer Boss/Eye_Shut": {"x": -0.5, "scaleX": -1, "width": 55, "height": 62}}, "EYE_RIGHT_TOP": {"Patroller,Chaser/Pouncer Boss/Eye_Transform1": {"name": "<PERSON>ler,Chaser/Chaser Boss/Eye_Transform1", "x": -0.5, "y": 0.5, "scaleX": -1, "width": 91, "height": 97}, "Patroller,Chaser/Pouncer Boss/Eye_Transform2": {"name": "<PERSON>ler,Chaser/Chaser Boss/Eye_Transform2", "x": -0.5, "y": 0.5, "scaleX": -1, "width": 91, "height": 97}, "Patroller,Chaser/Pouncer Boss/Eye_Normal": {"x": -0.5, "y": 0.5, "scaleX": -1, "width": 77, "height": 85}, "Patroller,Chaser/Pouncer Boss/Eye_Shocked": {"scaleX": -1, "width": 94, "height": 100}, "Patroller,Chaser/Pouncer Boss/Eye_Shut": {"x": -0.5, "scaleX": -1, "width": 55, "height": 62}}, "Head": {"Patroller,Chaser/Patroller_images/Head": {"name": "Patroller,<PERSON>r/Pouncer Boss/Head", "y": 13.88, "width": 294, "height": 301}}, "Patroller,Chaser/Pouncer Boss/Mouth": {"Mouth": {"name": "Patroller,<PERSON>r/Pouncer Boss/Mouth", "x": 3.72, "y": -1.86, "width": 193, "height": 188}, "Mouth_shut": {"name": "<PERSON>ler,<PERSON>r/Pouncer Boss/Mouth_Shut", "x": 0.13, "y": 20.86, "width": 189, "height": 85}}, "Patroller,Chaser/Pouncer Boss/Mouth2": {"Mouth": {"name": "Patroller,<PERSON>r/Pouncer Boss/Mouth", "x": 3.72, "y": -1.86, "width": 193, "height": 188}, "Mouth_shut": {"name": "<PERSON>ler,<PERSON>r/Pouncer Boss/Mouth_Shut", "x": 0.13, "y": 20.86, "width": 189, "height": 85}}}}, {"name": "POUNCERBOSS_BODY_0_BACK", "attachments": {"Anlter_0_Front": {"Patroller,Chaser/Patroller_images/Anlter_0": {"name": "<PERSON>ler,<PERSON>r/<PERSON>uncer <PERSON>/Antlers2", "type": "mesh", "uvs": [1, 1, 0.92251, 1, 0.08862, 1, 0, 1, 0, 0, 0.09278, 0, 0.90167, 0, 1, 0, 0.84538, 0.9016, 0.19285, 0.8458], "triangles": [9, 4, 5, 8, 9, 6, 2, 9, 8, 1, 2, 8, 2, 3, 9, 0, 1, 8, 9, 5, 6, 9, 3, 4, 0, 8, 7, 7, 8, 6], "vertices": [2, 8, 92.12, 45.45, 0.056, 1, 116.96, 185.3, 0.944, 2, 8, 76.13, 54.33, 0.056, 1, 98.68, 185.3, 0.944, 2, 1, -98.12, 185.3, 0.944, 9, 91.86, -35.3, 0.056, 2, 1, -119.04, 185.3, 0.944, 9, 108.27, -22.33, 0.056, 2, 1, -119.04, 282.3, 0.704, 9, 168.43, -98.42, 0.296, 2, 1, -97.14, 282.3, 0.704, 9, 151.25, -112, 0.296, 2, 8, 118.94, 141.52, 0.296, 1, 93.76, 282.3, 0.704, 2, 8, 139.23, 130.25, 0.28, 1, 116.96, 282.3, 0.72, 2, 8, 64.86, 71.52, 0.056, 1, 80.47, 194.84, 0.944, 2, 1, -73.52, 200.25, 0.944, 9, 81.84, -62.29, 0.056], "hull": 8, "edges": [6, 8, 0, 14, 12, 14, 12, 16, 0, 2, 16, 2, 8, 10, 10, 12, 10, 18, 2, 4, 4, 6, 18, 4], "width": 236, "height": 97}}, "Anlter_1_Behind": {"Patroller,Chaser/Patroller_images/Anlter_1": {"name": "<PERSON><PERSON>,<PERSON><PERSON>/<PERSON>uncer <PERSON>/Antlers1", "type": "mesh", "uvs": [1, 0.95093, 0.69086, 0.91979, 0.7361, 0.82948, 0.72964, 0.57129, 0.52061, 0.58245, 0.31479, 0.5866, 0.34872, 0.84194, 0.377, 0.91356, 0.04334, 0.9447, 0, 0, 0.32327, 0, 0.31883, 0.22854, 0.52401, 0.2491, 0.72485, 0.24478, 0.71631, 0, 1, 0], "triangles": [4, 12, 3, 12, 13, 3, 4, 5, 12, 12, 5, 11, 8, 6, 7, 8, 5, 6, 8, 9, 5, 5, 9, 11, 11, 9, 10, 1, 2, 0, 0, 3, 15, 0, 2, 3, 3, 13, 15, 13, 14, 15], "vertices": [2, 7, 76.02, -12.42, 0.424, 1, 97.41, 201.1, 0.576, 2, 7, 57.38, 38.28, 0.424, 1, 46.62, 219.24, 0.576, 2, 7, 73.65, 37.29, 0.424, 1, 57.78, 231.1, 0.576, 2, 7, 110.01, 55.81, 0.424, 1, 66.82, 270.88, 0.576, 3, 7, 92.82, 87.91, 0.2137, 1, 2, 278.53, 0.576, 6, 102.45, -79.75, 0.2103, 2, 1, -62.44, 270.45, 0.576, 6, 112.92, -45.49, 0.424, 2, 1, -47.96, 232.35, 0.576, 6, 72.73, -38.61, 0.424, 2, 1, -40.71, 222.38, 0.576, 6, 60.44, -39.79, 0.424, 2, 1, -96.22, 204.99, 0.576, 6, 73.74, 16.94, 0.424, 2, 1, -135.88, 349.02, 0.576, 6, 218, -22.1, 0.424, 2, 1, -81.07, 361.22, 0.576, 6, 200.59, -75.59, 0.424, 2, 1, -74.01, 325.81, 0.576, 6, 166.49, -63.67, 0.424, 3, 7, 140.64, 109.97, 0.2137, 1, 3.51, 329.72, 0.576, 6, 152.35, -96.61, 0.2103, 2, 7, 156.25, 78.7, 0.424, 1, 78.82, 321.04, 0.576, 2, 7, 190.55, 96.63, 0.424, 1, 86.99, 358.86, 0.576, 2, 7, 211.73, 52.05, 0.424, 1, 134.71, 346.58, 0.576], "hull": 16, "edges": [10, 12, 12, 14, 14, 16, 18, 20, 28, 30, 0, 30, 2, 0, 20, 22, 22, 10, 28, 26, 4, 6, 6, 26, 22, 24, 24, 26, 10, 8, 8, 6, 24, 8, 16, 18, 2, 4], "width": 174, "height": 158}}, "Head": {"Patroller,Chaser/Patroller_images/Head": {"name": "<PERSON>ler,<PERSON>r/Pouncer <PERSON>/Head_Back", "y": 13.88, "width": 294, "height": 301}}}}, {"name": "POUNCERBOSS_BODY_1", "attachments": {"Body_0": {"Body_0": {"name": "<PERSON><PERSON>,<PERSON>r/Pouncer Boss/Body_0", "type": "mesh", "uvs": [0.956, 0.05788, 0.82129, 0.15413, 0.8759, 0.17611, 0.92778, 0.12065, 1, 0.12484, 1, 0.27759, 0.91413, 0.30375, 0.95327, 0.34665, 0.89502, 0.41884, 1, 0.5287, 1, 0.62238, 0.96541, 0.64839, 0.97386, 0.79607, 0.92653, 0.92042, 0.79806, 0.94763, 0.62225, 1, 0.29601, 1, 0.15401, 0.93791, 0.06104, 0.84076, 0.05935, 0.61148, 0, 0.61731, 0, 0.53765, 0.11978, 0.3964, 0.05837, 0.36675, 0.11361, 0.30322, 0, 0.34514, 0, 0.1568, 0.03569, 0.11794, 0.13739, 0.21185, 0.19867, 0.13113, 0.14725, 0.06159, 0.2078, 0.02126, 0.29245, 0.07985, 0.30551, 0.02476, 0.39703, 0.03647, 0.45902, 0.08391, 0.52001, 0, 0.5728, 0, 0.59009, 0.0652, 0.72207, 0.02126, 0.7239, 0.09241, 0.7512, 0, 0.89046, 0, 0.29132, 0.30414, 0.17898, 0.39946, 0.20573, 0.44558, 0.10007, 0.46249, 0.09071, 0.54704, 0.20974, 0.54397, 0.25789, 0.20729, 0.36756, 0.19191, 0.63371, 0.23035, 0.76345, 0.21959, 0.73536, 0.31644, 0.83701, 0.39638, 0.74606, 0.60239, 0.80491, 0.59163, 0.78351, 0.52245, 0.10542, 0.59931, 0.09606, 0.74075, 0.19235, 0.70078], "triangles": [8, 54, 6, 8, 6, 7, 57, 53, 54, 8, 56, 57, 8, 57, 54, 56, 8, 9, 55, 53, 57, 55, 57, 56, 11, 56, 9, 10, 11, 9, 56, 12, 14, 12, 56, 11, 14, 55, 56, 13, 14, 12, 55, 60, 53, 55, 15, 60, 15, 55, 14, 29, 30, 31, 29, 31, 32, 34, 32, 33, 50, 34, 35, 50, 32, 34, 49, 29, 32, 49, 32, 50, 28, 29, 49, 28, 26, 27, 24, 26, 28, 43, 49, 50, 25, 26, 24, 44, 22, 24, 23, 24, 22, 24, 49, 43, 49, 24, 28, 43, 44, 24, 45, 44, 43, 46, 22, 44, 46, 44, 45, 46, 21, 22, 48, 45, 43, 46, 45, 48, 47, 21, 46, 47, 46, 48, 58, 47, 48, 19, 21, 47, 19, 47, 58, 20, 21, 19, 60, 58, 48, 59, 19, 58, 59, 58, 60, 59, 18, 19, 17, 59, 60, 18, 59, 17, 43, 51, 48, 48, 53, 60, 17, 60, 16, 51, 43, 50, 38, 39, 40, 0, 1, 42, 1, 41, 42, 40, 41, 1, 52, 40, 1, 51, 38, 40, 51, 40, 52, 3, 4, 5, 2, 3, 5, 6, 2, 5, 53, 51, 52, 52, 2, 6, 2, 52, 1, 54, 52, 6, 53, 52, 54, 36, 37, 38, 35, 36, 38, 35, 38, 51, 50, 35, 51, 51, 53, 48, 16, 60, 15], "vertices": [2, 2, 139.39, 250.48, 0.744, 15, 165.06, -2.5, 0.256, 2, 2, 95.88, 223.43, 0.824, 15, 117.57, 16.72, 0.176, 2, 2, 113.52, 217.25, 0.824, 8, 104.62, 75.06, 0.176, 2, 2, 130.28, 232.84, 0.712, 8, 126.84, 80.55, 0.288, 2, 2, 153.6, 231.66, 0.504, 8, 146.66, 68.19, 0.496, 2, 2, 153.6, 188.74, 0.504, 8, 125.82, 30.67, 0.496, 2, 2, 125.87, 181.39, 0.776, 8, 98, 37.71, 0.224, 2, 2, 138.51, 169.33, 0.824, 8, 103.2, 21.04, 0.176, 2, 2, 119.69, 149.05, 0.84, 15, 71.5, -46.35, 0.16, 2, 2, 153.6, 118.18, 0.696, 15, 66.54, -91.93, 0.304, 2, 2, 153.6, 91.85, 0.648, 15, 45.27, -107.44, 0.352, 1, 2, 142.43, 84.54, 1, 1, 2, 145.16, 43.04, 1, 1, 2, 129.87, 8.1, 1, 1, 2, 88.37, 0.46, 1, 1, 2, 31.59, -14.26, 1, 1, 2, -73.79, -14.26, 1, 1, 2, -119.65, 3.19, 1, 1, 2, -149.68, 30.49, 1, 2, 2, -150.23, 94.91, 0.792, 15, -131.27, 139.86, 0.208, 2, 2, -169.4, 93.28, 0.84, 15, -143.88, 154.38, 0.16, 2, 2, -169.4, 115.66, 0.84, 15, -125.8, 167.57, 0.16, 3, 2, -130.71, 155.35, 0.77434, 15, -70.93, 159.69, 0.128, 8, -138.93, 139.56, 0.09766, 2, 2, -150.55, 163.68, 0.696, 8, -152.22, 156.48, 0.304, 2, 2, -132.7, 181.53, 0.536, 8, -127.96, 163.41, 0.464, 2, 2, -169.4, 169.76, 0.664, 8, -165.75, 170.94, 0.336, 2, 2, -169.4, 222.68, 0.664, 8, -140.05, 217.2, 0.336, 2, 2, -157.87, 233.6, 0.632, 8, -124.67, 221.15, 0.368, 2, 2, -125.02, 207.21, 0.792, 8, -108.77, 182.13, 0.208, 3, 2, -105.23, 229.89, 0.80275, 8, -80.46, 192.34, 0.10125, 9, 125.1, -65.88, 0.096, 2, 2, -121.84, 249.43, 0.6, 9, 150.24, -70.91, 0.4, 2, 2, -102.28, 260.77, 0.536, 9, 141.93, -91.93, 0.464, 2, 2, -74.94, 244.3, 0.824, 9, 110.27, -95.97, 0.176, 2, 2, -70.72, 259.78, 0.664, 9, 116.57, -110.73, 0.336, 2, 2, -41.16, 256.49, 0.744, 9, 91.34, -126.48, 0.256, 1, 2, -21.13, 243.16, 1, 2, 2, -1.44, 266.74, 0.728, 15, 95.23, 120.87, 0.272, 2, 2, 15.62, 266.74, 0.76, 15, 105.28, 107.09, 0.24, 2, 2, 21.2, 248.42, 0.808, 15, 93.77, 91.78, 0.192, 2, 2, 63.83, 260.77, 0.824, 15, 128.86, 64.61, 0.176, 2, 2, 64.42, 240.77, 0.872, 15, 113.05, 52.36, 0.128, 2, 2, 73.24, 266.74, 0.936, 15, 139.23, 60.53, 0.064, 2, 2, 118.22, 266.74, 0.824, 15, 165.73, 24.18, 0.176, 1, 2, -75.3, 181.28, 1, 2, 2, -111.59, 154.49, 0.792, 15, -60.36, 143.74, 0.208, 2, 2, -102.95, 141.53, 0.904, 15, -65.74, 129.12, 0.096, 2, 2, -137.08, 136.78, 0.856, 15, -89.69, 153.9, 0.144, 2, 2, -140.1, 113.02, 0.792, 15, -110.67, 142.34, 0.208, 2, 2, -101.65, 113.88, 0.888, 15, -87.32, 111.78, 0.112, 1, 2, -86.1, 208.49, 1, 1, 2, -50.68, 212.81, 1, 1, 2, 35.29, 202.01, 1, 3, 2, 77.2, 205.04, 0.65261, 15, 91.7, 20.98, 0.208, 8, 66.94, 82.02, 0.13939, 2, 2, 68.12, 177.82, 0.856, 8, 45.79, 62.64, 0.144, 2, 2, 100.96, 155.36, 0.856, 8, 63.58, 27.06, 0.144, 2, 2, 71.58, 97.47, 0.808, 15, 1.48, -37.86, 0.192, 2, 2, 90.59, 100.49, 0.712, 15, 15.12, -51.44, 0.288, 2, 2, 83.68, 119.93, 0.824, 15, 26.76, -34.4, 0.176, 2, 2, -135.35, 98.33, 0.92, 15, -119.74, 129.85, 0.08, 1, 2, -138.37, 58.59, 1, 1, 2, -107.27, 69.82, 1], "hull": 43, "edges": [40, 38, 38, 36, 36, 34, 34, 32, 30, 32, 30, 28, 28, 26, 26, 24, 24, 22, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 2, 0, 82, 84, 0, 84, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 50, 52, 50, 48, 48, 46, 46, 44, 40, 42, 44, 42, 18, 20, 22, 20, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 86, 98, 98, 100, 102, 104, 104, 106, 106, 108, 110, 112, 112, 114, 38, 116, 116, 118, 118, 120, 66, 68, 68, 70], "width": 323, "height": 281}}}}, {"name": "POUNCERBOSS_BODY_2", "attachments": {"Body_1": {"Body_1": {"name": "<PERSON><PERSON>,<PERSON>r/Pouncer Boss/Body_1", "type": "mesh", "uvs": [1, 0.16602, 0.84221, 0.24642, 0.89307, 0.27438, 0.87854, 0.31283, 1, 0.35827, 1, 0.44741, 0.95265, 0.44391, 1, 0.50508, 1, 0.57674, 0.91778, 0.58373, 1, 0.63092, 0.97612, 0.72069, 0.94103, 0.7111, 0.92876, 0.92507, 0.7488, 0.95458, 0.71199, 1, 0.33796, 1, 0.15631, 0.95076, 0.09818, 0.70608, 0.03715, 0.6851, 0.07493, 0.58898, 0, 0.63267, 0, 0.51557, 0.07202, 0.46838, 0.02698, 0.43167, 0.13887, 0.31808, 0.09673, 0.28137, 0.18392, 0.20797, 0.05313, 0.12582, 0.14032, 0.04368, 0.26239, 0.11883, 0.2682, 0, 0.43096, 0.07339, 0.5167, 0, 0.56175, 0, 0.57773, 0.07689, 0.77536, 0.06815, 0.76374, 0.15379, 0.85384, 0, 1, 0, 0.38446, 0.27263, 0.2435, 0.23593, 0.25658, 0.31808, 0.13015, 0.41245, 0.15631, 0.45265, 0.23187, 0.47887, 0.17229, 0.57674, 0.19119, 0.65714, 0.49926, 0.20447, 0.64167, 0.14505, 0.63295, 0.2971, 0.89017, 0.40721, 0.8146, 0.63442, 0.70416, 0.28662, 0.79135, 0.30409, 0.77972, 0.48935, 0.85529, 0.5715, 0.31906, 0.6484], "triangles": [38, 39, 0, 30, 31, 32, 49, 35, 36, 37, 49, 36, 1, 37, 38, 35, 48, 32, 33, 35, 32, 35, 33, 34, 48, 35, 49, 27, 29, 30, 28, 29, 27, 41, 27, 30, 0, 1, 38, 40, 30, 32, 40, 32, 48, 41, 30, 40, 53, 49, 37, 54, 53, 37, 50, 48, 49, 50, 49, 53, 1, 54, 37, 3, 1, 2, 54, 1, 3, 25, 26, 27, 42, 41, 40, 51, 3, 4, 54, 3, 51, 25, 27, 41, 25, 41, 42, 43, 25, 42, 43, 24, 25, 6, 51, 4, 6, 4, 5, 44, 43, 42, 23, 24, 43, 23, 43, 44, 45, 44, 42, 55, 53, 54, 55, 54, 51, 56, 55, 51, 56, 51, 6, 9, 56, 6, 46, 44, 45, 23, 44, 46, 7, 9, 6, 8, 9, 7, 20, 23, 46, 22, 23, 20, 21, 22, 20, 52, 55, 56, 40, 45, 42, 57, 45, 40, 46, 45, 57, 40, 48, 50, 47, 46, 57, 47, 18, 20, 47, 20, 46, 19, 20, 18, 12, 9, 10, 52, 56, 9, 12, 52, 9, 11, 12, 10, 12, 14, 52, 17, 18, 47, 57, 40, 50, 55, 50, 53, 55, 57, 50, 13, 14, 12, 57, 55, 52, 14, 57, 52, 15, 16, 57, 17, 47, 57, 16, 17, 57, 14, 15, 57], "vertices": [2, 3, 130.89, 176.84, 0.408, 16, 164.43, -38.54, 0.592, 2, 3, 88.76, 158.99, 0.824, 16, 124.97, -15.39, 0.176, 2, 3, 102.34, 152.78, 0.712, 16, 128.09, -29.99, 0.288, 3, 3, 98.46, 144.25, 0.73875, 16, 118.93, -31.97, 0.21325, 15, 55.44, -31.24, 0.048, 2, 3, 130.89, 134.16, 0.76, 15, 61.62, -62.79, 0.24, 2, 3, 130.89, 114.37, 0.76, 15, 43.4, -73, 0.24, 2, 3, 118.25, 115.15, 0.888, 15, 38.21, -62.07, 0.112, 1, 3, 130.89, 101.57, 1, 1, 3, 130.89, 85.66, 1, 2, 3, 108.93, 84.11, 0.888, 16, 76.92, -76.26, 0.112, 2, 3, 130.89, 73.63, 0.728, 16, 81.61, -100.13, 0.272, 2, 3, 124.51, 53.7, 0.744, 16, 61.82, -106.9, 0.256, 2, 3, 115.14, 55.83, 0.824, 16, 57.94, -98.11, 0.176, 1, 3, 111.87, 8.33, 1, 1, 3, 63.82, 1.78, 1, 1, 3, 53.99, -8.3, 1, 1, 3, -45.88, -8.3, 1, 1, 3, -94.38, 2.63, 1, 2, 3, -109.9, 56.95, 0.888, 16, -75.45, 83.14, 0.112, 2, 3, -126.19, 61.6, 0.744, 16, -81.44, 98.99, 0.256, 2, 3, -116.11, 82.94, 0.616, 16, -58.3, 103.63, 0.384, 2, 3, -136.11, 73.24, 0.424, 16, -78.02, 113.9, 0.576, 2, 3, -136.11, 99.24, 0.488, 16, -57.16, 129.41, 0.512, 3, 3, -116.88, 109.72, 0.6384, 16, -37.28, 120.23, 0.2016, 15, -75.76, 131.2, 0.16, 3, 3, -128.91, 117.87, 0.65811, 16, -37.91, 134.74, 0.24589, 15, -73.65, 145.45, 0.096, 3, 3, -99.03, 143.08, 0.69216, 16, 0.15, 125.82, 0.13184, 15, -36.97, 133.34, 0.176, 2, 3, -110.29, 151.23, 0.856, 15, -35.27, 146.98, 0.144, 3, 3, -87.01, 167.53, 0.67795, 16, 26.94, 130.75, 0.17805, 15, -9.39, 135.99, 0.144, 2, 3, -121.93, 185.76, 0.696, 16, 20.74, 169.66, 0.304, 2, 3, -98.65, 204, 0.664, 16, 49.27, 161.86, 0.336, 2, 3, -66.05, 187.32, 0.824, 16, 55.32, 125.75, 0.176, 2, 3, -64.5, 213.7, 0.68, 16, 77.42, 140.24, 0.32, 2, 3, -21.05, 197.4, 0.808, 15, 48.93, 96.45, 0.192, 2, 3, 1.85, 213.7, 0.68, 15, 74.62, 85.78, 0.32, 2, 3, 13.87, 213.7, 0.584, 15, 80.24, 75.76, 0.416, 2, 3, 18.14, 196.63, 0.792, 15, 66.51, 63.39, 0.208, 2, 3, 70.91, 198.57, 0.712, 16, 146.08, 22.55, 0.288, 2, 3, 67.81, 179.56, 0.664, 16, 128.97, 13.7, 0.336, 2, 3, 91.86, 213.7, 0.568, 16, 170.72, 14.77, 0.432, 2, 3, 130.89, 213.7, 0.376, 16, 194.01, -16.55, 0.624, 1, 3, -33.46, 153.17, 1, 2, 3, -71.1, 161.32, 0.84, 15, -7.68, 119.53, 0.16, 2, 3, -67.61, 143.08, 0.872, 15, -22.84, 107.21, 0.128, 2, 3, -101.36, 122.13, 0.792, 15, -57.18, 124.56, 0.208, 2, 3, -94.38, 113.21, 0.84, 15, -62.34, 114.14, 0.16, 2, 3, -74.2, 107.39, 0.872, 15, -58.38, 94.3, 0.128, 1, 3, -90.11, 85.66, 1, 2, 3, -85.07, 67.81, 0.952, 16, -51.92, 69.69, 0.048, 1, 3, -2.81, 168.3, 1, 2, 3, 35.21, 181.5, 0.76, 15, 60.55, 41.36, 0.24, 1, 3, 32.89, 147.74, 1, 2, 3, 101.56, 123.3, 0.92, 15, 37.93, -43.96, 0.08, 2, 3, 81.39, 72.86, 0.84, 15, -17.95, -53.19, 0.16, 1, 3, 51.9, 150.07, 1, 1, 3, 75.18, 146.19, 1, 2, 3, 72.07, 105.06, 0.792, 15, 7.36, -28.8, 0.208, 2, 3, 92.25, 86.82, 0.808, 15, -0.01, -55.03, 0.192, 1, 3, -50.92, 69.75, 1], "hull": 40, "edges": [30, 28, 28, 26, 18, 20, 0, 78, 2, 0, 76, 78, 74, 76, 74, 72, 72, 70, 70, 68, 66, 68, 64, 66, 64, 62, 62, 60, 60, 58, 58, 56, 56, 54, 54, 52, 52, 50, 50, 48, 48, 46, 46, 44, 42, 44, 42, 40, 40, 38, 38, 36, 36, 34, 30, 32, 34, 32, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 96, 98, 98, 100, 2, 4, 4, 6, 6, 8, 102, 12, 8, 10, 12, 10, 12, 14, 14, 16, 18, 16, 104, 24, 26, 24, 106, 108, 108, 102, 110, 102, 110, 112, 104, 112, 94, 114, 20, 22, 22, 24], "width": 267, "height": 222}}}}, {"name": "POUNCERBOSS_BODY_3", "attachments": {"Body_2": {"Body_2": {"name": "<PERSON><PERSON>,<PERSON>r/Pouncer Boss/Body_2", "type": "mesh", "uvs": [0.56826, 0.10074, 0.68799, 0.09458, 0.6778, 0.20864, 0.85102, 0.29496, 0.83319, 0.34429, 0.93763, 0.36587, 0.92744, 0.48609, 0.86885, 0.48301, 0.88414, 0.5755, 0.99999, 0.7173, 0.92489, 0.79746, 0.87904, 0.72964, 0.87649, 0.90844, 0.59883, 1, 0.37466, 1, 0.08936, 0.91769, 0.05369, 0.68956, 0.01528, 0.69572, 0.00509, 0.60324, 0.06643, 0.57241, 0, 0.51384, 0.0486, 0.38436, 1e-05, 0.36895, 0, 0.26105, 0.13521, 0.2703, 0.15522, 0.22266, 0.2906, 0.20864, 0.28041, 0.0545, 0.4358, 0.09766, 0.48929, 0, 0.60138, 0, 0.57336, 0.31346, 0.69563, 0.32271, 0.78224, 0.40902, 0.70582, 0.50767, 0.75422, 0.59707, 0.2906, 0.31654, 0.19125, 0.36587, 0.15049, 0.45218, 0.24475, 0.49843, 0.19889, 0.6464, 0.34409, 0.67723], "triangles": [35, 41, 34, 15, 40, 41, 32, 34, 31, 31, 34, 41, 15, 16, 40, 10, 11, 9, 11, 8, 9, 11, 35, 8, 17, 18, 16, 16, 19, 40, 16, 18, 19, 0, 31, 28, 28, 31, 36, 40, 39, 41, 41, 39, 31, 39, 36, 31, 19, 38, 40, 40, 38, 39, 35, 7, 8, 35, 33, 7, 35, 34, 33, 19, 20, 38, 20, 21, 38, 34, 32, 33, 38, 37, 39, 39, 37, 36, 6, 7, 5, 33, 4, 7, 7, 4, 5, 38, 21, 37, 33, 32, 4, 21, 24, 37, 24, 21, 23, 21, 22, 23, 36, 37, 25, 4, 32, 3, 31, 2, 32, 32, 2, 3, 37, 24, 25, 25, 26, 36, 36, 26, 28, 31, 0, 2, 2, 0, 1, 26, 27, 28, 28, 29, 0, 0, 29, 30, 12, 13, 11, 41, 35, 13, 14, 15, 41, 14, 41, 13, 11, 13, 35], "vertices": [2, 4, 15.17, 131.45, 0.728, 16, 58.96, 27.23, 0.272, 2, 4, 37.92, 132.41, 0.84, 16, 73.31, 9.55, 0.16, 1, 4, 35.98, 114.51, 1, 2, 4, 68.9, 100.95, 0.808, 15, 2.72, -33.64, 0.192, 2, 4, 65.51, 93.21, 0.856, 15, -5.54, -35.46, 0.144, 2, 4, 85.35, 89.82, 0.504, 15, 3.42, -53.49, 0.496, 2, 4, 83.42, 70.95, 0.52, 15, -12.98, -63.05, 0.48, 2, 4, 72.28, 71.43, 0.712, 15, -19.14, -53.77, 0.288, 2, 4, 75.19, 56.91, 0.792, 16, 34.96, -65.41, 0.208, 2, 4, 97.2, 34.65, 0.616, 16, 30.23, -96.36, 0.384, 2, 4, 82.93, 22.06, 0.68, 16, 11.61, -92.42, 0.32, 2, 4, 74.22, 32.71, 0.888, 16, 14.96, -79.07, 0.112, 1, 4, 73.74, 4.64, 1, 1, 4, 20.98, -9.74, 1, 1, 4, -21.61, -9.74, 1, 1, 4, -75.82, 3.19, 1, 2, 4, -82.6, 39, 0.776, 16, -73.56, 50.52, 0.224, 2, 4, -89.89, 38.03, 0.6, 16, -78.69, 55.8, 0.4, 2, 4, -91.83, 52.55, 0.664, 16, -68.2, 66.02, 0.336, 2, 4, -80.18, 57.39, 0.84, 16, -57.36, 59.55, 0.16, 1, 4, -92.8, 66.59, 1, 2, 4, -83.56, 86.92, 0.792, 15, -98.45, 81.28, 0.208, 2, 4, -92.8, 89.34, 0.664, 15, -101.93, 90.17, 0.336, 2, 4, -92.8, 106.28, 0.712, 15, -88.25, 100.15, 0.288, 2, 4, -67.11, 104.83, 0.776, 15, -74.29, 78.54, 0.224, 2, 4, -63.31, 112.31, 0.824, 15, -66, 79.87, 0.176, 1, 4, -37.58, 114.51, 1, 2, 4, -39.52, 138.71, 0.712, 16, 32.15, 75.45, 0.288, 2, 4, -10, 131.93, 0.664, 16, 44.33, 47.71, 0.336, 2, 4, 0.17, 147.26, 0.584, 16, 62.7, 48.71, 0.416, 2, 4, 21.46, 147.26, 0.536, 16, 75.41, 31.62, 0.464, 1, 4, 16.14, 98.05, 1, 1, 4, 39.37, 96.6, 1, 2, 4, 55.83, 83.05, 0.872, 15, -19.45, -33.63, 0.128, 2, 4, 41.31, 67.56, 0.856, 16, 23.29, -31.87, 0.144, 2, 4, 50.5, 53.52, 0.872, 16, 17.51, -47.62, 0.128, 1, 4, -37.58, 97.57, 1, 2, 4, -56.46, 89.82, 0.856, 15, -80.14, 61.09, 0.144, 2, 4, -64.2, 76.27, 0.904, 15, -95.65, 59.36, 0.096, 2, 4, -46.3, 69.01, 0.904, 15, -90.96, 40.62, 0.096, 2, 4, -55.01, 45.78, 0.824, 15, -114.87, 33.97, 0.176, 2, 4, -27.42, 40.94, 0.904, 15, -102.52, 8.83, 0.096], "hull": 31, "edges": [52, 54, 54, 56, 56, 58, 58, 60, 60, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 22, 20, 20, 18, 46, 48, 44, 46, 44, 42, 42, 40, 40, 38, 38, 36, 34, 36, 34, 32, 32, 30, 30, 28, 26, 28, 26, 24, 24, 22, 62, 64, 64, 66, 66, 68, 68, 70, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 48, 50, 50, 52], "width": 190, "height": 157}}}}, {"name": "POUNCERBOSS_BODY_4", "attachments": {"Body_2": {"Body_2": {"name": "<PERSON><PERSON>,<PERSON>r/Pouncer Boss/Body_3", "type": "mesh", "uvs": [0.8023, 0.08052, 0.86992, 0.05434, 0.93253, 0.16489, 0.83987, 0.24343, 0.95006, 0.33652, 0.92251, 0.42961, 1, 0.54598, 1, 0.62723, 0.92369, 0.75402, 0.93061, 0.91361, 0.67809, 1, 0.47607, 1, 0.12675, 0.92339, 0.06783, 0.63983, 0, 0.62028, 0, 0.43939, 0.04348, 0.43543, 0, 0.35979, 0.12613, 0.20852, 0.2839, 0.18816, 0.20376, 0, 0.35402, 0, 0.48926, 0.06016, 0.66206, 0, 0.8048, 0, 0.39576, 0.1985, 0.26943, 0.26476, 0.36404, 0.35107, 0.27889, 0.48198, 0.41413, 0.54016, 0.70164, 0.2183, 0.47458, 0.21055, 0.86637, 0.6036], "triangles": [10, 8, 9, 10, 32, 8, 32, 10, 29, 12, 29, 11, 11, 29, 10, 12, 28, 29, 12, 13, 28, 8, 32, 7, 14, 16, 13, 32, 6, 7, 30, 5, 32, 30, 32, 29, 30, 29, 31, 28, 16, 26, 26, 16, 18, 16, 28, 13, 14, 15, 16, 32, 5, 6, 5, 30, 3, 28, 27, 29, 29, 27, 31, 31, 27, 25, 28, 26, 27, 16, 17, 18, 5, 3, 4, 27, 26, 25, 26, 19, 25, 26, 18, 19, 30, 0, 3, 2, 0, 1, 2, 3, 0, 31, 22, 30, 22, 23, 30, 30, 23, 0, 31, 25, 22, 19, 21, 25, 25, 21, 22, 19, 20, 21, 0, 23, 24], "vertices": [2, 4, 33.33, 83.56, 0.76, 15, -32.07, -19.58, 0.24, 2, 4, 41.1, 86.16, 0.68, 15, -24.97, -23.93, 0.32, 2, 4, 48.3, 75.21, 0.6, 15, -28.26, -36.46, 0.4, 3, 4, 37.65, 67.44, 0.65056, 15, -41.1, -33.2, 0.20544, 16, 23.16, -31.46, 0.144, 2, 4, 50.32, 58.22, 0.792, 16, 22.72, -46.65, 0.208, 2, 4, 47.15, 49, 0.728, 16, 13.86, -49.74, 0.272, 1, 4, 56.06, 37.48, 1, 2, 4, 56.06, 29.44, 0.904, 15, -56.88, -71.63, 0.096, 1, 4, 47.29, 16.89, 1, 1, 4, 48.08, 1.09, 1, 1, 4, 19.04, -7.46, 1, 1, 4, -4.19, -7.46, 1, 1, 4, -44.36, 0.12, 1, 2, 4, -51.14, 28.19, 0.712, 16, -66.46, 13.97, 0.288, 2, 4, -58.94, 30.13, 0.696, 16, -70.11, 17.47, 0.304, 2, 4, -58.94, 48.04, 0.728, 16, -57.27, 30.94, 0.272, 2, 4, -53.94, 48.43, 0.84, 16, -53.64, 29.49, 0.16, 1, 4, -58.94, 55.92, 1, 1, 4, -44.43, 70.89, 1, 1, 4, -26.29, 72.91, 1, 2, 4, -35.5, 91.54, 0.808, 15, -72.05, 38.63, 0.192, 2, 4, -18.22, 91.54, 0.92, 15, -60.53, 25.29, 0.08, 1, 4, -2.67, 85.58, 1, 2, 4, 17.2, 91.54, 0.776, 15, -36.93, -2.05, 0.224, 2, 4, 33.62, 91.54, 0.712, 15, -25.99, -14.72, 0.288, 1, 4, -13.42, 71.88, 1, 2, 4, -27.95, 65.32, 0.904, 15, -86.37, 16.09, 0.096, 2, 4, -17.07, 56.78, 0.92, 15, -85.43, 2.25, 0.08, 2, 4, -26.86, 43.82, 0.856, 15, -101.52, 1.54, 0.144, 2, 4, -11.31, 38.06, 0.872, 15, -95.41, -14.13, 0.128, 1, 4, 21.75, 69.92, 1, 1, 4, -4.36, 70.69, 1, 1, 4, 40.7, 31.78, 1], "hull": 25, "edges": [28, 26, 26, 24, 24, 22, 20, 22, 20, 18, 18, 16, 16, 14, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 14, 12, 10, 12, 50, 52, 52, 54, 54, 56, 56, 58, 0, 60, 50, 62, 14, 64], "width": 115, "height": 99}}}}, {"name": "POUNCER_BODY_0", "bones": ["SpawnBall"], "attachments": {"Anlter_1_Front": {"Patroller,Chaser/Patroller_images/Anlter_1": {"name": "<PERSON><PERSON>,<PERSON><PERSON>/Pouncer/Horn_2", "x": 22.81, "y": 11.11, "rotation": -64.59, "width": 37, "height": 54}}, "Anlter_4_Front": {"Patroller,Chaser/Patroller_images/Anlter_4": {"name": "<PERSON><PERSON>,<PERSON>r/Pouncer/Horn_1", "x": 22.32, "y": -18.57, "rotation": -108.03, "width": 39, "height": 51}}, "Eyes": {"Patroller,Chaser/Chaser_images/Eyes": {"name": "<PERSON><PERSON>,<PERSON>r/Pouncer/Eyes_Normal", "x": 1.58, "y": -3.25, "width": 116, "height": 62}, "Patroller,Chaser/Chaser_images/Eyes_Shocked": {"name": "<PERSON>ler,<PERSON>r/Pouncer/Eyes_Shocked", "x": 1.26, "y": -4.97, "width": 123, "height": 63}, "Patroller,Chaser/Chaser_images/Eyes_Shut": {"name": "<PERSON><PERSON>,<PERSON>r/Pouncer/Eyes_Shut", "x": 1.35, "y": -1.77, "width": 105, "height": 57}}, "Grass_0_back": {"Patroller,Chaser/Patroller_images/Grass_0": {"x": 22.49, "y": -6.43, "rotation": 90, "width": 75, "height": 62}}, "Head": {"Patroller,Chaser/Patroller_images/Head": {"name": "Patroller,Chaser/Pouncer/Head", "y": -24.56, "width": 190, "height": 199}}, "HeadInner": {"Patroller,Chaser/Patroller_images/HeadInner": {"name": "Patroller,<PERSON>r/Pouncer/Face", "x": 1.23, "y": 38.46, "width": 139, "height": 115}}}}, {"name": "POUNCER_BODY_0_BACK", "attachments": {"Anlter_1_Behind": {"Patroller,Chaser/Patroller_images/Anlter_1": {"name": "<PERSON><PERSON>,<PERSON><PERSON>/Pouncer/Horn_2", "x": 22.81, "y": 11.11, "rotation": -64.59, "width": 37, "height": 54}}, "Anlter_4_Behind": {"Patroller,Chaser/Patroller_images/Anlter_4": {"name": "<PERSON><PERSON>,<PERSON>r/Pouncer/Horn_1", "x": 22.32, "y": -18.57, "rotation": -108.03, "width": 39, "height": 51}}, "Grass_0_back": {"Patroller,Chaser/Patroller_images/Grass_0": {"x": 22.49, "y": -6.43, "rotation": 90, "width": 75, "height": 62}}, "Head": {"Patroller,Chaser/Patroller_images/Head": {"name": "<PERSON>ler,<PERSON>r/Pouncer/Head_Back", "y": -24.56, "width": 190, "height": 199}}}}, {"name": "POUNCER_BODY_1", "attachments": {"Body_0": {"Body_0": {"name": "<PERSON>ler,<PERSON>r/Pouncer/Body_1", "type": "mesh", "uvs": [0.72842, 0.09089, 0.87208, 0.11192, 0.94551, 0.18202, 0.89762, 0.25563, 0.9487, 0.33274, 0.89762, 0.38181, 1, 0.49748, 1, 0.7066, 0.9474, 0.74944, 0.9474, 0.86652, 0.81735, 1, 0.20353, 1, 0.04488, 0.82083, 0.04487, 0.6295, 0, 0.60666, 0, 0.34326, 0.14419, 0.20305, 0.10908, 0.11542, 0.29424, 0.1014, 0.27828, 0, 0.44004, 0.04626, 0.46607, 0, 0.54044, 0, 0.53486, 0.05238, 0.68359, 0, 0.75715, 0, 0.19573, 0.55811, 0.4012, 0.51242, 0.53125, 0.61808, 0.6665, 0.53526, 0.81735, 0.66377, 0.31797, 0.38391, 0.48963, 0.23827, 0.6665, 0.3525], "triangles": [3, 1, 2, 16, 17, 18, 23, 21, 22, 20, 21, 23, 0, 24, 25, 18, 19, 20, 32, 20, 23, 18, 20, 32, 23, 0, 32, 0, 23, 24, 33, 32, 0, 1, 3, 0, 5, 3, 4, 31, 18, 32, 16, 18, 31, 27, 31, 32, 27, 32, 33, 3, 29, 33, 27, 33, 29, 31, 15, 16, 26, 31, 27, 26, 15, 31, 14, 15, 26, 28, 27, 29, 13, 14, 26, 33, 0, 3, 5, 30, 3, 30, 5, 6, 30, 29, 3, 30, 6, 7, 8, 30, 7, 12, 13, 26, 30, 8, 9, 28, 11, 26, 28, 26, 27, 12, 26, 11, 30, 28, 29, 10, 30, 9, 10, 28, 30, 11, 28, 10], "vertices": [2, 2, 49.15, 167.76, 0.84, 15, 45.06, 21.67, 0.16, 2, 2, 79.75, 163.68, 0.44, 15, 59.79, -5.46, 0.56, 2, 2, 95.39, 150.08, 0.152, 15, 58.02, -26.11, 0.848, 2, 2, 85.19, 135.8, 0.456, 15, 40.47, -26.28, 0.544, 2, 2, 96.07, 120.84, 0.792, 15, 34.79, -43.88, 0.208, 2, 2, 85.19, 111.32, 0.856, 15, 20.69, -40.7, 0.144, 2, 2, 107, 88.88, 0.808, 15, 15.41, -71.54, 0.192, 2, 2, 107, 48.31, 0.648, 15, -17.37, -95.44, 0.352, 1, 2, 95.8, 40, 1, 1, 2, 95.8, 17.28, 1, 1, 2, 68.1, -8.61, 1, 1, 2, -62.65, -8.61, 1, 1, 2, -96.44, 26.15, 1, 2, 2, -96.44, 63.26, 0.92, 15, -125.15, 77.75, 0.08, 2, 2, -106, 67.7, 0.744, 15, -127.2, 88.09, 0.256, 2, 2, -106, 118.8, 0.568, 15, -85.91, 118.19, 0.432, 2, 2, -75.29, 146, 0.6, 15, -45.84, 109.4, 0.4, 2, 2, -82.77, 163, 0.408, 15, -36.51, 125.46, 0.592, 2, 2, -43.33, 165.72, 0.552, 15, -11.07, 95.2, 0.448, 2, 2, -46.73, 185.39, 0.744, 15, 2.82, 109.53, 0.256, 2, 2, -12.27, 176.41, 0.68381, 15, 15.87, 76.4, 0.31619, 2, 2, -6.73, 185.39, 0.5589, 15, 26.38, 77.21, 0.4411, 2, 2, 9.11, 185.39, 0.50096, 15, 35.72, 64.41, 0.49904, 2, 2, 7.93, 175.23, 0.58925, 15, 26.81, 59.39, 0.41075, 2, 2, 39.61, 185.39, 0.68983, 15, 53.68, 39.78, 0.31017, 2, 2, 55.27, 185.39, 0.488, 15, 62.91, 27.12, 0.512, 2, 2, -64.31, 77.11, 0.728, 15, -95.03, 59.95, 0.272, 2, 2, -20.54, 85.98, 0.872, 15, -62.08, 29.81, 0.128, 1, 2, 7.16, 65.48, 1, 1, 2, 35.96, 81.55, 1, 1, 2, 68.1, 56.62, 1, 2, 2, -38.27, 110.91, 0.664, 15, -52.38, 58.82, 0.336, 2, 2, -1.71, 139.16, 0.808, 15, -8.01, 45.92, 0.192, 2, 2, 35.96, 117, 0.568, 15, -3.72, 2.43, 0.432], "hull": 26, "edges": [30, 32, 32, 34, 34, 36, 36, 38, 50, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 28, 30, 28, 26, 26, 24, 24, 22, 20, 22, 20, 18, 18, 16, 12, 14, 16, 14, 26, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 16, 54, 62, 62, 64, 64, 66, 66, 58, 38, 40, 40, 42, 42, 44, 44, 46, 48, 50, 46, 48], "width": 213, "height": 194}}}}, {"name": "POUNCER_BODY_2", "attachments": {"Body_1": {"Body_1": {"name": "<PERSON><PERSON>,<PERSON>r/Pouncer/Body_2", "type": "mesh", "uvs": [0.73278, 0.13857, 0.91209, 0.21023, 0.88864, 0.2669, 0.93968, 0.28023, 0.95485, 0.37357, 0.90796, 0.39523, 0.96451, 0.5069, 0.93554, 0.53523, 1, 0.59357, 1, 0.64183, 0.95518, 0.7035, 0.96149, 0.87151, 0.78691, 0.9685, 0.66277, 0.97183, 0.6407, 1, 0.35105, 1, 0.33036, 0.97349, 0.14415, 0.95183, 0.06277, 0.89016, 0.0407, 0.68016, 0, 0.68683, 0, 0.5635, 0.0476, 0.53683, 0, 0.4635, 0.02382, 0.3369, 0.07209, 0.31357, 0.02244, 0.28357, 0.05002, 0.19357, 0.12865, 0.21857, 0.16313, 0.13523, 0.30244, 0.12357, 0.27623, 0, 0.40727, 0, 0.49278, 0.0519, 0.71071, 0, 0.45554, 0.6769, 0.5714, 0.57023, 0.63761, 0.63357, 0.75623, 0.57857, 0.17968, 0.63023, 0.25416, 0.47023, 0.14658, 0.37857, 0.27761, 0.26523, 0.30382, 0.16523, 0.44865, 0.2319, 0.60727, 0.2569, 0.73692, 0.2569, 0.84037, 0.3569, 0.75071, 0.46857, 0.80864, 0.55857], "triangles": [41, 28, 42, 26, 27, 25, 25, 27, 28, 40, 41, 42, 15, 35, 14, 14, 35, 13, 13, 35, 37, 15, 16, 35, 35, 16, 39, 13, 37, 12, 12, 10, 11, 16, 17, 39, 17, 18, 39, 35, 36, 37, 37, 38, 12, 10, 38, 49, 10, 12, 38, 18, 19, 39, 49, 7, 10, 10, 7, 9, 20, 21, 19, 19, 22, 39, 19, 21, 22, 39, 40, 35, 36, 35, 40, 40, 42, 44, 36, 40, 44, 7, 8, 9, 38, 37, 48, 22, 41, 39, 39, 41, 40, 37, 36, 48, 38, 48, 49, 48, 45, 46, 48, 36, 45, 36, 44, 45, 49, 5, 7, 49, 47, 5, 49, 48, 47, 22, 23, 41, 41, 24, 25, 24, 41, 23, 7, 5, 6, 48, 46, 47, 47, 2, 5, 5, 3, 4, 5, 2, 3, 25, 28, 41, 47, 46, 2, 1, 2, 0, 28, 29, 42, 42, 43, 44, 42, 29, 43, 45, 0, 46, 2, 46, 0, 0, 45, 33, 44, 30, 32, 45, 44, 33, 44, 32, 33, 29, 30, 43, 44, 43, 30, 33, 34, 0, 30, 31, 32], "vertices": [2, 3, 40.5, 115.21, 0.808, 16, 61.05, -2.79, 0.192, 1, 3, 71.7, 104.89, 1, 2, 3, 67.62, 96.73, 0.648, 16, 62.4, -35.58, 0.352, 2, 3, 76.5, 94.81, 0.456, 16, 66.16, -43.85, 0.544, 2, 3, 79.14, 81.37, 0.584, 16, 56.95, -53.99, 0.416, 2, 3, 70.98, 78.25, 0.568, 16, 49.57, -49.3, 0.432, 2, 3, 80.82, 62.17, 0.872, 16, 42.54, -66.79, 0.128, 2, 3, 75.78, 58.09, 0.792, 16, 36.26, -65.18, 0.208, 2, 3, 87, 49.69, 0.712, 16, 36.21, -79.2, 0.288, 2, 3, 87, 42.74, 0.76, 16, 30.64, -83.34, 0.24, 2, 3, 79.2, 33.86, 0.824, 16, 18.86, -82.38, 0.176, 1, 3, 80.3, 9.67, 1, 1, 3, 49.92, -4.3, 1, 1, 3, 28.32, -4.78, 1, 1, 3, 24.48, -8.84, 1, 1, 3, -25.92, -8.84, 1, 1, 3, -29.52, -5.02, 1, 1, 3, -61.92, -1.9, 1, 1, 3, -76.08, 6.98, 1, 2, 3, -79.92, 37.22, 0.84, 16, -73.39, 47.31, 0.16, 2, 3, -87, 36.26, 0.664, 16, -78.39, 52.42, 0.336, 2, 3, -87, 54.02, 0.728, 16, -64.14, 63.02, 0.272, 2, 3, -78.72, 57.86, 0.792, 16, -56.12, 58.66, 0.208, 2, 3, -87, 68.42, 0.68, 16, -52.58, 71.61, 0.32, 2, 3, -82.86, 86.65, 0.648, 16, -35.48, 79.16, 0.352, 2, 3, -74.46, 90.01, 0.584, 16, -27.77, 74.43, 0.416, 2, 3, -83.1, 94.33, 0.28, 16, -29.46, 83.94, 0.72, 2, 3, -78.3, 107.29, 0.296, 16, -16.2, 87.82, 0.704, 2, 3, -64.62, 103.69, 0.536, 16, -10.92, 74.69, 0.464, 2, 3, -58.62, 115.69, 0.808, 16, 2.29, 77.04, 0.192, 2, 3, -34.38, 117.37, 0.824, 16, 18.1, 58.59, 0.176, 2, 3, -38.94, 135.16, 0.52, 16, 29.66, 72.87, 0.48, 2, 3, -16.14, 135.16, 0.744, 16, 43.26, 54.57, 0.256, 2, 3, -1.26, 127.69, 0.824, 16, 46.14, 38.17, 0.176, 2, 3, 36.66, 135.16, 0.552, 16, 74.77, 12.2, 0.448, 1, 3, -7.74, 37.69, 1, 1, 3, 12.42, 53.05, 1, 1, 3, 23.94, 43.93, 1, 1, 3, 44.58, 51.85, 1, 1, 3, -55.74, 44.41, 1, 2, 3, -42.78, 67.45, 0.632, 16, -26.97, 35.54, 0.368, 2, 3, -61.5, 80.65, 0.408, 16, -27.55, 58.44, 0.592, 2, 3, -38.7, 96.97, 0.456, 16, -0.85, 49.88, 0.544, 2, 3, -34.14, 111.37, 0.744, 16, 13.43, 54.82, 0.256, 2, 3, -8.94, 101.77, 0.824, 16, 20.76, 28.87, 0.176, 1, 3, 18.66, 98.17, 1, 2, 3, 41.22, 98.17, 0.616, 16, 47.8, -13.53, 0.384, 2, 3, 59.22, 83.77, 0.552, 16, 46.99, -36.57, 0.448, 2, 3, 43.62, 67.69, 0.776, 16, 24.77, -33.65, 0.224, 2, 3, 53.7, 54.73, 0.776, 16, 20.39, -49.47, 0.224], "hull": 35, "edges": [22, 24, 24, 26, 26, 28, 28, 30, 32, 30, 32, 34, 34, 36, 36, 38, 38, 40, 22, 20, 20, 18, 40, 42, 42, 44, 44, 46, 64, 66, 66, 68, 68, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 18, 16, 14, 16, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 62, 64, 60, 62, 70, 72, 72, 74, 74, 76, 70, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 90, 92, 92, 94, 94, 96, 96, 98], "width": 174, "height": 144}}}}, {"name": "POUNCER_BODY_3", "attachments": {"Body_2": {"Body_2": {"name": "<PERSON><PERSON>,<PERSON>r/Pouncer/Body_3", "type": "mesh", "uvs": [0.87257, 0.05758, 0.93388, 0.16583, 0.84315, 0.23989, 1, 0.35952, 1, 0.52474, 1, 0.64581, 0.94308, 0.75914, 0.93983, 0.91403, 0.71543, 1, 0.38696, 1, 0.11703, 0.89892, 0.08178, 0.70508, 0.06825, 0.6307, 0, 0.63825, 0, 0.44181, 0, 0.36522, 0.12466, 0.2228, 0.27424, 0.18577, 0.19823, 0, 0.36743, 0, 0.47777, 0.06043, 0.64942, 0, 0.78184, 0, 0.04374, 0.44498, 0.80882, 0.07183, 0.91426, 0.43643, 0.33309, 0.63867, 0.45325, 0.76686, 0.57586, 0.61589, 0.77694, 0.75261, 0.81127, 0.61019, 0.36742, 0.34528, 0.26934, 0.49055, 0.41647, 0.56746, 0.25708, 0.25128, 0.43854, 0.19431], "triangles": [32, 34, 31, 2, 0, 1, 13, 23, 12, 24, 22, 0, 17, 18, 19, 35, 19, 20, 17, 19, 35, 2, 24, 0, 34, 16, 17, 34, 17, 35, 31, 34, 35, 23, 14, 15, 16, 23, 15, 25, 3, 4, 28, 33, 31, 32, 31, 33, 21, 24, 2, 30, 2, 25, 30, 25, 4, 21, 35, 20, 35, 2, 28, 28, 31, 35, 21, 22, 24, 21, 2, 35, 30, 28, 2, 34, 23, 16, 32, 23, 34, 13, 14, 23, 26, 32, 33, 30, 4, 5, 11, 12, 26, 29, 28, 30, 6, 30, 5, 29, 30, 6, 27, 33, 28, 26, 33, 27, 10, 11, 26, 7, 29, 6, 9, 26, 27, 10, 26, 9, 29, 27, 28, 8, 29, 7, 8, 27, 29, 9, 27, 8, 12, 32, 26, 23, 32, 12, 25, 2, 3], "vertices": [2, 4, 43.35, 85.78, 0.232, 16, 39.13, -22.63, 0.768, 2, 4, 50.4, 75.07, 0.264, 16, 34.74, -34.68, 0.736, 2, 4, 39.96, 67.74, 0.648, 16, 22.63, -30.68, 0.352, 2, 4, 58, 55.89, 0.52, 16, 23.88, -52.22, 0.48, 2, 4, 58, 39.54, 0.872, 16, 10.76, -61.98, 0.128, 1, 4, 58, 27.55, 1, 1, 4, 51.45, 16.33, 1, 1, 4, 51.08, 0.99, 1, 1, 4, 25.27, -7.52, 1, 1, 4, -12.5, -7.52, 1, 1, 4, -43.54, 2.49, 1, 1, 4, -47.6, 21.68, 1, 2, 4, -49.15, 29.04, 0.664, 16, -61.6, 17.74, 0.336, 2, 4, -57, 28.3, 0.552, 16, -66.88, 23.59, 0.448, 2, 4, -57, 47.74, 0.472, 16, -51.27, 35.2, 0.528, 2, 4, -57, 55.33, 0.648, 16, -45.19, 39.72, 0.352, 2, 4, -42.66, 69.43, 0.664, 16, -25.32, 36.63, 0.336, 2, 4, -25.46, 73.09, 0.68, 16, -12.11, 25.02, 0.32, 2, 4, -34.2, 91.48, 0.552, 16, -2.57, 43.01, 0.448, 2, 4, -14.75, 91.48, 0.776, 16, 9.04, 27.39, 0.224, 1, 4, -2.06, 85.5, 1, 2, 4, 17.68, 91.48, 0.872, 16, 28.39, 1.37, 0.128, 2, 4, 32.91, 91.48, 0.808, 16, 37.48, -10.85, 0.192, 2, 4, -51.97, 47.43, 0.712, 16, -48.52, 30.98, 0.288, 2, 4, 36.01, 84.37, 0.712, 16, 33.62, -17.58, 0.288, 2, 4, 48.14, 48.28, 0.552, 16, 11.89, -48.85, 0.448, 1, 4, -18.69, 28.26, 1, 1, 4, -4.88, 15.57, 1, 1, 4, 9.22, 30.51, 1, 1, 4, 32.35, 16.98, 1, 1, 4, 36.3, 31.08, 1, 2, 4, -14.75, 57.3, 0.376, 16, -18.39, 6.99, 0.624, 2, 4, -26.03, 42.92, 0.52, 16, -36.66, 7.46, 0.48, 2, 4, -9.11, 35.31, 0.728, 16, -32.68, -10.66, 0.272, 2, 4, -27.44, 66.61, 0.552, 16, -18.5, 22.73, 0.448, 2, 4, -6.57, 72.25, 0.792, 16, -1.52, 9.35, 0.208], "hull": 23, "edges": [26, 24, 20, 18, 16, 18, 16, 14, 14, 12, 12, 10, 26, 28, 28, 46, 28, 30, 46, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 48, 48, 0, 0, 2, 2, 4, 4, 6, 6, 50, 6, 8, 8, 10, 50, 8, 24, 22, 22, 20, 22, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 10, 62, 64, 64, 66, 62, 68, 68, 70, 44, 0], "width": 115, "height": 99}}}}, {"name": "POUNCER_BODY_4", "attachments": {"Body_2": {"Body_2": {"name": "<PERSON><PERSON>,<PERSON>r/Pouncer/Body_4", "type": "mesh", "uvs": [0.69847, 0.20001, 0.83424, 0.20228, 0.97311, 0.24274, 0.99999, 0.37092, 1, 0.52474, 1, 0.64581, 0.94308, 0.75914, 0.93983, 0.91403, 0.71543, 1, 0.38696, 1, 0.11703, 0.89892, 0.08178, 0.70508, 0.0094, 0.69906, 0.0564, 0.50153, 1e-05, 0.4646, 1e-05, 0.33104, 0.11976, 0.16583, 0.31838, 0.19716, 0.4508, 0.04334, 0.61264, 0, 0.92897, 0.47061, 0.33309, 0.63867, 0.45325, 0.76686, 0.57586, 0.61589, 0.77694, 0.75261, 0.83334, 0.64722, 0.40176, 0.30255, 0.25217, 0.29401, 0.24236, 0.45067, 0.36545, 0.52707], "triangles": [20, 0, 1, 9, 22, 8, 8, 22, 24, 8, 24, 7, 24, 22, 23, 10, 21, 9, 9, 21, 22, 7, 24, 6, 10, 11, 21, 23, 22, 29, 24, 25, 6, 6, 25, 5, 24, 23, 25, 12, 13, 11, 11, 28, 21, 11, 13, 28, 23, 0, 25, 5, 25, 4, 20, 25, 0, 25, 20, 4, 22, 21, 29, 21, 28, 29, 29, 26, 23, 23, 26, 0, 0, 18, 19, 18, 0, 26, 29, 28, 26, 26, 28, 27, 20, 3, 4, 28, 13, 15, 20, 2, 3, 20, 1, 2, 13, 14, 15, 28, 15, 27, 27, 15, 16, 27, 17, 26, 26, 17, 18, 27, 16, 17], "vertices": [2, 4, 18.64, 59.46, 0.264, 16, 3.27, -18.51, 0.736, 2, 4, 30.88, 59.29, 0.46775, 16, 10.43, -28.43, 0.53225, 2, 4, 43.4, 56.15, 0.648, 16, 15.38, -40.36, 0.352, 2, 4, 45.83, 46.2, 0.52, 16, 8.84, -48.24, 0.48, 2, 4, 45.83, 34.26, 0.872, 16, -0.74, -55.36, 0.128, 1, 4, 45.83, 24.86, 1, 1, 4, 40.7, 16.07, 1, 1, 4, 40.4, 4.04, 1, 1, 4, 20.17, -2.63, 1, 1, 4, -9.44, -2.63, 1, 1, 4, -33.78, 5.22, 1, 1, 4, -36.96, 20.26, 1, 2, 4, -43.49, 20.73, 0.664, 16, -64.89, 8.23, 0.336, 2, 4, -39.25, 36.06, 0.552, 16, -50.06, 13.98, 0.448, 2, 4, -44.33, 38.93, 0.472, 16, -50.79, 19.77, 0.528, 2, 4, -44.33, 49.29, 0.648, 16, -42.47, 25.96, 0.352, 2, 4, -33.54, 62.12, 0.664, 16, -25.74, 24.95, 0.336, 2, 4, -15.63, 59.68, 0.68, 16, -17, 9.12, 0.32, 1, 4, -3.69, 71.62, 1, 2, 4, 10.9, 74.99, 0.872, 16, 11.11, -3.03, 0.128, 2, 4, 39.42, 38.46, 0.552, 16, -1.19, -47.72, 0.448, 1, 4, -14.3, 25.42, 1, 1, 4, -3.47, 15.47, 1, 1, 4, 7.59, 27.18, 1, 1, 4, 25.72, 16.57, 1, 1, 4, 30.8, 24.75, 1, 2, 4, -8.11, 51.5, 0.376, 16, -19.08, -1.79, 0.624, 2, 4, -21.6, 52.17, 0.52, 16, -26.6, 9.43, 0.48, 2, 4, -22.48, 40.01, 0.728, 16, -36.88, 2.88, 0.272, 2, 4, -11.38, 34.08, 0.79452, 16, -35.02, -9.56, 0.20548], "hull": 20, "edges": [26, 24, 20, 18, 16, 18, 16, 14, 14, 12, 12, 10, 26, 28, 28, 30, 30, 32, 32, 34, 36, 38, 4, 6, 6, 40, 6, 8, 8, 10, 40, 8, 24, 22, 22, 20, 22, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 10, 52, 54, 54, 56, 0, 2, 2, 4, 38, 0, 34, 36, 56, 58, 58, 42], "width": 89, "height": 76}}}}, {"name": "POUNCER_CRAB", "bones": ["SpawnBall"], "attachments": {"Eyes": {"Patroller,Chaser/Chaser_images/Eyes": {"name": "<PERSON><PERSON>,<PERSON>r/Pouncer/Pouncer<PERSON>rab_Eyes", "x": 1.58, "y": 22.83, "width": 141, "height": 52}, "Patroller,Chaser/Chaser_images/Eyes_Shocked": {"name": "<PERSON><PERSON>,<PERSON>r/Pouncer/Pouncer<PERSON>rab_Eyes", "x": 1.58, "y": 9.79, "width": 141, "height": 52}, "Patroller,Chaser/Chaser_images/Eyes_Shut": {"name": "<PERSON><PERSON>,<PERSON>r/Pouncer/Pouncer<PERSON>rab_Eyes_Shut", "x": 1.58, "y": 9.79, "width": 137, "height": 45}}, "Grass_0_back": {"Patroller,Chaser/Patroller_images/Grass_0": {"x": 22.49, "y": -6.43, "rotation": 90, "width": 75, "height": 62}}, "Head": {"Patroller,Chaser/Patroller_images/Head": {"name": "<PERSON>ler,<PERSON>r/Pouncer/PouncerCrab_Body", "x": -1.13, "y": 15.13, "width": 239, "height": 223}}, "Patroller,Chaser/Pouncer/PouncerCrab_Leg1": {"Patroller,Chaser/Pouncer/PouncerCrab_Leg1": {"type": "mesh", "uvs": [1, 0.26937, 0.38422, 0.44612, 0.3727, 0.45798, 0.2421, 1, 0.14799, 1, 0, 0.76048, 1e-05, 0.27767, 0.0498, 0.23263, 0.35733, 0.05702, 0.72418, 0, 1, 0], "triangles": [2, 3, 5, 3, 4, 5, 5, 6, 2, 2, 6, 1, 6, 7, 1, 7, 8, 1, 0, 8, 9, 0, 1, 8, 9, 10, 0], "vertices": [1, 49, 11.33, 28.51, 1, 2, 49, 52.74, 17.74, 0.4992, 50, 11.56, 13.58, 0.5008, 2, 49, 54.07, 18.22, 0.38521, 50, 12.8, 12.9, 0.61479, 1, 50, 68.36, 6.85, 1, 1, 50, 68.59, 0.92, 1, 1, 50, 44.55, -9.35, 1, 2, 49, 60.48, -10.91, 0.45538, 50, -4.66, -11.28, 0.54462, 2, 49, 55.14, -12.46, 0.75685, 50, -9.38, -8.32, 0.24315, 1, 49, 28.8, -13.96, 1, 1, 49, 7.27, -3.75, 1, 1, 49, -6.14, 7.31, 1], "hull": 11, "edges": [18, 20, 16, 18, 10, 12, 10, 8, 6, 8, 6, 4, 4, 2, 0, 20, 2, 0, 12, 14, 14, 16, 14, 2], "width": 63, "height": 102}}, "Patroller,Chaser/Pouncer/PouncerCrab_Leg2": {"Patroller,Chaser/Pouncer/PouncerCrab_Leg2": {"type": "mesh", "uvs": [1, 0.16844, 0.76707, 0.38568, 0.42681, 0.47989, 0.41339, 0.48656, 0.32753, 0.99915, 0.20524, 1, 0, 0.79711, 1e-05, 0.35892, 0.02547, 0.33483, 0.07772, 0.31815, 0.28262, 0.25275, 0.47824, 0, 1, 0], "triangles": [7, 8, 3, 5, 6, 4, 4, 6, 3, 6, 7, 3, 8, 9, 3, 3, 9, 2, 9, 10, 2, 2, 10, 1, 1, 10, 0, 10, 11, 0, 11, 12, 0], "vertices": [1, 47, 12, 13.22, 1, 2, 47, 45.13, 23.84, 0.97081, 48, -5.7, 38.82, 0.02919, 2, 47, 71.23, 16.84, 0.75195, 48, 7.73, 15.37, 0.24805, 2, 47, 72.55, 16.86, 0.24048, 48, 8.68, 14.45, 0.75952, 1, 48, 81.48, 8.69, 1, 1, 48, 81.62, 0.25, 1, 1, 48, 52.84, -13.98, 1, 1, 48, -9.38, -14.11, 1, 2, 47, 76.42, -17.28, 0.448, 48, -12.8, -12.37, 0.552, 2, 47, 72.19, -16.43, 0.8294, 48, -15.18, -8.77, 0.1706, 1, 47, 55.61, -13.09, 1, 1, 47, 20.77, -29.11, 1, 1, 47, -4.82, -3.78, 1], "hull": 13, "edges": [12, 10, 8, 10, 4, 2, 0, 24, 2, 0, 22, 24, 20, 22, 12, 14, 8, 6, 6, 4, 14, 16, 16, 18, 18, 20, 18, 4, 16, 6], "width": 69, "height": 142}}, "Patroller,Chaser/Pouncer/PouncerCrab_Leg3": {"Patroller,Chaser/Pouncer/PouncerCrab_Leg1": {"type": "mesh", "uvs": [1, 0.26937, 0.38422, 0.44612, 0.3727, 0.45798, 0.2421, 1, 0.14799, 1, 0, 0.76048, 1e-05, 0.27767, 0.0498, 0.23263, 0.35733, 0.05702, 0.72418, 0, 1, 0], "triangles": [2, 3, 5, 3, 4, 5, 5, 6, 2, 2, 6, 1, 6, 7, 1, 7, 8, 1, 0, 8, 9, 0, 1, 8, 9, 10, 0], "vertices": [1, 51, 11.33, 28.51, 1, 2, 51, 52.74, 17.74, 0.4992, 52, 11.56, 13.58, 0.5008, 2, 51, 54.07, 18.22, 0.38521, 52, 12.8, 12.9, 0.61479, 1, 52, 68.36, 6.85, 1, 1, 52, 68.59, 0.92, 1, 1, 52, 44.55, -9.35, 1, 2, 51, 60.48, -10.91, 0.45538, 52, -4.66, -11.28, 0.54462, 2, 51, 55.14, -12.46, 0.75685, 52, -9.38, -8.32, 0.24315, 1, 51, 28.8, -13.96, 1, 1, 51, 7.27, -3.75, 1, 1, 51, -6.14, 7.31, 1], "hull": 11, "edges": [18, 20, 16, 18, 10, 12, 10, 8, 6, 8, 6, 4, 4, 2, 0, 20, 2, 0, 12, 14, 14, 16, 14, 2], "width": 63, "height": 102}}, "Patroller,Chaser/Pouncer/PouncerCrab_Leg4": {"Patroller,Chaser/Pouncer/PouncerCrab_Leg2": {"type": "mesh", "uvs": [1, 0.16844, 0.76707, 0.38568, 0.42681, 0.47989, 0.41339, 0.48656, 0.32753, 0.99915, 0.20524, 1, 0, 0.79711, 1e-05, 0.35892, 0.02547, 0.33483, 0.07772, 0.31815, 0.28262, 0.25275, 0.47824, 0, 1, 0], "triangles": [7, 8, 3, 5, 6, 4, 4, 6, 3, 6, 7, 3, 8, 9, 3, 3, 9, 2, 9, 10, 2, 2, 10, 1, 1, 10, 0, 10, 11, 0, 11, 12, 0], "vertices": [1, 53, 12, 13.22, 1, 2, 53, 45.13, 23.84, 0.97081, 54, -5.7, 38.82, 0.02919, 2, 53, 71.23, 16.84, 0.75195, 54, 7.73, 15.37, 0.24805, 2, 53, 72.55, 16.86, 0.24048, 54, 8.68, 14.45, 0.75952, 1, 54, 81.48, 8.69, 1, 1, 54, 81.62, 0.25, 1, 1, 54, 52.84, -13.98, 1, 1, 54, -9.38, -14.11, 1, 2, 53, 76.42, -17.28, 0.448, 54, -12.8, -12.37, 0.552, 2, 53, 72.19, -16.43, 0.8294, 54, -15.18, -8.77, 0.1706, 1, 53, 55.61, -13.09, 1, 1, 53, 20.77, -29.11, 1, 1, 53, -4.82, -3.78, 1], "hull": 13, "edges": [12, 10, 8, 10, 4, 2, 0, 24, 2, 0, 22, 24, 20, 22, 12, 14, 8, 6, 6, 4, 14, 16, 16, 18, 18, 20, 18, 4, 16, 6], "width": 69, "height": 142}}}}, {"name": "POUNCER_CRAB_BACK", "bones": ["SpawnBall"], "attachments": {"Grass_0_back": {"Patroller,Chaser/Patroller_images/Grass_0": {"x": 22.49, "y": -6.43, "rotation": 90, "width": 75, "height": 62}}, "Head": {"Patroller,Chaser/Patroller_images/Head": {"name": "<PERSON><PERSON>,<PERSON>r/Pouncer/Pouncer<PERSON>rab_Body_Back", "x": -1.13, "y": 21.35, "width": 239, "height": 212}}, "Patroller,Chaser/Pouncer/PouncerCrab_Leg1": {"Patroller,Chaser/Pouncer/PouncerCrab_Leg1": {"type": "<PERSON><PERSON><PERSON>", "skin": "POUNCER_CRAB", "parent": "<PERSON><PERSON>,<PERSON>r/Pouncer/PouncerCrab_Leg1", "width": 63, "height": 102}}, "Patroller,Chaser/Pouncer/PouncerCrab_Leg2": {"Patroller,Chaser/Pouncer/PouncerCrab_Leg2": {"type": "<PERSON><PERSON><PERSON>", "skin": "POUNCER_CRAB", "parent": "<PERSON><PERSON>,<PERSON>r/Pouncer/PouncerCrab_Leg2", "width": 69, "height": 142}}, "Patroller,Chaser/Pouncer/PouncerCrab_Leg3": {"Patroller,Chaser/Pouncer/PouncerCrab_Leg1": {"type": "<PERSON><PERSON><PERSON>", "skin": "POUNCER_CRAB", "parent": "<PERSON><PERSON>,<PERSON>r/Pouncer/PouncerCrab_Leg1", "width": 63, "height": 102}}, "Patroller,Chaser/Pouncer/PouncerCrab_Leg4": {"Patroller,Chaser/Pouncer/PouncerCrab_Leg2": {"type": "<PERSON><PERSON><PERSON>", "skin": "POUNCER_CRAB", "parent": "<PERSON><PERSON>,<PERSON>r/Pouncer/PouncerCrab_Leg2", "width": 69, "height": 142}}}}], "animations": {"animation": {"bones": {"Main": {"scale": [{"x": 1.05, "y": 0.959, "curve": [0.224, 1.044, 0.116, 0.969, 0.224, 0.965, 0.116, 1.058]}, {"time": 0.3333, "x": 0.963, "y": 1.065, "curve": [0.557, 0.969, 0.45, 1.044, 0.557, 1.058, 0.45, 0.966]}, {"time": 0.6667, "x": 1.05, "y": 0.959}]}, "BODY_1": {"scale": [{"x": 1.034, "y": 0.978, "curve": [0.018, 1.042, 0.046, 1.048, 0.018, 0.968, 0.046, 0.96]}, {"time": 0.1, "x": 1.05, "y": 0.959, "curve": [0.183, 1.05, 0.35, 0.963, 0.183, 0.959, 0.35, 1.065]}, {"time": 0.4333, "x": 0.963, "y": 1.065, "curve": [0.607, 0.967, 0.609, 1.007, 0.607, 1.06, 0.609, 1.012]}, {"time": 0.6667, "x": 1.034, "y": 0.978}]}, "BODY_2": {"scale": [{"x": 0.977, "y": 1.047, "curve": [0.06, 1.005, 0.061, 1.045, 0.06, 1.013, 0.061, 0.964]}, {"time": 0.2333, "x": 1.05, "y": 0.959, "curve": [0.317, 1.05, 0.483, 0.963, 0.317, 0.959, 0.483, 1.065]}, {"time": 0.5667, "x": 0.963, "y": 1.065, "curve": [0.621, 0.964, 0.648, 0.97, 0.621, 1.063, 0.648, 1.056]}, {"time": 0.6667, "x": 0.977, "y": 1.047}]}, "BODY_3": {"scale": [{"x": 0.963, "y": 1.065, "curve": [0.224, 0.969, 0.116, 1.044, 0.224, 1.058, 0.116, 0.966]}, {"time": 0.3333, "x": 1.05, "y": 0.959, "curve": [0.557, 1.044, 0.45, 0.969, 0.557, 0.965, 0.45, 1.058]}, {"time": 0.6667, "x": 0.963, "y": 1.065}]}, "FACE": {"translate": [{"y": 5.07, "curve": [0.063, 0, 0.125, 0, 0.063, 1.85, 0.125, -1.37]}, {"time": 0.1667, "y": -1.37, "curve": [0.25, 0, 0.417, 0, 0.25, -1.37, 0.417, 10.14]}, {"time": 0.5, "y": 10.14, "curve": [0.542, 0, 0.604, 0, 0.542, 10.14, 0.604, 7.61]}, {"time": 0.6667, "y": 5.07}], "scale": [{"curve": [0.083, 1, 0.25, 0.913, 0.083, 1, 0.25, 0.987]}, {"time": 0.3333, "x": 0.913, "y": 0.987, "curve": [0.417, 0.913, 0.583, 1, 0.417, 0.987, 0.583, 1]}, {"time": 0.6667}]}, "Twig2": {"rotate": [{"value": 4.09, "curve": [0.061, -1.03, 0.126, -6.8]}, {"time": 0.1667, "value": -6.8, "curve": [0.258, -6.8, 0.442, 12.2]}, {"time": 0.5333, "value": 12.2, "curve": [0.567, 12.2, 0.616, 8.36]}, {"time": 0.6667, "value": 4.09}]}, "Twig3": {"rotate": [{"value": 0.15, "curve": [0.063, 3.32, 0.125, 6.49]}, {"time": 0.1667, "value": 6.49, "curve": [0.25, 6.49, 0.417, -6.2]}, {"time": 0.5, "value": -6.2, "curve": [0.542, -6.2, 0.604, -3.02]}, {"time": 0.6667, "value": 0.15}]}, "MOUTH": {"scale": [{"y": 0.955, "curve": [0.08, 1, 0.177, 1, 0.08, 0.902, 0.177, 0.814]}, {"time": 0.2333, "y": 0.814, "curve": [0.317, 1, 0.483, 1, 0.317, 0.814, 0.483, 1]}, {"time": 0.5667, "curve": [0.594, 1, 0.628, 1, 0.594, 1, 0.628, 0.981]}, {"time": 0.6667, "y": 0.955}]}, "Fleshball_2": {"scale": [{"x": 0.992, "y": 0.992, "curve": [0.051, 0.966, 0.099, 0.944, 0.051, 0.966, 0.099, 0.944]}, {"time": 0.1333, "x": 0.944, "y": 0.944, "curve": [0.217, 0.944, 0.383, 1.076, 0.217, 0.944, 0.383, 1.076]}, {"time": 0.4667, "x": 1.076, "y": 1.076, "curve": [0.516, 1.076, 0.594, 1.03, 0.516, 1.076, 0.594, 1.03]}, {"time": 0.6667, "x": 0.992, "y": 0.992}]}, "Fleshball_1": {"scale": [{"x": 1.044, "y": 1.044, "curve": [0.08, 1.006, 0.177, 0.944, 0.08, 1.006, 0.177, 0.944]}, {"time": 0.2333, "x": 0.944, "y": 0.944, "curve": [0.317, 0.944, 0.483, 1.076, 0.317, 0.944, 0.483, 1.076]}, {"time": 0.5667, "x": 1.076, "y": 1.076, "curve": [0.594, 1.076, 0.628, 1.063, 0.594, 1.076, 0.628, 1.063]}, {"time": 0.6667, "x": 1.044, "y": 1.044}]}, "Fleshball_4": {"scale": [{"x": 1.044, "y": 1.044, "curve": [0.08, 1.006, 0.177, 0.944, 0.08, 1.006, 0.177, 0.944]}, {"time": 0.2333, "x": 0.944, "y": 0.944, "curve": [0.317, 0.944, 0.483, 1.076, 0.317, 0.944, 0.483, 1.076]}, {"time": 0.5667, "x": 1.076, "y": 1.076, "curve": [0.594, 1.076, 0.628, 1.063, 0.594, 1.076, 0.628, 1.063]}, {"time": 0.6667, "x": 1.044, "y": 1.044}]}, "Fleshball_5": {"scale": [{"x": 1.059, "y": 1.059, "curve": [0.025, 1.07, 0.048, 1.076, 0.025, 1.07, 0.048, 1.076]}, {"time": 0.0667, "x": 1.076, "y": 1.076, "curve": [0.15, 1.076, 0.317, 0.944, 0.15, 1.076, 0.317, 0.944]}, {"time": 0.4, "x": 0.944, "y": 0.944, "curve": [0.465, 0.944, 0.581, 1.026, 0.465, 0.944, 0.581, 1.026]}, {"time": 0.6667, "x": 1.059, "y": 1.059}]}, "Fleshball_3": {"scale": [{"x": 0.992, "y": 0.992, "curve": [0.051, 0.966, 0.099, 0.944, 0.051, 0.966, 0.099, 0.944]}, {"time": 0.1333, "x": 0.944, "y": 0.944, "curve": [0.217, 0.944, 0.383, 1.076, 0.217, 0.944, 0.383, 1.076]}, {"time": 0.4667, "x": 1.076, "y": 1.076, "curve": [0.516, 1.076, 0.594, 1.03, 0.516, 1.076, 0.594, 1.03]}, {"time": 0.6667, "x": 0.992, "y": 0.992}]}, "Fleshball_7": {"scale": [{"x": 0.938, "y": 0.938, "curve": [0.025, 0.919, 0.048, 0.908, 0.025, 0.919, 0.048, 0.908]}, {"time": 0.0667, "x": 0.908, "y": 0.908, "curve": [0.15, 0.908, 0.317, 1.141, 0.15, 0.908, 0.317, 1.141]}, {"time": 0.4, "x": 1.141, "y": 1.141, "curve": [0.465, 1.141, 0.581, 0.996, 0.465, 1.141, 0.581, 0.996]}, {"time": 0.6667, "x": 0.938, "y": 0.938}]}, "Fleshball_6": {"scale": [{"x": 1.11, "y": 1.11, "curve": [0.085, 1.052, 0.202, 0.908, 0.085, 1.052, 0.202, 0.908]}, {"time": 0.2667, "x": 0.908, "y": 0.908, "curve": [0.35, 0.908, 0.517, 1.141, 0.35, 0.908, 0.517, 1.141]}, {"time": 0.6, "x": 1.141, "y": 1.141, "curve": [0.619, 1.141, 0.642, 1.129, 0.619, 1.141, 0.642, 1.129]}, {"time": 0.6667, "x": 1.11, "y": 1.11}]}, "Fleshball_8": {"scale": [{"x": 0.993, "y": 0.993, "curve": [0.073, 1.059, 0.151, 1.141, 0.073, 1.059, 0.151, 1.141]}, {"time": 0.2, "x": 1.141, "y": 1.141, "curve": [0.283, 1.141, 0.45, 0.908, 0.283, 1.141, 0.45, 0.908]}, {"time": 0.5333, "x": 0.908, "y": 0.908, "curve": [0.568, 0.908, 0.616, 0.946, 0.568, 0.908, 0.616, 0.946]}, {"time": 0.6667, "x": 0.993, "y": 0.993}]}, "Fleshball_9": {"scale": [{"x": 0.999, "y": 0.999}, {"time": 0.1667, "x": 0.897, "y": 0.897}, {"time": 0.5, "x": 1.101, "y": 1.101}, {"time": 0.6667, "x": 0.999, "y": 0.999}]}, "MOUTH_BTM": {"translate": [{"curve": [0.083, 0, 0.25, 0, 0.083, 0, 0.25, 20.4]}, {"time": 0.3333, "y": 20.4, "curve": [0.417, 0, 0.583, 0, 0.417, 20.4, 0.583, 0]}, {"time": 0.6667}]}, "HeadFleshball_3": {"scale": [{"x": 1.005, "y": 1.005, "curve": [0.063, 0.973, 0.125, 0.94, 0.063, 0.973, 0.125, 0.94]}, {"time": 0.1667, "x": 0.94, "y": 0.94, "curve": [0.25, 0.94, 0.417, 1.07, 0.25, 0.94, 0.417, 1.07]}, {"time": 0.5, "x": 1.07, "y": 1.07, "curve": [0.542, 1.07, 0.604, 1.037, 0.542, 1.07, 0.604, 1.037]}, {"time": 0.6667, "x": 1.005, "y": 1.005}]}, "HeadFleshball_2": {"scale": [{"x": 1.053, "y": 1.053, "curve": [0.025, 1.063, 0.048, 1.07, 0.025, 1.063, 0.048, 1.07]}, {"time": 0.0667, "x": 1.07, "y": 1.07, "curve": [0.15, 1.07, 0.317, 0.94, 0.15, 1.07, 0.317, 0.94]}, {"time": 0.4, "x": 0.94, "y": 0.94, "curve": [0.465, 0.94, 0.581, 1.021, 0.465, 0.94, 0.581, 1.021]}, {"time": 0.6667, "x": 1.053, "y": 1.053}]}, "HeadFleshball_1": {"scale": [{"x": 1.005, "y": 1.005, "curve": [0.063, 1.037, 0.125, 1.07, 0.063, 1.037, 0.125, 1.07]}, {"time": 0.1667, "x": 1.07, "y": 1.07, "curve": [0.25, 1.07, 0.417, 0.94, 0.25, 1.07, 0.417, 0.94]}, {"time": 0.5, "x": 0.94, "y": 0.94, "curve": [0.542, 0.94, 0.604, 0.973, 0.542, 0.94, 0.604, 0.973]}, {"time": 0.6667, "x": 1.005, "y": 1.005}]}, "FleshTurret": {"scale": [{"x": 0.972, "y": 0.972, "curve": [0.08, 1.008, 0.177, 1.07, 0.08, 1.008, 0.177, 1.07]}, {"time": 0.2333, "x": 1.07, "y": 1.07, "curve": [0.317, 1.07, 0.483, 0.94, 0.317, 1.07, 0.483, 0.94]}, {"time": 0.5667, "x": 0.94, "y": 0.94, "curve": [0.594, 0.94, 0.628, 0.953, 0.594, 0.94, 0.628, 0.953]}, {"time": 0.6667, "x": 0.972, "y": 0.972}]}, "TurretMouth": {"scale": [{"x": 0.904, "y": 0.768, "curve": [0.215, 0.909, 0.125, 0.998, 0.215, 0.785, 0.125, 1.104]}, {"time": 0.3333, "y": 1.111, "curve": [0.549, 0.997, 0.459, 0.95, 0.549, 1.094, 0.459, 0.774]}, {"time": 0.6667, "x": 0.949, "y": 0.768}]}, "HeadFleshball_1_Behind": {"scale": [{"x": 1.005, "y": 1.005, "curve": [0.063, 1.037, 0.125, 1.07, 0.063, 1.037, 0.125, 1.07]}, {"time": 0.1667, "x": 1.07, "y": 1.07, "curve": [0.25, 1.07, 0.417, 0.94, 0.25, 1.07, 0.417, 0.94]}, {"time": 0.5, "x": 0.94, "y": 0.94, "curve": [0.542, 0.94, 0.604, 0.973, 0.542, 0.94, 0.604, 0.973]}, {"time": 0.6667, "x": 1.005, "y": 1.005}]}, "HeadFleshball_3_Behind": {"scale": [{"x": 1.005, "y": 1.005, "curve": [0.063, 0.973, 0.125, 0.94, 0.063, 0.973, 0.125, 0.94]}, {"time": 0.1667, "x": 0.94, "y": 0.94, "curve": [0.25, 0.94, 0.417, 1.07, 0.25, 0.94, 0.417, 1.07]}, {"time": 0.5, "x": 1.07, "y": 1.07, "curve": [0.542, 1.07, 0.604, 1.037, 0.542, 1.07, 0.604, 1.037]}, {"time": 0.6667, "x": 1.005, "y": 1.005}]}, "MOUTH_CHASER": {"scale": [{"y": 0.955, "curve": [0.08, 1, 0.177, 1, 0.08, 0.902, 0.177, 0.814]}, {"time": 0.2333, "y": 0.814, "curve": [0.317, 1, 0.483, 1, 0.317, 0.814, 0.483, 1]}, {"time": 0.5667, "curve": [0.594, 1, 0.628, 1, 0.594, 1, 0.628, 0.981]}, {"time": 0.6667, "y": 0.955}]}, "CrabLeg3": {"rotate": [{"value": -0.91, "curve": [0.036, 5.55, 0.075, 13.62]}, {"time": 0.1, "value": 13.62, "curve": [0.142, 13.62, 0.225, -9.37]}, {"time": 0.2667, "value": -9.37, "curve": [0.308, -9.37, 0.392, 13.62]}, {"time": 0.4333, "value": 13.62, "curve": [0.475, 13.62, 0.558, -9.37]}, {"time": 0.6, "value": -9.37, "curve": [0.617, -9.37, 0.641, -5.53]}, {"time": 0.6667, "value": -0.91}]}, "CrabLeg1": {"rotate": [{"value": -9.37, "curve": [0.042, -9.37, 0.125, 13.62]}, {"time": 0.1667, "value": 13.62, "curve": [0.208, 13.62, 0.292, -9.37]}, {"time": 0.3333, "value": -9.37, "curve": [0.375, -9.37, 0.458, 13.62]}, {"time": 0.5, "value": 13.62, "curve": [0.542, 13.62, 0.625, -9.37]}, {"time": 0.6667, "value": -9.37}]}, "CrabLeg4": {"rotate": [{"value": -11.72, "curve": [0.042, -11.72, 0.125, 16.88]}, {"time": 0.1667, "value": 16.88, "curve": [0.208, 16.88, 0.292, -11.72]}, {"time": 0.3333, "value": -11.72, "curve": [0.375, -11.72, 0.458, 16.88]}, {"time": 0.5, "value": 16.88, "curve": [0.525, 16.88, 0.564, 6.84]}, {"time": 0.6, "value": -1.2, "curve": [0.625, -6.93, 0.649, -11.72]}, {"time": 0.6667, "value": -11.72}]}, "CrabLeg2": {"rotate": [{"value": -1.2, "curve": [0.025, -6.93, 0.049, -11.72]}, {"time": 0.0667, "value": -11.72, "curve": [0.108, -11.72, 0.192, 16.88]}, {"time": 0.2333, "value": 16.88, "curve": [0.275, 16.88, 0.358, -11.72]}, {"time": 0.4, "value": -11.72, "curve": [0.442, -11.72, 0.525, 16.88]}, {"time": 0.5667, "value": 16.88, "curve": [0.591, 16.88, 0.63, 6.84]}, {"time": 0.6667, "value": -1.2}]}, "CrabLeg7": {"rotate": [{"value": 8.23, "curve": [0.042, 8.23, 0.125, -17.21]}, {"time": 0.1667, "value": -17.21, "curve": [0.208, -17.21, 0.292, 8.23]}, {"time": 0.3333, "value": 8.23, "curve": [0.375, 8.23, 0.458, -17.21]}, {"time": 0.5, "value": -17.21, "curve": [0.542, -17.21, 0.625, 8.23]}, {"time": 0.6667, "value": 8.23}]}, "CrabLeg8": {"rotate": [{"value": -8.58, "curve": [0.025, -15.74, 0.049, -21.7]}, {"time": 0.0667, "value": -21.7, "curve": [0.108, -21.7, 0.192, 13.97]}, {"time": 0.2333, "value": 13.97, "curve": [0.275, 13.97, 0.358, -21.7]}, {"time": 0.4, "value": -21.7, "curve": [0.442, -21.7, 0.525, 13.97]}, {"time": 0.5667, "value": 13.97, "curve": [0.591, 13.97, 0.63, 1.44]}, {"time": 0.6667, "value": -8.58}]}, "CrabLeg5": {"rotate": [{"value": -1.13, "curve": [0.036, -8.28, 0.075, -17.21]}, {"time": 0.1, "value": -17.21, "curve": [0.142, -17.21, 0.225, 8.23]}, {"time": 0.2667, "value": 8.23, "curve": [0.308, 8.23, 0.392, -17.21]}, {"time": 0.4333, "value": -17.21, "curve": [0.475, -17.21, 0.558, 8.23]}, {"time": 0.6, "value": 8.23, "curve": [0.617, 8.23, 0.641, 3.97]}, {"time": 0.6667, "value": -1.13}]}, "CrabLeg6": {"rotate": [{"value": -21.7, "curve": [0.042, -21.7, 0.125, 13.97]}, {"time": 0.1667, "value": 13.97, "curve": [0.208, 13.97, 0.292, -21.7]}, {"time": 0.3333, "value": -21.7, "curve": [0.375, -21.7, 0.458, 13.97]}, {"time": 0.5, "value": 13.97, "curve": [0.525, 13.97, 0.564, 1.44]}, {"time": 0.6, "value": -8.58, "curve": [0.625, -15.74, 0.649, -21.7]}, {"time": 0.6667, "value": -21.7}]}}}, "anticipation-jumpattack": {"slots": {"Eyes": {"attachment": [{"name": "Patroller,Chaser/Chaser_images/Eyes_Shut"}]}, "EYE_LEFT_BTM": {"attachment": [{"name": "<PERSON>ler,<PERSON>r/Pouncer <PERSON>/Eye_Shut"}]}, "EYE_LEFT_TOP": {"attachment": [{"name": "<PERSON>ler,<PERSON>r/Pouncer <PERSON>/Eye_Shut"}]}, "EYE_RIGHT_BTM": {"attachment": [{"name": "<PERSON>ler,<PERSON>r/Pouncer <PERSON>/Eye_Shut"}]}, "EYE_RIGHT_TOP": {"attachment": [{"name": "<PERSON>ler,<PERSON>r/Pouncer <PERSON>/Eye_Shut"}]}, "Other/WarningIcon": {"rgba": [{"color": "ffffff00", "curve": [0.075, 1, 0.225, 1, 0.075, 1, 0.225, 1, 0.075, 1, 0.225, 1, 0.075, 0, 0.225, 1]}, {"time": 0.3, "color": "fffffffe"}], "attachment": [{"name": "Patroller,<PERSON>r/<PERSON><PERSON>cer <PERSON>/JumpAttack"}]}, "Patroller,Chaser/Pouncer Boss/Mouth": {"attachment": [{"name": "Mouth_shut"}]}}, "bones": {"FACE": {"translate": [{"curve": [0.017, 0, 0.042, 2.75, 0.017, 0, 0.042, 0]}, {"time": 0.0667, "x": 5.49, "curve": [0.092, 2.75, 0.117, 0, 0.092, 0, 0.117, 0]}, {"time": 0.1333, "curve": [0.15, 0, 0.175, 2.75, 0.15, 0, 0.175, 0]}, {"time": 0.2, "x": 5.49, "curve": [0.225, 2.75, 0.25, 0, 0.225, 0, 0.25, 0]}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": [0.35, 0, 0.375, 2.75, 0.35, 0, 0.375, 0]}, {"time": 0.4, "x": 5.49, "curve": [0.425, 2.75, 0.45, 0, 0.425, 0, 0.45, 0]}, {"time": 0.4667, "curve": [0.483, 0, 0.508, 2.75, 0.483, 0, 0.508, 0]}, {"time": 0.5333, "x": 5.49, "curve": [0.558, 2.75, 0.583, 0, 0.558, 0, 0.583, 0]}, {"time": 0.6, "curve": "stepped"}, {"time": 0.6667, "curve": [0.683, 0, 0.708, 2.75, 0.683, 0, 0.708, 0]}, {"time": 0.7333, "x": 5.49, "curve": [0.758, 2.75, 0.783, 0, 0.758, 0, 0.783, 0]}, {"time": 0.8, "curve": [0.817, 0, 0.842, 2.75, 0.817, 0, 0.842, 0]}, {"time": 0.8667, "x": 5.49, "curve": [0.892, 2.75, 0.917, 0, 0.892, 0, 0.917, 0]}, {"time": 0.9333, "curve": [0.95, 0, 0.975, 2.75, 0.95, 0, 0.975, 0]}, {"time": 1, "x": 5.49}], "scale": [{"curve": [0.042, 1, 0.125, 0.913, 0.042, 1, 0.125, 0.987]}, {"time": 0.1667, "x": 0.913, "y": 0.987, "curve": [0.208, 0.913, 0.292, 1, 0.208, 0.987, 0.292, 1]}, {"time": 0.3333, "curve": [0.375, 1, 0.458, 0.913, 0.375, 1, 0.458, 0.987]}, {"time": 0.5, "x": 0.913, "y": 0.987, "curve": [0.542, 0.913, 0.625, 1, 0.542, 0.987, 0.625, 1]}, {"time": 0.6667, "curve": [0.708, 1, 0.792, 0.913, 0.708, 1, 0.792, 0.987]}, {"time": 0.8333, "x": 0.913, "y": 0.987, "curve": [0.875, 0.913, 0.958, 1, 0.875, 0.987, 0.958, 1]}, {"time": 1}]}, "MOUTH": {"scale": [{"y": 1.152}]}, "Main": {"scale": [{"x": 1.23, "y": 0.816, "curve": [0.042, 1.23, 0.125, 1.331, 0.042, 0.816, 0.125, 0.778]}, {"time": 0.1667, "x": 1.331, "y": 0.778, "curve": [0.208, 1.331, 0.292, 1.23, 0.208, 0.778, 0.292, 0.816]}, {"time": 0.3333, "x": 1.23, "y": 0.816, "curve": [0.375, 1.23, 0.458, 1.331, 0.375, 0.816, 0.458, 0.778]}, {"time": 0.5, "x": 1.331, "y": 0.778, "curve": [0.542, 1.331, 0.625, 1.23, 0.542, 0.778, 0.625, 0.816]}, {"time": 0.6667, "x": 1.23, "y": 0.816, "curve": [0.708, 1.23, 0.792, 1.331, 0.708, 0.816, 0.792, 0.778]}, {"time": 0.8333, "x": 1.331, "y": 0.778, "curve": [0.875, 1.331, 0.958, 1.23, 0.875, 0.778, 0.958, 0.816]}, {"time": 1, "x": 1.23, "y": 0.816}]}, "BODY_1": {"scale": [{"x": 1.212, "y": 0.832, "curve": [0.042, 1.212, 0.125, 1.311, 0.042, 0.832, 0.125, 0.794]}, {"time": 0.1667, "x": 1.311, "y": 0.794, "curve": [0.208, 1.311, 0.292, 1.212, 0.208, 0.794, 0.292, 0.832]}, {"time": 0.3333, "x": 1.212, "y": 0.832, "curve": [0.375, 1.212, 0.458, 1.311, 0.375, 0.832, 0.458, 0.794]}, {"time": 0.5, "x": 1.311, "y": 0.794, "curve": [0.542, 1.311, 0.625, 1.212, 0.542, 0.794, 0.625, 0.832]}, {"time": 0.6667, "x": 1.212, "y": 0.832, "curve": [0.708, 1.212, 0.792, 1.311, 0.708, 0.832, 0.792, 0.794]}, {"time": 0.8333, "x": 1.311, "y": 0.794, "curve": [0.875, 1.311, 0.958, 1.212, 0.875, 0.794, 0.958, 0.832]}, {"time": 1, "x": 1.212, "y": 0.832}]}, "BODY_2": {"scale": [{"x": 1.146, "y": 0.891, "curve": [0.042, 1.146, 0.125, 1.239, 0.042, 0.891, 0.125, 0.85]}, {"time": 0.1667, "x": 1.239, "y": 0.85, "curve": [0.208, 1.239, 0.292, 1.146, 0.208, 0.85, 0.292, 0.891]}, {"time": 0.3333, "x": 1.146, "y": 0.891, "curve": [0.375, 1.146, 0.458, 1.239, 0.375, 0.891, 0.458, 0.85]}, {"time": 0.5, "x": 1.239, "y": 0.85, "curve": [0.542, 1.239, 0.625, 1.146, 0.542, 0.85, 0.625, 0.891]}, {"time": 0.6667, "x": 1.146, "y": 0.891, "curve": [0.708, 1.146, 0.792, 1.239, 0.708, 0.891, 0.792, 0.85]}, {"time": 0.8333, "x": 1.239, "y": 0.85, "curve": [0.875, 1.239, 0.958, 1.146, 0.875, 0.85, 0.958, 0.891]}, {"time": 1, "x": 1.146, "y": 0.891}]}, "BODY_3": {"scale": [{"x": 1.129, "y": 0.906, "curve": [0.042, 1.129, 0.125, 1.221, 0.042, 0.906, 0.125, 0.864]}, {"time": 0.1667, "x": 1.221, "y": 0.864, "curve": [0.208, 1.221, 0.292, 1.129, 0.208, 0.864, 0.292, 0.906]}, {"time": 0.3333, "x": 1.129, "y": 0.906, "curve": [0.375, 1.129, 0.458, 1.221, 0.375, 0.906, 0.458, 0.864]}, {"time": 0.5, "x": 1.221, "y": 0.864, "curve": [0.542, 1.221, 0.625, 1.129, 0.542, 0.864, 0.625, 0.906]}, {"time": 0.6667, "x": 1.129, "y": 0.906, "curve": [0.708, 1.129, 0.792, 1.221, 0.708, 0.906, 0.792, 0.864]}, {"time": 0.8333, "x": 1.221, "y": 0.864, "curve": [0.875, 1.221, 0.958, 1.129, 0.875, 0.864, 0.958, 0.906]}, {"time": 1, "x": 1.129, "y": 0.906}]}, "Twig2": {"rotate": [{"value": 4.09, "curve": [0.037, -1.03, 0.075, -6.8]}, {"time": 0.1, "value": -6.8, "curve": [0.142, -6.8, 0.225, 12.2]}, {"time": 0.2667, "value": 12.2, "curve": [0.284, 12.2, 0.308, 8.36]}, {"time": 0.3333, "value": 4.09, "curve": [0.37, -1.03, 0.409, -6.8]}, {"time": 0.4333, "value": -6.8, "curve": [0.475, -6.8, 0.558, 12.2]}, {"time": 0.6, "value": 12.2, "curve": [0.617, 12.2, 0.641, 8.36]}, {"time": 0.6667, "value": 4.09, "curve": [0.704, -1.03, 0.742, -6.8]}, {"time": 0.7667, "value": -6.8, "curve": [0.808, -6.8, 0.892, 12.2]}, {"time": 0.9333, "value": 12.2, "curve": [0.95, 12.2, 0.975, 8.36]}, {"time": 1, "value": 4.09}]}, "Twig3": {"rotate": [{"value": 0.15, "curve": [0.038, 3.32, 0.075, 6.49]}, {"time": 0.1, "value": 6.49, "curve": [0.142, 6.49, 0.225, -6.2]}, {"time": 0.2667, "value": -6.2, "curve": [0.283, -6.2, 0.308, -3.02]}, {"time": 0.3333, "value": 0.15, "curve": [0.371, 3.32, 0.408, 6.49]}, {"time": 0.4333, "value": 6.49, "curve": [0.475, 6.49, 0.558, -6.2]}, {"time": 0.6, "value": -6.2, "curve": [0.617, -6.2, 0.642, -3.02]}, {"time": 0.6667, "value": 0.15, "curve": [0.704, 3.32, 0.742, 6.49]}, {"time": 0.7667, "value": 6.49, "curve": [0.808, 6.49, 0.892, -6.2]}, {"time": 0.9333, "value": -6.2, "curve": [0.95, -6.2, 0.975, -3.02]}, {"time": 1, "value": 0.15}]}}}, "anticipation-shootspiral": {"slots": {"Eyes": {"attachment": [{"name": "Patroller,Chaser/Chaser_images/Eyes_Shut"}]}, "EYE_LEFT_BTM": {"attachment": [{"name": "<PERSON>ler,<PERSON>r/Pouncer <PERSON>/Eye_Shut"}]}, "EYE_LEFT_TOP": {"attachment": [{"name": "<PERSON>ler,<PERSON>r/Pouncer <PERSON>/Eye_Shut"}]}, "EYE_RIGHT_BTM": {"attachment": [{"name": "<PERSON>ler,<PERSON>r/Pouncer <PERSON>/Eye_Shut"}]}, "EYE_RIGHT_TOP": {"attachment": [{"name": "<PERSON>ler,<PERSON>r/Pouncer <PERSON>/Eye_Shut"}]}, "Other/WarningIcon": {"rgba": [{"color": "ffffff00", "curve": [0.075, 1, 0.225, 1, 0.075, 1, 0.225, 1, 0.075, 1, 0.225, 1, 0.075, 0, 0.225, 1]}, {"time": 0.3, "color": "fffffffe"}], "attachment": [{"name": "<PERSON>ler,<PERSON>r/<PERSON><PERSON>cer <PERSON>/<PERSON><PERSON><PERSON>"}]}, "Patroller,Chaser/Pouncer Boss/Mouth": {"attachment": [{"name": "Mouth_shut"}]}}, "bones": {"FACE": {"translate": [{"curve": [0.017, 0, 0.042, 2.75, 0.017, 0, 0.042, 0]}, {"time": 0.0667, "x": 5.49, "curve": [0.092, 2.75, 0.117, 0, 0.092, 0, 0.117, 0]}, {"time": 0.1333, "curve": [0.15, 0, 0.175, 2.75, 0.15, 0, 0.175, 0]}, {"time": 0.2, "x": 5.49, "curve": [0.225, 2.75, 0.25, 0, 0.225, 0, 0.25, 0]}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": [0.35, 0, 0.375, 2.75, 0.35, 0, 0.375, 0]}, {"time": 0.4, "x": 5.49, "curve": [0.425, 2.75, 0.45, 0, 0.425, 0, 0.45, 0]}, {"time": 0.4667, "curve": [0.483, 0, 0.508, 2.75, 0.483, 0, 0.508, 0]}, {"time": 0.5333, "x": 5.49, "curve": [0.558, 2.75, 0.583, 0, 0.558, 0, 0.583, 0]}, {"time": 0.6, "curve": "stepped"}, {"time": 0.6667, "curve": [0.683, 0, 0.708, 2.75, 0.683, 0, 0.708, 0]}, {"time": 0.7333, "x": 5.49, "curve": [0.758, 2.75, 0.783, 0, 0.758, 0, 0.783, 0]}, {"time": 0.8, "curve": [0.817, 0, 0.842, 2.75, 0.817, 0, 0.842, 0]}, {"time": 0.8667, "x": 5.49, "curve": [0.892, 2.75, 0.917, 0, 0.892, 0, 0.917, 0]}, {"time": 0.9333, "curve": [0.95, 0, 0.975, 2.75, 0.95, 0, 0.975, 0]}, {"time": 1, "x": 5.49}], "scale": [{"curve": [0.042, 1, 0.125, 0.913, 0.042, 1, 0.125, 0.987]}, {"time": 0.1667, "x": 0.913, "y": 0.987, "curve": [0.208, 0.913, 0.292, 1, 0.208, 0.987, 0.292, 1]}, {"time": 0.3333, "curve": [0.375, 1, 0.458, 0.913, 0.375, 1, 0.458, 0.987]}, {"time": 0.5, "x": 0.913, "y": 0.987, "curve": [0.542, 0.913, 0.625, 1, 0.542, 0.987, 0.625, 1]}, {"time": 0.6667, "curve": [0.708, 1, 0.792, 0.913, 0.708, 1, 0.792, 0.987]}, {"time": 0.8333, "x": 0.913, "y": 0.987, "curve": [0.875, 0.913, 0.958, 1, 0.875, 0.987, 0.958, 1]}, {"time": 1}]}, "MOUTH": {"scale": [{"y": 1.152}]}, "Main": {"scale": [{"x": 1.23, "y": 0.816, "curve": [0.042, 1.23, 0.125, 1.331, 0.042, 0.816, 0.125, 0.778]}, {"time": 0.1667, "x": 1.331, "y": 0.778, "curve": [0.208, 1.331, 0.292, 1.23, 0.208, 0.778, 0.292, 0.816]}, {"time": 0.3333, "x": 1.23, "y": 0.816, "curve": [0.375, 1.23, 0.458, 1.331, 0.375, 0.816, 0.458, 0.778]}, {"time": 0.5, "x": 1.331, "y": 0.778, "curve": [0.542, 1.331, 0.625, 1.23, 0.542, 0.778, 0.625, 0.816]}, {"time": 0.6667, "x": 1.23, "y": 0.816, "curve": [0.708, 1.23, 0.792, 1.331, 0.708, 0.816, 0.792, 0.778]}, {"time": 0.8333, "x": 1.331, "y": 0.778, "curve": [0.875, 1.331, 0.958, 1.23, 0.875, 0.778, 0.958, 0.816]}, {"time": 1, "x": 1.23, "y": 0.816}]}, "BODY_1": {"scale": [{"x": 1.212, "y": 0.832, "curve": [0.042, 1.212, 0.125, 1.311, 0.042, 0.832, 0.125, 0.794]}, {"time": 0.1667, "x": 1.311, "y": 0.794, "curve": [0.208, 1.311, 0.292, 1.212, 0.208, 0.794, 0.292, 0.832]}, {"time": 0.3333, "x": 1.212, "y": 0.832, "curve": [0.375, 1.212, 0.458, 1.311, 0.375, 0.832, 0.458, 0.794]}, {"time": 0.5, "x": 1.311, "y": 0.794, "curve": [0.542, 1.311, 0.625, 1.212, 0.542, 0.794, 0.625, 0.832]}, {"time": 0.6667, "x": 1.212, "y": 0.832, "curve": [0.708, 1.212, 0.792, 1.311, 0.708, 0.832, 0.792, 0.794]}, {"time": 0.8333, "x": 1.311, "y": 0.794, "curve": [0.875, 1.311, 0.958, 1.212, 0.875, 0.794, 0.958, 0.832]}, {"time": 1, "x": 1.212, "y": 0.832}]}, "BODY_2": {"scale": [{"x": 1.146, "y": 0.891, "curve": [0.042, 1.146, 0.125, 1.239, 0.042, 0.891, 0.125, 0.85]}, {"time": 0.1667, "x": 1.239, "y": 0.85, "curve": [0.208, 1.239, 0.292, 1.146, 0.208, 0.85, 0.292, 0.891]}, {"time": 0.3333, "x": 1.146, "y": 0.891, "curve": [0.375, 1.146, 0.458, 1.239, 0.375, 0.891, 0.458, 0.85]}, {"time": 0.5, "x": 1.239, "y": 0.85, "curve": [0.542, 1.239, 0.625, 1.146, 0.542, 0.85, 0.625, 0.891]}, {"time": 0.6667, "x": 1.146, "y": 0.891, "curve": [0.708, 1.146, 0.792, 1.239, 0.708, 0.891, 0.792, 0.85]}, {"time": 0.8333, "x": 1.239, "y": 0.85, "curve": [0.875, 1.239, 0.958, 1.146, 0.875, 0.85, 0.958, 0.891]}, {"time": 1, "x": 1.146, "y": 0.891}]}, "BODY_3": {"scale": [{"x": 1.129, "y": 0.906, "curve": [0.042, 1.129, 0.125, 1.221, 0.042, 0.906, 0.125, 0.864]}, {"time": 0.1667, "x": 1.221, "y": 0.864, "curve": [0.208, 1.221, 0.292, 1.129, 0.208, 0.864, 0.292, 0.906]}, {"time": 0.3333, "x": 1.129, "y": 0.906, "curve": [0.375, 1.129, 0.458, 1.221, 0.375, 0.906, 0.458, 0.864]}, {"time": 0.5, "x": 1.221, "y": 0.864, "curve": [0.542, 1.221, 0.625, 1.129, 0.542, 0.864, 0.625, 0.906]}, {"time": 0.6667, "x": 1.129, "y": 0.906, "curve": [0.708, 1.129, 0.792, 1.221, 0.708, 0.906, 0.792, 0.864]}, {"time": 0.8333, "x": 1.221, "y": 0.864, "curve": [0.875, 1.221, 0.958, 1.129, 0.875, 0.864, 0.958, 0.906]}, {"time": 1, "x": 1.129, "y": 0.906}]}, "Twig2": {"rotate": [{"value": 4.09, "curve": [0.037, -1.03, 0.075, -6.8]}, {"time": 0.1, "value": -6.8, "curve": [0.142, -6.8, 0.225, 12.2]}, {"time": 0.2667, "value": 12.2, "curve": [0.284, 12.2, 0.308, 8.36]}, {"time": 0.3333, "value": 4.09, "curve": [0.37, -1.03, 0.409, -6.8]}, {"time": 0.4333, "value": -6.8, "curve": [0.475, -6.8, 0.558, 12.2]}, {"time": 0.6, "value": 12.2, "curve": [0.617, 12.2, 0.641, 8.36]}, {"time": 0.6667, "value": 4.09, "curve": [0.704, -1.03, 0.742, -6.8]}, {"time": 0.7667, "value": -6.8, "curve": [0.808, -6.8, 0.892, 12.2]}, {"time": 0.9333, "value": 12.2, "curve": [0.95, 12.2, 0.975, 8.36]}, {"time": 1, "value": 4.09}]}, "Twig3": {"rotate": [{"value": 0.15, "curve": [0.038, 3.32, 0.075, 6.49]}, {"time": 0.1, "value": 6.49, "curve": [0.142, 6.49, 0.225, -6.2]}, {"time": 0.2667, "value": -6.2, "curve": [0.283, -6.2, 0.308, -3.02]}, {"time": 0.3333, "value": 0.15, "curve": [0.371, 3.32, 0.408, 6.49]}, {"time": 0.4333, "value": 6.49, "curve": [0.475, 6.49, 0.558, -6.2]}, {"time": 0.6, "value": -6.2, "curve": [0.617, -6.2, 0.642, -3.02]}, {"time": 0.6667, "value": 0.15, "curve": [0.704, 3.32, 0.742, 6.49]}, {"time": 0.7667, "value": 6.49, "curve": [0.808, 6.49, 0.892, -6.2]}, {"time": 0.9333, "value": -6.2, "curve": [0.95, -6.2, 0.975, -3.02]}, {"time": 1, "value": 0.15}]}}}, "anticipation-shootstraight": {"slots": {"Eyes": {"attachment": [{"name": "Patroller,Chaser/Chaser_images/Eyes_Shut"}]}, "EYE_LEFT_BTM": {"attachment": [{"name": "<PERSON>ler,<PERSON>r/Pouncer <PERSON>/Eye_Shut"}]}, "EYE_LEFT_TOP": {"attachment": [{"name": "<PERSON>ler,<PERSON>r/Pouncer <PERSON>/Eye_Shut"}]}, "EYE_RIGHT_BTM": {"attachment": [{"name": "<PERSON>ler,<PERSON>r/Pouncer <PERSON>/Eye_Shut"}]}, "EYE_RIGHT_TOP": {"attachment": [{"name": "<PERSON>ler,<PERSON>r/Pouncer <PERSON>/Eye_Shut"}]}, "Other/WarningIcon": {"rgba": [{"color": "ffffff00", "curve": [0.075, 1, 0.225, 1, 0.075, 1, 0.225, 1, 0.075, 1, 0.225, 1, 0.075, 0, 0.225, 1]}, {"time": 0.3, "color": "fffffffe"}], "attachment": [{"name": "Patroller,<PERSON>r/<PERSON><PERSON>cer <PERSON>/StraightAttack"}]}, "Patroller,Chaser/Pouncer Boss/Mouth": {"attachment": [{"name": "Mouth_shut"}]}}, "bones": {"FACE": {"translate": [{"curve": [0.017, 0, 0.042, 2.75, 0.017, 0, 0.042, 0]}, {"time": 0.0667, "x": 5.49, "curve": [0.092, 2.75, 0.117, 0, 0.092, 0, 0.117, 0]}, {"time": 0.1333, "curve": [0.15, 0, 0.175, 2.75, 0.15, 0, 0.175, 0]}, {"time": 0.2, "x": 5.49, "curve": [0.225, 2.75, 0.25, 0, 0.225, 0, 0.25, 0]}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": [0.35, 0, 0.375, 2.75, 0.35, 0, 0.375, 0]}, {"time": 0.4, "x": 5.49, "curve": [0.425, 2.75, 0.45, 0, 0.425, 0, 0.45, 0]}, {"time": 0.4667, "curve": [0.483, 0, 0.508, 2.75, 0.483, 0, 0.508, 0]}, {"time": 0.5333, "x": 5.49, "curve": [0.558, 2.75, 0.583, 0, 0.558, 0, 0.583, 0]}, {"time": 0.6, "curve": "stepped"}, {"time": 0.6667, "curve": [0.683, 0, 0.708, 2.75, 0.683, 0, 0.708, 0]}, {"time": 0.7333, "x": 5.49, "curve": [0.758, 2.75, 0.783, 0, 0.758, 0, 0.783, 0]}, {"time": 0.8, "curve": [0.817, 0, 0.842, 2.75, 0.817, 0, 0.842, 0]}, {"time": 0.8667, "x": 5.49, "curve": [0.892, 2.75, 0.917, 0, 0.892, 0, 0.917, 0]}, {"time": 0.9333, "curve": [0.95, 0, 0.975, 2.75, 0.95, 0, 0.975, 0]}, {"time": 1, "x": 5.49}], "scale": [{"curve": [0.042, 1, 0.125, 0.913, 0.042, 1, 0.125, 0.987]}, {"time": 0.1667, "x": 0.913, "y": 0.987, "curve": [0.208, 0.913, 0.292, 1, 0.208, 0.987, 0.292, 1]}, {"time": 0.3333, "curve": [0.375, 1, 0.458, 0.913, 0.375, 1, 0.458, 0.987]}, {"time": 0.5, "x": 0.913, "y": 0.987, "curve": [0.542, 0.913, 0.625, 1, 0.542, 0.987, 0.625, 1]}, {"time": 0.6667, "curve": [0.708, 1, 0.792, 0.913, 0.708, 1, 0.792, 0.987]}, {"time": 0.8333, "x": 0.913, "y": 0.987, "curve": [0.875, 0.913, 0.958, 1, 0.875, 0.987, 0.958, 1]}, {"time": 1}]}, "MOUTH": {"scale": [{"y": 1.152}]}, "Main": {"scale": [{"x": 1.23, "y": 0.816, "curve": [0.042, 1.23, 0.125, 1.331, 0.042, 0.816, 0.125, 0.778]}, {"time": 0.1667, "x": 1.331, "y": 0.778, "curve": [0.208, 1.331, 0.292, 1.23, 0.208, 0.778, 0.292, 0.816]}, {"time": 0.3333, "x": 1.23, "y": 0.816, "curve": [0.375, 1.23, 0.458, 1.331, 0.375, 0.816, 0.458, 0.778]}, {"time": 0.5, "x": 1.331, "y": 0.778, "curve": [0.542, 1.331, 0.625, 1.23, 0.542, 0.778, 0.625, 0.816]}, {"time": 0.6667, "x": 1.23, "y": 0.816, "curve": [0.708, 1.23, 0.792, 1.331, 0.708, 0.816, 0.792, 0.778]}, {"time": 0.8333, "x": 1.331, "y": 0.778, "curve": [0.875, 1.331, 0.958, 1.23, 0.875, 0.778, 0.958, 0.816]}, {"time": 1, "x": 1.23, "y": 0.816}]}, "BODY_1": {"scale": [{"x": 1.212, "y": 0.832, "curve": [0.042, 1.212, 0.125, 1.311, 0.042, 0.832, 0.125, 0.794]}, {"time": 0.1667, "x": 1.311, "y": 0.794, "curve": [0.208, 1.311, 0.292, 1.212, 0.208, 0.794, 0.292, 0.832]}, {"time": 0.3333, "x": 1.212, "y": 0.832, "curve": [0.375, 1.212, 0.458, 1.311, 0.375, 0.832, 0.458, 0.794]}, {"time": 0.5, "x": 1.311, "y": 0.794, "curve": [0.542, 1.311, 0.625, 1.212, 0.542, 0.794, 0.625, 0.832]}, {"time": 0.6667, "x": 1.212, "y": 0.832, "curve": [0.708, 1.212, 0.792, 1.311, 0.708, 0.832, 0.792, 0.794]}, {"time": 0.8333, "x": 1.311, "y": 0.794, "curve": [0.875, 1.311, 0.958, 1.212, 0.875, 0.794, 0.958, 0.832]}, {"time": 1, "x": 1.212, "y": 0.832}]}, "BODY_2": {"scale": [{"x": 1.146, "y": 0.891, "curve": [0.042, 1.146, 0.125, 1.239, 0.042, 0.891, 0.125, 0.85]}, {"time": 0.1667, "x": 1.239, "y": 0.85, "curve": [0.208, 1.239, 0.292, 1.146, 0.208, 0.85, 0.292, 0.891]}, {"time": 0.3333, "x": 1.146, "y": 0.891, "curve": [0.375, 1.146, 0.458, 1.239, 0.375, 0.891, 0.458, 0.85]}, {"time": 0.5, "x": 1.239, "y": 0.85, "curve": [0.542, 1.239, 0.625, 1.146, 0.542, 0.85, 0.625, 0.891]}, {"time": 0.6667, "x": 1.146, "y": 0.891, "curve": [0.708, 1.146, 0.792, 1.239, 0.708, 0.891, 0.792, 0.85]}, {"time": 0.8333, "x": 1.239, "y": 0.85, "curve": [0.875, 1.239, 0.958, 1.146, 0.875, 0.85, 0.958, 0.891]}, {"time": 1, "x": 1.146, "y": 0.891}]}, "BODY_3": {"scale": [{"x": 1.129, "y": 0.906, "curve": [0.042, 1.129, 0.125, 1.221, 0.042, 0.906, 0.125, 0.864]}, {"time": 0.1667, "x": 1.221, "y": 0.864, "curve": [0.208, 1.221, 0.292, 1.129, 0.208, 0.864, 0.292, 0.906]}, {"time": 0.3333, "x": 1.129, "y": 0.906, "curve": [0.375, 1.129, 0.458, 1.221, 0.375, 0.906, 0.458, 0.864]}, {"time": 0.5, "x": 1.221, "y": 0.864, "curve": [0.542, 1.221, 0.625, 1.129, 0.542, 0.864, 0.625, 0.906]}, {"time": 0.6667, "x": 1.129, "y": 0.906, "curve": [0.708, 1.129, 0.792, 1.221, 0.708, 0.906, 0.792, 0.864]}, {"time": 0.8333, "x": 1.221, "y": 0.864, "curve": [0.875, 1.221, 0.958, 1.129, 0.875, 0.864, 0.958, 0.906]}, {"time": 1, "x": 1.129, "y": 0.906}]}, "Twig2": {"rotate": [{"value": 4.09, "curve": [0.037, -1.03, 0.075, -6.8]}, {"time": 0.1, "value": -6.8, "curve": [0.142, -6.8, 0.225, 12.2]}, {"time": 0.2667, "value": 12.2, "curve": [0.284, 12.2, 0.308, 8.36]}, {"time": 0.3333, "value": 4.09, "curve": [0.37, -1.03, 0.409, -6.8]}, {"time": 0.4333, "value": -6.8, "curve": [0.475, -6.8, 0.558, 12.2]}, {"time": 0.6, "value": 12.2, "curve": [0.617, 12.2, 0.641, 8.36]}, {"time": 0.6667, "value": 4.09, "curve": [0.704, -1.03, 0.742, -6.8]}, {"time": 0.7667, "value": -6.8, "curve": [0.808, -6.8, 0.892, 12.2]}, {"time": 0.9333, "value": 12.2, "curve": [0.95, 12.2, 0.975, 8.36]}, {"time": 1, "value": 4.09}]}, "Twig3": {"rotate": [{"value": 0.15, "curve": [0.038, 3.32, 0.075, 6.49]}, {"time": 0.1, "value": 6.49, "curve": [0.142, 6.49, 0.225, -6.2]}, {"time": 0.2667, "value": -6.2, "curve": [0.283, -6.2, 0.308, -3.02]}, {"time": 0.3333, "value": 0.15, "curve": [0.371, 3.32, 0.408, 6.49]}, {"time": 0.4333, "value": 6.49, "curve": [0.475, 6.49, 0.558, -6.2]}, {"time": 0.6, "value": -6.2, "curve": [0.617, -6.2, 0.642, -3.02]}, {"time": 0.6667, "value": 0.15, "curve": [0.704, 3.32, 0.742, 6.49]}, {"time": 0.7667, "value": 6.49, "curve": [0.808, 6.49, 0.892, -6.2]}, {"time": 0.9333, "value": -6.2, "curve": [0.95, -6.2, 0.975, -3.02]}, {"time": 1, "value": 0.15}]}}}, "attack-charge": {"slots": {"Eyes": {"attachment": [{"name": "Patroller,Chaser/Chaser_images/Eyes_Shut"}]}, "EYE_LEFT_BTM": {"attachment": [{"name": "<PERSON>ler,<PERSON>r/Pouncer <PERSON>/Eye_Shut"}]}, "EYE_LEFT_TOP": {"attachment": [{"name": "<PERSON>ler,<PERSON>r/Pouncer <PERSON>/Eye_Shut"}]}, "EYE_RIGHT_BTM": {"attachment": [{"name": "<PERSON>ler,<PERSON>r/Pouncer <PERSON>/Eye_Shut"}]}, "EYE_RIGHT_TOP": {"attachment": [{"name": "<PERSON>ler,<PERSON>r/Pouncer <PERSON>/Eye_Shut"}]}, "MouthBtm": {"attachment": [{"name": "Mouth_shut"}]}, "Patroller,Chaser/Pouncer Boss/Mouth": {"attachment": [{"name": "Mouth_shut"}]}, "Patroller,Chaser/Pouncer Boss/Mouth2": {"attachment": [{"name": "Mouth_shut"}]}, "TurretMouth": {"attachment": [{"name": "Patroller,Chaser/Chaser Boss/Tunneler_Mouth_Closed"}]}}, "bones": {"FACE": {"translate": [{"curve": [0.017, 0, 0.042, 2.75, 0.017, 0, 0.042, 0]}, {"time": 0.0667, "x": 5.49, "curve": [0.092, 2.75, 0.117, 0, 0.092, 0, 0.117, 0]}, {"time": 0.1333, "curve": [0.15, 0, 0.175, 2.75, 0.15, 0, 0.175, 0]}, {"time": 0.2, "x": 5.49, "curve": [0.225, 2.75, 0.25, 0, 0.225, 0, 0.25, 0]}, {"time": 0.2667, "curve": [0.283, 0, 0.308, 2.75, 0.283, 0, 0.308, 0]}, {"time": 0.3333, "x": 5.49}], "scale": [{"curve": [0.042, 1, 0.125, 0.913, 0.042, 1, 0.125, 0.987]}, {"time": 0.1667, "x": 0.913, "y": 0.987, "curve": [0.208, 0.913, 0.292, 1, 0.208, 0.987, 0.292, 1]}, {"time": 0.3333}]}, "MOUTH_CHASER": {"scale": [{"y": 1.152}]}, "MOUTH": {"scale": [{"y": 1.152}]}, "Main": {"scale": [{"x": 1.23, "y": 0.816, "curve": [0.042, 1.23, 0.125, 1.331, 0.042, 0.816, 0.125, 0.778]}, {"time": 0.1667, "x": 1.331, "y": 0.778, "curve": [0.208, 1.331, 0.292, 1.23, 0.208, 0.778, 0.292, 0.816]}, {"time": 0.3333, "x": 1.23, "y": 0.816}]}, "BODY_1": {"scale": [{"x": 1.212, "y": 0.832, "curve": [0.042, 1.212, 0.125, 1.311, 0.042, 0.832, 0.125, 0.794]}, {"time": 0.1667, "x": 1.311, "y": 0.794, "curve": [0.208, 1.311, 0.292, 1.212, 0.208, 0.794, 0.292, 0.832]}, {"time": 0.3333, "x": 1.212, "y": 0.832}]}, "BODY_2": {"scale": [{"x": 1.146, "y": 0.891, "curve": [0.042, 1.146, 0.125, 1.239, 0.042, 0.891, 0.125, 0.85]}, {"time": 0.1667, "x": 1.239, "y": 0.85, "curve": [0.208, 1.239, 0.292, 1.146, 0.208, 0.85, 0.292, 0.891]}, {"time": 0.3333, "x": 1.146, "y": 0.891}]}, "BODY_3": {"scale": [{"x": 1.129, "y": 0.906, "curve": [0.042, 1.129, 0.125, 1.221, 0.042, 0.906, 0.125, 0.864]}, {"time": 0.1667, "x": 1.221, "y": 0.864, "curve": [0.208, 1.221, 0.292, 1.129, 0.208, 0.864, 0.292, 0.906]}, {"time": 0.3333, "x": 1.129, "y": 0.906}]}, "Twig2": {"rotate": [{"value": 4.09, "curve": [0.037, -1.03, 0.075, -6.8]}, {"time": 0.1, "value": -6.8, "curve": [0.142, -6.8, 0.225, 12.2]}, {"time": 0.2667, "value": 12.2, "curve": [0.284, 12.2, 0.308, 8.36]}, {"time": 0.3333, "value": 4.09}]}, "Twig3": {"rotate": [{"value": 0.15, "curve": [0.038, 3.32, 0.075, 6.49]}, {"time": 0.1, "value": 6.49, "curve": [0.142, 6.49, 0.225, -6.2]}, {"time": 0.2667, "value": -6.2, "curve": [0.283, -6.2, 0.308, -3.02]}, {"time": 0.3333, "value": 0.15}]}, "MOUTH_BTM": {"translate": [{"y": 35.33}]}, "FleshTurret": {"translate": [{"curve": [0.017, 0, 0.05, 4.8, 0.017, 0, 0.05, 0]}, {"time": 0.0667, "x": 4.8, "curve": [0.083, 4.8, 0.117, 0, 0.083, 0, 0.117, 0]}, {"time": 0.1333, "curve": [0.15, 0, 0.183, 4.8, 0.15, 0, 0.183, 0]}, {"time": 0.2, "x": 4.8, "curve": [0.217, 4.8, 0.25, 0, 0.217, 0, 0.25, 0]}, {"time": 0.2667}, {"time": 0.3333, "x": 4.8}], "scale": [{"curve": [0.025, 1, 0.075, 0.809, 0.025, 1, 0.075, 1.167]}, {"time": 0.1, "x": 0.809, "y": 1.167, "curve": [0.125, 0.809, 0.175, 1, 0.125, 1.167, 0.175, 1]}, {"time": 0.2, "curve": [0.217, 1, 0.25, 0.809, 0.217, 1, 0.25, 1.167]}, {"time": 0.2667, "x": 0.809, "y": 1.167, "curve": [0.283, 0.809, 0.317, 1, 0.283, 1.167, 0.317, 1]}, {"time": 0.3333}]}, "HeadFleshball_1": {"scale": [{"curve": [0.025, 1, 0.075, 1, 0.025, 1, 0.075, 0.872]}, {"time": 0.1, "y": 0.872, "curve": [0.117, 1, 0.15, 1, 0.117, 0.872, 0.15, 1]}, {"time": 0.1667, "curve": [0.192, 1, 0.242, 1, 0.192, 1, 0.242, 0.872]}, {"time": 0.2667, "y": 0.872, "curve": [0.283, 1, 0.317, 1, 0.283, 0.872, 0.317, 1]}, {"time": 0.3333}]}, "HeadFleshball_2": {"scale": [{"y": 0.919, "curve": [0.024, 1, 0.05, 1, 0.024, 0.955, 0.05, 1]}, {"time": 0.0667, "curve": [0.083, 1, 0.117, 1, 0.083, 1, 0.117, 0.872]}, {"time": 0.1333, "y": 0.872, "curve": [0.142, 1, 0.154, 1, 0.142, 0.872, 0.154, 0.894]}, {"time": 0.1667, "y": 0.919, "curve": [0.191, 1, 0.217, 1, 0.191, 0.955, 0.217, 1]}, {"time": 0.2333, "curve": [0.25, 1, 0.283, 1, 0.25, 1, 0.283, 0.872]}, {"time": 0.3, "y": 0.872, "curve": [0.309, 1, 0.321, 1, 0.309, 0.872, 0.321, 0.894]}, {"time": 0.3333, "y": 0.919}]}, "HeadFleshball_3": {"scale": [{"y": 0.889, "curve": [0.012, 1, 0.024, 1, 0.012, 0.879, 0.024, 0.872]}, {"time": 0.0333, "y": 0.872, "curve": [0.05, 1, 0.083, 1, 0.05, 0.872, 0.083, 1]}, {"time": 0.1, "curve": [0.116, 1, 0.145, 1, 0.116, 1, 0.145, 0.921]}, {"time": 0.1667, "y": 0.889, "curve": [0.179, 1, 0.191, 1, 0.179, 0.879, 0.191, 0.872]}, {"time": 0.2, "y": 0.872, "curve": [0.217, 1, 0.25, 1, 0.217, 0.872, 0.25, 1]}, {"time": 0.2667, "curve": [0.283, 1, 0.312, 1, 0.283, 1, 0.312, 0.921]}, {"time": 0.3333, "y": 0.889}]}, "Fleshball_7": {"scale": [{"x": 0.938, "y": 0.938, "curve": [0.012, 0.919, 0.024, 0.908, 0.012, 0.919, 0.024, 0.908]}, {"time": 0.0333, "x": 0.908, "y": 0.908, "curve": [0.075, 0.908, 0.158, 1.141, 0.075, 0.908, 0.158, 1.141]}, {"time": 0.2, "x": 1.141, "y": 1.141, "curve": [0.232, 1.141, 0.291, 0.996, 0.232, 1.141, 0.291, 0.996]}, {"time": 0.3333, "x": 0.938, "y": 0.938}]}, "Fleshball_6": {"scale": [{"x": 1.11, "y": 1.11, "curve": [0.043, 1.052, 0.101, 0.908, 0.043, 1.052, 0.101, 0.908]}, {"time": 0.1333, "x": 0.908, "y": 0.908, "curve": [0.175, 0.908, 0.258, 1.141, 0.175, 0.908, 0.258, 1.141]}, {"time": 0.3, "x": 1.141, "y": 1.141, "curve": [0.309, 1.141, 0.321, 1.129, 0.309, 1.141, 0.321, 1.129]}, {"time": 0.3333, "x": 1.11, "y": 1.11}]}, "Fleshball_8": {"scale": [{"x": 0.993, "y": 0.993, "curve": [0.036, 1.059, 0.075, 1.141, 0.036, 1.059, 0.075, 1.141]}, {"time": 0.1, "x": 1.141, "y": 1.141, "curve": [0.142, 1.141, 0.225, 0.908, 0.142, 1.141, 0.225, 0.908]}, {"time": 0.2667, "x": 0.908, "y": 0.908, "curve": [0.284, 0.908, 0.308, 0.946, 0.284, 0.908, 0.308, 0.946]}, {"time": 0.3333, "x": 0.993, "y": 0.993}]}, "Fleshball_5": {"scale": [{"x": 1.059, "y": 1.059, "curve": [0.012, 1.07, 0.024, 1.076, 0.012, 1.07, 0.024, 1.076]}, {"time": 0.0333, "x": 1.076, "y": 1.076, "curve": [0.075, 1.076, 0.158, 0.944, 0.075, 1.076, 0.158, 0.944]}, {"time": 0.2, "x": 0.944, "y": 0.944, "curve": [0.232, 0.944, 0.291, 1.026, 0.232, 0.944, 0.291, 1.026]}, {"time": 0.3333, "x": 1.059, "y": 1.059}]}, "Fleshball_4": {"scale": [{"x": 1.044, "y": 1.044, "curve": [0.034, 1.006, 0.076, 0.944, 0.034, 1.006, 0.076, 0.944]}, {"time": 0.1, "x": 0.944, "y": 0.944, "curve": [0.142, 0.944, 0.225, 1.076, 0.142, 0.944, 0.225, 1.076]}, {"time": 0.2667, "x": 1.076, "y": 1.076, "curve": [0.285, 1.076, 0.308, 1.063, 0.285, 1.076, 0.308, 1.063]}, {"time": 0.3333, "x": 1.044, "y": 1.044}]}, "Fleshball_3": {"scale": [{"x": 0.992, "y": 0.992, "curve": [0.025, 0.966, 0.049, 0.944, 0.025, 0.966, 0.049, 0.944]}, {"time": 0.0667, "x": 0.944, "y": 0.944, "curve": [0.108, 0.944, 0.192, 1.076, 0.108, 0.944, 0.192, 1.076]}, {"time": 0.2333, "x": 1.076, "y": 1.076, "curve": [0.258, 1.076, 0.297, 1.03, 0.258, 1.076, 0.297, 1.03]}, {"time": 0.3333, "x": 0.992, "y": 0.992}]}, "Fleshball_1": {"scale": [{"x": 1.044, "y": 1.044, "curve": [0.034, 1.006, 0.076, 0.944, 0.034, 1.006, 0.076, 0.944]}, {"time": 0.1, "x": 0.944, "y": 0.944, "curve": [0.142, 0.944, 0.225, 1.076, 0.142, 0.944, 0.225, 1.076]}, {"time": 0.2667, "x": 1.076, "y": 1.076, "curve": [0.285, 1.076, 0.308, 1.063, 0.285, 1.076, 0.308, 1.063]}, {"time": 0.3333, "x": 1.044, "y": 1.044}]}, "Fleshball_2": {"scale": [{"x": 0.992, "y": 0.992, "curve": [0.025, 0.966, 0.049, 0.944, 0.025, 0.966, 0.049, 0.944]}, {"time": 0.0667, "x": 0.944, "y": 0.944, "curve": [0.108, 0.944, 0.192, 1.076, 0.108, 0.944, 0.192, 1.076]}, {"time": 0.2333, "x": 1.076, "y": 1.076, "curve": [0.258, 1.076, 0.297, 1.03, 0.258, 1.076, 0.297, 1.03]}, {"time": 0.3333, "x": 0.992, "y": 0.992}]}, "Fleshball_9": {"scale": [{"x": 0.999, "y": 0.999}, {"time": 0.0667, "x": 0.897, "y": 0.897}, {"time": 0.2333, "x": 1.101, "y": 1.101}, {"time": 0.3333, "x": 0.999, "y": 0.999}]}, "HeadFleshball_1_Behind": {"scale": [{"curve": [0.025, 1, 0.075, 1, 0.025, 1, 0.075, 0.872]}, {"time": 0.1, "y": 0.872, "curve": [0.117, 1, 0.15, 1, 0.117, 0.872, 0.15, 1]}, {"time": 0.1667, "curve": [0.192, 1, 0.242, 1, 0.192, 1, 0.242, 0.872]}, {"time": 0.2667, "y": 0.872, "curve": [0.283, 1, 0.317, 1, 0.283, 0.872, 0.317, 1]}, {"time": 0.3333}]}, "HeadFleshball_3_Behind": {"scale": [{"y": 0.889, "curve": [0.012, 1, 0.024, 1, 0.012, 0.879, 0.024, 0.872]}, {"time": 0.0333, "y": 0.872, "curve": [0.05, 1, 0.083, 1, 0.05, 0.872, 0.083, 1]}, {"time": 0.1, "curve": [0.116, 1, 0.145, 1, 0.116, 1, 0.145, 0.921]}, {"time": 0.1667, "y": 0.889, "curve": [0.179, 1, 0.191, 1, 0.179, 0.879, 0.191, 0.872]}, {"time": 0.2, "y": 0.872, "curve": [0.217, 1, 0.25, 1, 0.217, 0.872, 0.25, 1]}, {"time": 0.2667, "curve": [0.283, 1, 0.312, 1, 0.283, 1, 0.312, 0.921]}, {"time": 0.3333, "y": 0.889}]}}}, "attack-impact": {"slots": {"Eyes": {"attachment": [{"name": "<PERSON>ler,Chaser/Chaser_images/Eyes_Shocked"}, {"time": 0.5, "name": "Patroller,Chaser/Chaser_images/Eyes"}]}, "EYE_LEFT_BTM": {"attachment": [{"name": "<PERSON>ler,<PERSON><PERSON>/<PERSON>uncer <PERSON>/Eye_Shocked"}, {"time": 0.5, "name": "<PERSON>ler,<PERSON>r/Pouncer Boss/Eye_Normal"}]}, "EYE_LEFT_TOP": {"attachment": [{"name": "<PERSON>ler,<PERSON><PERSON>/<PERSON>uncer <PERSON>/Eye_Shocked"}, {"time": 0.5, "name": "<PERSON>ler,<PERSON>r/Pouncer Boss/Eye_Normal"}]}, "EYE_RIGHT_BTM": {"attachment": [{"name": "<PERSON>ler,<PERSON><PERSON>/<PERSON>uncer <PERSON>/Eye_Shocked"}, {"time": 0.5, "name": "<PERSON>ler,<PERSON>r/Pouncer Boss/Eye_Normal"}]}, "EYE_RIGHT_TOP": {"attachment": [{"name": "<PERSON>ler,<PERSON><PERSON>/<PERSON>uncer <PERSON>/Eye_Shocked"}, {"time": 0.5, "name": "<PERSON>ler,<PERSON>r/Pouncer Boss/Eye_Normal"}]}, "MouthBtm": {"attachment": [{"name": "Mouth_shut"}, {"time": 0.1, "name": "Mouth"}]}, "Patroller,Chaser/Pouncer Boss/Mouth": {"attachment": [{"name": "Mouth_shut"}, {"time": 0.1, "name": "Mouth"}]}, "Patroller,Chaser/Pouncer Boss/Mouth2": {"attachment": [{"name": "Mouth_shut"}, {"time": 0.1, "name": "Mouth"}]}, "TurretMouth": {"attachment": [{"name": "Patroller,Chaser/Chaser Boss/Tunneler_Mouth_Closed"}, {"time": 0.1, "name": "Patroller,Chaser/Chaser Boss/Tunneler_Mouth_Wide"}, {"time": 0.5, "name": "Patroller,Chaser/Chaser Boss/Tunneler_Mouth"}]}}, "bones": {"FACE": {"scale": [{"curve": [0.083, 1, 0.25, 0.913, 0.083, 1, 0.25, 0.987]}, {"time": 0.3333, "x": 0.913, "y": 0.987, "curve": [0.417, 0.913, 0.583, 1, 0.417, 0.987, 0.583, 1]}, {"time": 0.6667}]}, "MOUTH_CHASER": {"scale": [{"y": 1.152}, {"time": 0.1, "y": 0.51, "curve": [0.1, 1, 0.197, 1, 0.1, 0.956, 0.197, 1.287]}, {"time": 0.3333, "y": 1.287, "curve": [0.375, 1, 0.458, 1, 0.375, 1.287, 0.458, 0.78]}, {"time": 0.5, "y": 0.78, "curve": [0.558, 1, 0.675, 1, 0.558, 0.78, 0.675, 0.955]}, {"time": 0.7333, "y": 0.955}]}, "MOUTH": {"scale": [{"y": 1.152}, {"time": 0.1, "y": 0.51, "curve": [0.1, 1, 0.197, 1, 0.1, 0.956, 0.197, 1.287]}, {"time": 0.3333, "y": 1.287, "curve": [0.375, 1, 0.458, 1, 0.375, 1.287, 0.458, 0.78]}, {"time": 0.5, "y": 0.78, "curve": [0.558, 1, 0.675, 1, 0.558, 0.78, 0.675, 0.955]}, {"time": 0.7333, "y": 0.955}]}, "Main": {"scale": [{"x": 1.05, "y": 0.959, "curve": [0.008, 1.05, 0.025, 1.222, 0.008, 0.959, 0.025, 0.678]}, {"time": 0.0333, "x": 1.222, "y": 0.678, "curve": [0.058, 1.222, 0.108, 0.871, 0.058, 0.678, 0.108, 1.145]}, {"time": 0.1333, "x": 0.871, "y": 1.145, "curve": [0.175, 0.871, 0.258, 1.253, 0.175, 1.145, 0.258, 0.78]}, {"time": 0.3, "x": 1.253, "y": 0.78, "curve": [0.358, 1.253, 0.475, 0.96, 0.358, 0.78, 0.475, 1.054]}, {"time": 0.5333, "x": 0.96, "y": 1.054, "curve": [0.592, 0.96, 0.708, 1.05, 0.592, 1.054, 0.708, 0.959]}, {"time": 0.7667, "x": 1.05, "y": 0.959}]}, "BODY_1": {"scale": [{"x": 1.034, "y": 0.978, "curve": [0.017, 1.034, 0.05, 1.434, 0.017, 0.978, 0.05, 0.638]}, {"time": 0.0667, "x": 1.434, "y": 0.638, "curve": [0.092, 1.434, 0.142, 0.858, 0.092, 0.638, 0.142, 1.56]}, {"time": 0.1667, "x": 0.858, "y": 1.56, "curve": [0.208, 0.858, 0.292, 1.234, 0.208, 1.56, 0.292, 0.795]}, {"time": 0.3333, "x": 1.234, "y": 0.795, "curve": [0.392, 1.234, 0.508, 0.946, 0.392, 0.795, 0.508, 1.075]}, {"time": 0.5667, "x": 0.946, "y": 1.075, "curve": [0.617, 0.946, 0.717, 1.034, 0.617, 1.075, 0.717, 0.978]}, {"time": 0.7667, "x": 1.034, "y": 0.978}]}, "BODY_2": {"scale": [{"x": 0.977, "y": 1.047, "curve": [0.025, 0.977, 0.075, 1.356, 0.025, 1.047, 0.075, 0.684]}, {"time": 0.1, "x": 1.356, "y": 0.684, "curve": [0.125, 1.356, 0.175, 0.811, 0.125, 0.684, 0.175, 1.671]}, {"time": 0.2, "x": 0.811, "y": 1.671, "curve": [0.242, 0.811, 0.325, 1.166, 0.242, 1.671, 0.325, 0.852]}, {"time": 0.3667, "x": 1.166, "y": 0.852, "curve": [0.425, 1.166, 0.542, 0.894, 0.425, 0.852, 0.542, 1.152]}, {"time": 0.6, "x": 0.894, "y": 1.152, "curve": [0.642, 0.894, 0.725, 0.977, 0.642, 1.152, 0.725, 1.047]}, {"time": 0.7667, "x": 0.977, "y": 1.047}]}, "BODY_3": {"scale": [{"x": 0.963, "y": 1.065, "curve": [0.033, 0.963, 0.1, 1.336, 0.033, 1.065, 0.1, 0.695]}, {"time": 0.1333, "x": 1.336, "y": 0.695, "curve": [0.158, 1.336, 0.208, 0.799, 0.158, 0.695, 0.208, 1.699]}, {"time": 0.2333, "x": 0.799, "y": 1.699, "curve": [0.275, 0.799, 0.358, 1.149, 0.275, 1.699, 0.358, 0.866]}, {"time": 0.4, "x": 1.149, "y": 0.866, "curve": [0.458, 1.149, 0.575, 0.881, 0.458, 0.866, 0.575, 1.171]}, {"time": 0.6333, "x": 0.881, "y": 1.171, "curve": [0.667, 0.881, 0.733, 0.963, 0.667, 1.171, 0.733, 1.065]}, {"time": 0.7667, "x": 0.963, "y": 1.065}]}, "Twig2": {"rotate": [{"value": 4.09, "curve": [0.061, -1.03, 0.126, -6.8]}, {"time": 0.1667, "value": -6.8, "curve": [0.258, -6.8, 0.442, 12.2]}, {"time": 0.5333, "value": 12.2, "curve": [0.567, 12.2, 0.616, 8.36]}, {"time": 0.6667, "value": 4.09}]}, "Twig3": {"rotate": [{"value": 0.15, "curve": [0.063, 3.32, 0.125, 6.49]}, {"time": 0.1667, "value": 6.49, "curve": [0.25, 6.49, 0.417, -6.2]}, {"time": 0.5, "value": -6.2, "curve": [0.542, -6.2, 0.604, -3.02]}, {"time": 0.6667, "value": 0.15}]}, "Antler_1": {"rotate": [{"curve": [0.017, 0, 0.05, -28.81]}, {"time": 0.0667, "value": -28.81, "curve": [0.092, -28.81, 0.142, 18.63]}, {"time": 0.1667, "value": 18.63, "curve": [0.217, 18.63, 0.317, -14.31]}, {"time": 0.3667, "value": -14.31, "curve": [0.417, -14.31, 0.517, 6.43]}, {"time": 0.5667, "value": 6.43, "curve": [0.617, 6.43, 0.717, 0]}, {"time": 0.7667}]}, "Antler_4": {"rotate": [{"curve": [0.017, 0, 0.05, 25.06]}, {"time": 0.0667, "value": 25.06, "curve": [0.092, 25.06, 0.142, -16.43]}, {"time": 0.1667, "value": -16.43, "curve": [0.217, -16.43, 0.317, 6.76]}, {"time": 0.3667, "value": 6.76, "curve": [0.417, 6.76, 0.517, -5.33]}, {"time": 0.5667, "value": -5.33, "curve": [0.617, -5.33, 0.717, 0]}, {"time": 0.7667}]}, "Antler_3": {"rotate": [{"curve": [0.017, 0, 0.05, 58.17]}, {"time": 0.0667, "value": 58.17, "curve": [0.092, 58.17, 0.142, -22.19]}, {"time": 0.1667, "value": -22.19, "curve": [0.217, -22.19, 0.317, 13.8]}, {"time": 0.3667, "value": 13.8, "curve": [0.417, 13.8, 0.517, -7.7]}, {"time": 0.5667, "value": -7.7, "curve": [0.617, -7.7, 0.717, 0]}, {"time": 0.7667}]}, "Antler_0": {"rotate": [{"curve": [0.017, 0, 0.05, -53.81]}, {"time": 0.0667, "value": -53.81, "curve": [0.092, -53.81, 0.142, 31.58]}, {"time": 0.1667, "value": 31.58, "curve": [0.217, 31.58, 0.317, -10.48]}, {"time": 0.3667, "value": -10.48, "curve": [0.417, -10.48, 0.517, 10.12]}, {"time": 0.5667, "value": 10.12, "curve": [0.617, 10.12, 0.717, 0]}, {"time": 0.7667}]}, "MOUTH_BTM": {"translate": [{"curve": [0.042, 0, 0.125, 2.78, 0.042, 0, 0.125, -25.2]}, {"time": 0.1667, "x": 2.78, "y": -25.2, "curve": [0.2, 2.78, 0.267, -0.79, 0.2, -25.2, 0.267, 64.88]}, {"time": 0.3, "x": -0.79, "y": 64.88, "curve": [0.35, -0.79, 0.45, 0, 0.35, 64.88, 0.45, -20.98]}, {"time": 0.5, "y": -20.98, "curve": [0.567, 0, 0.7, 0, 0.567, -20.98, 0.7, 0]}, {"time": 0.7667}]}, "FleshTurret": {"scale": [{"x": 0.593, "y": 1.251, "curve": [0.042, 0.593, 0.125, 1.359, 0.042, 1.251, 0.125, 0.809]}, {"time": 0.1667, "x": 1.359, "y": 0.809, "curve": [0.208, 1.359, 0.292, 0.622, 0.208, 0.809, 0.292, 1.405]}, {"time": 0.3333, "x": 0.622, "y": 1.405, "curve": [0.392, 0.622, 0.508, 1.155, 0.392, 1.405, 0.508, 0.845]}, {"time": 0.5667, "x": 1.155, "y": 0.845, "curve": [0.617, 1.155, 0.717, 1, 0.617, 0.845, 0.717, 1]}, {"time": 0.7667}]}, "HeadFleshball_1": {"scale": [{"x": 1.005, "y": 1.005, "curve": [0.075, 1.037, 0.15, 1.07, 0.075, 1.037, 0.15, 1.07]}, {"time": 0.2, "x": 1.07, "y": 1.07, "curve": [0.3, 1.07, 0.5, 0.94, 0.3, 1.07, 0.5, 0.94]}, {"time": 0.6, "x": 0.94, "y": 0.94, "curve": [0.642, 0.94, 0.704, 0.973, 0.642, 0.94, 0.704, 0.973]}, {"time": 0.7667, "x": 1.005, "y": 1.005}]}, "HeadFleshball_3": {"scale": [{"x": 1.005, "y": 1.005, "curve": [0.075, 0.973, 0.15, 0.94, 0.075, 0.973, 0.15, 0.94]}, {"time": 0.2, "x": 0.94, "y": 0.94, "curve": [0.3, 0.94, 0.5, 1.07, 0.3, 0.94, 0.5, 1.07]}, {"time": 0.6, "x": 1.07, "y": 1.07, "curve": [0.642, 1.07, 0.704, 1.037, 0.642, 1.07, 0.704, 1.037]}, {"time": 0.7667, "x": 1.005, "y": 1.005}]}, "HeadFleshball_2": {"scale": [{"x": 1.053, "y": 1.053, "curve": [0.025, 1.063, 0.048, 1.07, 0.025, 1.063, 0.048, 1.07]}, {"time": 0.0667, "x": 1.07, "y": 1.07, "curve": [0.167, 1.07, 0.367, 0.94, 0.167, 1.07, 0.367, 0.94]}, {"time": 0.4667, "x": 0.94, "y": 0.94, "curve": [0.539, 0.94, 0.671, 1.021, 0.539, 0.94, 0.671, 1.021]}, {"time": 0.7667, "x": 1.053, "y": 1.053}]}, "Fleshball_7": {"scale": [{"x": 0.938, "y": 0.938, "curve": [0.025, 0.919, 0.048, 0.908, 0.025, 0.919, 0.048, 0.908]}, {"time": 0.0667, "x": 0.908, "y": 0.908, "curve": [0.167, 0.908, 0.367, 1.141, 0.167, 0.908, 0.367, 1.141]}, {"time": 0.4667, "x": 1.141, "y": 1.141, "curve": [0.539, 1.141, 0.671, 0.996, 0.539, 1.141, 0.671, 0.996]}, {"time": 0.7667, "x": 0.938, "y": 0.938}]}, "Fleshball_6": {"scale": [{"x": 1.11, "y": 1.11, "curve": [0.096, 1.052, 0.227, 0.908, 0.096, 1.052, 0.227, 0.908]}, {"time": 0.3, "x": 0.908, "y": 0.908, "curve": [0.4, 0.908, 0.6, 1.141, 0.4, 0.908, 0.6, 1.141]}, {"time": 0.7, "x": 1.141, "y": 1.141, "curve": [0.719, 1.141, 0.742, 1.129, 0.719, 1.141, 0.742, 1.129]}, {"time": 0.7667, "x": 1.11, "y": 1.11}]}, "Fleshball_8": {"scale": [{"x": 0.993, "y": 0.993, "curve": [0.085, 1.059, 0.176, 1.141, 0.085, 1.059, 0.176, 1.141]}, {"time": 0.2333, "x": 1.141, "y": 1.141, "curve": [0.333, 1.141, 0.533, 0.908, 0.333, 1.141, 0.533, 0.908]}, {"time": 0.6333, "x": 0.908, "y": 0.908, "curve": [0.668, 0.908, 0.716, 0.946, 0.668, 0.908, 0.716, 0.946]}, {"time": 0.7667, "x": 0.993, "y": 0.993}]}, "Fleshball_5": {"scale": [{"x": 1.059, "y": 1.059, "curve": [0.025, 1.07, 0.048, 1.076, 0.025, 1.07, 0.048, 1.076]}, {"time": 0.0667, "x": 1.076, "y": 1.076, "curve": [0.167, 1.076, 0.367, 0.944, 0.167, 1.076, 0.367, 0.944]}, {"time": 0.4667, "x": 0.944, "y": 0.944, "curve": [0.539, 0.944, 0.671, 1.026, 0.539, 0.944, 0.671, 1.026]}, {"time": 0.7667, "x": 1.059, "y": 1.059}]}, "Fleshball_4": {"scale": [{"x": 1.044, "y": 1.044, "curve": [0.092, 1.006, 0.202, 0.944, 0.092, 1.006, 0.202, 0.944]}, {"time": 0.2667, "x": 0.944, "y": 0.944, "curve": [0.358, 0.944, 0.542, 1.076, 0.358, 0.944, 0.542, 1.076]}, {"time": 0.6333, "x": 1.076, "y": 1.076, "curve": [0.669, 1.076, 0.716, 1.063, 0.669, 1.076, 0.716, 1.063]}, {"time": 0.7667, "x": 1.044, "y": 1.044}]}, "Fleshball_3": {"scale": [{"x": 0.992, "y": 0.992, "curve": [0.064, 0.966, 0.124, 0.944, 0.064, 0.966, 0.124, 0.944]}, {"time": 0.1667, "x": 0.944, "y": 0.944, "curve": [0.258, 0.944, 0.442, 1.076, 0.258, 0.944, 0.442, 1.076]}, {"time": 0.5333, "x": 1.076, "y": 1.076, "curve": [0.591, 1.076, 0.682, 1.03, 0.591, 1.076, 0.682, 1.03]}, {"time": 0.7667, "x": 0.992, "y": 0.992}]}, "Fleshball_1": {"scale": [{"x": 1.044, "y": 1.044, "curve": [0.092, 1.006, 0.202, 0.944, 0.092, 1.006, 0.202, 0.944]}, {"time": 0.2667, "x": 0.944, "y": 0.944, "curve": [0.358, 0.944, 0.542, 1.076, 0.358, 0.944, 0.542, 1.076]}, {"time": 0.6333, "x": 1.076, "y": 1.076, "curve": [0.669, 1.076, 0.716, 1.063, 0.669, 1.076, 0.716, 1.063]}, {"time": 0.7667, "x": 1.044, "y": 1.044}]}, "Fleshball_2": {"scale": [{"x": 0.992, "y": 0.992, "curve": [0.064, 0.966, 0.124, 0.944, 0.064, 0.966, 0.124, 0.944]}, {"time": 0.1667, "x": 0.944, "y": 0.944, "curve": [0.258, 0.944, 0.442, 1.076, 0.258, 0.944, 0.442, 1.076]}, {"time": 0.5333, "x": 1.076, "y": 1.076, "curve": [0.591, 1.076, 0.682, 1.03, 0.591, 1.076, 0.682, 1.03]}, {"time": 0.7667, "x": 0.992, "y": 0.992}]}, "Fleshball_9": {"scale": [{"x": 0.999, "y": 0.999}, {"time": 0.2, "x": 0.897, "y": 0.897}, {"time": 0.5667, "x": 1.101, "y": 1.101}, {"time": 0.7667, "x": 0.999, "y": 0.999}]}, "HeadFleshball_1_Behind": {"scale": [{"x": 1.005, "y": 1.005, "curve": [0.075, 1.037, 0.15, 1.07, 0.075, 1.037, 0.15, 1.07]}, {"time": 0.2, "x": 1.07, "y": 1.07, "curve": [0.3, 1.07, 0.5, 0.94, 0.3, 1.07, 0.5, 0.94]}, {"time": 0.6, "x": 0.94, "y": 0.94, "curve": [0.642, 0.94, 0.704, 0.973, 0.642, 0.94, 0.704, 0.973]}, {"time": 0.7667, "x": 1.005, "y": 1.005}]}, "HeadFleshball_3_Behind": {"scale": [{"x": 1.005, "y": 1.005, "curve": [0.075, 0.973, 0.15, 0.94, 0.075, 0.973, 0.15, 0.94]}, {"time": 0.2, "x": 0.94, "y": 0.94, "curve": [0.3, 0.94, 0.5, 1.07, 0.3, 0.94, 0.5, 1.07]}, {"time": 0.6, "x": 1.07, "y": 1.07, "curve": [0.642, 1.07, 0.704, 1.037, 0.642, 1.07, 0.704, 1.037]}, {"time": 0.7667, "x": 1.005, "y": 1.005}]}}}, "baby-spawn": {"slots": {"Anlter_0_Behind": {"attachment": [{}, {"time": 0.9, "name": "Patroller,Chaser/Patroller_images/Anlter_0"}]}, "Anlter_0_Front": {"attachment": [{}, {"time": 0.9, "name": "Patroller,Chaser/Patroller_images/Anlter_0"}]}, "Anlter_1_Behind": {"attachment": [{}, {"time": 0.9, "name": "Patroller,Chaser/Patroller_images/Anlter_1"}]}, "Anlter_1_Front": {"attachment": [{}, {"time": 0.9, "name": "Patroller,Chaser/Patroller_images/Anlter_1"}]}, "Anlter_3_Behind": {"attachment": [{}, {"time": 0.9, "name": "Patroller,Chaser/Patroller_images/Anlter_3"}]}, "Anlter_3_Front": {"attachment": [{}, {"time": 0.9, "name": "Patroller,Chaser/Patroller_images/Anlter_3"}]}, "Anlter_4_Behind": {"attachment": [{}, {"time": 0.9, "name": "Patroller,Chaser/Patroller_images/Anlter_4"}]}, "Anlter_4_Front": {"attachment": [{}, {"time": 0.9, "name": "Patroller,Chaser/Patroller_images/Anlter_4"}]}, "Body_0": {"attachment": [{}, {"time": 0.9, "name": "Body_0"}]}, "Body_1": {"attachment": [{}, {"time": 0.9, "name": "Body_1"}]}, "Body_2": {"attachment": [{}, {"time": 0.9, "name": "Body_2"}]}, "explode": {"attachment": [{"time": 0.9, "name": "Patroller,Chaser/Mini Chaser images/explode_00"}, {"time": 0.9333, "name": "Patroller,Chaser/Mini Chaser images/explode_01"}, {"time": 0.9667, "name": "Patroller,Chaser/Mini Chaser images/explode_02"}, {"time": 1, "name": "Patroller,Chaser/Mini Chaser images/explode_03"}, {"time": 1.0333, "name": "Patroller,Chaser/Mini Chaser images/explode_04"}, {"time": 1.0667, "name": "Patroller,Chaser/Mini Chaser images/explode_05"}, {"time": 1.1, "name": "Patroller,Chaser/Mini Chaser images/explode_06"}, {"time": 1.1333, "name": "Patroller,Chaser/Mini Chaser images/explode_07"}, {"time": 1.1667, "name": "Patroller,Chaser/Mini Chaser images/explode_08"}, {"time": 1.2, "name": "Patroller,Chaser/Mini Chaser images/explode_09"}, {"time": 1.2333}]}, "Eyes": {"attachment": [{}, {"time": 0.9, "name": "Patroller,Chaser/Chaser_images/Eyes"}]}, "EYE_LEFT_BTM": {"attachment": [{}, {"time": 0.9, "name": "<PERSON>ler,<PERSON>r/Pouncer Boss/Eye_Normal"}]}, "EYE_LEFT_TOP": {"attachment": [{}, {"time": 0.9, "name": "<PERSON>ler,<PERSON>r/Pouncer Boss/Eye_Normal"}]}, "EYE_RIGHT_BTM": {"attachment": [{}, {"time": 0.9, "name": "<PERSON>ler,<PERSON>r/Pouncer Boss/Eye_Normal"}]}, "EYE_RIGHT_TOP": {"attachment": [{}, {"time": 0.9, "name": "<PERSON>ler,<PERSON>r/Pouncer Boss/Eye_Normal"}]}, "Grass_0": {"attachment": [{}, {"time": 0.9, "name": "Patroller,Chaser/Patroller_images/Grass_0"}]}, "Grass_0_back": {"attachment": [{}, {"time": 0.9, "name": "Patroller,Chaser/Patroller_images/Grass_0"}]}, "Grass_1": {"attachment": [{}, {"time": 0.9, "name": "Patroller,Chaser/Patroller_images/Grass_1"}]}, "Head": {"attachment": [{}, {"time": 0.9, "name": "Patroller,Chaser/Patroller_images/Head"}]}, "HeadFleshball_1": {"attachment": [{}, {"time": 0.9, "name": "Patroller,Chaser/Chaser Boss/FleshballHead_2"}]}, "HeadFleshball_1_Behind": {"attachment": [{}, {"time": 0.9, "name": "Patroller,Chaser/Chaser Boss/FleshballHead_2"}]}, "HeadFleshball_2": {"attachment": [{}, {"time": 0.9, "name": "Patroller,Chaser/Chaser Boss/FleshballHead_1"}]}, "HeadFleshball_3": {"attachment": [{}, {"time": 0.9, "name": "Patroller,Chaser/Chaser Boss/FleshballHead_3"}]}, "HeadFleshball_3_Behind": {"attachment": [{}, {"time": 0.9, "name": "Patroller,Chaser/Chaser Boss/FleshballHead_3"}]}, "HeadInner": {"attachment": [{}, {"time": 0.9, "name": "Patroller,Chaser/Patroller_images/HeadInner"}]}, "MouthBtm": {"attachment": [{}, {"time": 0.9, "name": "Mouth"}]}, "Patroller,Chaser/Chaser Boss/FleshBall8": {"attachment": [{"name": "Patroller,Chaser/Chaser Boss/FleshBall1"}, {"time": 0.9}]}, "Patroller,Chaser/Chaser Boss/Tunneler_Ball": {"attachment": [{}, {"time": 0.9, "name": "Patroller,Chaser/Chaser Boss/Tunneler_Ball"}]}, "Patroller,Chaser/Pouncer Boss/Mouth": {"attachment": [{}, {"time": 0.9, "name": "Mouth"}]}, "Patroller,Chaser/Pouncer Boss/Mouth2": {"attachment": [{}, {"time": 0.9, "name": "Mouth"}]}, "Symbol": {"attachment": [{}, {"time": 0.9, "name": "Patroller,Chaser/Patroller_images/Symbol"}]}, "TurretMouth": {"attachment": [{}, {"time": 0.9, "name": "Patroller,Chaser/Chaser Boss/Tunneler_Mouth"}]}, "Twig_0": {"attachment": [{}, {"time": 0.9, "name": "Patroller,Chaser/Patroller_images/Twig_0"}]}}, "bones": {"Antler_1": {"scale": [{}, {"time": 0.4, "x": 1.087, "y": 1.087}]}, "Antler_3": {"scale": [{}, {"time": 0.4, "x": 1.087, "y": 1.087}]}, "Antler_4": {"scale": [{}, {"time": 0.4, "x": 1.087, "y": 1.087}]}, "Antler_0": {"scale": [{}, {"time": 0.4, "x": 1.087, "y": 1.087}]}, "BODY_1": {"scale": [{}, {"time": 0.4, "x": 1.087, "y": 1.087, "curve": [0.525, 1.087, 0.775, 0.799, 0.525, 1.087, 0.775, 0.574]}, {"time": 0.9, "x": 0.799, "y": 0.574, "curve": [0.9, 1.222, 0.925, 1.463, 0.9, 0.688, 0.925, 0.752]}, {"time": 0.9333, "x": 1.463, "y": 0.752, "curve": [0.967, 1.463, 1.033, 0.788, 0.967, 0.752, 1.033, 1.278]}, {"time": 1.0667, "x": 0.788, "y": 1.278, "curve": [1.108, 0.788, 1.192, 0.963, 1.108, 1.278, 1.192, 1.065]}, {"time": 1.2333, "x": 0.963, "y": 1.065}]}, "BODY_2": {"scale": [{}, {"time": 0.4, "x": 1.087, "y": 1.087, "curve": [0.525, 1.087, 0.775, 0.755, 0.525, 1.087, 0.775, 0.616]}, {"time": 0.9, "x": 0.755, "y": 0.616, "curve": [0.9, 1.195, 0.925, 1.445, 0.9, 0.711, 0.925, 0.764]}, {"time": 0.9333, "x": 1.445, "y": 0.764, "curve": [0.967, 1.445, 1.033, 0.843, 0.967, 0.764, 1.033, 1.253]}, {"time": 1.0667, "x": 0.843, "y": 1.253, "curve": [1.108, 0.843, 1.192, 0.963, 1.108, 1.253, 1.192, 1.065]}, {"time": 1.2333, "x": 0.963, "y": 1.065}]}, "BODY_3": {"scale": [{}, {"time": 0.4, "x": 1.087, "y": 1.087, "curve": [0.525, 1.087, 0.775, 0.744, 0.525, 1.087, 0.775, 0.626]}, {"time": 0.9, "x": 0.744, "y": 0.626, "curve": [0.9, 1.145, 0.925, 1.373, 0.9, 0.746, 0.925, 0.814]}, {"time": 0.9333, "x": 1.373, "y": 0.814, "curve": [0.967, 1.373, 1.033, 0.745, 0.967, 0.814, 1.033, 1.469]}, {"time": 1.0667, "x": 0.745, "y": 1.469, "curve": [1.108, 0.745, 1.192, 0.963, 1.108, 1.469, 1.192, 1.065]}, {"time": 1.2333, "x": 0.963, "y": 1.065}]}, "FACE": {"scale": [{"curve": [0.102, 1, 0.248, 1.022, 0.102, 1, 0.248, 1.038]}, {"time": 0.4, "x": 1.046, "y": 1.081, "curve": [0.525, 1.046, 0.775, 0.913, 0.525, 1.081, 0.775, 0.987]}, {"time": 0.9, "x": 0.913, "y": 0.987, "curve": [0.983, 0.913, 1.15, 1, 0.983, 0.987, 1.15, 1]}, {"time": 1.2333}]}, "Grass_0": {"scale": [{}, {"time": 0.4, "x": 1.087, "y": 1.087}]}, "Grass_1": {"scale": [{}, {"time": 0.4, "x": 1.087, "y": 1.087}]}, "HEAD": {"scale": [{}, {"time": 0.4, "x": 1.087, "y": 1.087}]}, "HeadFleshball_1": {"scale": [{"x": 1.005, "y": 1.005, "curve": [0.063, 1.037, 0.125, 1.07, 0.063, 1.037, 0.125, 1.07]}, {"time": 0.1667, "x": 1.07, "y": 1.07, "curve": [0.25, 1.07, 0.417, 0.94, 0.25, 1.07, 0.417, 0.94]}, {"time": 0.5, "x": 0.94, "y": 0.94, "curve": [0.542, 0.94, 0.604, 0.973, 0.542, 0.94, 0.604, 0.973]}, {"time": 0.6667, "x": 1.005, "y": 1.005}]}, "HeadFleshball_1_Behind": {"scale": [{"x": 1.005, "y": 1.005, "curve": [0.063, 1.037, 0.125, 1.07, 0.063, 1.037, 0.125, 1.07]}, {"time": 0.1667, "x": 1.07, "y": 1.07, "curve": [0.25, 1.07, 0.417, 0.94, 0.25, 1.07, 0.417, 0.94]}, {"time": 0.5, "x": 0.94, "y": 0.94, "curve": [0.542, 0.94, 0.604, 0.973, 0.542, 0.94, 0.604, 0.973]}, {"time": 0.6667, "x": 1.005, "y": 1.005}]}, "HeadFleshball_2": {"scale": [{"x": 1.053, "y": 1.053, "curve": [0.025, 1.063, 0.048, 1.07, 0.025, 1.063, 0.048, 1.07]}, {"time": 0.0667, "x": 1.07, "y": 1.07, "curve": [0.15, 1.07, 0.317, 0.94, 0.15, 1.07, 0.317, 0.94]}, {"time": 0.4, "x": 0.94, "y": 0.94, "curve": [0.465, 0.94, 0.581, 1.021, 0.465, 0.94, 0.581, 1.021]}, {"time": 0.6667, "x": 1.053, "y": 1.053}]}, "HeadFleshball_3": {"scale": [{"x": 1.005, "y": 1.005, "curve": [0.063, 0.973, 0.125, 0.94, 0.063, 0.973, 0.125, 0.94]}, {"time": 0.1667, "x": 0.94, "y": 0.94, "curve": [0.25, 0.94, 0.417, 1.07, 0.25, 0.94, 0.417, 1.07]}, {"time": 0.5, "x": 1.07, "y": 1.07, "curve": [0.542, 1.07, 0.604, 1.037, 0.542, 1.07, 0.604, 1.037]}, {"time": 0.6667, "x": 1.005, "y": 1.005}]}, "HeadFleshball_3_Behind": {"scale": [{"x": 1.005, "y": 1.005, "curve": [0.063, 0.973, 0.125, 0.94, 0.063, 0.973, 0.125, 0.94]}, {"time": 0.1667, "x": 0.94, "y": 0.94, "curve": [0.25, 0.94, 0.417, 1.07, 0.25, 0.94, 0.417, 1.07]}, {"time": 0.5, "x": 1.07, "y": 1.07, "curve": [0.542, 1.07, 0.604, 1.037, 0.542, 1.07, 0.604, 1.037]}, {"time": 0.6667, "x": 1.005, "y": 1.005}]}, "HeadInner": {"scale": [{}, {"time": 0.4, "x": 1.087, "y": 1.087}]}, "MOUTH_CHASER": {"scale": [{"y": 0.955, "curve": [0.08, 1, 0.177, 1, 0.08, 0.902, 0.177, 0.814]}, {"time": 0.2333, "y": 0.814, "curve": [0.317, 1, 0.483, 1, 0.317, 0.814, 0.483, 1]}, {"time": 0.5667, "curve": [0.594, 1, 0.628, 1, 0.594, 1, 0.628, 0.981]}, {"time": 0.6667, "y": 0.955}]}, "SpawnBall": {"rotate": [{"value": -153.38, "curve": [0.101, -153.38, 0.249, -78.88]}, {"time": 0.4, "curve": [0.491, 43.55, 0.584, 88.61]}, {"time": 0.6667, "value": 120.07, "curve": [0.741, 148.84, 0.809, 168.89]}, {"time": 0.8667, "value": 173.46, "curve": [0.878, 280.53, 0.889, 335.68]}, {"time": 0.9, "value": 335.68}], "translate": [{"curve": [0, 0.67, 0.257, 1.2, 0, 201.35, 0.257, 361.95]}, {"time": 0.4, "x": 1.2, "y": 361.95, "curve": [0.517, 1.2, 0.826, 0.61, 0.517, 361.95, 0.826, 177.12]}, {"time": 0.8667, "y": -14.35, "curve": [0.875, 0, 0.892, 27.02, 0.875, -14.35, 0.892, -12.49]}, {"time": 0.9, "x": 27.02, "y": -12.49}], "scale": [{"x": 0.332, "y": 0.388, "curve": [0.006, 1.029, 0.175, 1.522, 0.006, 1.052, 0.175, 1.522]}, {"time": 0.2333, "x": 1.522, "y": 1.522, "curve": [0.267, 1.522, 0.333, 0.908, 0.267, 1.522, 0.333, 0.908]}, {"time": 0.3667, "x": 0.908, "y": 0.908, "curve": [0.392, 0.908, 0.442, 0.976, 0.392, 0.908, 0.442, 0.976]}, {"time": 0.4667, "x": 0.976, "y": 0.976, "curve": "stepped"}, {"time": 0.8667, "x": 0.976, "y": 0.976}, {"time": 0.9, "x": 1.866, "y": 1.866}]}, "FleshTurret": {"scale": [{"x": 0.972, "y": 0.972, "curve": [0.08, 1.008, 0.177, 1.07, 0.08, 1.008, 0.177, 1.07]}, {"time": 0.2333, "x": 1.07, "y": 1.07, "curve": [0.317, 1.07, 0.483, 0.94, 0.317, 1.07, 0.483, 0.94]}, {"time": 0.5667, "x": 0.94, "y": 0.94, "curve": [0.594, 0.94, 0.628, 0.953, 0.594, 0.94, 0.628, 0.953]}, {"time": 0.6667, "x": 0.972, "y": 0.972}]}, "MOUTH": {"scale": [{"y": 0.955, "curve": [0.08, 1, 0.177, 1, 0.08, 0.902, 0.177, 0.814]}, {"time": 0.2333, "y": 0.814, "curve": [0.317, 1, 0.483, 1, 0.317, 0.814, 0.483, 1]}, {"time": 0.5667, "curve": [0.594, 1, 0.628, 1, 0.594, 1, 0.628, 0.981]}, {"time": 0.6667, "y": 0.955}]}, "TurretMouth": {"scale": [{"x": 0.904, "y": 0.768, "curve": [0.215, 0.909, 0.125, 0.998, 0.215, 0.785, 0.125, 1.104]}, {"time": 0.3333, "y": 1.111, "curve": [0.549, 0.997, 0.459, 0.95, 0.549, 1.094, 0.459, 0.774]}, {"time": 0.6667, "x": 0.949, "y": 0.768}]}, "Twig": {"scale": [{}, {"time": 0.4, "x": 1.087, "y": 1.087}]}, "Main": {"scale": [{}, {"time": 0.4, "x": 1.087, "y": 1.087, "curve": [0.525, 1.087, 0.775, 0.811, 0.525, 1.087, 0.775, 0.563]}, {"time": 0.9, "x": 0.811, "y": 0.563, "curve": [0.9, 1.207, 0.925, 1.432, 0.9, 0.697, 0.925, 0.773]}, {"time": 0.9333, "x": 1.432, "y": 0.773, "curve": [0.967, 1.432, 1.033, 0.895, 0.967, 0.773, 1.033, 1.163]}, {"time": 1.0667, "x": 0.895, "y": 1.163, "curve": [1.108, 0.895, 1.192, 1.05, 1.108, 1.163, 1.192, 0.959]}, {"time": 1.2333, "x": 1.05, "y": 0.959}]}, "Twig2": {"rotate": [{"value": 4.09, "curve": [0.295, -1.03, 0.602, -6.8]}, {"time": 0.8, "value": -6.8, "curve": [0.875, -6.8, 1.025, 12.2]}, {"time": 1.1, "value": 12.2, "curve": [1.134, 12.2, 1.183, 8.36]}, {"time": 1.2333, "value": 4.09}], "scale": [{}, {"time": 0.4, "x": 1.087, "y": 1.087}]}, "Twig3": {"rotate": [{"value": 0.15, "curve": [0.3, 3.32, 0.6, 6.49]}, {"time": 0.8, "value": 6.49, "curve": [0.867, 6.49, 1, -6.2]}, {"time": 1.0667, "value": -6.2, "curve": [1.108, -6.2, 1.171, -3.02]}, {"time": 1.2333, "value": 0.15}], "scale": [{}, {"time": 0.4, "x": 1.087, "y": 1.087}]}, "Fleshball_2": {"scale": [{"x": 0.992, "y": 0.992, "curve": [0.051, 0.966, 0.099, 0.944, 0.051, 0.966, 0.099, 0.944]}, {"time": 0.1333, "x": 0.944, "y": 0.944, "curve": [0.217, 0.944, 0.383, 1.076, 0.217, 0.944, 0.383, 1.076]}, {"time": 0.4667, "x": 1.076, "y": 1.076, "curve": [0.516, 1.076, 0.594, 1.03, 0.516, 1.076, 0.594, 1.03]}, {"time": 0.6667, "x": 0.992, "y": 0.992}]}, "Fleshball_1": {"scale": [{"x": 1.044, "y": 1.044, "curve": [0.08, 1.006, 0.177, 0.944, 0.08, 1.006, 0.177, 0.944]}, {"time": 0.2333, "x": 0.944, "y": 0.944, "curve": [0.317, 0.944, 0.483, 1.076, 0.317, 0.944, 0.483, 1.076]}, {"time": 0.5667, "x": 1.076, "y": 1.076, "curve": [0.594, 1.076, 0.628, 1.063, 0.594, 1.076, 0.628, 1.063]}, {"time": 0.6667, "x": 1.044, "y": 1.044}]}, "Fleshball_4": {"scale": [{"x": 1.044, "y": 1.044, "curve": [0.08, 1.006, 0.177, 0.944, 0.08, 1.006, 0.177, 0.944]}, {"time": 0.2333, "x": 0.944, "y": 0.944, "curve": [0.317, 0.944, 0.483, 1.076, 0.317, 0.944, 0.483, 1.076]}, {"time": 0.5667, "x": 1.076, "y": 1.076, "curve": [0.594, 1.076, 0.628, 1.063, 0.594, 1.076, 0.628, 1.063]}, {"time": 0.6667, "x": 1.044, "y": 1.044}]}, "Fleshball_5": {"scale": [{"x": 1.059, "y": 1.059, "curve": [0.025, 1.07, 0.048, 1.076, 0.025, 1.07, 0.048, 1.076]}, {"time": 0.0667, "x": 1.076, "y": 1.076, "curve": [0.15, 1.076, 0.317, 0.944, 0.15, 1.076, 0.317, 0.944]}, {"time": 0.4, "x": 0.944, "y": 0.944, "curve": [0.465, 0.944, 0.581, 1.026, 0.465, 0.944, 0.581, 1.026]}, {"time": 0.6667, "x": 1.059, "y": 1.059}]}, "Fleshball_3": {"scale": [{"x": 0.992, "y": 0.992, "curve": [0.051, 0.966, 0.099, 0.944, 0.051, 0.966, 0.099, 0.944]}, {"time": 0.1333, "x": 0.944, "y": 0.944, "curve": [0.217, 0.944, 0.383, 1.076, 0.217, 0.944, 0.383, 1.076]}, {"time": 0.4667, "x": 1.076, "y": 1.076, "curve": [0.516, 1.076, 0.594, 1.03, 0.516, 1.076, 0.594, 1.03]}, {"time": 0.6667, "x": 0.992, "y": 0.992}]}, "Fleshball_7": {"scale": [{"x": 0.938, "y": 0.938, "curve": [0.025, 0.919, 0.048, 0.908, 0.025, 0.919, 0.048, 0.908]}, {"time": 0.0667, "x": 0.908, "y": 0.908, "curve": [0.15, 0.908, 0.317, 1.141, 0.15, 0.908, 0.317, 1.141]}, {"time": 0.4, "x": 1.141, "y": 1.141, "curve": [0.465, 1.141, 0.581, 0.996, 0.465, 1.141, 0.581, 0.996]}, {"time": 0.6667, "x": 0.938, "y": 0.938}]}, "Fleshball_6": {"scale": [{"x": 1.11, "y": 1.11, "curve": [0.085, 1.052, 0.202, 0.908, 0.085, 1.052, 0.202, 0.908]}, {"time": 0.2667, "x": 0.908, "y": 0.908, "curve": [0.35, 0.908, 0.517, 1.141, 0.35, 0.908, 0.517, 1.141]}, {"time": 0.6, "x": 1.141, "y": 1.141, "curve": [0.619, 1.141, 0.642, 1.129, 0.619, 1.141, 0.642, 1.129]}, {"time": 0.6667, "x": 1.11, "y": 1.11}]}, "Fleshball_8": {"scale": [{"x": 0.993, "y": 0.993, "curve": [0.073, 1.059, 0.151, 1.141, 0.073, 1.059, 0.151, 1.141]}, {"time": 0.2, "x": 1.141, "y": 1.141, "curve": [0.283, 1.141, 0.45, 0.908, 0.283, 1.141, 0.45, 0.908]}, {"time": 0.5333, "x": 0.908, "y": 0.908, "curve": [0.568, 0.908, 0.616, 0.946, 0.568, 0.908, 0.616, 0.946]}, {"time": 0.6667, "x": 0.993, "y": 0.993}]}, "Fleshball_9": {"scale": [{"x": 0.999, "y": 0.999}, {"time": 0.1667, "x": 0.897, "y": 0.897}, {"time": 0.5, "x": 1.101, "y": 1.101}, {"time": 0.6667, "x": 0.999, "y": 0.999}]}, "MOUTH_BTM": {"translate": [{"curve": [0.083, 0, 0.25, 0, 0.083, 0, 0.25, 20.4]}, {"time": 0.3333, "y": 20.4, "curve": [0.417, 0, 0.583, 0, 0.417, 20.4, 0.583, 0]}, {"time": 0.6667}]}, "BtmSpike_Left": {"scale": [{}, {"time": 0.4, "x": 1.087, "y": 1.087}]}, "BtmSpike_Right_Behind": {"scale": [{}, {"time": 0.4, "x": 1.087, "y": 1.087}]}, "warning": {"scale": [{}, {"time": 0.4, "x": 1.087, "y": 1.087}]}}}, "hidden": {"slots": {"Anlter_0_Behind": {"attachment": [{}]}, "Anlter_0_Front": {"attachment": [{}]}, "Anlter_1_Behind": {"attachment": [{}]}, "Anlter_1_Front": {"attachment": [{}]}, "Anlter_3_Behind": {"attachment": [{}]}, "Anlter_3_Front": {"attachment": [{}]}, "Anlter_4_Behind": {"attachment": [{}]}, "Anlter_4_Front": {"attachment": [{}]}, "Body_0": {"attachment": [{}]}, "Body_1": {"attachment": [{}]}, "Body_2": {"attachment": [{}]}, "BtmSpike_Left_Behind": {"attachment": [{}]}, "BtmSpike_Left_Front": {"attachment": [{}]}, "BtmSpike_Right_Behind": {"attachment": [{}]}, "BtmSpike_Right_Front": {"attachment": [{}]}, "Eyes": {"attachment": [{}]}, "EYE_LEFT_BTM": {"attachment": [{}]}, "EYE_LEFT_TOP": {"attachment": [{"name": "<PERSON>ler,<PERSON>r/Pouncer <PERSON>/Eye_Shut"}, {"time": 0.2333, "name": "<PERSON>ler,<PERSON>r/Pouncer Boss/Eye_Normal"}, {"time": 0.4667, "name": "<PERSON>ler,<PERSON>r/Pouncer <PERSON>/Eye_Shut"}, {"time": 0.9667, "name": "<PERSON>ler,<PERSON>r/Pouncer Boss/Eye_Normal"}, {"time": 1.1667, "name": "<PERSON>ler,<PERSON>r/Pouncer <PERSON>/Eye_Shut"}, {"time": 1.5, "name": "<PERSON>ler,<PERSON><PERSON>/<PERSON>uncer <PERSON>/Eye_Shocked"}, {"time": 2.9, "name": "<PERSON>ler,<PERSON>r/Pouncer <PERSON>/Eye_Shut"}]}, "EYE_RIGHT_BTM": {"attachment": [{}]}, "EYE_RIGHT_TOP": {"attachment": [{"name": "<PERSON>ler,<PERSON>r/Pouncer <PERSON>/Eye_Shut"}, {"time": 0.2333, "name": "<PERSON>ler,<PERSON>r/Pouncer Boss/Eye_Normal"}, {"time": 0.4667, "name": "<PERSON>ler,<PERSON>r/Pouncer <PERSON>/Eye_Shut"}, {"time": 0.9667, "name": "<PERSON>ler,<PERSON>r/Pouncer Boss/Eye_Normal"}, {"time": 1.1667, "name": "<PERSON>ler,<PERSON>r/Pouncer <PERSON>/Eye_Shut"}, {"time": 1.5, "name": "<PERSON>ler,<PERSON><PERSON>/<PERSON>uncer <PERSON>/Eye_Shocked"}, {"time": 2.9, "name": "<PERSON>ler,<PERSON>r/Pouncer <PERSON>/Eye_Shut"}]}, "Head": {"attachment": [{}]}, "HeadFleshball_1": {"attachment": [{}]}, "HeadFleshball_1_Behind": {"attachment": [{}]}, "HeadFleshball_2": {"attachment": [{}]}, "HeadFleshball_3": {"attachment": [{}]}, "HeadFleshball_3_Behind": {"attachment": [{}]}, "HeadInner": {"attachment": [{}]}, "MouthBtm": {"attachment": [{}]}, "Patroller,Chaser/Chaser Boss/FleshBall1": {"attachment": [{}]}, "Patroller,Chaser/Chaser Boss/FleshBall2": {"attachment": [{}]}, "Patroller,Chaser/Chaser Boss/FleshBall3": {"attachment": [{}]}, "Patroller,Chaser/Chaser Boss/FleshBall4": {"attachment": [{}]}, "Patroller,Chaser/Chaser Boss/FleshBall5": {"attachment": [{}]}, "Patroller,Chaser/Chaser Boss/FleshBall6": {"attachment": [{}]}, "Patroller,Chaser/Chaser Boss/FleshBall7": {"attachment": [{}]}, "Patroller,Chaser/Chaser Boss/FleshBall9": {"attachment": [{}]}, "Patroller,Chaser/Chaser Boss/FleshBall11": {"attachment": [{}]}, "Patroller,Chaser/Chaser Boss/Tunneler_Ball": {"attachment": [{}]}, "Patroller,Chaser/Pouncer Boss/Mouth": {"attachment": [{"time": 0.5667, "name": "Mouth_shut"}, {"time": 1.5, "name": "Mouth"}, {"time": 2.9333, "name": "Mouth_shut"}]}, "Patroller,Chaser/Pouncer Boss/Mouth2": {"attachment": [{}]}, "Symbol": {"attachment": [{}]}, "TurretMouth": {"attachment": [{}]}, "Twig_0": {"attachment": [{}]}}, "bones": {"BODY_1": {"scale": [{"x": 1.268, "y": 0.732}]}, "BODY_2": {"scale": [{"x": 1.268, "y": 0.732}]}, "BODY_3": {"scale": [{"x": 1.268, "y": 0.732}]}, "FACE": {"translate": [{"x": 2.01, "y": -48.7}]}, "MOUTH": {"scale": [{"x": 1.064, "y": 0.499, "curve": [0.042, 1.064, 0.125, 1, 0.042, 0.499, 0.125, 1.119]}, {"time": 0.1667, "y": 1.119, "curve": [0.208, 1, 0.292, 1, 0.208, 1.119, 0.292, 1]}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5}, {"time": 0.5333, "y": 0.52, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 1.4667, "curve": "stepped"}, {"time": 1.5, "y": 0.52, "curve": [1.517, 1, 1.55, 1, 1.517, 0.52, 1.55, 1.224]}, {"time": 1.5667, "y": 1.224, "curve": [1.583, 1, 1.617, 1, 1.583, 1.224, 1.617, 1.001]}, {"time": 1.6333, "y": 1.001, "curve": [1.65, 1, 1.683, 1, 1.65, 1.001, 1.683, 1.224]}, {"time": 1.7, "y": 1.224, "curve": [1.717, 1, 1.75, 1, 1.717, 1.224, 1.75, 1.001]}, {"time": 1.7667, "y": 1.001, "curve": [1.783, 1, 1.817, 1, 1.783, 1.001, 1.817, 1.224]}, {"time": 1.8333, "y": 1.224, "curve": [1.85, 1, 1.883, 1, 1.85, 1.224, 1.883, 1.001]}, {"time": 1.9, "y": 1.001, "curve": [1.917, 1, 1.95, 1, 1.917, 1.001, 1.95, 1.224]}, {"time": 1.9667, "y": 1.224, "curve": [1.983, 1, 2.017, 1, 1.983, 1.224, 2.017, 1.001]}, {"time": 2.0333, "y": 1.001, "curve": [2.05, 1, 2.083, 1, 2.05, 1.001, 2.083, 1.224]}, {"time": 2.1, "y": 1.224, "curve": [2.125, 1, 2.175, 1.043, 2.125, 1.224, 2.175, 0.75]}, {"time": 2.2, "x": 1.043, "y": 0.75, "curve": [2.23, 1.043, 2.263, 1.027, 2.23, 0.75, 2.263, 0.866]}, {"time": 2.3, "y": 1.065, "curve": [2.379, 1.009, 2.472, 1.025, 2.379, 0.985, 2.472, 0.826]}, {"time": 2.5667, "x": 1.043, "y": 0.662, "curve": [2.596, 1.043, 2.63, 1.027, 2.596, 0.662, 2.63, 0.827]}, {"time": 2.6667, "y": 1.108, "curve": [2.726, 1, 2.796, 1, 2.726, 1.055, 2.796, 0.95]}, {"time": 2.8667, "y": 0.842}, {"time": 2.9, "y": 0.52, "curve": "stepped"}, {"time": 2.9333}]}, "Main": {"scale": [{"x": 0.899, "y": 0.311}]}, "MOUTH_BTM": {"translate": [{"x": 1.07, "y": 34.11}]}}}, "jump": {"bones": {"Main": {"scale": [{"x": 1.05, "y": 0.959, "curve": [0.033, 1.05, 0.1, 0.829, 0.033, 0.959, 0.1, 1.651]}, {"time": 0.1333, "x": 0.829, "y": 1.651, "curve": [0.167, 0.829, 0.233, 1.151, 0.167, 1.651, 0.233, 0.897]}, {"time": 0.2667, "x": 1.151, "y": 0.897, "curve": [0.317, 1.151, 0.417, 0.969, 0.317, 0.897, 0.417, 1.106]}, {"time": 0.4667, "x": 0.969, "y": 1.106, "curve": [0.517, 0.969, 0.617, 1.05, 0.517, 1.106, 0.617, 0.959]}, {"time": 0.6667, "x": 1.05, "y": 0.959}]}, "BODY_1": {"scale": [{"x": 1.034, "y": 0.978, "curve": [0.018, 1.042, 0.046, 1.048, 0.018, 0.968, 0.046, 0.96]}, {"time": 0.1, "x": 1.05, "y": 0.959, "curve": [0.183, 1.05, 0.35, 0.963, 0.183, 0.959, 0.35, 1.065]}, {"time": 0.4333, "x": 0.963, "y": 1.065, "curve": [0.607, 0.967, 0.609, 1.007, 0.607, 1.06, 0.609, 1.012]}, {"time": 0.6667, "x": 1.034, "y": 0.978}]}, "BODY_2": {"scale": [{"x": 0.977, "y": 1.047, "curve": [0.06, 1.005, 0.061, 1.045, 0.06, 1.013, 0.061, 0.964]}, {"time": 0.2333, "x": 1.05, "y": 0.959, "curve": [0.317, 1.05, 0.483, 0.963, 0.317, 0.959, 0.483, 1.065]}, {"time": 0.5667, "x": 0.963, "y": 1.065, "curve": [0.621, 0.964, 0.648, 0.97, 0.621, 1.063, 0.648, 1.056]}, {"time": 0.6667, "x": 0.977, "y": 1.047}]}, "BODY_3": {"scale": [{"x": 0.963, "y": 1.065, "curve": [0.224, 0.969, 0.116, 1.044, 0.224, 1.058, 0.116, 0.966]}, {"time": 0.3333, "x": 1.05, "y": 0.959, "curve": [0.557, 1.044, 0.45, 0.969, 0.557, 0.965, 0.45, 1.058]}, {"time": 0.6667, "x": 0.963, "y": 1.065}]}, "FACE": {"scale": [{"curve": [0.083, 1, 0.25, 0.913, 0.083, 1, 0.25, 0.987]}, {"time": 0.3333, "x": 0.913, "y": 0.987, "curve": [0.417, 0.913, 0.583, 1, 0.417, 0.987, 0.583, 1]}, {"time": 0.6667}]}, "Twig2": {"rotate": [{"value": 4.09, "curve": [0.061, -1.03, 0.126, -6.8]}, {"time": 0.1667, "value": -6.8, "curve": [0.258, -6.8, 0.442, 12.2]}, {"time": 0.5333, "value": 12.2, "curve": [0.567, 12.2, 0.616, 8.36]}, {"time": 0.6667, "value": 4.09}]}, "Twig3": {"rotate": [{"value": 0.15, "curve": [0.063, 3.32, 0.125, 6.49]}, {"time": 0.1667, "value": 6.49, "curve": [0.25, 6.49, 0.417, -6.2]}, {"time": 0.5, "value": -6.2, "curve": [0.542, -6.2, 0.604, -3.02]}, {"time": 0.6667, "value": 0.15}]}, "MOUTH": {"scale": [{"y": 0.955, "curve": [0.08, 1, 0.177, 1, 0.08, 0.902, 0.177, 0.814]}, {"time": 0.2333, "y": 0.814, "curve": [0.317, 1, 0.483, 1, 0.317, 0.814, 0.483, 1]}, {"time": 0.5667, "curve": [0.594, 1, 0.628, 1, 0.594, 1, 0.628, 0.981]}, {"time": 0.6667, "y": 0.955}]}, "Fleshball_2": {"scale": [{"x": 0.992, "y": 0.992, "curve": [0.051, 0.966, 0.099, 0.944, 0.051, 0.966, 0.099, 0.944]}, {"time": 0.1333, "x": 0.944, "y": 0.944, "curve": [0.217, 0.944, 0.383, 1.076, 0.217, 0.944, 0.383, 1.076]}, {"time": 0.4667, "x": 1.076, "y": 1.076, "curve": [0.516, 1.076, 0.594, 1.03, 0.516, 1.076, 0.594, 1.03]}, {"time": 0.6667, "x": 0.992, "y": 0.992}]}, "Fleshball_1": {"scale": [{"x": 1.044, "y": 1.044, "curve": [0.08, 1.006, 0.177, 0.944, 0.08, 1.006, 0.177, 0.944]}, {"time": 0.2333, "x": 0.944, "y": 0.944, "curve": [0.317, 0.944, 0.483, 1.076, 0.317, 0.944, 0.483, 1.076]}, {"time": 0.5667, "x": 1.076, "y": 1.076, "curve": [0.594, 1.076, 0.628, 1.063, 0.594, 1.076, 0.628, 1.063]}, {"time": 0.6667, "x": 1.044, "y": 1.044}]}, "Fleshball_4": {"scale": [{"x": 1.044, "y": 1.044, "curve": [0.08, 1.006, 0.177, 0.944, 0.08, 1.006, 0.177, 0.944]}, {"time": 0.2333, "x": 0.944, "y": 0.944, "curve": [0.317, 0.944, 0.483, 1.076, 0.317, 0.944, 0.483, 1.076]}, {"time": 0.5667, "x": 1.076, "y": 1.076, "curve": [0.594, 1.076, 0.628, 1.063, 0.594, 1.076, 0.628, 1.063]}, {"time": 0.6667, "x": 1.044, "y": 1.044}]}, "Fleshball_5": {"scale": [{"x": 1.059, "y": 1.059, "curve": [0.025, 1.07, 0.048, 1.076, 0.025, 1.07, 0.048, 1.076]}, {"time": 0.0667, "x": 1.076, "y": 1.076, "curve": [0.15, 1.076, 0.317, 0.944, 0.15, 1.076, 0.317, 0.944]}, {"time": 0.4, "x": 0.944, "y": 0.944, "curve": [0.465, 0.944, 0.581, 1.026, 0.465, 0.944, 0.581, 1.026]}, {"time": 0.6667, "x": 1.059, "y": 1.059}]}, "Fleshball_3": {"scale": [{"x": 0.992, "y": 0.992, "curve": [0.051, 0.966, 0.099, 0.944, 0.051, 0.966, 0.099, 0.944]}, {"time": 0.1333, "x": 0.944, "y": 0.944, "curve": [0.217, 0.944, 0.383, 1.076, 0.217, 0.944, 0.383, 1.076]}, {"time": 0.4667, "x": 1.076, "y": 1.076, "curve": [0.516, 1.076, 0.594, 1.03, 0.516, 1.076, 0.594, 1.03]}, {"time": 0.6667, "x": 0.992, "y": 0.992}]}, "Fleshball_7": {"scale": [{"x": 0.938, "y": 0.938, "curve": [0.025, 0.919, 0.048, 0.908, 0.025, 0.919, 0.048, 0.908]}, {"time": 0.0667, "x": 0.908, "y": 0.908, "curve": [0.15, 0.908, 0.317, 1.141, 0.15, 0.908, 0.317, 1.141]}, {"time": 0.4, "x": 1.141, "y": 1.141, "curve": [0.465, 1.141, 0.581, 0.996, 0.465, 1.141, 0.581, 0.996]}, {"time": 0.6667, "x": 0.938, "y": 0.938}]}, "Fleshball_6": {"scale": [{"x": 1.11, "y": 1.11, "curve": [0.085, 1.052, 0.202, 0.908, 0.085, 1.052, 0.202, 0.908]}, {"time": 0.2667, "x": 0.908, "y": 0.908, "curve": [0.35, 0.908, 0.517, 1.141, 0.35, 0.908, 0.517, 1.141]}, {"time": 0.6, "x": 1.141, "y": 1.141, "curve": [0.619, 1.141, 0.642, 1.129, 0.619, 1.141, 0.642, 1.129]}, {"time": 0.6667, "x": 1.11, "y": 1.11}]}, "Fleshball_8": {"scale": [{"x": 0.993, "y": 0.993, "curve": [0.073, 1.059, 0.151, 1.141, 0.073, 1.059, 0.151, 1.141]}, {"time": 0.2, "x": 1.141, "y": 1.141, "curve": [0.283, 1.141, 0.45, 0.908, 0.283, 1.141, 0.45, 0.908]}, {"time": 0.5333, "x": 0.908, "y": 0.908, "curve": [0.568, 0.908, 0.616, 0.946, 0.568, 0.908, 0.616, 0.946]}, {"time": 0.6667, "x": 0.993, "y": 0.993}]}, "Fleshball_9": {"scale": [{"x": 0.999, "y": 0.999}, {"time": 0.1667, "x": 0.897, "y": 0.897}, {"time": 0.5, "x": 1.101, "y": 1.101}, {"time": 0.6667, "x": 0.999, "y": 0.999}]}, "MOUTH_BTM": {"translate": [{"curve": [0.083, 0, 0.25, 0, 0.083, 0, 0.25, 20.4]}, {"time": 0.3333, "y": 20.4, "curve": [0.417, 0, 0.583, 0, 0.417, 20.4, 0.583, 0]}, {"time": 0.6667}]}, "HeadFleshball_3": {"scale": [{"x": 1.005, "y": 1.005, "curve": [0.063, 0.973, 0.125, 0.94, 0.063, 0.973, 0.125, 0.94]}, {"time": 0.1667, "x": 0.94, "y": 0.94, "curve": [0.25, 0.94, 0.417, 1.07, 0.25, 0.94, 0.417, 1.07]}, {"time": 0.5, "x": 1.07, "y": 1.07, "curve": [0.542, 1.07, 0.604, 1.037, 0.542, 1.07, 0.604, 1.037]}, {"time": 0.6667, "x": 1.005, "y": 1.005}]}, "HeadFleshball_2": {"scale": [{"x": 1.053, "y": 1.053, "curve": [0.025, 1.063, 0.048, 1.07, 0.025, 1.063, 0.048, 1.07]}, {"time": 0.0667, "x": 1.07, "y": 1.07, "curve": [0.15, 1.07, 0.317, 0.94, 0.15, 1.07, 0.317, 0.94]}, {"time": 0.4, "x": 0.94, "y": 0.94, "curve": [0.465, 0.94, 0.581, 1.021, 0.465, 0.94, 0.581, 1.021]}, {"time": 0.6667, "x": 1.053, "y": 1.053}]}, "HeadFleshball_1": {"scale": [{"x": 1.005, "y": 1.005, "curve": [0.063, 1.037, 0.125, 1.07, 0.063, 1.037, 0.125, 1.07]}, {"time": 0.1667, "x": 1.07, "y": 1.07, "curve": [0.25, 1.07, 0.417, 0.94, 0.25, 1.07, 0.417, 0.94]}, {"time": 0.5, "x": 0.94, "y": 0.94, "curve": [0.542, 0.94, 0.604, 0.973, 0.542, 0.94, 0.604, 0.973]}, {"time": 0.6667, "x": 1.005, "y": 1.005}]}, "FleshTurret": {"scale": [{"x": 0.972, "y": 0.972, "curve": [0.08, 1.008, 0.177, 1.07, 0.08, 1.008, 0.177, 1.07]}, {"time": 0.2333, "x": 1.07, "y": 1.07, "curve": [0.317, 1.07, 0.483, 0.94, 0.317, 1.07, 0.483, 0.94]}, {"time": 0.5667, "x": 0.94, "y": 0.94, "curve": [0.594, 0.94, 0.628, 0.953, 0.594, 0.94, 0.628, 0.953]}, {"time": 0.6667, "x": 0.972, "y": 0.972}]}, "TurretMouth": {"scale": [{"x": 0.904, "y": 0.768, "curve": [0.215, 0.909, 0.125, 0.998, 0.215, 0.785, 0.125, 1.104]}, {"time": 0.3333, "y": 1.111, "curve": [0.549, 0.997, 0.459, 0.95, 0.549, 1.094, 0.459, 0.774]}, {"time": 0.6667, "x": 0.949, "y": 0.768}]}, "HeadFleshball_1_Behind": {"scale": [{"x": 1.005, "y": 1.005, "curve": [0.063, 1.037, 0.125, 1.07, 0.063, 1.037, 0.125, 1.07]}, {"time": 0.1667, "x": 1.07, "y": 1.07, "curve": [0.25, 1.07, 0.417, 0.94, 0.25, 1.07, 0.417, 0.94]}, {"time": 0.5, "x": 0.94, "y": 0.94, "curve": [0.542, 0.94, 0.604, 0.973, 0.542, 0.94, 0.604, 0.973]}, {"time": 0.6667, "x": 1.005, "y": 1.005}]}, "HeadFleshball_3_Behind": {"scale": [{"x": 1.005, "y": 1.005, "curve": [0.063, 0.973, 0.125, 0.94, 0.063, 0.973, 0.125, 0.94]}, {"time": 0.1667, "x": 0.94, "y": 0.94, "curve": [0.25, 0.94, 0.417, 1.07, 0.25, 0.94, 0.417, 1.07]}, {"time": 0.5, "x": 1.07, "y": 1.07, "curve": [0.542, 1.07, 0.604, 1.037, 0.542, 1.07, 0.604, 1.037]}, {"time": 0.6667, "x": 1.005, "y": 1.005}]}, "MOUTH_CHASER": {"scale": [{"y": 0.955, "curve": [0.08, 1, 0.177, 1, 0.08, 0.902, 0.177, 0.814]}, {"time": 0.2333, "y": 0.814, "curve": [0.317, 1, 0.483, 1, 0.317, 0.814, 0.483, 1]}, {"time": 0.5667, "curve": [0.594, 1, 0.628, 1, 0.594, 1, 0.628, 0.981]}, {"time": 0.6667, "y": 0.955}]}, "CrabLeg3": {"rotate": [{"curve": [0.025, 0, 0.075, 53.19]}, {"time": 0.1, "value": 53.19, "curve": [0.167, 53.19, 0.3, -43.63]}, {"time": 0.3667, "value": -43.63}], "translate": [{"curve": [0.025, 0, 0.075, -0.43, 0.025, 0, 0.075, -14.71]}, {"time": 0.1, "x": -0.43, "y": -14.71, "curve": [0.167, -0.43, 0.3, 0, 0.167, -14.71, 0.3, 0]}, {"time": 0.3667}]}, "CrabLeg4": {"rotate": [{"curve": [0.025, 0, 0.075, -28.03]}, {"time": 0.1, "value": -28.03, "curve": [0.167, -28.03, 0.3, 50.07]}, {"time": 0.3667, "value": 50.07}]}, "CrabLeg2": {"rotate": [{"curve": [0.025, 0, 0.075, -20.63]}, {"time": 0.1, "value": -20.63, "curve": [0.167, -20.63, 0.3, 34.91]}, {"time": 0.3667, "value": 34.91}]}, "CrabLeg1": {"rotate": [{"curve": [0.025, 0, 0.075, 35.15]}, {"time": 0.1, "value": 35.15, "curve": [0.167, 35.15, 0.3, -43.05]}, {"time": 0.3667, "value": -43.05}], "translate": [{"curve": [0.025, 0, 0.075, -0.43, 0.025, 0, 0.075, -14.71]}, {"time": 0.1, "x": -0.43, "y": -14.71, "curve": [0.167, -0.43, 0.3, 0, 0.167, -14.71, 0.3, 0]}, {"time": 0.3667}]}, "CrabLeg5": {"rotate": [{"curve": [0.025, 0, 0.075, -52.81]}, {"time": 0.1, "value": -52.81, "curve": [0.167, -52.81, 0.3, 44.24]}, {"time": 0.3667, "value": 44.24}], "translate": [{"curve": [0.025, 0, 0.075, -0.43, 0.025, 0, 0.075, -14.71]}, {"time": 0.1, "x": -0.43, "y": -14.71, "curve": [0.167, -0.43, 0.3, 0, 0.167, -14.71, 0.3, 0]}, {"time": 0.3667}]}, "CrabLeg7": {"rotate": [{"curve": [0.025, 0, 0.075, -32.49]}, {"time": 0.1, "value": -32.49, "curve": [0.167, -32.49, 0.3, 42.81]}, {"time": 0.3667, "value": 42.81}], "translate": [{"curve": [0.025, 0, 0.075, -0.43, 0.025, 0, 0.075, -14.71]}, {"time": 0.1, "x": -0.43, "y": -14.71, "curve": [0.167, -0.43, 0.3, 0, 0.167, -14.71, 0.3, 0]}, {"time": 0.3667}]}, "CrabLeg8": {"rotate": [{"curve": [0.025, 0, 0.075, -8.78]}, {"time": 0.1, "value": -8.78, "curve": [0.167, -8.78, 0.3, 30.25]}, {"time": 0.3667, "value": 30.25}]}, "CrabLeg6": {"rotate": [{"curve": [0.025, 0, 0.075, -25.9]}, {"time": 0.1, "value": -25.9, "curve": [0.167, -25.9, 0.3, 44.38]}, {"time": 0.3667, "value": 44.38}]}}}, "land": {"slots": {"Eyes": {"attachment": [{"name": "Patroller,Chaser/Chaser_images/Eyes_Shut"}, {"time": 0.5, "name": "Patroller,Chaser/Chaser_images/Eyes"}]}, "EYE_LEFT_BTM": {"attachment": [{"name": "<PERSON>ler,<PERSON>r/Pouncer <PERSON>/Eye_Shut"}, {"time": 0.3333, "name": "<PERSON>ler,<PERSON>r/Pouncer Boss/Eye_Normal"}]}, "EYE_LEFT_TOP": {"attachment": [{"name": "<PERSON>ler,<PERSON>r/Pouncer <PERSON>/Eye_Shut"}, {"time": 0.3333, "name": "<PERSON>ler,<PERSON>r/Pouncer Boss/Eye_Normal"}]}, "EYE_RIGHT_BTM": {"attachment": [{"name": "<PERSON>ler,<PERSON>r/Pouncer <PERSON>/Eye_Shut"}, {"time": 0.3333, "name": "<PERSON>ler,<PERSON>r/Pouncer Boss/Eye_Normal"}]}, "EYE_RIGHT_TOP": {"attachment": [{"name": "<PERSON>ler,<PERSON>r/Pouncer <PERSON>/Eye_Shut"}, {"time": 0.3333, "name": "<PERSON>ler,<PERSON>r/Pouncer Boss/Eye_Normal"}]}, "MouthBtm": {"attachment": [{"time": 0.0667, "name": "Mouth_shut"}]}, "Patroller,Chaser/Pouncer Boss/Mouth": {"attachment": [{"time": 0.0667, "name": "Mouth_shut"}, {"time": 0.4667, "name": "Mouth"}]}, "Patroller,Chaser/Pouncer Boss/Mouth2": {"attachment": [{"time": 0.0667, "name": "Mouth_shut"}]}, "TurretMouth": {"attachment": [{"name": "Patroller,Chaser/Chaser Boss/Tunneler_Mouth_Closed"}, {"time": 0.1, "name": "Patroller,Chaser/Chaser Boss/Tunneler_Mouth_Wide"}, {"time": 0.5, "name": "Patroller,Chaser/Chaser Boss/Tunneler_Mouth"}]}}, "bones": {"FACE": {"translate": [{"y": 21.63, "curve": [0.021, 0, 0.1, 0, 0.021, -21.49, 0.1, -22.18]}, {"time": 0.1333, "y": -22.18, "curve": [0.158, 0, 0.208, -2.29, 0.158, -22.18, 0.208, 51.45]}, {"time": 0.2333, "x": -2.29, "y": 51.45, "curve": [0.275, -2.29, 0.358, 0, 0.275, 51.45, 0.358, 0]}, {"time": 0.4}], "scale": [{"curve": [0.083, 1, 0.25, 0.913, 0.083, 1, 0.25, 0.987]}, {"time": 0.3333, "x": 0.913, "y": 0.987, "curve": [0.417, 0.913, 0.583, 1, 0.417, 0.987, 0.583, 1]}, {"time": 0.6667}]}, "MOUTH_CHASER": {"scale": [{"y": 1.152}, {"time": 0.0333, "y": 0.801}, {"time": 0.0667, "y": 0.955}, {"time": 0.4667, "y": 0.681, "curve": [0.5, 1, 0.538, 1, 0.5, 0.771, 0.538, 0.911]}, {"time": 0.5667, "y": 0.955}]}, "MOUTH": {"scale": [{"y": 1.152}, {"time": 0.0333, "y": 0.801}, {"time": 0.0667, "y": 0.955, "curve": "stepped"}, {"time": 0.4667, "y": 0.681, "curve": [0.5, 1, 0.538, 1, 0.5, 0.771, 0.538, 0.911]}, {"time": 0.5667, "y": 0.955}]}, "Main": {"scale": [{"x": 1.05, "y": 0.959, "curve": [0, 1.231, 0.075, 1.309, 0, 0.763, 0.075, 0.678]}, {"time": 0.1, "x": 1.309, "y": 0.678, "curve": [0.125, 1.309, 0.175, 0.975, 0.125, 0.678, 0.175, 1.016]}, {"time": 0.2, "x": 0.975, "y": 1.016, "curve": [0.242, 0.975, 0.325, 1.253, 0.242, 1.016, 0.325, 0.78]}, {"time": 0.3667, "x": 1.253, "y": 0.78, "curve": [0.417, 1.253, 0.517, 0.96, 0.417, 0.78, 0.517, 1.054]}, {"time": 0.5667, "x": 0.96, "y": 1.054, "curve": [0.617, 0.96, 0.717, 1.05, 0.617, 1.054, 0.717, 0.959]}, {"time": 0.7667, "x": 1.05, "y": 0.959}]}, "BODY_1": {"scale": [{"x": 1.034, "y": 0.978, "curve": [0.017, 1.034, 0.05, 1.434, 0.017, 0.978, 0.05, 0.638]}, {"time": 0.0667, "x": 1.434, "y": 0.638, "curve": [0.092, 1.434, 0.142, 0.858, 0.092, 0.638, 0.142, 1.56]}, {"time": 0.1667, "x": 0.858, "y": 1.56, "curve": [0.208, 0.858, 0.292, 1.234, 0.208, 1.56, 0.292, 0.795]}, {"time": 0.3333, "x": 1.234, "y": 0.795, "curve": [0.392, 1.234, 0.508, 0.946, 0.392, 0.795, 0.508, 1.075]}, {"time": 0.5667, "x": 0.946, "y": 1.075, "curve": [0.617, 0.946, 0.717, 1.034, 0.617, 1.075, 0.717, 0.978]}, {"time": 0.7667, "x": 1.034, "y": 0.978}]}, "BODY_2": {"scale": [{"x": 0.977, "y": 1.047, "curve": [0.025, 0.977, 0.075, 1.356, 0.025, 1.047, 0.075, 0.684]}, {"time": 0.1, "x": 1.356, "y": 0.684, "curve": [0.125, 1.356, 0.175, 0.811, 0.125, 0.684, 0.175, 1.671]}, {"time": 0.2, "x": 0.811, "y": 1.671, "curve": [0.242, 0.811, 0.325, 1.166, 0.242, 1.671, 0.325, 0.852]}, {"time": 0.3667, "x": 1.166, "y": 0.852, "curve": [0.425, 1.166, 0.542, 0.894, 0.425, 0.852, 0.542, 1.152]}, {"time": 0.6, "x": 0.894, "y": 1.152, "curve": [0.642, 0.894, 0.725, 0.977, 0.642, 1.152, 0.725, 1.047]}, {"time": 0.7667, "x": 0.977, "y": 1.047}]}, "BODY_3": {"scale": [{"x": 0.963, "y": 1.065, "curve": [0.033, 0.963, 0.1, 1.336, 0.033, 1.065, 0.1, 0.695]}, {"time": 0.1333, "x": 1.336, "y": 0.695, "curve": [0.158, 1.336, 0.208, 0.799, 0.158, 0.695, 0.208, 1.699]}, {"time": 0.2333, "x": 0.799, "y": 1.699, "curve": [0.275, 0.799, 0.358, 1.149, 0.275, 1.699, 0.358, 0.866]}, {"time": 0.4, "x": 1.149, "y": 0.866, "curve": [0.458, 1.149, 0.575, 0.881, 0.458, 0.866, 0.575, 1.171]}, {"time": 0.6333, "x": 0.881, "y": 1.171, "curve": [0.667, 0.881, 0.733, 0.963, 0.667, 1.171, 0.733, 1.065]}, {"time": 0.7667, "x": 0.963, "y": 1.065}]}, "Twig2": {"rotate": [{"value": 4.09, "curve": [0.061, -1.03, 0.126, -6.8]}, {"time": 0.1667, "value": -6.8, "curve": [0.258, -6.8, 0.442, 12.2]}, {"time": 0.5333, "value": 12.2, "curve": [0.567, 12.2, 0.616, 8.36]}, {"time": 0.6667, "value": 4.09}]}, "Twig3": {"rotate": [{"value": 0.15, "curve": [0.063, 3.32, 0.125, 6.49]}, {"time": 0.1667, "value": 6.49, "curve": [0.25, 6.49, 0.417, -6.2]}, {"time": 0.5, "value": -6.2, "curve": [0.542, -6.2, 0.604, -3.02]}, {"time": 0.6667, "value": 0.15}]}, "Antler_1": {"rotate": [{"curve": [0.017, 0, 0.05, -28.81]}, {"time": 0.0667, "value": -28.81, "curve": [0.092, -28.81, 0.142, 18.63]}, {"time": 0.1667, "value": 18.63, "curve": [0.217, 18.63, 0.317, -14.31]}, {"time": 0.3667, "value": -14.31, "curve": [0.417, -14.31, 0.517, 6.43]}, {"time": 0.5667, "value": 6.43, "curve": [0.617, 6.43, 0.717, 0]}, {"time": 0.7667}]}, "Antler_4": {"rotate": [{"curve": [0.017, 0, 0.05, 25.06]}, {"time": 0.0667, "value": 25.06, "curve": [0.092, 25.06, 0.142, -16.43]}, {"time": 0.1667, "value": -16.43, "curve": [0.217, -16.43, 0.317, 6.76]}, {"time": 0.3667, "value": 6.76, "curve": [0.417, 6.76, 0.517, -5.33]}, {"time": 0.5667, "value": -5.33, "curve": [0.617, -5.33, 0.717, 0]}, {"time": 0.7667}]}, "Antler_3": {"rotate": [{"curve": [0.017, 0, 0.05, 58.17]}, {"time": 0.0667, "value": 58.17, "curve": [0.092, 58.17, 0.142, -22.19]}, {"time": 0.1667, "value": -22.19, "curve": [0.217, -22.19, 0.317, 13.8]}, {"time": 0.3667, "value": 13.8, "curve": [0.417, 13.8, 0.517, -7.7]}, {"time": 0.5667, "value": -7.7, "curve": [0.617, -7.7, 0.717, 0]}, {"time": 0.7667}]}, "Antler_0": {"rotate": [{"curve": [0.017, 0, 0.05, -53.81]}, {"time": 0.0667, "value": -53.81, "curve": [0.092, -53.81, 0.142, 31.58]}, {"time": 0.1667, "value": 31.58, "curve": [0.217, 31.58, 0.317, -10.48]}, {"time": 0.3667, "value": -10.48, "curve": [0.417, -10.48, 0.517, 10.12]}, {"time": 0.5667, "value": 10.12, "curve": [0.617, 10.12, 0.717, 0]}, {"time": 0.7667}]}, "MOUTH_BTM": {"translate": [{"curve": [0.042, 0, 0.125, 2.78, 0.042, 0, 0.125, -25.2]}, {"time": 0.1667, "x": 2.78, "y": -25.2, "curve": [0.2, 2.78, 0.267, -0.79, 0.2, -25.2, 0.267, 64.88]}, {"time": 0.3, "x": -0.79, "y": 64.88, "curve": [0.35, -0.79, 0.45, 0, 0.35, 64.88, 0.45, -20.98]}, {"time": 0.5, "y": -20.98, "curve": [0.567, 0, 0.7, 0, 0.567, -20.98, 0.7, 0]}, {"time": 0.7667}]}, "FleshTurret": {"scale": [{"x": 0.593, "y": 1.251, "curve": [0.042, 0.593, 0.125, 1.359, 0.042, 1.251, 0.125, 0.809]}, {"time": 0.1667, "x": 1.359, "y": 0.809, "curve": [0.208, 1.359, 0.292, 0.622, 0.208, 0.809, 0.292, 1.405]}, {"time": 0.3333, "x": 0.622, "y": 1.405, "curve": [0.392, 0.622, 0.508, 1.155, 0.392, 1.405, 0.508, 0.845]}, {"time": 0.5667, "x": 1.155, "y": 0.845, "curve": [0.617, 1.155, 0.717, 1, 0.617, 0.845, 0.717, 1]}, {"time": 0.7667}]}, "HeadFleshball_1": {"scale": [{"x": 1.005, "y": 1.005, "curve": [0.075, 1.037, 0.15, 1.07, 0.075, 1.037, 0.15, 1.07]}, {"time": 0.2, "x": 1.07, "y": 1.07, "curve": [0.3, 1.07, 0.5, 0.94, 0.3, 1.07, 0.5, 0.94]}, {"time": 0.6, "x": 0.94, "y": 0.94, "curve": [0.642, 0.94, 0.704, 0.973, 0.642, 0.94, 0.704, 0.973]}, {"time": 0.7667, "x": 1.005, "y": 1.005}]}, "HeadFleshball_3": {"scale": [{"x": 1.005, "y": 1.005, "curve": [0.075, 0.973, 0.15, 0.94, 0.075, 0.973, 0.15, 0.94]}, {"time": 0.2, "x": 0.94, "y": 0.94, "curve": [0.3, 0.94, 0.5, 1.07, 0.3, 0.94, 0.5, 1.07]}, {"time": 0.6, "x": 1.07, "y": 1.07, "curve": [0.642, 1.07, 0.704, 1.037, 0.642, 1.07, 0.704, 1.037]}, {"time": 0.7667, "x": 1.005, "y": 1.005}]}, "HeadFleshball_2": {"scale": [{"x": 1.053, "y": 1.053, "curve": [0.025, 1.063, 0.048, 1.07, 0.025, 1.063, 0.048, 1.07]}, {"time": 0.0667, "x": 1.07, "y": 1.07, "curve": [0.167, 1.07, 0.367, 0.94, 0.167, 1.07, 0.367, 0.94]}, {"time": 0.4667, "x": 0.94, "y": 0.94, "curve": [0.539, 0.94, 0.671, 1.021, 0.539, 0.94, 0.671, 1.021]}, {"time": 0.7667, "x": 1.053, "y": 1.053}]}, "Fleshball_7": {"scale": [{"x": 0.938, "y": 0.938, "curve": [0.025, 0.919, 0.048, 0.908, 0.025, 0.919, 0.048, 0.908]}, {"time": 0.0667, "x": 0.908, "y": 0.908, "curve": [0.167, 0.908, 0.367, 1.141, 0.167, 0.908, 0.367, 1.141]}, {"time": 0.4667, "x": 1.141, "y": 1.141, "curve": [0.539, 1.141, 0.671, 0.996, 0.539, 1.141, 0.671, 0.996]}, {"time": 0.7667, "x": 0.938, "y": 0.938}]}, "Fleshball_6": {"scale": [{"x": 1.11, "y": 1.11, "curve": [0.096, 1.052, 0.227, 0.908, 0.096, 1.052, 0.227, 0.908]}, {"time": 0.3, "x": 0.908, "y": 0.908, "curve": [0.4, 0.908, 0.6, 1.141, 0.4, 0.908, 0.6, 1.141]}, {"time": 0.7, "x": 1.141, "y": 1.141, "curve": [0.719, 1.141, 0.742, 1.129, 0.719, 1.141, 0.742, 1.129]}, {"time": 0.7667, "x": 1.11, "y": 1.11}]}, "Fleshball_8": {"scale": [{"x": 0.993, "y": 0.993, "curve": [0.085, 1.059, 0.176, 1.141, 0.085, 1.059, 0.176, 1.141]}, {"time": 0.2333, "x": 1.141, "y": 1.141, "curve": [0.333, 1.141, 0.533, 0.908, 0.333, 1.141, 0.533, 0.908]}, {"time": 0.6333, "x": 0.908, "y": 0.908, "curve": [0.668, 0.908, 0.716, 0.946, 0.668, 0.908, 0.716, 0.946]}, {"time": 0.7667, "x": 0.993, "y": 0.993}]}, "Fleshball_5": {"scale": [{"x": 1.059, "y": 1.059, "curve": [0.025, 1.07, 0.048, 1.076, 0.025, 1.07, 0.048, 1.076]}, {"time": 0.0667, "x": 1.076, "y": 1.076, "curve": [0.167, 1.076, 0.367, 0.944, 0.167, 1.076, 0.367, 0.944]}, {"time": 0.4667, "x": 0.944, "y": 0.944, "curve": [0.539, 0.944, 0.671, 1.026, 0.539, 0.944, 0.671, 1.026]}, {"time": 0.7667, "x": 1.059, "y": 1.059}]}, "Fleshball_4": {"scale": [{"x": 1.044, "y": 1.044, "curve": [0.092, 1.006, 0.202, 0.944, 0.092, 1.006, 0.202, 0.944]}, {"time": 0.2667, "x": 0.944, "y": 0.944, "curve": [0.358, 0.944, 0.542, 1.076, 0.358, 0.944, 0.542, 1.076]}, {"time": 0.6333, "x": 1.076, "y": 1.076, "curve": [0.669, 1.076, 0.716, 1.063, 0.669, 1.076, 0.716, 1.063]}, {"time": 0.7667, "x": 1.044, "y": 1.044}]}, "Fleshball_3": {"scale": [{"x": 0.992, "y": 0.992, "curve": [0.064, 0.966, 0.124, 0.944, 0.064, 0.966, 0.124, 0.944]}, {"time": 0.1667, "x": 0.944, "y": 0.944, "curve": [0.258, 0.944, 0.442, 1.076, 0.258, 0.944, 0.442, 1.076]}, {"time": 0.5333, "x": 1.076, "y": 1.076, "curve": [0.591, 1.076, 0.682, 1.03, 0.591, 1.076, 0.682, 1.03]}, {"time": 0.7667, "x": 0.992, "y": 0.992}]}, "Fleshball_1": {"scale": [{"x": 1.044, "y": 1.044, "curve": [0.092, 1.006, 0.202, 0.944, 0.092, 1.006, 0.202, 0.944]}, {"time": 0.2667, "x": 0.944, "y": 0.944, "curve": [0.358, 0.944, 0.542, 1.076, 0.358, 0.944, 0.542, 1.076]}, {"time": 0.6333, "x": 1.076, "y": 1.076, "curve": [0.669, 1.076, 0.716, 1.063, 0.669, 1.076, 0.716, 1.063]}, {"time": 0.7667, "x": 1.044, "y": 1.044}]}, "Fleshball_2": {"scale": [{"x": 0.992, "y": 0.992, "curve": [0.064, 0.966, 0.124, 0.944, 0.064, 0.966, 0.124, 0.944]}, {"time": 0.1667, "x": 0.944, "y": 0.944, "curve": [0.258, 0.944, 0.442, 1.076, 0.258, 0.944, 0.442, 1.076]}, {"time": 0.5333, "x": 1.076, "y": 1.076, "curve": [0.591, 1.076, 0.682, 1.03, 0.591, 1.076, 0.682, 1.03]}, {"time": 0.7667, "x": 0.992, "y": 0.992}]}, "Fleshball_9": {"scale": [{"x": 0.999, "y": 0.999}, {"time": 0.2, "x": 0.897, "y": 0.897}, {"time": 0.5667, "x": 1.101, "y": 1.101}, {"time": 0.7667, "x": 0.999, "y": 0.999}]}, "HeadFleshball_1_Behind": {"scale": [{"x": 1.005, "y": 1.005, "curve": [0.075, 1.037, 0.15, 1.07, 0.075, 1.037, 0.15, 1.07]}, {"time": 0.2, "x": 1.07, "y": 1.07, "curve": [0.3, 1.07, 0.5, 0.94, 0.3, 1.07, 0.5, 0.94]}, {"time": 0.6, "x": 0.94, "y": 0.94, "curve": [0.642, 0.94, 0.704, 0.973, 0.642, 0.94, 0.704, 0.973]}, {"time": 0.7667, "x": 1.005, "y": 1.005}]}, "HeadFleshball_3_Behind": {"scale": [{"x": 1.005, "y": 1.005, "curve": [0.075, 0.973, 0.15, 0.94, 0.075, 0.973, 0.15, 0.94]}, {"time": 0.2, "x": 0.94, "y": 0.94, "curve": [0.3, 0.94, 0.5, 1.07, 0.3, 0.94, 0.5, 1.07]}, {"time": 0.6, "x": 1.07, "y": 1.07, "curve": [0.642, 1.07, 0.704, 1.037, 0.642, 1.07, 0.704, 1.037]}, {"time": 0.7667, "x": 1.005, "y": 1.005}]}, "CrabLeg3": {"rotate": [{"value": -31.63}, {"time": 0.0333}]}, "CrabLeg1": {"rotate": [{"value": -33.5}, {"time": 0.0333}]}, "CrabLeg7": {"rotate": [{"value": 38.87}, {"time": 0.0333}]}, "CrabLeg5": {"rotate": [{"value": 33.58}, {"time": 0.0333}]}, "HEAD": {"translate": [{"curve": [0.021, 0, 0.1, 0, 0.021, -44.83, 0.1, -45.54]}, {"time": 0.1333, "y": -45.54, "curve": [0.158, 0, 0.208, 0, 0.158, -45.54, 0.208, 0]}, {"time": 0.2333}]}}}, "notice-player": {"slots": {"Eyes": {"attachment": [{"name": "<PERSON>ler,Chaser/Chaser_images/Eyes_Shocked"}, {"time": 0.7333, "name": "Patroller,Chaser/Chaser_images/Eyes"}]}, "EYE_LEFT_BTM": {"attachment": [{"name": "<PERSON>ler,<PERSON><PERSON>/<PERSON>uncer <PERSON>/Eye_Shocked"}, {"time": 0.7333, "name": "<PERSON>ler,<PERSON>r/Pouncer Boss/Eye_Normal"}]}, "EYE_LEFT_TOP": {"attachment": [{"name": "<PERSON>ler,<PERSON><PERSON>/<PERSON>uncer <PERSON>/Eye_Shocked"}, {"time": 0.7333, "name": "<PERSON>ler,<PERSON>r/Pouncer Boss/Eye_Normal"}]}, "EYE_RIGHT_BTM": {"attachment": [{"name": "<PERSON>ler,<PERSON><PERSON>/<PERSON>uncer <PERSON>/Eye_Shocked"}, {"time": 0.7333, "name": "<PERSON>ler,<PERSON>r/Pouncer Boss/Eye_Normal"}]}, "EYE_RIGHT_TOP": {"attachment": [{"name": "<PERSON>ler,<PERSON><PERSON>/<PERSON>uncer <PERSON>/Eye_Shocked"}, {"time": 0.7333, "name": "<PERSON>ler,<PERSON>r/Pouncer Boss/Eye_Normal"}]}}, "bones": {"FACE": {"scale": [{"curve": [0.117, 1, 0.35, 0.913, 0.117, 1, 0.35, 0.987]}, {"time": 0.4667, "x": 0.913, "y": 0.987, "curve": [0.6, 0.913, 0.867, 1, 0.6, 0.987, 0.867, 1]}, {"time": 1}]}, "Antler_0": {"rotate": [{"curve": [0.017, 0, 0.05, -53.81]}, {"time": 0.0667, "value": -53.81, "curve": [0.1, -53.81, 0.167, 31.58]}, {"time": 0.2, "value": 31.58, "curve": [0.275, 31.58, 0.425, -10.48]}, {"time": 0.5, "value": -10.48, "curve": [0.583, -10.48, 0.75, 10.12]}, {"time": 0.8333, "value": 10.12, "curve": [0.908, 10.12, 1.058, 0]}, {"time": 1.1333}]}, "Main": {"scale": [{"x": 1.05, "y": 0.959, "curve": [0.008, 1.05, 0.025, 1.456, 0.008, 0.959, 0.025, 0.625]}, {"time": 0.0333, "x": 1.456, "y": 0.625, "curve": [0.058, 1.456, 0.108, 0.871, 0.058, 0.625, 0.108, 1.529]}, {"time": 0.1333, "x": 0.871, "y": 1.529, "curve": [0.2, 0.871, 0.333, 1.253, 0.2, 1.529, 0.333, 0.78]}, {"time": 0.4, "x": 1.253, "y": 0.78, "curve": [0.492, 1.253, 0.675, 0.96, 0.492, 0.78, 0.675, 1.054]}, {"time": 0.7667, "x": 0.96, "y": 1.054, "curve": [0.858, 0.96, 1.042, 1.05, 0.858, 1.054, 1.042, 0.959]}, {"time": 1.1333, "x": 1.05, "y": 0.959}]}, "BODY_1": {"scale": [{"x": 1.034, "y": 0.978, "curve": [0.017, 1.034, 0.05, 1.434, 0.017, 0.978, 0.05, 0.638]}, {"time": 0.0667, "x": 1.434, "y": 0.638, "curve": [0.1, 1.434, 0.167, 0.858, 0.1, 0.638, 0.167, 1.56]}, {"time": 0.2, "x": 0.858, "y": 1.56, "curve": [0.267, 0.858, 0.4, 1.234, 0.267, 1.56, 0.4, 0.795]}, {"time": 0.4667, "x": 1.234, "y": 0.795, "curve": [0.558, 1.234, 0.742, 0.946, 0.558, 0.795, 0.742, 1.075]}, {"time": 0.8333, "x": 0.946, "y": 1.075, "curve": [0.908, 0.946, 1.058, 1.034, 0.908, 1.075, 1.058, 0.978]}, {"time": 1.1333, "x": 1.034, "y": 0.978}]}, "BODY_2": {"scale": [{"x": 0.977, "y": 1.047, "curve": [0.025, 0.977, 0.075, 1.356, 0.025, 1.047, 0.075, 0.684]}, {"time": 0.1, "x": 1.356, "y": 0.684, "curve": [0.133, 1.356, 0.2, 0.811, 0.133, 0.684, 0.2, 1.671]}, {"time": 0.2333, "x": 0.811, "y": 1.671, "curve": [0.3, 0.811, 0.433, 1.166, 0.3, 1.671, 0.433, 0.852]}, {"time": 0.5, "x": 1.166, "y": 0.852, "curve": [0.592, 1.166, 0.775, 0.894, 0.592, 0.852, 0.775, 1.152]}, {"time": 0.8667, "x": 0.894, "y": 1.152, "curve": [0.933, 0.894, 1.067, 0.977, 0.933, 1.152, 1.067, 1.047]}, {"time": 1.1333, "x": 0.977, "y": 1.047}]}, "BODY_3": {"scale": [{"x": 0.963, "y": 1.065, "curve": [0.033, 0.963, 0.1, 1.336, 0.033, 1.065, 0.1, 0.695]}, {"time": 0.1333, "x": 1.336, "y": 0.695, "curve": [0.175, 1.336, 0.258, 0.799, 0.175, 0.695, 0.258, 1.699]}, {"time": 0.3, "x": 0.799, "y": 1.699, "curve": [0.367, 0.799, 0.5, 1.149, 0.367, 1.699, 0.5, 0.866]}, {"time": 0.5667, "x": 1.149, "y": 0.866, "curve": [0.658, 1.149, 0.842, 0.881, 0.658, 0.866, 0.842, 1.171]}, {"time": 0.9333, "x": 0.881, "y": 1.171, "curve": [0.983, 0.881, 1.083, 0.963, 0.983, 1.171, 1.083, 1.065]}, {"time": 1.1333, "x": 0.963, "y": 1.065}]}, "Antler_1": {"rotate": [{"curve": [0.017, 0, 0.05, -28.81]}, {"time": 0.0667, "value": -28.81, "curve": [0.1, -28.81, 0.167, 18.63]}, {"time": 0.2, "value": 18.63, "curve": [0.275, 18.63, 0.425, -14.31]}, {"time": 0.5, "value": -14.31, "curve": [0.583, -14.31, 0.75, 6.43]}, {"time": 0.8333, "value": 6.43, "curve": [0.908, 6.43, 1.058, 0]}, {"time": 1.1333}]}, "Antler_4": {"rotate": [{"curve": [0.017, 0, 0.05, 25.06]}, {"time": 0.0667, "value": 25.06, "curve": [0.1, 25.06, 0.167, -16.43]}, {"time": 0.2, "value": -16.43, "curve": [0.275, -16.43, 0.425, 6.76]}, {"time": 0.5, "value": 6.76, "curve": [0.583, 6.76, 0.75, -5.33]}, {"time": 0.8333, "value": -5.33, "curve": [0.908, -5.33, 1.058, 0]}, {"time": 1.1333}]}, "Antler_3": {"rotate": [{"curve": [0.017, 0, 0.05, 58.17]}, {"time": 0.0667, "value": 58.17, "curve": [0.1, 58.17, 0.167, -22.19]}, {"time": 0.2, "value": -22.19, "curve": [0.275, -22.19, 0.425, 13.8]}, {"time": 0.5, "value": 13.8, "curve": [0.583, 13.8, 0.75, -7.7]}, {"time": 0.8333, "value": -7.7, "curve": [0.908, -7.7, 1.058, 0]}, {"time": 1.1333}]}, "Twig2": {"rotate": [{"value": 4.09, "curve": [0.074, -1.03, 0.151, -6.8]}, {"time": 0.2, "value": -6.8, "curve": [0.342, -6.8, 0.625, 12.2]}, {"time": 0.7667, "value": 12.2, "curve": [0.826, 12.2, 0.911, 8.36]}, {"time": 1, "value": 4.09}]}, "Twig3": {"rotate": [{"value": 0.15, "curve": [0.075, 3.32, 0.15, 6.49]}, {"time": 0.2, "value": 6.49, "curve": [0.333, 6.49, 0.6, -6.2]}, {"time": 0.7333, "value": -6.2, "curve": [0.8, -6.2, 0.9, -3.02]}, {"time": 1, "value": 0.15}]}, "warning": {"scale": [{"time": 0.0667, "x": 1.493, "y": 1.493, "curve": [0.083, 1.493, 0.117, 1, 0.083, 1.493, 0.117, 1]}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.9667, "curve": [1.008, 1, 1.133, 0.811, 1.008, 1, 1.133, 0.811]}, {"time": 1.1333, "x": 0.422, "y": 0.422}]}, "MOUTH_BTM": {"translate": [{"curve": [0.042, 0, 0.125, 1.37, 0.042, 0, 0.125, -33.22]}, {"time": 0.1667, "x": 1.37, "y": -33.22, "curve": [0.208, 1.37, 0.292, 2.65, 0.208, -33.22, 0.292, 30.9]}, {"time": 0.3333, "x": 2.65, "y": 30.9, "curve": [0.392, 2.65, 0.508, 0, 0.392, 30.9, 0.508, 0]}, {"time": 0.5667}]}, "HeadFleshball_1": {"scale": [{"x": 1.005, "y": 1.005, "curve": [0.05, 1.037, 0.1, 1.07, 0.05, 1.037, 0.1, 1.07]}, {"time": 0.1333, "x": 1.07, "y": 1.07, "curve": [0.208, 1.07, 0.358, 0.94, 0.208, 1.07, 0.358, 0.94]}, {"time": 0.4333, "x": 0.94, "y": 0.94, "curve": [0.467, 0.94, 0.517, 0.973, 0.467, 0.94, 0.517, 0.973]}, {"time": 0.5667, "x": 1.005, "y": 1.005, "curve": [0.617, 1.037, 0.667, 1.07, 0.617, 1.037, 0.667, 1.07]}, {"time": 0.7, "x": 1.07, "y": 1.07, "curve": [0.775, 1.07, 0.925, 0.94, 0.775, 1.07, 0.925, 0.94]}, {"time": 1, "x": 0.94, "y": 0.94, "curve": [1.033, 0.94, 1.083, 0.973, 1.033, 0.94, 1.083, 0.973]}, {"time": 1.1333, "x": 1.005, "y": 1.005}]}, "HeadFleshball_3": {"scale": [{"x": 1.005, "y": 1.005, "curve": [0.05, 0.973, 0.1, 0.94, 0.05, 0.973, 0.1, 0.94]}, {"time": 0.1333, "x": 0.94, "y": 0.94, "curve": [0.208, 0.94, 0.358, 1.07, 0.208, 0.94, 0.358, 1.07]}, {"time": 0.4333, "x": 1.07, "y": 1.07, "curve": [0.467, 1.07, 0.517, 1.037, 0.467, 1.07, 0.517, 1.037]}, {"time": 0.5667, "x": 1.005, "y": 1.005, "curve": [0.617, 0.973, 0.667, 0.94, 0.617, 0.973, 0.667, 0.94]}, {"time": 0.7, "x": 0.94, "y": 0.94, "curve": [0.775, 0.94, 0.925, 1.07, 0.775, 0.94, 0.925, 1.07]}, {"time": 1, "x": 1.07, "y": 1.07, "curve": [1.033, 1.07, 1.083, 1.037, 1.033, 1.07, 1.083, 1.037]}, {"time": 1.1333, "x": 1.005, "y": 1.005}]}, "HeadFleshball_2": {"scale": [{"x": 1.053, "y": 1.053, "curve": [0.025, 1.063, 0.048, 1.07, 0.025, 1.063, 0.048, 1.07]}, {"time": 0.0667, "x": 1.07, "y": 1.07, "curve": [0.133, 1.07, 0.267, 0.94, 0.133, 1.07, 0.267, 0.94]}, {"time": 0.3333, "x": 0.94, "y": 0.94, "curve": [0.39, 0.94, 0.492, 1.021, 0.39, 0.94, 0.492, 1.021]}, {"time": 0.5667, "x": 1.053, "y": 1.053, "curve": [0.592, 1.063, 0.614, 1.07, 0.592, 1.063, 0.614, 1.07]}, {"time": 0.6333, "x": 1.07, "y": 1.07, "curve": [0.7, 1.07, 0.833, 0.94, 0.7, 1.07, 0.833, 0.94]}, {"time": 0.9, "x": 0.94, "y": 0.94, "curve": [0.957, 0.94, 1.059, 1.021, 0.957, 0.94, 1.059, 1.021]}, {"time": 1.1333, "x": 1.053, "y": 1.053}]}, "Fleshball_7": {"scale": [{"x": 0.938, "y": 0.938, "curve": [0.025, 0.919, 0.048, 0.908, 0.025, 0.919, 0.048, 0.908]}, {"time": 0.0667, "x": 0.908, "y": 0.908, "curve": [0.133, 0.908, 0.267, 1.141, 0.133, 0.908, 0.267, 1.141]}, {"time": 0.3333, "x": 1.141, "y": 1.141, "curve": [0.39, 1.141, 0.492, 0.996, 0.39, 1.141, 0.492, 0.996]}, {"time": 0.5667, "x": 0.938, "y": 0.938, "curve": [0.592, 0.919, 0.614, 0.908, 0.592, 0.919, 0.614, 0.908]}, {"time": 0.6333, "x": 0.908, "y": 0.908, "curve": [0.7, 0.908, 0.833, 1.141, 0.7, 0.908, 0.833, 1.141]}, {"time": 0.9, "x": 1.141, "y": 1.141, "curve": [0.957, 1.141, 1.059, 0.996, 0.957, 1.141, 1.059, 0.996]}, {"time": 1.1333, "x": 0.938, "y": 0.938}]}, "Fleshball_6": {"scale": [{"x": 1.11, "y": 1.11, "curve": [0.075, 1.052, 0.177, 0.908, 0.075, 1.052, 0.177, 0.908]}, {"time": 0.2333, "x": 0.908, "y": 0.908, "curve": [0.3, 0.908, 0.433, 1.141, 0.3, 0.908, 0.433, 1.141]}, {"time": 0.5, "x": 1.141, "y": 1.141, "curve": [0.519, 1.141, 0.542, 1.129, 0.519, 1.141, 0.542, 1.129]}, {"time": 0.5667, "x": 1.11, "y": 1.11, "curve": [0.641, 1.052, 0.743, 0.908, 0.641, 1.052, 0.743, 0.908]}, {"time": 0.8, "x": 0.908, "y": 0.908, "curve": [0.867, 0.908, 1, 1.141, 0.867, 0.908, 1, 1.141]}, {"time": 1.0667, "x": 1.141, "y": 1.141, "curve": [1.086, 1.141, 1.108, 1.129, 1.086, 1.141, 1.108, 1.129]}, {"time": 1.1333, "x": 1.11, "y": 1.11}]}, "Fleshball_8": {"scale": [{"x": 0.993, "y": 0.993, "curve": [0.06, 1.059, 0.126, 1.141, 0.06, 1.059, 0.126, 1.141]}, {"time": 0.1667, "x": 1.141, "y": 1.141, "curve": [0.242, 1.141, 0.392, 0.908, 0.242, 1.141, 0.392, 0.908]}, {"time": 0.4667, "x": 0.908, "y": 0.908, "curve": [0.492, 0.908, 0.529, 0.946, 0.492, 0.908, 0.529, 0.946]}, {"time": 0.5667, "x": 0.993, "y": 0.993, "curve": [0.627, 1.059, 0.692, 1.141, 0.627, 1.059, 0.692, 1.141]}, {"time": 0.7333, "x": 1.141, "y": 1.141, "curve": [0.8, 1.141, 0.933, 0.908, 0.8, 1.141, 0.933, 0.908]}, {"time": 1, "x": 0.908, "y": 0.908, "curve": [1.034, 0.908, 1.082, 0.946, 1.034, 0.908, 1.082, 0.946]}, {"time": 1.1333, "x": 0.993, "y": 0.993}]}, "Fleshball_5": {"scale": [{"x": 1.059, "y": 1.059, "curve": [0.025, 1.07, 0.048, 1.076, 0.025, 1.07, 0.048, 1.076]}, {"time": 0.0667, "x": 1.076, "y": 1.076, "curve": [0.133, 1.076, 0.267, 0.944, 0.133, 1.076, 0.267, 0.944]}, {"time": 0.3333, "x": 0.944, "y": 0.944, "curve": [0.39, 0.944, 0.492, 1.026, 0.39, 0.944, 0.492, 1.026]}, {"time": 0.5667, "x": 1.059, "y": 1.059, "curve": [0.592, 1.07, 0.614, 1.076, 0.592, 1.07, 0.614, 1.076]}, {"time": 0.6333, "x": 1.076, "y": 1.076, "curve": [0.7, 1.076, 0.833, 0.944, 0.7, 1.076, 0.833, 0.944]}, {"time": 0.9, "x": 0.944, "y": 0.944, "curve": [0.957, 0.944, 1.059, 1.026, 0.957, 0.944, 1.059, 1.026]}, {"time": 1.1333, "x": 1.059, "y": 1.059}]}, "Fleshball_4": {"scale": [{"x": 1.044, "y": 1.044, "curve": [0.069, 1.006, 0.151, 0.944, 0.069, 1.006, 0.151, 0.944]}, {"time": 0.2, "x": 0.944, "y": 0.944, "curve": [0.267, 0.944, 0.4, 1.076, 0.267, 0.944, 0.4, 1.076]}, {"time": 0.4667, "x": 1.076, "y": 1.076, "curve": [0.494, 1.076, 0.528, 1.063, 0.494, 1.076, 0.528, 1.063]}, {"time": 0.5667, "x": 1.044, "y": 1.044, "curve": [0.636, 1.006, 0.718, 0.944, 0.636, 1.006, 0.718, 0.944]}, {"time": 0.7667, "x": 0.944, "y": 0.944, "curve": [0.833, 0.944, 0.967, 1.076, 0.833, 0.944, 0.967, 1.076]}, {"time": 1.0333, "x": 1.076, "y": 1.076, "curve": [1.06, 1.076, 1.095, 1.063, 1.06, 1.076, 1.095, 1.063]}, {"time": 1.1333, "x": 1.044, "y": 1.044}]}, "Fleshball_3": {"scale": [{"x": 0.992, "y": 0.992, "curve": [0.038, 0.966, 0.074, 0.944, 0.038, 0.966, 0.074, 0.944]}, {"time": 0.1, "x": 0.944, "y": 0.944, "curve": [0.175, 0.944, 0.325, 1.076, 0.175, 0.944, 0.325, 1.076]}, {"time": 0.4, "x": 1.076, "y": 1.076, "curve": [0.441, 1.076, 0.506, 1.03, 0.441, 1.076, 0.506, 1.03]}, {"time": 0.5667, "x": 0.992, "y": 0.992, "curve": [0.605, 0.966, 0.641, 0.944, 0.605, 0.966, 0.641, 0.944]}, {"time": 0.6667, "x": 0.944, "y": 0.944, "curve": [0.742, 0.944, 0.892, 1.076, 0.742, 0.944, 0.892, 1.076]}, {"time": 0.9667, "x": 1.076, "y": 1.076, "curve": [1.008, 1.076, 1.073, 1.03, 1.008, 1.076, 1.073, 1.03]}, {"time": 1.1333, "x": 0.992, "y": 0.992}]}, "Fleshball_1": {"scale": [{"x": 1.044, "y": 1.044, "curve": [0.069, 1.006, 0.151, 0.944, 0.069, 1.006, 0.151, 0.944]}, {"time": 0.2, "x": 0.944, "y": 0.944, "curve": [0.267, 0.944, 0.4, 1.076, 0.267, 0.944, 0.4, 1.076]}, {"time": 0.4667, "x": 1.076, "y": 1.076, "curve": [0.494, 1.076, 0.528, 1.063, 0.494, 1.076, 0.528, 1.063]}, {"time": 0.5667, "x": 1.044, "y": 1.044, "curve": [0.636, 1.006, 0.718, 0.944, 0.636, 1.006, 0.718, 0.944]}, {"time": 0.7667, "x": 0.944, "y": 0.944, "curve": [0.833, 0.944, 0.967, 1.076, 0.833, 0.944, 0.967, 1.076]}, {"time": 1.0333, "x": 1.076, "y": 1.076, "curve": [1.06, 1.076, 1.095, 1.063, 1.06, 1.076, 1.095, 1.063]}, {"time": 1.1333, "x": 1.044, "y": 1.044}]}, "Fleshball_2": {"scale": [{"x": 0.992, "y": 0.992, "curve": [0.038, 0.966, 0.074, 0.944, 0.038, 0.966, 0.074, 0.944]}, {"time": 0.1, "x": 0.944, "y": 0.944, "curve": [0.175, 0.944, 0.325, 1.076, 0.175, 0.944, 0.325, 1.076]}, {"time": 0.4, "x": 1.076, "y": 1.076, "curve": [0.441, 1.076, 0.506, 1.03, 0.441, 1.076, 0.506, 1.03]}, {"time": 0.5667, "x": 0.992, "y": 0.992, "curve": [0.605, 0.966, 0.641, 0.944, 0.605, 0.966, 0.641, 0.944]}, {"time": 0.6667, "x": 0.944, "y": 0.944, "curve": [0.742, 0.944, 0.892, 1.076, 0.742, 0.944, 0.892, 1.076]}, {"time": 0.9667, "x": 1.076, "y": 1.076, "curve": [1.008, 1.076, 1.073, 1.03, 1.008, 1.076, 1.073, 1.03]}, {"time": 1.1333, "x": 0.992, "y": 0.992}]}, "Fleshball_9": {"scale": [{"x": 0.999, "y": 0.999}, {"time": 0.1333, "x": 0.897, "y": 0.897}, {"time": 0.4333, "x": 1.101, "y": 1.101}, {"time": 0.5667, "x": 0.999, "y": 0.999}, {"time": 0.7, "x": 0.897, "y": 0.897}, {"time": 1, "x": 1.101, "y": 1.101}, {"time": 1.1333, "x": 0.999, "y": 0.999}]}, "MOUTH": {"scale": [{"y": 0.713}, {"time": 0.1667, "x": 1.509, "y": 1.174}, {"time": 0.3333}]}, "HeadFleshball_1_Behind": {"scale": [{"x": 1.005, "y": 1.005, "curve": [0.05, 1.037, 0.1, 1.07, 0.05, 1.037, 0.1, 1.07]}, {"time": 0.1333, "x": 1.07, "y": 1.07, "curve": [0.208, 1.07, 0.358, 0.94, 0.208, 1.07, 0.358, 0.94]}, {"time": 0.4333, "x": 0.94, "y": 0.94, "curve": [0.467, 0.94, 0.517, 0.973, 0.467, 0.94, 0.517, 0.973]}, {"time": 0.5667, "x": 1.005, "y": 1.005, "curve": [0.617, 1.037, 0.667, 1.07, 0.617, 1.037, 0.667, 1.07]}, {"time": 0.7, "x": 1.07, "y": 1.07, "curve": [0.775, 1.07, 0.925, 0.94, 0.775, 1.07, 0.925, 0.94]}, {"time": 1, "x": 0.94, "y": 0.94, "curve": [1.033, 0.94, 1.083, 0.973, 1.033, 0.94, 1.083, 0.973]}, {"time": 1.1333, "x": 1.005, "y": 1.005}]}, "HeadFleshball_3_Behind": {"scale": [{"x": 1.005, "y": 1.005, "curve": [0.05, 0.973, 0.1, 0.94, 0.05, 0.973, 0.1, 0.94]}, {"time": 0.1333, "x": 0.94, "y": 0.94, "curve": [0.208, 0.94, 0.358, 1.07, 0.208, 0.94, 0.358, 1.07]}, {"time": 0.4333, "x": 1.07, "y": 1.07, "curve": [0.467, 1.07, 0.517, 1.037, 0.467, 1.07, 0.517, 1.037]}, {"time": 0.5667, "x": 1.005, "y": 1.005, "curve": [0.617, 0.973, 0.667, 0.94, 0.617, 0.973, 0.667, 0.94]}, {"time": 0.7, "x": 0.94, "y": 0.94, "curve": [0.775, 0.94, 0.925, 1.07, 0.775, 0.94, 0.925, 1.07]}, {"time": 1, "x": 1.07, "y": 1.07, "curve": [1.033, 1.07, 1.083, 1.037, 1.033, 1.07, 1.083, 1.037]}, {"time": 1.1333, "x": 1.005, "y": 1.005}]}, "MOUTH_CHASER": {"scale": [{"y": 0.713}, {"time": 0.1667, "x": 1.509, "y": 1.174}, {"time": 0.3333}]}}}, "roar": {"slots": {"Eyes": {"attachment": [{"time": 0.2, "name": "Patroller,Chaser/Chaser_images/Eyes_Shut"}]}, "EYE_LEFT_BTM": {"attachment": [{"time": 0.2, "name": "<PERSON>ler,<PERSON>r/Pouncer <PERSON>/Eye_Shut"}, {"time": 0.5667, "name": "<PERSON>ler,<PERSON><PERSON>/<PERSON>uncer <PERSON>/Eye_Shocked"}, {"time": 1.7, "name": "<PERSON>ler,<PERSON>r/Pouncer Boss/Eye_Normal"}]}, "EYE_LEFT_TOP": {"attachment": [{"time": 0.2, "name": "<PERSON>ler,<PERSON>r/Pouncer <PERSON>/Eye_Shut"}, {"time": 0.5667, "name": "<PERSON>ler,<PERSON><PERSON>/<PERSON>uncer <PERSON>/Eye_Shocked"}, {"time": 1.7, "name": "<PERSON>ler,<PERSON>r/Pouncer Boss/Eye_Normal"}]}, "EYE_RIGHT_BTM": {"attachment": [{"time": 0.2, "name": "<PERSON>ler,<PERSON>r/Pouncer <PERSON>/Eye_Shut"}, {"time": 0.5667, "name": "<PERSON>ler,<PERSON><PERSON>/<PERSON>uncer <PERSON>/Eye_Shocked"}, {"time": 1.7, "name": "<PERSON>ler,<PERSON>r/Pouncer Boss/Eye_Normal"}]}, "EYE_RIGHT_TOP": {"attachment": [{"time": 0.2, "name": "<PERSON>ler,<PERSON>r/Pouncer <PERSON>/Eye_Shut"}, {"time": 0.5667, "name": "<PERSON>ler,<PERSON><PERSON>/<PERSON>uncer <PERSON>/Eye_Shocked"}, {"time": 1.7, "name": "<PERSON>ler,<PERSON>r/Pouncer Boss/Eye_Normal"}]}, "Patroller,Chaser/Pouncer Boss/Mouth": {"attachment": [{"time": 0.1, "name": "Mouth_shut"}, {"time": 0.5667, "name": "Mouth"}, {"time": 1.7667, "name": "Mouth_shut"}, {"time": 1.9333, "name": "Mouth"}]}, "TurretMouth": {"attachment": [{"time": 0.2, "name": "Patroller,Chaser/Chaser Boss/Tunneler_Mouth_Closed"}, {"time": 0.5, "name": "Patroller,Chaser/Chaser Boss/Tunneler_Mouth_Wide"}, {"time": 0.5667, "name": "Patroller,Chaser/Chaser Boss/Tunneler_Mouth"}, {"time": 0.6333, "name": "Patroller,Chaser/Chaser Boss/Tunneler_Mouth_Wide"}, {"time": 0.7, "name": "Patroller,Chaser/Chaser Boss/Tunneler_Mouth"}, {"time": 0.7667, "name": "Patroller,Chaser/Chaser Boss/Tunneler_Mouth_Wide"}, {"time": 0.8333, "name": "Patroller,Chaser/Chaser Boss/Tunneler_Mouth"}, {"time": 0.9, "name": "Patroller,Chaser/Chaser Boss/Tunneler_Mouth_Wide"}, {"time": 0.9667, "name": "Patroller,Chaser/Chaser Boss/Tunneler_Mouth"}, {"time": 1.0333, "name": "Patroller,Chaser/Chaser Boss/Tunneler_Mouth_Wide"}, {"time": 1.1, "name": "Patroller,Chaser/Chaser Boss/Tunneler_Mouth"}, {"time": 1.1667, "name": "Patroller,Chaser/Chaser Boss/Tunneler_Mouth_Wide"}, {"time": 1.2333, "name": "Patroller,Chaser/Chaser Boss/Tunneler_Mouth"}, {"time": 1.3, "name": "Patroller,Chaser/Chaser Boss/Tunneler_Mouth_Wide"}, {"time": 1.3667, "name": "Patroller,Chaser/Chaser Boss/Tunneler_Mouth"}, {"time": 1.4333, "name": "Patroller,Chaser/Chaser Boss/Tunneler_Mouth_Wide"}, {"time": 1.5, "name": "Patroller,Chaser/Chaser Boss/Tunneler_Mouth"}, {"time": 1.5667, "name": "Patroller,Chaser/Chaser Boss/Tunneler_Mouth_Wide"}, {"time": 1.6333, "name": "Patroller,Chaser/Chaser Boss/Tunneler_Mouth"}, {"time": 1.7, "name": "Patroller,Chaser/Chaser Boss/Tunneler_Mouth_Wide"}, {"time": 1.7667, "name": "Patroller,Chaser/Chaser Boss/Tunneler_Mouth_Closed"}]}}, "bones": {"EYE_LEFT_BTM": {"scale": [{"time": 0.5667, "curve": [0.575, 1, 0.592, 0.865, 0.575, 1, 0.592, 0.865]}, {"time": 0.6, "x": 0.865, "y": 0.865, "curve": [0.625, 0.865, 0.675, 1.062, 0.625, 0.865, 0.675, 1.062]}, {"time": 0.7, "x": 1.062, "y": 1.062, "curve": [0.725, 1.062, 0.775, 0.865, 0.725, 1.062, 0.775, 0.865]}, {"time": 0.8, "x": 0.865, "y": 0.865, "curve": [0.825, 0.865, 0.875, 1.062, 0.825, 0.865, 0.875, 1.062]}, {"time": 0.9, "x": 1.062, "y": 1.062, "curve": [0.925, 1.062, 0.975, 0.865, 0.925, 1.062, 0.975, 0.865]}, {"time": 1, "x": 0.865, "y": 0.865, "curve": [1.025, 0.865, 1.075, 1.062, 1.025, 0.865, 1.075, 1.062]}, {"time": 1.1, "x": 1.062, "y": 1.062, "curve": [1.125, 1.062, 1.175, 0.865, 1.125, 1.062, 1.175, 0.865]}, {"time": 1.2, "x": 0.865, "y": 0.865, "curve": [1.225, 0.865, 1.275, 1.062, 1.225, 0.865, 1.275, 1.062]}, {"time": 1.3, "x": 1.062, "y": 1.062, "curve": [1.325, 1.062, 1.375, 0.865, 1.325, 1.062, 1.375, 0.865]}, {"time": 1.4, "x": 0.865, "y": 0.865, "curve": [1.425, 0.865, 1.475, 1.062, 1.425, 0.865, 1.475, 1.062]}, {"time": 1.5, "x": 1.062, "y": 1.062, "curve": [1.525, 1.062, 1.575, 0.865, 1.525, 1.062, 1.575, 0.865]}, {"time": 1.6, "x": 0.865, "y": 0.865, "curve": [1.616, 0.865, 1.646, 0.844, 1.616, 0.865, 1.646, 0.844]}, {"time": 1.6667, "x": 0.838, "y": 0.838, "curve": [1.679, 0.939, 1.69, 1, 1.679, 0.939, 1.69, 1]}, {"time": 1.7}]}, "EYE_LEFT_TOP": {"scale": [{"time": 0.5667, "curve": [0.575, 1, 0.592, 0.865, 0.575, 1, 0.592, 0.865]}, {"time": 0.6, "x": 0.865, "y": 0.865, "curve": [0.625, 0.865, 0.675, 1.062, 0.625, 0.865, 0.675, 1.062]}, {"time": 0.7, "x": 1.062, "y": 1.062, "curve": [0.725, 1.062, 0.775, 0.865, 0.725, 1.062, 0.775, 0.865]}, {"time": 0.8, "x": 0.865, "y": 0.865, "curve": [0.825, 0.865, 0.875, 1.062, 0.825, 0.865, 0.875, 1.062]}, {"time": 0.9, "x": 1.062, "y": 1.062, "curve": [0.925, 1.062, 0.975, 0.865, 0.925, 1.062, 0.975, 0.865]}, {"time": 1, "x": 0.865, "y": 0.865, "curve": [1.025, 0.865, 1.075, 1.062, 1.025, 0.865, 1.075, 1.062]}, {"time": 1.1, "x": 1.062, "y": 1.062, "curve": [1.125, 1.062, 1.175, 0.865, 1.125, 1.062, 1.175, 0.865]}, {"time": 1.2, "x": 0.865, "y": 0.865, "curve": [1.225, 0.865, 1.275, 1.062, 1.225, 0.865, 1.275, 1.062]}, {"time": 1.3, "x": 1.062, "y": 1.062, "curve": [1.325, 1.062, 1.375, 0.865, 1.325, 1.062, 1.375, 0.865]}, {"time": 1.4, "x": 0.865, "y": 0.865, "curve": [1.425, 0.865, 1.475, 1.062, 1.425, 0.865, 1.475, 1.062]}, {"time": 1.5, "x": 1.062, "y": 1.062, "curve": [1.525, 1.062, 1.575, 0.865, 1.525, 1.062, 1.575, 0.865]}, {"time": 1.6, "x": 0.865, "y": 0.865, "curve": [1.616, 0.865, 1.646, 0.844, 1.616, 0.865, 1.646, 0.844]}, {"time": 1.6667, "x": 0.838, "y": 0.838, "curve": [1.679, 0.939, 1.69, 1, 1.679, 0.939, 1.69, 1]}, {"time": 1.7}]}, "EYE_RIGHT_BTM": {"scale": [{"time": 0.5667, "curve": [0.575, 1, 0.592, 0.865, 0.575, 1, 0.592, 0.865]}, {"time": 0.6, "x": 0.865, "y": 0.865, "curve": [0.625, 0.865, 0.675, 1.062, 0.625, 0.865, 0.675, 1.062]}, {"time": 0.7, "x": 1.062, "y": 1.062, "curve": [0.725, 1.062, 0.775, 0.865, 0.725, 1.062, 0.775, 0.865]}, {"time": 0.8, "x": 0.865, "y": 0.865, "curve": [0.825, 0.865, 0.875, 1.062, 0.825, 0.865, 0.875, 1.062]}, {"time": 0.9, "x": 1.062, "y": 1.062, "curve": [0.925, 1.062, 0.975, 0.865, 0.925, 1.062, 0.975, 0.865]}, {"time": 1, "x": 0.865, "y": 0.865, "curve": [1.025, 0.865, 1.075, 1.062, 1.025, 0.865, 1.075, 1.062]}, {"time": 1.1, "x": 1.062, "y": 1.062, "curve": [1.125, 1.062, 1.175, 0.865, 1.125, 1.062, 1.175, 0.865]}, {"time": 1.2, "x": 0.865, "y": 0.865, "curve": [1.225, 0.865, 1.275, 1.062, 1.225, 0.865, 1.275, 1.062]}, {"time": 1.3, "x": 1.062, "y": 1.062, "curve": [1.325, 1.062, 1.375, 0.865, 1.325, 1.062, 1.375, 0.865]}, {"time": 1.4, "x": 0.865, "y": 0.865, "curve": [1.425, 0.865, 1.475, 1.062, 1.425, 0.865, 1.475, 1.062]}, {"time": 1.5, "x": 1.062, "y": 1.062, "curve": [1.525, 1.062, 1.575, 0.865, 1.525, 1.062, 1.575, 0.865]}, {"time": 1.6, "x": 0.865, "y": 0.865, "curve": [1.616, 0.865, 1.646, 0.844, 1.616, 0.865, 1.646, 0.844]}, {"time": 1.6667, "x": 0.838, "y": 0.838, "curve": [1.679, 0.939, 1.69, 1, 1.679, 0.939, 1.69, 1]}, {"time": 1.7}]}, "EYE_RIGHT_TOP": {"scale": [{"time": 0.5667, "curve": [0.575, 1, 0.592, 0.865, 0.575, 1, 0.592, 0.865]}, {"time": 0.6, "x": 0.865, "y": 0.865, "curve": [0.625, 0.865, 0.675, 1.062, 0.625, 0.865, 0.675, 1.062]}, {"time": 0.7, "x": 1.062, "y": 1.062, "curve": [0.725, 1.062, 0.775, 0.865, 0.725, 1.062, 0.775, 0.865]}, {"time": 0.8, "x": 0.865, "y": 0.865, "curve": [0.825, 0.865, 0.875, 1.062, 0.825, 0.865, 0.875, 1.062]}, {"time": 0.9, "x": 1.062, "y": 1.062, "curve": [0.925, 1.062, 0.975, 0.865, 0.925, 1.062, 0.975, 0.865]}, {"time": 1, "x": 0.865, "y": 0.865, "curve": [1.025, 0.865, 1.075, 1.062, 1.025, 0.865, 1.075, 1.062]}, {"time": 1.1, "x": 1.062, "y": 1.062, "curve": [1.125, 1.062, 1.175, 0.865, 1.125, 1.062, 1.175, 0.865]}, {"time": 1.2, "x": 0.865, "y": 0.865, "curve": [1.225, 0.865, 1.275, 1.062, 1.225, 0.865, 1.275, 1.062]}, {"time": 1.3, "x": 1.062, "y": 1.062, "curve": [1.325, 1.062, 1.375, 0.865, 1.325, 1.062, 1.375, 0.865]}, {"time": 1.4, "x": 0.865, "y": 0.865, "curve": [1.425, 0.865, 1.475, 1.062, 1.425, 0.865, 1.475, 1.062]}, {"time": 1.5, "x": 1.062, "y": 1.062, "curve": [1.525, 1.062, 1.575, 0.865, 1.525, 1.062, 1.575, 0.865]}, {"time": 1.6, "x": 0.865, "y": 0.865, "curve": [1.616, 0.865, 1.646, 0.844, 1.616, 0.865, 1.646, 0.844]}, {"time": 1.6667, "x": 0.838, "y": 0.838, "curve": [1.679, 0.939, 1.69, 1, 1.679, 0.939, 1.69, 1]}, {"time": 1.7}]}, "FACE": {"translate": [{"curve": [0.025, 0, 0.075, 19.44, 0.025, 0, 0.075, 0]}, {"time": 0.1, "x": 19.44, "curve": [0.125, 19.44, 0.175, -20.53, 0.125, 0, 0.175, 0]}, {"time": 0.2, "x": -20.53, "curve": [0.217, -20.53, 0.25, 0.58, 0.217, 0, 0.25, 0]}, {"time": 0.2667, "x": 0.58, "curve": [0.283, 0.58, 0.317, 0, 0.283, 0, 0.317, 0]}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667, "curve": [0.583, 0, 0.617, 7.13, 0.583, 0, 0.617, 0]}, {"time": 0.6333, "x": 7.13, "curve": [0.65, 7.13, 0.683, -4.75, 0.65, 0, 0.683, 0]}, {"time": 0.7, "x": -4.75, "curve": [0.717, -4.75, 0.75, 7.13, 0.717, 0, 0.75, 0]}, {"time": 0.7667, "x": 7.13, "curve": [0.783, 7.13, 0.817, -4.75, 0.783, 0, 0.817, 0]}, {"time": 0.8333, "x": -4.75, "curve": [0.85, -4.75, 0.883, 7.13, 0.85, 0, 0.883, 0]}, {"time": 0.9, "x": 7.13, "curve": [0.917, 7.13, 0.95, -4.75, 0.917, 0, 0.95, 0]}, {"time": 0.9667, "x": -4.75, "curve": [0.983, -4.75, 1.017, 7.13, 0.983, 0, 1.017, 0]}, {"time": 1.0333, "x": 7.13, "curve": [1.05, 7.13, 1.083, -4.75, 1.05, 0, 1.083, 0]}, {"time": 1.1, "x": -4.75, "curve": [1.117, -4.75, 1.15, 7.13, 1.117, 0, 1.15, 0]}, {"time": 1.1667, "x": 7.13, "curve": [1.183, 7.13, 1.217, -4.75, 1.183, 0, 1.217, 0]}, {"time": 1.2333, "x": -4.75, "curve": [1.25, -4.75, 1.283, 7.13, 1.25, 0, 1.283, 0]}, {"time": 1.3, "x": 7.13, "curve": [1.317, 7.13, 1.35, -4.75, 1.317, 0, 1.35, 0]}, {"time": 1.3667, "x": -4.75, "curve": [1.383, -4.75, 1.417, 7.13, 1.383, 0, 1.417, 0]}, {"time": 1.4333, "x": 7.13, "curve": [1.45, 7.13, 1.483, -4.75, 1.45, 0, 1.483, 0]}, {"time": 1.5, "x": -4.75, "curve": [1.517, -4.75, 1.55, 7.13, 1.517, 0, 1.55, 0]}, {"time": 1.5667, "x": 7.13, "curve": [1.583, 7.13, 1.617, -4.75, 1.583, 0, 1.617, 0]}, {"time": 1.6333, "x": -4.75, "curve": [1.65, -4.75, 1.683, 7.13, 1.65, 0, 1.683, 0]}, {"time": 1.7, "x": 7.13, "curve": [1.717, 7.13, 1.75, -4.75, 1.717, 0, 1.75, 0]}, {"time": 1.7667, "x": -4.75, "curve": [1.783, -4.75, 1.817, 7.13, 1.783, 0, 1.817, 0]}, {"time": 1.8333, "x": 7.13, "curve": [1.85, 7.13, 1.883, -4.75, 1.85, 0, 1.883, 0]}, {"time": 1.9, "x": -4.75, "curve": [1.917, -4.75, 1.95, 7.13, 1.917, 0, 1.95, 0]}, {"time": 1.9667, "x": 7.13, "curve": [1.975, 7.13, 1.992, 0, 1.975, 0, 1.992, 0]}, {"time": 2}]}, "MOUTH": {"scale": [{"y": 0.955, "curve": [0.022, 1, 0.048, 1, 0.022, 0.859, 0.048, 0.709]}, {"time": 0.0667, "y": 0.662, "curve": "stepped"}, {"time": 0.1, "y": 1.152, "curve": "stepped"}, {"time": 0.5333, "y": 1.152, "curve": "stepped"}, {"time": 0.5667, "y": 0.662, "curve": [0.575, 1, 0.592, 1.12, 0.575, 0.662, 0.592, 1.263]}, {"time": 0.6, "x": 1.12, "y": 1.263, "curve": [0.608, 1.12, 0.625, 1.12, 0.608, 1.263, 0.625, 1.041]}, {"time": 0.6333, "x": 1.12, "y": 1.041, "curve": [0.65, 1.12, 0.683, 1.12, 0.65, 1.041, 0.683, 1.263]}, {"time": 0.7, "x": 1.12, "y": 1.263, "curve": [0.717, 1.12, 0.75, 1.12, 0.717, 1.263, 0.75, 1.041]}, {"time": 0.7667, "x": 1.12, "y": 1.041, "curve": [0.783, 1.12, 0.817, 1.12, 0.783, 1.041, 0.817, 1.263]}, {"time": 0.8333, "x": 1.12, "y": 1.263, "curve": [0.85, 1.12, 0.883, 1.12, 0.85, 1.263, 0.883, 1.041]}, {"time": 0.9, "x": 1.12, "y": 1.041, "curve": [0.917, 1.12, 0.95, 1.12, 0.917, 1.041, 0.95, 1.263]}, {"time": 0.9667, "x": 1.12, "y": 1.263, "curve": [0.983, 1.12, 1.017, 1.12, 0.983, 1.263, 1.017, 1.041]}, {"time": 1.0333, "x": 1.12, "y": 1.041, "curve": [1.05, 1.12, 1.083, 1.12, 1.05, 1.041, 1.083, 1.263]}, {"time": 1.1, "x": 1.12, "y": 1.263, "curve": [1.117, 1.12, 1.15, 1.12, 1.117, 1.263, 1.15, 1.041]}, {"time": 1.1667, "x": 1.12, "y": 1.041, "curve": [1.183, 1.12, 1.217, 1.12, 1.183, 1.041, 1.217, 1.263]}, {"time": 1.2333, "x": 1.12, "y": 1.263, "curve": [1.25, 1.12, 1.283, 1.12, 1.25, 1.263, 1.283, 1.041]}, {"time": 1.3, "x": 1.12, "y": 1.041, "curve": [1.317, 1.12, 1.35, 1.12, 1.317, 1.041, 1.35, 1.263]}, {"time": 1.3667, "x": 1.12, "y": 1.263, "curve": [1.383, 1.12, 1.417, 1.12, 1.383, 1.263, 1.417, 1.041]}, {"time": 1.4333, "x": 1.12, "y": 1.041, "curve": [1.45, 1.12, 1.483, 1.12, 1.45, 1.041, 1.483, 1.263]}, {"time": 1.5, "x": 1.12, "y": 1.263, "curve": [1.517, 1.12, 1.55, 1.12, 1.517, 1.263, 1.55, 1.041]}, {"time": 1.5667, "x": 1.12, "y": 1.041, "curve": [1.592, 1.12, 1.642, 1, 1.592, 1.041, 1.642, 0.955]}, {"time": 1.6667, "y": 0.955, "curve": [1.689, 1, 1.715, 1, 1.689, 0.859, 1.715, 0.709]}, {"time": 1.7333, "y": 0.662, "curve": "stepped"}, {"time": 1.7667, "y": 1.152, "curve": "stepped"}, {"time": 1.9, "y": 1.152, "curve": "stepped"}, {"time": 1.9333, "y": 0.662, "curve": [1.95, 1, 1.983, 1, 1.95, 0.662, 1.983, 0.955]}, {"time": 2, "y": 0.955}]}, "Main": {"scale": [{"x": 1.05, "y": 0.959, "curve": [0.125, 1.05, 0.183, 1.331, 0.125, 0.959, 0.183, 0.778]}, {"time": 0.5, "x": 1.331, "y": 0.778, "curve": [0.525, 1.331, 0.575, 0.964, 0.525, 0.778, 0.575, 1.017]}, {"time": 0.6, "x": 0.964, "y": 1.017, "curve": [0.617, 0.964, 0.65, 1.05, 0.617, 1.017, 0.65, 0.901]}, {"time": 0.6667, "x": 1.05, "y": 0.901, "curve": [0.683, 1.05, 0.717, 0.964, 0.683, 0.901, 0.717, 1.017]}, {"time": 0.7333, "x": 0.964, "y": 1.017, "curve": [0.75, 0.964, 0.783, 1.05, 0.75, 1.017, 0.783, 0.901]}, {"time": 0.8, "x": 1.05, "y": 0.901, "curve": [0.817, 1.05, 0.85, 0.964, 0.817, 0.901, 0.85, 1.017]}, {"time": 0.8667, "x": 0.964, "y": 1.017, "curve": [0.883, 0.964, 0.917, 1.05, 0.883, 1.017, 0.917, 0.901]}, {"time": 0.9333, "x": 1.05, "y": 0.901, "curve": [0.95, 1.05, 0.983, 0.964, 0.95, 0.901, 0.983, 1.017]}, {"time": 1, "x": 0.964, "y": 1.017, "curve": [1.017, 0.964, 1.05, 1.05, 1.017, 1.017, 1.05, 0.901]}, {"time": 1.0667, "x": 1.05, "y": 0.901, "curve": [1.083, 1.05, 1.117, 0.964, 1.083, 0.901, 1.117, 1.017]}, {"time": 1.1333, "x": 0.964, "y": 1.017, "curve": [1.15, 0.964, 1.183, 1.05, 1.15, 1.017, 1.183, 0.901]}, {"time": 1.2, "x": 1.05, "y": 0.901, "curve": [1.217, 1.05, 1.25, 0.964, 1.217, 0.901, 1.25, 1.017]}, {"time": 1.2667, "x": 0.964, "y": 1.017, "curve": [1.283, 0.964, 1.317, 1.05, 1.283, 1.017, 1.317, 0.901]}, {"time": 1.3333, "x": 1.05, "y": 0.901, "curve": [1.35, 1.05, 1.383, 0.964, 1.35, 0.901, 1.383, 1.017]}, {"time": 1.4, "x": 0.964, "y": 1.017, "curve": [1.417, 0.964, 1.45, 1.05, 1.417, 1.017, 1.45, 0.901]}, {"time": 1.4667, "x": 1.05, "y": 0.901, "curve": [1.483, 1.05, 1.517, 0.964, 1.483, 0.901, 1.517, 1.017]}, {"time": 1.5333, "x": 0.964, "y": 1.017, "curve": [1.55, 0.964, 1.583, 1.05, 1.55, 1.017, 1.583, 0.901]}, {"time": 1.6, "x": 1.05, "y": 0.901, "curve": [1.617, 1.05, 1.65, 0.964, 1.617, 0.901, 1.65, 1.017]}, {"time": 1.6667, "x": 0.964, "y": 1.017, "curve": [1.708, 0.964, 1.792, 1.175, 1.708, 1.017, 1.792, 0.847]}, {"time": 1.8333, "x": 1.175, "y": 0.847, "curve": [1.875, 1.175, 1.958, 1.05, 1.875, 0.847, 1.958, 0.959]}, {"time": 2, "x": 1.05, "y": 0.959}]}, "BODY_1": {"scale": [{"x": 1.034, "y": 0.978, "curve": [0.125, 1.034, 0.21, 1.311, 0.125, 0.978, 0.21, 0.794]}, {"time": 0.5, "x": 1.311, "y": 0.794, "curve": [0.525, 1.311, 0.575, 0.95, 0.525, 0.794, 0.575, 1.037]}, {"time": 0.6, "x": 0.95, "y": 1.037, "curve": [0.617, 0.95, 0.65, 1.034, 0.617, 1.037, 0.65, 0.978]}, {"time": 0.6667, "x": 1.034, "y": 0.978, "curve": [0.683, 1.034, 0.717, 0.95, 0.683, 0.978, 0.717, 1.037]}, {"time": 0.7333, "x": 0.95, "y": 1.037, "curve": [0.75, 0.95, 0.783, 1.034, 0.75, 1.037, 0.783, 0.978]}, {"time": 0.8, "x": 1.034, "y": 0.978, "curve": [0.817, 1.034, 0.85, 0.95, 0.817, 0.978, 0.85, 1.037]}, {"time": 0.8667, "x": 0.95, "y": 1.037, "curve": [0.883, 0.95, 0.917, 1.034, 0.883, 1.037, 0.917, 0.978]}, {"time": 0.9333, "x": 1.034, "y": 0.978, "curve": [0.95, 1.034, 0.983, 0.95, 0.95, 0.978, 0.983, 1.037]}, {"time": 1, "x": 0.95, "y": 1.037, "curve": [1.017, 0.95, 1.05, 1.034, 1.017, 1.037, 1.05, 0.978]}, {"time": 1.0667, "x": 1.034, "y": 0.978, "curve": [1.083, 1.034, 1.117, 0.95, 1.083, 0.978, 1.117, 1.037]}, {"time": 1.1333, "x": 0.95, "y": 1.037, "curve": [1.15, 0.95, 1.183, 1.034, 1.15, 1.037, 1.183, 0.978]}, {"time": 1.2, "x": 1.034, "y": 0.978, "curve": [1.217, 1.034, 1.25, 0.95, 1.217, 0.978, 1.25, 1.037]}, {"time": 1.2667, "x": 0.95, "y": 1.037, "curve": [1.283, 0.95, 1.317, 1.034, 1.283, 1.037, 1.317, 0.978]}, {"time": 1.3333, "x": 1.034, "y": 0.978, "curve": [1.35, 1.034, 1.383, 0.95, 1.35, 0.978, 1.383, 1.037]}, {"time": 1.4, "x": 0.95, "y": 1.037, "curve": [1.417, 0.95, 1.45, 1.034, 1.417, 1.037, 1.45, 0.978]}, {"time": 1.4667, "x": 1.034, "y": 0.978, "curve": [1.483, 1.034, 1.517, 0.95, 1.483, 0.978, 1.517, 1.037]}, {"time": 1.5333, "x": 0.95, "y": 1.037, "curve": [1.55, 0.95, 1.583, 1.034, 1.55, 1.037, 1.583, 0.978]}, {"time": 1.6, "x": 1.034, "y": 0.978, "curve": [1.617, 1.034, 1.65, 0.95, 1.617, 0.978, 1.65, 1.037]}, {"time": 1.6667, "x": 0.95, "y": 1.037, "curve": [1.683, 0.95, 1.717, 1.034, 1.683, 1.037, 1.717, 0.978]}, {"time": 1.7333, "x": 1.034, "y": 0.978, "curve": [1.75, 1.034, 1.783, 0.95, 1.75, 0.978, 1.783, 1.037]}, {"time": 1.8, "x": 0.95, "y": 1.037, "curve": [1.85, 0.95, 1.95, 1.034, 1.85, 1.037, 1.95, 0.978]}, {"time": 2, "x": 1.034, "y": 0.978}]}, "BODY_2": {"scale": [{"x": 0.977, "y": 1.047, "curve": [0.125, 0.977, 0.21, 1.239, 0.125, 1.047, 0.21, 0.85]}, {"time": 0.5, "x": 1.239, "y": 0.85, "curve": [0.525, 1.239, 0.575, 0.898, 0.525, 0.85, 0.575, 1.111]}, {"time": 0.6, "x": 0.898, "y": 1.111, "curve": [0.617, 0.898, 0.65, 0.977, 0.617, 1.111, 0.65, 1.047]}, {"time": 0.6667, "x": 0.977, "y": 1.047, "curve": [0.683, 0.977, 0.717, 0.898, 0.683, 1.047, 0.717, 1.111]}, {"time": 0.7333, "x": 0.898, "y": 1.111, "curve": [0.75, 0.898, 0.783, 0.977, 0.75, 1.111, 0.783, 1.047]}, {"time": 0.8, "x": 0.977, "y": 1.047, "curve": [0.817, 0.977, 0.85, 0.898, 0.817, 1.047, 0.85, 1.111]}, {"time": 0.8667, "x": 0.898, "y": 1.111, "curve": [0.883, 0.898, 0.917, 0.977, 0.883, 1.111, 0.917, 1.047]}, {"time": 0.9333, "x": 0.977, "y": 1.047, "curve": [0.95, 0.977, 0.983, 0.898, 0.95, 1.047, 0.983, 1.111]}, {"time": 1, "x": 0.898, "y": 1.111, "curve": [1.017, 0.898, 1.05, 0.977, 1.017, 1.111, 1.05, 1.047]}, {"time": 1.0667, "x": 0.977, "y": 1.047, "curve": [1.083, 0.977, 1.117, 0.898, 1.083, 1.047, 1.117, 1.111]}, {"time": 1.1333, "x": 0.898, "y": 1.111, "curve": [1.15, 0.898, 1.183, 0.977, 1.15, 1.111, 1.183, 1.047]}, {"time": 1.2, "x": 0.977, "y": 1.047, "curve": [1.217, 0.977, 1.25, 0.898, 1.217, 1.047, 1.25, 1.111]}, {"time": 1.2667, "x": 0.898, "y": 1.111, "curve": [1.283, 0.898, 1.317, 0.977, 1.283, 1.111, 1.317, 1.047]}, {"time": 1.3333, "x": 0.977, "y": 1.047, "curve": [1.35, 0.977, 1.383, 0.898, 1.35, 1.047, 1.383, 1.111]}, {"time": 1.4, "x": 0.898, "y": 1.111, "curve": [1.417, 0.898, 1.45, 0.977, 1.417, 1.111, 1.45, 1.047]}, {"time": 1.4667, "x": 0.977, "y": 1.047, "curve": [1.483, 0.977, 1.517, 0.898, 1.483, 1.047, 1.517, 1.111]}, {"time": 1.5333, "x": 0.898, "y": 1.111, "curve": [1.55, 0.898, 1.583, 0.977, 1.55, 1.111, 1.583, 1.047]}, {"time": 1.6, "x": 0.977, "y": 1.047, "curve": [1.617, 0.977, 1.65, 0.898, 1.617, 1.047, 1.65, 1.111]}, {"time": 1.6667, "x": 0.898, "y": 1.111, "curve": [1.683, 0.898, 1.717, 0.977, 1.683, 1.111, 1.717, 1.047]}, {"time": 1.7333, "x": 0.977, "y": 1.047, "curve": [1.75, 0.977, 1.783, 0.898, 1.75, 1.047, 1.783, 1.111]}, {"time": 1.8, "x": 0.898, "y": 1.111, "curve": [1.85, 0.898, 1.95, 0.977, 1.85, 1.111, 1.95, 1.047]}, {"time": 2, "x": 0.977, "y": 1.047}]}, "BODY_3": {"scale": [{"x": 0.963, "y": 1.065, "curve": [0.125, 0.963, 0.21, 1.221, 0.125, 1.065, 0.21, 0.864]}, {"time": 0.5, "x": 1.221, "y": 0.864, "curve": [0.525, 1.221, 0.575, 0.885, 0.525, 0.864, 0.575, 1.129]}, {"time": 0.6, "x": 0.885, "y": 1.129, "curve": [0.617, 0.885, 0.65, 0.963, 0.617, 1.129, 0.65, 1.065]}, {"time": 0.6667, "x": 0.963, "y": 1.065, "curve": [0.683, 0.963, 0.717, 0.885, 0.683, 1.065, 0.717, 1.129]}, {"time": 0.7333, "x": 0.885, "y": 1.129, "curve": [0.75, 0.885, 0.783, 0.963, 0.75, 1.129, 0.783, 1.065]}, {"time": 0.8, "x": 0.963, "y": 1.065, "curve": [0.817, 0.963, 0.85, 0.885, 0.817, 1.065, 0.85, 1.129]}, {"time": 0.8667, "x": 0.885, "y": 1.129, "curve": [0.883, 0.885, 0.917, 0.963, 0.883, 1.129, 0.917, 1.065]}, {"time": 0.9333, "x": 0.963, "y": 1.065, "curve": [0.95, 0.963, 0.983, 0.885, 0.95, 1.065, 0.983, 1.129]}, {"time": 1, "x": 0.885, "y": 1.129, "curve": [1.017, 0.885, 1.05, 0.963, 1.017, 1.129, 1.05, 1.065]}, {"time": 1.0667, "x": 0.963, "y": 1.065, "curve": [1.083, 0.963, 1.117, 0.885, 1.083, 1.065, 1.117, 1.129]}, {"time": 1.1333, "x": 0.885, "y": 1.129, "curve": [1.15, 0.885, 1.183, 0.963, 1.15, 1.129, 1.183, 1.065]}, {"time": 1.2, "x": 0.963, "y": 1.065, "curve": [1.217, 0.963, 1.25, 0.885, 1.217, 1.065, 1.25, 1.129]}, {"time": 1.2667, "x": 0.885, "y": 1.129, "curve": [1.283, 0.885, 1.317, 0.963, 1.283, 1.129, 1.317, 1.065]}, {"time": 1.3333, "x": 0.963, "y": 1.065, "curve": [1.35, 0.963, 1.383, 0.885, 1.35, 1.065, 1.383, 1.129]}, {"time": 1.4, "x": 0.885, "y": 1.129, "curve": [1.417, 0.885, 1.45, 0.963, 1.417, 1.129, 1.45, 1.065]}, {"time": 1.4667, "x": 0.963, "y": 1.065, "curve": [1.483, 0.963, 1.517, 0.885, 1.483, 1.065, 1.517, 1.129]}, {"time": 1.5333, "x": 0.885, "y": 1.129, "curve": [1.55, 0.885, 1.583, 0.963, 1.55, 1.129, 1.583, 1.065]}, {"time": 1.6, "x": 0.963, "y": 1.065, "curve": [1.617, 0.963, 1.65, 0.885, 1.617, 1.065, 1.65, 1.129]}, {"time": 1.6667, "x": 0.885, "y": 1.129, "curve": [1.683, 0.885, 1.717, 0.963, 1.683, 1.129, 1.717, 1.065]}, {"time": 1.7333, "x": 0.963, "y": 1.065, "curve": [1.75, 0.963, 1.783, 0.885, 1.75, 1.065, 1.783, 1.129]}, {"time": 1.8, "x": 0.885, "y": 1.129, "curve": [1.85, 0.885, 1.95, 0.963, 1.85, 1.129, 1.95, 1.065]}, {"time": 2, "x": 0.963, "y": 1.065}]}, "Twig2": {"rotate": [{"value": 4.09}]}, "Twig3": {"rotate": [{"value": 0.15}]}, "MOUTH_BTM": {"translate": [{"curve": [0.125, 0, 0.375, 0, 0.125, 0, 0.375, 45.15]}, {"time": 0.5, "y": 45.15}, {"time": 0.6, "x": -1.32, "y": -51.14, "curve": [0.617, -1.32, 0.65, -0.11, 0.617, -51.14, 0.65, -22.86]}, {"time": 0.6667, "x": -0.11, "y": -22.86, "curve": [0.683, -0.11, 0.717, -1.32, 0.683, -22.86, 0.717, -51.14]}, {"time": 0.7333, "x": -1.32, "y": -51.14, "curve": [0.75, -1.32, 0.783, -0.11, 0.75, -51.14, 0.783, -22.86]}, {"time": 0.8, "x": -0.11, "y": -22.86, "curve": [0.817, -0.11, 0.85, -1.32, 0.817, -22.86, 0.85, -51.14]}, {"time": 0.8667, "x": -1.32, "y": -51.14, "curve": [0.883, -1.32, 0.917, -0.11, 0.883, -51.14, 0.917, -22.86]}, {"time": 0.9333, "x": -0.11, "y": -22.86, "curve": [0.95, -0.11, 0.983, -1.32, 0.95, -22.86, 0.983, -51.14]}, {"time": 1, "x": -1.32, "y": -51.14, "curve": [1.017, -1.32, 1.05, -0.11, 1.017, -51.14, 1.05, -22.86]}, {"time": 1.0667, "x": -0.11, "y": -22.86, "curve": [1.083, -0.11, 1.117, -1.32, 1.083, -22.86, 1.117, -51.14]}, {"time": 1.1333, "x": -1.32, "y": -51.14, "curve": [1.15, -1.32, 1.183, -0.11, 1.15, -51.14, 1.183, -22.86]}, {"time": 1.2, "x": -0.11, "y": -22.86, "curve": [1.217, -0.11, 1.25, -1.32, 1.217, -22.86, 1.25, -51.14]}, {"time": 1.2667, "x": -1.32, "y": -51.14, "curve": [1.283, -1.32, 1.317, -0.11, 1.283, -51.14, 1.317, -22.86]}, {"time": 1.3333, "x": -0.11, "y": -22.86, "curve": [1.35, -0.11, 1.383, -1.32, 1.35, -22.86, 1.383, -51.14]}, {"time": 1.4, "x": -1.32, "y": -51.14, "curve": [1.417, -1.32, 1.45, -0.11, 1.417, -51.14, 1.45, -22.86]}, {"time": 1.4667, "x": -0.11, "y": -22.86, "curve": [1.483, -0.11, 1.517, -1.32, 1.483, -22.86, 1.517, -51.14]}, {"time": 1.5333, "x": -1.32, "y": -51.14, "curve": [1.567, -1.32, 1.633, 0, 1.567, -51.14, 1.633, 40.34]}, {"time": 1.6667, "y": 40.34}, {"time": 1.9}]}, "FleshTurret": {"translate": [{"curve": [0.017, 0, 0.05, 4.8, 0.017, 0, 0.05, 0]}, {"time": 0.0667, "x": 4.8, "curve": [0.083, 4.8, 0.117, 0, 0.083, 0, 0.117, 0]}, {"time": 0.1333, "curve": [0.15, 0, 0.183, 4.8, 0.15, 0, 0.183, 0]}, {"time": 0.2, "x": 4.8, "curve": [0.217, 4.8, 0.25, 0, 0.217, 0, 0.25, 0]}, {"time": 0.2667}, {"time": 0.3333, "x": 4.8}], "scale": [{"curve": [0.025, 1, 0.075, 0.809, 0.025, 1, 0.075, 1.167]}, {"time": 0.1, "x": 0.809, "y": 1.167, "curve": [0.125, 0.809, 0.175, 1, 0.125, 1.167, 0.175, 1]}, {"time": 0.2, "curve": [0.217, 1, 0.25, 0.809, 0.217, 1, 0.25, 1.167]}, {"time": 0.2667, "x": 0.809, "y": 1.167, "curve": [0.283, 0.809, 0.317, 1, 0.283, 1.167, 0.317, 1]}, {"time": 0.3333}]}, "HeadFleshball_1": {"scale": [{"curve": [0.025, 1, 0.075, 1, 0.025, 1, 0.075, 0.872]}, {"time": 0.1, "y": 0.872, "curve": [0.117, 1, 0.15, 1, 0.117, 0.872, 0.15, 1]}, {"time": 0.1667, "curve": [0.192, 1, 0.242, 1, 0.192, 1, 0.242, 0.872]}, {"time": 0.2667, "y": 0.872, "curve": [0.283, 1, 0.317, 1, 0.283, 0.872, 0.317, 1]}, {"time": 0.3333}]}, "HeadFleshball_2": {"scale": [{"y": 0.919, "curve": [0.024, 1, 0.05, 1, 0.024, 0.955, 0.05, 1]}, {"time": 0.0667, "curve": [0.083, 1, 0.117, 1, 0.083, 1, 0.117, 0.872]}, {"time": 0.1333, "y": 0.872, "curve": [0.142, 1, 0.154, 1, 0.142, 0.872, 0.154, 0.894]}, {"time": 0.1667, "y": 0.919, "curve": [0.191, 1, 0.217, 1, 0.191, 0.955, 0.217, 1]}, {"time": 0.2333, "curve": [0.25, 1, 0.283, 1, 0.25, 1, 0.283, 0.872]}, {"time": 0.3, "y": 0.872, "curve": [0.309, 1, 0.321, 1, 0.309, 0.872, 0.321, 0.894]}, {"time": 0.3333, "y": 0.919}]}, "HeadFleshball_3": {"scale": [{"y": 0.889, "curve": [0.012, 1, 0.024, 1, 0.012, 0.879, 0.024, 0.872]}, {"time": 0.0333, "y": 0.872, "curve": [0.05, 1, 0.083, 1, 0.05, 0.872, 0.083, 1]}, {"time": 0.1, "curve": [0.116, 1, 0.145, 1, 0.116, 1, 0.145, 0.921]}, {"time": 0.1667, "y": 0.889, "curve": [0.179, 1, 0.191, 1, 0.179, 0.879, 0.191, 0.872]}, {"time": 0.2, "y": 0.872, "curve": [0.217, 1, 0.25, 1, 0.217, 0.872, 0.25, 1]}, {"time": 0.2667, "curve": [0.283, 1, 0.312, 1, 0.283, 1, 0.312, 0.921]}, {"time": 0.3333, "y": 0.889}]}, "Fleshball_7": {"scale": [{"x": 0.938, "y": 0.938, "curve": [0.012, 0.919, 0.024, 0.908, 0.012, 0.919, 0.024, 0.908]}, {"time": 0.0333, "x": 0.908, "y": 0.908, "curve": [0.075, 0.908, 0.158, 1.141, 0.075, 0.908, 0.158, 1.141]}, {"time": 0.2, "x": 1.141, "y": 1.141, "curve": [0.232, 1.141, 0.291, 0.996, 0.232, 1.141, 0.291, 0.996]}, {"time": 0.3333, "x": 0.938, "y": 0.938}]}, "Fleshball_6": {"scale": [{"x": 1.11, "y": 1.11, "curve": [0.043, 1.052, 0.101, 0.908, 0.043, 1.052, 0.101, 0.908]}, {"time": 0.1333, "x": 0.908, "y": 0.908, "curve": [0.175, 0.908, 0.258, 1.141, 0.175, 0.908, 0.258, 1.141]}, {"time": 0.3, "x": 1.141, "y": 1.141, "curve": [0.309, 1.141, 0.321, 1.129, 0.309, 1.141, 0.321, 1.129]}, {"time": 0.3333, "x": 1.11, "y": 1.11}]}, "Fleshball_8": {"scale": [{"x": 0.993, "y": 0.993, "curve": [0.036, 1.059, 0.075, 1.141, 0.036, 1.059, 0.075, 1.141]}, {"time": 0.1, "x": 1.141, "y": 1.141, "curve": [0.142, 1.141, 0.225, 0.908, 0.142, 1.141, 0.225, 0.908]}, {"time": 0.2667, "x": 0.908, "y": 0.908, "curve": [0.284, 0.908, 0.308, 0.946, 0.284, 0.908, 0.308, 0.946]}, {"time": 0.3333, "x": 0.993, "y": 0.993}]}, "Fleshball_5": {"scale": [{"x": 1.059, "y": 1.059, "curve": [0.012, 1.07, 0.024, 1.076, 0.012, 1.07, 0.024, 1.076]}, {"time": 0.0333, "x": 1.076, "y": 1.076, "curve": [0.075, 1.076, 0.158, 0.944, 0.075, 1.076, 0.158, 0.944]}, {"time": 0.2, "x": 0.944, "y": 0.944, "curve": [0.232, 0.944, 0.291, 1.026, 0.232, 0.944, 0.291, 1.026]}, {"time": 0.3333, "x": 1.059, "y": 1.059}]}, "Fleshball_4": {"scale": [{"x": 1.044, "y": 1.044, "curve": [0.034, 1.006, 0.076, 0.944, 0.034, 1.006, 0.076, 0.944]}, {"time": 0.1, "x": 0.944, "y": 0.944, "curve": [0.142, 0.944, 0.225, 1.076, 0.142, 0.944, 0.225, 1.076]}, {"time": 0.2667, "x": 1.076, "y": 1.076, "curve": [0.285, 1.076, 0.308, 1.063, 0.285, 1.076, 0.308, 1.063]}, {"time": 0.3333, "x": 1.044, "y": 1.044}]}, "Fleshball_3": {"scale": [{"x": 0.992, "y": 0.992, "curve": [0.025, 0.966, 0.049, 0.944, 0.025, 0.966, 0.049, 0.944]}, {"time": 0.0667, "x": 0.944, "y": 0.944, "curve": [0.108, 0.944, 0.192, 1.076, 0.108, 0.944, 0.192, 1.076]}, {"time": 0.2333, "x": 1.076, "y": 1.076, "curve": [0.258, 1.076, 0.297, 1.03, 0.258, 1.076, 0.297, 1.03]}, {"time": 0.3333, "x": 0.992, "y": 0.992}]}, "Fleshball_1": {"scale": [{"x": 1.044, "y": 1.044, "curve": [0.034, 1.006, 0.076, 0.944, 0.034, 1.006, 0.076, 0.944]}, {"time": 0.1, "x": 0.944, "y": 0.944, "curve": [0.142, 0.944, 0.225, 1.076, 0.142, 0.944, 0.225, 1.076]}, {"time": 0.2667, "x": 1.076, "y": 1.076, "curve": [0.285, 1.076, 0.308, 1.063, 0.285, 1.076, 0.308, 1.063]}, {"time": 0.3333, "x": 1.044, "y": 1.044}]}, "Fleshball_2": {"scale": [{"x": 0.992, "y": 0.992, "curve": [0.025, 0.966, 0.049, 0.944, 0.025, 0.966, 0.049, 0.944]}, {"time": 0.0667, "x": 0.944, "y": 0.944, "curve": [0.108, 0.944, 0.192, 1.076, 0.108, 0.944, 0.192, 1.076]}, {"time": 0.2333, "x": 1.076, "y": 1.076, "curve": [0.258, 1.076, 0.297, 1.03, 0.258, 1.076, 0.297, 1.03]}, {"time": 0.3333, "x": 0.992, "y": 0.992}]}, "Fleshball_9": {"scale": [{"x": 0.999, "y": 0.999}, {"time": 0.0667, "x": 0.897, "y": 0.897}, {"time": 0.2333, "x": 1.101, "y": 1.101}, {"time": 0.3333, "x": 0.999, "y": 0.999}]}, "HeadFleshball_1_Behind": {"scale": [{"curve": [0.025, 1, 0.075, 1, 0.025, 1, 0.075, 0.872]}, {"time": 0.1, "y": 0.872, "curve": [0.117, 1, 0.15, 1, 0.117, 0.872, 0.15, 1]}, {"time": 0.1667, "curve": [0.192, 1, 0.242, 1, 0.192, 1, 0.242, 0.872]}, {"time": 0.2667, "y": 0.872, "curve": [0.283, 1, 0.317, 1, 0.283, 0.872, 0.317, 1]}, {"time": 0.3333}]}, "HeadFleshball_3_Behind": {"scale": [{"y": 0.889, "curve": [0.012, 1, 0.024, 1, 0.012, 0.879, 0.024, 0.872]}, {"time": 0.0333, "y": 0.872, "curve": [0.05, 1, 0.083, 1, 0.05, 0.872, 0.083, 1]}, {"time": 0.1, "curve": [0.116, 1, 0.145, 1, 0.116, 1, 0.145, 0.921]}, {"time": 0.1667, "y": 0.889, "curve": [0.179, 1, 0.191, 1, 0.179, 0.879, 0.191, 0.872]}, {"time": 0.2, "y": 0.872, "curve": [0.217, 1, 0.25, 1, 0.217, 0.872, 0.25, 1]}, {"time": 0.2667, "curve": [0.283, 1, 0.312, 1, 0.283, 1, 0.312, 0.921]}, {"time": 0.3333, "y": 0.889}]}, "Antler_4": {"rotate": [{"curve": [0.125, 0, 0.183, 8.37]}, {"time": 0.5, "value": 8.37, "curve": [0.525, 8.37, 0.575, 0]}, {"time": 0.6}]}, "Antler_1": {"rotate": [{"curve": [0.125, 0, 0.183, -6.17]}, {"time": 0.5, "value": -6.17, "curve": [0.525, -6.17, 0.575, 0]}, {"time": 0.6}]}, "Antler_3": {"rotate": [{"curve": [0.125, 0, 0.183, 10.89]}, {"time": 0.5, "value": 10.89, "curve": [0.525, 10.89, 0.575, 0]}, {"time": 0.6}]}, "Antler_0": {"rotate": [{"curve": [0.125, 0, 0.183, -10.17]}, {"time": 0.5, "value": -10.17, "curve": [0.525, -10.17, 0.575, 0]}, {"time": 0.6}]}, "MOUTH_CHASER": {"scale": [{"y": 0.955}]}}}, "transform": {"slots": {"EYE_LEFT_BTM": {"attachment": [{"name": "<PERSON>ler,<PERSON>r/Pouncer <PERSON>/Eye_Shut"}, {"time": 0.2333, "name": "<PERSON>ler,<PERSON>r/Pouncer Boss/Eye_Normal"}, {"time": 0.4667, "name": "<PERSON>ler,<PERSON>r/Pouncer <PERSON>/Eye_Shut"}, {"time": 0.9333, "name": "<PERSON>ler,<PERSON>r/Pouncer Boss/Eye_Normal"}]}, "EYE_LEFT_TOP": {"attachment": [{"name": "<PERSON>ler,<PERSON>r/Pouncer <PERSON>/Eye_Shut"}, {"time": 0.2333, "name": "<PERSON>ler,<PERSON>r/Pouncer Boss/Eye_Normal"}, {"time": 0.4667, "name": "<PERSON>ler,<PERSON>r/Pouncer <PERSON>/Eye_Shut"}, {"time": 0.9667, "name": "<PERSON>ler,<PERSON>r/Pouncer Boss/Eye_Normal"}, {"time": 1.1333, "name": "<PERSON>ler,<PERSON>r/Pouncer <PERSON>/Eye_Shut"}]}, "EYE_RIGHT_BTM": {"attachment": [{"name": "<PERSON>ler,<PERSON>r/Pouncer <PERSON>/Eye_Shut"}, {"time": 0.2333, "name": "<PERSON>ler,<PERSON>r/Pouncer Boss/Eye_Normal"}, {"time": 0.4667, "name": "<PERSON>ler,<PERSON>r/Pouncer <PERSON>/Eye_Shut"}, {"time": 0.9333, "name": "<PERSON>ler,<PERSON>r/Pouncer Boss/Eye_Normal"}]}, "EYE_RIGHT_TOP": {"attachment": [{"name": "<PERSON>ler,<PERSON>r/Pouncer <PERSON>/Eye_Shut"}, {"time": 0.2333, "name": "<PERSON>ler,<PERSON>r/Pouncer Boss/Eye_Normal"}, {"time": 0.4667, "name": "<PERSON>ler,<PERSON>r/Pouncer <PERSON>/Eye_Shut"}, {"time": 0.9667, "name": "<PERSON>ler,<PERSON>r/Pouncer Boss/Eye_Normal"}, {"time": 1.1333, "name": "<PERSON>ler,<PERSON>r/Pouncer <PERSON>/Eye_Shut"}]}, "MouthBtm": {"attachment": [{"time": 0.5667, "name": "Mouth_shut"}]}, "Patroller,Chaser/Pouncer Boss/Mouth": {"attachment": [{"time": 0.5667, "name": "Mouth_shut"}]}, "Patroller,Chaser/Pouncer Boss/Mouth2": {"attachment": [{"time": 0.5667, "name": "Mouth_shut"}]}, "TurretMouth": {"attachment": [{"time": 0.5, "name": "Patroller,Chaser/Chaser Boss/Tunneler_Mouth_Closed"}, {"time": 1.1333, "name": "Patroller,Chaser/Chaser Boss/Tunneler_Mouth"}]}}, "bones": {"MOUTH_CHASER": {"scale": [{"time": 1.1333, "y": 0.955}]}, "MOUTH": {"scale": [{"x": 1.064, "y": 0.499, "curve": [0.042, 1.064, 0.125, 1, 0.042, 0.499, 0.125, 1.119]}, {"time": 0.1667, "y": 1.119, "curve": [0.208, 1, 0.292, 1, 0.208, 1.119, 0.292, 1]}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5}, {"time": 0.5333, "y": 0.52, "curve": "stepped"}, {"time": 0.5667}]}, "BODY_1": {"rotate": [{"curve": [0.017, 0, 0.05, -5.3]}, {"time": 0.0667, "value": -5.3, "curve": [0.1, -5.3, 0.167, 3.18]}, {"time": 0.2, "value": 3.18, "curve": [0.233, 3.18, 0.3, -6.66]}, {"time": 0.3333, "value": -6.66, "curve": [0.375, -6.66, 0.458, -2.6]}, {"time": 0.5, "value": -2.6}], "translate": [{"curve": [0, 0, 0.075, 0, 0, 95.89, 0.075, 121.52]}, {"time": 0.1, "y": 121.52, "curve": [0.192, 0, 0.467, 0, 0.192, 121.52, 0.467, 71.59]}, {"time": 0.4667}], "scale": [{"x": 1.268, "y": 0.732, "curve": [0, 1.311, 0.035, 1.333, 0, 0.706, 0.035, 0.693]}, {"time": 0.0667, "x": 1.343, "y": 0.687, "curve": [0.08, 0.833, 0.093, 0.636, 0.08, 1.426, 0.093, 1.712]}, {"time": 0.1, "x": 0.636, "y": 1.712, "curve": [0.142, 0.636, 0.229, 0.758, 0.142, 1.712, 0.229, 1.517]}, {"time": 0.3, "x": 0.964, "y": 1.187, "curve": [0.392, 0.974, 0.467, 0.986, 0.392, 1.139, 0.467, 1.075]}, {"time": 0.4667, "curve": [0.483, 1, 0.517, 1.361, 0.483, 1, 0.517, 0.809]}, {"time": 0.5333, "x": 1.361, "y": 0.809, "curve": [0.558, 1.361, 0.608, 0.966, 0.558, 0.809, 0.608, 1.07]}, {"time": 0.6333, "x": 0.966, "y": 1.07, "curve": [0.675, 0.966, 0.758, 1.056, 0.675, 1.07, 0.758, 0.973]}, {"time": 0.8, "x": 1.056, "y": 0.973, "curve": [0.858, 1.056, 0.975, 0.966, 0.858, 0.973, 0.975, 1.07]}, {"time": 1.0333, "x": 0.966, "y": 1.07, "curve": [1.058, 0.966, 1.108, 1.034, 1.058, 1.07, 1.108, 0.978]}, {"time": 1.1333, "x": 1.034, "y": 0.978}]}, "BODY_2": {"rotate": [{"curve": [0.033, 0, 0.1, -5.3]}, {"time": 0.1333, "value": -5.3, "curve": [0.158, -5.3, 0.208, 3.18]}, {"time": 0.2333, "value": 3.18, "curve": [0.258, 3.18, 0.308, -6.66]}, {"time": 0.3333, "value": -6.66, "curve": [0.375, -6.66, 0.458, -2.6]}, {"time": 0.5, "value": -2.6}], "translate": [{"curve": [0, 0, 0.075, 0, 0, 95.89, 0.075, 121.52]}, {"time": 0.1, "y": 121.52, "curve": [0.192, 0, 0.467, 0, 0.192, 121.52, 0.467, 71.59]}, {"time": 0.4667}], "scale": [{"x": 1.268, "y": 0.732, "curve": [0, 1.311, 0.035, 1.333, 0, 0.706, 0.035, 0.693]}, {"time": 0.0667, "x": 1.343, "y": 0.687, "curve": [0.08, 0.833, 0.093, 0.636, 0.08, 1.426, 0.093, 1.712]}, {"time": 0.1, "x": 0.636, "y": 1.712, "curve": [0.142, 0.636, 0.229, 0.758, 0.142, 1.712, 0.229, 1.517]}, {"time": 0.3, "x": 0.964, "y": 1.187, "curve": [0.392, 0.974, 0.467, 0.986, 0.392, 1.139, 0.467, 1.075]}, {"time": 0.4667, "curve": [0.483, 1, 0.517, 1.361, 0.483, 1, 0.517, 0.809]}, {"time": 0.5333, "x": 1.361, "y": 0.809, "curve": [0.558, 1.361, 0.608, 0.966, 0.558, 0.809, 0.608, 1.07]}, {"time": 0.6333, "x": 0.966, "y": 1.07, "curve": [0.675, 0.966, 0.758, 1.056, 0.675, 1.07, 0.758, 0.973]}, {"time": 0.8, "x": 1.056, "y": 0.973, "curve": [0.858, 1.056, 0.975, 0.966, 0.858, 0.973, 0.975, 1.07]}, {"time": 1.0333, "x": 0.966, "y": 1.07, "curve": [1.058, 0.966, 1.108, 0.977, 1.058, 1.07, 1.108, 1.047]}, {"time": 1.1333, "x": 0.977, "y": 1.047}]}, "BODY_3": {"rotate": [{"curve": [0.033, 0, 0.1, -5.3]}, {"time": 0.1333, "value": -5.3, "curve": [0.158, -5.3, 0.208, 3.18]}, {"time": 0.2333, "value": 3.18, "curve": [0.258, 3.18, 0.308, -6.66]}, {"time": 0.3333, "value": -6.66, "curve": [0.375, -6.66, 0.458, -2.6]}, {"time": 0.5, "value": -2.6}], "translate": [{"curve": [0, 0, 0.075, 0, 0, 95.89, 0.075, 121.52]}, {"time": 0.1, "y": 121.52, "curve": [0.192, 0, 0.467, 0, 0.192, 121.52, 0.467, 71.59]}, {"time": 0.4667}], "scale": [{"x": 1.268, "y": 0.732, "curve": [0, 1.311, 0.035, 1.333, 0, 0.706, 0.035, 0.693]}, {"time": 0.0667, "x": 1.343, "y": 0.687, "curve": [0.08, 0.833, 0.093, 0.636, 0.08, 1.426, 0.093, 1.712]}, {"time": 0.1, "x": 0.636, "y": 1.712, "curve": [0.142, 0.636, 0.229, 0.758, 0.142, 1.712, 0.229, 1.517]}, {"time": 0.3, "x": 0.964, "y": 1.187, "curve": [0.392, 0.974, 0.467, 0.986, 0.392, 1.139, 0.467, 1.075]}, {"time": 0.4667, "curve": [0.483, 1, 0.517, 1.361, 0.483, 1, 0.517, 0.809]}, {"time": 0.5333, "x": 1.361, "y": 0.809, "curve": [0.558, 1.361, 0.608, 0.966, 0.558, 0.809, 0.608, 1.07]}, {"time": 0.6333, "x": 0.966, "y": 1.07, "curve": [0.675, 0.966, 0.758, 1.056, 0.675, 1.07, 0.758, 0.973]}, {"time": 0.8, "x": 1.056, "y": 0.973, "curve": [0.858, 1.056, 0.975, 0.966, 0.858, 0.973, 0.975, 1.07]}, {"time": 1.0333, "x": 0.966, "y": 1.07, "curve": [1.058, 0.966, 1.108, 0.963, 1.058, 1.07, 1.108, 1.065]}, {"time": 1.1333, "x": 0.963, "y": 1.065}]}, "Main": {"rotate": [{"curve": [0.017, 0, 0.05, -5.3]}, {"time": 0.0667, "value": -5.3, "curve": [0.1, -5.3, 0.167, 3.18]}, {"time": 0.2, "value": 3.18, "curve": [0.233, 3.18, 0.3, -6.66]}, {"time": 0.3333, "value": -6.66, "curve": [0.375, -6.66, 0.458, 0]}, {"time": 0.5}], "translate": [{"curve": [0, 0, 0.075, 0, 0, 95.89, 0.075, 121.52]}, {"time": 0.1, "y": 121.52, "curve": [0.192, 0, 0.467, 0, 0.192, 121.52, 0.467, 71.59]}, {"time": 0.4667}], "scale": [{"x": 0.899, "y": 0.311, "curve": [0, 1.154, 0.035, 1.284, 0, 0.527, 0.035, 0.638]}, {"time": 0.0667, "x": 1.343, "y": 0.687, "curve": [0.08, 0.833, 0.093, 0.636, 0.08, 1.426, 0.093, 1.712]}, {"time": 0.1, "x": 0.636, "y": 1.712, "curve": [0.142, 0.636, 0.229, 1.028, 0.142, 1.712, 0.229, 1.413]}, {"time": 0.3, "x": 1.69, "y": 0.907, "curve": [0.392, 1.513, 0.467, 1.276, 0.392, 0.931, 0.467, 0.963]}, {"time": 0.4667, "curve": [0.483, 1, 0.517, 1.656, 0.483, 1, 0.517, 0.628]}, {"time": 0.5333, "x": 1.656, "y": 0.628, "curve": [0.598, 1.656, 0.608, 0.871, 0.598, 0.628, 0.608, 1.19]}, {"time": 0.6333, "x": 0.871, "y": 1.19, "curve": [0.711, 0.871, 0.732, 1.056, 0.711, 1.19, 0.732, 0.973]}, {"time": 0.8, "x": 1.056, "y": 0.973, "curve": [0.909, 1.056, 0.938, 0.966, 0.909, 0.973, 0.938, 1.07]}, {"time": 1.0333, "x": 0.966, "y": 1.07, "curve": [1.058, 0.966, 1.108, 1.05, 1.058, 1.07, 1.108, 0.959]}, {"time": 1.1333, "x": 1.05, "y": 0.959}]}, "FACE": {"rotate": [{"curve": [0.025, 0, 0.075, -4.87]}, {"time": 0.1, "value": -4.87, "curve": [0.125, -4.87, 0.175, 2.69]}, {"time": 0.2, "value": 2.69, "curve": [0.233, 2.69, 0.3, 0]}, {"time": 0.3333}], "translate": [{"x": 2.01, "y": -48.7, "curve": [0.033, 2.01, 0.1, -17.57, 0.033, -48.7, 0.1, 59.99]}, {"time": 0.1333, "x": -17.57, "y": 59.99, "curve": [0.217, -17.57, 0.437, -9.77, 0.217, 59.99, 0.437, 33.38]}, {"time": 0.4667}]}, "Antler_1": {"rotate": [{"time": 0.4667, "curve": [0.483, 0, 0.517, -28.81]}, {"time": 0.5333, "value": -28.81, "curve": [0.558, -28.81, 0.608, 18.63]}, {"time": 0.6333, "value": 18.63, "curve": [0.683, 18.63, 0.783, -14.31]}, {"time": 0.8333, "value": -14.31, "curve": [0.883, -14.31, 0.983, 6.43]}, {"time": 1.0333, "value": 6.43, "curve": [1.058, 6.43, 1.108, 0]}, {"time": 1.1333}]}, "Antler_4": {"rotate": [{"time": 0.4667, "curve": [0.483, 0, 0.517, 25.06]}, {"time": 0.5333, "value": 25.06, "curve": [0.558, 25.06, 0.608, -16.43]}, {"time": 0.6333, "value": -16.43, "curve": [0.683, -16.43, 0.783, 6.76]}, {"time": 0.8333, "value": 6.76, "curve": [0.883, 6.76, 0.983, -5.33]}, {"time": 1.0333, "value": -5.33, "curve": [1.058, -5.33, 1.108, 0]}, {"time": 1.1333}]}, "Antler_3": {"rotate": [{"time": 0.4667, "curve": [0.483, 0, 0.517, 58.17]}, {"time": 0.5333, "value": 58.17, "curve": [0.558, 58.17, 0.608, -22.19]}, {"time": 0.6333, "value": -22.19, "curve": [0.683, -22.19, 0.783, 13.8]}, {"time": 0.8333, "value": 13.8, "curve": [0.883, 13.8, 0.983, -7.7]}, {"time": 1.0333, "value": -7.7, "curve": [1.058, -7.7, 1.108, 0]}, {"time": 1.1333}]}, "Antler_0": {"rotate": [{"time": 0.4667, "curve": [0.483, 0, 0.517, -53.81]}, {"time": 0.5333, "value": -53.81, "curve": [0.558, -53.81, 0.608, 31.58]}, {"time": 0.6333, "value": 31.58, "curve": [0.683, 31.58, 0.783, -10.48]}, {"time": 0.8333, "value": -10.48, "curve": [0.883, -10.48, 0.983, 10.12]}, {"time": 1.0333, "value": 10.12, "curve": [1.058, 10.12, 1.108, 0]}, {"time": 1.1333}]}, "MOUTH_BTM": {"translate": [{"x": 1.07, "y": 34.11, "curve": [0.033, 1.07, 0.1, -0.6, 0.033, 34.11, 0.1, -56.63]}, {"time": 0.1333, "x": -0.6, "y": -56.63, "curve": "stepped"}, {"time": 0.4667, "x": -0.6, "y": -56.63, "curve": [0.492, -0.6, 0.542, -1.71, 0.492, -56.63, 0.542, 51.9]}, {"time": 0.5667, "x": -1.71, "y": 51.9, "curve": "stepped"}, {"time": 0.7333, "x": -1.71, "y": 51.9, "curve": [0.833, -1.71, 1.033, 0, 0.833, 51.9, 1.033, 0]}, {"time": 1.1333}]}, "HeadFleshball_1_Behind": {"scale": [{"time": 0.4333, "curve": [0.458, 1, 0.508, 1, 0.458, 1, 0.508, 0.872]}, {"time": 0.5333, "y": 0.872, "curve": [0.55, 1, 0.583, 1, 0.55, 0.872, 0.583, 1]}, {"time": 0.6, "curve": [0.625, 1, 0.675, 1, 0.625, 1, 0.675, 0.872]}, {"time": 0.7, "y": 0.872, "curve": [0.717, 1, 0.75, 1.005, 0.717, 0.872, 0.75, 1.005]}, {"time": 0.7667, "x": 1.005, "y": 1.005, "curve": [0.804, 1.002, 0.842, 1, 0.804, 0.939, 0.842, 0.872]}, {"time": 0.8667, "y": 0.872, "curve": [0.883, 1, 0.917, 1, 0.883, 0.872, 0.917, 1]}, {"time": 0.9333, "curve": [0.958, 1, 1.008, 1, 0.958, 1, 1.008, 0.872]}, {"time": 1.0333, "y": 0.872, "curve": [1.058, 1, 1.108, 1, 1.058, 0.872, 1.108, 1]}, {"time": 1.1333}]}, "HeadFleshball_2": {"scale": [{"time": 0.4333, "y": 0.919, "curve": [0.458, 1, 0.484, 1, 0.458, 0.955, 0.484, 1]}, {"time": 0.5, "curve": [0.517, 1, 0.55, 1, 0.517, 1, 0.55, 0.872]}, {"time": 0.5667, "y": 0.872, "curve": [0.575, 1, 0.587, 1, 0.575, 0.872, 0.587, 0.894]}, {"time": 0.6, "y": 0.919, "curve": [0.624, 1, 0.65, 1, 0.624, 0.955, 0.65, 1]}, {"time": 0.6667, "curve": [0.683, 1, 0.717, 1, 0.683, 1, 0.717, 0.872]}, {"time": 0.7333, "y": 0.872, "curve": [0.742, 1, 0.754, 1.024, 0.742, 0.872, 0.754, 0.954]}, {"time": 0.7667, "x": 1.053, "y": 1.053, "curve": [0.792, 1.02, 0.814, 1, 0.792, 1.02, 0.814, 1]}, {"time": 0.8333, "curve": [0.85, 1, 0.883, 1, 0.85, 1, 0.883, 0.872]}, {"time": 0.9, "y": 0.872, "curve": [0.909, 1, 0.921, 1, 0.909, 0.872, 0.921, 0.894]}, {"time": 0.9333, "y": 0.919, "curve": [0.958, 1, 0.984, 1, 0.958, 0.955, 0.984, 1]}, {"time": 1, "curve": [1.033, 1, 1.1, 1, 1.033, 1, 1.1, 0.919]}, {"time": 1.1333, "y": 0.919}]}, "HeadFleshball_3": {"scale": [{"time": 0.4333, "y": 0.889, "curve": [0.446, 1, 0.457, 1, 0.446, 0.879, 0.457, 0.872]}, {"time": 0.4667, "y": 0.872, "curve": [0.483, 1, 0.517, 1, 0.483, 0.872, 0.517, 1]}, {"time": 0.5333, "curve": [0.55, 1, 0.579, 1, 0.55, 1, 0.579, 0.921]}, {"time": 0.6, "y": 0.889, "curve": [0.613, 1, 0.624, 1, 0.613, 0.879, 0.624, 0.872]}, {"time": 0.6333, "y": 0.872, "curve": [0.65, 1, 0.683, 1, 0.65, 0.872, 0.683, 1]}, {"time": 0.7, "curve": [0.716, 1, 0.745, 1.004, 0.716, 1, 0.745, 1.004]}, {"time": 0.7667, "x": 1.005, "y": 1.005, "curve": [0.779, 1.002, 0.792, 1, 0.779, 0.939, 0.792, 0.872]}, {"time": 0.8, "y": 0.872, "curve": [0.817, 1, 0.85, 1, 0.817, 0.872, 0.85, 1]}, {"time": 0.8667, "curve": [0.883, 1, 0.912, 1, 0.883, 1, 0.912, 0.921]}, {"time": 0.9333, "y": 0.889, "curve": [0.946, 1, 0.957, 1, 0.946, 0.879, 0.957, 0.872]}, {"time": 0.9667, "y": 0.872, "curve": [0.983, 1, 1.017, 1, 0.983, 0.872, 1.017, 1]}, {"time": 1.0333, "curve": [1.058, 1, 1.101, 1, 1.058, 1, 1.101, 0.921]}, {"time": 1.1333, "y": 0.889}]}, "HeadFleshball_3_Behind": {"scale": [{"time": 0.4333, "y": 0.889, "curve": [0.446, 1, 0.457, 1, 0.446, 0.879, 0.457, 0.872]}, {"time": 0.4667, "y": 0.872, "curve": [0.483, 1, 0.517, 1, 0.483, 0.872, 0.517, 1]}, {"time": 0.5333, "curve": [0.55, 1, 0.579, 1, 0.55, 1, 0.579, 0.921]}, {"time": 0.6, "y": 0.889, "curve": [0.613, 1, 0.624, 1, 0.613, 0.879, 0.624, 0.872]}, {"time": 0.6333, "y": 0.872, "curve": [0.65, 1, 0.683, 1, 0.65, 0.872, 0.683, 1]}, {"time": 0.7, "curve": [0.716, 1, 0.745, 1.004, 0.716, 1, 0.745, 1.004]}, {"time": 0.7667, "x": 1.005, "y": 1.005, "curve": [0.779, 1.002, 0.792, 1, 0.779, 0.939, 0.792, 0.872]}, {"time": 0.8, "y": 0.872, "curve": [0.817, 1, 0.85, 1, 0.817, 0.872, 0.85, 1]}, {"time": 0.8667, "curve": [0.883, 1, 0.912, 1, 0.883, 1, 0.912, 0.921]}, {"time": 0.9333, "y": 0.889, "curve": [0.946, 1, 0.957, 1, 0.946, 0.879, 0.957, 0.872]}, {"time": 0.9667, "y": 0.872, "curve": [0.983, 1, 1.017, 1, 0.983, 0.872, 1.017, 1]}, {"time": 1.0333, "curve": [1.058, 1, 1.101, 1, 1.058, 1, 1.101, 0.921]}, {"time": 1.1333, "y": 0.889}]}, "Twig2": {"rotate": [{"time": 1.1333, "value": 4.09}]}, "Twig3": {"rotate": [{"time": 1.1333, "value": 0.15}]}}}, "transform2": {"slots": {"EyeGoop1": {"attachment": [{"time": 2.6333, "name": "EyeGoop1"}]}, "EyeGoop2": {"attachment": [{"time": 2.6333, "name": "EyeGoop2"}]}, "EyeGoop3": {"attachment": [{"time": 2.6333, "name": "EyeGoop3"}]}, "EyeGoop4": {"attachment": [{"time": 2.6333, "name": "EyeGoop4"}]}, "EyeGoop5": {"attachment": [{"time": 2.6333, "name": "EyeGoop5"}]}, "EyeGoop6": {"attachment": [{"time": 2.6333, "name": "EyeGoop6"}]}, "EYE_LEFT_BTM": {"attachment": [{"name": "<PERSON>ler,<PERSON>r/Pouncer <PERSON>/Eye_Shut"}, {"time": 0.2333, "name": "<PERSON>ler,<PERSON>r/Pouncer Boss/Eye_Normal"}, {"time": 0.4667, "name": "<PERSON>ler,<PERSON>r/Pouncer <PERSON>/Eye_Shut"}, {"time": 0.9333, "name": "<PERSON>ler,<PERSON>r/Pouncer Boss/Eye_Normal"}, {"time": 1.1333, "name": "<PERSON>ler,<PERSON>r/Pouncer <PERSON>/Eye_Shut"}, {"time": 1.4667, "name": "<PERSON>ler,<PERSON><PERSON>/<PERSON>uncer <PERSON>/Eye_Shocked"}, {"time": 2.8667, "name": "<PERSON>ler,<PERSON>r/Pouncer <PERSON>/Eye_Shut"}, {"time": 3.4667, "name": "<PERSON><PERSON>,<PERSON><PERSON>/Pouncer Boss/Eye_Transform1"}, {"time": 3.5667, "name": "<PERSON><PERSON>,<PERSON><PERSON>/Pouncer Boss/Eye_Transform2"}, {"time": 3.6667, "name": "<PERSON><PERSON>,<PERSON><PERSON>/Pouncer Boss/Eye_Transform1"}, {"time": 3.7667, "name": "<PERSON><PERSON>,<PERSON><PERSON>/Pouncer Boss/Eye_Transform2"}, {"time": 3.8667, "name": "<PERSON><PERSON>,<PERSON><PERSON>/Pouncer Boss/Eye_Transform1"}, {"time": 3.9667, "name": "<PERSON><PERSON>,<PERSON><PERSON>/Pouncer Boss/Eye_Transform2"}, {"time": 4.0667, "name": "<PERSON><PERSON>,<PERSON><PERSON>/Pouncer Boss/Eye_Transform1"}]}, "EYE_LEFT_TOP": {"attachment": [{"name": "<PERSON>ler,<PERSON>r/Pouncer <PERSON>/Eye_Shut"}, {"time": 0.2333, "name": "<PERSON>ler,<PERSON>r/Pouncer Boss/Eye_Normal"}, {"time": 0.4667, "name": "<PERSON>ler,<PERSON>r/Pouncer <PERSON>/Eye_Shut"}, {"time": 0.9667, "name": "<PERSON>ler,<PERSON>r/Pouncer Boss/Eye_Normal"}, {"time": 1.1667, "name": "<PERSON>ler,<PERSON>r/Pouncer <PERSON>/Eye_Shut"}, {"time": 1.5, "name": "<PERSON>ler,<PERSON><PERSON>/<PERSON>uncer <PERSON>/Eye_Shocked"}, {"time": 2.9, "name": "<PERSON>ler,<PERSON>r/Pouncer <PERSON>/Eye_Shut"}, {"time": 3.5, "name": "<PERSON><PERSON>,<PERSON><PERSON>/Pouncer Boss/Eye_Transform1"}, {"time": 3.6, "name": "<PERSON><PERSON>,<PERSON><PERSON>/Pouncer Boss/Eye_Transform2"}, {"time": 3.7, "name": "<PERSON><PERSON>,<PERSON><PERSON>/Pouncer Boss/Eye_Transform1"}, {"time": 3.8, "name": "<PERSON><PERSON>,<PERSON><PERSON>/Pouncer Boss/Eye_Transform2"}, {"time": 3.9, "name": "<PERSON><PERSON>,<PERSON><PERSON>/Pouncer Boss/Eye_Transform1"}, {"time": 4, "name": "<PERSON><PERSON>,<PERSON><PERSON>/Pouncer Boss/Eye_Transform2"}, {"time": 4.1, "name": "<PERSON><PERSON>,<PERSON><PERSON>/Pouncer Boss/Eye_Transform1"}]}, "EYE_RIGHT_BTM": {"attachment": [{"name": "<PERSON>ler,<PERSON>r/Pouncer <PERSON>/Eye_Shut"}, {"time": 0.2333, "name": "<PERSON>ler,<PERSON>r/Pouncer Boss/Eye_Normal"}, {"time": 0.4667, "name": "<PERSON>ler,<PERSON>r/Pouncer <PERSON>/Eye_Shut"}, {"time": 0.9333, "name": "<PERSON>ler,<PERSON>r/Pouncer Boss/Eye_Normal"}, {"time": 1.1333, "name": "<PERSON>ler,<PERSON>r/Pouncer <PERSON>/Eye_Shut"}, {"time": 1.4667, "name": "<PERSON>ler,<PERSON><PERSON>/<PERSON>uncer <PERSON>/Eye_Shocked"}, {"time": 2.8667, "name": "<PERSON>ler,<PERSON>r/Pouncer <PERSON>/Eye_Shut"}, {"time": 3.4667, "name": "<PERSON><PERSON>,<PERSON><PERSON>/Pouncer Boss/Eye_Transform1"}, {"time": 3.5667, "name": "<PERSON><PERSON>,<PERSON><PERSON>/Pouncer Boss/Eye_Transform2"}, {"time": 3.6667, "name": "<PERSON><PERSON>,<PERSON><PERSON>/Pouncer Boss/Eye_Transform1"}, {"time": 3.7667, "name": "<PERSON><PERSON>,<PERSON><PERSON>/Pouncer Boss/Eye_Transform2"}, {"time": 3.8667, "name": "<PERSON><PERSON>,<PERSON><PERSON>/Pouncer Boss/Eye_Transform1"}, {"time": 3.9667, "name": "<PERSON><PERSON>,<PERSON><PERSON>/Pouncer Boss/Eye_Transform2"}, {"time": 4.0667, "name": "<PERSON><PERSON>,<PERSON><PERSON>/Pouncer Boss/Eye_Transform1"}]}, "EYE_RIGHT_TOP": {"attachment": [{"name": "<PERSON>ler,<PERSON>r/Pouncer <PERSON>/Eye_Shut"}, {"time": 0.2333, "name": "<PERSON>ler,<PERSON>r/Pouncer Boss/Eye_Normal"}, {"time": 0.4667, "name": "<PERSON>ler,<PERSON>r/Pouncer <PERSON>/Eye_Shut"}, {"time": 0.9667, "name": "<PERSON>ler,<PERSON>r/Pouncer Boss/Eye_Normal"}, {"time": 1.1667, "name": "<PERSON>ler,<PERSON>r/Pouncer <PERSON>/Eye_Shut"}, {"time": 1.5, "name": "<PERSON>ler,<PERSON><PERSON>/<PERSON>uncer <PERSON>/Eye_Shocked"}, {"time": 2.9, "name": "<PERSON>ler,<PERSON>r/Pouncer <PERSON>/Eye_Shut"}, {"time": 3.5, "name": "<PERSON><PERSON>,<PERSON><PERSON>/Pouncer Boss/Eye_Transform1"}, {"time": 3.6, "name": "<PERSON><PERSON>,<PERSON><PERSON>/Pouncer Boss/Eye_Transform2"}, {"time": 3.7, "name": "<PERSON><PERSON>,<PERSON><PERSON>/Pouncer Boss/Eye_Transform1"}, {"time": 3.8, "name": "<PERSON><PERSON>,<PERSON><PERSON>/Pouncer Boss/Eye_Transform2"}, {"time": 3.9, "name": "<PERSON><PERSON>,<PERSON><PERSON>/Pouncer Boss/Eye_Transform1"}, {"time": 4, "name": "<PERSON><PERSON>,<PERSON><PERSON>/Pouncer Boss/Eye_Transform2"}, {"time": 4.1, "name": "<PERSON><PERSON>,<PERSON><PERSON>/Pouncer Boss/Eye_Transform1"}]}, "MouthBtm": {"attachment": [{"time": 0.5667, "name": "Mouth_shut"}]}, "Patroller,Chaser/Pouncer Boss/Mouth": {"attachment": [{"time": 0.5667, "name": "Mouth_shut"}, {"time": 1.5, "name": "Mouth"}, {"time": 2.9333, "name": "Mouth_shut"}, {"time": 3.5, "name": "Mouth"}, {"time": 5.1667, "name": "Mouth_shut"}]}, "Patroller,Chaser/Pouncer Boss/Mouth2": {"attachment": [{"time": 0.5667, "name": "Mouth_shut"}]}, "TurretMouth": {"attachment": [{"time": 0.5, "name": "Patroller,Chaser/Chaser Boss/Tunneler_Mouth_Closed"}, {"time": 1.4333, "name": "Patroller,Chaser/Chaser Boss/Tunneler_Mouth_Wide"}, {"time": 2.1, "name": "Patroller,Chaser/Chaser Boss/Tunneler_Mouth_Closed"}, {"time": 2.2, "name": "Patroller,Chaser/Chaser Boss/Tunneler_Mouth_Wide"}, {"time": 2.4667, "name": "Patroller,Chaser/Chaser Boss/Tunneler_Mouth_Closed"}, {"time": 2.5667, "name": "Patroller,Chaser/Chaser Boss/Tunneler_Mouth_Wide"}, {"time": 2.9, "name": "Patroller,Chaser/Chaser Boss/Tunneler_Mouth_Closed"}, {"time": 2.9667, "name": "Patroller,Chaser/Chaser Boss/Tunneler_Mouth"}, {"time": 3.2333, "name": "Patroller,Chaser/Chaser Boss/Tunneler_Mouth_Closed"}, {"time": 4.2333, "name": "Patroller,Chaser/Chaser Boss/Tunneler_Mouth_Wide"}, {"time": 4.6333, "name": "Patroller,Chaser/Chaser Boss/Tunneler_Mouth"}, {"time": 4.9, "name": "Patroller,Chaser/Chaser Boss/Tunneler_Mouth_Closed"}, {"time": 5, "name": "Patroller,Chaser/Chaser Boss/Tunneler_Mouth_Wide"}, {"time": 5.2333, "name": "Patroller,Chaser/Chaser Boss/Tunneler_Mouth_Closed"}]}}, "bones": {"EYE_LEFT_BTM": {"scale": [{"time": 3.4667}, {"time": 3.5667, "x": 1.234, "y": 1.234, "curve": "stepped"}, {"time": 4.6333, "x": 1.234, "y": 1.234, "curve": [4.65, 1.234, 4.683, 1.472, 4.65, 1.234, 4.683, 1.472]}, {"time": 4.7, "x": 1.472, "y": 1.472, "curve": "stepped"}, {"time": 5.0667, "x": 1.472, "y": 1.472}, {"time": 5.1333, "x": 2.175, "y": 2.175}]}, "EYE_LEFT_TOP": {"scale": [{"time": 3.5}, {"time": 3.6, "x": 1.234, "y": 1.234, "curve": "stepped"}, {"time": 4.6667, "x": 1.234, "y": 1.234, "curve": [4.683, 1.234, 4.717, 1.472, 4.683, 1.234, 4.717, 1.472]}, {"time": 4.7333, "x": 1.472, "y": 1.472, "curve": "stepped"}, {"time": 5.1, "x": 1.472, "y": 1.472}, {"time": 5.1667, "x": 2.175, "y": 2.175}]}, "EYE_RIGHT_BTM": {"scale": [{"time": 3.4667}, {"time": 3.5667, "x": 1.234, "y": 1.234, "curve": "stepped"}, {"time": 4.6333, "x": 1.234, "y": 1.234, "curve": [4.65, 1.234, 4.683, 1.472, 4.65, 1.234, 4.683, 1.472]}, {"time": 4.7, "x": 1.472, "y": 1.472, "curve": "stepped"}, {"time": 5.0667, "x": 1.472, "y": 1.472}, {"time": 5.1333, "x": 2.175, "y": 2.175}]}, "EYE_RIGHT_TOP": {"scale": [{"time": 3.5}, {"time": 3.6, "x": 1.234, "y": 1.234, "curve": "stepped"}, {"time": 4.6667, "x": 1.234, "y": 1.234, "curve": [4.683, 1.234, 4.717, 1.472, 4.683, 1.234, 4.717, 1.472]}, {"time": 4.7333, "x": 1.472, "y": 1.472, "curve": "stepped"}, {"time": 5.1, "x": 1.472, "y": 1.472}, {"time": 5.1667, "x": 2.175, "y": 2.175}]}, "EyeGoopLeft": {"scale": [{"time": 2.6333, "y": 0.495}, {"time": 3.4667, "y": 1.338}]}, "EyeGoopRight": {"scale": [{"time": 2.6333, "y": 0.495}, {"time": 3.4667, "y": 1.338}]}, "MOUTH": {"scale": [{"x": 1.064, "y": 0.499, "curve": [0.042, 1.064, 0.125, 1, 0.042, 0.499, 0.125, 1.119]}, {"time": 0.1667, "y": 1.119, "curve": [0.208, 1, 0.292, 1, 0.208, 1.119, 0.292, 1]}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5}, {"time": 0.5333, "y": 0.52, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 1.4667, "curve": "stepped"}, {"time": 1.5, "y": 0.52, "curve": [1.517, 1, 1.55, 1, 1.517, 0.52, 1.55, 1.224]}, {"time": 1.5667, "y": 1.224, "curve": [1.583, 1, 1.617, 1, 1.583, 1.224, 1.617, 1.001]}, {"time": 1.6333, "y": 1.001, "curve": [1.65, 1, 1.683, 1, 1.65, 1.001, 1.683, 1.224]}, {"time": 1.7, "y": 1.224, "curve": [1.717, 1, 1.75, 1, 1.717, 1.224, 1.75, 1.001]}, {"time": 1.7667, "y": 1.001, "curve": [1.783, 1, 1.817, 1, 1.783, 1.001, 1.817, 1.224]}, {"time": 1.8333, "y": 1.224, "curve": [1.85, 1, 1.883, 1, 1.85, 1.224, 1.883, 1.001]}, {"time": 1.9, "y": 1.001, "curve": [1.917, 1, 1.95, 1, 1.917, 1.001, 1.95, 1.224]}, {"time": 1.9667, "y": 1.224, "curve": [1.983, 1, 2.017, 1, 1.983, 1.224, 2.017, 1.001]}, {"time": 2.0333, "y": 1.001, "curve": [2.05, 1, 2.083, 1, 2.05, 1.001, 2.083, 1.224]}, {"time": 2.1, "y": 1.224, "curve": [2.125, 1, 2.175, 1.043, 2.125, 1.224, 2.175, 0.75]}, {"time": 2.2, "x": 1.043, "y": 0.75, "curve": [2.23, 1.043, 2.263, 1.027, 2.23, 0.75, 2.263, 0.866]}, {"time": 2.3, "y": 1.065, "curve": [2.379, 1.009, 2.472, 1.025, 2.379, 0.985, 2.472, 0.826]}, {"time": 2.5667, "x": 1.043, "y": 0.662, "curve": [2.596, 1.043, 2.63, 1.027, 2.596, 0.662, 2.63, 0.827]}, {"time": 2.6667, "y": 1.108, "curve": [2.726, 1, 2.796, 1, 2.726, 1.055, 2.796, 0.95]}, {"time": 2.8667, "y": 0.842}, {"time": 2.9, "y": 0.52, "curve": "stepped"}, {"time": 2.9333, "curve": "stepped"}, {"time": 3.4667, "curve": "stepped"}, {"time": 3.5, "y": 0.52, "curve": [3.517, 1, 3.55, 1, 3.517, 0.52, 3.55, 1.224]}, {"time": 3.5667, "y": 1.224, "curve": [3.583, 1, 3.617, 1, 3.583, 1.224, 3.617, 1.001]}, {"time": 3.6333, "y": 1.001, "curve": [3.65, 1, 3.683, 1, 3.65, 1.001, 3.683, 1.224]}, {"time": 3.7, "y": 1.224, "curve": [3.717, 1, 3.75, 1, 3.717, 1.224, 3.75, 1.001]}, {"time": 3.7667, "y": 1.001, "curve": [3.783, 1, 3.817, 1, 3.783, 1.001, 3.817, 1.224]}, {"time": 3.8333, "y": 1.224, "curve": [3.85, 1, 3.883, 1, 3.85, 1.224, 3.883, 1.001]}, {"time": 3.9, "y": 1.001, "curve": [3.917, 1, 3.95, 1, 3.917, 1.001, 3.95, 1.224]}, {"time": 3.9667, "y": 1.224, "curve": [3.983, 1, 4.017, 1, 3.983, 1.224, 4.017, 1.001]}, {"time": 4.0333, "y": 1.001, "curve": [4.05, 1, 4.083, 1, 4.05, 1.001, 4.083, 1.224]}, {"time": 4.1, "y": 1.224, "curve": [4.125, 1, 4.175, 1.043, 4.125, 1.224, 4.175, 0.75]}, {"time": 4.2, "x": 1.043, "y": 0.75, "curve": [4.23, 1.043, 4.263, 1.027, 4.23, 0.75, 4.263, 0.866]}, {"time": 4.3, "y": 1.065, "curve": [4.379, 1.009, 4.472, 1.025, 4.379, 0.985, 4.472, 0.826]}, {"time": 4.5667, "x": 1.043, "y": 0.662, "curve": [4.596, 1.043, 4.63, 1.027, 4.596, 0.662, 4.63, 0.827]}, {"time": 4.6667, "y": 1.108, "curve": [4.726, 1, 4.796, 1, 4.726, 1.055, 4.796, 0.95]}, {"time": 4.8667, "y": 0.842}, {"time": 5, "y": 0.793, "curve": [5.079, 1.009, 5.172, 1.025, 5.079, 0.85, 5.172, 0.962]}, {"time": 5.2667, "x": 1.043, "y": 1.079, "curve": [5.277, 1.043, 5.288, 1.027, 5.277, 1.079, 5.288, 1.05]}, {"time": 5.3}]}, "BODY_1": {"rotate": [{"curve": [0.017, 0, 0.05, -5.3]}, {"time": 0.0667, "value": -5.3, "curve": [0.1, -5.3, 0.167, 3.18]}, {"time": 0.2, "value": 3.18, "curve": [0.233, 3.18, 0.3, -6.66]}, {"time": 0.3333, "value": -6.66, "curve": [0.375, -6.66, 0.458, -2.6]}, {"time": 0.5, "value": -2.6}], "translate": [{"curve": [0, 0, 0.075, 0, 0, 95.89, 0.075, 121.52]}, {"time": 0.1, "y": 121.52, "curve": [0.192, 0, 0.467, 0, 0.192, 121.52, 0.467, 71.59]}, {"time": 0.4667}], "scale": [{"x": 1.268, "y": 0.732, "curve": [0, 1.311, 0.035, 1.333, 0, 0.706, 0.035, 0.693]}, {"time": 0.0667, "x": 1.343, "y": 0.687, "curve": [0.08, 0.833, 0.093, 0.636, 0.08, 1.426, 0.093, 1.712]}, {"time": 0.1, "x": 0.636, "y": 1.712, "curve": [0.142, 0.636, 0.229, 0.758, 0.142, 1.712, 0.229, 1.517]}, {"time": 0.3, "x": 0.964, "y": 1.187, "curve": [0.392, 0.974, 0.467, 0.986, 0.392, 1.139, 0.467, 1.075]}, {"time": 0.4667, "curve": [0.483, 1, 0.517, 1.361, 0.483, 1, 0.517, 0.809]}, {"time": 0.5333, "x": 1.361, "y": 0.809, "curve": [0.558, 1.361, 0.608, 0.966, 0.558, 0.809, 0.608, 1.07]}, {"time": 0.6333, "x": 0.966, "y": 1.07, "curve": [0.675, 0.966, 0.758, 1.056, 0.675, 1.07, 0.758, 0.973]}, {"time": 0.8, "x": 1.056, "y": 0.973, "curve": [0.858, 1.056, 0.975, 0.966, 0.858, 0.973, 0.975, 1.07]}, {"time": 1.0333, "x": 0.966, "y": 1.07, "curve": [1.125, 0.966, 1.308, 1.235, 1.125, 1.07, 1.308, 0.832]}, {"time": 1.4, "x": 1.235, "y": 0.832, "curve": [1.433, 1.235, 1.5, 0.902, 1.433, 0.832, 1.5, 1.192]}, {"time": 1.5333, "x": 0.902, "y": 1.192, "curve": [1.558, 0.902, 1.608, 1.118, 1.558, 1.192, 1.608, 0.901]}, {"time": 1.6333, "x": 1.118, "y": 0.901, "curve": [1.65, 1.118, 1.683, 0.964, 1.65, 0.901, 1.683, 1.027]}, {"time": 1.7, "x": 0.964, "y": 1.027, "curve": [1.717, 0.964, 1.75, 1.118, 1.717, 1.027, 1.75, 0.901]}, {"time": 1.7667, "x": 1.118, "y": 0.901, "curve": [1.783, 1.118, 1.817, 0.964, 1.783, 0.901, 1.817, 1.027]}, {"time": 1.8333, "x": 0.964, "y": 1.027, "curve": [1.85, 0.964, 1.883, 1.118, 1.85, 1.027, 1.883, 0.901]}, {"time": 1.9, "x": 1.118, "y": 0.901, "curve": [1.917, 1.118, 1.95, 0.964, 1.917, 0.901, 1.95, 1.027]}, {"time": 1.9667, "x": 0.964, "y": 1.027, "curve": [1.983, 0.964, 2.017, 1.118, 1.983, 1.027, 2.017, 0.901]}, {"time": 2.0333, "x": 1.118, "y": 0.901, "curve": [2.05, 1.118, 2.083, 0.964, 2.05, 0.901, 2.083, 1.027]}, {"time": 2.1, "x": 0.964, "y": 1.027, "curve": [2.117, 0.964, 2.163, 1.026, 2.117, 1.027, 2.163, 0.977]}, {"time": 2.1667, "x": 1.118, "y": 0.901, "curve": [2.169, 0.917, 2.242, 0.791, 2.169, 1.202, 2.242, 1.39]}, {"time": 2.2667, "x": 0.791, "y": 1.39, "curve": [2.333, 0.791, 2.529, 0.965, 2.333, 1.39, 2.529, 1.13]}, {"time": 2.5333, "x": 1.118, "y": 0.901, "curve": [2.558, 1.118, 2.608, 0.791, 2.558, 0.901, 2.608, 1.39]}, {"time": 2.6333, "x": 0.791, "y": 1.39, "curve": [2.692, 0.791, 2.863, 1.077, 2.692, 1.39, 2.863, 1.06]}, {"time": 2.8667, "x": 1.329, "y": 0.768, "curve": [2.883, 1.329, 2.917, 1.175, 2.883, 0.768, 2.917, 0.894]}, {"time": 2.9333, "x": 1.175, "y": 0.894, "curve": [2.95, 1.175, 2.983, 1.258, 2.95, 0.894, 2.983, 0.808]}, {"time": 3, "x": 1.258, "y": 0.808, "curve": [3.017, 1.258, 3.05, 1.175, 3.017, 0.808, 3.05, 0.894]}, {"time": 3.0667, "x": 1.175, "y": 0.894, "curve": [3.083, 1.175, 3.117, 1.258, 3.083, 0.894, 3.117, 0.808]}, {"time": 3.1333, "x": 1.258, "y": 0.808, "curve": [3.15, 1.258, 3.183, 1.175, 3.15, 0.808, 3.183, 0.894]}, {"time": 3.2, "x": 1.175, "y": 0.894, "curve": [3.217, 1.175, 3.25, 1.258, 3.217, 0.894, 3.25, 0.808]}, {"time": 3.2667, "x": 1.258, "y": 0.808, "curve": [3.283, 1.258, 3.317, 1.175, 3.283, 0.808, 3.317, 0.894]}, {"time": 3.3333, "x": 1.175, "y": 0.894, "curve": [3.35, 1.175, 3.383, 1.445, 3.35, 0.894, 3.383, 0.698]}, {"time": 3.4, "x": 1.445, "y": 0.698, "curve": [3.433, 1.445, 3.5, 0.902, 3.433, 0.698, 3.5, 1.192]}, {"time": 3.5333, "x": 0.902, "y": 1.192, "curve": [3.558, 0.902, 3.608, 1.118, 3.558, 1.192, 3.608, 0.901]}, {"time": 3.6333, "x": 1.118, "y": 0.901, "curve": [3.65, 1.118, 3.683, 0.964, 3.65, 0.901, 3.683, 1.027]}, {"time": 3.7, "x": 0.964, "y": 1.027, "curve": [3.717, 0.964, 3.75, 1.118, 3.717, 1.027, 3.75, 0.901]}, {"time": 3.7667, "x": 1.118, "y": 0.901, "curve": [3.783, 1.118, 3.817, 0.964, 3.783, 0.901, 3.817, 1.027]}, {"time": 3.8333, "x": 0.964, "y": 1.027, "curve": [3.85, 0.964, 3.883, 1.118, 3.85, 1.027, 3.883, 0.901]}, {"time": 3.9, "x": 1.118, "y": 0.901, "curve": [3.917, 1.118, 3.95, 0.964, 3.917, 0.901, 3.95, 1.027]}, {"time": 3.9667, "x": 0.964, "y": 1.027, "curve": [3.983, 0.964, 4.017, 1.118, 3.983, 1.027, 4.017, 0.901]}, {"time": 4.0333, "x": 1.118, "y": 0.901, "curve": [4.05, 1.118, 4.083, 0.964, 4.05, 0.901, 4.083, 1.027]}, {"time": 4.1, "x": 0.964, "y": 1.027, "curve": [4.117, 0.964, 4.163, 1.026, 4.117, 1.027, 4.163, 0.977]}, {"time": 4.1667, "x": 1.118, "y": 0.901, "curve": [4.169, 0.917, 4.242, 0.791, 4.169, 1.202, 4.242, 1.39]}, {"time": 4.2667, "x": 0.791, "y": 1.39, "curve": [4.333, 0.791, 4.529, 0.965, 4.333, 1.39, 4.529, 1.13]}, {"time": 4.5333, "x": 1.118, "y": 0.901, "curve": [4.558, 1.118, 4.608, 0.791, 4.558, 0.901, 4.608, 1.39]}, {"time": 4.6333, "x": 0.791, "y": 1.39, "curve": [4.692, 0.791, 4.863, 1.077, 4.692, 1.39, 4.863, 1.06]}, {"time": 4.8667, "x": 1.329, "y": 0.768, "curve": [4.892, 1.329, 4.942, 1.258, 4.892, 0.768, 4.942, 0.808]}, {"time": 4.9667, "x": 1.258, "y": 0.808, "curve": [4.983, 1.258, 5.017, 1.175, 4.983, 0.808, 5.017, 0.894]}, {"time": 5.0333, "x": 1.175, "y": 0.894, "curve": [5.05, 1.175, 5.083, 1.258, 5.05, 0.894, 5.083, 0.808]}, {"time": 5.1, "x": 1.258, "y": 0.808, "curve": [5.117, 1.258, 5.15, 1.175, 5.117, 0.808, 5.15, 0.894]}, {"time": 5.1667, "x": 1.175, "y": 0.894, "curve": [5.183, 1.175, 5.217, 1.258, 5.183, 0.894, 5.217, 0.808]}, {"time": 5.2333, "x": 1.258, "y": 0.808, "curve": [5.25, 1.258, 5.283, 1.175, 5.25, 0.808, 5.283, 0.894]}, {"time": 5.3, "x": 1.175, "y": 0.894, "curve": [5.317, 1.175, 5.35, 1.258, 5.317, 0.894, 5.35, 0.808]}, {"time": 5.3667, "x": 1.258, "y": 0.808, "curve": [5.383, 1.258, 5.417, 1.175, 5.383, 0.808, 5.417, 0.894]}, {"time": 5.4333, "x": 1.175, "y": 0.894, "curve": [5.45, 1.175, 5.483, 1.258, 5.45, 0.894, 5.483, 0.808]}, {"time": 5.5, "x": 1.258, "y": 0.808}], "shear": [{"time": 2.1667, "curve": [2.183, 0, 2.217, 0, 2.183, 0, 2.217, 11.95]}, {"time": 2.2333, "y": 11.95, "curve": [2.3, 0, 2.433, 0, 2.3, 11.95, 2.433, -0.31]}, {"time": 2.5, "y": -0.31}, {"time": 2.5333, "curve": [2.55, 0, 2.583, 0, 2.55, 0, 2.583, -9.07]}, {"time": 2.6, "y": -9.07, "curve": [2.675, 0, 2.825, 0, 2.675, -9.07, 2.825, -0.31]}, {"time": 2.9, "y": -0.31, "curve": "stepped"}, {"time": 4.1667, "curve": [4.183, 0, 4.217, 0, 4.183, 0, 4.217, 11.95]}, {"time": 4.2333, "y": 11.95, "curve": [4.3, 0, 4.433, 0, 4.3, 11.95, 4.433, -0.31]}, {"time": 4.5, "y": -0.31}, {"time": 4.5333, "curve": [4.55, 0, 4.583, 0, 4.55, 0, 4.583, -9.07]}, {"time": 4.6, "y": -9.07, "curve": [4.675, 0, 4.825, 0, 4.675, -9.07, 4.825, -0.31]}, {"time": 4.9, "y": -0.31}]}, "BODY_2": {"rotate": [{"curve": [0.033, 0, 0.1, -5.3]}, {"time": 0.1333, "value": -5.3, "curve": [0.158, -5.3, 0.208, 3.18]}, {"time": 0.2333, "value": 3.18, "curve": [0.258, 3.18, 0.308, -6.66]}, {"time": 0.3333, "value": -6.66, "curve": [0.375, -6.66, 0.458, -2.6]}, {"time": 0.5, "value": -2.6}], "translate": [{"curve": [0, 0, 0.075, 0, 0, 95.89, 0.075, 121.52]}, {"time": 0.1, "y": 121.52, "curve": [0.192, 0, 0.467, 0, 0.192, 121.52, 0.467, 71.59]}, {"time": 0.4667}], "scale": [{"x": 1.268, "y": 0.732, "curve": [0, 1.311, 0.035, 1.333, 0, 0.706, 0.035, 0.693]}, {"time": 0.0667, "x": 1.343, "y": 0.687, "curve": [0.08, 0.833, 0.093, 0.636, 0.08, 1.426, 0.093, 1.712]}, {"time": 0.1, "x": 0.636, "y": 1.712, "curve": [0.142, 0.636, 0.229, 0.758, 0.142, 1.712, 0.229, 1.517]}, {"time": 0.3, "x": 0.964, "y": 1.187, "curve": [0.392, 0.974, 0.467, 0.986, 0.392, 1.139, 0.467, 1.075]}, {"time": 0.4667, "curve": [0.483, 1, 0.517, 1.361, 0.483, 1, 0.517, 0.809]}, {"time": 0.5333, "x": 1.361, "y": 0.809, "curve": [0.558, 1.361, 0.608, 0.966, 0.558, 0.809, 0.608, 1.07]}, {"time": 0.6333, "x": 0.966, "y": 1.07, "curve": [0.675, 0.966, 0.758, 1.056, 0.675, 1.07, 0.758, 0.973]}, {"time": 0.8, "x": 1.056, "y": 0.973, "curve": [0.858, 1.056, 0.975, 0.966, 0.858, 0.973, 0.975, 1.07]}, {"time": 1.0333, "x": 0.966, "y": 1.07, "curve": [1.125, 0.966, 1.308, 1.235, 1.125, 1.07, 1.308, 0.832]}, {"time": 1.4, "x": 1.235, "y": 0.832, "curve": [1.433, 1.235, 1.5, 0.902, 1.433, 0.832, 1.5, 1.192]}, {"time": 1.5333, "x": 0.902, "y": 1.192, "curve": [1.558, 0.902, 1.608, 1.118, 1.558, 1.192, 1.608, 0.901]}, {"time": 1.6333, "x": 1.118, "y": 0.901, "curve": [1.65, 1.118, 1.683, 0.964, 1.65, 0.901, 1.683, 1.027]}, {"time": 1.7, "x": 0.964, "y": 1.027, "curve": [1.717, 0.964, 1.75, 1.118, 1.717, 1.027, 1.75, 0.901]}, {"time": 1.7667, "x": 1.118, "y": 0.901, "curve": [1.783, 1.118, 1.817, 0.964, 1.783, 0.901, 1.817, 1.027]}, {"time": 1.8333, "x": 0.964, "y": 1.027, "curve": [1.85, 0.964, 1.883, 1.118, 1.85, 1.027, 1.883, 0.901]}, {"time": 1.9, "x": 1.118, "y": 0.901, "curve": [1.917, 1.118, 1.95, 0.964, 1.917, 0.901, 1.95, 1.027]}, {"time": 1.9667, "x": 0.964, "y": 1.027, "curve": [1.983, 0.964, 2.017, 1.118, 1.983, 1.027, 2.017, 0.901]}, {"time": 2.0333, "x": 1.118, "y": 0.901, "curve": [2.05, 1.118, 2.083, 0.964, 2.05, 0.901, 2.083, 1.027]}, {"time": 2.1, "x": 0.964, "y": 1.027, "curve": [2.117, 0.964, 2.163, 1.026, 2.117, 1.027, 2.163, 0.977]}, {"time": 2.1667, "x": 1.118, "y": 0.901, "curve": [2.169, 0.917, 2.242, 0.791, 2.169, 1.202, 2.242, 1.39]}, {"time": 2.2667, "x": 0.791, "y": 1.39, "curve": [2.333, 0.791, 2.529, 0.965, 2.333, 1.39, 2.529, 1.13]}, {"time": 2.5333, "x": 1.118, "y": 0.901, "curve": [2.558, 1.118, 2.608, 0.791, 2.558, 0.901, 2.608, 1.39]}, {"time": 2.6333, "x": 0.791, "y": 1.39, "curve": [2.692, 0.791, 2.863, 1.077, 2.692, 1.39, 2.863, 1.06]}, {"time": 2.8667, "x": 1.329, "y": 0.768, "curve": [2.883, 1.329, 2.917, 1.175, 2.883, 0.768, 2.917, 0.894]}, {"time": 2.9333, "x": 1.175, "y": 0.894, "curve": [2.95, 1.175, 2.983, 1.258, 2.95, 0.894, 2.983, 0.808]}, {"time": 3, "x": 1.258, "y": 0.808, "curve": [3.017, 1.258, 3.05, 1.175, 3.017, 0.808, 3.05, 0.894]}, {"time": 3.0667, "x": 1.175, "y": 0.894, "curve": [3.083, 1.175, 3.117, 1.258, 3.083, 0.894, 3.117, 0.808]}, {"time": 3.1333, "x": 1.258, "y": 0.808, "curve": [3.15, 1.258, 3.183, 1.175, 3.15, 0.808, 3.183, 0.894]}, {"time": 3.2, "x": 1.175, "y": 0.894, "curve": [3.217, 1.175, 3.25, 1.258, 3.217, 0.894, 3.25, 0.808]}, {"time": 3.2667, "x": 1.258, "y": 0.808, "curve": [3.283, 1.258, 3.317, 1.175, 3.283, 0.808, 3.317, 0.894]}, {"time": 3.3333, "x": 1.175, "y": 0.894, "curve": [3.35, 1.175, 3.383, 1.445, 3.35, 0.894, 3.383, 0.698]}, {"time": 3.4, "x": 1.445, "y": 0.698, "curve": [3.433, 1.445, 3.5, 0.902, 3.433, 0.698, 3.5, 1.192]}, {"time": 3.5333, "x": 0.902, "y": 1.192, "curve": [3.558, 0.902, 3.608, 1.118, 3.558, 1.192, 3.608, 0.901]}, {"time": 3.6333, "x": 1.118, "y": 0.901, "curve": [3.65, 1.118, 3.683, 0.964, 3.65, 0.901, 3.683, 1.027]}, {"time": 3.7, "x": 0.964, "y": 1.027, "curve": [3.717, 0.964, 3.75, 1.118, 3.717, 1.027, 3.75, 0.901]}, {"time": 3.7667, "x": 1.118, "y": 0.901, "curve": [3.783, 1.118, 3.817, 0.964, 3.783, 0.901, 3.817, 1.027]}, {"time": 3.8333, "x": 0.964, "y": 1.027, "curve": [3.85, 0.964, 3.883, 1.118, 3.85, 1.027, 3.883, 0.901]}, {"time": 3.9, "x": 1.118, "y": 0.901, "curve": [3.917, 1.118, 3.95, 0.964, 3.917, 0.901, 3.95, 1.027]}, {"time": 3.9667, "x": 0.964, "y": 1.027, "curve": [3.983, 0.964, 4.017, 1.118, 3.983, 1.027, 4.017, 0.901]}, {"time": 4.0333, "x": 1.118, "y": 0.901, "curve": [4.05, 1.118, 4.083, 0.964, 4.05, 0.901, 4.083, 1.027]}, {"time": 4.1, "x": 0.964, "y": 1.027, "curve": [4.117, 0.964, 4.163, 1.026, 4.117, 1.027, 4.163, 0.977]}, {"time": 4.1667, "x": 1.118, "y": 0.901, "curve": [4.169, 0.917, 4.242, 0.791, 4.169, 1.202, 4.242, 1.39]}, {"time": 4.2667, "x": 0.791, "y": 1.39, "curve": [4.333, 0.791, 4.529, 0.965, 4.333, 1.39, 4.529, 1.13]}, {"time": 4.5333, "x": 1.118, "y": 0.901, "curve": [4.558, 1.118, 4.608, 0.791, 4.558, 0.901, 4.608, 1.39]}, {"time": 4.6333, "x": 0.791, "y": 1.39, "curve": [4.692, 0.791, 4.863, 1.077, 4.692, 1.39, 4.863, 1.06]}, {"time": 4.8667, "x": 1.329, "y": 0.768, "curve": [4.892, 1.329, 4.942, 1.258, 4.892, 0.768, 4.942, 0.808]}, {"time": 4.9667, "x": 1.258, "y": 0.808, "curve": [4.983, 1.258, 5.017, 1.175, 4.983, 0.808, 5.017, 0.894]}, {"time": 5.0333, "x": 1.175, "y": 0.894, "curve": [5.05, 1.175, 5.083, 1.258, 5.05, 0.894, 5.083, 0.808]}, {"time": 5.1, "x": 1.258, "y": 0.808, "curve": [5.117, 1.258, 5.15, 1.175, 5.117, 0.808, 5.15, 0.894]}, {"time": 5.1667, "x": 1.175, "y": 0.894, "curve": [5.183, 1.175, 5.217, 1.258, 5.183, 0.894, 5.217, 0.808]}, {"time": 5.2333, "x": 1.258, "y": 0.808, "curve": [5.25, 1.258, 5.283, 1.175, 5.25, 0.808, 5.283, 0.894]}, {"time": 5.3, "x": 1.175, "y": 0.894, "curve": [5.317, 1.175, 5.35, 1.258, 5.317, 0.894, 5.35, 0.808]}, {"time": 5.3667, "x": 1.258, "y": 0.808, "curve": [5.383, 1.258, 5.417, 1.175, 5.383, 0.808, 5.417, 0.894]}, {"time": 5.4333, "x": 1.175, "y": 0.894, "curve": [5.45, 1.175, 5.483, 1.258, 5.45, 0.894, 5.483, 0.808]}, {"time": 5.5, "x": 1.258, "y": 0.808}], "shear": [{"time": 2.1667, "curve": [2.183, 0, 2.217, 0, 2.183, 0, 2.217, 11.95]}, {"time": 2.2333, "y": 11.95, "curve": [2.3, 0, 2.433, 0, 2.3, 11.95, 2.433, -0.31]}, {"time": 2.5, "y": -0.31}, {"time": 2.5333, "curve": [2.55, 0, 2.583, 0, 2.55, 0, 2.583, -9.07]}, {"time": 2.6, "y": -9.07, "curve": [2.675, 0, 2.825, 0, 2.675, -9.07, 2.825, -0.31]}, {"time": 2.9, "y": -0.31}, {"time": 3.2, "curve": "stepped"}, {"time": 4.1667, "curve": [4.183, 0, 4.217, 0, 4.183, 0, 4.217, 11.95]}, {"time": 4.2333, "y": 11.95, "curve": [4.3, 0, 4.433, 0, 4.3, 11.95, 4.433, -0.31]}, {"time": 4.5, "y": -0.31}, {"time": 4.5333, "curve": [4.55, 0, 4.583, 0, 4.55, 0, 4.583, -9.07]}, {"time": 4.6, "y": -9.07, "curve": [4.675, 0, 4.825, 0, 4.675, -9.07, 4.825, -0.31]}, {"time": 4.9, "y": -0.31}]}, "BODY_3": {"rotate": [{"curve": [0.033, 0, 0.1, -5.3]}, {"time": 0.1333, "value": -5.3, "curve": [0.158, -5.3, 0.208, 3.18]}, {"time": 0.2333, "value": 3.18, "curve": [0.258, 3.18, 0.308, -6.66]}, {"time": 0.3333, "value": -6.66, "curve": [0.375, -6.66, 0.458, -2.6]}, {"time": 0.5, "value": -2.6}], "translate": [{"curve": [0, 0, 0.075, 0, 0, 95.89, 0.075, 121.52]}, {"time": 0.1, "y": 121.52, "curve": [0.192, 0, 0.467, 0, 0.192, 121.52, 0.467, 71.59]}, {"time": 0.4667}], "scale": [{"x": 1.268, "y": 0.732, "curve": [0, 1.311, 0.035, 1.333, 0, 0.706, 0.035, 0.693]}, {"time": 0.0667, "x": 1.343, "y": 0.687, "curve": [0.08, 0.833, 0.093, 0.636, 0.08, 1.426, 0.093, 1.712]}, {"time": 0.1, "x": 0.636, "y": 1.712, "curve": [0.142, 0.636, 0.229, 0.758, 0.142, 1.712, 0.229, 1.517]}, {"time": 0.3, "x": 0.964, "y": 1.187, "curve": [0.392, 0.974, 0.467, 0.986, 0.392, 1.139, 0.467, 1.075]}, {"time": 0.4667, "curve": [0.483, 1, 0.517, 1.361, 0.483, 1, 0.517, 0.809]}, {"time": 0.5333, "x": 1.361, "y": 0.809, "curve": [0.558, 1.361, 0.608, 0.966, 0.558, 0.809, 0.608, 1.07]}, {"time": 0.6333, "x": 0.966, "y": 1.07, "curve": [0.675, 0.966, 0.758, 1.056, 0.675, 1.07, 0.758, 0.973]}, {"time": 0.8, "x": 1.056, "y": 0.973, "curve": [0.858, 1.056, 0.975, 0.966, 0.858, 0.973, 0.975, 1.07]}, {"time": 1.0333, "x": 0.966, "y": 1.07, "curve": [1.125, 0.966, 1.308, 1.235, 1.125, 1.07, 1.308, 0.832]}, {"time": 1.4, "x": 1.235, "y": 0.832, "curve": [1.433, 1.235, 1.5, 0.902, 1.433, 0.832, 1.5, 1.192]}, {"time": 1.5333, "x": 0.902, "y": 1.192, "curve": [1.558, 0.902, 1.608, 1.118, 1.558, 1.192, 1.608, 0.901]}, {"time": 1.6333, "x": 1.118, "y": 0.901, "curve": [1.65, 1.118, 1.683, 0.964, 1.65, 0.901, 1.683, 1.027]}, {"time": 1.7, "x": 0.964, "y": 1.027, "curve": [1.717, 0.964, 1.75, 1.118, 1.717, 1.027, 1.75, 0.901]}, {"time": 1.7667, "x": 1.118, "y": 0.901, "curve": [1.783, 1.118, 1.817, 0.964, 1.783, 0.901, 1.817, 1.027]}, {"time": 1.8333, "x": 0.964, "y": 1.027, "curve": [1.85, 0.964, 1.883, 1.118, 1.85, 1.027, 1.883, 0.901]}, {"time": 1.9, "x": 1.118, "y": 0.901, "curve": [1.917, 1.118, 1.95, 0.964, 1.917, 0.901, 1.95, 1.027]}, {"time": 1.9667, "x": 0.964, "y": 1.027, "curve": [1.983, 0.964, 2.017, 1.118, 1.983, 1.027, 2.017, 0.901]}, {"time": 2.0333, "x": 1.118, "y": 0.901, "curve": [2.05, 1.118, 2.083, 0.964, 2.05, 0.901, 2.083, 1.027]}, {"time": 2.1, "x": 0.964, "y": 1.027, "curve": [2.117, 0.964, 2.163, 1.026, 2.117, 1.027, 2.163, 0.977]}, {"time": 2.1667, "x": 1.118, "y": 0.901, "curve": [2.169, 0.917, 2.242, 0.791, 2.169, 1.202, 2.242, 1.39]}, {"time": 2.2667, "x": 0.791, "y": 1.39, "curve": [2.333, 0.791, 2.529, 0.965, 2.333, 1.39, 2.529, 1.13]}, {"time": 2.5333, "x": 1.118, "y": 0.901, "curve": [2.558, 1.118, 2.608, 0.791, 2.558, 0.901, 2.608, 1.39]}, {"time": 2.6333, "x": 0.791, "y": 1.39, "curve": [2.692, 0.791, 2.863, 1.077, 2.692, 1.39, 2.863, 1.06]}, {"time": 2.8667, "x": 1.329, "y": 0.768, "curve": [2.883, 1.329, 2.917, 1.175, 2.883, 0.768, 2.917, 0.894]}, {"time": 2.9333, "x": 1.175, "y": 0.894, "curve": [2.95, 1.175, 2.983, 1.258, 2.95, 0.894, 2.983, 0.808]}, {"time": 3, "x": 1.258, "y": 0.808, "curve": [3.017, 1.258, 3.05, 1.175, 3.017, 0.808, 3.05, 0.894]}, {"time": 3.0667, "x": 1.175, "y": 0.894, "curve": [3.083, 1.175, 3.117, 1.258, 3.083, 0.894, 3.117, 0.808]}, {"time": 3.1333, "x": 1.258, "y": 0.808, "curve": [3.15, 1.258, 3.183, 1.175, 3.15, 0.808, 3.183, 0.894]}, {"time": 3.2, "x": 1.175, "y": 0.894, "curve": [3.217, 1.175, 3.25, 1.258, 3.217, 0.894, 3.25, 0.808]}, {"time": 3.2667, "x": 1.258, "y": 0.808, "curve": [3.283, 1.258, 3.317, 1.175, 3.283, 0.808, 3.317, 0.894]}, {"time": 3.3333, "x": 1.175, "y": 0.894, "curve": [3.35, 1.175, 3.383, 1.445, 3.35, 0.894, 3.383, 0.698]}, {"time": 3.4, "x": 1.445, "y": 0.698, "curve": [3.433, 1.445, 3.5, 0.902, 3.433, 0.698, 3.5, 1.192]}, {"time": 3.5333, "x": 0.902, "y": 1.192, "curve": [3.558, 0.902, 3.608, 1.118, 3.558, 1.192, 3.608, 0.901]}, {"time": 3.6333, "x": 1.118, "y": 0.901, "curve": [3.65, 1.118, 3.683, 0.964, 3.65, 0.901, 3.683, 1.027]}, {"time": 3.7, "x": 0.964, "y": 1.027, "curve": [3.717, 0.964, 3.75, 1.118, 3.717, 1.027, 3.75, 0.901]}, {"time": 3.7667, "x": 1.118, "y": 0.901, "curve": [3.783, 1.118, 3.817, 0.964, 3.783, 0.901, 3.817, 1.027]}, {"time": 3.8333, "x": 0.964, "y": 1.027, "curve": [3.85, 0.964, 3.883, 1.118, 3.85, 1.027, 3.883, 0.901]}, {"time": 3.9, "x": 1.118, "y": 0.901, "curve": [3.917, 1.118, 3.95, 0.964, 3.917, 0.901, 3.95, 1.027]}, {"time": 3.9667, "x": 0.964, "y": 1.027, "curve": [3.983, 0.964, 4.017, 1.118, 3.983, 1.027, 4.017, 0.901]}, {"time": 4.0333, "x": 1.118, "y": 0.901, "curve": [4.05, 1.118, 4.083, 0.964, 4.05, 0.901, 4.083, 1.027]}, {"time": 4.1, "x": 0.964, "y": 1.027, "curve": [4.117, 0.964, 4.163, 1.026, 4.117, 1.027, 4.163, 0.977]}, {"time": 4.1667, "x": 1.118, "y": 0.901, "curve": [4.169, 0.917, 4.242, 0.791, 4.169, 1.202, 4.242, 1.39]}, {"time": 4.2667, "x": 0.791, "y": 1.39, "curve": [4.333, 0.791, 4.529, 0.965, 4.333, 1.39, 4.529, 1.13]}, {"time": 4.5333, "x": 1.118, "y": 0.901, "curve": [4.558, 1.118, 4.608, 0.791, 4.558, 0.901, 4.608, 1.39]}, {"time": 4.6333, "x": 0.791, "y": 1.39, "curve": [4.692, 0.791, 4.863, 1.077, 4.692, 1.39, 4.863, 1.06]}, {"time": 4.8667, "x": 1.329, "y": 0.768, "curve": [4.892, 1.329, 4.942, 1.258, 4.892, 0.768, 4.942, 0.808]}, {"time": 4.9667, "x": 1.258, "y": 0.808, "curve": [4.983, 1.258, 5.017, 1.175, 4.983, 0.808, 5.017, 0.894]}, {"time": 5.0333, "x": 1.175, "y": 0.894, "curve": [5.05, 1.175, 5.083, 1.258, 5.05, 0.894, 5.083, 0.808]}, {"time": 5.1, "x": 1.258, "y": 0.808, "curve": [5.117, 1.258, 5.15, 1.175, 5.117, 0.808, 5.15, 0.894]}, {"time": 5.1667, "x": 1.175, "y": 0.894, "curve": [5.183, 1.175, 5.217, 1.258, 5.183, 0.894, 5.217, 0.808]}, {"time": 5.2333, "x": 1.258, "y": 0.808, "curve": [5.25, 1.258, 5.283, 1.175, 5.25, 0.808, 5.283, 0.894]}, {"time": 5.3, "x": 1.175, "y": 0.894, "curve": [5.317, 1.175, 5.35, 1.258, 5.317, 0.894, 5.35, 0.808]}, {"time": 5.3667, "x": 1.258, "y": 0.808, "curve": [5.383, 1.258, 5.417, 1.175, 5.383, 0.808, 5.417, 0.894]}, {"time": 5.4333, "x": 1.175, "y": 0.894, "curve": [5.45, 1.175, 5.483, 1.258, 5.45, 0.894, 5.483, 0.808]}, {"time": 5.5, "x": 1.258, "y": 0.808}], "shear": [{"time": 2.1667, "curve": [2.183, 0, 2.217, 0, 2.183, 0, 2.217, 11.95]}, {"time": 2.2333, "y": 11.95, "curve": [2.3, 0, 2.433, 0, 2.3, 11.95, 2.433, -0.31]}, {"time": 2.5, "y": -0.31}, {"time": 2.5333, "curve": [2.55, 0, 2.583, 0, 2.55, 0, 2.583, -9.07]}, {"time": 2.6, "y": -9.07, "curve": [2.675, 0, 2.825, 0, 2.675, -9.07, 2.825, -0.31]}, {"time": 2.9, "y": -0.31}, {"time": 3.2, "curve": "stepped"}, {"time": 4.1667, "curve": [4.183, 0, 4.217, 0, 4.183, 0, 4.217, 11.95]}, {"time": 4.2333, "y": 11.95, "curve": [4.3, 0, 4.433, 0, 4.3, 11.95, 4.433, -0.31]}, {"time": 4.5, "y": -0.31}, {"time": 4.5333, "curve": [4.55, 0, 4.583, 0, 4.55, 0, 4.583, -9.07]}, {"time": 4.6, "y": -9.07, "curve": [4.675, 0, 4.825, 0, 4.675, -9.07, 4.825, -0.31]}, {"time": 4.9, "y": -0.31}]}, "Main": {"rotate": [{"curve": [0.017, 0, 0.05, -5.3]}, {"time": 0.0667, "value": -5.3, "curve": [0.1, -5.3, 0.167, 3.18]}, {"time": 0.2, "value": 3.18, "curve": [0.233, 3.18, 0.3, -6.66]}, {"time": 0.3333, "value": -6.66, "curve": [0.375, -6.66, 0.458, 0]}, {"time": 0.5}], "translate": [{"curve": [0, 0, 0.075, 0, 0, 95.89, 0.075, 121.52]}, {"time": 0.1, "y": 121.52, "curve": [0.192, 0, 0.467, 0, 0.192, 121.52, 0.467, 71.59]}, {"time": 0.4667}], "scale": [{"x": 1.268, "y": 0.732, "curve": [0, 1.311, 0.035, 1.333, 0, 0.706, 0.035, 0.693]}, {"time": 0.0667, "x": 1.343, "y": 0.687, "curve": [0.08, 0.833, 0.093, 0.636, 0.08, 1.426, 0.093, 1.712]}, {"time": 0.1, "x": 0.636, "y": 1.712, "curve": [0.142, 0.636, 0.229, 0.758, 0.142, 1.712, 0.229, 1.517]}, {"time": 0.3, "x": 0.964, "y": 1.187, "curve": [0.392, 0.974, 0.467, 0.986, 0.392, 1.139, 0.467, 1.075]}, {"time": 0.4667, "curve": [0.483, 1, 0.517, 1.361, 0.483, 1, 0.517, 0.809]}, {"time": 0.5333, "x": 1.361, "y": 0.809, "curve": [0.558, 1.361, 0.608, 0.966, 0.558, 0.809, 0.608, 1.07]}, {"time": 0.6333, "x": 0.966, "y": 1.07, "curve": [0.675, 0.966, 0.758, 1.056, 0.675, 1.07, 0.758, 0.973]}, {"time": 0.8, "x": 1.056, "y": 0.973, "curve": [0.858, 1.056, 0.975, 0.966, 0.858, 0.973, 0.975, 1.07]}, {"time": 1.0333, "x": 0.966, "y": 1.07, "curve": [1.125, 0.966, 1.308, 1.235, 1.125, 1.07, 1.308, 0.832]}, {"time": 1.4, "x": 1.235, "y": 0.832, "curve": [1.433, 1.235, 1.5, 0.902, 1.433, 0.832, 1.5, 1.192]}, {"time": 1.5333, "x": 0.902, "y": 1.192, "curve": [1.558, 0.902, 1.608, 1.118, 1.558, 1.192, 1.608, 0.901]}, {"time": 1.6333, "x": 1.118, "y": 0.901, "curve": [1.65, 1.118, 1.683, 0.964, 1.65, 0.901, 1.683, 1.027]}, {"time": 1.7, "x": 0.964, "y": 1.027, "curve": [1.717, 0.964, 1.75, 1.118, 1.717, 1.027, 1.75, 0.901]}, {"time": 1.7667, "x": 1.118, "y": 0.901, "curve": [1.783, 1.118, 1.817, 0.964, 1.783, 0.901, 1.817, 1.027]}, {"time": 1.8333, "x": 0.964, "y": 1.027, "curve": [1.85, 0.964, 1.883, 1.118, 1.85, 1.027, 1.883, 0.901]}, {"time": 1.9, "x": 1.118, "y": 0.901, "curve": [1.917, 1.118, 1.95, 0.964, 1.917, 0.901, 1.95, 1.027]}, {"time": 1.9667, "x": 0.964, "y": 1.027, "curve": [1.983, 0.964, 2.017, 1.118, 1.983, 1.027, 2.017, 0.901]}, {"time": 2.0333, "x": 1.118, "y": 0.901, "curve": [2.05, 1.118, 2.083, 0.964, 2.05, 0.901, 2.083, 1.027]}, {"time": 2.1, "x": 0.964, "y": 1.027, "curve": [2.117, 0.964, 2.163, 1.026, 2.117, 1.027, 2.163, 0.977]}, {"time": 2.1667, "x": 1.118, "y": 0.901, "curve": [2.169, 0.917, 2.242, 0.791, 2.169, 1.202, 2.242, 1.39]}, {"time": 2.2667, "x": 0.791, "y": 1.39, "curve": [2.333, 0.791, 2.529, 0.965, 2.333, 1.39, 2.529, 1.13]}, {"time": 2.5333, "x": 1.118, "y": 0.901, "curve": [2.558, 1.118, 2.608, 0.791, 2.558, 0.901, 2.608, 1.39]}, {"time": 2.6333, "x": 0.791, "y": 1.39, "curve": [2.692, 0.791, 2.863, 1.077, 2.692, 1.39, 2.863, 1.06]}, {"time": 2.8667, "x": 1.329, "y": 0.768, "curve": [2.883, 1.329, 2.917, 1.175, 2.883, 0.768, 2.917, 0.894]}, {"time": 2.9333, "x": 1.175, "y": 0.894, "curve": [2.95, 1.175, 2.983, 1.258, 2.95, 0.894, 2.983, 0.808]}, {"time": 3, "x": 1.258, "y": 0.808, "curve": [3.017, 1.258, 3.05, 1.175, 3.017, 0.808, 3.05, 0.894]}, {"time": 3.0667, "x": 1.175, "y": 0.894, "curve": [3.083, 1.175, 3.117, 1.258, 3.083, 0.894, 3.117, 0.808]}, {"time": 3.1333, "x": 1.258, "y": 0.808, "curve": [3.15, 1.258, 3.183, 1.175, 3.15, 0.808, 3.183, 0.894]}, {"time": 3.2, "x": 1.175, "y": 0.894, "curve": [3.217, 1.175, 3.25, 1.258, 3.217, 0.894, 3.25, 0.808]}, {"time": 3.2667, "x": 1.258, "y": 0.808, "curve": [3.283, 1.258, 3.317, 1.175, 3.283, 0.808, 3.317, 0.894]}, {"time": 3.3333, "x": 1.175, "y": 0.894, "curve": [3.35, 1.175, 3.383, 1.445, 3.35, 0.894, 3.383, 0.698]}, {"time": 3.4, "x": 1.445, "y": 0.698, "curve": [3.433, 1.445, 3.5, 0.902, 3.433, 0.698, 3.5, 1.192]}, {"time": 3.5333, "x": 0.902, "y": 1.192, "curve": [3.558, 0.902, 3.608, 1.118, 3.558, 1.192, 3.608, 0.901]}, {"time": 3.6333, "x": 1.118, "y": 0.901, "curve": [3.65, 1.118, 3.683, 0.964, 3.65, 0.901, 3.683, 1.027]}, {"time": 3.7, "x": 0.964, "y": 1.027, "curve": [3.717, 0.964, 3.75, 1.118, 3.717, 1.027, 3.75, 0.901]}, {"time": 3.7667, "x": 1.118, "y": 0.901, "curve": [3.783, 1.118, 3.817, 0.964, 3.783, 0.901, 3.817, 1.027]}, {"time": 3.8333, "x": 0.964, "y": 1.027, "curve": [3.85, 0.964, 3.883, 1.118, 3.85, 1.027, 3.883, 0.901]}, {"time": 3.9, "x": 1.118, "y": 0.901, "curve": [3.917, 1.118, 3.95, 0.964, 3.917, 0.901, 3.95, 1.027]}, {"time": 3.9667, "x": 0.964, "y": 1.027, "curve": [3.983, 0.964, 4.017, 1.118, 3.983, 1.027, 4.017, 0.901]}, {"time": 4.0333, "x": 1.118, "y": 0.901, "curve": [4.05, 1.118, 4.083, 0.964, 4.05, 0.901, 4.083, 1.027]}, {"time": 4.1, "x": 0.964, "y": 1.027, "curve": [4.117, 0.964, 4.163, 1.026, 4.117, 1.027, 4.163, 0.977]}, {"time": 4.1667, "x": 1.118, "y": 0.901, "curve": [4.169, 0.917, 4.242, 0.791, 4.169, 1.202, 4.242, 1.39]}, {"time": 4.2667, "x": 0.791, "y": 1.39, "curve": [4.333, 0.791, 4.529, 0.965, 4.333, 1.39, 4.529, 1.13]}, {"time": 4.5333, "x": 1.118, "y": 0.901, "curve": [4.558, 1.118, 4.608, 0.791, 4.558, 0.901, 4.608, 1.39]}, {"time": 4.6333, "x": 0.791, "y": 1.39, "curve": [4.692, 0.791, 4.863, 1.077, 4.692, 1.39, 4.863, 1.06]}, {"time": 4.8667, "x": 1.329, "y": 0.768, "curve": [4.892, 1.329, 4.942, 1.258, 4.892, 0.768, 4.942, 0.808]}, {"time": 4.9667, "x": 1.258, "y": 0.808, "curve": [4.983, 1.258, 5.017, 1.175, 4.983, 0.808, 5.017, 0.894]}, {"time": 5.0333, "x": 1.175, "y": 0.894, "curve": [5.05, 1.175, 5.083, 1.258, 5.05, 0.894, 5.083, 0.808]}, {"time": 5.1, "x": 1.258, "y": 0.808, "curve": [5.117, 1.258, 5.15, 1.175, 5.117, 0.808, 5.15, 0.894]}, {"time": 5.1667, "x": 1.175, "y": 0.894, "curve": [5.183, 1.175, 5.217, 1.258, 5.183, 0.894, 5.217, 0.808]}, {"time": 5.2333, "x": 1.258, "y": 0.808, "curve": [5.25, 1.258, 5.283, 1.175, 5.25, 0.808, 5.283, 0.894]}, {"time": 5.3, "x": 1.175, "y": 0.894, "curve": [5.317, 1.175, 5.35, 1.258, 5.317, 0.894, 5.35, 0.808]}, {"time": 5.3667, "x": 1.258, "y": 0.808, "curve": [5.383, 1.258, 5.417, 1.175, 5.383, 0.808, 5.417, 0.894]}, {"time": 5.4333, "x": 1.175, "y": 0.894, "curve": [5.45, 1.175, 5.483, 1.258, 5.45, 0.894, 5.483, 0.808]}, {"time": 5.5, "x": 1.258, "y": 0.808}], "shear": [{"time": 2.1667, "curve": [2.183, 0, 2.217, 0, 2.183, 0, 2.217, 11.95]}, {"time": 2.2333, "y": 11.95, "curve": [2.3, 0, 2.433, 0, 2.3, 11.95, 2.433, -0.31]}, {"time": 2.5, "y": -0.31}, {"time": 2.5333, "curve": [2.55, 0, 2.583, 0, 2.55, 0, 2.583, -9.07]}, {"time": 2.6, "y": -9.07, "curve": [2.675, 0, 2.825, 0, 2.675, -9.07, 2.825, -0.31]}, {"time": 2.9, "y": -0.31}, {"time": 3.2, "curve": "stepped"}, {"time": 4.1667, "curve": [4.183, 0, 4.217, 0, 4.183, 0, 4.217, 11.95]}, {"time": 4.2333, "y": 11.95, "curve": [4.3, 0, 4.433, 0, 4.3, 11.95, 4.433, -0.31]}, {"time": 4.5, "y": -0.31}, {"time": 4.5333, "curve": [4.55, 0, 4.583, 0, 4.55, 0, 4.583, -9.07]}, {"time": 4.6, "y": -9.07, "curve": [4.675, 0, 4.825, 0, 4.675, -9.07, 4.825, -0.31]}, {"time": 4.9, "y": -0.31}]}, "FACE": {"rotate": [{"curve": [0.025, 0, 0.075, -4.87]}, {"time": 0.1, "value": -4.87, "curve": [0.125, -4.87, 0.175, 2.69]}, {"time": 0.2, "value": 2.69, "curve": [0.233, 2.69, 0.3, 0]}, {"time": 0.3333}], "translate": [{"x": 2.01, "y": -48.7, "curve": [0.033, 2.01, 0.1, -17.57, 0.033, -48.7, 0.1, 59.99]}, {"time": 0.1333, "x": -17.57, "y": 59.99, "curve": [0.217, -17.57, 0.437, -9.77, 0.217, 59.99, 0.437, 33.38]}, {"time": 0.4667}]}, "root": {"translate": [{"time": 4.8333, "curve": [4.85, 0, 4.883, 12.79, 4.85, 0, 4.883, 0]}, {"time": 4.9, "x": 12.79, "curve": [4.917, 12.79, 4.95, 0, 4.917, 0, 4.95, 0]}, {"time": 4.9667, "curve": [4.983, 0, 5.017, 12.79, 4.983, 0, 5.017, 0]}, {"time": 5.0333, "x": 12.79, "curve": [5.05, 12.79, 5.083, 0, 5.05, 0, 5.083, 0]}, {"time": 5.1, "curve": [5.117, 0, 5.15, 12.79, 5.117, 0, 5.15, 0]}, {"time": 5.1667, "x": 12.79, "curve": [5.183, 12.79, 5.217, 0, 5.183, 0, 5.217, 0]}, {"time": 5.2333, "curve": [5.25, 0, 5.283, 12.79, 5.25, 0, 5.283, 0]}, {"time": 5.3, "x": 12.79, "curve": [5.317, 12.79, 5.35, 0, 5.317, 0, 5.35, 0]}, {"time": 5.3667, "curve": [5.383, 0, 5.417, 12.79, 5.383, 0, 5.417, 0]}, {"time": 5.4333, "x": 12.79, "curve": [5.45, 12.79, 5.483, 0, 5.45, 0, 5.483, 0]}, {"time": 5.5}], "scale": [{"time": 3.4667, "curve": [3.483, 1, 3.517, 1.13, 3.483, 1, 3.517, 1.13]}, {"time": 3.5333, "x": 1.13, "y": 1.13, "curve": "stepped"}, {"time": 4.1333, "x": 1.13, "y": 1.13, "curve": [4.15, 1.13, 4.183, 0.846, 4.15, 1.13, 4.183, 0.846]}, {"time": 4.2, "x": 0.846, "y": 0.846, "curve": "stepped"}, {"time": 4.5667, "x": 0.846, "y": 0.846, "curve": [4.583, 0.846, 4.617, 0.456, 4.583, 0.846, 4.617, 0.798]}, {"time": 4.6333, "x": 0.456, "y": 0.798, "curve": "stepped"}, {"time": 5, "x": 0.456, "y": 0.798, "curve": [5.017, 0.456, 5.05, 0.525, 5.017, 0.798, 5.05, 0.43]}, {"time": 5.0667, "x": 0.525, "y": 0.43, "curve": "stepped"}, {"time": 5.2333, "x": 0.525, "y": 0.43, "curve": [5.25, 0.525, 5.283, 0.267, 5.25, 0.43, 5.283, 0.419]}, {"time": 5.3, "x": 0.267, "y": 0.419}]}, "Antler_1": {"rotate": [{"time": 0.4667, "curve": [0.483, 0, 0.517, -28.81]}, {"time": 0.5333, "value": -28.81, "curve": [0.558, -28.81, 0.608, 18.63]}, {"time": 0.6333, "value": 18.63, "curve": [0.683, 18.63, 0.783, -14.31]}, {"time": 0.8333, "value": -14.31, "curve": [0.883, -14.31, 0.983, 6.43]}, {"time": 1.0333, "value": 6.43, "curve": [1.083, 6.43, 1.183, 0]}, {"time": 1.2333, "curve": "stepped"}, {"time": 2.1333, "curve": [2.15, 0, 2.183, -28.81]}, {"time": 2.2, "value": -28.81, "curve": [2.225, -28.81, 2.275, 18.63]}, {"time": 2.3, "value": 18.63, "curve": [2.35, 18.63, 2.45, -14.31]}, {"time": 2.5, "value": -14.31, "curve": [2.525, -14.31, 2.575, 0]}, {"time": 2.6, "curve": [2.617, 0, 2.65, 18.63]}, {"time": 2.6667, "value": 18.63, "curve": [2.717, 18.63, 2.817, -14.31]}, {"time": 2.8667, "value": -14.31, "curve": [2.917, -14.31, 3.017, 6.43]}, {"time": 3.0667, "value": 6.43, "curve": [3.117, 6.43, 3.217, 0]}, {"time": 3.2667, "curve": "stepped"}, {"time": 4.1667, "curve": [4.183, 0, 4.217, -28.81]}, {"time": 4.2333, "value": -28.81, "curve": [4.258, -28.81, 4.308, 18.63]}, {"time": 4.3333, "value": 18.63, "curve": [4.383, 18.63, 4.483, -14.31]}, {"time": 4.5333, "value": -14.31, "curve": [4.558, -14.31, 4.608, 0]}, {"time": 4.6333, "curve": [4.65, 0, 4.683, 18.63]}, {"time": 4.7, "value": 18.63, "curve": [4.75, 18.63, 4.85, -14.31]}, {"time": 4.9, "value": -14.31, "curve": [4.95, -14.31, 5.05, 6.43]}, {"time": 5.1, "value": 6.43, "curve": [5.15, 6.43, 5.25, 0]}, {"time": 5.3}]}, "Antler_4": {"rotate": [{"time": 0.4667, "curve": [0.483, 0, 0.517, 25.06]}, {"time": 0.5333, "value": 25.06, "curve": [0.558, 25.06, 0.608, -16.43]}, {"time": 0.6333, "value": -16.43, "curve": [0.683, -16.43, 0.783, 6.76]}, {"time": 0.8333, "value": 6.76, "curve": [0.883, 6.76, 0.983, -5.33]}, {"time": 1.0333, "value": -5.33, "curve": [1.083, -5.33, 1.183, 0]}, {"time": 1.2333, "curve": "stepped"}, {"time": 2.1333, "curve": [2.15, 0, 2.183, 25.06]}, {"time": 2.2, "value": 25.06, "curve": [2.225, 25.06, 2.275, -16.43]}, {"time": 2.3, "value": -16.43, "curve": [2.35, -16.43, 2.45, 6.76]}, {"time": 2.5, "value": 6.76, "curve": [2.525, 6.76, 2.575, 0]}, {"time": 2.6, "curve": [2.617, 0, 2.65, -16.43]}, {"time": 2.6667, "value": -16.43, "curve": [2.717, -16.43, 2.817, 6.76]}, {"time": 2.8667, "value": 6.76, "curve": [2.917, 6.76, 3.017, -5.33]}, {"time": 3.0667, "value": -5.33, "curve": [3.117, -5.33, 3.217, 0]}, {"time": 3.2667, "curve": "stepped"}, {"time": 4.1667, "curve": [4.183, 0, 4.217, 25.06]}, {"time": 4.2333, "value": 25.06, "curve": [4.258, 25.06, 4.308, -16.43]}, {"time": 4.3333, "value": -16.43, "curve": [4.383, -16.43, 4.483, 6.76]}, {"time": 4.5333, "value": 6.76, "curve": [4.558, 6.76, 4.608, 0]}, {"time": 4.6333, "curve": [4.65, 0, 4.683, -16.43]}, {"time": 4.7, "value": -16.43, "curve": [4.75, -16.43, 4.85, 6.76]}, {"time": 4.9, "value": 6.76, "curve": [4.95, 6.76, 5.05, -5.33]}, {"time": 5.1, "value": -5.33, "curve": [5.15, -5.33, 5.25, 0]}, {"time": 5.3}]}, "Antler_3": {"rotate": [{"time": 0.4667, "curve": [0.483, 0, 0.517, 58.17]}, {"time": 0.5333, "value": 58.17, "curve": [0.558, 58.17, 0.608, -22.19]}, {"time": 0.6333, "value": -22.19, "curve": [0.683, -22.19, 0.783, 13.8]}, {"time": 0.8333, "value": 13.8, "curve": [0.883, 13.8, 0.983, -7.7]}, {"time": 1.0333, "value": -7.7, "curve": [1.083, -7.7, 1.183, 0]}, {"time": 1.2333, "curve": "stepped"}, {"time": 2.1333, "curve": [2.15, 0, 2.183, 58.17]}, {"time": 2.2, "value": 58.17, "curve": [2.225, 58.17, 2.275, -22.19]}, {"time": 2.3, "value": -22.19, "curve": [2.35, -22.19, 2.45, 13.8]}, {"time": 2.5, "value": 13.8, "curve": [2.525, 13.8, 2.575, 0]}, {"time": 2.6, "curve": [2.617, 0, 2.65, -22.19]}, {"time": 2.6667, "value": -22.19, "curve": [2.717, -22.19, 2.817, 13.8]}, {"time": 2.8667, "value": 13.8, "curve": [2.917, 13.8, 3.017, -7.7]}, {"time": 3.0667, "value": -7.7, "curve": [3.117, -7.7, 3.217, 0]}, {"time": 3.2667, "curve": "stepped"}, {"time": 4.1667, "curve": [4.183, 0, 4.217, 58.17]}, {"time": 4.2333, "value": 58.17, "curve": [4.258, 58.17, 4.308, -22.19]}, {"time": 4.3333, "value": -22.19, "curve": [4.383, -22.19, 4.483, 13.8]}, {"time": 4.5333, "value": 13.8, "curve": [4.558, 13.8, 4.608, 0]}, {"time": 4.6333, "curve": [4.65, 0, 4.683, -22.19]}, {"time": 4.7, "value": -22.19, "curve": [4.75, -22.19, 4.85, 13.8]}, {"time": 4.9, "value": 13.8, "curve": [4.95, 13.8, 5.05, -7.7]}, {"time": 5.1, "value": -7.7, "curve": [5.15, -7.7, 5.25, 0]}, {"time": 5.3}]}, "Antler_0": {"rotate": [{"time": 0.4667, "curve": [0.483, 0, 0.517, -53.81]}, {"time": 0.5333, "value": -53.81, "curve": [0.558, -53.81, 0.608, 31.58]}, {"time": 0.6333, "value": 31.58, "curve": [0.683, 31.58, 0.783, -10.48]}, {"time": 0.8333, "value": -10.48, "curve": [0.883, -10.48, 0.983, 10.12]}, {"time": 1.0333, "value": 10.12, "curve": [1.083, 10.12, 1.183, 0]}, {"time": 1.2333, "curve": "stepped"}, {"time": 2.1333, "curve": [2.15, 0, 2.183, -53.81]}, {"time": 2.2, "value": -53.81, "curve": [2.225, -53.81, 2.275, 31.58]}, {"time": 2.3, "value": 31.58, "curve": [2.35, 31.58, 2.45, -10.48]}, {"time": 2.5, "value": -10.48, "curve": [2.525, -10.48, 2.575, 0]}, {"time": 2.6, "curve": [2.617, 0, 2.65, 31.58]}, {"time": 2.6667, "value": 31.58, "curve": [2.717, 31.58, 2.817, -10.48]}, {"time": 2.8667, "value": -10.48, "curve": [2.917, -10.48, 3.017, 10.12]}, {"time": 3.0667, "value": 10.12, "curve": [3.117, 10.12, 3.217, 0]}, {"time": 3.2667, "curve": "stepped"}, {"time": 4.1667, "curve": [4.183, 0, 4.217, -53.81]}, {"time": 4.2333, "value": -53.81, "curve": [4.258, -53.81, 4.308, 31.58]}, {"time": 4.3333, "value": 31.58, "curve": [4.383, 31.58, 4.483, -10.48]}, {"time": 4.5333, "value": -10.48, "curve": [4.558, -10.48, 4.608, 0]}, {"time": 4.6333, "curve": [4.65, 0, 4.683, 31.58]}, {"time": 4.7, "value": 31.58, "curve": [4.75, 31.58, 4.85, -10.48]}, {"time": 4.9, "value": -10.48, "curve": [4.95, -10.48, 5.05, 10.12]}, {"time": 5.1, "value": 10.12, "curve": [5.15, 10.12, 5.25, 0]}, {"time": 5.3}]}, "MOUTH_BTM": {"translate": [{"x": 1.07, "y": 34.11, "curve": [0.033, 1.07, 0.1, -0.6, 0.033, 34.11, 0.1, -56.63]}, {"time": 0.1333, "x": -0.6, "y": -56.63, "curve": "stepped"}, {"time": 0.4667, "x": -0.6, "y": -56.63, "curve": [0.492, -0.6, 0.542, -1.71, 0.492, -56.63, 0.542, 51.9]}, {"time": 0.5667, "x": -1.71, "y": 51.9, "curve": "stepped"}, {"time": 1.4, "x": -1.71, "y": 51.9, "curve": [1.433, -1.71, 1.5, 1.84, 1.433, 51.9, 1.5, -35.77]}, {"time": 1.5333, "x": 1.84, "y": -35.77, "curve": [1.55, 1.84, 1.583, 2.79, 1.55, -35.77, 1.583, -16.84]}, {"time": 1.6, "x": 2.79, "y": -16.84, "curve": [1.617, 2.79, 1.65, 1.84, 1.617, -16.84, 1.65, -35.77]}, {"time": 1.6667, "x": 1.84, "y": -35.77, "curve": [1.683, 1.84, 1.717, 2.79, 1.683, -35.77, 1.717, -16.84]}, {"time": 1.7333, "x": 2.79, "y": -16.84, "curve": [1.75, 2.79, 1.783, 1.84, 1.75, -16.84, 1.783, -35.77]}, {"time": 1.8, "x": 1.84, "y": -35.77, "curve": [1.817, 1.84, 1.85, 2.79, 1.817, -35.77, 1.85, -16.84]}, {"time": 1.8667, "x": 2.79, "y": -16.84, "curve": [1.883, 2.79, 1.917, 1.84, 1.883, -16.84, 1.917, -35.77]}, {"time": 1.9333, "x": 1.84, "y": -35.77, "curve": [1.95, 1.84, 1.983, 2.79, 1.95, -35.77, 1.983, -16.84]}, {"time": 2, "x": 2.79, "y": -16.84, "curve": [2.042, 2.79, 2.125, -1.78, 2.042, -16.84, 2.125, 58.71]}, {"time": 2.1667, "x": -1.78, "y": 58.71, "curve": [2.2, -1.78, 2.267, -1.35, 2.2, 58.71, 2.267, -41.14]}, {"time": 2.3, "x": -1.35, "y": -41.14, "curve": [2.367, -1.35, 2.5, -1.78, 2.367, -41.14, 2.5, 58.71]}, {"time": 2.5667, "x": -1.78, "y": 58.71, "curve": [2.583, -1.78, 2.617, -1.35, 2.583, 58.71, 2.617, -41.14]}, {"time": 2.6333, "x": -1.35, "y": -41.14, "curve": [2.7, -1.35, 2.833, -0.96, 2.7, -41.14, 2.833, 26.36]}, {"time": 2.9, "x": -0.96, "y": 26.36}, {"time": 3.0333, "x": -3.61, "y": 55.66, "curve": [3.05, -3.61, 3.083, -2.66, 3.05, 55.66, 3.083, 74.59]}, {"time": 3.1, "x": -2.66, "y": 74.59, "curve": [3.117, -2.66, 3.15, -3.61, 3.117, 74.59, 3.15, 55.66]}, {"time": 3.1667, "x": -3.61, "y": 55.66, "curve": [3.183, -3.61, 3.217, -2.66, 3.183, 55.66, 3.217, 74.59]}, {"time": 3.2333, "x": -2.66, "y": 74.59, "curve": [3.25, -2.66, 3.283, -3.61, 3.25, 74.59, 3.283, 55.66]}, {"time": 3.3, "x": -3.61, "y": 55.66, "curve": [3.317, -3.61, 3.35, -2.66, 3.317, 55.66, 3.35, 74.59]}, {"time": 3.3667, "x": -2.66, "y": 74.59, "curve": [3.383, -2.66, 3.417, -3.61, 3.383, 74.59, 3.417, 55.66]}, {"time": 3.4333, "x": -3.61, "y": 55.66, "curve": [3.45, -3.61, 3.483, -4.66, 3.45, 55.66, 3.483, -12.71]}, {"time": 3.5, "x": -4.66, "y": -12.71, "curve": [3.517, -4.66, 3.55, -5.61, 3.517, -12.71, 3.55, -31.64]}, {"time": 3.5667, "x": -5.61, "y": -31.64, "curve": [3.583, -5.61, 3.617, -4.66, 3.583, -31.64, 3.617, -12.71]}, {"time": 3.6333, "x": -4.66, "y": -12.71, "curve": [3.65, -4.66, 3.683, -5.61, 3.65, -12.71, 3.683, -31.64]}, {"time": 3.7, "x": -5.61, "y": -31.64, "curve": [3.717, -5.61, 3.75, -4.66, 3.717, -31.64, 3.75, -12.71]}, {"time": 3.7667, "x": -4.66, "y": -12.71, "curve": [3.783, -4.66, 3.817, -5.61, 3.783, -12.71, 3.817, -31.64]}, {"time": 3.8333, "x": -5.61, "y": -31.64, "curve": [3.85, -5.61, 3.883, -4.66, 3.85, -31.64, 3.883, -12.71]}, {"time": 3.9, "x": -4.66, "y": -12.71, "curve": [3.917, -4.66, 3.95, -5.61, 3.917, -12.71, 3.95, -31.64]}, {"time": 3.9667, "x": -5.61, "y": -31.64, "curve": [4.008, -5.61, 4.092, -1.78, 4.008, -31.64, 4.092, 58.71]}, {"time": 4.1333, "x": -1.78, "y": 58.71, "curve": [4.167, -1.78, 4.233, -1.35, 4.167, 58.71, 4.233, -41.14]}, {"time": 4.2667, "x": -1.35, "y": -41.14, "curve": [4.333, -1.35, 4.467, -1.78, 4.333, -41.14, 4.467, 58.71]}, {"time": 4.5333, "x": -1.78, "y": 58.71, "curve": [4.55, -1.78, 4.583, -1.35, 4.55, 58.71, 4.583, -41.14]}, {"time": 4.6, "x": -1.35, "y": -41.14, "curve": [4.667, -1.35, 4.8, -0.96, 4.667, -41.14, 4.8, 26.36]}, {"time": 4.8667, "x": -0.96, "y": 26.36}, {"time": 5, "x": 1.84, "y": -35.77, "curve": [5.017, 1.84, 5.05, 2.79, 5.017, -35.77, 5.05, -16.84]}, {"time": 5.0667, "x": 2.79, "y": -16.84, "curve": [5.083, 2.79, 5.117, 1.84, 5.083, -16.84, 5.117, -35.77]}, {"time": 5.1333, "x": 1.84, "y": -35.77, "curve": [5.15, 1.84, 5.183, 2.79, 5.15, -35.77, 5.183, -16.84]}, {"time": 5.2, "x": 2.79, "y": -16.84, "curve": [5.217, 2.79, 5.25, 1.84, 5.217, -16.84, 5.25, -35.77]}, {"time": 5.2667, "x": 1.84, "y": -35.77, "curve": [5.283, 1.84, 5.317, 2.79, 5.283, -35.77, 5.317, -16.84]}, {"time": 5.3333, "x": 2.79, "y": -16.84, "curve": [5.35, 2.79, 5.383, 1.84, 5.35, -16.84, 5.383, -35.77]}, {"time": 5.4, "x": 1.84, "y": -35.77, "curve": [5.417, 1.84, 5.45, 2.79, 5.417, -35.77, 5.45, -16.84]}, {"time": 5.4667, "x": 2.79, "y": -16.84}]}, "HeadFleshball_1_Behind": {"scale": [{"time": 0.4333, "curve": [0.458, 1, 0.508, 1, 0.458, 1, 0.508, 0.872]}, {"time": 0.5333, "y": 0.872, "curve": [0.55, 1, 0.583, 1, 0.55, 0.872, 0.583, 1]}, {"time": 0.6, "curve": [0.625, 1, 0.675, 1, 0.625, 1, 0.675, 0.872]}, {"time": 0.7, "y": 0.872, "curve": [0.717, 1, 0.75, 1.005, 0.717, 0.872, 0.75, 1.005]}, {"time": 0.7667, "x": 1.005, "y": 1.005, "curve": [0.804, 1.002, 0.842, 1, 0.804, 0.939, 0.842, 0.872]}, {"time": 0.8667, "y": 0.872, "curve": [0.883, 1, 0.917, 1, 0.883, 0.872, 0.917, 1]}, {"time": 0.9333, "curve": [0.958, 1, 1.008, 1, 0.958, 1, 1.008, 0.872]}, {"time": 1.0333, "y": 0.872, "curve": [1.05, 1, 1.083, 1, 1.05, 0.872, 1.083, 1]}, {"time": 1.1, "curve": [1.125, 1, 1.175, 1, 1.125, 1, 1.175, 0.872]}, {"time": 1.2, "y": 0.872, "curve": [1.217, 1, 1.25, 1, 1.217, 0.872, 1.25, 1]}, {"time": 1.2667, "curve": [1.292, 1, 1.342, 1, 1.292, 1, 1.342, 0.872]}, {"time": 1.3667, "y": 0.872, "curve": [1.383, 1, 1.417, 1.005, 1.383, 0.872, 1.417, 1.005]}, {"time": 1.4333, "x": 1.005, "y": 1.005, "curve": [1.471, 1.002, 1.508, 1, 1.471, 0.939, 1.508, 0.872]}, {"time": 1.5333, "y": 0.872, "curve": [1.55, 1, 1.583, 1, 1.55, 0.872, 1.583, 1]}, {"time": 1.6, "curve": [1.625, 1, 1.675, 1, 1.625, 1, 1.675, 0.872]}, {"time": 1.7, "y": 0.872, "curve": [1.717, 1, 1.75, 1, 1.717, 0.872, 1.75, 1]}, {"time": 1.7667, "curve": [1.792, 1, 1.842, 1, 1.792, 1, 1.842, 0.872]}, {"time": 1.8667, "y": 0.872, "curve": [1.883, 1, 1.917, 1, 1.883, 0.872, 1.917, 1]}, {"time": 1.9333, "curve": [1.958, 1, 2.008, 1, 1.958, 1, 2.008, 0.872]}, {"time": 2.0333, "y": 0.872, "curve": [2.05, 1, 2.083, 1.005, 2.05, 0.872, 2.083, 1.005]}, {"time": 2.1, "x": 1.005, "y": 1.005, "curve": [2.175, 1.037, 2.25, 1.07, 2.175, 1.037, 2.25, 1.07]}, {"time": 2.3, "x": 1.07, "y": 1.07, "curve": [2.342, 1.07, 2.425, 1.005, 2.342, 1.07, 2.425, 1.005]}, {"time": 2.4667, "x": 1.005, "y": 1.005, "curve": [2.542, 1.037, 2.617, 1.07, 2.542, 1.037, 2.617, 1.07]}, {"time": 2.6667, "x": 1.07, "y": 1.07, "curve": [2.717, 1.07, 2.817, 1.005, 2.717, 1.07, 2.817, 1.005]}, {"time": 2.8667, "x": 1.005, "y": 1.005, "curve": [2.879, 1.002, 2.892, 1, 2.879, 1.002, 2.892, 1]}, {"time": 2.9, "curve": [2.925, 1, 2.975, 1, 2.925, 1, 2.975, 0.872]}, {"time": 3, "y": 0.872, "curve": [3.017, 1, 3.05, 0.94, 3.017, 0.872, 3.05, 0.94]}, {"time": 3.0667, "x": 0.94, "y": 0.94, "curve": [3.092, 0.94, 3.129, 0.97, 3.092, 0.94, 3.129, 0.906]}, {"time": 3.1667, "y": 0.872, "curve": [3.183, 1, 3.217, 1.005, 3.183, 0.872, 3.217, 1.005]}, {"time": 3.2333, "x": 1.005, "y": 1.005, "curve": [3.271, 1.002, 3.308, 1, 3.271, 0.939, 3.308, 0.872]}, {"time": 3.3333, "y": 0.872, "curve": [3.35, 1, 3.383, 1, 3.35, 0.872, 3.383, 1]}, {"time": 3.4, "curve": [3.425, 1, 3.475, 1, 3.425, 1, 3.475, 0.872]}, {"time": 3.5, "y": 0.872, "curve": [3.517, 1, 3.55, 1, 3.517, 0.872, 3.55, 1]}, {"time": 3.5667}, {"time": 4.1333, "x": 1.005, "y": 1.005, "curve": [4.208, 1.037, 4.283, 1.07, 4.208, 1.037, 4.283, 1.07]}, {"time": 4.3333, "x": 1.07, "y": 1.07, "curve": [4.433, 1.07, 4.633, 0.94, 4.433, 1.07, 4.633, 0.94]}, {"time": 4.7333, "x": 0.94, "y": 0.94, "curve": [4.775, 0.94, 4.838, 0.973, 4.775, 0.94, 4.838, 0.973]}, {"time": 4.9, "x": 1.005, "y": 1.005, "curve": [4.975, 1.037, 5.05, 1.07, 4.975, 1.037, 5.05, 1.07]}, {"time": 5.1, "x": 1.07, "y": 1.07, "curve": [5.2, 1.07, 5.4, 0.94, 5.2, 1.07, 5.4, 0.94]}, {"time": 5.5, "x": 0.94, "y": 0.94}]}, "HeadFleshball_2": {"scale": [{"time": 0.4333, "y": 0.919, "curve": [0.458, 1, 0.484, 1, 0.458, 0.955, 0.484, 1]}, {"time": 0.5, "curve": [0.517, 1, 0.55, 1, 0.517, 1, 0.55, 0.872]}, {"time": 0.5667, "y": 0.872, "curve": [0.575, 1, 0.587, 1, 0.575, 0.872, 0.587, 0.894]}, {"time": 0.6, "y": 0.919, "curve": [0.624, 1, 0.65, 1, 0.624, 0.955, 0.65, 1]}, {"time": 0.6667, "curve": [0.683, 1, 0.717, 1, 0.683, 1, 0.717, 0.872]}, {"time": 0.7333, "y": 0.872, "curve": [0.742, 1, 0.754, 1.024, 0.742, 0.872, 0.754, 0.954]}, {"time": 0.7667, "x": 1.053, "y": 1.053, "curve": [0.792, 1.02, 0.814, 1, 0.792, 1.02, 0.814, 1]}, {"time": 0.8333, "curve": [0.85, 1, 0.883, 1, 0.85, 1, 0.883, 0.872]}, {"time": 0.9, "y": 0.872, "curve": [0.909, 1, 0.921, 1, 0.909, 0.872, 0.921, 0.894]}, {"time": 0.9333, "y": 0.919, "curve": [0.958, 1, 0.984, 1, 0.958, 0.955, 0.984, 1]}, {"time": 1, "curve": [1.017, 1, 1.05, 1, 1.017, 1, 1.05, 0.872]}, {"time": 1.0667, "y": 0.872, "curve": [1.075, 1, 1.087, 1, 1.075, 0.872, 1.087, 0.894]}, {"time": 1.1, "y": 0.919, "curve": [1.124, 1, 1.15, 1, 1.124, 0.955, 1.15, 1]}, {"time": 1.1667, "curve": [1.183, 1, 1.217, 1, 1.183, 1, 1.217, 0.872]}, {"time": 1.2333, "y": 0.872, "curve": [1.242, 1, 1.254, 1, 1.242, 0.872, 1.254, 0.894]}, {"time": 1.2667, "y": 0.919, "curve": [1.291, 1, 1.317, 1, 1.291, 0.955, 1.317, 1]}, {"time": 1.3333, "curve": [1.35, 1, 1.383, 1, 1.35, 1, 1.383, 0.872]}, {"time": 1.4, "y": 0.872, "curve": [1.409, 1, 1.421, 1.024, 1.409, 0.872, 1.421, 0.954]}, {"time": 1.4333, "x": 1.053, "y": 1.053, "curve": [1.458, 1.02, 1.481, 1, 1.458, 1.02, 1.481, 1]}, {"time": 1.5, "curve": [1.517, 1, 1.55, 1, 1.517, 1, 1.55, 0.872]}, {"time": 1.5667, "y": 0.872, "curve": [1.575, 1, 1.587, 1, 1.575, 0.872, 1.587, 0.894]}, {"time": 1.6, "y": 0.919, "curve": [1.624, 1, 1.65, 1, 1.624, 0.955, 1.65, 1]}, {"time": 1.6667, "curve": [1.683, 1, 1.717, 1, 1.683, 1, 1.717, 0.872]}, {"time": 1.7333, "y": 0.872, "curve": [1.742, 1, 1.754, 1, 1.742, 0.872, 1.754, 0.894]}, {"time": 1.7667, "y": 0.919, "curve": [1.791, 1, 1.817, 1, 1.791, 0.955, 1.817, 1]}, {"time": 1.8333, "curve": [1.85, 1, 1.883, 1, 1.85, 1, 1.883, 0.872]}, {"time": 1.9, "y": 0.872, "curve": [1.909, 1, 1.921, 1, 1.909, 0.872, 1.921, 0.894]}, {"time": 1.9333, "y": 0.919, "curve": [1.958, 1, 1.984, 1, 1.958, 0.955, 1.984, 1]}, {"time": 2, "curve": [2.017, 1, 2.05, 1, 2.017, 1, 2.05, 0.872]}, {"time": 2.0667, "y": 0.872, "curve": [2.075, 1, 2.087, 1.024, 2.075, 0.872, 2.087, 0.954]}, {"time": 2.1, "x": 1.053, "y": 1.053, "curve": [2.125, 1.063, 2.148, 1.07, 2.125, 1.063, 2.148, 1.07]}, {"time": 2.1667, "x": 1.07, "y": 1.07, "curve": [2.242, 1.07, 2.392, 1.053, 2.242, 1.07, 2.392, 1.053]}, {"time": 2.4667, "x": 1.053, "y": 1.053, "curve": [2.492, 1.063, 2.514, 1.07, 2.492, 1.063, 2.514, 1.07]}, {"time": 2.5333, "x": 1.07, "y": 1.07, "curve": [2.617, 1.07, 2.783, 1.053, 2.617, 1.07, 2.783, 1.053]}, {"time": 2.8667, "x": 1.053, "y": 1.053, "curve": [2.879, 1.02, 2.891, 1, 2.879, 0.971, 2.891, 0.919]}, {"time": 2.9, "y": 0.919, "curve": [2.912, 0.973, 2.925, 0.94, 2.912, 0.929, 2.925, 0.94]}, {"time": 2.9333, "x": 0.94, "y": 0.94, "curve": [2.941, 0.94, 2.956, 0.983, 2.941, 0.94, 2.956, 0.983]}, {"time": 2.9667, "curve": [2.983, 1, 3.017, 1, 2.983, 1, 3.017, 0.872]}, {"time": 3.0333, "y": 0.872, "curve": [3.042, 1, 3.054, 1, 3.042, 0.872, 3.054, 0.894]}, {"time": 3.0667, "y": 0.919, "curve": [3.091, 1, 3.117, 1, 3.091, 0.955, 3.117, 1]}, {"time": 3.1333, "curve": [3.15, 1, 3.183, 1, 3.15, 1, 3.183, 0.872]}, {"time": 3.2, "y": 0.872, "curve": [3.209, 1, 3.221, 1.024, 3.209, 0.872, 3.221, 0.954]}, {"time": 3.2333, "x": 1.053, "y": 1.053, "curve": [3.258, 1.02, 3.281, 1, 3.258, 1.02, 3.281, 1]}, {"time": 3.3, "curve": [3.317, 1, 3.35, 1, 3.317, 1, 3.35, 0.872]}, {"time": 3.3667, "y": 0.872, "curve": [3.375, 1, 3.387, 1, 3.375, 0.872, 3.387, 0.894]}, {"time": 3.4, "y": 0.919, "curve": [3.424, 1, 3.45, 1, 3.424, 0.955, 3.45, 1]}, {"time": 3.4667, "curve": [3.483, 1, 3.517, 1, 3.483, 1, 3.517, 0.872]}, {"time": 3.5333, "y": 0.872, "curve": [3.542, 1, 3.554, 1, 3.542, 0.872, 3.554, 0.894]}, {"time": 3.5667, "y": 0.919, "curve": [3.772, 1.024, 3.994, 1.053, 3.772, 0.979, 3.994, 1.053]}, {"time": 4.1333, "x": 1.053, "y": 1.053, "curve": [4.158, 1.063, 4.181, 1.07, 4.158, 1.063, 4.181, 1.07]}, {"time": 4.2, "x": 1.07, "y": 1.07, "curve": [4.3, 1.07, 4.5, 0.94, 4.3, 1.07, 4.5, 0.94]}, {"time": 4.6, "x": 0.94, "y": 0.94, "curve": [4.673, 0.94, 4.804, 1.021, 4.673, 0.94, 4.804, 1.021]}, {"time": 4.9, "x": 1.053, "y": 1.053, "curve": [4.925, 1.063, 4.948, 1.07, 4.925, 1.063, 4.948, 1.07]}, {"time": 4.9667, "x": 1.07, "y": 1.07, "curve": [5.067, 1.07, 5.267, 0.94, 5.067, 1.07, 5.267, 0.94]}, {"time": 5.3667, "x": 0.94, "y": 0.94}]}, "HeadFleshball_3": {"scale": [{"time": 0.4333, "y": 0.889, "curve": [0.446, 1, 0.457, 1, 0.446, 0.879, 0.457, 0.872]}, {"time": 0.4667, "y": 0.872, "curve": [0.483, 1, 0.517, 1, 0.483, 0.872, 0.517, 1]}, {"time": 0.5333, "curve": [0.55, 1, 0.579, 1, 0.55, 1, 0.579, 0.921]}, {"time": 0.6, "y": 0.889, "curve": [0.613, 1, 0.624, 1, 0.613, 0.879, 0.624, 0.872]}, {"time": 0.6333, "y": 0.872, "curve": [0.65, 1, 0.683, 1, 0.65, 0.872, 0.683, 1]}, {"time": 0.7, "curve": [0.716, 1, 0.745, 1.004, 0.716, 1, 0.745, 1.004]}, {"time": 0.7667, "x": 1.005, "y": 1.005, "curve": [0.779, 1.002, 0.792, 1, 0.779, 0.939, 0.792, 0.872]}, {"time": 0.8, "y": 0.872, "curve": [0.817, 1, 0.85, 1, 0.817, 0.872, 0.85, 1]}, {"time": 0.8667, "curve": [0.883, 1, 0.912, 1, 0.883, 1, 0.912, 0.921]}, {"time": 0.9333, "y": 0.889, "curve": [0.946, 1, 0.957, 1, 0.946, 0.879, 0.957, 0.872]}, {"time": 0.9667, "y": 0.872, "curve": [0.983, 1, 1.017, 1, 0.983, 0.872, 1.017, 1]}, {"time": 1.0333, "curve": [1.05, 1, 1.079, 1, 1.05, 1, 1.079, 0.921]}, {"time": 1.1, "y": 0.889, "curve": [1.113, 1, 1.124, 1, 1.113, 0.879, 1.124, 0.872]}, {"time": 1.1333, "y": 0.872, "curve": [1.15, 1, 1.183, 1, 1.15, 0.872, 1.183, 1]}, {"time": 1.2, "curve": [1.216, 1, 1.245, 1, 1.216, 1, 1.245, 0.921]}, {"time": 1.2667, "y": 0.889, "curve": [1.279, 1, 1.291, 1, 1.279, 0.879, 1.291, 0.872]}, {"time": 1.3, "y": 0.872, "curve": [1.317, 1, 1.35, 1, 1.317, 0.872, 1.35, 1]}, {"time": 1.3667, "curve": [1.383, 1, 1.412, 1.004, 1.383, 1, 1.412, 1.004]}, {"time": 1.4333, "x": 1.005, "y": 1.005, "curve": [1.446, 1.002, 1.458, 1, 1.446, 0.939, 1.458, 0.872]}, {"time": 1.4667, "y": 0.872, "curve": [1.483, 1, 1.517, 1, 1.483, 0.872, 1.517, 1]}, {"time": 1.5333, "curve": [1.55, 1, 1.579, 1, 1.55, 1, 1.579, 0.921]}, {"time": 1.6, "y": 0.889, "curve": [1.613, 1, 1.624, 1, 1.613, 0.879, 1.624, 0.872]}, {"time": 1.6333, "y": 0.872, "curve": [1.65, 1, 1.683, 1, 1.65, 0.872, 1.683, 1]}, {"time": 1.7, "curve": [1.716, 1, 1.745, 1, 1.716, 1, 1.745, 0.921]}, {"time": 1.7667, "y": 0.889, "curve": [1.779, 1, 1.791, 1, 1.779, 0.879, 1.791, 0.872]}, {"time": 1.8, "y": 0.872, "curve": [1.817, 1, 1.85, 1, 1.817, 0.872, 1.85, 1]}, {"time": 1.8667, "curve": [1.883, 1, 1.912, 1, 1.883, 1, 1.912, 0.921]}, {"time": 1.9333, "y": 0.889, "curve": [1.946, 1, 1.957, 1, 1.946, 0.879, 1.957, 0.872]}, {"time": 1.9667, "y": 0.872, "curve": [1.983, 1, 2.017, 1, 1.983, 0.872, 2.017, 1]}, {"time": 2.0333, "curve": [2.05, 1, 2.079, 1.004, 2.05, 1, 2.079, 1.004]}, {"time": 2.1, "x": 1.005, "y": 1.005, "curve": [2.175, 0.973, 2.25, 0.94, 2.175, 0.973, 2.25, 0.94]}, {"time": 2.3, "x": 0.94, "y": 0.94, "curve": [2.342, 0.94, 2.425, 1.005, 2.342, 0.94, 2.425, 1.005]}, {"time": 2.4667, "x": 1.005, "y": 1.005, "curve": [2.542, 0.973, 2.617, 0.94, 2.542, 0.973, 2.617, 0.94]}, {"time": 2.6667, "x": 0.94, "y": 0.94, "curve": [2.717, 0.94, 2.817, 1.005, 2.717, 0.94, 2.817, 1.005]}, {"time": 2.8667, "x": 1.005, "y": 1.005, "curve": [2.879, 1.002, 2.892, 1, 2.879, 0.947, 2.892, 0.889]}, {"time": 2.9, "y": 0.889, "curve": [2.913, 1, 2.924, 1, 2.913, 0.879, 2.924, 0.872]}, {"time": 2.9333, "y": 0.872, "curve": [2.95, 1, 2.983, 1, 2.95, 0.872, 2.983, 1]}, {"time": 3, "curve": [3.016, 1, 3.045, 1.05, 3.016, 1, 3.045, 1.05]}, {"time": 3.0667, "x": 1.07, "y": 1.07, "curve": [3.075, 1.07, 3.088, 1.035, 3.075, 1.07, 3.088, 0.971]}, {"time": 3.1, "y": 0.872, "curve": [3.117, 1, 3.15, 1, 3.117, 0.872, 3.15, 1]}, {"time": 3.1667, "curve": [3.183, 1, 3.212, 1.004, 3.183, 1, 3.212, 1.004]}, {"time": 3.2333, "x": 1.005, "y": 1.005, "curve": [3.246, 1.002, 3.258, 1, 3.246, 0.939, 3.258, 0.872]}, {"time": 3.2667, "y": 0.872, "curve": [3.283, 1, 3.317, 1, 3.283, 0.872, 3.317, 1]}, {"time": 3.3333, "curve": [3.35, 1, 3.379, 1, 3.35, 1, 3.379, 0.921]}, {"time": 3.4, "y": 0.889, "curve": [3.413, 1, 3.424, 1, 3.413, 0.879, 3.424, 0.872]}, {"time": 3.4333, "y": 0.872, "curve": [3.45, 1, 3.483, 1, 3.45, 0.872, 3.483, 1]}, {"time": 3.5, "curve": [3.516, 1, 3.545, 1, 3.516, 1, 3.545, 0.921]}, {"time": 3.5667, "y": 0.889, "curve": [3.779, 1.003, 3.972, 1.005, 3.779, 0.96, 3.972, 1.005]}, {"time": 4.1333, "x": 1.005, "y": 1.005, "curve": [4.208, 0.973, 4.283, 0.94, 4.208, 0.973, 4.283, 0.94]}, {"time": 4.3333, "x": 0.94, "y": 0.94, "curve": [4.433, 0.94, 4.633, 1.07, 4.433, 0.94, 4.633, 1.07]}, {"time": 4.7333, "x": 1.07, "y": 1.07, "curve": [4.775, 1.07, 4.838, 1.037, 4.775, 1.07, 4.838, 1.037]}, {"time": 4.9, "x": 1.005, "y": 1.005, "curve": [4.975, 0.973, 5.05, 0.94, 4.975, 0.973, 5.05, 0.94]}, {"time": 5.1, "x": 0.94, "y": 0.94, "curve": [5.2, 0.94, 5.4, 1.07, 5.2, 0.94, 5.4, 1.07]}, {"time": 5.5, "x": 1.07, "y": 1.07}]}, "HeadFleshball_3_Behind": {"scale": [{"time": 0.4333, "y": 0.889, "curve": [0.446, 1, 0.457, 1, 0.446, 0.879, 0.457, 0.872]}, {"time": 0.4667, "y": 0.872, "curve": [0.483, 1, 0.517, 1, 0.483, 0.872, 0.517, 1]}, {"time": 0.5333, "curve": [0.55, 1, 0.579, 1, 0.55, 1, 0.579, 0.921]}, {"time": 0.6, "y": 0.889, "curve": [0.613, 1, 0.624, 1, 0.613, 0.879, 0.624, 0.872]}, {"time": 0.6333, "y": 0.872, "curve": [0.65, 1, 0.683, 1, 0.65, 0.872, 0.683, 1]}, {"time": 0.7, "curve": [0.716, 1, 0.745, 1.004, 0.716, 1, 0.745, 1.004]}, {"time": 0.7667, "x": 1.005, "y": 1.005, "curve": [0.779, 1.002, 0.792, 1, 0.779, 0.939, 0.792, 0.872]}, {"time": 0.8, "y": 0.872, "curve": [0.817, 1, 0.85, 1, 0.817, 0.872, 0.85, 1]}, {"time": 0.8667, "curve": [0.883, 1, 0.912, 1, 0.883, 1, 0.912, 0.921]}, {"time": 0.9333, "y": 0.889, "curve": [0.946, 1, 0.957, 1, 0.946, 0.879, 0.957, 0.872]}, {"time": 0.9667, "y": 0.872, "curve": [0.983, 1, 1.017, 1, 0.983, 0.872, 1.017, 1]}, {"time": 1.0333, "curve": [1.05, 1, 1.079, 1, 1.05, 1, 1.079, 0.921]}, {"time": 1.1, "y": 0.889, "curve": [1.113, 1, 1.124, 1, 1.113, 0.879, 1.124, 0.872]}, {"time": 1.1333, "y": 0.872, "curve": [1.15, 1, 1.183, 1, 1.15, 0.872, 1.183, 1]}, {"time": 1.2, "curve": [1.216, 1, 1.245, 1, 1.216, 1, 1.245, 0.921]}, {"time": 1.2667, "y": 0.889, "curve": [1.279, 1, 1.291, 1, 1.279, 0.879, 1.291, 0.872]}, {"time": 1.3, "y": 0.872, "curve": [1.317, 1, 1.35, 1, 1.317, 0.872, 1.35, 1]}, {"time": 1.3667, "curve": [1.383, 1, 1.412, 1.004, 1.383, 1, 1.412, 1.004]}, {"time": 1.4333, "x": 1.005, "y": 1.005, "curve": [1.446, 1.002, 1.458, 1, 1.446, 0.939, 1.458, 0.872]}, {"time": 1.4667, "y": 0.872, "curve": [1.483, 1, 1.517, 1, 1.483, 0.872, 1.517, 1]}, {"time": 1.5333, "curve": [1.55, 1, 1.579, 1, 1.55, 1, 1.579, 0.921]}, {"time": 1.6, "y": 0.889, "curve": [1.613, 1, 1.624, 1, 1.613, 0.879, 1.624, 0.872]}, {"time": 1.6333, "y": 0.872, "curve": [1.65, 1, 1.683, 1, 1.65, 0.872, 1.683, 1]}, {"time": 1.7, "curve": [1.716, 1, 1.745, 1, 1.716, 1, 1.745, 0.921]}, {"time": 1.7667, "y": 0.889, "curve": [1.779, 1, 1.791, 1, 1.779, 0.879, 1.791, 0.872]}, {"time": 1.8, "y": 0.872, "curve": [1.817, 1, 1.85, 1, 1.817, 0.872, 1.85, 1]}, {"time": 1.8667, "curve": [1.883, 1, 1.912, 1, 1.883, 1, 1.912, 0.921]}, {"time": 1.9333, "y": 0.889, "curve": [1.946, 1, 1.957, 1, 1.946, 0.879, 1.957, 0.872]}, {"time": 1.9667, "y": 0.872, "curve": [1.983, 1, 2.017, 1, 1.983, 0.872, 2.017, 1]}, {"time": 2.0333, "curve": [2.05, 1, 2.079, 1.004, 2.05, 1, 2.079, 1.004]}, {"time": 2.1, "x": 1.005, "y": 1.005, "curve": [2.175, 0.973, 2.25, 0.94, 2.175, 0.973, 2.25, 0.94]}, {"time": 2.3, "x": 0.94, "y": 0.94, "curve": [2.342, 0.94, 2.425, 1.005, 2.342, 0.94, 2.425, 1.005]}, {"time": 2.4667, "x": 1.005, "y": 1.005, "curve": [2.542, 0.973, 2.617, 0.94, 2.542, 0.973, 2.617, 0.94]}, {"time": 2.6667, "x": 0.94, "y": 0.94, "curve": [2.717, 0.94, 2.817, 1.005, 2.717, 0.94, 2.817, 1.005]}, {"time": 2.8667, "x": 1.005, "y": 1.005, "curve": [2.879, 1.002, 2.892, 1, 2.879, 0.947, 2.892, 0.889]}, {"time": 2.9, "y": 0.889, "curve": [2.913, 1, 2.924, 1, 2.913, 0.879, 2.924, 0.872]}, {"time": 2.9333, "y": 0.872, "curve": [2.95, 1, 2.983, 1, 2.95, 0.872, 2.983, 1]}, {"time": 3, "curve": [3.016, 1, 3.045, 1.05, 3.016, 1, 3.045, 1.05]}, {"time": 3.0667, "x": 1.07, "y": 1.07, "curve": [3.075, 1.07, 3.088, 1.035, 3.075, 1.07, 3.088, 0.971]}, {"time": 3.1, "y": 0.872, "curve": [3.117, 1, 3.15, 1, 3.117, 0.872, 3.15, 1]}, {"time": 3.1667, "curve": [3.183, 1, 3.212, 1.004, 3.183, 1, 3.212, 1.004]}, {"time": 3.2333, "x": 1.005, "y": 1.005, "curve": [3.246, 1.002, 3.258, 1, 3.246, 0.939, 3.258, 0.872]}, {"time": 3.2667, "y": 0.872, "curve": [3.283, 1, 3.317, 1, 3.283, 0.872, 3.317, 1]}, {"time": 3.3333, "curve": [3.35, 1, 3.379, 1, 3.35, 1, 3.379, 0.921]}, {"time": 3.4, "y": 0.889, "curve": [3.413, 1, 3.424, 1, 3.413, 0.879, 3.424, 0.872]}, {"time": 3.4333, "y": 0.872, "curve": [3.45, 1, 3.483, 1, 3.45, 0.872, 3.483, 1]}, {"time": 3.5, "curve": [3.516, 1, 3.545, 1, 3.516, 1, 3.545, 0.921]}, {"time": 3.5667, "y": 0.889, "curve": [3.779, 1.003, 3.972, 1.005, 3.779, 0.96, 3.972, 1.005]}, {"time": 4.1333, "x": 1.005, "y": 1.005, "curve": [4.208, 0.973, 4.283, 0.94, 4.208, 0.973, 4.283, 0.94]}, {"time": 4.3333, "x": 0.94, "y": 0.94, "curve": [4.433, 0.94, 4.633, 1.07, 4.433, 0.94, 4.633, 1.07]}, {"time": 4.7333, "x": 1.07, "y": 1.07, "curve": [4.775, 1.07, 4.838, 1.037, 4.775, 1.07, 4.838, 1.037]}, {"time": 4.9, "x": 1.005, "y": 1.005, "curve": [4.975, 0.973, 5.05, 0.94, 4.975, 0.973, 5.05, 0.94]}, {"time": 5.1, "x": 0.94, "y": 0.94, "curve": [5.2, 0.94, 5.4, 1.07, 5.2, 0.94, 5.4, 1.07]}, {"time": 5.5, "x": 1.07, "y": 1.07}]}, "FleshTurret": {"scale": [{"time": 2.1, "x": 0.593, "y": 1.251, "curve": [2.142, 0.593, 2.225, 1.359, 2.142, 1.251, 2.225, 0.809]}, {"time": 2.2667, "x": 1.359, "y": 0.809, "curve": [2.308, 1.359, 2.392, 0.622, 2.308, 0.809, 2.392, 1.405]}, {"time": 2.4333, "x": 0.622, "y": 1.405, "curve": [2.442, 0.622, 2.458, 0.593, 2.442, 1.405, 2.458, 1.251]}, {"time": 2.4667, "x": 0.593, "y": 1.251, "curve": [2.508, 0.593, 2.592, 1.359, 2.508, 1.251, 2.592, 0.809]}, {"time": 2.6333, "x": 1.359, "y": 0.809, "curve": [2.675, 1.359, 2.758, 0.622, 2.675, 0.809, 2.758, 1.405]}, {"time": 2.8, "x": 0.622, "y": 1.405, "curve": [2.817, 0.622, 2.85, 1, 2.817, 1.405, 2.85, 1]}, {"time": 2.8667}, {"time": 3.0333, "x": 1.155, "y": 0.845, "curve": [3.083, 1.155, 3.183, 1, 3.083, 0.845, 3.183, 1]}, {"time": 3.2333}, {"time": 4.1333, "x": 0.593, "y": 1.251, "curve": [4.175, 0.593, 4.258, 1.359, 4.175, 1.251, 4.258, 0.809]}, {"time": 4.3, "x": 1.359, "y": 0.809, "curve": [4.342, 1.359, 4.425, 0.622, 4.342, 0.809, 4.425, 1.405]}, {"time": 4.4667, "x": 0.622, "y": 1.405, "curve": [4.525, 0.622, 4.642, 1.155, 4.525, 1.405, 4.642, 0.845]}, {"time": 4.7, "x": 1.155, "y": 0.845, "curve": [4.75, 1.155, 4.85, 0.593, 4.75, 0.845, 4.85, 1.251]}, {"time": 4.9, "x": 0.593, "y": 1.251, "curve": [4.942, 0.593, 5.025, 1.359, 4.942, 1.251, 5.025, 0.809]}, {"time": 5.0667, "x": 1.359, "y": 0.809, "curve": [5.108, 1.359, 5.192, 0.622, 5.108, 0.809, 5.192, 1.405]}, {"time": 5.2333, "x": 0.622, "y": 1.405, "curve": [5.292, 0.622, 5.408, 1.155, 5.292, 1.405, 5.408, 0.845]}, {"time": 5.4667, "x": 1.155, "y": 0.845}]}}}}}