{"skeleton": {"hash": "D7mtkO6yXF4", "spine": "4.1.24", "x": -283.55, "y": -27.71, "width": 574.82, "height": 830.34, "images": "./images/", "audio": "C:/Users/<USER>/Desktop/咩咩启示录（Cult of the Lamb）/ForestCultLeader"}, "bones": [{"name": "root"}, {"name": "MAIN", "parent": "root", "rotation": 90.8}, {"name": "BodyBtm", "parent": "MAIN", "length": 144.01, "x": 0.84, "y": -3.09}, {"name": "BodyTop", "parent": "BodyBtm", "length": 176.01, "rotation": -0.14, "x": 146.01, "y": -0.03}, {"name": "Head", "parent": "BodyTop", "length": 226.14, "rotation": 1.5, "x": 199.99, "y": -2.27, "transform": "noScale"}, {"name": "Face", "parent": "Head", "x": 138.8, "y": -0.06}, {"name": "Neck", "parent": "BodyTop", "rotation": 1.38, "x": 199.19, "y": -2.2}, {"name": "Antlers", "parent": "Head", "x": 171.97, "y": 1.76}, {"name": "Antler1", "parent": "Antlers", "length": 113.64, "rotation": -32.37, "x": 69.58, "y": -77.16}, {"name": "Antler2", "parent": "Antlers", "length": 111.5, "rotation": -56.63, "x": 61.05, "y": -115.16}, {"name": "Antler3", "parent": "Antlers", "length": 124.81, "rotation": -73.59, "x": 28.31, "y": -127.16}, {"name": "Antler4", "parent": "Antlers", "length": 129.37, "rotation": -72.59, "x": -31.91, "y": -138.2}, {"name": "Antler5", "parent": "Antlers", "length": 110.56, "rotation": -76.34, "x": -92.15, "y": -115.72}, {"name": "Antler6", "parent": "Antlers", "length": 116.79, "rotation": 32.96, "x": 70.82, "y": 59.22}, {"name": "Antler7", "parent": "Antlers", "length": 101.91, "rotation": 52.04, "x": 63.72, "y": 95.37}, {"name": "Antler8", "parent": "Antlers", "length": 101.55, "rotation": 55.97, "x": 23.67, "y": 113.55}, {"name": "Antler9", "parent": "Antlers", "length": 88.29, "rotation": 59.67, "x": -31.87, "y": 133.46}, {"name": "Antler10", "parent": "Antlers", "length": 86.26, "rotation": 71.05, "x": -81.38, "y": 120.86}, {"name": "HairBit0", "parent": "Head", "length": 27.63, "rotation": -179.01, "x": 635.14, "y": -440.4}, {"name": "HairBit1", "parent": "Head", "length": 23.28, "rotation": 174.4, "x": 646.58, "y": 334.73}, {"name": "Crown", "parent": "Head", "length": 69.7, "rotation": -1.43, "x": 321.09, "y": -12.31}, {"name": "CrownEye", "parent": "Crown", "x": 75.58, "y": -1.41}, {"name": "Hands_1", "parent": "BodyTop", "length": 55.56, "rotation": -88.5, "x": 28.77, "y": -3.52}, {"name": "Hands_0", "parent": "BodyBtm", "length": 56.32, "rotation": 85.67, "x": 117.91, "y": -0.05}, {"name": "DangleHandle1", "parent": "Neck", "rotation": -92.03, "x": -118.24, "y": -16.12}, {"name": "DangleHandle2", "parent": "Neck", "rotation": -92.03, "x": -101.85, "y": -4.95}, {"name": "NeckLeaf1", "parent": "Neck", "length": 61.02, "rotation": 124.5, "x": 15.22, "y": 58.48}, {"name": "NeckLeaf4", "parent": "Neck", "length": 55.75, "rotation": -122.35, "x": 14.72, "y": -84.15}, {"name": "NeckLeaf3", "parent": "Neck", "length": 43.25, "rotation": -131.06, "x": 1.9, "y": -35.54}, {"name": "NeckLeaf2", "parent": "Neck", "length": 45.59, "rotation": 133.78, "x": 2.89, "y": 18.03}, {"name": "NeckLeaf5", "parent": "Neck", "length": 53.84, "rotation": 179.63, "x": 2.91, "y": -2.17}, {"name": "Shoulder_Left", "parent": "BodyTop", "x": 149.31, "y": 108.74}, {"name": "Shoulder_Right", "parent": "BodyTop", "x": 146.86, "y": -106.63}, {"name": "Cloak_Right1", "parent": "BodyTop", "length": 85.94, "rotation": -164.77, "x": 192.91, "y": -66.69}, {"name": "Cloak_Right2", "parent": "Cloak_Right1", "length": 89.93, "rotation": -2.61, "x": 86.95, "y": 1.11}, {"name": "Cloak_Right3", "parent": "Cloak_Right2", "length": 86.62, "rotation": -6.8, "x": 89.71, "y": -1.33}, {"name": "Cloak_Right4", "parent": "Cloak_Right3", "length": 95.71, "rotation": -5.4, "x": 85.95, "y": 0.11}, {"name": "CloakWave1", "parent": "BodyTop", "x": -159.73, "y": 175.21}, {"name": "CloakWave2", "parent": "BodyTop", "x": -173.81, "y": 114.84}, {"name": "CloakWave3", "parent": "BodyTop", "x": -179, "y": 37.9}, {"name": "CloakWave4", "parent": "BodyTop", "x": -178.6, "y": -40.54}, {"name": "CloakWave5", "parent": "BodyTop", "x": -174.31, "y": -119.55}, {"name": "CloakWave6", "parent": "BodyTop", "x": -156.42, "y": -179.76}, {"name": "Cloak_Left1", "parent": "BodyTop", "length": 80.71, "rotation": 162.97, "x": 195.36, "y": 49.91}, {"name": "Cloak_Left2", "parent": "Cloak_Left1", "length": 85.78, "rotation": 4.14, "x": 89.39, "y": -0.89}, {"name": "Cloak_Left3", "parent": "Cloak_Left2", "length": 87.82, "rotation": 5.34, "x": 86.94, "y": 0.83}, {"name": "Cloak_Left4", "parent": "Cloak_Left3", "length": 93.21, "rotation": 4.94, "x": 91.54, "y": -1.87}, {"name": "CrownHolder", "parent": "root", "x": -1.99, "y": 832.9}, {"name": "Drip1", "parent": "Face", "rotation": -92.15, "x": -4.3, "y": 49.43}, {"name": "Drip2", "parent": "Face", "rotation": -92.15, "x": -12.3, "y": 13.7}, {"name": "Drip3", "parent": "Face", "rotation": -92.15, "x": -8.1, "y": -66.29}, {"name": "Drip4", "parent": "Face", "rotation": -92.15, "x": 6.66, "y": 105.02}, {"name": "Drip5", "parent": "Face", "rotation": -92.15, "x": -12.28, "y": -30.1}, {"name": "Drip6", "parent": "Face", "rotation": -92.15, "x": 0.48, "y": -118.17}], "slots": [{"name": "MASK", "bone": "root"}, {"name": "BehindCloak", "bone": "BodyBtm", "dark": "000000", "attachment": "BehindCloak"}, {"name": "Cloak", "bone": "BodyBtm", "dark": "000000", "attachment": "Cloak"}, {"name": "Cloak_Left", "bone": "MAIN", "dark": "000000"}, {"name": "Cloak_Right", "bone": "MAIN", "dark": "000000"}, {"name": "Vine", "bone": "Neck", "dark": "000000"}, {"name": "Vine_2", "bone": "Neck", "dark": "000000"}, {"name": "Vine_1", "bone": "Neck", "dark": "000000"}, {"name": "Vine_0", "bone": "Neck", "dark": "000000"}, {"name": "Antler_0", "bone": "Antler10", "dark": "000000", "attachment": "Antler_0"}, {"name": "Antler_2", "bone": "Antler9", "dark": "000000", "attachment": "Antler_2"}, {"name": "Antler_3", "bone": "Antler8", "dark": "000000", "attachment": "Antler_3"}, {"name": "Symbol_0", "bone": "Antler7", "dark": "000000", "attachment": "Symbol_0"}, {"name": "Symbol_1", "bone": "Antler6", "dark": "000000", "attachment": "Symbol_1"}, {"name": "Antler_4", "bone": "Antler3", "dark": "000000", "attachment": "Antler_4"}, {"name": "Antler_5", "bone": "Antler4", "dark": "000000", "attachment": "Antler_5"}, {"name": "Antler_6", "bone": "Antler5", "dark": "000000", "attachment": "Antler_6"}, {"name": "Symbol_2", "bone": "Antler1", "dark": "000000", "attachment": "Symbol_2"}, {"name": "Symbol_3", "bone": "Antler2", "dark": "000000", "attachment": "Symbol_3"}, {"name": "Neck", "bone": "Neck", "dark": "000000"}, {"name": "<PERSON><PERSON><PERSON>", "bone": "Neck", "dark": "000000", "attachment": "<PERSON><PERSON><PERSON>"}, {"name": "Hands_1", "bone": "Hands_1", "dark": "000000"}, {"name": "Hands_0", "bone": "Hands_0", "dark": "000000"}, {"name": "AntlersRight", "bone": "Antlers", "dark": "000000"}, {"name": "AntlersLeft", "bone": "Antlers", "dark": "000000"}, {"name": "HEAD", "bone": "Head", "dark": "000000", "attachment": "HEAD"}, {"name": "CROWN", "bone": "Crown", "dark": "000000", "attachment": "CROWN"}, {"name": "CrownEye", "bone": "CrownEye", "dark": "000000", "attachment": "CrownEye"}, {"name": "CrownGrass", "bone": "Crown", "dark": "000000", "attachment": "CrownGrass"}, {"name": "HairBit_1", "bone": "HairBit1", "dark": "000000", "attachment": "BandageHair1"}, {"name": "HairBit_0", "bone": "HairBit0", "dark": "000000", "attachment": "BandageHair2"}, {"name": "Drip1", "bone": "Drip1", "dark": "000000"}, {"name": "Drip5", "bone": "Drip5", "dark": "000000"}, {"name": "Drip2", "bone": "Drip2", "dark": "000000"}, {"name": "Drip6", "bone": "Drip6", "dark": "000000"}, {"name": "Drip4", "bone": "Drip4", "dark": "000000"}, {"name": "Drip3", "bone": "Drip3", "dark": "000000"}], "transform": [{"name": "Antlers", "order": 2, "bones": ["Antlers"], "target": "Face", "mixRotate": 0, "mixX": -0.5, "mixScaleX": 0, "mixShearY": 0}, {"name": "Crown", "order": 3, "bones": ["Crown"], "target": "Face", "mixRotate": 0, "mixX": 0.253, "mixScaleX": 0, "mixShearY": 0}, {"name": "CrownHolder", "order": 1, "bones": ["Crown"], "target": "CrownHolder", "mixRotate": 0, "mixX": 0, "mixScaleX": 0, "mixShearY": 0}, {"name": "Hairbits", "order": 4, "bones": ["HairBit1", "HairBit0"], "target": "Face", "mixRotate": 0, "mixX": 0.75, "mixScaleX": 0, "mixShearY": 0}, {"name": "Neck", "bones": ["Neck"], "target": "Head", "mixRotate": 0.7, "mixX": 0.7, "mixScaleX": 0, "mixShearY": 0}], "skins": [{"name": "default", "attachments": {"AntlersLeft": {"AntlersLeft": {"type": "mesh", "uvs": [1, 0.14022, 1, 0.4602, 1, 1, 0.65581, 1, 0.40507, 1, 0.23428, 0.82378, 0.19436, 0.70696, 0.22811, 0.67961, 0.26012, 0.69351, 0.29021, 0.71463, 0.37093, 0.71332, 0.45252, 0.74322, 0.47153, 0.74554, 0.54839, 0.74415, 0.79054, 0.78415, 0.75188, 0.75104, 0.54164, 0.73532, 0.45383, 0.68065, 0.37027, 0.70794, 0.28085, 0.70237, 0.20501, 0.6522, 0.06796, 0.65294, 0, 0.58555, 0, 0.52639, 0.09927, 0.48592, 0.25784, 0.58813, 0.49639, 0.595, 0.63599, 0.66517, 0.76475, 0.67814, 0.85421, 0.59271, 0.64141, 0.61254, 0.49232, 0.53169, 0.27546, 0.55762, 0.10604, 0.46914, 0.10198, 0.2998, 0.15249, 0.29246, 0.23788, 0.43357, 0.61206, 0.45866, 0.70486, 0.51831, 0.83174, 0.52462, 0.89241, 0.52893, 0.72977, 0.51281, 0.59926, 0.44395, 0.44078, 0.41051, 0.27647, 0.4092, 0.17611, 0.29877, 0.29963, 0.17875, 0.55133, 0.28237, 0.58415, 0.30998, 0.5868, 0.36143, 0.72299, 0.40776, 0.81928, 0.4513, 0.77384, 0.4113, 0.59788, 0.31686, 0.58389, 0.29391, 0.6702, 0, 1, 0, 0.86701, 0.29064, 0.80384, 0.20449, 0.729, 0.86935, 0.8293, 0.8602], "triangles": [47, 45, 46, 43, 44, 45, 45, 47, 43, 48, 43, 47, 48, 49, 43, 42, 49, 50, 43, 49, 42, 41, 42, 50, 41, 50, 51, 36, 33, 34, 36, 34, 35, 31, 36, 37, 31, 37, 38, 32, 36, 31, 36, 32, 33, 38, 30, 31, 29, 30, 38, 15, 16, 27, 17, 18, 25, 16, 17, 27, 25, 21, 22, 20, 21, 25, 17, 25, 26, 17, 26, 27, 25, 18, 19, 22, 23, 24, 25, 22, 24, 20, 25, 19, 9, 5, 6, 6, 7, 8, 10, 5, 9, 10, 11, 5, 12, 5, 11, 12, 13, 59, 9, 6, 8, 12, 4, 5, 4, 12, 59, 3, 4, 59, 59, 13, 14, 3, 59, 60, 55, 56, 0, 58, 55, 0, 57, 58, 0, 58, 54, 55, 57, 54, 58, 57, 53, 54, 57, 52, 53, 1, 51, 52, 57, 0, 1, 1, 52, 57, 51, 40, 41, 1, 40, 51, 29, 39, 40, 29, 38, 39, 15, 27, 28, 14, 28, 29, 15, 28, 14, 60, 14, 29, 59, 14, 60, 29, 40, 1, 2, 29, 1, 60, 29, 2, 2, 3, 60], "vertices": [2, 13, 112.3, -101.49, 0.68, 4, 398.44, 31.02, 0.32, 2, 13, -0.42, -22.59, 0.68, 4, 260.93, 35.89, 0.32, 1, 13, -190.58, 110.52, 1, 2, 4, 31.91, 127.34, 0.32, 17, -11.02, 64.82, 0.68, 2, 4, 34.06, 187.98, 0.32, 17, 47.04, 82.49, 0.68, 2, 4, 111.25, 226.61, 0.32, 17, 108.64, 22.03, 0.68, 2, 4, 161.79, 234.48, 0.32, 17, 132.51, -23.21, 0.68, 2, 4, 173.25, 225.91, 0.32, 17, 128.12, -36.84, 0.68, 2, 4, 167.01, 218.38, 0.32, 17, 118.97, -33.38, 0.68, 2, 4, 157.67, 211.42, 0.32, 17, 109.36, -26.81, 0.68, 2, 4, 157.54, 191.88, 0.32, 17, 90.83, -33.04, 0.68, 2, 4, 144, 172.6, 0.32, 17, 68.2, -26.49, 0.68, 2, 4, 142.84, 168.04, 0.32, 17, 63.51, -26.87, 0.68, 2, 4, 142.78, 149.43, 0.32, 17, 45.89, -32.86, 0.68, 2, 13, -85.47, 98.82, 0.68, 4, 123.51, 91.47, 0.32, 1, 13, -68.44, 98.32, 1, 2, 4, 146.63, 150.93, 0.224, 16, 18.82, 10.64, 0.776, 2, 4, 170.87, 171.33, 0.224, 16, 48.68, 0.02, 0.776, 2, 4, 159.86, 191.95, 0.224, 16, 60.92, 19.93, 0.776, 2, 4, 163.02, 213.5, 0.224, 16, 81.1, 28.08, 0.776, 2, 4, 185.23, 231.08, 0.224, 16, 107.49, 17.79, 0.776, 2, 4, 186.08, 264.23, 0.224, 16, 136.54, 33.8, 0.776, 2, 4, 215.63, 279.64, 0.224, 16, 164.76, 16.08, 0.776, 2, 4, 241.05, 278.74, 0.224, 16, 176.82, -6.32, 0.776, 2, 4, 257.59, 254.12, 0.224, 16, 163.92, -33.03, 0.776, 2, 4, 212.31, 217.32, 0.224, 16, 109.3, -12.53, 0.776, 2, 4, 207.32, 159.74, 0.224, 16, 57.07, -37.3, 0.776, 2, 4, 175.97, 127.04, 0.224, 16, 13.02, -26.74, 0.776, 1, 13, -44.55, 77.79, 1, 1, 13, -26.87, 38.99, 1, 1, 15, 11.02, 11.44, 1, 1, 15, 60.04, 1.08, 1, 1, 15, 98.63, 38.35, 1, 1, 15, 153.57, 27.82, 1, 1, 15, 192.99, -33.41, 1, 1, 15, 184.3, -42.57, 1, 1, 15, 134.62, -2.06, 1, 1, 15, 52.11, -40.9, 1, 1, 15, 19.47, -31.06, 1, 2, 13, 0.24, 26.65, 0.68, 4, 234.69, 77.56, 0.32, 2, 13, -9.7, 15.69, 0.68, 4, 232.32, 62.95, 0.32, 2, 4, 240.64, 102.04, 0.224, 14, 7.76, 7.65, 0.776, 2, 4, 271.34, 132.56, 0.224, 14, 50.71, 2.21, 0.776, 2, 4, 287.07, 170.38, 0.224, 14, 90.2, 13.06, 0.776, 2, 4, 289.04, 210.1, 0.224, 14, 122.73, 35.94, 0.776, 2, 4, 337.36, 232.69, 0.24, 14, 170.26, 11.74, 0.76, 2, 4, 387.87, 200.99, 0.224, 14, 176.33, -47.59, 0.776, 2, 4, 341.19, 141.69, 0.224, 14, 100.87, -47.25, 0.776, 2, 4, 329.04, 134.17, 0.24, 14, 87.47, -42.3, 0.76, 2, 4, 306.92, 134.32, 0.224, 14, 73.97, -24.76, 0.776, 2, 4, 285.84, 102.08, 0.224, 14, 35.59, -27.97, 0.776, 2, 13, 27.79, 11.05, 0.68, 4, 266.3, 79.46, 0.32, 2, 13, 48.19, 10.19, 0.68, 4, 283.88, 89.84, 0.32, 2, 13, 105.88, 21.79, 0.68, 4, 325.97, 130.96, 0.32, 2, 13, 115.91, 18.9, 0.68, 4, 335.95, 133.99, 0.32, 2, 13, 207.46, -70.68, 0.68, 4, 461.52, 108.65, 0.32, 2, 13, 161.69, -136.07, 0.68, 4, 458.69, 28.89, 0.32, 2, 13, 77.77, -38.04, 0.68, 4, 334.94, 65.47, 0.32, 2, 13, 116.88, -46.75, 0.68, 4, 372.5, 79.44, 0.32, 2, 4, 87.43, 107.65, 0.32, 17, -11.6, 5.92, 0.68, 2, 13, -117.64, 109.89, 0.68, 4, 90.5, 83.26, 0.32], "hull": 57, "edges": [0, 112, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112], "width": 218, "height": 387}}, "AntlersRight": {"AntlersRight": {"type": "mesh", "uvs": [0.42394, 0.2222, 0.40668, 0.3159, 0.25552, 0.38872, 0.19147, 0.44855, 0.28242, 0.40242, 0.42589, 0.3606, 0.41261, 0.32604, 0.42117, 0.30133, 0.68982, 0.17923, 0.80437, 0.29792, 0.80891, 0.31503, 0.71709, 0.40968, 0.548, 0.40968, 0.39709, 0.44447, 0.29982, 0.50433, 0.17528, 0.53298, 0.18073, 0.54884, 0.30619, 0.51405, 0.38567, 0.45769, 0.74972, 0.43493, 0.84136, 0.30707, 0.90245, 0.30814, 0.87381, 0.48542, 0.74781, 0.55741, 0.50536, 0.53592, 0.41563, 0.58105, 0.21136, 0.61758, 0.25527, 0.68641, 0.4099, 0.63806, 0.49581, 0.58756, 0.74208, 0.58541, 0.9139, 0.48334, 1, 0.49624, 1, 0.62731, 0.80699, 0.64451, 0.69245, 0.71193, 0.5569, 0.69689, 0.44999, 0.73664, 0.26672, 0.75383, 0.21551, 0.79784, 0.34486, 0.77668, 0.44435, 0.74624, 0.59273, 0.7528, 0.63934, 0.71345, 0.7178, 0.71608, 0.75088, 0.6839, 0.81704, 0.69443, 0.73875, 0.80707, 0.61406, 1, 0.343, 1, 0, 1, 0, 0.42303, 0.17834, 0.21156, 0, 0.16667, 0, 0, 0.32528, 0, 0.20611, 0.8744, 0.28608, 0.87058], "triangles": [26, 17, 25, 25, 17, 24, 23, 19, 22, 23, 24, 19, 17, 18, 24, 24, 18, 19, 22, 19, 21, 21, 19, 20, 27, 28, 37, 37, 28, 36, 28, 29, 36, 35, 36, 30, 36, 29, 30, 34, 35, 30, 34, 30, 33, 30, 31, 33, 31, 32, 33, 56, 57, 49, 57, 39, 40, 57, 48, 49, 48, 57, 42, 47, 42, 44, 44, 42, 43, 42, 47, 48, 41, 42, 57, 57, 40, 41, 46, 44, 45, 46, 47, 44, 26, 16, 17, 37, 38, 27, 56, 39, 57, 50, 56, 49, 39, 51, 26, 26, 51, 16, 16, 51, 15, 15, 51, 3, 51, 39, 50, 56, 50, 39, 39, 26, 27, 39, 27, 38, 3, 51, 2, 15, 3, 14, 3, 4, 14, 13, 14, 4, 13, 5, 12, 13, 4, 5, 11, 12, 7, 9, 10, 11, 6, 7, 5, 9, 11, 7, 9, 7, 8, 12, 5, 7, 51, 52, 2, 2, 52, 1, 0, 1, 52, 0, 52, 55, 52, 53, 55, 53, 54, 55], "vertices": [2, 8, 126.12, -16.56, 0.696, 4, 382.16, -171.29, 0.304, 2, 8, 89.23, -33.31, 0.696, 4, 342.04, -165.69, 0.304, 3, 8, 43.73, -17.55, 0.68324, 4, 312.05, -128.03, 0.304, 9, 20.04, 29.98, 0.01276, 2, 4, 286.88, -111.62, 0.304, 7, 71.95, -99, 0.696, 2, 4, 305.93, -134.32, 0.192, 9, 21.94, 21.41, 0.808, 2, 4, 322.67, -169.66, 0.192, 9, 60.65, 15.96, 0.808, 2, 4, 337.63, -166.97, 0.192, 9, 66.64, 29.93, 0.808, 2, 4, 348.18, -169.42, 0.192, 9, 74.48, 37.4, 0.808, 2, 4, 398.35, -236.25, 0.192, 9, 157.89, 42.54, 0.808, 2, 4, 346.36, -262.14, 0.192, 9, 150.93, -15.12, 0.808, 2, 4, 338.98, -262.98, 0.192, 9, 147.56, -21.75, 0.808, 2, 4, 299.09, -239.34, 0.192, 9, 105.88, -42.06, 0.808, 2, 4, 300.53, -198.44, 0.192, 9, 72.52, -18.36, 0.808, 2, 4, 286.88, -161.42, 0.192, 9, 34.08, -9.4, 0.808, 5, 4, 261.98, -136.98, 0.192, 9, -0.01, -16.75, 0.80481, 12, 41.28, 133.23, 9e-05, 11, 10.43, 79.49, 0.00031, 10, 2.6, 18.77, 0.00278, 1, 7, 35.81, -93.8, 1, 6, 8, -24.83, -36.72, 0.0892, 9, -34.59, -15.66, 0.03971, 7, 28.95, -94.88, 0.704, 12, 8.36, 122.59, 0.00948, 11, -23.12, 71.03, 0.02019, 10, -30.79, 9.72, 0.13743, 1, 10, 2.74, 14.32, 1, 1, 10, 28.65, 31.22, 1, 1, 10, 115.33, 12.65, 1, 1, 10, 153.75, 57.79, 1, 1, 10, 167.63, 52.68, 1, 1, 10, 136.95, -17.45, 1, 1, 10, 98.24, -37.17, 1, 1, 10, 45.49, -9.85, 1, 1, 10, 18.76, -21.39, 1, 6, 8, -46.6, -58.05, 0.01614, 9, -45.67, -44.05, 0.00238, 7, -0.86, -101.24, 0.912, 12, 7.5, 92.13, 0.00827, 11, -25.97, 40.69, 0.01728, 10, -33.11, -20.66, 0.04394, 1, 7, -30.81, -110.82, 1, 2, 4, 203.57, -161.57, 0.192, 11, 16.41, 16.4, 0.808, 2, 4, 224.54, -183.11, 0.192, 11, 43.24, 29.95, 0.808, 2, 4, 223.35, -242.71, 0.192, 11, 99.75, 10.99, 0.808, 2, 4, 265.75, -285.81, 0.192, 11, 153.57, 38.54, 0.808, 1, 11, 171.37, 26.38, 1, 2, 4, 203.14, -304.45, 0.192, 11, 152.61, -26.77, 0.808, 2, 4, 197.4, -257.51, 0.192, 11, 106.11, -18.2, 0.808, 2, 4, 169.41, -228.78, 0.192, 11, 70.32, -36.31, 0.808, 2, 4, 177.04, -196.23, 0.192, 11, 41.54, -19.3, 0.808, 2, 4, 160.87, -169.77, 0.192, 11, 11.45, -26.81, 0.808, 1, 7, -59.88, -112.56, 1, 6, 8, -112.98, -98.07, 0.00147, 4, 136.58, -112.13, 0.304, 7, -78.35, -99.51, 0.62362, 12, -12.5, 17.24, 0.05596, 11, -50.82, -32.73, 0.01236, 10, -56.66, -94.51, 0.00259, 2, 4, 144.56, -143.73, 0.304, 12, 20.1, 17.53, 0.696, 2, 4, 156.79, -168.25, 0.304, 12, 46.82, 23.62, 0.696, 2, 4, 152.7, -204.04, 0.304, 12, 80.63, 11.19, 0.696, 2, 4, 169.21, -215.91, 0.304, 12, 96.06, 24.43, 0.696, 2, 4, 167.41, -234.85, 0.304, 12, 114.04, 18.21, 0.696, 2, 4, 180.95, -243.34, 0.304, 12, 125.48, 29.37, 0.696, 2, 4, 175.87, -259.18, 0.304, 12, 139.68, 20.68, 0.696, 2, 4, 128.13, -238.53, 0.304, 12, 108.34, -20.83, 0.696, 2, 4, 46.29, -205.44, 0.304, 12, 56.85, -92.54, 0.696, 2, 4, 48.61, -139.88, 0.304, 12, -6.3, -74.8, 0.696, 2, 4, 51.55, -56.93, 0.304, 7, -163.38, -44.31, 0.696, 2, 4, 299.49, -65.71, 0.304, 7, 84.56, -53.09, 0.696, 2, 8, 100.04, 37.05, 0.696, 4, 388.83, -112.05, 0.304, 2, 8, 94.89, 84.05, 0.696, 4, 409.65, -69.61, 0.304, 2, 8, 156.75, 120.25, 0.696, 4, 481.28, -72.14, 0.304, 2, 8, 196.51, 52.32, 0.696, 4, 478.49, -150.81, 0.304, 6, 8, -142.54, -112.74, 0.00012, 4, 103.76, -108.69, 0.304, 7, -111.17, -96.07, 0.64589, 12, -23.59, -13.84, 0.04888, 11, -63.92, -63.02, 0.00088, 10, -69.23, -125.02, 0.00023, 2, 4, 104.71, -128.09, 0.304, 12, -4.51, -17.5, 0.696], "hull": 56, "edges": [0, 110, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110], "width": 218, "height": 387}}, "Antler_0": {"Antler_0": {"x": 52.46, "y": 2.06, "rotation": -163.2, "width": 140, "height": 54}, "Antler_1": {"path": "Antlers_4", "x": 56.71, "y": 7.85, "rotation": -155.4, "width": 143, "height": 52}}, "Antler_2": {"Antler_2": {"x": 85.91, "y": -13.85, "rotation": -151.83, "width": 147, "height": 104}, "Antler_3": {"path": "Antlers_3", "x": 104.53, "y": -36.46, "rotation": -145.95, "width": 212, "height": 212}}, "Antler_3": {"Antler_3": {"x": 65.4, "y": -7.98, "rotation": -148.12, "width": 116, "height": 89}, "Antler_4": {"path": "Antlers_2", "x": 111.31, "y": 1.12, "rotation": -153.75, "width": 198, "height": 127}}, "Antler_4": {"Antler_4": {"x": 72.78, "y": 4.24, "rotation": -18.56, "width": 109, "height": 91}, "Antler_5": {"path": "Antlers_2", "x": 113.91, "y": 10.48, "scaleX": -1, "rotation": -15.38, "width": 198, "height": 127}}, "Antler_5": {"Antler_5": {"x": 97.13, "y": 8.54, "rotation": -19.57, "width": 144, "height": 111}, "Antler_6": {"path": "Antlers_3", "x": 120.7, "y": 33.06, "scaleX": -1, "rotation": -29.31, "width": 212, "height": 212}}, "Antler_6": {"Antler_6": {"x": 79.7, "y": -8.66, "rotation": -15.82, "width": 141, "height": 53}, "Antler_7": {"path": "Antlers_4", "x": 64.74, "y": -13.77, "scaleX": -1, "rotation": -15.82, "width": 143, "height": 52}}, "BehindCloak": {"BehindCloak": {"x": 164.71, "y": -5.5, "scaleX": 1.0758, "rotation": -90.8, "width": 87, "height": 327}, "BehindCloak_Floating": {"x": 164.71, "y": -5.5, "rotation": -90.8, "width": 169, "height": 424}}, "Cloak": {"Cloak": {"type": "mesh", "uvs": [0.64547, 0.01952, 0.76758, 0.04918, 0.83227, 0.13264, 0.87707, 0.22431, 0.90466, 0.33371, 0.93103, 0.47356, 0.94936, 0.57349, 0.96661, 0.67233, 0.98062, 0.76896, 0.99079, 0.86969, 1, 1, 0.87883, 1, 0.76282, 1, 0.65622, 1, 0.54031, 1, 0.46171, 1, 0.34111, 1, 0.2298, 1, 0.1232, 1, 0, 1, 0.01038, 0.87748, 0.02031, 0.77521, 0.0348, 0.66963, 0.05486, 0.55724, 0.0727, 0.44031, 0.09833, 0.32337, 0.13623, 0.21212, 0.17635, 0.10994, 0.2644, 0.0282, 0.35351, 0, 0.46376, 0, 0.5651, 0, 0.46171, 0.89169, 0.45994, 0.79326, 0.46105, 0.68768, 0.46328, 0.58097, 0.46551, 0.47198, 0.47108, 0.36413, 0.47666, 0.25741, 0.47889, 0.15435, 0.4878, 0.06239, 0.5392, 0.89665, 0.54142, 0.79334, 0.5392, 0.68889, 0.53808, 0.57877, 0.53474, 0.46978, 0.52916, 0.36647, 0.52582, 0.24841, 0.51802, 0.1525, 0.51468, 0.06281, 0.34111, 0.79167, 0.33954, 0.58249, 0.34581, 0.45474, 0.35678, 0.32699, 0.22823, 0.78369, 0.23764, 0.57291, 0.24547, 0.45314, 0.27212, 0.32219, 0.12476, 0.7805, 0.14828, 0.56173, 0.16082, 0.44037, 0.18589, 0.32378, 0.20648, 0.22146, 0.29522, 0.23327, 0.65465, 0.78848, 0.66248, 0.57451, 0.6562, 0.45796, 0.64365, 0.32703, 0.63266, 0.24752, 0.3717, 0.24345, 0.75655, 0.79327, 0.74714, 0.57292, 0.74869, 0.44839, 0.733, 0.32706, 0.72439, 0.23396, 0.86472, 0.79647, 0.8459, 0.57771, 0.83647, 0.45159, 0.82862, 0.33346, 0.81367, 0.2269], "triangles": [18, 20, 58, 19, 20, 18, 18, 54, 17, 16, 17, 50, 15, 16, 32, 15, 32, 41, 13, 14, 41, 15, 41, 14, 11, 12, 75, 13, 70, 12, 9, 11, 75, 11, 9, 10, 34, 35, 43, 58, 22, 59, 21, 22, 58, 54, 58, 55, 54, 55, 51, 50, 54, 51, 64, 43, 65, 71, 70, 64, 34, 50, 51, 33, 50, 34, 33, 34, 43, 42, 33, 43, 64, 42, 43, 70, 76, 75, 75, 8, 9, 32, 33, 42, 41, 32, 42, 58, 20, 21, 18, 58, 54, 17, 54, 50, 32, 16, 50, 32, 50, 33, 13, 64, 70, 41, 42, 64, 64, 13, 41, 12, 70, 75, 49, 40, 30, 31, 49, 30, 0, 48, 49, 40, 29, 30, 31, 0, 49, 48, 39, 40, 48, 40, 49, 63, 62, 27, 26, 27, 62, 2, 74, 1, 79, 2, 3, 63, 28, 29, 63, 27, 28, 39, 29, 40, 69, 63, 29, 74, 0, 1, 79, 74, 2, 39, 69, 29, 68, 0, 74, 68, 48, 0, 47, 48, 68, 38, 69, 39, 39, 48, 47, 38, 39, 47, 57, 62, 63, 61, 26, 62, 61, 62, 57, 25, 26, 61, 53, 63, 69, 57, 63, 53, 67, 68, 74, 73, 74, 79, 67, 74, 73, 78, 79, 3, 73, 79, 78, 78, 3, 4, 38, 53, 69, 37, 53, 38, 67, 46, 47, 67, 47, 68, 38, 47, 46, 37, 38, 46, 60, 24, 25, 61, 60, 25, 72, 73, 78, 77, 78, 4, 72, 78, 77, 56, 61, 57, 60, 61, 56, 52, 57, 53, 52, 53, 37, 56, 57, 52, 66, 67, 73, 66, 73, 72, 46, 67, 66, 45, 46, 66, 36, 52, 37, 36, 37, 46, 36, 46, 45, 77, 4, 5, 59, 24, 60, 59, 60, 56, 23, 24, 59, 55, 59, 56, 71, 66, 72, 71, 72, 77, 65, 66, 71, 45, 66, 65, 76, 77, 5, 76, 5, 6, 71, 77, 76, 44, 45, 65, 36, 45, 44, 36, 51, 52, 35, 36, 44, 51, 56, 52, 35, 51, 36, 55, 56, 51, 34, 51, 35, 43, 44, 65, 35, 44, 43, 59, 22, 23, 58, 59, 55, 64, 65, 71, 76, 70, 71, 7, 75, 76, 7, 76, 6, 75, 7, 8], "vertices": [3, 3, 216.69, -60.04, 0.26127, 4, 15.31, -58.15, 0.64273, 32, 69.82, 46.59, 0.096, 3, 3, 204.97, -106.8, 0.30066, 4, 2.47, -104.62, 0.36334, 32, 58.11, -0.17, 0.336, 4, 3, 173.23, -131.28, 0.47103, 4, -29.85, -128.33, 0.19278, 32, 26.36, -24.65, 0.336, 2, 318.91, -131.74, 0.00019, 4, 3, 138.48, -148.09, 0.62387, 4, -64.99, -144.32, 0.13132, 32, -8.38, -41.46, 0.24, 2, 284.12, -148.46, 0.00482, 3, 3, 97.11, -158.22, 0.88008, 4, -106.59, -153.45, 0.08376, 2, 242.73, -158.49, 0.03617, 4, 3, 44.28, -167.74, 0.81728, 4, -159.64, -161.68, 0.03426, 32, -102.58, -61.11, 0.048, 2, 189.87, -167.88, 0.10046, 3, 3, 6.52, -174.35, 0.76845, 4, -197.54, -167.38, 0.01229, 2, 152.1, -174.39, 0.21926, 3, 3, -30.81, -180.55, 0.64088, 4, -235.01, -172.68, 0.00345, 2, 114.75, -180.5, 0.35567, 3, 3, -67.3, -185.52, 0.51174, 4, -271.61, -176.77, 0.00058, 2, 78.25, -185.37, 0.48767, 4, 3, -95.88, -189.1, 0.29875, 4, -300.27, -179.69, 3e-05, 2, 49.66, -188.88, 0.36789, 1, 50.5, -191.97, 0.33333, 1, 1, 1.33, -194.82, 1, 1, 1, -16.9, -148.04, 1, 1, 1, -24.86, -103.37, 1, 1, 1, -28.58, -62.38, 1, 1, 1, -27.96, -17.88, 1, 1, 1, -27.54, 12.3, 1, 1, 1, -26.9, 58.61, 1, 1, 1, -22.01, 101.29, 1, 1, 1, -12.87, 142.1, 1, 1, 1, 6.67, 189.14, 1, 3, 3, -94.54, 187.39, 0.1597, 2, 51.95, 187.6, 0.50697, 1, 52.79, 184.51, 0.33333, 2, 3, -65.47, 183.24, 0.31549, 2, 81.01, 183.38, 0.68451, 3, 3, -25.73, 177.23, 0.57726, 4, -221.34, 184.87, 0.00056, 2, 120.73, 177.27, 0.42219, 3, 3, 16.55, 169.04, 0.67148, 4, -179.26, 175.67, 0.00074, 2, 163, 168.97, 0.32778, 4, 3, 60.56, 161.7, 0.69231, 4, -135.45, 167.27, 0.0054, 2, 206.98, 161.51, 0.23829, 31, -88.75, 52.95, 0.064, 4, 3, 104.53, 151.35, 0.8065, 4, -91.74, 155.87, 0.03054, 2, 250.92, 151.06, 0.05095, 31, -44.78, 42.61, 0.112, 4, 3, 146.3, 136.32, 0.70633, 4, -50.34, 139.85, 0.07558, 2, 292.66, 135.93, 0.01009, 31, -3.01, 27.58, 0.208, 4, 3, 184.65, 120.48, 0.52319, 4, -12.39, 123.08, 0.14009, 2, 330.96, 119.98, 0.00072, 31, 35.34, 11.74, 0.336, 3, 3, 215.08, 86.32, 0.44998, 4, 17.21, 88.2, 0.27802, 31, 65.77, -22.42, 0.272, 3, 3, 225.32, 51.98, 0.31754, 4, 26.63, 53.63, 0.57046, 31, 76.01, -56.76, 0.112, 2, 3, 224.84, 9.65, 0.13174, 4, 25.13, 11.32, 0.86826, 2, 3, 224.39, -29.26, 0.18123, 4, 23.75, -27.57, 0.81877, 3, 3, -133.61, 14.51, 0.00091, 2, 12.44, 14.82, 0.66576, 1, 13.29, 11.73, 0.33333, 2, 3, -96.49, 14.77, 0.00264, 2, 49.56, 14.99, 0.99736, 2, 3, -43.83, 13.74, 0.33571, 2, 102.22, 13.83, 0.66429, 2, 3, 5.83, 12.32, 0.66839, 2, 151.87, 12.28, 0.33161, 1, 3, 46.9, 11, 1, 1, 3, 87.54, 8.4, 1, 1, 3, 127.74, 5.8, 1, 2, 3, 166.59, 4.5, 0.85375, 4, -33.23, 7.58, 0.14625, 2, 3, 201.21, 0.69, 0.34745, 4, 1.3, 2.93, 0.65255, 3, 3, -135.81, -15.22, 0.01743, 2, 10.16, -14.91, 0.64924, 1, 11.01, -17.99, 0.33333, 2, 3, -96.88, -16.52, 0.07143, 2, 49.09, -16.3, 0.92857, 2, 3, -44.63, -16.26, 0.40027, 2, 101.35, -16.17, 0.59973, 3, 3, 6.33, -16.41, 0.72054, 4, -193.94, -9.48, 0.00013, 2, 152.3, -16.45, 0.27933, 2, 3, 47.43, -15.59, 0.99911, 4, -152.83, -9.65, 0.00089, 2, 3, 86.4, -13.89, 0.99574, 4, -113.83, -8.89, 0.00426, 2, 3, 130.92, -13.12, 0.97428, 4, -69.3, -9.18, 0.02572, 2, 3, 167.11, -10.53, 0.83095, 4, -33.06, -7.47, 0.16905, 2, 3, 200.94, -9.63, 0.35081, 4, 0.77, -7.38, 0.64919, 3, 3, -95.38, 60.39, 0.08467, 2, 50.79, 60.6, 0.90011, 1, 51.63, 57.52, 0.01522, 4, 3, 5.79, 59.84, 0.64594, 4, -192.5, 67.19, 0.00022, 2, 151.96, 59.8, 0.35324, 1, 152.8, 56.71, 0.00061, 5, 3, 53.92, 56.89, 0.80689, 4, -144.46, 62.97, 0.00553, 2, 200.08, 56.72, 0.16738, 1, 200.92, 53.64, 0.00027, 31, -95.39, -51.85, 0.01993, 5, 3, 102.03, 52.13, 0.86463, 4, -96.49, 56.95, 0.01725, 2, 248.18, 51.84, 0.06867, 1, 249.02, 48.76, 0.00011, 31, -47.28, -56.62, 0.04934, 4, 3, -87.59, 103.65, 0.1715, 4, -284.69, 113.43, 1e-05, 2, 58.69, 103.84, 0.81864, 1, 59.54, 100.76, 0.00985, 5, 3, 9.85, 98.93, 0.65518, 4, -187.42, 106.16, 0.00042, 2, 156.11, 98.88, 0.34371, 1, 156.96, 95.79, 0.00039, 31, -139.46, -9.81, 0.00029, 5, 3, 54.96, 95.41, 0.76162, 4, -142.41, 101.45, 0.00533, 2, 201.22, 95.24, 0.19786, 1, 202.06, 92.15, 0.00018, 31, -94.35, -13.34, 0.03501, 5, 3, 104.21, 84.61, 0.83255, 4, -93.47, 89.37, 0.02652, 2, 250.43, 84.32, 0.05799, 1, 251.28, 81.24, 7e-05, 31, -45.1, -24.13, 0.08287, 4, 3, -77.35, 143.27, 0.24153, 4, -273.42, 152.77, 1e-05, 2, 69.03, 143.43, 0.75019, 1, 69.87, 140.35, 0.00828, 5, 3, 14.45, 133.19, 0.66382, 4, -181.92, 140.29, 0.00072, 2, 160.8, 133.13, 0.33322, 1, 161.65, 130.04, 0.0002, 31, -134.85, 24.45, 0.00205, 5, 3, 60.15, 127.86, 0.73333, 4, -136.38, 133.75, 0.00674, 2, 206.48, 127.68, 0.20777, 1, 207.33, 124.59, 9e-05, 31, -89.16, 19.12, 0.05207, 5, 3, 103.98, 117.73, 0.81773, 4, -92.82, 122.48, 0.02746, 2, 250.29, 117.44, 0.06076, 1, 251.14, 114.35, 4e-05, 31, -45.33, 8.98, 0.09401, 4, 3, 142.45, 109.38, 0.76693, 4, -54.58, 113.12, 0.05998, 2, 288.74, 108.99, 0.00801, 31, -6.85, 0.64, 0.16507, 4, 3, 137.62, 75.36, 0.84349, 4, -60.31, 79.24, 0.04028, 2, 283.83, 74.98, 0.00538, 31, -11.69, -33.38, 0.11085, 4, 3, -95.54, -60.01, 0.18303, 4, -296.94, -49.97, 0.00015, 2, 50.32, -59.8, 0.81031, 1, 51.17, -62.89, 0.00651, 4, 3, 7.39, -64.19, 0.73639, 4, -194.15, -56.85, 0.00441, 32, -139.47, 42.43, 0.00128, 2, 153.25, -64.24, 0.25793, 4, 3, 51.36, -62.29, 0.85876, 4, -150.15, -56.09, 0.0185, 32, -95.5, 44.34, 0.00057, 2, 197.22, -62.44, 0.12217, 4, 3, 100.78, -58.03, 0.86152, 4, -100.64, -53.14, 0.04405, 32, -46.08, 48.6, 0.04798, 2, 246.65, -58.31, 0.04645, 4, 3, 130.8, -54.15, 0.80395, 4, -70.53, -50.05, 0.08226, 32, -16.06, 52.47, 0.11372, 2, 276.68, -54.51, 7e-05, 4, 3, 133.46, 46.04, 0.90946, 4, -65.24, 50.04, 0.0233, 2, 279.59, 45.67, 0.00311, 31, -15.85, -62.7, 0.06413, 4, 3, -93.5, -99.17, 0.26598, 4, -295.93, -89.17, 0.00026, 2, 52.26, -98.96, 0.68096, 1, 53.11, -102.05, 0.0528, 4, 3, 7.63, -96.71, 0.74651, 4, -194.77, -89.36, 0.00702, 32, -139.23, 9.92, 0.00152, 2, 153.4, -96.75, 0.24495, 4, 3, 54.57, -97.84, 0.84444, 4, -147.87, -91.72, 0.03612, 32, -92.29, 8.78, 0.00057, 2, 200.34, -98.01, 0.11886, 4, 3, 100.39, -92.34, 0.78315, 4, -101.93, -87.43, 0.07093, 32, -46.47, 14.28, 0.10311, 2, 246.17, -92.62, 0.04281, 4, 3, 135.52, -89.44, 0.66873, 4, -66.73, -85.45, 0.12715, 32, -11.34, 17.18, 0.204, 2, 281.31, -89.81, 0.00012, 4, 3, -86.6, -140.79, 0.35176, 4, -290.12, -130.95, 0.00033, 2, 59.06, -140.6, 0.57137, 1, 59.9, -143.68, 0.07653, 4, 3, 5.39, -134.61, 0.75131, 4, -198, -127.19, 0.00917, 32, -141.47, -27.98, 0.00078, 2, 151.06, -134.65, 0.23874, 4, 3, 52.99, -131.54, 0.83599, 4, -150.34, -125.37, 0.03907, 32, -93.88, -24.91, 0.02016, 2, 198.67, -131.7, 0.10478, 4, 3, 97.57, -129.04, 0.79469, 4, -105.71, -124.03, 0.08664, 32, -49.3, -22.41, 0.08517, 2, 243.25, -129.31, 0.03351, 4, 3, 137.8, -123.76, 0.54786, 4, -65.36, -119.81, 0.16727, 32, -9.06, -17.13, 0.2847, 2, 283.5, -124.13, 0.00016], "hull": 32, "edges": [0, 62, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62], "width": 344, "height": 342}}, "Cloak_Left": {"Cloak_Left": {"type": "mesh", "uvs": [1, 0.11356, 0.98611, 0.20456, 0.97818, 0.30556, 0.96628, 0.40656, 0.95834, 0.52456, 0.95636, 0.62656, 0.95041, 0.71756, 0.95041, 0.81356, 0.95041, 0.90456, 0.95834, 0.99999, 0.72318, 1, 0.49505, 0.99999, 0.30857, 0.99999, 0.134, 0.99999, 0, 1, 0, 0.89356, 0.0213, 0.80783, 0.04795, 0.70056, 0.07374, 0.60056, 0.11406, 0.50556, 0.1585, 0.39472, 0.21613, 0.29337, 0.26931, 0.18835, 0.3225, 0.09451, 0.41115, 0, 0.54266, 0, 0.62201, 0, 0.72715, 0.001, 0.82237, 0, 1, 0, 0.12408, 0.80956, 0.11956, 0.89316, 0.14097, 0.69957, 0.1657, 0.60388, 0.19808, 0.5089, 0.24965, 0.40069, 0.31041, 0.29098, 0.38069, 0.19101, 0.4474, 0.09592, 0.49523, 0.89765, 0.49507, 0.80779, 0.50301, 0.70212, 0.51383, 0.61131, 0.536, 0.51608, 0.55781, 0.39933, 0.59392, 0.29954, 0.70747, 0.90205, 0.70344, 0.81273, 0.70506, 0.71299, 0.70008, 0.6195, 0.69959, 0.52142, 0.70727, 0.4021, 0.73329, 0.30141, 0.77469, 0.20026, 0.79863, 0.1079, 0.63636, 0.19469, 0.67706, 0.10225, 0.3052, 0.89401, 0.29868, 0.80945, 0.30853, 0.69935, 0.32675, 0.60732, 0.35994, 0.51251, 0.39987, 0.4005, 0.45933, 0.29525, 0.51322, 0.19313, 0.5644, 0.09895], "triangles": [32, 18, 33, 46, 7, 8, 10, 46, 8, 46, 47, 7, 11, 46, 10, 14, 31, 13, 14, 15, 31, 11, 39, 46, 11, 12, 39, 13, 57, 12, 12, 57, 39, 13, 31, 57, 39, 47, 46, 57, 40, 39, 39, 40, 47, 57, 31, 58, 31, 30, 58, 57, 58, 40, 15, 16, 31, 31, 16, 30, 16, 17, 30, 30, 32, 58, 30, 17, 32, 58, 59, 40, 58, 32, 59, 17, 18, 32, 10, 8, 9, 32, 33, 59, 45, 55, 52, 63, 37, 64, 52, 53, 2, 2, 53, 1, 52, 55, 53, 45, 64, 55, 53, 54, 1, 1, 54, 0, 55, 56, 53, 53, 56, 54, 64, 65, 55, 55, 65, 56, 37, 38, 64, 64, 38, 65, 22, 23, 37, 37, 23, 38, 54, 28, 0, 28, 29, 0, 56, 27, 54, 54, 27, 28, 27, 56, 26, 56, 65, 26, 38, 25, 65, 65, 25, 26, 23, 24, 38, 38, 24, 25, 43, 44, 50, 60, 34, 61, 18, 19, 33, 33, 19, 34, 3, 4, 51, 50, 44, 51, 61, 62, 43, 43, 62, 44, 34, 35, 61, 61, 35, 62, 35, 34, 20, 34, 19, 20, 51, 52, 3, 3, 52, 2, 44, 45, 51, 51, 45, 52, 20, 21, 35, 35, 36, 62, 35, 21, 36, 62, 63, 44, 62, 36, 63, 44, 63, 45, 63, 64, 45, 36, 37, 63, 21, 22, 36, 36, 22, 37, 33, 34, 60, 7, 47, 6, 47, 48, 6, 40, 41, 48, 40, 59, 41, 6, 48, 5, 41, 49, 48, 48, 49, 5, 59, 60, 41, 41, 42, 49, 41, 60, 42, 59, 33, 60, 5, 49, 4, 49, 42, 50, 42, 43, 50, 49, 50, 4, 60, 61, 42, 42, 61, 43, 4, 50, 51, 47, 40, 48], "vertices": [3, 45, -155.33, 72.7, 0.00468, 44, -74.48, 58.75, 0.03613, 43, 10.87, 52.33, 0.95919, 3, 45, -121.86, 74.15, 0.02886, 44, -41.3, 63.32, 0.16676, 43, 43.63, 59.28, 0.80437, 3, 45, -84.89, 77.15, 0.11349, 44, -4.76, 69.74, 0.39224, 43, 79.61, 68.32, 0.49427, 5, 45, -47.83, 79.41, 0.32175, 44, 31.93, 75.45, 0.44547, 43, 115.79, 76.66, 0.22262, 46, -131.85, 92.98, 0.00017, 39, 241.2, -27.39, 0.01, 5, 45, -4.66, 83.15, 0.60723, 44, 74.56, 83.19, 0.26881, 43, 157.76, 87.46, 0.06625, 46, -88.52, 92.99, 0.00771, 39, 197.91, -25.43, 0.05, 5, 45, 32.55, 87.28, 0.73566, 44, 111.22, 90.77, 0.09735, 43, 193.78, 97.66, 0.01283, 46, -51.09, 93.9, 0.04015, 39, 160.48, -24.64, 0.114, 5, 45, 78.82, 92.88, 0.65352, 44, 156.77, 100.65, 0.02044, 43, 238.49, 110.8, 0.00098, 46, -4.51, 95.49, 0.14466, 39, 113.88, -24.11, 0.1804, 5, 45, 118.67, 96.59, 0.41205, 44, 196.1, 108.05, 0.00236, 46, 35.51, 95.76, 0.31833, 39, 73.89, -22.55, 0.2557, 38, 68.69, -99.49, 0.01156, 4, 45, 150.37, 100.42, 0.22996, 46, 67.42, 96.85, 0.43821, 39, 41.96, -22.19, 0.31943, 38, 36.77, -99.13, 0.0124, 4, 45, 186.84, 106.31, 0.14031, 46, 104.26, 99.57, 0.46124, 39, 5.03, -23.24, 0.35844, 38, -0.16, -100.17, 0.04, 4, 45, 192.69, 63.2, 0.09507, 46, 106.38, 56.12, 0.5046, 39, 4.89, 20.27, 0.28243, 38, -0.3, -56.67, 0.1179, 5, 45, 194.16, 20.86, 0.03715, 46, 104.2, 13.81, 0.56425, 39, 9, 62.43, 0.1239, 38, 3.8, -14.5, 0.2422, 37, -10.27, -74.88, 0.0325, 6, 45, 191.01, -14.27, 0.007, 44, 278.45, 4.42, 0.0001, 46, 98.04, -20.92, 0.59301, 39, 16.73, 96.85, 0.0305, 38, 11.54, 19.91, 0.2422, 37, -2.54, -40.46, 0.1272, 5, 45, 183.96, -47.65, 0.00232, 44, 274.54, -29.48, 0.00074, 46, 88.14, -53.57, 0.59077, 38, 22.91, 52.08, 0.1179, 37, 8.83, -8.29, 0.28827, 5, 45, 177.46, -73.41, 0.00458, 44, 270.47, -55.73, 0.00167, 46, 79.45, -78.67, 0.59086, 38, 32.73, 76.76, 0.04, 37, 18.65, 16.38, 0.36289, 5, 45, 137.95, -78.19, 0.02603, 44, 231.58, -64.17, 0.0099, 46, 39.68, -80.03, 0.6339, 38, 72.53, 76.3, 0.0124, 37, 58.45, 15.93, 0.31777, 5, 45, 112.85, -77.62, 0.10037, 44, 206.53, -65.94, 0.04639, 46, 14.71, -77.31, 0.61924, 38, 97.34, 72.45, 0.0124, 37, 83.26, 12.08, 0.2216, 4, 45, 75.04, -77.6, 0.22105, 44, 168.88, -69.44, 0.14822, 46, -22.95, -74.03, 0.46553, 37, 120.74, 7.09, 0.1652, 5, 45, 42.68, -75.97, 0.27869, 44, 136.51, -70.83, 0.36073, 43, 230.65, -61.69, 0.00013, 46, -55.05, -69.62, 0.27195, 37, 152.6, 1.22, 0.0885, 5, 45, 7.17, -72.75, 0.1939, 44, 100.86, -70.93, 0.64514, 43, 195.1, -64.36, 0.00735, 46, -90.15, -63.35, 0.11361, 37, 187.38, -6.63, 0.04, 5, 45, -34.2, -69.47, 0.07755, 44, 59.36, -71.52, 0.82137, 43, 153.75, -67.94, 0.06239, 46, -131.09, -56.52, 0.03268, 37, 227.96, -15.32, 0.006, 4, 45, -72.41, -63.35, 0.01541, 44, 20.75, -68.98, 0.74512, 43, 115.06, -68.2, 0.23432, 46, -168.63, -47.14, 0.00515, 4, 45, -111.85, -58.21, 0.0007, 44, -19, -67.54, 0.46211, 43, 75.31, -69.63, 0.53691, 46, -207.48, -38.62, 0.00028, 2, 44, -54.74, -65.22, 0.20435, 43, 39.49, -69.89, 0.79565, 2, 44, -92.12, -56.55, 0.08117, 43, 1.59, -63.94, 0.91883, 2, 44, -97.27, -32.77, 0.05008, 43, -5.27, -40.6, 0.94992, 2, 44, -100.39, -18.43, 0.02137, 43, -9.41, -26.51, 0.97863, 2, 44, -104.15, 0.66, 0.0026, 43, -14.54, -7.75, 0.9974, 3, 45, -192.76, 35.07, 6e-05, 44, -108.24, 17.8, 0.00144, 43, -19.86, 9.05, 0.99851, 3, 45, -196.7, 67.69, 0.00041, 44, -115.21, 49.91, 0.00645, 43, -29.12, 40.58, 0.99314, 5, 45, 118.03, -57.1, 0.10711, 44, 209.78, -45.03, 0.03379, 46, 21.64, -57.31, 0.62521, 38, 89.51, 52.79, 0.0592, 37, 75.43, -7.58, 0.17468, 5, 45, 144.99, -55.05, 0.01893, 44, 236.43, -40.48, 0.00606, 46, 48.68, -57.59, 0.65669, 38, 62.51, 54.3, 0.0928, 37, 48.43, -6.07, 0.22552, 5, 45, 77.26, -59.26, 0.27788, 44, 169.39, -50.97, 0.12426, 46, -19.16, -55.95, 0.44898, 38, 130.21, 49.57, 0.04064, 37, 116.13, -10.8, 0.10824, 6, 45, 41.85, -58.93, 0.33381, 44, 134.1, -53.94, 0.33201, 43, 227.03, -45.02, 0.0001, 46, -54.42, -52.57, 0.24033, 38, 165.27, 44.59, 0.0208, 37, 151.19, -15.78, 0.07296, 6, 45, 6.52, -57.17, 0.24967, 44, 98.76, -55.48, 0.61688, 43, 191.89, -49.1, 0.00572, 46, -89.46, -47.77, 0.09093, 38, 200.06, 38.21, 0.0112, 37, 185.98, -22.16, 0.0256, 5, 45, -35.51, -52.64, 0.07345, 44, 56.49, -54.89, 0.84557, 43, 149.69, -51.56, 0.05263, 46, -130.94, -39.65, 0.02195, 37, 227.05, -32.17, 0.0064, 4, 45, -75.37, -46.14, 0.00989, 44, 16.2, -52.12, 0.74472, 43, 109.3, -51.71, 0.24251, 46, -170.1, -29.74, 0.00287, 4, 45, -113.36, -37.64, 0.00021, 44, -22.42, -47.2, 0.42266, 43, 70.43, -49.58, 0.57702, 46, -207.21, -18, 0.0001, 2, 44, -59.14, -42.53, 0.15547, 43, 33.47, -47.58, 0.84453, 6, 45, 157.59, 16.47, 0.09573, 44, 242.32, 31.91, 8e-05, 46, 67.39, 12.59, 0.58218, 39, 45.82, 61.98, 0.10065, 38, 40.63, -14.96, 0.2108, 37, 26.55, -75.33, 0.01056, 6, 45, 126.63, 13.07, 0.26745, 44, 211.81, 25.64, 0.00283, 46, 36.25, 11.86, 0.47458, 39, 76.96, 61.29, 0.06793, 38, 71.77, -15.65, 0.17725, 37, 57.69, -76.02, 0.00996, 5, 45, 78.89, 8.41, 0.57498, 44, 164.71, 16.55, 0.02093, 46, -11.71, 11.33, 0.23472, 39, 124.9, 59.64, 0.04297, 38, 119.71, -17.29, 0.1264, 6, 45, 36.82, 5.34, 0.67092, 44, 123.11, 9.57, 0.13953, 43, 211.48, 17.54, 0.00022, 46, -53.89, 11.89, 0.08181, 39, 167.01, 57.16, 0.0208, 38, 161.82, -19.77, 0.08672, 6, 45, 1.63, 5.21, 0.54871, 44, 88.08, 6.17, 0.38414, 43, 176.8, 11.62, 0.005, 46, -88.96, 14.79, 0.01095, 39, 201.91, 52.66, 0.0112, 38, 196.72, -24.27, 0.04, 5, 45, -41.39, 4.07, 0.21732, 44, 45.36, 1.04, 0.67726, 43, 134.55, 3.41, 0.09127, 46, -131.92, 17.36, 0.00135, 38, 239.51, -28.8, 0.0128, 4, 45, -78.55, 6.31, 0.05561, 44, 8.15, -0.2, 0.61111, 43, 97.53, -0.51, 0.33327, 46, -168.75, 22.79, 1e-05, 4, 45, 157.03, 55.96, 0.16523, 46, 70.23, 51.97, 0.50049, 39, 41.19, 22.77, 0.24216, 38, 36, -54.17, 0.09213, 5, 45, 124.21, 51.24, 0.38025, 44, 205.85, 63.42, 0.00039, 46, 37.13, 50.1, 0.36521, 39, 74.34, 23.14, 0.19241, 38, 69.15, -53.8, 0.06173, 6, 45, 80.23, 45.85, 0.63095, 44, 162.55, 53.96, 0.0135, 43, 247.63, 64.65, 0.00011, 46, -7.16, 48.52, 0.16859, 39, 118.66, 22.7, 0.14048, 38, 113.46, -54.24, 0.04637, 6, 45, 35.67, 39.9, 0.77898, 44, 118.74, 43.89, 0.08148, 43, 204.65, 51.44, 0.00365, 46, -52.06, 46.43, 0.03477, 39, 163.61, 22.74, 0.08512, 38, 158.42, -54.2, 0.016, 6, 45, -0.05, 35.49, 0.59447, 44, 83.58, 36.17, 0.31107, 43, 170.15, 41.21, 0.03792, 46, -88.03, 45.11, 0.00374, 39, 199.6, 22.42, 0.04, 38, 194.41, -54.51, 0.0128, 4, 45, -43.7, 31.65, 0.29984, 44, 40.49, 28.27, 0.5387, 43, 127.73, 30.23, 0.15025, 39, 243.38, 20.51, 0.0112, 3, 45, -80.96, 31.99, 0.0777, 44, 3.35, 25.14, 0.48234, 43, 90.92, 24.43, 0.43995, 3, 45, -118.74, 35.13, 0.01365, 44, -34.55, 24.76, 0.21254, 43, 53.14, 21.31, 0.77381, 3, 45, -152.92, 35.46, 0.0014, 44, -68.61, 21.9, 0.03283, 43, 19.37, 16, 0.96577, 3, 45, -117.69, 9.48, 0.00208, 44, -31.12, -0.68, 0.27776, 43, 58.4, -3.82, 0.72016, 3, 45, -152.28, 12.88, 9e-05, 44, -65.87, -0.52, 0.06291, 43, 23.73, -6.16, 0.937, 6, 45, 152.83, -19.51, 0.03221, 44, 240.93, -4.36, 0.0021, 46, 59.55, -22.86, 0.64756, 39, 55.26, 97.03, 0.01152, 38, 50.07, 20.09, 0.2076, 37, 35.99, -40.28, 0.099, 6, 45, 123.95, -23.85, 0.17507, 44, 212.58, -11.37, 0.01811, 46, 30.4, -24.69, 0.57245, 39, 84.47, 97.54, 0.01065, 38, 79.27, 20.6, 0.17152, 37, 65.2, -39.77, 0.0522, 5, 45, 77.42, -27.64, 0.38162, 44, 166.6, -19.48, 0.08174, 46, -16.28, -24.46, 0.36731, 38, 125.9, 18.25, 0.128, 37, 111.82, -42.12, 0.04132, 6, 45, 39.52, -29.2, 0.51856, 44, 129.01, -24.56, 0.22956, 43, 219.84, -16.08, 1e-05, 46, -54.17, -22.75, 0.15875, 38, 163.67, 14.82, 0.07872, 37, 149.59, -45.56, 0.0144, 6, 45, 4.24, -27.28, 0.34789, 44, 93.71, -25.93, 0.54341, 43, 184.72, -20, 0.00397, 46, -89.16, -17.8, 0.05354, 38, 198.4, 8.28, 0.0448, 37, 184.32, -52.09, 0.0064, 5, 45, -38.59, -24.65, 0.14558, 44, 50.82, -27.3, 0.78096, 43, 142.04, -24.46, 0.05238, 46, -131.6, -11.49, 0.00988, 38, 240.51, 0.05, 0.0112, 4, 45, -77.12, -18.6, 0.01087, 44, 11.89, -24.87, 0.70739, 43, 103.04, -24.84, 0.28088, 46, -169.47, -2.15, 0.00086, 3, 45, -115.53, -13.2, 5e-05, 44, -26.85, -23.07, 0.3612, 43, 64.27, -25.84, 0.63875, 2, 44, -62.64, -21.14, 0.0996, 43, 28.44, -26.5, 0.9004], "hull": 30, "edges": [0, 58, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58], "width": 167, "height": 330}}, "Cloak_Right": {"Cloak_Right": {"type": "mesh", "uvs": [0.65879, 0.10156, 0.72624, 0.20556, 0.7798, 0.29156, 0.84328, 0.39456, 0.89486, 0.51356, 0.9266, 0.61756, 0.95239, 0.71656, 0.97619, 0.81156, 1, 0.90256, 1, 1, 0.86006, 0.99999, 0.68549, 0.99799, 0.47323, 1, 0.2808, 1, 0.03769, 0.99999, 0.03769, 0.89556, 0.04114, 0.80783, 0.03803, 0.70056, 0.04001, 0.60056, 0.03272, 0.50156, 0.02559, 0.39472, 0.01378, 0.29337, 0.01142, 0.18835, 0.00708, 0.09451, 0.08383, 0.021, 0.1955, 0.005, 0.31651, 0.001, 0.4157, 0, 0.50893, 0.019, 0.56952, 0.03, 0.29468, 0.80756, 0.29215, 0.89516, 0.30165, 0.70057, 0.3006, 0.59688, 0.2933, 0.5039, 0.27346, 0.39369, 0.24494, 0.28998, 0.22794, 0.19101, 0.20537, 0.09892, 0.6936, 0.89765, 0.68749, 0.81279, 0.67956, 0.70312, 0.67253, 0.60331, 0.64114, 0.50308, 0.61336, 0.39033, 0.59194, 0.29354, 0.84634, 0.90205, 0.84826, 0.81573, 0.82409, 0.71099, 0.79927, 0.6095, 0.77498, 0.50642, 0.73306, 0.3911, 0.69758, 0.29241, 0.63979, 0.19526, 0.57843, 0.0999, 0.54709, 0.18769, 0.49653, 0.09626, 0.47977, 0.89401, 0.48317, 0.81045, 0.48311, 0.69935, 0.48148, 0.59932, 0.47698, 0.50351, 0.46137, 0.3925, 0.44742, 0.29225, 0.40609, 0.19213, 0.36999, 0.09895], "triangles": [40, 41, 48, 14, 15, 13, 13, 31, 12, 13, 15, 31, 10, 8, 9, 12, 57, 11, 12, 31, 57, 11, 46, 10, 10, 46, 8, 11, 39, 46, 11, 57, 39, 8, 46, 7, 46, 47, 7, 46, 39, 47, 57, 40, 39, 39, 40, 47, 15, 16, 31, 31, 30, 57, 31, 16, 30, 57, 58, 40, 57, 30, 58, 40, 48, 47, 47, 6, 7, 47, 48, 6, 58, 41, 40, 30, 32, 58, 16, 17, 30, 58, 59, 41, 48, 5, 6, 41, 49, 48, 48, 49, 5, 59, 42, 41, 41, 42, 49, 17, 18, 32, 18, 33, 32, 32, 60, 59, 32, 33, 60, 59, 60, 42, 60, 43, 42, 18, 34, 33, 18, 19, 34, 33, 61, 60, 60, 61, 43, 33, 34, 61, 19, 35, 34, 19, 20, 35, 32, 59, 58, 30, 17, 32, 49, 4, 5, 42, 50, 49, 49, 50, 4, 42, 43, 50, 50, 3, 4, 43, 51, 50, 50, 51, 3, 34, 62, 61, 34, 35, 62, 61, 44, 43, 61, 62, 44, 43, 44, 51, 51, 2, 3, 35, 63, 62, 62, 45, 44, 62, 63, 45, 44, 52, 51, 51, 52, 2, 44, 45, 52, 63, 55, 45, 45, 53, 52, 45, 55, 53, 52, 1, 2, 52, 53, 1, 53, 0, 1, 20, 36, 35, 20, 21, 36, 21, 37, 36, 21, 22, 37, 36, 64, 63, 36, 37, 64, 0, 53, 54, 37, 65, 64, 64, 56, 55, 64, 65, 56, 22, 38, 37, 37, 38, 65, 22, 23, 38, 53, 55, 54, 55, 56, 54, 54, 28, 29, 0, 54, 29, 38, 26, 65, 65, 27, 56, 65, 26, 27, 23, 24, 38, 24, 25, 38, 38, 25, 26, 54, 56, 28, 56, 27, 28, 35, 36, 63, 63, 64, 55], "vertices": [2, 33, 34.1, 59.73, 0.64387, 34, -55.47, 56.15, 0.35613, 2, 33, 74.23, 61.29, 0.35457, 34, -15.45, 59.53, 0.64543, 4, 33, 107.29, 62.18, 0.11307, 34, 17.54, 61.93, 0.87904, 35, -79.15, 54.27, 0.00783, 36, -169.47, 38.38, 6e-05, 5, 33, 146.87, 63.13, 0.01559, 34, 57.03, 64.68, 0.92054, 35, -40.27, 61.68, 0.05317, 36, -131.45, 49.41, 0.00469, 42, 224.21, 13.57, 0.006, 5, 33, 191.48, 60.35, 0.00043, 34, 101.72, 63.94, 0.74524, 35, 4.2, 66.23, 0.17174, 36, -87.61, 58.14, 0.03459, 42, 180.43, 4.53, 0.048, 4, 34, 140.22, 60.9, 0.43581, 35, 42.79, 67.77, 0.3185, 36, -49.34, 63.29, 0.12419, 42, 142.2, -0.91, 0.1215, 4, 34, 176.68, 57.2, 0.17769, 35, 79.43, 68.41, 0.3211, 36, -12.92, 67.38, 0.29881, 42, 105.81, -5.27, 0.2024, 5, 34, 206.45, 53.95, 0.05198, 35, 109.38, 68.71, 0.17439, 36, 16.87, 70.49, 0.49078, 42, 76.05, -8.6, 0.2722, 41, 93.94, -68.81, 0.01065, 5, 34, 235.6, 51.22, 0.01079, 35, 138.64, 69.45, 0.06174, 36, 45.93, 73.99, 0.59504, 42, 47.01, -12.31, 0.32003, 41, 64.9, -72.52, 0.0124, 5, 34, 269.16, 42.55, 0.00223, 35, 173, 64.82, 0.02026, 36, 80.57, 72.6, 0.58173, 42, 12.36, -11.18, 0.35578, 41, 30.26, -71.39, 0.04, 5, 34, 274, 16.69, 0.00082, 35, 180.86, 39.72, 0.00979, 36, 90.76, 48.36, 0.58795, 42, 2, 12.99, 0.28353, 41, 19.89, -47.21, 0.1179, 6, 34, 275.41, -15.69, 9e-05, 35, 186.1, 7.73, 0.00208, 36, 98.98, 17, 0.60434, 42, -6.46, 44.29, 0.12579, 41, 11.44, -15.92, 0.2362, 40, 15.72, -94.94, 0.0315, 5, 35, 189.33, -32.16, 0.00581, 36, 105.95, -22.4, 0.59349, 42, -13.72, 83.64, 0.0325, 41, 4.17, 23.43, 0.2422, 40, 8.46, -55.58, 0.126, 4, 35, 188.11, -68.96, 0.01751, 36, 108.2, -59.15, 0.57805, 41, 1.65, 60.16, 0.1179, 40, 5.94, -18.85, 0.28653, 4, 35, 184.13, -113.77, 0.03018, 36, 108.46, -104.14, 0.56738, 41, 1.07, 105.15, 0.04, 40, 5.35, 26.13, 0.36244, 5, 33, 304.4, -135.51, 0.00039, 35, 147.64, -108.52, 0.08753, 36, 71.63, -102.35, 0.58864, 41, 37.91, 103.63, 0.0124, 40, 42.2, 24.61, 0.31103, 6, 33, 274.82, -127.19, 0.00414, 34, 193.52, -119.61, 0.00047, 35, 117.09, -105.15, 0.23304, 36, 40.9, -101.87, 0.49726, 41, 68.64, 103.38, 0.01109, 40, 72.92, 24.36, 0.254, 5, 33, 228.18, -113.75, 0.02289, 34, 146.31, -108.31, 0.00642, 35, 68.88, -99.52, 0.4839, 36, -7.63, -100.8, 0.31679, 40, 121.46, 23.65, 0.17, 5, 33, 181.03, -101.1, 0.08293, 34, 98.64, -97.81, 0.03919, 35, 20.29, -94.75, 0.626, 36, -56.44, -100.62, 0.13438, 40, 170.28, 23.83, 0.1175, 5, 33, 143.6, -91.85, 0.22632, 34, 60.82, -90.28, 0.12435, 35, -18.15, -91.75, 0.5607, 36, -95, -101.25, 0.04062, 40, 208.83, 24.74, 0.048, 5, 33, 105.52, -82.39, 0.47554, 34, 22.35, -82.56, 0.1699, 35, -57.26, -88.64, 0.3352, 36, -134.23, -101.83, 0.00737, 40, 248.05, 25.61, 0.012, 4, 33, 69.15, -74.31, 0.72325, 34, -14.35, -76.15, 0.13436, 35, -94.46, -86.62, 0.14162, 36, -171.46, -103.32, 0.00077, 3, 33, 31.96, -64.18, 0.93585, 34, -51.96, -67.72, 0.02077, 35, -132.81, -82.71, 0.04337, 3, 33, -1.39, -55.52, 0.98848, 34, -85.67, -60.6, 0.00249, 35, -167.12, -79.63, 0.00903, 3, 33, -23.45, -34.48, 0.99859, 34, -108.66, -40.59, 6e-05, 35, -192.33, -62.48, 0.00135, 3, 33, -23.44, -13.01, 0.99748, 34, -109.64, -19.13, 0.00213, 35, -195.83, -41.29, 0.00038, 3, 33, -18.73, 8.93, 0.97923, 34, -105.92, 2.99, 0.02077, 35, -194.77, -18.88, 1e-05, 2, 33, -14.06, 26.68, 0.92518, 34, -102.07, 20.94, 0.07482, 2, 33, -2.63, 41.36, 0.8656, 34, -91.32, 36.13, 0.1344, 2, 33, 4.32, 51.03, 0.80946, 34, -84.82, 46.11, 0.19054, 6, 33, 286.91, -83, 0.00171, 34, 203.58, -74.92, 9e-05, 35, 121.79, -59.59, 0.23197, 36, 41.3, -56.06, 0.51556, 41, 68.58, 57.57, 0.06135, 40, 72.87, -21.45, 0.18932, 5, 33, 315.08, -90.74, 8e-05, 35, 150.85, -62.61, 0.05845, 36, 70.51, -56.35, 0.60195, 41, 39.37, 57.63, 0.09175, 40, 43.66, -21.38, 0.24776, 6, 33, 240.27, -67.25, 0.01126, 34, 156.27, -61.3, 0.00821, 35, 73.2, -51.67, 0.49824, 36, -7.83, -52.75, 0.2927, 41, 117.73, 54.62, 0.04119, 40, 122.01, -24.39, 0.1484, 6, 33, 192.57, -54.26, 0.04962, 34, 108.03, -50.5, 0.07864, 35, 24.02, -46.66, 0.66067, 36, -57.26, -52.39, 0.11187, 41, 167.16, 54.62, 0.016, 40, 171.45, -24.39, 0.0832, 6, 33, 157.62, -45.72, 0.15612, 34, 72.72, -43.56, 0.23559, 35, -11.86, -43.94, 0.53414, 36, -93.24, -53.07, 0.02616, 41, 203.13, 55.56, 0.0064, 40, 207.42, -23.45, 0.0416, 5, 33, 117.71, -38.18, 0.33892, 34, 32.51, -37.84, 0.35739, 35, -52.47, -43.03, 0.29095, 36, -133.75, -55.98, 0.00315, 40, 247.91, -20.24, 0.0096, 4, 33, 79.65, -32.83, 0.63164, 34, -5.74, -34.24, 0.26904, 35, -90.88, -43.99, 0.09916, 36, -171.9, -60.54, 0.00017, 3, 33, 43.86, -25.92, 0.84817, 34, -41.82, -28.96, 0.12693, 35, -127.33, -43.02, 0.0249, 3, 33, 10.21, -20.68, 0.98505, 34, -75.67, -25.27, 0.01097, 35, -161.38, -43.36, 0.00398, 6, 34, 239.47, -6.05, 0.00219, 35, 149.27, 13.05, 0.04341, 36, 61.82, 18.83, 0.64274, 42, 30.72, 42.73, 0.09606, 41, 48.61, -17.48, 0.2044, 40, 52.9, -96.49, 0.0112, 6, 34, 210.33, -0.33, 0.02447, 35, 119.65, 15.27, 0.19963, 36, 32.13, 18.26, 0.52535, 42, 60.41, 43.52, 0.07326, 41, 78.3, -16.69, 0.16692, 40, 82.59, -95.7, 0.01036, 6, 33, 252.08, 1.7, 0.00026, 34, 164.93, 8.11, 0.1135, 35, 73.58, 18.28, 0.44593, 36, -14.03, 16.92, 0.27712, 42, 106.55, 45.2, 0.04479, 41, 124.44, -15.01, 0.1184, 6, 33, 209.79, 12.38, 0.00174, 34, 122.19, 16.86, 0.33293, 35, 30.11, 21.9, 0.49107, 36, -57.65, 16.44, 0.08274, 42, 150.17, 46, 0.0224, 41, 168.06, -14.2, 0.06912, 6, 33, 174.94, 16.26, 0.00567, 34, 87.2, 19.14, 0.6681, 35, -4.91, 20.03, 0.28057, 36, -92.33, 11.28, 0.00886, 42, 184.81, 51.42, 0.008, 41, 202.7, -8.79, 0.0288, 5, 33, 133.73, 22.64, 0.03845, 34, 45.75, 23.64, 0.86486, 35, -46.6, 19.59, 0.08863, 36, -133.8, 6.91, 6e-05, 41, 244.14, -4.12, 0.008, 3, 33, 98.48, 28.55, 0.22152, 34, 10.27, 27.94, 0.77557, 35, -82.35, 19.65, 0.00291, 5, 34, 238.43, 22.47, 0.00681, 35, 144.86, 41.25, 0.04411, 36, 54.77, 46.49, 0.62033, 42, 37.97, 15.12, 0.24216, 41, 55.86, -45.08, 0.0866, 5, 34, 210.62, 29.78, 0.03925, 35, 116.38, 45.2, 0.18537, 36, 26.05, 47.75, 0.51047, 42, 66.7, 14.07, 0.20551, 41, 84.59, -46.13, 0.0594, 5, 34, 169.24, 34.57, 0.15711, 35, 74.72, 45.06, 0.36455, 36, -15.41, 43.69, 0.28301, 42, 108.13, 18.44, 0.15608, 41, 126.02, -41.77, 0.03924, 6, 33, 220.51, 33.71, 9e-05, 34, 131.93, 38.65, 0.41861, 35, 37.19, 44.7, 0.37432, 36, -52.74, 39.8, 0.10106, 42, 145.43, 22.61, 0.09152, 41, 163.32, -37.6, 0.0144, 6, 33, 182.89, 39.74, 0.00068, 34, 94.08, 42.96, 0.73908, 35, -0.9, 44.49, 0.19657, 36, -90.64, 36.01, 0.02046, 42, 183.3, 26.67, 0.0352, 41, 201.2, -33.53, 0.008, 5, 33, 140.06, 43.86, 0.01596, 34, 51.11, 45.13, 0.93798, 35, -43.83, 41.56, 0.0367, 36, -133.11, 29.05, 0.00136, 42, 225.71, 33.95, 0.008, 3, 33, 103.43, 47.46, 0.12495, 34, 14.35, 47.06, 0.87238, 35, -80.56, 39.12, 0.00267, 2, 33, 66.21, 46.94, 0.41131, 34, -22.81, 44.84, 0.58869, 2, 33, 29.44, 45.6, 0.70571, 34, -59.47, 41.82, 0.29429, 3, 33, 58.85, 31.2, 0.51495, 34, -29.45, 28.78, 0.48479, 35, -121.88, 15.79, 0.00026, 2, 33, 24.01, 31.39, 0.80961, 34, -64.26, 27.38, 0.19039, 6, 34, 235.87, -45.84, 8e-05, 35, 150.41, -26.89, 0.04646, 36, 66.71, -20.82, 0.6367, 42, 25.53, 82.35, 0.01024, 41, 43.43, 22.14, 0.206, 40, 47.71, -56.87, 0.10052, 7, 33, 292.88, -48.45, 0.00025, 34, 207.98, -40.13, 0.00286, 35, 122.03, -24.52, 0.22143, 36, 38.24, -21.13, 0.51976, 42, 54.01, 82.87, 0.00927, 41, 71.9, 22.66, 0.17367, 40, 76.18, -56.36, 0.07276, 6, 33, 246.81, -34.59, 0.00287, 34, 161.31, -28.38, 0.0455, 35, 74.31, -18.38, 0.50665, 36, -9.85, -19.51, 0.28102, 41, 120, 21.39, 0.1216, 40, 124.29, -57.62, 0.04236, 6, 33, 201.98, -21.76, 0.01303, 34, 115.95, -17.61, 0.19994, 35, 27.99, -13.06, 0.60919, 36, -56.47, -18.57, 0.08632, 41, 166.62, 20.79, 0.06912, 40, 170.91, -58.22, 0.0224, 6, 33, 166.78, -12.99, 0.04739, 34, 80.38, -10.45, 0.45649, 35, -8.18, -10.16, 0.44414, 36, -92.74, -19.09, 0.01198, 41, 202.89, 21.58, 0.0304, 40, 207.18, -57.43, 0.0096, 5, 33, 126.8, -4.62, 0.17032, 34, 40.07, -3.91, 0.6413, 35, -48.98, -8.44, 0.18153, 36, -133.53, -21.21, 0.00045, 41, 243.66, 24.01, 0.0064, 3, 33, 90.71, 2.97, 0.37521, 34, 3.67, 2.03, 0.5726, 35, -85.83, -6.86, 0.05218, 3, 33, 53.27, 5.67, 0.7098, 34, -33.85, 3.02, 0.28454, 35, -123.2, -10.32, 0.00566, 3, 33, 18.55, 8.6, 0.89878, 34, -68.67, 4.37, 0.10069, 35, -157.93, -13.1, 0.00053], "hull": 30, "edges": [0, 58, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58], "width": 165, "height": 332}}, "CROWN": {"CROWN": {"x": 102.53, "y": -0.42, "rotation": -90.6, "width": 89, "height": 156}}, "CrownEye": {"CrownEye": {"x": 1.01, "y": 1.26, "rotation": -90.6, "width": 49, "height": 43}, "CrownEye_Closed": {"x": -6.11, "y": 1.34, "rotation": -90.6, "width": 47, "height": 20}}, "CrownGrass": {"CrownGrass": {"x": 20.82, "y": 0.43, "rotation": -90.6, "width": 112, "height": 24}}, "Drip1": {"Drip1": {"x": 1.24, "y": -50.64, "width": 20, "height": 95}}, "Drip2": {"Drip2": {"x": 1.24, "y": -50.64, "width": 24, "height": 92}}, "Drip3": {"Drip1": {"x": 1.24, "y": -50.64, "width": 20, "height": 95}}, "Drip4": {"Drip2": {"x": 1.24, "y": -50.64, "width": 24, "height": 92}}, "Drip5": {"Drip1": {"x": 1.24, "y": -50.64, "width": 20, "height": 95}}, "Drip6": {"Drip2": {"x": 1.24, "y": -50.64, "width": 24, "height": 92}}, "HairBit_0": {"BandageHair2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 18, 32.7, 32.89, 1, 1, 18, 29.44, -29.02, 1, 1, 5, 134.12, -82.99, 1, 2, 5, 131.93, -144.95, 0.432, 4, 270.73, -145.02, 0.568], "hull": 4, "edges": [0, 6, 0, 2, 2, 4, 4, 6], "width": 46, "height": 31}}, "HairBit_1": {"BandageHair1": {"type": "mesh", "uvs": [1, 0.34406, 1, 1, 0, 1, 0, 0, 0.47549, 0], "triangles": [2, 4, 1, 2, 3, 4, 4, 0, 1], "vertices": [1, 5, 122.2, 42.17, 1, 1, 19, 24.49, 43.29, 1, 1, 19, 29.17, -31.57, 1, 2, 5, 138.61, 116.63, 0.528, 4, 277.41, 116.57, 0.472, 1, 5, 137.35, 80.99, 1], "hull": 5, "edges": [0, 8, 0, 2, 2, 4, 4, 6, 6, 8], "width": 68, "height": 36}}, "Hands_0": {"Hands_0": {"x": 40.16, "y": 1.83, "rotation": -176.47, "width": 88, "height": 47}}, "Hands_1": {"Hands_1": {"x": 39.45, "y": -3.19, "rotation": -2.15, "width": 90, "height": 50}}, "HEAD": {"HEAD": {"type": "mesh", "uvs": [0.61539, 0.03105, 0.67304, 0.01253, 0.74211, 0.06514, 0.8324, 0.04932, 0.88895, 0.13729, 0.96561, 0.19089, 0.99235, 0.27017, 0.98183, 0.32964, 1, 0.42001, 1, 0.50311, 0.99223, 0.59533, 1, 0.62815, 1, 0.72277, 0.95124, 0.75892, 0.966, 0.85831, 0.90595, 0.87229, 0.86204, 0.94459, 0.7755, 0.94732, 0.7523, 0.95497, 0.70055, 0.98541, 0.61019, 1, 0.57013, 1, 0.50577, 1, 0.45647, 1, 0.37791, 1, 0.33836, 1, 0.24964, 1, 0.16401, 0.95204, 0.12683, 0.88312, 0.083, 0.88135, 0.06766, 0.81625, 0.0239, 0.76191, 0, 0.67504, 0.00129, 0.60782, 0.00387, 0.47836, 0.01571, 0.35438, 0.02721, 0.3154, 0, 0.27128, 0.0229, 0.21442, 0.07042, 0.1333, 0.14195, 0.12215, 0.15484, 0.08128, 0.25684, 0.02803, 0.28263, 0.04909, 0.33305, 0, 0.50049, 0, 0.57436, 0, 0.04518, 0.36992, 0.1196, 0.38463, 0.09684, 0.22804, 0.12775, 0.26096, 0.10588, 0.51726, 0.10717, 0.64004, 0.04001, 0.71098, 0.11492, 0.71916, 0.10201, 0.82421, 0.17015, 0.83642, 0.24303, 0.87122, 0.26732, 0.91975, 0.79814, 0.87609, 0.82496, 0.80886, 0.17433, 0.19604, 0.8742, 0.39149, 0.8638, 0.53478, 0.32732, 0.69238, 0.31957, 0.5505, 0.32344, 0.40862, 0.70832, 0.40589, 0.33636, 0.28857, 0.36637, 0.85485, 0.39965, 0.90299, 0.70302, 0.56488, 0.6967, 0.68198, 0.70187, 0.2977, 0.51072, 0.29634, 0.39577, 0.13672, 0.60758, 0.12854, 0.84394, 0.6671, 0.51072, 0.56782, 0.50555, 0.40684, 0.51072, 0.6947, 0.52037, 0.84866, 0.65274, 0.83635, 0.62907, 0.9071, 0.87089, 0.28804], "triangles": [68, 75, 74, 74, 76, 73, 48, 50, 68, 73, 84, 62, 67, 73, 62, 79, 68, 74, 66, 48, 68, 66, 68, 79, 51, 48, 66, 63, 67, 62, 65, 51, 66, 74, 73, 79, 71, 67, 63, 79, 73, 67, 71, 78, 67, 78, 79, 67, 65, 66, 79, 78, 65, 79, 52, 51, 65, 77, 71, 63, 72, 78, 71, 72, 71, 77, 64, 65, 78, 52, 65, 64, 80, 78, 72, 64, 78, 80, 54, 52, 64, 60, 72, 77, 82, 80, 72, 82, 72, 60, 56, 54, 64, 81, 80, 82, 69, 64, 80, 69, 80, 81, 76, 46, 0, 75, 44, 45, 43, 44, 75, 43, 61, 41, 43, 41, 42, 40, 41, 61, 49, 39, 40, 49, 40, 61, 38, 39, 49, 50, 49, 61, 4, 2, 3, 84, 4, 5, 84, 5, 6, 75, 61, 43, 68, 61, 75, 45, 46, 76, 75, 45, 76, 50, 61, 68, 74, 75, 76, 1, 76, 0, 1, 2, 76, 73, 76, 2, 4, 73, 2, 84, 73, 4, 49, 37, 38, 36, 49, 50, 36, 37, 49, 7, 84, 6, 48, 47, 36, 35, 36, 47, 50, 48, 36, 62, 84, 7, 62, 7, 8, 34, 35, 47, 48, 34, 47, 51, 34, 48, 9, 63, 62, 9, 62, 8, 10, 63, 9, 33, 34, 51, 33, 51, 52, 77, 63, 10, 77, 10, 11, 32, 33, 52, 53, 32, 52, 53, 52, 54, 13, 77, 11, 12, 13, 11, 31, 32, 53, 60, 77, 13, 54, 31, 53, 30, 31, 54, 55, 30, 54, 55, 54, 56, 56, 64, 69, 15, 60, 13, 57, 56, 69, 14, 15, 13, 59, 82, 60, 59, 60, 15, 29, 30, 55, 28, 55, 56, 29, 55, 28, 70, 69, 81, 83, 81, 82, 58, 57, 69, 16, 59, 15, 59, 18, 82, 17, 59, 16, 18, 83, 82, 27, 28, 56, 27, 56, 57, 27, 57, 58, 59, 17, 18, 19, 83, 18, 26, 27, 58, 70, 25, 58, 70, 58, 69, 26, 58, 25, 24, 25, 70, 23, 70, 81, 22, 23, 81, 24, 70, 23, 81, 21, 22, 83, 21, 81, 20, 21, 83, 19, 20, 83], "vertices": [1, 4, 351.22, -53.95, 1, 1, 4, 356.45, -73.66, 1, 1, 4, 338.79, -96.42, 1, 1, 4, 342.77, -127.14, 1, 1, 4, 313.94, -145.26, 1, 1, 4, 295.82, -170.58, 1, 1, 4, 270.5, -178.73, 1, 1, 4, 251.28, -174.49, 1, 1, 4, 222.32, -179.62, 1, 1, 4, 195.57, -178.67, 1, 1, 4, 166.1, -175, 1, 1, 4, 155.51, -177.26, 1, 1, 4, 125.23, -176.18, 1, 1, 4, 114.24, -159.29, 1, 1, 4, 82.25, -163.15, 1, 1, 4, 78.5, -142.68, 1, 1, 4, 55.88, -127.01, 1, 1, 4, 56.04, -97.72, 1, 1, 4, 53.88, -89.79, 1, 1, 4, 44.83, -71.94, 1, 1, 4, 41.16, -41.22, 1, 1, 4, 41.64, -27.67, 1, 1, 4, 42.41, -5.91, 1, 1, 4, 43, 10.76, 1, 1, 4, 43.94, 37.33, 1, 1, 4, 44.42, 50.71, 1, 1, 4, 45.48, 80.71, 1, 1, 4, 61.86, 109.12, 1, 1, 4, 84.36, 120.92, 1, 1, 4, 85.45, 135.72, 1, 1, 4, 106.47, 140.17, 1, 1, 4, 124.39, 154.35, 1, 1, 4, 152.55, 161.45, 1, 1, 4, 173.97, 160.25, 1, 1, 4, 215.38, 157.91, 1, 1, 4, 254.92, 152.51, 1, 1, 4, 267.32, 148.17, 1, 1, 4, 282.05, 156.86, 1, 1, 4, 299.62, 148.49, 1, 1, 4, 325.02, 131.5, 1, 1, 4, 327.73, 107.18, 1, 1, 4, 340.65, 102.36, 1, 1, 4, 356.48, 67.26, 1, 1, 4, 349.43, 58.78, 1, 1, 4, 364.54, 41.17, 1, 1, 4, 362.53, -15.45, 1, 1, 4, 361.65, -40.43, 1, 1, 4, 249.59, 142.72, 1, 2, 4, 243.98, 117.71, 0.488, 5, 103.57, 117.83, 0.512, 1, 4, 294.38, 123.64, 1, 2, 4, 283.51, 113.56, 0.6, 5, 143.1, 113.68, 0.4, 2, 4, 201.66, 123.86, 0.488, 5, 61.25, 123.98, 0.512, 2, 4, 162.34, 124.81, 0.488, 5, 21.93, 124.93, 0.512, 1, 4, 140.49, 148.33, 1, 2, 4, 136.93, 123.09, 0.536, 5, -3.48, 123.21, 0.464, 1, 4, 103.51, 128.64, 1, 2, 4, 98.76, 105.74, 0.712, 5, -41.65, 105.86, 0.288, 2, 4, 86.76, 81.49, 0.808, 5, -53.65, 81.61, 0.192, 2, 4, 70.95, 73.82, 0.952, 5, -69.46, 73.94, 0.048, 2, 4, 78.56, -106.18, 0.936, 5, -61.85, -106.06, 0.064, 2, 4, 99.74, -116.01, 0.664, 5, -40.67, -115.89, 0.336, 2, 4, 303.67, 97.07, 0.744, 5, 163.26, 97.19, 0.256, 2, 4, 232.73, -137.39, 0.488, 5, 92.32, -137.27, 0.512, 2, 4, 186.97, -132.25, 0.424, 5, 46.56, -132.13, 0.576, 2, 4, 142.93, 50.96, 0.2, 5, 2.52, 51.08, 0.8, 2, 4, 188.42, 51.97, 0.152, 5, 48.01, 52.09, 0.848, 2, 4, 233.79, 49.05, 0.168, 5, 93.38, 49.18, 0.832, 2, 4, 230.05, -81.13, 0.136, 5, 89.64, -81.01, 0.864, 2, 4, 272.07, 43.33, 0.328, 5, 131.66, 43.45, 0.672, 2, 4, 90.5, 39.59, 0.6, 5, -49.91, 39.71, 0.4, 2, 4, 74.71, 28.88, 0.712, 5, -65.71, 29, 0.288, 2, 4, 179.23, -77.54, 0.12, 5, 38.82, -77.42, 0.88, 2, 4, 141.83, -74.07, 0.2, 5, 1.42, -73.95, 0.8, 2, 4, 264.77, -80.18, 0.328, 5, 124.36, -80.05, 0.672, 2, 4, 267.49, -15.55, 0.2, 5, 127.08, -15.43, 0.8, 2, 4, 320.01, 21.51, 0.808, 5, 179.6, 21.63, 0.192, 2, 4, 320.09, -50.21, 0.808, 5, 179.68, -50.09, 0.192, 2, 4, 144.8, -124.03, 0.488, 5, 4.39, -123.91, 0.512, 1, 5, 40.17, -12.35, 1, 1, 5, 91.75, -12.43, 1, 1, 5, -0.44, -10.91, 1, 2, 4, 90.62, -12.56, 0.44, 5, -49.79, -12.44, 0.56, 2, 4, 92.99, -57.46, 0.52, 5, -47.43, -57.34, 0.48, 2, 4, 70.65, -48.65, 0.808, 5, -69.76, -48.53, 0.192, 2, 4, 265.87, -137.45, 0.584, 5, 125.46, -137.33, 0.416], "hull": 47, "edges": [0, 92, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92], "width": 306, "height": 291}, "Head_Drips": {"type": "<PERSON><PERSON><PERSON>", "parent": "HEAD", "width": 306, "height": 291}}, "MASK": {"MASK": {"type": "clipping", "end": "MASK", "vertexCount": 4, "vertices": [-501.26, -4.28, 670.61, -0.54, 799.84, 1215.37, -470.13, 1231.82], "color": "3a3acdff"}}, "Neck": {"Neck": {"type": "mesh", "uvs": [0.84453, 0.18788, 0.84574, 0.27092, 0.92013, 0.28575, 1, 0.41544, 1, 0.5266, 0.92166, 0.50297, 0.96713, 0.5677, 0.98406, 0.64916, 0.95021, 0.74163, 0.8336, 0.68219, 0.7252, 0.53457, 0.77471, 0.69871, 0.70845, 0.78639, 0.60666, 0.73468, 0.55029, 0.62916, 0.57616, 0.82068, 0.56753, 0.94944, 0.49627, 0.97408, 0.46632, 0.83231, 0.45378, 0.9677, 0.39152, 1, 0.3676, 0.87128, 0.38788, 0.64749, 0.34259, 0.78189, 0.25713, 0.86282, 0.20911, 0.7729, 0.25519, 0.60926, 0.19474, 0.72637, 0.14504, 0.80263, 0.08558, 0.86029, 0.05461, 0.70729, 0.10646, 0.5985, 0.02662, 0.69876, 0, 0.55991, 0.04213, 0.41196, 0.13855, 0.35717, 0.14191, 0.15954, 0.28191, 0, 0.67824, 0, 0.18088, 0.4404, 0.23234, 0.4925, 0.30466, 0.51854, 0.41313, 0.56087, 0.53274, 0.5511, 0.70241, 0.47622, 0.79837, 0.38831, 0.15585, 0.53482, 0.46459, 0.72691], "triangles": [18, 47, 15, 21, 47, 18, 45, 38, 0, 45, 0, 1, 39, 36, 37, 35, 36, 39, 44, 38, 45, 40, 39, 37, 5, 2, 3, 37, 42, 41, 38, 43, 37, 40, 37, 41, 5, 3, 4, 10, 44, 45, 46, 35, 39, 31, 34, 35, 44, 43, 38, 43, 42, 37, 46, 31, 35, 33, 34, 31, 26, 40, 41, 14, 43, 44, 22, 41, 42, 45, 5, 9, 1, 5, 45, 2, 5, 1, 10, 45, 9, 32, 33, 31, 40, 46, 39, 26, 46, 40, 26, 27, 46, 31, 46, 27, 47, 42, 43, 47, 43, 14, 22, 42, 47, 13, 14, 44, 13, 44, 10, 12, 13, 10, 8, 5, 6, 8, 6, 7, 9, 5, 8, 23, 41, 22, 26, 41, 23, 24, 25, 26, 11, 12, 10, 28, 31, 27, 30, 31, 28, 47, 14, 15, 29, 30, 28, 23, 24, 26, 21, 22, 47, 15, 17, 18, 19, 21, 18, 15, 16, 17, 20, 21, 19], "vertices": [1, 6, 30.84, -116.96, 1, 1, 6, 20.13, -116.95, 1, 2, 6, 17.42, -139.33, 0.76, 27, 45.17, 31.81, 0.24, 2, 6, -0.15, -162.85, 0.584, 27, 74.44, 29.55, 0.416, 2, 6, -14.48, -162.34, 0.584, 27, 81.68, 17.17, 0.416, 2, 6, -10.6, -138.8, 0.584, 27, 59.72, 7.86, 0.416, 2, 6, -19.43, -152.23, 0.584, 27, 75.79, 7.58, 0.416, 2, 6, -30.11, -156.97, 0.584, 27, 85.51, 1.09, 0.416, 2, 6, -41.67, -146.33, 0.584, 27, 82.7, -14.37, 0.416, 2, 6, -32.76, -111.41, 0.76, 27, 48.43, -25.53, 0.24, 2, 6, -12.58, -79.36, 0.99197, 27, 10.56, -25.62, 0.00803, 2, 6, -34.27, -93.56, 0.696, 28, 67.5, 10.84, 0.304, 2, 6, -44.86, -73.16, 0.696, 28, 59.08, -10.55, 0.304, 2, 6, -37.11, -42.68, 0.776, 28, 31, -24.73, 0.224, 1, 6, -22.9, -26.14, 1, 2, 6, -47.87, -33.08, 0.6, 30, 50.58, 31.24, 0.4, 2, 6, -64.38, -29.88, 0.472, 30, 67.11, 28.15, 0.528, 2, 6, -66.79, -8.26, 0.472, 30, 69.67, 6.55, 0.528, 2, 6, -48.19, 0.13, 0.6, 30, 51.12, -1.97, 0.4, 2, 6, -65.52, 4.53, 0.472, 30, 68.47, -6.26, 0.528, 2, 6, -69.01, 23.47, 0.472, 30, 72.09, -25.17, 0.528, 2, 6, -52.16, 30.1, 0.6, 30, 55.28, -31.91, 0.4, 2, 6, -23.53, 22.96, 0.744, 29, 21.84, 15.66, 0.256, 2, 6, -40.37, 37.24, 0.68, 29, 43.8, 17.94, 0.32, 2, 6, -49.89, 63.4, 0.616, 29, 69.28, 6.71, 0.384, 2, 6, -37.79, 77.48, 0.616, 29, 71.07, -11.77, 0.384, 3, 6, -17.18, 62.83, 0.54163, 29, 46.24, -16.51, 0.256, 26, 21.94, 24.24, 0.20237, 2, 6, -31.64, 81.61, 0.6, 26, 45.61, 25.51, 0.4, 2, 6, -40.94, 96.96, 0.6, 26, 63.52, 24.48, 0.4, 2, 6, -47.73, 115.17, 0.6, 26, 82.38, 19.77, 0.4, 2, 6, -27.68, 123.81, 0.6, 26, 78.15, -1.65, 0.4, 2, 6, -14.21, 107.67, 0.6, 26, 57.21, -3.61, 0.4, 2, 6, -26.28, 132.22, 0.568, 26, 84.28, -7.57, 0.432, 2, 6, -8.1, 139.63, 0.6, 26, 80.08, -26.75, 0.4, 2, 6, 10.53, 126.23, 0.728, 26, 58.5, -34.51, 0.272, 1, 6, 16.56, 96.88, 1, 1, 6, 42, 94.97, 1, 1, 6, 61.08, 51.99, 1, 1, 6, 56.84, -67.63, 1, 1, 6, 5.38, 84.49, 1, 1, 6, -1.89, 69.19, 1, 1, 6, -6.02, 47.49, 1, 1, 6, -12.63, 14.94, 1, 1, 6, -12.65, -21.2, 1, 1, 6, -4.81, -72.75, 1, 1, 6, 5.5, -102.11, 1, 2, 6, -6.53, 92.47, 0.728, 26, 40.34, -1.33, 0.272, 2, 6, -34.59, 0.17, 0.584, 30, 37.52, -2.09, 0.416], "hull": 39, "edges": [0, 76, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76], "width": 272, "height": 116}}, "Scarf": {"Scarf": {"type": "mesh", "uvs": [0.92757, 0.0876, 1, 0.204, 1, 0.37434, 0.96594, 0.45383, 1, 0.53616, 1, 0.79735, 0.86116, 0.91091, 0.64422, 1, 0.50863, 0.99512, 0.33579, 1, 0.12279, 0.89532, 0, 0.75476, 0, 0.54468, 0.03621, 0.45951, 0, 0.35163, 0, 0.19832, 0.09671, 0.07057, 0.33874, 1e-05, 0.52026, 0, 0.76524, 1e-05, 0.1587, 0.56739, 0.33431, 0.64404, 0.50993, 0.64972, 0.6457, 0.63553, 0.8405, 0.56739], "triangles": [10, 11, 20, 6, 24, 5, 8, 9, 22, 10, 21, 9, 7, 23, 6, 8, 22, 7, 14, 15, 16, 3, 0, 1, 2, 3, 1, 13, 14, 16, 20, 16, 17, 13, 16, 20, 24, 19, 0, 24, 0, 3, 23, 18, 19, 23, 19, 24, 21, 20, 17, 22, 21, 17, 17, 18, 22, 23, 22, 18, 20, 11, 12, 20, 12, 13, 24, 3, 4, 5, 24, 4, 10, 20, 21, 23, 24, 6, 22, 9, 21, 7, 22, 23], "vertices": [2, 4, 70.74, -99.25, 0.97858, 3, 273.09, -99.79, 0.02142, 2, 4, 56.44, -115.19, 0.87122, 3, 259.17, -116.07, 0.12878, 2, 4, 36.35, -114.48, 0.8702, 3, 239.08, -115.85, 0.1298, 2, 4, 27.25, -106.42, 0.64571, 3, 229.78, -108.01, 0.35429, 2, 4, 17.27, -113.81, 0.70513, 3, 219.98, -115.63, 0.29487, 2, 4, -13.53, -112.72, 0.28, 3, 189.16, -115.28, 0.72, 2, 4, -25.81, -80.75, 0.168, 3, 176.12, -83.61, 0.832, 2, 4, -34.58, -31.16, 0.144, 3, 166.17, -34.25, 0.856, 2, 4, -32.91, -0.42, 0.1878, 3, 167.09, -3.48, 0.8122, 2, 4, -32.1, 38.81, 0.13198, 3, 166.97, 35.76, 0.86802, 2, 4, -18.06, 86.7, 0.128, 3, 179.87, 83.97, 0.872, 2, 4, -0.48, 113.96, 0.312, 3, 196.77, 111.65, 0.688, 2, 4, 24.29, 113.09, 0.50105, 3, 221.56, 111.37, 0.49895, 2, 4, 34.05, 104.52, 0.66033, 3, 231.51, 103.04, 0.33967, 2, 4, 47.06, 112.28, 0.8132, 3, 244.33, 111.11, 0.1868, 2, 4, 65.14, 111.64, 0.83696, 3, 262.42, 110.9, 0.16304, 1, 4, 79.43, 89.17, 1, 2, 4, 85.8, 33.97, 0.9757, 3, 284.95, 33.75, 0.0243, 1, 4, 84.35, -7.21, 1, 2, 4, 82.38, -62.79, 0.99676, 3, 283.85, -63.06, 0.00324, 2, 4, 20.34, 77.18, 0.72408, 3, 218.47, 75.38, 0.27592, 2, 4, 9.89, 37.66, 0.66404, 3, 208.97, 35.62, 0.33596, 2, 4, 7.81, -2.16, 0.744, 3, 207.85, -4.24, 0.256, 2, 4, 8.39, -33.02, 0.68283, 3, 209.17, -35.07, 0.31717, 2, 4, 14.86, -77.49, 0.71973, 3, 216.71, -79.38, 0.28027], "hull": 20, "edges": [0, 38, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38], "width": 204, "height": 106}}, "Symbol_0": {"Symbol_0": {"x": 68.85, "y": -33.12, "rotation": -144.2, "width": 50, "height": 93}, "Symbol_1": {"path": "Antlers_1", "x": 110.39, "y": -44.3, "rotation": -144.2, "width": 206, "height": 180}}, "Symbol_1": {"Symbol_1": {"x": 112.64, "y": -44.57, "rotation": -125.12, "width": 71, "height": 152}}, "Symbol_2": {"Symbol_2": {"x": 122.63, "y": 21.63, "rotation": -59.78, "width": 70, "height": 146}}, "Symbol_3": {"Symbol_3": {"x": 76.6, "y": 25.24, "rotation": -35.52, "width": 49, "height": 90}, "Symbol_4": {"path": "Antlers_1", "x": 118.68, "y": 22.23, "scaleX": -1, "rotation": -35.52, "width": 206, "height": 180}}, "Vine": {"Vine": {"type": "mesh", "uvs": [1, 0.26623, 1, 0.47193, 0.94314, 0.73847, 0.79015, 0.92099, 0.65327, 1, 0.38755, 1, 0.19431, 0.90361, 0.0695, 0.7066, 0, 0.44973, 0, 0.2811, 0, 0, 1, 0], "triangles": [3, 6, 7, 2, 3, 7, 5, 6, 3, 4, 5, 3, 2, 7, 1, 10, 11, 0, 9, 10, 0, 8, 9, 0, 8, 0, 1, 7, 8, 1], "vertices": [1, 6, -13.84, -26.89, 1, 2, 6, -37.12, -19.22, 0.89067, 25, 66.58, 75.85, 0.10933, 2, 6, -69.39, 7.19, 0.55733, 25, 41.33, 42.67, 0.44267, 2, 6, -91.11, 33.66, 0.33511, 25, 15.64, 20.03, 0.66489, 2, 6, -100.49, 51.5, 0.224, 25, -1.85, 10.02, 0.776, 2, 6, -99.76, 71.95, 0.224, 25, -22.31, 10.02, 0.776, 2, 6, -87.62, 79.45, 0.33511, 25, -30.24, 21.89, 0.66489, 2, 6, -63.6, 74.3, 0.55733, 25, -25.94, 46.08, 0.44267, 2, 6, -32.02, 57.65, 0.89067, 25, -10.42, 78.22, 0.10933, 1, 6, -12.71, 50.12, 1, 1, 6, 17.35, 49.05, 1, 1, 6, 14.63, -27.9, 1], "hull": 12, "edges": [0, 22, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22], "width": 69, "height": 96}}, "Vine_0": {"Vine_0": {"type": "mesh", "uvs": [1, 0.18233, 1, 0.38113, 0.96287, 0.62642, 0.92496, 0.803, 0.83935, 0.9362, 0.68525, 1, 0.45696, 1, 0.31143, 0.9326, 0.20013, 0.7814, 0.10596, 0.587, 0.03828, 0.37595, 0, 0.17607, 0, 0, 1, 0], "triangles": [8, 9, 2, 3, 8, 2, 7, 8, 3, 3, 5, 7, 5, 6, 7, 3, 4, 5, 2, 9, 1, 11, 12, 13, 11, 13, 0, 10, 11, 0, 10, 0, 1, 9, 10, 1], "vertices": [1, 6, -1.62, 17.26, 1, 2, 6, -26.97, 15.24, 0.89067, 24, -61.21, 84.78, 0.10933, 2, 6, -57.27, 10.47, 0.55733, 24, -55.38, 54.67, 0.44267, 2, 6, -79.13, 8.43, 0.33511, 24, -52.56, 32.9, 0.66489, 2, 6, -95.7, 13.08, 0.224, 24, -56.62, 16.17, 0.776, 2, 6, -103.54, 26.01, 0.224, 24, -69.26, 7.88, 0.776, 2, 6, -102.87, 44.71, 0.224, 24, -87.98, 7.88, 0.776, 2, 6, -93.7, 56.33, 0.224, 24, -99.91, 16.64, 0.776, 2, 6, -74.22, 67.73, 0.33511, 24, -112, 35.7, 0.66489, 2, 6, -49.66, 80.52, 0.55733, 24, -125.64, 59.79, 0.44267, 2, 6, -23.5, 94.03, 0.89067, 24, -140.07, 85.45, 0.10933, 1, 6, 2.09, 99.18, 1, 1, 6, 24.97, 98.37, 1, 1, 6, 22.07, 16.42, 1], "hull": 14, "edges": [0, 26, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26], "width": 74, "height": 117}}, "Vine_1": {"Vine_1": {"type": "mesh", "uvs": [1, 0.24178, 1, 0.50426, 0.92683, 0.74721, 0.88076, 0.92434, 0.79803, 1, 0.38437, 1, 0.2913, 0.91782, 0.17237, 0.72865, 0.0793, 0.49708, 0, 0.27574, 0, 0, 1, 0], "triangles": [6, 7, 2, 3, 6, 2, 5, 6, 3, 4, 5, 3, 2, 7, 1, 10, 11, 0, 9, 10, 0, 8, 9, 0, 8, 0, 1, 7, 8, 1], "vertices": [1, 6, -28.05, -128.82, 1, 2, 6, -60.69, -136.55, 0.66667, 24, 91.68, 56.45, 0.33333, 2, 6, -90.58, -138.37, 0.33333, 24, 94.56, 26.64, 0.66667, 1, 24, 99.66, 5.39, 1, 1, 24, 92.88, -4.45, 1, 1, 24, 58.96, -4.45, 1, 1, 24, 51.32, 6.23, 1, 2, 6, -85.98, -76.63, 0.33333, 24, 32.69, 29.05, 0.66667, 2, 6, -57.09, -61.13, 0.66667, 24, 16.18, 57.38, 0.33333, 1, 6, -29.56, -46.71, 1, 1, 6, 6.26, -47.98, 1, 1, 6, 3.36, -129.93, 1], "hull": 12, "edges": [0, 22, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22], "width": 74, "height": 117}}, "Vine_2": {"Vine_2": {"type": "mesh", "uvs": [1, 0.27278, 0.99694, 0.47569, 0.93308, 0.70948, 0.80742, 0.90137, 0.67869, 1, 0.34768, 1, 0.16992, 0.88593, 0.06571, 0.69625, 0.00306, 0.45768, 0, 0.22866, 0, 0, 1, 0], "triangles": [7, 8, 1, 1, 8, 0, 8, 9, 0, 9, 11, 0, 9, 10, 11, 4, 5, 3, 5, 6, 3, 2, 3, 7, 3, 6, 7, 2, 7, 1], "vertices": [2, 25, 132.84, 79.48, 0.11111, 6, -35.84, -85.56, 0.88889, 2, 25, 118.69, 54.66, 0.33333, 6, -60.14, -70.54, 0.66667, 2, 25, 92.9, 24.98, 0.66667, 6, -88.89, -43.72, 0.33333, 2, 25, 69.32, 1.34, 0.88889, 6, -111.68, -19.32, 0.11111, 1, 25, 52.45, -10.76, 1, 1, 25, 26.96, -10.76, 1, 2, 25, 20.23, 3, 0.88889, 6, -108.29, 29.68, 0.11111, 2, 25, 26.12, 26.4, 0.66667, 6, -85.11, 22.97, 0.33333, 2, 25, 42.16, 56.59, 0.33333, 6, -55.51, 5.87, 0.66667, 2, 25, 55.84, 84.2, 0.11111, 6, -28.4, -8.78, 0.88889, 1, 6, -2.64, -16.65, 1, 1, 6, -5.36, -93.6, 1], "hull": 12, "edges": [0, 22, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22], "width": 69, "height": 96}}}}], "events": {"transform": {}}, "animations": {"enter": {"slots": {"CrownEye": {"attachment": [{"name": "CrownEye_Closed"}, {"time": 1.8667, "name": "CrownEye"}]}, "MASK": {"attachment": [{"name": "MASK"}]}}, "bones": {"CrownEye": {"scale": [{"curve": [0.017, 1, 0.05, 0.815, 0.017, 1, 0.05, 1.097]}, {"time": 0.0667, "x": 0.815, "y": 1.097, "curve": [0.092, 0.815, 0.142, 1, 0.092, 1.097, 0.142, 1]}, {"time": 0.1667, "curve": "stepped"}, {"time": 1.8667, "curve": [1.892, 1, 1.942, 0.815, 1.892, 1, 1.942, 1.097]}, {"time": 1.9667, "x": 0.815, "y": 1.097, "curve": [1.992, 0.815, 2.042, 1, 1.992, 1.097, 2.042, 1]}, {"time": 2.0667}]}, "Head": {"translate": [{"curve": [0.308, 0, 0.925, -32.85, 0.308, 0, 0.925, 0.37]}, {"time": 1.2333, "x": -32.85, "y": 0.37, "curve": [1.342, -32.85, 1.558, 0, 1.342, 0.37, 1.558, 0]}, {"time": 1.6667}]}, "Face": {"translate": [{"x": -63.5, "y": -0.19, "curve": [0.342, -63.5, 1.025, -75.03, 0.342, -0.19, 1.025, 4.97]}, {"time": 1.3667, "x": -75.03, "y": 4.97, "curve": [1.567, -75.03, 1.967, 16.5, 1.567, 4.97, 1.967, -1.64]}, {"time": 2.1667, "x": 16.5, "y": -1.64, "curve": [2.292, 16.5, 2.542, 5.65, 2.292, -1.64, 2.542, -0.19]}, {"time": 2.6667, "x": 5.65, "y": -0.19}]}, "Antler5": {"rotate": [{"value": -8, "curve": [0.486, -11.66, 1, -16.01]}, {"time": 1.5, "value": -20.02, "curve": [1.676, -11.67, 1.878, 0.85]}, {"time": 2, "value": 0.85, "curve": [2.234, -2.69, 2.505, -8]}, {"time": 2.6667, "value": -8}]}, "Antler4": {"rotate": [{"value": -5.59, "curve": [0.508, -9.79, 1.016, -13.99]}, {"time": 1.5, "value": -17.6, "curve": [1.688, -7.16, 1.875, 3.27]}, {"time": 2, "value": 3.27, "curve": [2.25, -1.16, 2.5, -5.59]}, {"time": 2.6667, "value": -5.59}]}, "Antler3": {"rotate": [{"value": -3.17, "curve": [0.519, -7.71, 1.022, -11.82]}, {"time": 1.5, "value": -15.18, "curve": [1.691, -3.26, 1.868, 5.69]}, {"time": 2, "value": 5.69, "curve": [2.255, 0.63, 2.49, -3.17]}, {"time": 2.6667, "value": -3.17}]}, "Antler2": {"rotate": [{"value": -1.08, "curve": [0.518, -5.84, 1.019, -9.89]}, {"time": 1.5, "value": -13.09, "curve": [1.685, -0.05, 1.855, 7.78]}, {"time": 2, "value": 7.78, "curve": [2.247, 2.24, 2.473, -1.08]}, {"time": 2.6667, "value": -1.08}]}, "Antler1": {"rotate": [{"curve": [0.39, 0, 0.927, -5.34]}, {"time": 1.5, "value": -12.01, "curve": [1.625, -12.01, 1.875, 8.86]}, {"time": 2, "value": 8.86, "curve": [2.167, 8.86, 2.5, 0]}, {"time": 2.6667}]}, "Antler10": {"rotate": [{"value": 5.71, "curve": [0.527, 10.59, 1.135, 17.9]}, {"time": 1.5, "value": 17.9, "curve": [1.676, 10.7, 1.878, -0.11]}, {"time": 2, "value": -0.11, "curve": [2.234, 2.22, 2.505, 5.71]}, {"time": 2.6667, "value": 5.71}]}, "Antler9": {"rotate": [{"value": 3.99, "curve": [0.563, 10.08, 1.125, 16.18]}, {"time": 1.5, "value": 16.18, "curve": [1.688, 7.18, 1.875, -1.83]}, {"time": 2, "value": -1.83, "curve": [2.25, 1.08, 2.5, 3.99]}, {"time": 2.6667, "value": 3.99}]}, "Antler8": {"rotate": [{"value": 2.26, "curve": [0.574, 9.23, 1.103, 14.46]}, {"time": 1.5, "value": 14.46, "curve": [1.691, 4.16, 1.868, -3.55]}, {"time": 2, "value": -3.55, "curve": [2.255, -0.23, 2.49, 2.26]}, {"time": 2.6667, "value": 2.26}]}, "Antler7": {"rotate": [{"value": 0.77, "curve": [0.556, 8.39, 1.065, 12.96]}, {"time": 1.5, "value": 12.96, "curve": [1.685, 1.71, 1.855, -5.05]}, {"time": 2, "value": -5.05, "curve": [2.247, -1.41, 2.473, 0.77]}, {"time": 2.6667, "value": 0.77}]}, "Antler6": {"rotate": [{"curve": [0.375, 0, 1.125, 12.19]}, {"time": 1.5, "value": 12.19, "curve": [1.625, 12.19, 1.875, -5.82]}, {"time": 2, "value": -5.82, "curve": [2.167, -5.82, 2.5, 0]}, {"time": 2.6667}]}, "Hands_1": {"rotate": [{"value": -5.24, "curve": [0.581, -20.5, 1.119, -29.14]}, {"time": 1.6, "value": -29.14, "curve": [1.654, -2.34, 1.95, 2.92]}, {"time": 2.0667, "value": 2.92, "curve": [2.217, 2.92, 2.517, -5.24]}, {"time": 2.6667, "value": -5.24}], "translate": [{"time": 1.6, "curve": [1.654, 3.5, 1.95, 4.19, 1.654, 0.27, 1.95, 0.32]}, {"time": 2.0667, "x": 4.19, "y": 0.32, "curve": [2.217, 4.19, 2.517, 0, 2.217, 0.32, 2.517, 0]}, {"time": 2.6667}]}, "Hands_0": {"rotate": [{"value": 1.63, "curve": [0.612, 15.69, 1.18, 26.57]}, {"time": 1.6, "value": 26.57, "curve": [1.654, -0.27, 1.95, -5.53]}, {"time": 2.0667, "value": -5.53, "curve": [2.217, -5.53, 2.517, 1.63]}, {"time": 2.6667, "value": 1.63}], "translate": [{"time": 1.6, "curve": [1.654, 1.62, 1.95, 1.94, 1.654, -0.28, 1.95, -0.34]}, {"time": 2.0667, "x": 1.94, "y": -0.34, "curve": [2.217, 1.94, 2.517, 0, 2.217, -0.34, 2.517, 0]}, {"time": 2.6667}]}, "HairBit0": {"rotate": [{"value": 4.86, "curve": [0.089, 2.27, 0.174, 0]}, {"time": 0.2333, "curve": [0.408, 0, 0.758, 11.97]}, {"time": 0.9333, "value": 11.97, "curve": [1.075, 11.97, 1.358, 0]}, {"time": 1.5, "curve": [1.792, 0, 2.375, 4.86]}, {"time": 2.6667, "value": 4.86}]}, "HairBit1": {"rotate": [{"value": 9.4, "curve": [0.142, 9.4, 0.425, 0]}, {"time": 0.5667, "curve": [0.733, 0, 1.067, 15.87]}, {"time": 1.2333, "value": 15.87, "curve": [1.592, 15.87, 2.308, 9.4]}, {"time": 2.6667, "value": 9.4}]}, "NeckLeaf1": {"rotate": [{"value": -11.45, "curve": [0.167, -7.63, 0.379, 0]}, {"time": 0.5, "curve": [0.683, 0, 1.05, -14.03]}, {"time": 1.2333, "value": -14.03, "curve": [1.592, -14.03, 2.308, -11.45]}, {"time": 2.6667, "value": -11.45}]}, "NeckLeaf2": {"rotate": [{"curve": [0.125, 0, 0.375, -12.24]}, {"time": 0.5, "value": -12.24, "curve": [0.667, -12.24, 1, 11.87]}, {"time": 1.1667, "value": 11.87, "curve": [1.542, 11.87, 2.292, 0]}, {"time": 2.6667}]}, "NeckLeaf5": {"rotate": [{"value": 0.17, "curve": [0.191, 1.02, 0.527, 8.75]}, {"time": 0.7, "value": 8.75, "curve": [0.85, 8.75, 1.15, 0]}, {"time": 1.3, "curve": [1.642, 0, 2.325, 0.17]}, {"time": 2.6667, "value": 0.17}]}, "NeckLeaf3": {"rotate": [{"value": 12.29, "curve": [0.126, 17.11, 0.249, 21.44]}, {"time": 0.3333, "value": 21.44, "curve": [0.483, 21.44, 0.783, 0]}, {"time": 0.9333, "curve": [1.058, 0, 1.308, 21.44]}, {"time": 1.4333, "value": 21.44, "curve": [1.742, 21.44, 2.358, 12.29]}, {"time": 2.6667, "value": 12.29}]}, "NeckLeaf4": {"rotate": [{"value": 8.95, "curve": [0.13, 5.22, 0.277, 0]}, {"time": 0.3667, "curve": [0.492, 0, 0.742, 20.86]}, {"time": 0.8667, "value": 20.86, "curve": [1.033, 20.86, 1.367, -5.18]}, {"time": 1.5333, "value": -5.18, "curve": [1.817, -5.18, 2.383, 8.95]}, {"time": 2.6667, "value": 8.95}]}, "BodyBtm": {"scale": [{"curve": [0.417, 1, 1.25, 0.967, 0.417, 1, 1.25, 1.049]}, {"time": 1.6667, "x": 0.967, "y": 1.049, "curve": [1.738, 1.005, 2.042, 1.015, 1.738, 1.001, 2.042, 0.989]}, {"time": 2.1667, "x": 1.015, "y": 0.989, "curve": [2.292, 1.015, 2.542, 1, 2.292, 0.989, 2.542, 1]}, {"time": 2.6667}]}, "BodyTop": {"scale": [{"x": 0.991, "y": 1.014, "curve": [0.166, 0.996, 0.319, 1, 0.166, 1.006, 0.319, 1]}, {"time": 0.4333, "curve": [0.742, 1, 1.358, 0.967, 0.742, 1, 1.358, 1.049]}, {"time": 1.6667, "x": 0.967, "y": 1.049, "curve": [1.738, 1.005, 2.042, 1.015, 1.738, 1.001, 2.042, 0.989]}, {"time": 2.1667, "x": 1.015, "y": 0.989, "curve": [2.292, 1.015, 2.542, 0.991, 2.292, 0.989, 2.542, 1.014]}, {"time": 2.6667, "x": 0.991, "y": 1.014}]}, "DangleHandle1": {"translate": [{"curve": [0.203, -0.09, 0.397, -0.17, 0.203, -2.37, 0.397, -4.47]}, {"time": 0.5333, "x": -0.17, "y": -4.47, "curve": [1.067, -0.17, 2.133, 0, 1.067, -4.47, 2.133, 0]}, {"time": 2.6667}]}, "DangleHandle2": {"translate": [{"x": 0.1, "y": 2.85, "curve": [0.166, 0.17, 0.32, 0.23, 0.166, 4.82, 0.32, 6.38]}, {"time": 0.4333, "x": 0.23, "y": 6.38, "curve": [0.992, 0.23, 2.108, 0.1, 0.992, 6.38, 2.108, 2.85]}, {"time": 2.6667, "x": 0.1, "y": 2.85}]}, "MAIN": {"translate": [{"y": -822.7}, {"time": 1.6667}], "scale": [{"time": 1.6667, "curve": [1.696, 1.055, 1.942, 1.079, 1.696, 0.935, 1.942, 0.907]}, {"time": 2.0333, "x": 1.079, "y": 0.907, "curve": [2.192, 1.079, 2.508, 1, 2.192, 0.907, 2.508, 1]}, {"time": 2.6667}]}}, "attachments": {"default": {"MASK": {"MASK": {"deform": [{"time": 1.3333}, {"time": 1.6667, "offset": 1, "vertices": [-33.17596, 0, -33.17596]}]}}}}}, "exit": {"slots": {"CrownEye": {"attachment": [{"time": 2.6667, "name": "CrownEye_Closed"}]}, "MASK": {"attachment": [{"time": 0.6667, "name": "MASK"}]}}, "bones": {"CrownEye": {"scale": [{"time": 0.6, "curve": [0.6, 0.954, 0.7, 0.861, 0.6, 1.024, 0.7, 1.073]}, {"time": 0.7, "x": 0.815, "y": 1.097}, {"time": 0.8, "curve": "stepped"}, {"time": 2.5, "curve": [2.5, 0.954, 2.6, 0.861, 2.5, 1.024, 2.6, 1.073]}, {"time": 2.6, "x": 0.815, "y": 1.097, "curve": [2.637, 0.886, 2.667, 0.952, 2.637, 1.06, 2.667, 1.025]}, {"time": 2.6667}]}, "Hands_1": {"rotate": [{"value": -5.24, "curve": [0.117, -5.24, 0.35, -15.57]}, {"time": 0.4667, "value": -15.57, "curve": [0.467, -7.77, 1.531, 7.59]}, {"time": 1.6, "value": 15.89, "curve": [2.303, 8.63, 2.667, 1.59]}, {"time": 2.6667, "value": -5.24}]}, "Antler3": {"rotate": [{"value": -3.17, "curve": [0.167, -3.17, 0.5, 5.69]}, {"time": 0.6667, "value": 5.69, "curve": [0.792, 5.69, 1.042, 0.85]}, {"time": 1.1667, "value": 0.85, "curve": [1.542, 0.85, 2.292, 13.02]}, {"time": 2.6667, "value": 13.02}]}, "Antler5": {"rotate": [{"value": -8, "curve": [0.167, -8, 0.5, 0.85]}, {"time": 0.6667, "value": 0.85, "curve": [0.792, 0.85, 1.042, -3.98]}, {"time": 1.1667, "value": -3.98, "curve": [1.542, -3.98, 2.292, 8.19]}, {"time": 2.6667, "value": 8.19}]}, "Antler4": {"rotate": [{"value": -5.59, "curve": [0.167, -5.59, 0.5, 3.27]}, {"time": 0.6667, "value": 3.27, "curve": [0.792, 3.27, 1.042, -1.57]}, {"time": 1.1667, "value": -1.57, "curve": [1.542, -1.57, 2.292, 10.6]}, {"time": 2.6667, "value": 10.6}]}, "Antler1": {"rotate": [{"curve": [0.167, 0, 0.5, 8.86]}, {"time": 0.6667, "value": 8.86, "curve": [0.792, 8.86, 1.042, 4.02]}, {"time": 1.1667, "value": 4.02, "curve": [1.542, 4.02, 2.292, 16.19]}, {"time": 2.6667, "value": 16.19}]}, "Antler6": {"rotate": [{"curve": [0.167, 0, 0.5, -5.82]}, {"time": 0.6667, "value": -5.82, "curve": [0.792, -5.82, 1.042, 0.16]}, {"time": 1.1667, "value": 0.16, "curve": [1.542, 0.16, 2.292, -14.01]}, {"time": 2.6667, "value": -14.01}]}, "Antler8": {"rotate": [{"value": 2.26, "curve": [0.167, 2.26, 0.5, -3.55]}, {"time": 0.6667, "value": -3.55, "curve": [0.792, -3.55, 1.042, 2.42]}, {"time": 1.1667, "value": 2.42, "curve": [1.542, 2.42, 2.292, -11.75]}, {"time": 2.6667, "value": -11.75}]}, "Antler7": {"rotate": [{"value": 0.77, "curve": [0.167, 0.77, 0.5, -5.05]}, {"time": 0.6667, "value": -5.05, "curve": [0.792, -5.05, 1.042, 0.93]}, {"time": 1.1667, "value": 0.93, "curve": [1.542, 0.93, 2.292, -13.24]}, {"time": 2.6667, "value": -13.24}]}, "Antler10": {"rotate": [{"value": 5.71, "curve": [0.167, 5.71, 0.5, -0.11]}, {"time": 0.6667, "value": -0.11, "curve": [0.792, -0.11, 1.042, 5.87]}, {"time": 1.1667, "value": 5.87, "curve": [1.542, 5.87, 2.292, -8.3]}, {"time": 2.6667, "value": -8.3}]}, "Antler9": {"rotate": [{"value": 3.99, "curve": [0.167, 3.99, 0.5, -1.83]}, {"time": 0.6667, "value": -1.83, "curve": [0.792, -1.83, 1.042, 4.15]}, {"time": 1.1667, "value": 4.15, "curve": [1.542, 4.15, 2.292, -10.03]}, {"time": 2.6667, "value": -10.03}]}, "Antler2": {"rotate": [{"value": -1.08, "curve": [0.167, -1.08, 0.5, 7.78]}, {"time": 0.6667, "value": 7.78, "curve": [0.792, 7.78, 1.042, 2.94]}, {"time": 1.1667, "value": 2.94, "curve": [1.542, 2.94, 2.292, 15.11]}, {"time": 2.6667, "value": 15.11}]}, "Face": {"translate": [{"x": 5.65, "y": -0.19, "curve": [0.125, 5.65, 0.375, 50.49, 0.125, -0.19, 0.375, -1.64]}, {"time": 0.5, "x": 50.49, "y": -1.64, "curve": [0.858, 50.49, 1.091, -75.03, 0.858, -1.64, 1.091, 2.48]}, {"time": 1.9333, "x": -75.03, "y": 2.48}]}, "NeckLeaf4": {"rotate": [{"value": 8.95, "curve": [0.648, 3.55, 1.133, -1.44]}, {"time": 1.1333, "value": -5.18, "curve": [1.55, 4.48, 1.8, 13.3]}, {"time": 1.8, "value": 20.86, "curve": [1.8, 15.65, 2.3, 5.22]}, {"time": 2.3, "curve": [2.3, 2.24, 2.667, 6.71]}, {"time": 2.6667, "value": 8.95}]}, "Hands_0": {"rotate": [{"value": 1.63, "curve": [0.117, 1.63, 0.35, 16.19]}, {"time": 0.4667, "value": 16.19, "curve": [0.815, 7.29, 1.229, -8]}, {"time": 1.6, "value": -19.09, "curve": [2.008, -7.14, 2.383, 1.63]}, {"time": 2.6667, "value": 1.63}]}, "HairBit0": {"rotate": [{"value": 4.86, "curve": [0.583, 3.04, 1.167, 1.21]}, {"time": 1.1667, "curve": [1.45, 4.49, 1.733, 8.98]}, {"time": 1.7333, "value": 11.97, "curve": [1.733, 8.98, 2.433, 2.99]}, {"time": 2.4333}, {"time": 2.6667, "value": 4.86}]}, "HairBit1": {"rotate": [{"value": 9.4, "curve": [0.896, 11.8, 1.433, 13.99]}, {"time": 1.4333, "value": 15.87, "curve": [1.433, 11.9, 2.1, 3.97]}, {"time": 2.1, "curve": [2.1, 2.35, 2.667, 7.05]}, {"time": 2.6667, "value": 9.4}]}, "NeckLeaf1": {"rotate": [{"value": -11.45, "curve": [0, -12.09, 1.433, -13.39]}, {"time": 1.4333, "value": -14.03, "curve": [1.433, -10.53, 2.167, -3.51]}, {"time": 2.1667, "curve": [2.167, -2.86, 2.667, -8.58]}, {"time": 2.6667, "value": -11.45}]}, "NeckLeaf2": {"rotate": [{"curve": [0.8, 4.51, 1.5, 8.84]}, {"time": 1.5, "value": 11.87, "curve": [1.5, 5.85, 2.167, -6.21]}, {"time": 2.1667, "value": -12.24, "curve": [2.167, -9.18, 2.667, -3.06]}, {"time": 2.6667}]}, "NeckLeaf5": {"rotate": [{"value": 0.17, "curve": [0, 0.12, 1.367, 0.04]}, {"time": 1.3667, "curve": [1.367, 2.19, 1.967, 6.56]}, {"time": 1.9667, "value": 8.75, "curve": [1.967, 6.6, 2.667, 2.31]}, {"time": 2.6667, "value": 0.17}]}, "NeckLeaf3": {"rotate": [{"value": 12.29, "curve": [0.514, 15.55, 1.233, 19.21]}, {"time": 1.2333, "value": 21.44, "curve": [1.233, 16.08, 1.733, 5.36]}, {"time": 1.7333, "curve": [1.733, 5.36, 2.333, 16.08]}, {"time": 2.3333, "value": 21.44, "curve": [2.333, 19.15, 2.667, 14.58]}, {"time": 2.6667, "value": 12.29}]}, "BodyBtm": {"scale": [{"curve": [0.125, 1, 0.375, 1.015, 0.125, 1, 0.375, 0.989]}, {"time": 0.5, "x": 1.015, "y": 0.989, "curve": [0.625, 1.015, 0.875, 0.967, 0.625, 0.989, 0.875, 1.049]}, {"time": 1, "x": 0.967, "y": 1.049, "curve": [2.161, 0.97, 2.667, 0.992, 2.161, 1.045, 2.667, 1.012]}, {"time": 2.6667}]}, "BodyTop": {"scale": [{"x": 0.991, "y": 1.014, "curve": [0.125, 0.991, 0.375, 1.015, 0.125, 1.014, 0.375, 0.989]}, {"time": 0.5, "x": 1.015, "y": 0.989, "curve": [0.625, 1.015, 0.875, 0.967, 0.625, 0.989, 0.875, 1.049]}, {"time": 1, "x": 0.967, "y": 1.049, "curve": [1.991, 0.972, 2.233, 0.992, 1.991, 1.042, 2.233, 1.012]}, {"time": 2.2333, "curve": [2.233, 0.998, 2.667, 0.993, 2.233, 1.003, 2.667, 1.01]}, {"time": 2.6667, "x": 0.991, "y": 1.014}]}, "DangleHandle1": {"translate": [{"curve": [0.213, -0.05, 2.133, -0.13, 0.213, -1.22, 2.133, -3.37]}, {"time": 2.1333, "x": -0.17, "y": -4.47, "curve": [2.133, -0.13, 2.667, -0.04, 2.133, -3.35, 2.667, -1.12]}, {"time": 2.6667}]}, "DangleHandle2": {"translate": [{"x": 0.1, "y": 2.85, "curve": [0.744, 0.14, 2.233, 0.2, 0.744, 4.03, 2.233, 5.52]}, {"time": 2.2333, "x": 0.23, "y": 6.38, "curve": [2.233, 0.2, 2.667, 0.13, 2.233, 5.5, 2.667, 3.73]}, {"time": 2.6667, "x": 0.1, "y": 2.85}]}, "Head": {"translate": [{"time": 1, "curve": [1.348, -4.71, 1.433, -24.64, 1.348, 0.05, 1.433, 0.28]}, {"time": 1.4333, "x": -32.85, "y": 0.37, "curve": [1.433, -24.64, 2.667, -8.21, 1.433, 0.28, 2.667, 0.09]}, {"time": 2.6667}]}, "MAIN": {"translate": [{"time": 0.6667, "curve": [1.167, 0, 2.433, 0.31, 1.167, 0, 2.433, -544.74]}, {"time": 2.6667, "x": 0.48, "y": -857.52}], "scale": [{"curve": [0.158, 1, 0.475, 1.079, 0.158, 1, 0.475, 0.907]}, {"time": 0.6333, "x": 1.079, "y": 0.907, "curve": [0.725, 1.079, 0.908, 1, 0.725, 0.907, 0.908, 1]}, {"time": 1}]}}, "attachments": {"default": {"MASK": {"MASK": {"deform": [{"time": 0.6667, "offset": 1, "vertices": [-33.17596, 0, -33.17596]}, {"time": 1.3}]}}}}}, "hidden": {"slots": {"CrownEye": {"attachment": [{"time": 0.7, "name": "CrownEye_Closed"}, {"time": 1.5333, "name": "CrownEye"}]}, "MASK": {"attachment": [{"name": "MASK"}]}}, "bones": {"CrownEye": {"scale": [{"time": 0.7, "curve": [0.725, 1, 0.775, 0.815, 0.725, 1, 0.775, 1.097]}, {"time": 0.8, "x": 0.815, "y": 1.097, "curve": [0.833, 0.815, 0.9, 1, 0.833, 1.097, 0.9, 1]}, {"time": 0.9333, "curve": "stepped"}, {"time": 1.5333, "curve": [1.558, 1, 1.608, 0.815, 1.558, 1, 1.608, 1.097]}, {"time": 1.6333, "x": 0.815, "y": 1.097, "curve": [1.667, 0.815, 1.733, 1, 1.667, 1.097, 1.733, 1]}, {"time": 1.7667}]}, "Head": {"translate": [{"curve": [0.417, 0, 1.25, -32.85, 0.417, 0, 1.25, 0.37]}, {"time": 1.6667, "x": -32.85, "y": 0.37}]}, "Face": {"translate": [{"x": 5.65, "y": -0.19, "curve": [0.217, 10.77, 0.416, 14.55, 0.217, -0.37, 0.416, -0.5]}, {"time": 0.5667, "x": 14.55, "y": -0.5, "curve": [0.967, 14.55, 1.767, -18.38, 0.967, -0.5, 1.767, 0.65]}, {"time": 2.1667, "x": -18.38, "y": 0.65}]}, "Antler5": {"rotate": [{"value": -8, "curve": [0.386, -4.8, 0.832, 0]}, {"time": 1.1}]}, "Antler4": {"rotate": [{"value": -5.59, "curve": [0.313, -2.79, 0.625, 0]}, {"time": 0.8333}]}, "Antler3": {"rotate": [{"value": -3.17, "curve": [0.217, -1.36, 0.417, 0]}, {"time": 0.5667, "curve": [0.983, 0, 1.817, -11.17]}, {"time": 2.2333, "value": -11.17}]}, "Antler2": {"rotate": [{"value": -1.08, "curve": [0.099, -0.4, 0.189, 0]}, {"time": 0.2667, "curve": [0.692, 0, 1.542, -11.17]}, {"time": 1.9667, "value": -11.17}]}, "Antler1": {"rotate": [{"curve": [0.417, 0, 1.25, -11.17]}, {"time": 1.6667, "value": -11.17}]}, "Antler10": {"rotate": [{"value": 5.71, "curve": [0.386, 3.43, 0.832, 0]}, {"time": 1.1}]}, "Antler9": {"rotate": [{"value": 3.99, "curve": [0.313, 1.99, 0.625, 0]}, {"time": 0.8333}]}, "Antler8": {"rotate": [{"value": 2.26, "curve": [0.217, 0.97, 0.417, 0]}, {"time": 0.5667, "curve": [0.983, 0, 1.817, 7.97]}, {"time": 2.2333, "value": 7.97}]}, "Antler7": {"rotate": [{"value": 0.77, "curve": [0.099, 0.29, 0.189, 0]}, {"time": 0.2667, "curve": [0.692, 0, 1.542, 7.97]}, {"time": 1.9667, "value": 7.97}]}, "Antler6": {"rotate": [{"curve": [0.417, 0, 1.25, 7.97]}, {"time": 1.6667, "value": 7.97}]}, "Hands_1": {"rotate": [{"value": -5.24, "curve": [0.036, -5.45, 0.07, -5.57]}, {"time": 0.1, "value": -5.57, "curve": [0.308, -5.57, 0.725, 4.08]}, {"time": 0.9333, "value": 4.08, "curve": [1.133, 4.08, 1.533, -11.11]}, {"time": 1.7333, "value": -11.11}]}, "Hands_0": {"rotate": [{"value": 1.63, "curve": [0.127, -0.05, 0.246, -1.34]}, {"time": 0.3333, "value": -1.34, "curve": [0.483, -1.34, 0.783, 5.53]}, {"time": 0.9333, "value": 5.53, "curve": [1.133, 5.53, 1.533, 2.01]}, {"time": 1.7333, "value": 2.01}]}, "HairBit0": {"rotate": [{"value": 4.86, "curve": [0.127, 2.27, 0.248, 0]}, {"time": 0.3333, "curve": [0.558, 0, 1.008, 11.97]}, {"time": 1.2333, "value": 11.97, "curve": [1.425, 11.97, 1.808, 0]}, {"time": 2}]}, "HairBit1": {"rotate": [{"value": 9.4, "curve": [0.192, 9.4, 0.575, 0]}, {"time": 0.7667, "curve": [0.992, 0, 1.442, 15.87]}, {"time": 1.6667, "value": 15.87}]}, "NeckLeaf1": {"rotate": [{"value": -11.45, "curve": [0.222, -7.63, 0.505, 0]}, {"time": 0.6667, "curve": [0.917, 0, 1.417, -14.03]}, {"time": 1.6667, "value": -14.03}]}, "NeckLeaf2": {"rotate": [{"curve": [0.167, 0, 0.5, -12.24]}, {"time": 0.6667, "value": -12.24, "curve": [0.892, -12.24, 1.342, 11.87]}, {"time": 1.5667, "value": 11.87}]}, "NeckLeaf5": {"rotate": [{"value": 0.17, "curve": [0.255, 1.02, 0.703, 8.75]}, {"time": 0.9333, "value": 8.75, "curve": [1.133, 8.75, 1.533, 0]}, {"time": 1.7333}]}, "NeckLeaf3": {"rotate": [{"value": 12.29, "curve": [0.164, 17.11, 0.323, 21.44]}, {"time": 0.4333, "value": 21.44, "curve": [0.633, 21.44, 1.033, 0]}, {"time": 1.2333, "curve": [1.4, 0, 1.733, 21.44]}, {"time": 1.9, "value": 21.44}]}, "NeckLeaf4": {"rotate": [{"value": 8.95, "curve": [0.178, 5.22, 0.378, 0]}, {"time": 0.5, "curve": [0.667, 0, 1, 20.86]}, {"time": 1.1667, "value": 20.86, "curve": [1.392, 20.86, 1.842, -5.18]}, {"time": 2.0667, "value": -5.18}]}, "BodyBtm": {"scale": [{"curve": [0.417, 1, 1.25, 0.967, 0.417, 1, 1.25, 1.049]}, {"time": 1.6667, "x": 0.967, "y": 1.049}]}, "BodyTop": {"scale": [{"x": 0.991, "y": 1.014, "curve": [0.217, 0.996, 0.417, 1, 0.217, 1.006, 0.417, 1]}, {"time": 0.5667, "curve": [0.983, 1, 1.817, 0.967, 0.983, 1, 1.817, 1.049]}, {"time": 2.2333, "x": 0.967, "y": 1.049}]}, "DangleHandle1": {"translate": [{"curve": [0.279, -0.09, 0.546, -0.17, 0.279, -2.37, 0.546, -4.47]}, {"time": 0.7333, "x": -0.17, "y": -4.47}]}, "DangleHandle2": {"translate": [{"x": 0.1, "y": 2.85, "curve": [0.229, 0.17, 0.443, 0.23, 0.229, 4.82, 0.443, 6.38]}, {"time": 0.6, "x": 0.23, "y": 6.38}]}, "MAIN": {"translate": [{"y": -822.7}]}}}, "idle": {"slots": {"CrownEye": {"attachment": [{"time": 0.7, "name": "CrownEye_Closed"}, {"time": 1.5333, "name": "CrownEye"}]}}, "bones": {"CrownEye": {"scale": [{"time": 0.7, "curve": [0.725, 1, 0.775, 0.815, 0.725, 1, 0.775, 1.097]}, {"time": 0.8, "x": 0.815, "y": 1.097, "curve": [0.833, 0.815, 0.9, 1, 0.833, 1.097, 0.9, 1]}, {"time": 0.9333, "curve": "stepped"}, {"time": 1.5333, "curve": [1.558, 1, 1.608, 0.815, 1.558, 1, 1.608, 1.097]}, {"time": 1.6333, "x": 0.815, "y": 1.097, "curve": [1.667, 0.815, 1.733, 1, 1.667, 1.097, 1.733, 1]}, {"time": 1.7667}]}, "Head": {"rotate": [{"time": 2.4, "curve": [2.425, 0, 2.475, -4.18]}, {"time": 2.5, "value": -4.18, "curve": [2.519, -4.18, 2.542, -4]}, {"time": 2.5667, "value": -3.71, "curve": [2.598, -4.71, 2.642, -7.44]}, {"time": 2.6667, "value": -7.44, "curve": "stepped"}, {"time": 4.6333, "value": -7.44, "curve": [5.203, -7.39, 5.433, 0]}, {"time": 5.7}], "translate": [{"curve": [0.417, 0, 1.25, -32.85, 0.417, 0, 1.25, 0.37]}, {"time": 1.6667, "x": -32.85, "y": 0.37, "curve": [2.083, -32.85, 2.917, 0, 2.083, 0.37, 2.917, 0]}, {"time": 3.3333, "curve": [3.725, 0, 4.508, -32.85, 3.725, 0, 4.508, 0.37]}, {"time": 4.9, "x": -32.85, "y": 0.37, "curve": [5.342, -32.85, 6.225, 0, 5.342, 0.37, 6.225, 0]}, {"time": 6.6667, "curve": [7.083, 0, 7.917, -32.85, 7.083, 0, 7.917, 0.37]}, {"time": 8.3333, "x": -32.85, "y": 0.37, "curve": [8.75, -32.85, 9.583, 0, 8.75, 0.37, 9.583, 0]}, {"time": 10}], "scale": [{"time": 2.4, "curve": [2.425, 1, 2.475, 0.965, 2.425, 1, 2.475, 1.039]}, {"time": 2.5, "x": 0.965, "y": 1.039, "curve": [2.517, 0.965, 2.55, 1, 2.517, 1.039, 2.55, 1]}, {"time": 2.5667, "curve": [2.592, 1, 2.642, 0.965, 2.592, 1, 2.642, 1.039]}, {"time": 2.6667, "x": 0.965, "y": 1.039, "curve": [2.692, 0.965, 2.742, 1, 2.692, 1.039, 2.742, 1]}, {"time": 2.7667}]}, "Face": {"translate": [{"x": 5.65, "y": -0.19, "curve": [0.217, 10.77, 0.416, 14.55, 0.217, -0.37, 0.416, -0.5]}, {"time": 0.5667, "x": 14.55, "y": -0.5, "curve": [0.967, 14.55, 1.767, -18.38, 0.967, -0.5, 1.767, 0.65]}, {"time": 2.1667, "x": -18.38, "y": 0.65, "curve": [2.45, -18.38, 2.926, -3.78, 2.45, 0.65, 2.926, 0.14]}, {"time": 3.3333, "x": 5.65, "y": -0.19, "curve": [3.55, 10.77, 3.749, 14.55, 3.55, -0.37, 3.749, -0.5]}, {"time": 3.9, "x": 14.55, "y": -0.5, "curve": [4.3, 14.55, 5.1, -18.38, 4.3, -0.5, 5.1, 0.65]}, {"time": 5.5, "x": -18.38, "y": 0.65, "curve": [5.784, -18.38, 6.259, -3.78, 5.784, 0.65, 6.259, 0.14]}, {"time": 6.6667, "x": 5.65, "y": -0.19, "curve": [6.883, 10.77, 7.083, 14.55, 6.883, -0.37, 7.083, -0.5]}, {"time": 7.2333, "x": 14.55, "y": -0.5, "curve": [7.633, 14.55, 8.433, -18.38, 7.633, -0.5, 8.433, 0.65]}, {"time": 8.8333, "x": -18.38, "y": 0.65, "curve": [9.117, -18.38, 9.593, -3.78, 9.117, 0.65, 9.593, 0.14]}, {"time": 10, "x": 5.65, "y": -0.19}]}, "Antler5": {"rotate": [{"value": -8, "curve": [0.386, -4.8, 0.832, 0]}, {"time": 1.1, "curve": [1.525, 0, 2.375, -11.17]}, {"time": 2.8, "value": -11.17, "curve": [2.941, -11.17, 3.129, -9.82]}, {"time": 3.3333, "value": -8, "curve": [3.72, -4.8, 4.166, 0]}, {"time": 4.4333, "curve": [4.858, 0, 5.708, -11.17]}, {"time": 6.1333, "value": -11.17, "curve": [6.275, -11.17, 6.463, -9.82]}, {"time": 6.6667, "value": -8, "curve": [7.053, -4.8, 7.499, 0]}, {"time": 7.7667, "curve": [8.192, 0, 9.042, -11.17]}, {"time": 9.4667, "value": -11.17, "curve": [9.608, -11.17, 9.796, -9.82]}, {"time": 10, "value": -8}]}, "Antler4": {"rotate": [{"value": -5.59, "curve": [0.313, -2.79, 0.625, 0]}, {"time": 0.8333, "curve": [1.25, 0, 2.083, -11.17]}, {"time": 2.5, "value": -11.17, "curve": [2.708, -11.17, 3.021, -8.38]}, {"time": 3.3333, "value": -5.59, "curve": [3.646, -2.79, 3.958, 0]}, {"time": 4.1667, "curve": [4.583, 0, 5.417, -11.17]}, {"time": 5.8333, "value": -11.17, "curve": [6.042, -11.17, 6.354, -8.38]}, {"time": 6.6667, "value": -5.59, "curve": [6.979, -2.79, 7.292, 0]}, {"time": 7.5, "curve": [7.917, 0, 8.75, -11.17]}, {"time": 9.1667, "value": -11.17, "curve": [9.375, -11.17, 9.688, -8.38]}, {"time": 10, "value": -5.59}]}, "Antler3": {"rotate": [{"value": -3.17, "curve": [0.217, -1.36, 0.417, 0]}, {"time": 0.5667, "curve": [0.983, 0, 1.817, -11.17]}, {"time": 2.2333, "value": -11.17, "curve": [2.501, -11.17, 2.947, -6.37]}, {"time": 3.3333, "value": -3.17, "curve": [3.55, -1.36, 3.75, 0]}, {"time": 3.9, "curve": [4.317, 0, 5.15, -11.17]}, {"time": 5.5667, "value": -11.17, "curve": [5.834, -11.17, 6.28, -6.37]}, {"time": 6.6667, "value": -3.17, "curve": [6.883, -1.36, 7.083, 0]}, {"time": 7.2333, "curve": [7.65, 0, 8.483, -11.17]}, {"time": 8.9, "value": -11.17, "curve": [9.168, -11.17, 9.614, -6.37]}, {"time": 10, "value": -3.17}]}, "Antler2": {"rotate": [{"value": -1.08, "curve": [0.099, -0.4, 0.189, 0]}, {"time": 0.2667, "curve": [0.692, 0, 1.542, -11.17]}, {"time": 1.9667, "value": -11.17, "curve": [2.299, -11.17, 2.909, -3.6]}, {"time": 3.3333, "value": -1.08, "curve": [3.432, -0.4, 3.523, 0]}, {"time": 3.6, "curve": [4.025, 0, 4.875, -11.17]}, {"time": 5.3, "value": -11.17, "curve": [5.632, -11.17, 6.242, -3.6]}, {"time": 6.6667, "value": -1.08, "curve": [6.766, -0.4, 6.856, 0]}, {"time": 6.9333, "curve": [7.358, 0, 8.208, -11.17]}, {"time": 8.6333, "value": -11.17, "curve": [8.966, -11.17, 9.575, -3.6]}, {"time": 10, "value": -1.08}]}, "Antler1": {"rotate": [{"curve": [0.417, 0, 1.25, -11.17]}, {"time": 1.6667, "value": -11.17, "curve": [2.083, -11.17, 2.917, 0]}, {"time": 3.3333, "curve": [3.75, 0, 4.583, -11.17]}, {"time": 5, "value": -11.17, "curve": [5.417, -11.17, 6.25, 0]}, {"time": 6.6667, "curve": [7.083, 0, 7.917, -11.17]}, {"time": 8.3333, "value": -11.17, "curve": [8.75, -11.17, 9.583, 0]}, {"time": 10}]}, "Antler10": {"rotate": [{"value": 5.71, "curve": [0.386, 3.43, 0.832, 0]}, {"time": 1.1, "curve": [1.525, 0, 2.375, 7.97]}, {"time": 2.8, "value": 7.97, "curve": [2.941, 7.97, 3.129, 7]}, {"time": 3.3333, "value": 5.71, "curve": [3.72, 3.43, 4.166, 0]}, {"time": 4.4333, "curve": [4.858, 0, 5.708, 7.97]}, {"time": 6.1333, "value": 7.97, "curve": [6.275, 7.97, 6.463, 7]}, {"time": 6.6667, "value": 5.71, "curve": [7.053, 3.43, 7.499, 0]}, {"time": 7.7667, "curve": [8.192, 0, 9.042, 7.97]}, {"time": 9.4667, "value": 7.97, "curve": [9.608, 7.97, 9.796, 7]}, {"time": 10, "value": 5.71}]}, "Antler9": {"rotate": [{"value": 3.99, "curve": [0.313, 1.99, 0.625, 0]}, {"time": 0.8333, "curve": [1.25, 0, 2.083, 7.97]}, {"time": 2.5, "value": 7.97, "curve": [2.708, 7.97, 3.021, 5.98]}, {"time": 3.3333, "value": 3.99, "curve": [3.646, 1.99, 3.958, 0]}, {"time": 4.1667, "curve": [4.583, 0, 5.417, 7.97]}, {"time": 5.8333, "value": 7.97, "curve": [6.042, 7.97, 6.354, 5.98]}, {"time": 6.6667, "value": 3.99, "curve": [6.979, 1.99, 7.292, 0]}, {"time": 7.5, "curve": [7.917, 0, 8.75, 7.97]}, {"time": 9.1667, "value": 7.97, "curve": [9.375, 7.97, 9.688, 5.98]}, {"time": 10, "value": 3.99}]}, "Antler8": {"rotate": [{"value": 2.26, "curve": [0.217, 0.97, 0.417, 0]}, {"time": 0.5667, "curve": [0.983, 0, 1.817, 7.97]}, {"time": 2.2333, "value": 7.97, "curve": [2.501, 7.97, 2.947, 4.55]}, {"time": 3.3333, "value": 2.26, "curve": [3.55, 0.97, 3.75, 0]}, {"time": 3.9, "curve": [4.317, 0, 5.15, 7.97]}, {"time": 5.5667, "value": 7.97, "curve": [5.834, 7.97, 6.28, 4.55]}, {"time": 6.6667, "value": 2.26, "curve": [6.883, 0.97, 7.083, 0]}, {"time": 7.2333, "curve": [7.65, 0, 8.483, 7.97]}, {"time": 8.9, "value": 7.97, "curve": [9.168, 7.97, 9.614, 4.55]}, {"time": 10, "value": 2.26}]}, "Antler7": {"rotate": [{"value": 0.77, "curve": [0.099, 0.29, 0.189, 0]}, {"time": 0.2667, "curve": [0.692, 0, 1.542, 7.97]}, {"time": 1.9667, "value": 7.97, "curve": [2.299, 7.97, 2.909, 2.57]}, {"time": 3.3333, "value": 0.77, "curve": [3.432, 0.29, 3.523, 0]}, {"time": 3.6, "curve": [4.025, 0, 4.875, 7.97]}, {"time": 5.3, "value": 7.97, "curve": [5.632, 7.97, 6.242, 2.57]}, {"time": 6.6667, "value": 0.77, "curve": [6.766, 0.29, 6.856, 0]}, {"time": 6.9333, "curve": [7.358, 0, 8.208, 7.97]}, {"time": 8.6333, "value": 7.97, "curve": [8.966, 7.97, 9.575, 2.57]}, {"time": 10, "value": 0.77}]}, "Antler6": {"rotate": [{"curve": [0.417, 0, 1.25, 7.97]}, {"time": 1.6667, "value": 7.97, "curve": [2.083, 7.97, 2.917, 0]}, {"time": 3.3333, "curve": [3.75, 0, 4.583, 7.97]}, {"time": 5, "value": 7.97, "curve": [5.417, 7.97, 6.25, 0]}, {"time": 6.6667, "curve": [7.083, 0, 7.917, 7.97]}, {"time": 8.3333, "value": 7.97, "curve": [8.75, 7.97, 9.583, 0]}, {"time": 10}]}, "Hands_1": {"rotate": [{"value": -5.24, "curve": [0.036, -5.45, 0.07, -5.57]}, {"time": 0.1, "value": -5.57, "curve": [0.308, -5.57, 0.725, 4.08]}, {"time": 0.9333, "value": 4.08, "curve": [1.133, 4.08, 1.533, -11.11]}, {"time": 1.7333, "value": -11.11, "curve": [1.925, -11.11, 2.308, 0.31]}, {"time": 2.5, "value": 0.31, "curve": [2.704, 0.31, 3.087, -4.18]}, {"time": 3.3333, "value": -5.24, "curve": [3.37, -5.45, 3.403, -5.57]}, {"time": 3.4333, "value": -5.57, "curve": [3.642, -5.57, 4.058, 10.37]}, {"time": 4.2667, "value": 10.37, "curve": [4.467, 10.37, 4.867, -2.25]}, {"time": 5.0667, "value": -2.25, "curve": [5.258, -2.25, 5.642, 0.31]}, {"time": 5.8333, "value": 0.31, "curve": [6.037, 0.31, 6.421, -4.18]}, {"time": 6.6667, "value": -5.24, "curve": [6.703, -5.45, 6.737, -5.57]}, {"time": 6.7667, "value": -5.57, "curve": [6.975, -5.57, 7.392, 4.08]}, {"time": 7.6, "value": 4.08, "curve": [7.8, 4.08, 8.2, -11.11]}, {"time": 8.4, "value": -11.11, "curve": [8.592, -11.11, 8.975, 0.31]}, {"time": 9.1667, "value": 0.31, "curve": [9.37, 0.31, 9.754, -4.18]}, {"time": 10, "value": -5.24}]}, "Hands_0": {"rotate": [{"value": 1.63, "curve": [0.127, -0.05, 0.246, -1.34]}, {"time": 0.3333, "value": -1.34, "curve": [0.483, -1.34, 0.783, 5.53]}, {"time": 0.9333, "value": 5.53, "curve": [1.133, 5.53, 1.533, 2.01]}, {"time": 1.7333, "value": 2.01, "curve": [1.983, 2.01, 2.483, 8.29]}, {"time": 2.7333, "value": 8.29, "curve": [2.88, 8.29, 3.12, 4.39]}, {"time": 3.3333, "value": 1.63, "curve": [3.461, -0.05, 3.579, -1.34]}, {"time": 3.6667, "value": -1.34, "curve": [3.817, -1.34, 4.117, 5.53]}, {"time": 4.2667, "value": 5.53, "curve": [4.467, 5.53, 4.867, 2.01]}, {"time": 5.0667, "value": 2.01, "curve": [5.317, 2.01, 5.817, 8.29]}, {"time": 6.0667, "value": 8.29, "curve": [6.213, 8.29, 6.454, 4.39]}, {"time": 6.6667, "value": 1.63, "curve": [6.794, -0.05, 6.913, -1.34]}, {"time": 7, "value": -1.34, "curve": [7.15, -1.34, 7.45, 5.53]}, {"time": 7.6, "value": 5.53, "curve": [7.8, 5.53, 8.2, 2.01]}, {"time": 8.4, "value": 2.01, "curve": [8.65, 2.01, 9.15, 8.29]}, {"time": 9.4, "value": 8.29, "curve": [9.546, 8.29, 9.787, 4.39]}, {"time": 10, "value": 1.63}]}, "HairBit0": {"rotate": [{"value": 4.86, "curve": [0.127, 2.27, 0.248, 0]}, {"time": 0.3333, "curve": [0.558, 0, 1.008, 11.97]}, {"time": 1.2333, "value": 11.97, "curve": [1.425, 11.97, 1.808, 0]}, {"time": 2, "curve": [2.225, 0, 2.675, 11.97]}, {"time": 2.9, "value": 11.97, "curve": [3.007, 11.97, 3.174, 8.14]}, {"time": 3.3333, "value": 4.86, "curve": [3.46, 2.27, 3.582, 0]}, {"time": 3.6667, "curve": [3.892, 0, 4.342, 11.97]}, {"time": 4.5667, "value": 11.97, "curve": [4.758, 11.97, 5.142, 0]}, {"time": 5.3333, "curve": [5.558, 0, 6.008, 11.97]}, {"time": 6.2333, "value": 11.97, "curve": [6.34, 11.97, 6.508, 8.14]}, {"time": 6.6667, "value": 4.86, "curve": [6.793, 2.27, 6.915, 0]}, {"time": 7, "curve": [7.225, 0, 7.675, 11.97]}, {"time": 7.9, "value": 11.97, "curve": [8.092, 11.97, 8.475, 0]}, {"time": 8.6667, "curve": [8.892, 0, 9.342, 11.97]}, {"time": 9.5667, "value": 11.97, "curve": [9.673, 11.97, 9.841, 8.14]}, {"time": 10, "value": 4.86}]}, "HairBit1": {"rotate": [{"value": 9.4, "curve": [0.192, 9.4, 0.575, 0]}, {"time": 0.7667, "curve": [0.992, 0, 1.442, 15.87]}, {"time": 1.6667, "value": 15.87, "curve": [1.867, 15.87, 2.267, -6.87]}, {"time": 2.4667, "value": -6.87, "curve": [2.683, -6.87, 3.117, 9.4]}, {"time": 3.3333, "value": 9.4, "curve": [3.525, 9.4, 3.908, 0]}, {"time": 4.1, "curve": [4.325, 0, 4.775, 15.87]}, {"time": 5, "value": 15.87, "curve": [5.2, 15.87, 5.6, -6.87]}, {"time": 5.8, "value": -6.87, "curve": [6.017, -6.87, 6.45, 9.4]}, {"time": 6.6667, "value": 9.4, "curve": [6.858, 9.4, 7.242, 0]}, {"time": 7.4333, "curve": [7.658, 0, 8.108, 15.87]}, {"time": 8.3333, "value": 15.87, "curve": [8.533, 15.87, 8.933, -6.87]}, {"time": 9.1333, "value": -6.87, "curve": [9.35, -6.87, 9.783, 9.4]}, {"time": 10, "value": 9.4}]}, "NeckLeaf1": {"rotate": [{"value": -11.45, "curve": [0.222, -7.63, 0.505, 0]}, {"time": 0.6667, "curve": [0.917, 0, 1.417, -14.03]}, {"time": 1.6667, "value": -14.03, "curve": [1.867, -14.03, 2.267, 0]}, {"time": 2.4667, "curve": [2.633, 0, 2.967, -14.03]}, {"time": 3.1333, "value": -14.03, "curve": [3.189, -14.03, 3.257, -13]}, {"time": 3.3333, "value": -11.45, "curve": [3.556, -7.63, 3.838, 0]}, {"time": 4, "curve": [4.25, 0, 4.75, -14.03]}, {"time": 5, "value": -14.03, "curve": [5.2, -14.03, 5.6, 0]}, {"time": 5.8, "curve": [5.967, 0, 6.3, -14.03]}, {"time": 6.4667, "value": -14.03, "curve": [6.522, -14.03, 6.591, -13]}, {"time": 6.6667, "value": -11.45, "curve": [6.889, -7.63, 7.172, 0]}, {"time": 7.3333, "curve": [7.583, 0, 8.083, -14.03]}, {"time": 8.3333, "value": -14.03, "curve": [8.533, -14.03, 8.933, 0]}, {"time": 9.1333, "curve": [9.3, 0, 9.633, -14.03]}, {"time": 9.8, "value": -14.03, "curve": [9.855, -14.03, 9.924, -13]}, {"time": 10, "value": -11.45}]}, "NeckLeaf2": {"rotate": [{"curve": [0.167, 0, 0.5, -12.24]}, {"time": 0.6667, "value": -12.24, "curve": [0.892, -12.24, 1.342, 11.87]}, {"time": 1.5667, "value": 11.87, "curve": [1.817, 11.87, 2.317, -12.24]}, {"time": 2.5667, "value": -12.24, "curve": [2.758, -12.24, 3.142, 0]}, {"time": 3.3333, "curve": [3.5, 0, 3.833, -12.24]}, {"time": 4, "value": -12.24, "curve": [4.225, -12.24, 4.675, 11.87]}, {"time": 4.9, "value": 11.87, "curve": [5.15, 11.87, 5.65, -12.24]}, {"time": 5.9, "value": -12.24, "curve": [6.092, -12.24, 6.475, 0]}, {"time": 6.6667, "curve": [6.833, 0, 7.167, -12.24]}, {"time": 7.3333, "value": -12.24, "curve": [7.558, -12.24, 8.008, 11.87]}, {"time": 8.2333, "value": 11.87, "curve": [8.483, 11.87, 8.983, -12.24]}, {"time": 9.2333, "value": -12.24, "curve": [9.425, -12.24, 9.808, 0]}, {"time": 10}]}, "NeckLeaf5": {"rotate": [{"value": 0.17, "curve": [0.255, 1.02, 0.703, 8.75]}, {"time": 0.9333, "value": 8.75, "curve": [1.133, 8.75, 1.533, 0]}, {"time": 1.7333, "curve": [1.9, 0, 2.233, 8.75]}, {"time": 2.4, "value": 8.75, "curve": [2.625, 8.75, 3.075, 0]}, {"time": 3.3, "curve": [3.311, 0, 3.322, 0.06]}, {"time": 3.3333, "value": 0.17, "curve": [3.588, 1.02, 4.036, 8.75]}, {"time": 4.2667, "value": 8.75, "curve": [4.467, 8.75, 4.867, 0]}, {"time": 5.0667, "curve": [5.233, 0, 5.567, 8.75]}, {"time": 5.7333, "value": 8.75, "curve": [5.958, 8.75, 6.408, 0]}, {"time": 6.6333, "curve": [6.644, 0, 6.655, 0.06]}, {"time": 6.6667, "value": 0.17, "curve": [6.921, 1.02, 7.37, 8.75]}, {"time": 7.6, "value": 8.75, "curve": [7.8, 8.75, 8.2, 0]}, {"time": 8.4, "curve": [8.567, 0, 8.9, 8.75]}, {"time": 9.0667, "value": 8.75, "curve": [9.292, 8.75, 9.742, 0]}, {"time": 9.9667, "curve": [9.977, 0, 9.988, 0.06]}, {"time": 10, "value": 0.17}]}, "NeckLeaf3": {"rotate": [{"value": 12.29, "curve": [0.164, 17.11, 0.323, 21.44]}, {"time": 0.4333, "value": 21.44, "curve": [0.633, 21.44, 1.033, 0]}, {"time": 1.2333, "curve": [1.4, 0, 1.733, 21.44]}, {"time": 1.9, "value": 21.44, "curve": [2.125, 21.44, 2.575, 0]}, {"time": 2.8, "curve": [2.932, 0, 3.137, 6.51]}, {"time": 3.3333, "value": 12.29, "curve": [3.498, 17.11, 3.657, 21.44]}, {"time": 3.7667, "value": 21.44, "curve": [3.967, 21.44, 4.367, 0]}, {"time": 4.5667, "curve": [4.733, 0, 5.067, 21.44]}, {"time": 5.2333, "value": 21.44, "curve": [5.458, 21.44, 5.908, 0]}, {"time": 6.1333, "curve": [6.265, 0, 6.47, 6.51]}, {"time": 6.6667, "value": 12.29, "curve": [6.831, 17.11, 6.99, 21.44]}, {"time": 7.1, "value": 21.44, "curve": [7.3, 21.44, 7.7, 0]}, {"time": 7.9, "curve": [8.067, 0, 8.4, 21.44]}, {"time": 8.5667, "value": 21.44, "curve": [8.792, 21.44, 9.242, 0]}, {"time": 9.4667, "curve": [9.598, 0, 9.803, 6.51]}, {"time": 10, "value": 12.29}]}, "NeckLeaf4": {"rotate": [{"value": 8.95, "curve": [0.178, 5.22, 0.378, 0]}, {"time": 0.5, "curve": [0.667, 0, 1, 20.86]}, {"time": 1.1667, "value": 20.86, "curve": [1.392, 20.86, 1.842, -5.18]}, {"time": 2.0667, "value": -5.18, "curve": [2.317, -5.18, 2.817, 13.04]}, {"time": 3.0667, "value": 13.04, "curve": [3.137, 13.04, 3.231, 11.25]}, {"time": 3.3333, "value": 8.95, "curve": [3.511, 5.22, 3.711, 0]}, {"time": 3.8333, "curve": [4, 0, 4.333, 20.86]}, {"time": 4.5, "value": 20.86, "curve": [4.725, 20.86, 5.175, -5.18]}, {"time": 5.4, "value": -5.18, "curve": [5.65, -5.18, 6.15, 13.04]}, {"time": 6.4, "value": 13.04, "curve": [6.47, 13.04, 6.565, 11.25]}, {"time": 6.6667, "value": 8.95, "curve": [6.845, 5.22, 7.045, 0]}, {"time": 7.1667, "curve": [7.333, 0, 7.667, 20.86]}, {"time": 7.8333, "value": 20.86, "curve": [8.058, 20.86, 8.508, -5.18]}, {"time": 8.7333, "value": -5.18, "curve": [8.983, -5.18, 9.483, 13.04]}, {"time": 9.7333, "value": 13.04, "curve": [9.803, 13.04, 9.898, 11.25]}, {"time": 10, "value": 8.95}]}, "BodyBtm": {"scale": [{"curve": [0.417, 1, 1.25, 0.967, 0.417, 1, 1.25, 1.049]}, {"time": 1.6667, "x": 0.967, "y": 1.049, "curve": [2.083, 0.967, 2.917, 1, 2.083, 1.049, 2.917, 1]}, {"time": 3.3333, "curve": [3.75, 1, 4.583, 0.967, 3.75, 1, 4.583, 1.049]}, {"time": 5, "x": 0.967, "y": 1.049, "curve": [5.417, 0.967, 6.25, 1, 5.417, 1.049, 6.25, 1]}, {"time": 6.6667, "curve": [7.083, 1, 7.917, 0.967, 7.083, 1, 7.917, 1.049]}, {"time": 8.3333, "x": 0.967, "y": 1.049, "curve": [8.75, 0.967, 9.583, 1, 8.75, 1.049, 9.583, 1]}, {"time": 10}]}, "BodyTop": {"scale": [{"x": 0.991, "y": 1.014, "curve": [0.217, 0.996, 0.417, 1, 0.217, 1.006, 0.417, 1]}, {"time": 0.5667, "curve": [0.983, 1, 1.817, 0.967, 0.983, 1, 1.817, 1.049]}, {"time": 2.2333, "x": 0.967, "y": 1.049, "curve": [2.501, 0.967, 2.947, 0.981, 2.501, 1.049, 2.947, 1.028]}, {"time": 3.3333, "x": 0.991, "y": 1.014, "curve": [3.55, 0.996, 3.75, 1, 3.55, 1.006, 3.75, 1]}, {"time": 3.9, "curve": [4.317, 1, 5.15, 0.967, 4.317, 1, 5.15, 1.049]}, {"time": 5.5667, "x": 0.967, "y": 1.049, "curve": [5.834, 0.967, 6.28, 0.981, 5.834, 1.049, 6.28, 1.028]}, {"time": 6.6667, "x": 0.991, "y": 1.014, "curve": [6.883, 0.996, 7.083, 1, 6.883, 1.006, 7.083, 1]}, {"time": 7.2333, "curve": [7.65, 1, 8.483, 0.967, 7.65, 1, 8.483, 1.049]}, {"time": 8.9, "x": 0.967, "y": 1.049, "curve": [9.168, 0.967, 9.614, 0.981, 9.168, 1.049, 9.614, 1.028]}, {"time": 10, "x": 0.991, "y": 1.014}]}, "DangleHandle1": {"translate": [{"curve": [0.279, -0.09, 0.546, -0.17, 0.279, -2.37, 0.546, -4.47]}, {"time": 0.7333, "x": -0.17, "y": -4.47, "curve": [1.15, -0.17, 1.983, 0.23, 1.15, -4.47, 1.983, 6.38]}, {"time": 2.4, "x": 0.23, "y": 6.38, "curve": [2.63, 0.23, 2.99, 0.1, 2.63, 6.38, 2.99, 2.96]}, {"time": 3.3333, "curve": [3.612, -0.09, 3.88, -0.17, 3.612, -2.37, 3.88, -4.47]}, {"time": 4.0667, "x": -0.17, "y": -4.47, "curve": [4.483, -0.17, 5.317, 0.23, 4.483, -4.47, 5.317, 6.38]}, {"time": 5.7333, "x": 0.23, "y": 6.38, "curve": [5.963, 0.23, 6.324, 0.1, 5.963, 6.38, 6.324, 2.96]}, {"time": 6.6667, "curve": [6.945, -0.09, 7.213, -0.17, 6.945, -2.37, 7.213, -4.47]}, {"time": 7.4, "x": -0.17, "y": -4.47, "curve": [7.817, -0.17, 8.65, 0.23, 7.817, -4.47, 8.65, 6.38]}, {"time": 9.0667, "x": 0.23, "y": 6.38, "curve": [9.297, 0.23, 9.657, 0.1, 9.297, 6.38, 9.657, 2.96]}, {"time": 10}]}, "DangleHandle2": {"translate": [{"x": 0.1, "y": 2.85, "curve": [0.229, 0.17, 0.443, 0.23, 0.229, 4.82, 0.443, 6.38]}, {"time": 0.6, "x": 0.23, "y": 6.38, "curve": [1.025, 0.23, 1.875, -0.17, 1.025, 6.38, 1.875, -4.47]}, {"time": 2.3, "x": -0.17, "y": -4.47, "curve": [2.552, -0.17, 2.964, -0.01, 2.552, -4.47, 2.964, -0.25]}, {"time": 3.3333, "x": 0.1, "y": 2.85, "curve": [3.563, 0.17, 3.777, 0.23, 3.563, 4.82, 3.777, 6.38]}, {"time": 3.9333, "x": 0.23, "y": 6.38, "curve": [4.358, 0.23, 5.208, -0.17, 4.358, 6.38, 5.208, -4.47]}, {"time": 5.6333, "x": -0.17, "y": -4.47, "curve": [5.886, -0.17, 6.297, -0.01, 5.886, -4.47, 6.297, -0.25]}, {"time": 6.6667, "x": 0.1, "y": 2.85, "curve": [6.896, 0.17, 7.11, 0.23, 6.896, 4.82, 7.11, 6.38]}, {"time": 7.2667, "x": 0.23, "y": 6.38, "curve": [7.692, 0.23, 8.542, -0.17, 7.692, 6.38, 8.542, -4.47]}, {"time": 8.9667, "x": -0.17, "y": -4.47, "curve": [9.219, -0.17, 9.631, -0.01, 9.219, -4.47, 9.631, -0.25]}, {"time": 10, "x": 0.1, "y": 2.85}]}}}, "talk": {"slots": {"CrownEye": {"attachment": [{"time": 0.7, "name": "CrownEye_Closed"}, {"time": 1.5333, "name": "CrownEye"}]}}, "bones": {"CrownEye": {"scale": [{"time": 0.7, "curve": [0.725, 1, 0.775, 0.815, 0.725, 1, 0.775, 1.097]}, {"time": 0.8, "x": 0.815, "y": 1.097, "curve": [0.833, 0.815, 0.9, 1, 0.833, 1.097, 0.9, 1]}, {"time": 0.9333, "curve": "stepped"}, {"time": 1.5333, "curve": [1.558, 1, 1.608, 0.815, 1.558, 1, 1.608, 1.097]}, {"time": 1.6333, "x": 0.815, "y": 1.097, "curve": [1.667, 0.815, 1.733, 1, 1.667, 1.097, 1.733, 1]}, {"time": 1.7667}]}, "Head": {"translate": [{"curve": [0.417, 0, 1.25, -32.85, 0.417, 0, 1.25, 0.37]}, {"time": 1.6667, "x": -32.85, "y": 0.37, "curve": [2.083, -32.85, 2.917, 0, 2.083, 0.37, 2.917, 0]}, {"time": 3.3333}], "scale": [{"time": 1.0333, "curve": [1.058, 1, 1.108, 0.965, 1.058, 1, 1.108, 1.039]}, {"time": 1.1333, "x": 0.965, "y": 1.039, "curve": [1.15, 0.965, 1.183, 1, 1.15, 1.039, 1.183, 1]}, {"time": 1.2, "curve": [1.225, 1, 1.275, 0.965, 1.225, 1, 1.275, 1.039]}, {"time": 1.3, "x": 0.965, "y": 1.039, "curve": [1.325, 0.965, 1.375, 1, 1.325, 1.039, 1.375, 1]}, {"time": 1.4, "curve": "stepped"}, {"time": 2.4, "curve": [2.425, 1, 2.475, 0.965, 2.425, 1, 2.475, 1.039]}, {"time": 2.5, "x": 0.965, "y": 1.039, "curve": [2.517, 0.965, 2.55, 1, 2.517, 1.039, 2.55, 1]}, {"time": 2.5667, "curve": [2.592, 1, 2.642, 0.965, 2.592, 1, 2.642, 1.039]}, {"time": 2.6667, "x": 0.965, "y": 1.039, "curve": [2.692, 0.965, 2.742, 1, 2.692, 1.039, 2.742, 1]}, {"time": 2.7667}]}, "Face": {"translate": [{"x": 5.65, "y": -0.19, "curve": [0.217, 10.77, 0.416, 14.55, 0.217, -0.37, 0.416, -0.5]}, {"time": 0.5667, "x": 14.55, "y": -0.5, "curve": [0.967, 14.55, 1.767, -18.38, 0.967, -0.5, 1.767, 0.65]}, {"time": 2.1667, "x": -18.38, "y": 0.65, "curve": [2.217, -18.38, 2.317, -16.68, 2.217, 0.65, 2.317, 20.01]}, {"time": 2.3667, "x": -16.68, "y": 20.01, "curve": [2.408, -16.68, 2.492, -11.49, 2.408, 20.01, 2.492, -34.65]}, {"time": 2.5333, "x": -11.49, "y": -34.65, "curve": [2.575, -11.49, 2.658, -16.68, 2.575, -34.65, 2.658, 20.01]}, {"time": 2.7, "x": -16.68, "y": 20.01, "curve": [2.742, -16.68, 2.825, -11.49, 2.742, 20.01, 2.825, -12.74]}, {"time": 2.8667, "x": -11.49, "y": -12.74, "curve": [2.908, -11.49, 2.992, -5.16, 2.908, -12.74, 2.992, 0.19]}, {"time": 3.0333, "x": -5.16, "y": 0.19, "curve": [3.108, -5.16, 3.258, 5.65, 3.108, 0.19, 3.258, -0.19]}, {"time": 3.3333, "x": 5.65, "y": -0.19}], "scale": [{"curve": [0.033, 1, 0.1, 0.882, 0.033, 1, 0.1, 1.174]}, {"time": 0.1333, "x": 0.882, "y": 1.174, "curve": [0.183, 0.882, 0.283, 1, 0.183, 1.174, 0.283, 1]}, {"time": 0.3333, "curve": [0.367, 1, 0.433, 0.882, 0.367, 1, 0.433, 1.174]}, {"time": 0.4667, "x": 0.882, "y": 1.174, "curve": [0.517, 0.882, 0.617, 1, 0.517, 1.174, 0.617, 1]}, {"time": 0.6667, "curve": "stepped"}, {"time": 0.8333}, {"time": 0.9667, "x": 0.882, "y": 1.174, "curve": [1.017, 0.882, 1.117, 1, 1.017, 1.174, 1.117, 1]}, {"time": 1.1667, "curve": "stepped"}, {"time": 1.3333, "curve": [1.367, 1, 1.433, 0.882, 1.367, 1, 1.433, 1.174]}, {"time": 1.4667, "x": 0.882, "y": 1.174, "curve": [1.5, 0.882, 1.567, 1, 1.5, 1.174, 1.567, 1]}, {"time": 1.6, "curve": [1.65, 1, 1.75, 0.882, 1.65, 1, 1.75, 1.174]}, {"time": 1.8, "x": 0.882, "y": 1.174, "curve": [1.85, 0.882, 1.95, 1, 1.85, 1.174, 1.95, 1]}, {"time": 2}]}, "Antler5": {"rotate": [{"value": -8, "curve": [0.386, -4.8, 0.832, 0]}, {"time": 1.1, "curve": [1.525, 0, 2.375, -11.17]}, {"time": 2.8, "value": -11.17, "curve": [2.941, -11.17, 3.129, -9.82]}, {"time": 3.3333, "value": -8}]}, "Antler4": {"rotate": [{"value": -5.59, "curve": [0.313, -2.79, 0.625, 0]}, {"time": 0.8333, "curve": [1.25, 0, 2.083, -11.17]}, {"time": 2.5, "value": -11.17, "curve": [2.708, -11.17, 3.021, -8.38]}, {"time": 3.3333, "value": -5.59}]}, "Antler3": {"rotate": [{"value": -3.17, "curve": [0.217, -1.36, 0.417, 0]}, {"time": 0.5667, "curve": [0.983, 0, 1.817, -11.17]}, {"time": 2.2333, "value": -11.17, "curve": [2.501, -11.17, 2.947, -6.37]}, {"time": 3.3333, "value": -3.17}]}, "Antler2": {"rotate": [{"value": -1.08, "curve": [0.099, -0.4, 0.189, 0]}, {"time": 0.2667, "curve": [0.692, 0, 1.542, -11.17]}, {"time": 1.9667, "value": -11.17, "curve": [2.299, -11.17, 2.909, -3.6]}, {"time": 3.3333, "value": -1.08}]}, "Antler1": {"rotate": [{"curve": [0.417, 0, 1.25, -11.17]}, {"time": 1.6667, "value": -11.17, "curve": [2.083, -11.17, 2.917, 0]}, {"time": 3.3333}]}, "Antler10": {"rotate": [{"value": 5.71, "curve": [0.386, 3.43, 0.832, 0]}, {"time": 1.1, "curve": [1.525, 0, 2.375, 7.97]}, {"time": 2.8, "value": 7.97, "curve": [2.941, 7.97, 3.129, 7]}, {"time": 3.3333, "value": 5.71}]}, "Antler9": {"rotate": [{"value": 3.99, "curve": [0.313, 1.99, 0.625, 0]}, {"time": 0.8333, "curve": [1.25, 0, 2.083, 7.97]}, {"time": 2.5, "value": 7.97, "curve": [2.708, 7.97, 3.021, 5.98]}, {"time": 3.3333, "value": 3.99}]}, "Antler8": {"rotate": [{"value": 2.26, "curve": [0.217, 0.97, 0.417, 0]}, {"time": 0.5667, "curve": [0.983, 0, 1.817, 7.97]}, {"time": 2.2333, "value": 7.97, "curve": [2.501, 7.97, 2.947, 4.55]}, {"time": 3.3333, "value": 2.26}]}, "Antler7": {"rotate": [{"value": 0.77, "curve": [0.099, 0.29, 0.189, 0]}, {"time": 0.2667, "curve": [0.692, 0, 1.542, 7.97]}, {"time": 1.9667, "value": 7.97, "curve": [2.299, 7.97, 2.909, 2.57]}, {"time": 3.3333, "value": 0.77}]}, "Antler6": {"rotate": [{"curve": [0.417, 0, 1.25, 7.97]}, {"time": 1.6667, "value": 7.97, "curve": [2.083, 7.97, 2.917, 0]}, {"time": 3.3333}]}, "Hands_1": {"rotate": [{"value": -5.24, "curve": [0.036, -5.45, 0.07, -5.57]}, {"time": 0.1, "value": -5.57, "curve": [0.308, -5.57, 0.725, 4.08]}, {"time": 0.9333, "value": 4.08, "curve": [1.133, 4.08, 1.533, -11.11]}, {"time": 1.7333, "value": -11.11, "curve": [1.925, -11.11, 2.308, 0.31]}, {"time": 2.5, "value": 0.31, "curve": [2.704, 0.31, 3.087, -4.18]}, {"time": 3.3333, "value": -5.24}]}, "Hands_0": {"rotate": [{"value": 1.63, "curve": [0.127, -0.05, 0.246, -1.34]}, {"time": 0.3333, "value": -1.34, "curve": [0.483, -1.34, 0.783, 5.53]}, {"time": 0.9333, "value": 5.53, "curve": [1.133, 5.53, 1.533, 2.01]}, {"time": 1.7333, "value": 2.01, "curve": [1.983, 2.01, 2.483, 8.29]}, {"time": 2.7333, "value": 8.29, "curve": [2.88, 8.29, 3.12, 4.39]}, {"time": 3.3333, "value": 1.63}]}, "HairBit0": {"rotate": [{"value": 4.86, "curve": [0.033, 4.86, 0.1, 20.27]}, {"time": 0.1333, "value": 20.27, "curve": [0.183, 20.27, 0.283, 0]}, {"time": 0.3333, "curve": [0.367, 0, 0.433, 20.27]}, {"time": 0.4667, "value": 20.27, "curve": [0.517, 20.27, 0.617, 3.9]}, {"time": 0.6667, "value": 3.9, "curve": "stepped"}, {"time": 0.8333, "value": 3.9, "curve": [0.867, 3.9, 0.933, 20.27]}, {"time": 0.9667, "value": 20.27, "curve": [1.017, 20.27, 1.117, 0]}, {"time": 1.1667, "curve": [1.208, 0, 1.292, 4.86]}, {"time": 1.3333, "value": 4.86, "curve": [1.367, 4.86, 1.433, 20.27]}, {"time": 1.4667, "value": 20.27, "curve": [1.5, 20.27, 1.567, 0]}, {"time": 1.6, "curve": [1.65, 0, 1.75, 20.27]}, {"time": 1.8, "value": 20.27, "curve": [1.85, 20.27, 1.95, 3.9]}, {"time": 2, "value": 3.9, "curve": [2.225, 3.9, 2.675, 11.97]}, {"time": 2.9, "value": 11.97, "curve": [3.007, 11.97, 3.174, 8.14]}, {"time": 3.3333, "value": 4.86}], "scale": [{"curve": [0.033, 1, 0.1, 1.333, 0.033, 1, 0.1, 1]}, {"time": 0.1333, "x": 1.333, "curve": [0.183, 1.333, 0.283, 1, 0.183, 1, 0.283, 1]}, {"time": 0.3333, "curve": [0.367, 1, 0.433, 1.333, 0.367, 1, 0.433, 1]}, {"time": 0.4667, "x": 1.333, "curve": [0.517, 1.333, 0.617, 1, 0.517, 1, 0.617, 1]}, {"time": 0.6667, "curve": "stepped"}, {"time": 0.8333}, {"time": 0.9667, "x": 1.333, "curve": [1.017, 1.333, 1.117, 1, 1.017, 1, 1.117, 1]}, {"time": 1.1667, "curve": "stepped"}, {"time": 1.3333, "curve": [1.367, 1, 1.433, 1.333, 1.367, 1, 1.433, 1]}, {"time": 1.4667, "x": 1.333, "curve": [1.5, 1.333, 1.567, 1, 1.5, 1, 1.567, 1]}, {"time": 1.6, "curve": [1.65, 1, 1.75, 1.333, 1.65, 1, 1.75, 1]}, {"time": 1.8, "x": 1.333, "curve": [1.85, 1.333, 1.95, 1, 1.85, 1, 1.95, 1]}, {"time": 2}]}, "HairBit1": {"rotate": [{"value": 9.4, "curve": [0.033, 9.4, 0.1, -7.35]}, {"time": 0.1333, "value": -7.35, "curve": [0.183, -7.35, 0.283, 5.57]}, {"time": 0.3333, "value": 5.57, "curve": [0.367, 5.57, 0.433, 3.39]}, {"time": 0.4667, "value": 3.39, "curve": [0.517, 3.39, 0.617, 0]}, {"time": 0.6667, "curve": "stepped"}, {"time": 0.8333, "curve": [0.867, 0, 0.933, -7.35]}, {"time": 0.9667, "value": -7.35, "curve": [1.017, -7.35, 1.117, 5.57]}, {"time": 1.1667, "value": 5.57, "curve": [1.208, 5.57, 1.292, 9.4]}, {"time": 1.3333, "value": 9.4, "curve": [1.367, 9.4, 1.433, -7.35]}, {"time": 1.4667, "value": -7.35, "curve": [1.517, -7.35, 1.617, 5.57]}, {"time": 1.6667, "value": 5.57, "curve": [1.7, 5.57, 1.767, 3.39]}, {"time": 1.8, "value": 3.39, "curve": [1.85, 3.39, 1.95, 0]}, {"time": 2, "curve": [2.117, 0, 2.35, -6.87]}, {"time": 2.4667, "value": -6.87, "curve": [2.683, -6.87, 3.117, 9.4]}, {"time": 3.3333, "value": 9.4}], "scale": [{"curve": [0.033, 1, 0.1, 1.454, 0.033, 1, 0.1, 1]}, {"time": 0.1333, "x": 1.454, "curve": [0.183, 1.454, 0.283, 1, 0.183, 1, 0.283, 1]}, {"time": 0.3333, "curve": [0.367, 1, 0.433, 1.454, 0.367, 1, 0.433, 1]}, {"time": 0.4667, "x": 1.454, "curve": [0.517, 1.454, 0.617, 1, 0.517, 1, 0.617, 1]}, {"time": 0.6667, "curve": "stepped"}, {"time": 0.8333}, {"time": 0.9667, "x": 1.454, "curve": [1.017, 1.454, 1.117, 1, 1.017, 1, 1.117, 1]}, {"time": 1.1667, "curve": "stepped"}, {"time": 1.3333, "curve": [1.367, 1, 1.433, 1.454, 1.367, 1, 1.433, 1]}, {"time": 1.4667, "x": 1.454, "curve": [1.517, 1.454, 1.617, 1, 1.517, 1, 1.617, 1]}, {"time": 1.6667, "curve": [1.7, 1, 1.767, 1.454, 1.7, 1, 1.767, 1]}, {"time": 1.8, "x": 1.454, "curve": [1.85, 1.454, 1.95, 1, 1.85, 1, 1.95, 1]}, {"time": 2}]}, "NeckLeaf1": {"rotate": [{"value": -11.45, "curve": [0.222, -7.63, 0.505, 0]}, {"time": 0.6667, "curve": [0.917, 0, 1.417, -14.03]}, {"time": 1.6667, "value": -14.03, "curve": [1.867, -14.03, 2.267, 0]}, {"time": 2.4667, "curve": [2.633, 0, 2.967, -14.03]}, {"time": 3.1333, "value": -14.03, "curve": [3.189, -14.03, 3.257, -13]}, {"time": 3.3333, "value": -11.45}]}, "NeckLeaf2": {"rotate": [{"curve": [0.167, 0, 0.5, -12.24]}, {"time": 0.6667, "value": -12.24, "curve": [0.892, -12.24, 1.342, 11.87]}, {"time": 1.5667, "value": 11.87, "curve": [1.817, 11.87, 2.317, -12.24]}, {"time": 2.5667, "value": -12.24, "curve": [2.758, -12.24, 3.142, 0]}, {"time": 3.3333}]}, "NeckLeaf5": {"rotate": [{"value": 0.17, "curve": [0.255, 1.02, 0.703, 8.75]}, {"time": 0.9333, "value": 8.75, "curve": [1.133, 8.75, 1.533, 0]}, {"time": 1.7333, "curve": [1.9, 0, 2.233, 8.75]}, {"time": 2.4, "value": 8.75, "curve": [2.625, 8.75, 3.075, 0]}, {"time": 3.3, "curve": [3.311, 0, 3.322, 0.06]}, {"time": 3.3333, "value": 0.17}]}, "NeckLeaf3": {"rotate": [{"value": 12.29, "curve": [0.164, 17.11, 0.323, 21.44]}, {"time": 0.4333, "value": 21.44, "curve": [0.633, 21.44, 1.033, 0]}, {"time": 1.2333, "curve": [1.4, 0, 1.733, 21.44]}, {"time": 1.9, "value": 21.44, "curve": [2.125, 21.44, 2.575, 0]}, {"time": 2.8, "curve": [2.932, 0, 3.137, 6.51]}, {"time": 3.3333, "value": 12.29}]}, "NeckLeaf4": {"rotate": [{"value": 8.95, "curve": [0.178, 5.22, 0.378, 0]}, {"time": 0.5, "curve": [0.667, 0, 1, 20.86]}, {"time": 1.1667, "value": 20.86, "curve": [1.392, 20.86, 1.842, -5.18]}, {"time": 2.0667, "value": -5.18, "curve": [2.317, -5.18, 2.817, 13.04]}, {"time": 3.0667, "value": 13.04, "curve": [3.137, 13.04, 3.231, 11.25]}, {"time": 3.3333, "value": 8.95}]}, "BodyBtm": {"scale": [{"curve": [0.417, 1, 1.25, 0.967, 0.417, 1, 1.25, 1.049]}, {"time": 1.6667, "x": 0.967, "y": 1.049, "curve": [2.083, 0.967, 2.917, 1, 2.083, 1.049, 2.917, 1]}, {"time": 3.3333}]}, "BodyTop": {"scale": [{"x": 0.991, "y": 1.014, "curve": [0.217, 0.996, 0.417, 1, 0.217, 1.006, 0.417, 1]}, {"time": 0.5667, "curve": [0.983, 1, 1.817, 0.967, 0.983, 1, 1.817, 1.049]}, {"time": 2.2333, "x": 0.967, "y": 1.049, "curve": [2.501, 0.967, 2.947, 0.981, 2.501, 1.049, 2.947, 1.028]}, {"time": 3.3333, "x": 0.991, "y": 1.014}]}, "DangleHandle1": {"translate": [{"curve": [0.279, -0.09, 0.546, -0.17, 0.279, -2.37, 0.546, -4.47]}, {"time": 0.7333, "x": -0.17, "y": -4.47, "curve": [1.15, -0.17, 1.983, 0.23, 1.15, -4.47, 1.983, 6.38]}, {"time": 2.4, "x": 0.23, "y": 6.38, "curve": [2.63, 0.23, 2.99, 0.1, 2.63, 6.38, 2.99, 2.96]}, {"time": 3.3333}]}, "DangleHandle2": {"translate": [{"x": 0.1, "y": 2.85, "curve": [0.229, 0.17, 0.443, 0.23, 0.229, 4.82, 0.443, 6.38]}, {"time": 0.6, "x": 0.23, "y": 6.38, "curve": [1.025, 0.23, 1.875, -0.17, 1.025, 6.38, 1.875, -4.47]}, {"time": 2.3, "x": -0.17, "y": -4.47, "curve": [2.552, -0.17, 2.964, -0.01, 2.552, -4.47, 2.964, -0.25]}, {"time": 3.3333, "x": 0.1, "y": 2.85}]}}, "attachments": {"default": {"HairBit_1": {"BandageHair1": {"deform": [{}, {"time": 0.1333, "offset": 10, "vertices": [4.79947, -3.1349]}]}}}}}, "transform": {"slots": {"AntlersLeft": {"rgba2": [{"time": 6.0333, "light": "ffffffff", "dark": "000000"}, {"time": 7.1333, "light": "ff0000ff", "dark": "000000"}]}, "AntlersRight": {"rgba2": [{"time": 6.0333, "light": "ffffffff", "dark": "000000"}, {"time": 7.1333, "light": "ff0000ff", "dark": "000000"}]}, "Antler_0": {"rgba2": [{"time": 6.0333, "light": "ffffffff", "dark": "000000"}, {"time": 7.1333, "light": "ff0000ff", "dark": "000000"}], "attachment": [{"time": 3.4333, "name": "Antler_1"}]}, "Antler_2": {"rgba2": [{"time": 6.0333, "light": "ffffffff", "dark": "000000"}, {"time": 7.1333, "light": "ff0000ff", "dark": "000000"}], "attachment": [{"time": 3.5, "name": "Antler_3"}]}, "Antler_3": {"rgba2": [{"time": 6.0333, "light": "ffffffff", "dark": "000000"}, {"time": 7.1333, "light": "ff0000ff", "dark": "000000"}], "attachment": [{"time": 3.5667, "name": "Antler_4"}]}, "Antler_4": {"rgba2": [{"time": 6.0333, "light": "ffffffff", "dark": "000000"}, {"time": 7.1333, "light": "ff0000ff", "dark": "000000"}], "attachment": [{"time": 5, "name": "Antler_5"}]}, "Antler_5": {"rgba2": [{"time": 6.0333, "light": "ffffffff", "dark": "000000"}, {"time": 7.1333, "light": "ff0000ff", "dark": "000000"}], "attachment": [{"time": 4.9333, "name": "Antler_6"}]}, "Antler_6": {"rgba2": [{"time": 6.0333, "light": "ffffffff", "dark": "000000"}, {"time": 7.1333, "light": "ff0000ff", "dark": "000000"}], "attachment": [{"time": 4.8667, "name": "Antler_7"}]}, "BehindCloak": {"rgba2": [{"time": 6.0333, "light": "ffffffff", "dark": "000000"}, {"time": 7.1333, "light": "ff0000ff", "dark": "000000"}], "attachment": [{"time": 0.9667, "name": "BehindCloak_Floating"}, {"time": 6.0667, "name": "BehindCloak"}]}, "Cloak": {"rgba2": [{"time": 6.0333, "light": "ffffffff", "dark": "000000"}, {"time": 7.1333, "light": "ff0000ff", "dark": "000000"}], "attachment": [{}]}, "Cloak_Left": {"rgba2": [{"time": 6.0333, "light": "ffffffff", "dark": "000000"}, {"time": 7.1333, "light": "ff0000ff", "dark": "000000"}], "attachment": [{"name": "Cloak_Left"}]}, "Cloak_Right": {"rgba2": [{"time": 6.0333, "light": "ffffffff", "dark": "000000"}, {"time": 7.1333, "light": "ff0000ff", "dark": "000000"}], "attachment": [{"name": "Cloak_Right"}]}, "CROWN": {"rgba2": [{"time": 6.0333, "light": "ffffffff", "dark": "000000"}, {"time": 7.1333, "light": "ff0000ff", "dark": "000000"}]}, "CrownEye": {"rgba2": [{"time": 6.0333, "light": "ffffffff", "dark": "000000"}, {"time": 7.1333, "light": "ff0000ff", "dark": "000000"}], "attachment": [{"time": 0.3, "name": "CrownEye_Closed"}, {"time": 1.0667, "name": "CrownEye"}, {"time": 3.7333, "name": "CrownEye_Closed"}, {"time": 4.5667, "name": "CrownEye"}]}, "CrownGrass": {"rgba2": [{"time": 6.0333, "light": "ffffffff", "dark": "000000"}, {"time": 7.1333, "light": "ff0000ff", "dark": "000000"}]}, "Drip1": {"rgba2": [{"time": 6.0333, "light": "ffffffff", "dark": "000000"}, {"time": 7.1333, "light": "ff0000ff", "dark": "000000"}], "attachment": [{"time": 1.5, "name": "Drip1"}, {"time": 3.3667}]}, "Drip2": {"rgba2": [{"time": 6.0333, "light": "ffffffff", "dark": "000000"}, {"time": 7.1333, "light": "ff0000ff", "dark": "000000"}], "attachment": [{"time": 0.8333, "name": "Drip2"}, {"time": 3.3667}]}, "Drip3": {"rgba2": [{"time": 6.0333, "light": "ffffffff", "dark": "000000"}, {"time": 7.1333, "light": "ff0000ff", "dark": "000000"}], "attachment": [{"time": 1.2, "name": "Drip1"}, {"time": 3.3667}]}, "Drip4": {"rgba2": [{"time": 6.0333, "light": "ffffffff", "dark": "000000"}, {"time": 7.1333, "light": "ff0000ff", "dark": "000000"}], "attachment": [{"time": 1.6, "name": "Drip2"}, {"time": 3.3667}]}, "Drip5": {"rgba2": [{"time": 6.0333, "light": "ffffffff", "dark": "000000"}, {"time": 7.1333, "light": "ff0000ff", "dark": "000000"}], "attachment": [{"time": 1.7667, "name": "Drip1"}, {"time": 3.3667}]}, "Drip6": {"rgba2": [{"time": 6.0333, "light": "ffffffff", "dark": "000000"}, {"time": 7.1333, "light": "ff0000ff", "dark": "000000"}], "attachment": [{"time": 1.5, "name": "Drip2"}, {"time": 3.3667}]}, "HairBit_0": {"rgba2": [{"time": 6.0333, "light": "ffffffff", "dark": "000000"}, {"time": 7.1333, "light": "ff0000ff", "dark": "000000"}]}, "HairBit_1": {"rgba2": [{"time": 6.0333, "light": "ffffffff", "dark": "000000"}, {"time": 7.1333, "light": "ff0000ff", "dark": "000000"}]}, "Hands_0": {"rgba2": [{"time": 6.0333, "light": "ffffffff", "dark": "000000"}, {"time": 7.1333, "light": "ff0000ff", "dark": "000000"}]}, "Hands_1": {"rgba2": [{"time": 6.0333, "light": "ffffffff", "dark": "000000"}, {"time": 7.1333, "light": "ff0000ff", "dark": "000000"}]}, "HEAD": {"rgba2": [{"time": 6.0333, "light": "ffffffff", "dark": "000000"}, {"time": 7.1333, "light": "ff0000ff", "dark": "000000"}], "attachment": [{"time": 3.3667, "name": "Head_Drips"}]}, "Neck": {"rgba2": [{"time": 6.0333, "light": "ffffffff", "dark": "000000"}, {"time": 7.1333, "light": "ff0000ff", "dark": "000000"}]}, "Scarf": {"rgba2": [{"time": 6.0333, "light": "ffffffff", "dark": "000000"}, {"time": 7.1333, "light": "ff0000ff", "dark": "000000"}]}, "Symbol_0": {"rgba2": [{"time": 6.0333, "light": "ffffffff", "dark": "000000"}, {"time": 7.1333, "light": "ff0000ff", "dark": "000000"}], "attachment": [{"time": 3.6333, "name": "Symbol_1"}]}, "Symbol_1": {"rgba2": [{"time": 6.0333, "light": "ffffffff", "dark": "000000"}, {"time": 7.1333, "light": "ff0000ff", "dark": "000000"}]}, "Symbol_2": {"rgba2": [{"time": 6.0333, "light": "ffffffff", "dark": "000000"}, {"time": 7.1333, "light": "ff0000ff", "dark": "000000"}]}, "Symbol_3": {"rgba2": [{"time": 6.0333, "light": "ffffffff", "dark": "000000"}, {"time": 7.1333, "light": "ff0000ff", "dark": "000000"}], "attachment": [{"time": 5.0667, "name": "Symbol_4"}]}, "Vine": {"rgba2": [{"time": 6.0333, "light": "ffffffff", "dark": "000000"}, {"time": 7.1333, "light": "ff0000ff", "dark": "000000"}]}, "Vine_0": {"rgba2": [{"time": 6.0333, "light": "ffffffff", "dark": "000000"}, {"time": 7.1333, "light": "ff0000ff", "dark": "000000"}]}, "Vine_1": {"rgba2": [{"time": 6.0333, "light": "ffffffff", "dark": "000000"}, {"time": 7.1333, "light": "ff0000ff", "dark": "000000"}]}, "Vine_2": {"rgba2": [{"time": 6.0333, "light": "ffffffff", "dark": "000000"}, {"time": 7.1333, "light": "ff0000ff", "dark": "000000"}]}}, "bones": {"Antler10": {"rotate": [{"value": 5.71, "curve": [0.167, 5.71, 0.5, -20.5]}, {"time": 0.6667, "value": -20.5, "curve": [0.75, -20.5, 0.917, 28]}, {"time": 1, "value": 28, "curve": [1.167, 28, 1.5, -0.08]}, {"time": 1.6667, "value": -0.08, "curve": [1.833, -0.08, 2.167, 14.78]}, {"time": 2.3333, "value": 14.78, "curve": [2.5, 14.78, 2.833, -0.08]}, {"time": 3, "value": -0.08, "curve": [3.133, -0.08, 3.4, 5.71]}, {"time": 3.5333, "value": 5.71, "curve": "stepped"}, {"time": 5.6667, "value": 5.71, "curve": [5.775, 5.71, 5.992, -26.59]}, {"time": 6.1, "value": -26.59, "curve": [6.183, -26.59, 6.35, 20.46]}, {"time": 6.4333, "value": 20.46, "curve": [6.92, 20.46, 7.233, -26.59]}, {"time": 7.5, "value": -26.59}], "scale": [{"time": 3.4333, "curve": [3.45, 1, 3.483, 1.522, 3.45, 1, 3.483, 1.319]}, {"time": 3.5, "x": 1.522, "y": 1.319, "curve": [3.525, 1.522, 3.575, 0.832, 3.525, 1.319, 3.575, 1.077]}, {"time": 3.6, "x": 0.832, "y": 1.077, "curve": [3.642, 0.832, 3.725, 1.159, 3.642, 1.077, 3.725, 1.086]}, {"time": 3.7667, "x": 1.159, "y": 1.086, "curve": [3.825, 1.159, 3.942, 1, 3.825, 1.086, 3.942, 1]}, {"time": 4}]}, "Antler9": {"rotate": [{"value": 3.99, "curve": [0.167, 3.99, 0.5, -22.22]}, {"time": 0.6667, "value": -22.22, "curve": [0.75, -22.22, 0.917, 26.28]}, {"time": 1, "value": 26.28, "curve": [1.167, 26.28, 1.5, -1.81]}, {"time": 1.6667, "value": -1.81, "curve": [1.833, -1.81, 2.167, 13.05]}, {"time": 2.3333, "value": 13.05, "curve": [2.5, 13.05, 2.833, -1.81]}, {"time": 3, "value": -1.81, "curve": [3.133, -1.81, 3.4, 3.99]}, {"time": 3.5333, "value": 3.99, "curve": "stepped"}, {"time": 5.6667, "value": 3.99, "curve": [5.775, 3.99, 5.992, -28.32]}, {"time": 6.1, "value": -28.32, "curve": [6.183, -28.32, 6.35, 18.73]}, {"time": 6.4333, "value": 18.73, "curve": [6.92, 18.73, 7.233, -28.32]}, {"time": 7.5, "value": -28.32}], "scale": [{"time": 3.5, "curve": [3.517, 1, 3.55, 1.522, 3.517, 1, 3.55, 1.319]}, {"time": 3.5667, "x": 1.522, "y": 1.319, "curve": [3.592, 1.522, 3.642, 0.832, 3.592, 1.319, 3.642, 1.077]}, {"time": 3.6667, "x": 0.832, "y": 1.077, "curve": [3.708, 0.832, 3.792, 1.159, 3.708, 1.077, 3.792, 1.086]}, {"time": 3.8333, "x": 1.159, "y": 1.086, "curve": [3.892, 1.159, 4.008, 1, 3.892, 1.086, 4.008, 1]}, {"time": 4.0667}]}, "Antler8": {"rotate": [{"value": 2.26, "curve": [0.167, 2.26, 0.5, -23.94]}, {"time": 0.6667, "value": -23.94, "curve": [0.75, -23.94, 0.917, 24.56]}, {"time": 1, "value": 24.56, "curve": [1.167, 24.56, 1.5, -3.53]}, {"time": 1.6667, "value": -3.53, "curve": [1.833, -3.53, 2.167, 11.33]}, {"time": 2.3333, "value": 11.33, "curve": [2.5, 11.33, 2.833, -3.53]}, {"time": 3, "value": -3.53, "curve": [3.133, -3.53, 3.4, 2.26]}, {"time": 3.5333, "value": 2.26, "curve": "stepped"}, {"time": 5.6667, "value": 2.26, "curve": [5.775, 2.26, 5.992, -30.04]}, {"time": 6.1, "value": -30.04, "curve": [6.183, -30.04, 6.35, 17.01]}, {"time": 6.4333, "value": 17.01, "curve": [6.92, 17.01, 7.233, -30.04]}, {"time": 7.5, "value": -30.04}], "scale": [{"time": 3.5667, "curve": [3.583, 1, 3.617, 1.522, 3.583, 1, 3.617, 1.319]}, {"time": 3.6333, "x": 1.522, "y": 1.319, "curve": [3.658, 1.522, 3.708, 0.832, 3.658, 1.319, 3.708, 1.077]}, {"time": 3.7333, "x": 0.832, "y": 1.077, "curve": [3.775, 0.832, 3.858, 1.159, 3.775, 1.077, 3.858, 1.086]}, {"time": 3.9, "x": 1.159, "y": 1.086, "curve": [3.958, 1.159, 4.075, 1, 3.958, 1.086, 4.075, 1]}, {"time": 4.1333}]}, "Antler3": {"rotate": [{"value": -3.17, "curve": [0.167, -3.17, 0.5, 29.06]}, {"time": 0.6667, "value": 29.06, "curve": [0.75, 29.06, 0.917, -23.17]}, {"time": 1, "value": -23.17, "curve": [1.167, -23.17, 1.5, 3.32]}, {"time": 1.6667, "value": 3.32, "curve": [1.833, 3.32, 2.167, -10.84]}, {"time": 2.3333, "value": -10.84, "curve": [2.5, -10.84, 2.833, 3.32]}, {"time": 3, "value": 3.32, "curve": [3.133, 3.32, 3.4, -3.17]}, {"time": 3.5333, "value": -3.17, "curve": "stepped"}, {"time": 5.6667, "value": -3.17, "curve": [5.775, -3.17, 5.992, 38.12]}, {"time": 6.1, "value": 38.12, "curve": [6.183, 38.12, 6.35, -13.14]}, {"time": 6.4333, "value": -13.14, "curve": [6.92, -13.14, 7.233, 38.12]}, {"time": 7.5, "value": 38.12}], "scale": [{"time": 5, "curve": [5.017, 1, 5.05, 1.639, 5.017, 1, 5.05, 1.317]}, {"time": 5.0667, "x": 1.639, "y": 1.317, "curve": [5.092, 1.639, 5.142, 1, 5.092, 1.317, 5.142, 1]}, {"time": 5.1667, "curve": [5.208, 1, 5.292, 1.308, 5.208, 1, 5.292, 1.184]}, {"time": 5.3333, "x": 1.308, "y": 1.184, "curve": [5.392, 1.308, 5.508, 1, 5.392, 1.184, 5.508, 1]}, {"time": 5.5667}]}, "Antler4": {"rotate": [{"value": -5.59, "curve": [0.167, -5.59, 0.5, 26.64]}, {"time": 0.6667, "value": 26.64, "curve": [0.75, 26.64, 0.917, -25.59]}, {"time": 1, "value": -25.59, "curve": [1.167, -25.59, 1.5, 0.91]}, {"time": 1.6667, "value": 0.91, "curve": [1.833, 0.91, 2.167, -13.26]}, {"time": 2.3333, "value": -13.26, "curve": [2.5, -13.26, 2.833, 0.91]}, {"time": 3, "value": 0.91, "curve": [3.133, 0.91, 3.4, -5.59]}, {"time": 3.5333, "value": -5.59, "curve": "stepped"}, {"time": 5.6667, "value": -5.59, "curve": [5.775, -5.59, 5.992, 35.7]}, {"time": 6.1, "value": 35.7, "curve": [6.183, 35.7, 6.35, -15.56]}, {"time": 6.4333, "value": -15.56, "curve": [6.92, -15.56, 7.233, 35.7]}, {"time": 7.5, "value": 35.7}], "scale": [{"time": 4.9333, "curve": [4.95, 1, 4.983, 1.639, 4.95, 1, 4.983, 1.317]}, {"time": 5, "x": 1.639, "y": 1.317, "curve": [5.025, 1.639, 5.075, 1, 5.025, 1.317, 5.075, 1]}, {"time": 5.1, "curve": [5.142, 1, 5.225, 1.308, 5.142, 1, 5.225, 1.184]}, {"time": 5.2667, "x": 1.308, "y": 1.184, "curve": [5.325, 1.308, 5.442, 1, 5.325, 1.184, 5.442, 1]}, {"time": 5.5}]}, "Antler5": {"rotate": [{"value": -8, "curve": [0.167, -8, 0.5, 24.22]}, {"time": 0.6667, "value": 24.22, "curve": [0.75, 24.22, 0.917, -28]}, {"time": 1, "value": -28, "curve": [1.167, -28, 1.5, -1.51]}, {"time": 1.6667, "value": -1.51, "curve": [1.833, -1.51, 2.167, -15.68]}, {"time": 2.3333, "value": -15.68, "curve": [2.5, -15.68, 2.833, -1.51]}, {"time": 3, "value": -1.51, "curve": [3.133, -1.51, 3.4, -8]}, {"time": 3.5333, "value": -8, "curve": "stepped"}, {"time": 5.6667, "value": -8, "curve": [5.775, -8, 5.992, 33.29]}, {"time": 6.1, "value": 33.29, "curve": [6.183, 33.29, 6.35, -17.97]}, {"time": 6.4333, "value": -17.97, "curve": [6.92, -17.97, 7.233, 33.29]}, {"time": 7.5, "value": 33.29}], "scale": [{"time": 4.8667, "curve": [4.883, 1, 4.917, 1.639, 4.883, 1, 4.917, 1.317]}, {"time": 4.9333, "x": 1.639, "y": 1.317, "curve": [4.958, 1.639, 5.008, 1, 4.958, 1.317, 5.008, 1]}, {"time": 5.0333, "curve": [5.075, 1, 5.158, 1.308, 5.075, 1, 5.158, 1.184]}, {"time": 5.2, "x": 1.308, "y": 1.184, "curve": [5.258, 1.308, 5.375, 1, 5.258, 1.184, 5.375, 1]}, {"time": 5.4333}]}, "BodyBtm": {"scale": [{"curve": [0.175, 1, 0.079, 0.873, 0.175, 1, 0.079, 1.129]}, {"time": 0.7, "x": 0.873, "y": 1.129, "curve": [0.783, 0.873, 0.95, 1.055, 0.783, 1.129, 0.95, 0.938]}, {"time": 1.0333, "x": 1.055, "y": 0.938, "curve": [1.125, 1.055, 1.308, 0.89, 1.125, 0.938, 1.308, 1.138]}, {"time": 1.4, "x": 0.89, "y": 1.138, "curve": [1.592, 0.89, 1.975, 0.967, 1.592, 1.138, 1.975, 0.995]}, {"time": 2.1667, "x": 0.967, "y": 0.995, "curve": "stepped"}, {"time": 6.0667, "x": 0.967, "y": 0.995, "curve": [6.117, 0.967, 6.217, 0.832, 6.117, 0.995, 6.217, 1.128]}, {"time": 6.2667, "x": 0.832, "y": 1.128, "curve": [6.35, 0.832, 6.517, 0.894, 6.35, 1.128, 6.517, 1.081]}, {"time": 6.6, "x": 0.894, "y": 1.081, "curve": [6.642, 0.894, 6.725, 0.871, 6.642, 1.081, 6.725, 1.098]}, {"time": 6.7667, "x": 0.871, "y": 1.098, "curve": [6.95, 0.871, 7.41, 0.803, 6.95, 1.098, 7.41, 1.177]}, {"time": 7.5, "x": 0.765, "y": 1.221}]}, "MAIN": {"translate": [{"time": 0.7, "curve": [0.85, 0, 0.885, 0, 0.85, 0, 0.885, 216.4]}, {"time": 1.3, "y": 216.4, "curve": [1.467, 0, 1.8, 0, 1.467, 216.4, 1.8, 87.13]}, {"time": 1.9667, "y": 87.13, "curve": [2.133, 0, 2.467, 0, 2.133, 87.13, 2.467, 152.1]}, {"time": 2.6333, "y": 152.1, "curve": [2.8, 0, 3.133, 0, 2.8, 152.1, 3.133, 108.19]}, {"time": 3.3, "y": 108.19, "curve": [3.483, 0, 3.85, 0, 3.483, 108.19, 3.85, 152.1]}, {"time": 4.0333, "y": 152.1, "curve": [4.217, 0, 4.583, 0, 4.217, 152.1, 4.583, 108.19]}, {"time": 4.7667, "y": 108.19, "curve": [5, 0, 5.467, 0, 5, 108.19, 5.467, 165.11]}, {"time": 5.7, "y": 165.11, "curve": [5.792, 0, 6.02, 0, 5.792, 165.11, 6.02, 93.83]}, {"time": 6.0667}]}, "CrownEye": {"scale": [{"time": 0.3, "curve": [0.317, 1, 0.35, 0.815, 0.317, 1, 0.35, 1.097]}, {"time": 0.3667, "x": 0.815, "y": 1.097, "curve": [0.4, 0.815, 0.467, 1, 0.4, 1.097, 0.467, 1]}, {"time": 0.5, "curve": "stepped"}, {"time": 1.0667, "curve": [1.092, 1, 1.142, 0.815, 1.092, 1, 1.142, 1.097]}, {"time": 1.1667, "x": 0.815, "y": 1.097, "curve": [1.2, 0.815, 1.267, 1, 1.2, 1.097, 1.267, 1]}, {"time": 1.3, "curve": "stepped"}, {"time": 3.7333, "curve": [3.758, 1, 3.808, 0.815, 3.758, 1, 3.808, 1.097]}, {"time": 3.8333, "x": 0.815, "y": 1.097, "curve": [3.867, 0.815, 3.933, 1, 3.867, 1.097, 3.933, 1]}, {"time": 3.9667, "curve": "stepped"}, {"time": 4.5667, "curve": [4.592, 1, 4.642, 0.815, 4.592, 1, 4.642, 1.097]}, {"time": 4.6667, "x": 0.815, "y": 1.097, "curve": [4.7, 0.815, 4.767, 1, 4.7, 1.097, 4.767, 1]}, {"time": 4.8}]}, "Drip1": {"translate": [{"time": 1.5, "x": -14.36, "y": 1.27}], "scale": [{"time": 1.5, "y": 0.032}, {"time": 3.3333, "y": 1.167}]}, "Drip2": {"scale": [{"time": 0.8333, "y": 0.022}, {"time": 3, "y": 1.288}]}, "Drip3": {"translate": [{"time": 1.2, "x": -13.5, "y": -3.77}], "scale": [{"time": 1.2, "y": 0.022}, {"time": 2.9333, "y": 1.255}]}, "Drip4": {"translate": [{"time": 1.6, "x": -23.68, "y": -7.7}], "scale": [{"time": 1.6, "y": 0.022}, {"time": 3.1333}]}, "Drip5": {"translate": [{"time": 1.7667, "x": -2.82, "y": -0.97}], "scale": [{"time": 1.7667, "y": 0.001}, {"time": 3.0667, "y": 1.283}]}, "Drip6": {"translate": [{"time": 1.5, "x": -27.28, "y": -1.97}], "scale": [{"time": 1.5, "y": 0.022}, {"time": 3.3333, "y": 0.911}]}, "Head": {"rotate": [{"time": 3.4333, "curve": [3.483, 0, 3.583, -25.28]}, {"time": 3.6333, "value": -25.28, "curve": "stepped"}, {"time": 3.7667, "value": -25.28, "curve": [4.495, -25.28, 4.592, -14.05]}, {"time": 4.8667, "value": -14.05, "curve": [4.933, -14.05, 5.067, 32.92]}, {"time": 5.1333, "value": 32.92, "curve": "stepped"}, {"time": 5.6333, "value": 32.92}, {"time": 6.2}], "translate": [{"curve": [0.175, 0, 0.072, -56.34, 0.175, 0, 0.072, 0.57]}, {"time": 0.7, "x": -57.49, "y": 0.58, "curve": [0.867, -57.49, 1.2, 0, 0.867, 0.58, 1.2, 0]}, {"time": 1.3667, "curve": [2.618, 0, 2.41, 1.08, 2.618, 0, 2.41, -0.01]}, {"time": 4.6333, "x": 1.67, "y": -0.02, "curve": [4.92, 0.72, 5.321, 0.05, 4.92, -0.01, 5.321, 0]}, {"time": 5.9}, {"time": 6.2333, "x": -91.06, "y": 0.64, "curve": "stepped"}, {"time": 6.7667, "x": -91.06, "y": 0.64, "curve": [6.95, -91.06, 7.41, -131.53, 6.95, 0.64, 7.41, 0.81]}, {"time": 7.5, "x": -154.52, "y": 0.9}], "scale": [{"time": 3.5333, "curve": [3.558, 1, 3.608, 1.183, 3.558, 1, 3.608, 0.994]}, {"time": 3.6333, "x": 1.183, "y": 0.994, "curve": [3.667, 1.183, 3.733, 0.944, 3.667, 0.994, 3.733, 1.099]}, {"time": 3.7667, "x": 0.944, "y": 1.099, "curve": [3.8, 0.944, 3.867, 1, 3.8, 1.099, 3.867, 1]}, {"time": 3.9, "curve": "stepped"}, {"time": 4.8667, "curve": [4.892, 1, 4.942, 1.183, 4.892, 1, 4.942, 0.994]}, {"time": 4.9667, "x": 1.183, "y": 0.994, "curve": [5, 1.183, 5.067, 0.944, 5, 0.994, 5.067, 1.099]}, {"time": 5.1, "x": 0.944, "y": 1.099, "curve": [5.133, 0.944, 5.2, 1, 5.133, 1.099, 5.2, 1]}, {"time": 5.2333, "curve": "stepped"}, {"time": 5.6, "curve": [5.617, 1, 5.65, 0.958, 5.617, 1, 5.65, 1.077]}, {"time": 5.6667, "x": 0.958, "y": 1.077, "curve": [5.683, 0.958, 5.717, 1, 5.683, 1.077, 5.717, 1]}, {"time": 5.7333, "curve": [5.75, 1, 5.783, 0.958, 5.75, 1, 5.783, 1.077]}, {"time": 5.8, "x": 0.958, "y": 1.077, "curve": [5.817, 0.958, 5.85, 1, 5.817, 1.077, 5.85, 1]}, {"time": 5.8667, "curve": [5.883, 1, 5.917, 0.958, 5.883, 1, 5.917, 1.077]}, {"time": 5.9333, "x": 0.958, "y": 1.077, "curve": [5.95, 0.958, 5.983, 1, 5.95, 1.077, 5.983, 1]}, {"time": 6, "curve": [6.017, 1, 6.05, 0.958, 6.017, 1, 6.05, 1.077]}, {"time": 6.0667, "x": 0.958, "y": 1.077, "curve": [6.083, 0.958, 6.117, 1, 6.083, 1.077, 6.117, 1]}, {"time": 6.1333, "curve": [6.15, 1, 6.183, 0.958, 6.15, 1, 6.183, 1.077]}, {"time": 6.2, "x": 0.958, "y": 1.077, "curve": [6.217, 0.958, 6.25, 1, 6.217, 1.077, 6.25, 1]}, {"time": 6.2667, "curve": [6.283, 1, 6.317, 0.958, 6.283, 1, 6.317, 1.077]}, {"time": 6.3333, "x": 0.958, "y": 1.077, "curve": [6.35, 0.958, 6.383, 1, 6.35, 1.077, 6.383, 1]}, {"time": 6.4, "curve": [6.417, 1, 6.45, 0.958, 6.417, 1, 6.45, 1.077]}, {"time": 6.4667, "x": 0.958, "y": 1.077, "curve": [6.483, 0.958, 6.517, 1, 6.483, 1.077, 6.517, 1]}, {"time": 6.5333, "curve": [6.55, 1, 6.583, 0.958, 6.55, 1, 6.583, 1.077]}, {"time": 6.6, "x": 0.958, "y": 1.077, "curve": [6.617, 0.958, 6.65, 1, 6.617, 1.077, 6.65, 1]}, {"time": 6.6667, "curve": [6.683, 1, 6.717, 0.958, 6.683, 1, 6.717, 1.077]}, {"time": 6.7333, "x": 0.958, "y": 1.077, "curve": [6.75, 0.958, 6.783, 1, 6.75, 1.077, 6.783, 1]}, {"time": 6.8, "curve": [6.817, 1, 6.85, 0.958, 6.817, 1, 6.85, 1.077]}, {"time": 6.8667, "x": 0.958, "y": 1.077, "curve": [6.883, 0.958, 6.917, 1, 6.883, 1.077, 6.917, 1]}, {"time": 6.9333, "curve": [6.95, 1, 6.983, 0.93, 6.95, 1, 6.983, 1.135]}, {"time": 7, "x": 0.93, "y": 1.135, "curve": [7.017, 0.93, 7.05, 1, 7.017, 1.135, 7.05, 1]}, {"time": 7.0667}, {"time": 7.1333, "x": 0.93, "y": 1.135, "curve": [7.15, 0.93, 7.183, 1, 7.15, 1.135, 7.183, 1]}, {"time": 7.2, "curve": [7.217, 1, 7.25, 0.878, 7.217, 1, 7.25, 1.207]}, {"time": 7.2667, "x": 0.878, "y": 1.207, "curve": [7.283, 0.878, 7.317, 1, 7.283, 1.207, 7.317, 1]}, {"time": 7.3333, "curve": [7.35, 1, 7.383, 0.878, 7.35, 1, 7.383, 1.207]}, {"time": 7.4, "x": 0.878, "y": 1.207, "curve": [7.417, 0.878, 7.45, 1, 7.417, 1.207, 7.45, 1]}, {"time": 7.4667}]}, "HairBit0": {"rotate": [{"value": 4.86}]}, "HairBit1": {"rotate": [{"value": 9.4}]}, "Hands_0": {"rotate": [{"value": 1.63}]}, "Hands_1": {"rotate": [{"value": -5.24}]}, "Antler7": {"rotate": [{"value": 0.77, "curve": [0.167, 0.77, 0.5, -17.91]}, {"time": 0.6667, "value": -17.91, "curve": [0.75, -17.91, 0.917, 23.06]}, {"time": 1, "value": 23.06, "curve": [1.167, 23.06, 1.5, -5.02]}, {"time": 1.6667, "value": -5.02, "curve": [1.833, -5.02, 2.167, 9.83]}, {"time": 2.3333, "value": 9.83, "curve": [2.5, 9.83, 2.833, -5.02]}, {"time": 3, "value": -5.02, "curve": [3.133, -5.02, 3.4, 0.77]}, {"time": 3.5333, "value": 0.77, "curve": "stepped"}, {"time": 5.6667, "value": 0.77, "curve": [5.775, 0.77, 5.992, -24.01]}, {"time": 6.1, "value": -24.01, "curve": [6.183, -24.01, 6.35, 15.51]}, {"time": 6.4333, "value": 15.51, "curve": [6.92, 15.51, 7.233, -24.01]}, {"time": 7.5, "value": -24.01}], "scale": [{"time": 3.6333, "curve": [3.65, 1, 3.683, 1.522, 3.65, 1, 3.683, 1.319]}, {"time": 3.7, "x": 1.522, "y": 1.319, "curve": [3.725, 1.522, 3.775, 0.832, 3.725, 1.319, 3.775, 1.077]}, {"time": 3.8, "x": 0.832, "y": 1.077, "curve": [3.842, 0.832, 3.925, 1.159, 3.842, 1.077, 3.925, 1.086]}, {"time": 3.9667, "x": 1.159, "y": 1.086, "curve": [4.025, 1.159, 4.142, 1, 4.025, 1.086, 4.142, 1]}, {"time": 4.2}]}, "Antler6": {"rotate": [{"curve": [0.167, 0, 0.5, -11.36]}, {"time": 0.6667, "value": -11.36, "curve": [0.75, -11.36, 0.917, 14.75]}, {"time": 1, "value": 14.75, "curve": [1.167, 14.75, 1.5, -5.79]}, {"time": 1.6667, "value": -5.79, "curve": [1.833, -5.79, 2.167, 0]}, {"time": 2.3333, "curve": [2.5, 0, 2.833, -5.79]}, {"time": 3, "value": -5.79, "curve": [3.133, -5.79, 3.4, 0]}, {"time": 3.5333, "curve": "stepped"}, {"time": 5.6667, "curve": [5.775, 0, 5.992, -11.36]}, {"time": 6.1, "value": -11.36, "curve": [6.183, -11.36, 6.35, 14.75]}, {"time": 6.4333, "value": 14.75, "curve": [6.92, 14.75, 7.233, -11.36]}, {"time": 7.5, "value": -11.36}]}, "Antler1": {"rotate": [{"curve": [0.167, 0, 0.5, 14.28]}, {"time": 0.6667, "value": 14.28, "curve": [0.75, 14.28, 0.917, -9.97]}, {"time": 1, "value": -9.97, "curve": [1.167, -9.97, 1.5, 6.49]}, {"time": 1.6667, "value": 6.49, "curve": [1.833, 6.49, 2.167, 0]}, {"time": 2.3333, "curve": [2.5, 0, 2.833, 6.49]}, {"time": 3, "value": 6.49, "curve": [3.133, 6.49, 3.4, 0]}, {"time": 3.5333, "curve": "stepped"}, {"time": 5.6667, "curve": [5.775, 0, 5.992, 14.28]}, {"time": 6.1, "value": 14.28, "curve": [6.183, 14.28, 6.35, -9.97]}, {"time": 6.4333, "value": -9.97, "curve": [6.92, -9.97, 7.233, 14.28]}, {"time": 7.5, "value": 14.28}]}, "Antler2": {"rotate": [{"value": -1.08, "curve": [0.167, -1.08, 0.5, 19]}, {"time": 0.6667, "value": 19, "curve": [0.75, 19, 0.917, -21.08]}, {"time": 1, "value": -21.08, "curve": [1.167, -21.08, 1.5, 5.42]}, {"time": 1.6667, "value": 5.42, "curve": [1.833, 5.42, 2.167, -8.75]}, {"time": 2.3333, "value": -8.75, "curve": [2.5, -8.75, 2.833, 5.42]}, {"time": 3, "value": 5.42, "curve": [3.133, 5.42, 3.4, -1.08]}, {"time": 3.5333, "value": -1.08, "curve": "stepped"}, {"time": 5.6667, "value": -1.08, "curve": [5.775, -1.08, 5.992, 28.07]}, {"time": 6.1, "value": 28.07, "curve": [6.183, 28.07, 6.35, -11.05]}, {"time": 6.4333, "value": -11.05, "curve": [6.92, -11.05, 7.233, 28.07]}, {"time": 7.5, "value": 28.07}], "scale": [{"time": 5.0667, "curve": [5.083, 1, 5.117, 1.639, 5.083, 1, 5.117, 1.317]}, {"time": 5.1333, "x": 1.639, "y": 1.317, "curve": [5.158, 1.639, 5.208, 1, 5.158, 1.317, 5.208, 1]}, {"time": 5.2333, "curve": [5.275, 1, 5.358, 1.308, 5.275, 1, 5.358, 1.184]}, {"time": 5.4, "x": 1.308, "y": 1.184, "curve": [5.458, 1.308, 5.575, 1, 5.458, 1.184, 5.575, 1]}, {"time": 5.6333}]}, "DangleHandle2": {"translate": [{"x": 0.1, "y": 2.85}]}, "DangleHandle1": {"translate": [{}]}, "Face": {"translate": [{"x": 5.65, "y": -0.19, "curve": [0.076, 10.77, 0.147, 14.55, 0.076, -0.37, 0.147, -0.5]}, {"time": 0.2, "x": 14.55, "y": -0.5, "curve": [0.325, 14.55, 0.575, -54.93, 0.325, -0.5, 0.575, 2.53]}, {"time": 0.7, "x": -54.93, "y": 2.53, "curve": [0.825, -54.93, 1.075, 58.18, 0.825, 2.53, 1.075, -2.05]}, {"time": 1.2, "x": 58.18, "y": -2.05}, {"time": 1.3333, "x": 58.55, "y": 7.87}, {"time": 1.4, "x": 58.09, "y": -5.69}, {"time": 1.4667, "x": 58.25, "y": 1.39}, {"time": 1.5333, "x": 58.09, "y": -5.69}, {"time": 1.6, "x": 58.25, "y": 1.39}, {"time": 1.6667, "x": 58.09, "y": -5.69}, {"time": 1.7333, "x": 58.25, "y": 1.39}, {"time": 1.8, "x": 58.09, "y": -5.69}, {"time": 1.8667, "x": 58.25, "y": 1.39}, {"time": 1.9333, "x": 58.09, "y": -5.69}, {"time": 2, "x": 58.25, "y": 1.39}, {"time": 2.0667, "x": 58.09, "y": -5.69}, {"time": 2.1333, "x": 58.25, "y": 1.39}, {"time": 2.2, "x": 58.09, "y": -5.69}, {"time": 2.2667, "x": 58.55, "y": 7.87}, {"time": 2.3333, "x": 57.84, "y": -10.54}, {"time": 2.4, "x": 58.55, "y": 7.87}, {"time": 2.4667, "x": 57.84, "y": -10.54}, {"time": 2.5333, "x": 58.55, "y": 7.87}, {"time": 2.6, "x": 57.84, "y": -10.54}, {"time": 2.6667, "x": 58.55, "y": 7.87}, {"time": 2.7333, "x": 57.84, "y": -10.54}, {"time": 2.8, "x": 58.55, "y": 7.87}, {"time": 2.8667, "x": 57.84, "y": -10.54}, {"time": 2.9333, "x": 58.55, "y": 7.87}, {"time": 3, "x": 57.84, "y": -10.54}, {"time": 3.0667, "x": 58.55, "y": 7.87}, {"time": 3.1333, "x": 57.84, "y": -10.54}, {"time": 3.2, "x": 58.55, "y": 7.87}, {"time": 3.2667, "x": 57.84, "y": -10.54}, {"time": 3.3333, "x": 58.55, "y": 7.87}, {"time": 3.4, "x": 57.84, "y": -10.54}, {"time": 3.7333, "x": 14.91, "y": -37.33}, {"time": 3.8, "x": 14.2, "y": -55.74}, {"time": 3.8667, "x": 14.91, "y": -37.33}, {"time": 3.9333, "x": 14.2, "y": -55.74}, {"time": 4, "x": 14.91, "y": -37.33}, {"time": 4.0667, "x": 14.2, "y": -55.74}, {"time": 4.1333, "x": 14.91, "y": -37.33}, {"time": 4.2, "x": 14.2, "y": -55.74}, {"time": 4.2667, "x": 14.91, "y": -37.33}, {"time": 4.3333, "x": 14.2, "y": -55.74}, {"time": 4.4, "x": 14.91, "y": -37.33}, {"time": 4.4667, "x": 14.2, "y": -55.74}, {"time": 4.5333, "x": 14.91, "y": -37.33}, {"time": 4.6, "x": 14.2, "y": -55.74}, {"time": 4.6667, "x": 14.91, "y": -37.33}, {"time": 4.7333, "x": 14.2, "y": -55.74}, {"time": 4.8, "x": 14.91, "y": -37.33}, {"time": 4.8667, "x": 14.2, "y": -55.74}, {"time": 5.1333, "x": -14.85, "y": 40.98}, {"time": 5.2, "x": -14.13, "y": 59.39}, {"time": 5.2667, "x": -14.85, "y": 40.98}, {"time": 5.3333, "x": -14.13, "y": 59.39}, {"time": 5.4, "x": -14.85, "y": 40.98}, {"time": 5.4667, "x": -14.13, "y": 59.39}, {"time": 5.5333, "x": -14.85, "y": 40.98}, {"time": 5.6, "x": -14.13, "y": 59.39}, {"time": 6.0333, "x": 57.84, "y": -10.54, "curve": [6.117, 57.84, 6.283, -82.92, 6.117, -10.54, 6.283, -3.42]}, {"time": 6.3667, "x": -82.92, "y": -3.42, "curve": [6.467, -82.92, 6.667, -66.74, 6.467, -3.42, 6.667, -4.28]}, {"time": 6.7667, "x": -66.74, "y": -4.28}]}, "NeckLeaf1": {"rotate": [{"value": -11.45}]}, "NeckLeaf5": {"rotate": [{"value": 0.17}]}, "NeckLeaf3": {"rotate": [{"value": 12.29}]}, "NeckLeaf4": {"rotate": [{"value": 8.95}]}, "BodyTop": {"scale": [{"x": 0.991, "y": 1.014, "curve": [0.175, 0.991, 0.079, 0.903, 0.175, 1.014, 0.079, 1.076]}, {"time": 0.7, "x": 0.903, "y": 1.076, "curve": [0.783, 0.903, 0.95, 1.09, 0.783, 1.076, 0.95, 0.894]}, {"time": 1.0333, "x": 1.09, "y": 0.894, "curve": [1.125, 1.09, 1.308, 0.92, 1.125, 0.894, 1.308, 1.085]}, {"time": 1.4, "x": 0.92, "y": 1.085, "curve": [1.592, 0.92, 1.975, 1, 1.592, 1.085, 1.975, 0.949]}, {"time": 2.1667, "y": 0.949, "curve": "stepped"}, {"time": 6.0667, "y": 0.949, "curve": [6.117, 1, 6.217, 0.86, 6.117, 0.949, 6.217, 1.076]}, {"time": 6.2667, "x": 0.86, "y": 1.076, "curve": [6.35, 0.86, 6.517, 0.908, 6.35, 1.076, 6.517, 1.053]}, {"time": 6.6, "x": 0.908, "y": 1.053, "curve": [6.642, 0.908, 6.725, 0.89, 6.642, 1.053, 6.725, 1.061]}, {"time": 6.7667, "x": 0.89, "y": 1.061, "curve": [6.95, 0.89, 7.41, 0.821, 6.95, 1.061, 7.41, 1.137]}, {"time": 7.5, "x": 0.782, "y": 1.18}]}, "Cloak_Right1": {"rotate": [{"curve": [0.167, 0, 0.141, 11.89]}, {"time": 0.6667, "value": 11.89, "curve": [0.75, 11.89, 0.737, 1.51]}, {"time": 1, "value": 1.51, "curve": [1.133, 1.51, 1.4, 9.05]}, {"time": 1.5333, "value": 9.05, "curve": [1.625, 9.05, 1.808, -0.37]}, {"time": 1.9, "value": -0.37, "curve": [1.992, -0.37, 2.175, 9.05]}, {"time": 2.2667, "value": 9.05, "curve": [2.358, 9.05, 2.542, -0.37]}, {"time": 2.6333, "value": -0.37, "curve": [2.742, -0.37, 2.958, 9.05]}, {"time": 3.0667, "value": 9.05, "curve": [3.175, 9.05, 3.392, -0.37]}, {"time": 3.5, "value": -0.37, "curve": [3.617, -0.37, 3.85, 9.05]}, {"time": 3.9667, "value": 9.05, "curve": [4.075, 9.05, 4.292, -0.37]}, {"time": 4.4, "value": -0.37, "curve": [4.508, -0.37, 4.725, 9.05]}, {"time": 4.8333, "value": 9.05, "curve": [4.942, 9.05, 5.158, -0.37]}, {"time": 5.2667, "value": -0.37, "curve": [5.375, -0.37, 5.592, 9.05]}, {"time": 5.7, "value": 9.05, "curve": [5.808, 9.05, 6.025, -0.37]}, {"time": 6.1333, "value": -0.37, "curve": [6.158, -0.37, 6.208, 0]}, {"time": 6.2333}], "scale": [{"curve": [0.167, 1, 0.141, 1.119, 0.167, 1, 0.141, 1]}, {"time": 0.6667, "x": 1.119, "curve": [0.75, 1.119, 0.737, 1, 0.75, 1, 0.737, 1]}, {"time": 1, "curve": "stepped"}, {"time": 6}, {"time": 6.2333, "x": 1.119, "curve": "stepped"}, {"time": 6.6667, "x": 1.119, "curve": [6.875, 1.119, 7.292, 1.25, 6.875, 1, 7.292, 1]}, {"time": 7.5, "x": 1.25}]}, "Cloak_Right2": {"rotate": [{"curve": [0.167, 0, 0.196, -6.12]}, {"time": 0.6667, "value": -6.12, "curve": [0.75, -6.12, 0.766, -12.16]}, {"time": 1, "value": -12.16, "curve": [1.183, -12.16, 1.55, 14.04]}, {"time": 1.7333, "value": 14.04, "curve": [1.825, 14.04, 2.008, -3.95]}, {"time": 2.1, "value": -3.95, "curve": [2.192, -3.95, 2.375, 5.47]}, {"time": 2.4667, "value": 5.47, "curve": [2.558, 5.47, 2.742, -3.95]}, {"time": 2.8333, "value": -3.95, "curve": [2.942, -3.95, 3.158, 17.47]}, {"time": 3.2667, "value": 17.47, "curve": [3.375, 17.47, 3.592, -3.95]}, {"time": 3.7, "value": -3.95, "curve": [3.808, -3.95, 4.025, 5.47]}, {"time": 4.1333, "value": 5.47, "curve": [4.242, 5.47, 4.458, -3.95]}, {"time": 4.5667, "value": -3.95, "curve": [4.675, -3.95, 4.892, 5.47]}, {"time": 5, "value": 5.47, "curve": [5.108, 5.47, 5.325, -3.95]}, {"time": 5.4333, "value": -3.95, "curve": [5.542, -3.95, 5.758, 5.47]}, {"time": 5.8667, "value": 5.47, "curve": [5.932, 5.47, 6.037, 2]}, {"time": 6.1333, "value": -0.67, "curve": [6.172, -0.3, 6.207, 0]}, {"time": 6.2333}]}, "Cloak_Right3": {"rotate": [{"curve": [0.167, 0, 0.196, -5.28]}, {"time": 0.6667, "value": -5.28, "curve": [0.75, -5.28, 0.766, 7.88]}, {"time": 1, "value": 7.88, "curve": [1.075, 7.88, 1.225, 3.53]}, {"time": 1.3, "value": 3.53}, {"time": 1.7667, "value": 25.27, "curve": [1.858, 25.27, 2.042, -16.63]}, {"time": 2.1333, "value": -16.63, "curve": [2.225, -16.63, 2.408, 14.6]}, {"time": 2.5, "value": 14.6, "curve": [2.592, 14.6, 2.775, -16.63]}, {"time": 2.8667, "value": -16.63, "curve": [2.975, -16.63, 3.192, 24.05]}, {"time": 3.3, "value": 24.05, "curve": [3.408, 24.05, 3.625, -16.63]}, {"time": 3.7333, "value": -16.63, "curve": [3.842, -16.63, 4.058, 14.6]}, {"time": 4.1667, "value": 14.6, "curve": [4.275, 14.6, 4.492, -16.63]}, {"time": 4.6, "value": -16.63, "curve": [4.708, -16.63, 4.925, 14.6]}, {"time": 5.0333, "value": 14.6, "curve": [5.142, 14.6, 5.358, -16.63]}, {"time": 5.4667, "value": -16.63, "curve": [5.575, -16.63, 5.792, 40.59]}, {"time": 5.9, "value": 40.59, "curve": [5.958, 40.59, 6.047, 18.13]}, {"time": 6.1333, "value": -2.6, "curve": [6.171, -1.25, 6.208, 0]}, {"time": 6.2333}]}, "Cloak_Right4": {"rotate": [{"curve": [0.167, 0, 0.196, 1.16]}, {"time": 0.6667, "value": 1.16, "curve": [0.75, 1.16, 0.766, 10.19]}, {"time": 1, "value": 10.19, "curve": [1.1, 10.19, 1.3, -10.15]}, {"time": 1.4, "value": -10.15}, {"time": 1.9, "value": 17.5, "curve": [1.992, 17.5, 2.175, -14.64]}, {"time": 2.2667, "value": -14.64, "curve": [2.358, -14.64, 2.542, 17.5]}, {"time": 2.6333, "value": 17.5, "curve": [2.662, 17.5, 2.696, 16.42]}, {"time": 2.7333, "value": 14.69, "curve": [2.84, 6.31, 2.986, -14.64]}, {"time": 3.0667, "value": -14.64, "curve": [3.175, -14.64, 3.392, 48.71]}, {"time": 3.5, "value": 48.71, "curve": [3.617, 48.71, 3.85, -14.64]}, {"time": 3.9667, "value": -14.64, "curve": [4.075, -14.64, 4.292, 17.5]}, {"time": 4.4, "value": 17.5, "curve": [4.508, 17.5, 4.725, -14.64]}, {"time": 4.8333, "value": -14.64, "curve": [4.942, -14.64, 5.158, 17.5]}, {"time": 5.2667, "value": 17.5, "curve": [5.375, 17.5, 5.592, -14.64]}, {"time": 5.7, "value": -14.64, "curve": [5.808, -14.64, 6.025, 17.5]}, {"time": 6.1333, "value": 17.5, "curve": [6.158, 17.5, 6.208, 0]}, {"time": 6.2333}]}, "Cloak_Left1": {"rotate": [{"curve": [0.167, 0, 0.141, -7.62]}, {"time": 0.6667, "value": -7.62, "curve": [0.75, -7.62, 0.737, 3.13]}, {"time": 1, "value": 3.13, "curve": [1.133, 3.13, 1.4, -9.48]}, {"time": 1.5333, "value": -9.48, "curve": [1.625, -9.48, 1.808, 3.09]}, {"time": 1.9, "value": 3.09, "curve": [1.992, 3.09, 2.175, -9.48]}, {"time": 2.2667, "value": -9.48, "curve": [2.358, -9.48, 2.542, 3.09]}, {"time": 2.6333, "value": 3.09, "curve": [2.742, 3.09, 2.958, -9.48]}, {"time": 3.0667, "value": -9.48, "curve": [3.175, -9.48, 3.392, 3.09]}, {"time": 3.5, "value": 3.09, "curve": [3.617, 3.09, 3.85, -9.48]}, {"time": 3.9667, "value": -9.48, "curve": [4.075, -9.48, 4.292, 3.09]}, {"time": 4.4, "value": 3.09, "curve": [4.508, 3.09, 4.725, -9.48]}, {"time": 4.8333, "value": -9.48, "curve": [4.942, -9.48, 5.158, 3.09]}, {"time": 5.2667, "value": 3.09, "curve": [5.375, 3.09, 5.592, -9.48]}, {"time": 5.7, "value": -9.48, "curve": [5.808, -9.48, 6.025, 3.09]}, {"time": 6.1333, "value": 3.09, "curve": [6.158, 3.09, 6.208, 0]}, {"time": 6.2333}], "scale": [{"curve": [0.167, 1, 0.141, 1.119, 0.167, 1, 0.141, 1]}, {"time": 0.6667, "x": 1.119, "curve": [0.75, 1.119, 0.737, 1, 0.75, 1, 0.737, 1]}, {"time": 1, "curve": "stepped"}, {"time": 6}, {"time": 6.2333, "x": 1.119, "curve": "stepped"}, {"time": 6.6667, "x": 1.119, "curve": [6.875, 1.119, 7.292, 1.25, 6.875, 1, 7.292, 1]}, {"time": 7.5, "x": 1.25}]}, "Cloak_Left2": {"rotate": [{"curve": [0.167, 0, 0.196, 2.33]}, {"time": 0.6667, "value": 2.33, "curve": [0.75, 2.33, 0.766, 3.62]}, {"time": 1, "value": 3.62, "curve": [1.183, 3.62, 1.55, -15.32]}, {"time": 1.7333, "value": -15.32, "curve": [1.825, -15.32, 2.008, 5.94]}, {"time": 2.1, "value": 5.94, "curve": [2.192, 5.94, 2.375, -6.63]}, {"time": 2.4667, "value": -6.63, "curve": [2.558, -6.63, 2.742, 5.94]}, {"time": 2.8333, "value": 5.94, "curve": [2.942, 5.94, 3.158, -16.77]}, {"time": 3.2667, "value": -16.77, "curve": [3.375, -16.77, 3.592, 5.94]}, {"time": 3.7, "value": 5.94, "curve": [3.808, 5.94, 4.025, -6.63]}, {"time": 4.1333, "value": -6.63, "curve": [4.242, -6.63, 4.458, 5.94]}, {"time": 4.5667, "value": 5.94, "curve": [4.675, 5.94, 4.892, -6.63]}, {"time": 5, "value": -6.63, "curve": [5.108, -6.63, 5.325, 5.94]}, {"time": 5.4333, "value": 5.94, "curve": [5.542, 5.94, 5.758, -6.63]}, {"time": 5.8667, "value": -6.63, "curve": [5.932, -6.63, 6.037, -1.99]}, {"time": 6.1333, "value": 1.57, "curve": [6.172, 0.7, 6.207, 0]}, {"time": 6.2333}]}, "Cloak_Left3": {"rotate": [{"curve": [0.167, 0, 0.196, 4.81]}, {"time": 0.6667, "value": 4.81, "curve": [0.75, 4.81, 0.766, -10.23]}, {"time": 1, "value": -10.23, "curve": [1.075, -10.23, 1.225, -1.56]}, {"time": 1.3, "value": -1.56}, {"time": 1.7667, "value": -28.29, "curve": [1.858, -28.29, 2.042, 10.98]}, {"time": 2.1333, "value": 10.98, "curve": [2.225, 10.98, 2.408, -17.68]}, {"time": 2.5, "value": -17.68, "curve": [2.592, -17.68, 2.775, 10.98]}, {"time": 2.8667, "value": 10.98, "curve": [2.975, 10.98, 3.192, -28.24]}, {"time": 3.3, "value": -28.24, "curve": [3.408, -28.24, 3.625, 10.98]}, {"time": 3.7333, "value": 10.98, "curve": [3.842, 10.98, 4.058, -17.68]}, {"time": 4.1667, "value": -17.68, "curve": [4.275, -17.68, 4.492, 10.98]}, {"time": 4.6, "value": 10.98, "curve": [4.708, 10.98, 4.925, -17.68]}, {"time": 5.0333, "value": -17.68, "curve": [5.142, -17.68, 5.358, 10.98]}, {"time": 5.4667, "value": 10.98, "curve": [5.575, 10.98, 5.792, -38.78]}, {"time": 5.9, "value": -38.78, "curve": [5.958, -38.78, 6.047, -19.6]}, {"time": 6.1333, "value": -1.89, "curve": [6.171, -0.91, 6.208, 0]}, {"time": 6.2333}]}, "Cloak_Left4": {"rotate": [{"time": 0.6667, "curve": [0.75, 0, 0.766, 0.05]}, {"time": 1, "value": 0.05, "curve": [1.125, 0.05, 1.375, 13.26]}, {"time": 1.5, "value": 13.26}, {"time": 2, "value": -19.31, "curve": [2.092, -19.31, 2.275, 15.26]}, {"time": 2.3667, "value": 15.26, "curve": [2.458, 15.26, 2.642, -19.31]}, {"time": 2.7333, "value": -19.31, "curve": [2.842, -19.31, 3.058, 15.26]}, {"time": 3.1667, "value": 15.26, "curve": [3.265, 15.26, 3.454, -25.1]}, {"time": 3.5667, "value": -31.31, "curve": [3.578, -23.52, 3.59, -19.31]}, {"time": 3.6, "value": -19.31, "curve": [3.717, -19.31, 3.95, 15.26]}, {"time": 4.0667, "value": 15.26, "curve": [4.175, 15.26, 4.392, -19.31]}, {"time": 4.5, "value": -19.31, "curve": [4.608, -19.31, 4.825, 15.26]}, {"time": 4.9333, "value": 15.26, "curve": [5.042, 15.26, 5.258, -19.31]}, {"time": 5.3667, "value": -19.31, "curve": [5.475, -19.31, 5.692, 15.26]}, {"time": 5.8, "value": 15.26, "curve": [5.883, 15.26, 6.05, -19.31]}, {"time": 6.1333, "value": -19.31, "curve": [6.158, -19.31, 6.208, 0]}, {"time": 6.2333}]}, "CloakWave6": {"translate": [{"time": 1.0333, "curve": [1.175, 0, 1.458, 91.56, 1.175, 0, 1.458, -0.79]}, {"time": 1.6, "x": 91.56, "y": -0.79, "curve": [1.717, 91.56, 1.95, -53.67, 1.717, -0.79, 1.95, 0.46]}, {"time": 2.0667, "x": -53.67, "y": 0.46, "curve": [2.175, -53.67, 2.392, 91.56, 2.175, 0.46, 2.392, -0.79]}, {"time": 2.5, "x": 91.56, "y": -0.79, "curve": [2.617, 91.56, 2.85, -53.67, 2.617, -0.79, 2.85, 0.46]}, {"time": 2.9667, "x": -53.67, "y": 0.46, "curve": [3.083, -53.67, 3.317, 91.56, 3.083, 0.46, 3.317, -0.79]}, {"time": 3.4333, "x": 91.56, "y": -0.79, "curve": [3.542, 91.56, 3.758, -53.67, 3.542, -0.79, 3.758, 0.46]}, {"time": 3.8667, "x": -53.67, "y": 0.46, "curve": [3.983, -53.67, 4.217, 91.56, 3.983, 0.46, 4.217, -0.79]}, {"time": 4.3333, "x": 91.56, "y": -0.79, "curve": [4.442, 91.56, 4.658, -53.67, 4.442, -0.79, 4.658, 0.46]}, {"time": 4.7667, "x": -53.67, "y": 0.46, "curve": [4.883, -53.67, 5.117, 91.56, 4.883, 0.46, 5.117, -0.79]}, {"time": 5.2333, "x": 91.56, "y": -0.79, "curve": [5.342, 91.56, 5.558, -53.67, 5.342, -0.79, 5.558, 0.46]}, {"time": 5.6667, "x": -53.67, "y": 0.46, "curve": [5.783, -53.67, 6.017, 91.56, 5.783, 0.46, 6.017, -0.79]}, {"time": 6.1333, "x": 91.56, "y": -0.79, "curve": [6.183, 91.56, 6.283, 0, 6.183, -0.79, 6.283, 0]}, {"time": 6.3333}]}, "CloakWave5": {"translate": [{"time": 0.6333}, {"time": 0.9667, "x": -87.08, "y": 10.16}, {"time": 1.3, "curve": [1.417, 0, 1.65, 91.56, 1.417, 0, 1.65, -0.79]}, {"time": 1.7667, "x": 91.56, "y": -0.79, "curve": [1.883, 91.56, 2.117, -53.67, 1.883, -0.79, 2.117, 0.46]}, {"time": 2.2333, "x": -53.67, "y": 0.46, "curve": [2.342, -53.67, 2.558, 91.56, 2.342, 0.46, 2.558, -0.79]}, {"time": 2.6667, "x": 91.56, "y": -0.79, "curve": [2.783, 91.56, 3.017, -53.67, 2.783, -0.79, 3.017, 0.46]}, {"time": 3.1333, "x": -53.67, "y": 0.46, "curve": [3.25, -53.67, 3.483, 91.56, 3.25, 0.46, 3.483, -0.79]}, {"time": 3.6, "x": 91.56, "y": -0.79, "curve": [3.708, 91.56, 3.925, -53.67, 3.708, -0.79, 3.925, 0.46]}, {"time": 4.0333, "x": -53.67, "y": 0.46, "curve": [4.15, -53.67, 4.383, 91.56, 4.15, 0.46, 4.383, -0.79]}, {"time": 4.5, "x": 91.56, "y": -0.79, "curve": [4.617, 91.56, 4.85, -53.67, 4.617, -0.79, 4.85, 0.46]}, {"time": 4.9667, "x": -53.67, "y": 0.46, "curve": [5.075, -53.67, 5.292, 91.56, 5.075, 0.46, 5.292, -0.79]}, {"time": 5.4, "x": 91.56, "y": -0.79, "curve": [5.517, 91.56, 5.75, -53.67, 5.517, -0.79, 5.75, 0.46]}, {"time": 5.8667, "x": -53.67, "y": 0.46, "curve": [5.925, -53.67, 6.042, 0, 5.925, 0.46, 6.042, 0]}, {"time": 6.1}], "scale": [{"time": 4.3667}]}, "CloakWave4": {"translate": [{"time": 0.7333}, {"time": 0.9667, "x": -130.77, "y": -1.8}, {"time": 1.4667, "curve": [1.583, 0, 1.817, 91.56, 1.583, 0, 1.817, -0.79]}, {"time": 1.9333, "x": 91.56, "y": -0.79, "curve": [2.05, 91.56, 2.283, -53.67, 2.05, -0.79, 2.283, 0.46]}, {"time": 2.4, "x": -53.67, "y": 0.46, "curve": [2.508, -53.67, 2.725, 91.56, 2.508, 0.46, 2.725, -0.79]}, {"time": 2.8333, "x": 91.56, "y": -0.79, "curve": [2.95, 91.56, 3.183, -53.67, 2.95, -0.79, 3.183, 0.46]}, {"time": 3.3, "x": -53.67, "y": 0.46, "curve": [3.408, -53.67, 3.625, 91.56, 3.408, 0.46, 3.625, -0.79]}, {"time": 3.7333, "x": 91.56, "y": -0.79, "curve": [3.842, 91.56, 4.058, -53.67, 3.842, -0.79, 4.058, 0.46]}, {"time": 4.1667, "x": -53.67, "y": 0.46, "curve": [4.283, -53.67, 4.517, 91.56, 4.283, 0.46, 4.517, -0.79]}, {"time": 4.6333, "x": 91.56, "y": -0.79, "curve": [4.742, 91.56, 4.958, -53.67, 4.742, -0.79, 4.958, 0.46]}, {"time": 5.0667, "x": -53.67, "y": 0.46, "curve": [5.183, -53.67, 5.417, 91.56, 5.183, 0.46, 5.417, -0.79]}, {"time": 5.5333, "x": 91.56, "y": -0.79, "curve": [5.65, 91.56, 5.883, -53.67, 5.65, -0.79, 5.883, 0.46]}, {"time": 6, "x": -53.67, "y": 0.46, "curve": [6.025, -53.67, 6.075, 0, 6.025, 0.46, 6.075, 0]}, {"time": 6.1}]}, "CloakWave3": {"translate": [{"time": 0.7333, "curve": [0.792, 0, 0.908, -130.78, 0.792, 0, 0.908, -1.8]}, {"time": 0.9667, "x": -130.78, "y": -1.8}, {"time": 1.3667, "curve": [1.483, 0, 1.717, 90.88, 1.483, 0, 1.717, -36.57]}, {"time": 1.8333, "x": 90.88, "y": -36.57, "curve": [1.95, 90.88, 2.183, -53.67, 1.95, -36.57, 2.183, 0.46]}, {"time": 2.3, "x": -53.67, "y": 0.46, "curve": [2.408, -53.67, 2.625, 90.88, 2.408, 0.46, 2.625, -36.57]}, {"time": 2.7333, "x": 90.88, "y": -36.57, "curve": [2.85, 90.88, 3.083, -53.67, 2.85, -36.57, 3.083, 0.46]}, {"time": 3.2, "x": -53.67, "y": 0.46, "curve": [3.308, -53.67, 3.525, 90.88, 3.308, 0.46, 3.525, -36.57]}, {"time": 3.6333, "x": 90.88, "y": -36.57, "curve": [3.75, 90.88, 3.983, -53.67, 3.75, -36.57, 3.983, 0.46]}, {"time": 4.1, "x": -53.67, "y": 0.46, "curve": [4.217, -53.67, 4.45, 90.88, 4.217, 0.46, 4.45, -36.57]}, {"time": 4.5667, "x": 90.88, "y": -36.57, "curve": [4.675, 90.88, 4.892, -53.67, 4.675, -36.57, 4.892, 0.46]}, {"time": 5, "x": -53.67, "y": 0.46, "curve": [5.117, -53.67, 5.35, 90.88, 5.117, 0.46, 5.35, -36.57]}, {"time": 5.4667, "x": 90.88, "y": -36.57, "curve": [5.583, 90.88, 5.817, -53.67, 5.583, -36.57, 5.817, 0.46]}, {"time": 5.9333, "x": -53.67, "y": 0.46, "curve": [5.975, -53.67, 6.058, 0, 5.975, 0.46, 6.058, 0]}, {"time": 6.1}]}, "CloakWave1": {"translate": [{"time": 0.6333, "curve": [0.733, 0, 0.933, -0.64, 0.733, 0, 0.933, -79.24]}, {"time": 1.0333, "x": -0.64, "y": -79.24, "curve": [1.15, -0.64, 1.383, 90.76, 1.15, -79.24, 1.383, -34.03]}, {"time": 1.5, "x": 90.76, "y": -34.03, "curve": [1.608, 90.76, 1.825, -53.67, 1.608, -34.03, 1.825, 0.46]}, {"time": 1.9333, "x": -53.67, "y": 0.46, "curve": [2.05, -53.67, 2.283, 90.76, 2.05, 0.46, 2.283, -34.03]}, {"time": 2.4, "x": 90.76, "y": -34.03, "curve": [2.517, 90.76, 2.75, -53.67, 2.517, -34.03, 2.75, 0.46]}, {"time": 2.8667, "x": -53.67, "y": 0.46, "curve": [2.975, -53.67, 3.192, 90.76, 2.975, 0.46, 3.192, -34.03]}, {"time": 3.3, "x": 90.76, "y": -34.03, "curve": [3.417, 90.76, 3.65, -53.67, 3.417, -34.03, 3.65, 0.46]}, {"time": 3.7667, "x": -53.67, "y": 0.46, "curve": [3.883, -53.67, 4.117, 90.76, 3.883, 0.46, 4.117, -34.03]}, {"time": 4.2333, "x": 90.76, "y": -34.03, "curve": [4.342, 90.76, 4.558, -53.67, 4.342, -34.03, 4.558, 0.46]}, {"time": 4.6667, "x": -53.67, "y": 0.46, "curve": [4.783, -53.67, 5.017, 90.76, 4.783, 0.46, 5.017, -34.03]}, {"time": 5.1333, "x": 90.76, "y": -34.03, "curve": [5.242, 90.76, 5.458, -53.67, 5.242, -34.03, 5.458, 0.46]}, {"time": 5.5667, "x": -53.67, "y": 0.46, "curve": [5.683, -53.67, 5.917, 90.76, 5.683, 0.46, 5.917, -34.03]}, {"time": 6.0333, "x": 90.76, "y": -34.03, "curve": [6.108, 90.76, 6.258, 0, 6.108, -34.03, 6.258, 0]}, {"time": 6.3333}]}, "CloakWave2": {"translate": [{"time": 0.6333, "curve": [0.714, 0, 0.86, -62.74, 0.714, 0, 0.86, -58.4]}, {"time": 0.9667, "x": -87.84, "y": -81.76, "curve": [1.054, -33.78, 1.134, 0, 1.054, -31.45, 1.134, 0]}, {"time": 1.2, "curve": [1.317, 0, 1.55, 90.73, 1.317, 0, 1.55, -40.25]}, {"time": 1.6667, "x": 90.73, "y": -40.25, "curve": [1.783, 90.73, 2.017, -53.67, 1.783, -40.25, 2.017, 0.46]}, {"time": 2.1333, "x": -53.67, "y": 0.46, "curve": [2.242, -53.67, 2.458, 90.73, 2.242, 0.46, 2.458, -40.25]}, {"time": 2.5667, "x": 90.73, "y": -40.25, "curve": [2.683, 90.73, 2.917, -53.67, 2.683, -40.25, 2.917, 0.46]}, {"time": 3.0333, "x": -53.67, "y": 0.46, "curve": [3.15, -53.67, 3.383, 90.73, 3.15, 0.46, 3.383, -40.25]}, {"time": 3.5, "x": 90.73, "y": -40.25, "curve": [3.608, 90.73, 3.825, -53.67, 3.608, -40.25, 3.825, 0.46]}, {"time": 3.9333, "x": -53.67, "y": 0.46, "curve": [4.05, -53.67, 4.283, 90.73, 4.05, 0.46, 4.283, -40.25]}, {"time": 4.4, "x": 90.73, "y": -40.25, "curve": [4.517, 90.73, 4.75, -53.67, 4.517, -40.25, 4.75, 0.46]}, {"time": 4.8667, "x": -53.67, "y": 0.46, "curve": [4.975, -53.67, 5.192, 90.73, 4.975, 0.46, 5.192, -40.25]}, {"time": 5.3, "x": 90.73, "y": -40.25, "curve": [5.417, 90.73, 5.65, -53.67, 5.417, -40.25, 5.65, 0.46]}, {"time": 5.7667, "x": -53.67, "y": 0.46, "curve": [5.85, -53.67, 6.017, 0, 5.85, 0.46, 6.017, 0]}, {"time": 6.1}]}, "CrownHolder": {"rotate": [{"time": 1.0667, "value": 90.86}], "translate": [{"time": 1.0667, "x": 4.38, "y": -51.36, "curve": [1.141, 2.86, 1.367, 2.44, 1.141, -6.32, 1.367, 6.13]}, {"time": 1.4667, "x": 2.44, "y": 6.13, "curve": [1.625, 2.44, 1.942, 2.25, 1.625, 6.13, 1.942, -39.01]}, {"time": 2.1, "x": 2.25, "y": -39.01, "curve": [2.267, 2.25, 2.6, 2.44, 2.267, -39.01, 2.6, 6.13]}, {"time": 2.7667, "x": 2.44, "y": 6.13, "curve": [2.933, 2.44, 3.267, 2.25, 2.933, 6.13, 3.267, -39.01]}, {"time": 3.4333, "x": 2.25, "y": -39.01, "curve": [3.617, 2.25, 3.983, 2.44, 3.617, -39.01, 3.983, 6.13]}, {"time": 4.1667, "x": 2.44, "y": 6.13, "curve": [4.35, 2.44, 4.717, 2.25, 4.35, 6.13, 4.717, -39.01]}, {"time": 4.9, "x": 2.25, "y": -39.01, "curve": [5.083, 2.25, 5.45, 2.44, 5.083, -39.01, 5.45, 6.13]}, {"time": 5.6333, "x": 2.44, "y": 6.13, "curve": [5.725, 2.44, 5.908, 5.6, 5.725, 6.13, 5.908, -39.01]}, {"time": 6, "x": 5.6, "y": -39.01, "curve": [6.158, 5.6, 6.59, 3.91, 6.158, -39.01, 6.59, -201.29]}, {"time": 6.6333, "x": 2.25, "y": -360.42}]}}, "events": [{"time": 7.5, "name": "transform"}]}, "transform2": {"slots": {"CrownEye": {"attachment": [{"time": 0.7, "name": "CrownEye_Closed"}, {"time": 1.5333, "name": "CrownEye"}, {"time": 4.0333, "name": "CrownEye_Closed"}, {"time": 4.8667, "name": "CrownEye"}]}, "CrownGrass": {"attachment": [{"time": 1.4333}]}, "MASK": {"attachment": [{"time": 2.2333, "name": "MASK"}]}}, "bones": {"CrownEye": {"scale": [{"time": 0.7, "curve": [0.725, 1, 0.775, 0.815, 0.725, 1, 0.775, 1.097]}, {"time": 0.8, "x": 0.815, "y": 1.097, "curve": [0.833, 0.815, 0.9, 1, 0.833, 1.097, 0.9, 1]}, {"time": 0.9333, "curve": "stepped"}, {"time": 1.5333, "curve": [1.558, 1, 1.608, 0.815, 1.558, 1, 1.608, 1.097]}, {"time": 1.6333, "x": 0.815, "y": 1.097, "curve": [1.667, 0.815, 1.733, 1, 1.667, 1.097, 1.733, 1]}, {"time": 1.7667, "curve": "stepped"}, {"time": 4.0333, "curve": [4.058, 1, 4.108, 0.815, 4.058, 1, 4.108, 1.097]}, {"time": 4.1333, "x": 0.815, "y": 1.097, "curve": [4.167, 0.815, 4.233, 1, 4.167, 1.097, 4.233, 1]}, {"time": 4.2667, "curve": "stepped"}, {"time": 4.8667, "curve": [4.892, 1, 4.942, 0.815, 4.892, 1, 4.942, 1.097]}, {"time": 4.9667, "x": 0.815, "y": 1.097, "curve": [5, 0.815, 5.067, 1, 5, 1.097, 5.067, 1]}, {"time": 5.1}]}, "Crown": {"translate": [{"time": 1.4333}, {"time": 2.1, "x": 297.99, "y": -12.37}]}, "DangleHandle2": {"translate": [{"x": 0.1, "y": 2.85, "curve": [0.229, 0.17, 0.443, 0.23, 0.229, 4.82, 0.443, 6.38]}, {"time": 0.6, "x": 0.23, "y": 6.38, "curve": [1.025, 0.23, 1.875, -0.17, 1.025, 6.38, 1.875, -4.47]}, {"time": 2.3, "x": -0.17, "y": -4.47, "curve": [2.552, -0.17, 2.964, -0.01, 2.552, -4.47, 2.964, -0.25]}, {"time": 3.3333, "x": 0.1, "y": 2.85, "curve": [3.563, 0.17, 3.777, 0.23, 3.563, 4.82, 3.777, 6.38]}, {"time": 3.9333, "x": 0.23, "y": 6.38, "curve": [4.358, 0.23, 5.208, -0.17, 4.358, 6.38, 5.208, -4.47]}, {"time": 5.6333, "x": -0.17, "y": -4.47, "curve": [5.886, -0.17, 6.297, -0.01, 5.886, -4.47, 6.297, -0.25]}, {"time": 6.6667, "x": 0.1, "y": 2.85}]}, "DangleHandle1": {"translate": [{"curve": [0.279, -0.09, 0.546, -0.17, 0.279, -2.37, 0.546, -4.47]}, {"time": 0.7333, "x": -0.17, "y": -4.47, "curve": [1.15, -0.17, 1.983, 0.23, 1.15, -4.47, 1.983, 6.38]}, {"time": 2.4, "x": 0.23, "y": 6.38, "curve": [2.63, 0.23, 2.99, 0.1, 2.63, 6.38, 2.99, 2.96]}, {"time": 3.3333, "curve": [3.612, -0.09, 3.88, -0.17, 3.612, -2.37, 3.88, -4.47]}, {"time": 4.0667, "x": -0.17, "y": -4.47, "curve": [4.483, -0.17, 5.317, 0.23, 4.483, -4.47, 5.317, 6.38]}, {"time": 5.7333, "x": 0.23, "y": 6.38, "curve": [5.963, 0.23, 6.324, 0.1, 5.963, 6.38, 6.324, 2.96]}, {"time": 6.6667}]}, "Face": {"translate": [{"x": 5.65, "y": -0.19, "curve": [0.217, 10.77, 0.416, 14.55, 0.217, -0.37, 0.416, -0.5]}, {"time": 0.5667, "x": 14.55, "y": -0.5, "curve": [0.967, 14.55, 1.767, -18.38, 0.967, -0.5, 1.767, 0.65]}, {"time": 2.1667, "x": -18.38, "y": 0.65, "curve": [2.217, -18.38, 2.317, -16.68, 2.217, 0.65, 2.317, 20.01]}, {"time": 2.3667, "x": -16.68, "y": 20.01, "curve": [2.408, -16.68, 2.492, -11.49, 2.408, 20.01, 2.492, -34.65]}, {"time": 2.5333, "x": -11.49, "y": -34.65, "curve": [2.575, -11.49, 2.658, -16.68, 2.575, -34.65, 2.658, 20.01]}, {"time": 2.7, "x": -16.68, "y": 20.01, "curve": [2.742, -16.68, 2.825, -11.49, 2.742, 20.01, 2.825, -12.74]}, {"time": 2.8667, "x": -11.49, "y": -12.74, "curve": [2.908, -11.49, 2.992, -5.16, 2.908, -12.74, 2.992, 0.19]}, {"time": 3.0333, "x": -5.16, "y": 0.19, "curve": [3.108, -5.16, 3.258, 5.65, 3.108, 0.19, 3.258, -0.19]}, {"time": 3.3333, "x": 5.65, "y": -0.19, "curve": [3.55, 10.77, 3.749, 14.55, 3.55, -0.37, 3.749, -0.5]}, {"time": 3.9, "x": 14.55, "y": -0.5, "curve": [4.3, 14.55, 5.1, -18.38, 4.3, -0.5, 5.1, 0.65]}, {"time": 5.5, "x": -18.38, "y": 0.65, "curve": [5.55, -18.38, 5.65, -16.68, 5.55, 0.65, 5.65, 20.01]}, {"time": 5.7, "x": -16.68, "y": 20.01, "curve": [5.742, -16.68, 5.825, -11.49, 5.742, 20.01, 5.825, -34.65]}, {"time": 5.8667, "x": -11.49, "y": -34.65, "curve": [5.908, -11.49, 5.992, -16.68, 5.908, -34.65, 5.992, 20.01]}, {"time": 6.0333, "x": -16.68, "y": 20.01, "curve": [6.075, -16.68, 6.158, -11.49, 6.075, 20.01, 6.158, -12.74]}, {"time": 6.2, "x": -11.49, "y": -12.74, "curve": [6.242, -11.49, 6.325, -5.16, 6.242, -12.74, 6.325, 0.19]}, {"time": 6.3667, "x": -5.16, "y": 0.19, "curve": [6.442, -5.16, 6.592, 5.65, 6.442, 0.19, 6.592, -0.19]}, {"time": 6.6667, "x": 5.65, "y": -0.19}], "scale": [{"curve": [0.033, 1, 0.1, 0.856, 0.033, 1, 0.1, 1.213]}, {"time": 0.1333, "x": 0.856, "y": 1.213, "curve": [0.183, 0.856, 0.283, 1, 0.183, 1.213, 0.283, 1]}, {"time": 0.3333, "curve": [0.367, 1, 0.433, 0.896, 0.367, 1, 0.433, 1.156]}, {"time": 0.4667, "x": 0.896, "y": 1.156, "curve": [0.517, 0.896, 0.617, 1, 0.517, 1.156, 0.617, 1]}, {"time": 0.6667, "curve": "stepped"}, {"time": 0.8333, "curve": [0.867, 1, 0.933, 0.856, 0.867, 1, 0.933, 1.213]}, {"time": 0.9667, "x": 0.856, "y": 1.213, "curve": [1.017, 0.856, 1.117, 1, 1.017, 1.213, 1.117, 1]}, {"time": 1.1667, "curve": "stepped"}, {"time": 1.3333, "curve": [1.367, 1, 1.433, 0.856, 1.367, 1, 1.433, 1.068]}, {"time": 1.4667, "x": 0.856, "y": 1.068, "curve": [1.5, 0.856, 1.567, 1, 1.5, 1.068, 1.567, 1]}, {"time": 1.6, "curve": [1.65, 1, 1.75, 0.889, 1.65, 1, 1.75, 1.156]}, {"time": 1.8, "x": 0.889, "y": 1.156, "curve": [1.85, 0.889, 1.95, 1, 1.85, 1.156, 1.95, 1]}, {"time": 2, "curve": "stepped"}, {"time": 3.3333, "curve": [3.367, 1, 3.433, 0.856, 3.367, 1, 3.433, 1.213]}, {"time": 3.4667, "x": 0.856, "y": 1.213, "curve": [3.517, 0.856, 3.617, 1, 3.517, 1.213, 3.617, 1]}, {"time": 3.6667, "curve": [3.7, 1, 3.767, 0.896, 3.7, 1, 3.767, 1.156]}, {"time": 3.8, "x": 0.896, "y": 1.156, "curve": [3.85, 0.896, 3.95, 1, 3.85, 1.156, 3.95, 1]}, {"time": 4, "curve": "stepped"}, {"time": 4.1667, "curve": [4.2, 1, 4.267, 0.856, 4.2, 1, 4.267, 1.213]}, {"time": 4.3, "x": 0.856, "y": 1.213, "curve": [4.35, 0.856, 4.45, 1, 4.35, 1.213, 4.45, 1]}, {"time": 4.5, "curve": "stepped"}, {"time": 4.6667, "curve": [4.7, 1, 4.767, 0.856, 4.7, 1, 4.767, 1.068]}, {"time": 4.8, "x": 0.856, "y": 1.068, "curve": [4.833, 0.856, 4.9, 1, 4.833, 1.068, 4.9, 1]}, {"time": 4.9333, "curve": [4.983, 1, 5.083, 0.889, 4.983, 1, 5.083, 1.156]}, {"time": 5.1333, "x": 0.889, "y": 1.156, "curve": [5.183, 0.889, 5.283, 1, 5.183, 1.156, 5.283, 1]}, {"time": 5.3333}]}, "Antler5": {"rotate": [{"value": -8, "curve": [0.386, -4.8, 0.832, 0]}, {"time": 1.1, "curve": [1.525, 0, 2.375, -11.17]}, {"time": 2.8, "value": -11.17, "curve": [2.941, -11.17, 3.129, -9.82]}, {"time": 3.3333, "value": -8, "curve": [3.72, -4.8, 4.166, 0]}, {"time": 4.4333, "curve": [4.858, 0, 5.708, -11.17]}, {"time": 6.1333, "value": -11.17, "curve": [6.275, -11.17, 6.463, -9.82]}, {"time": 6.6667, "value": -8}]}, "Antler4": {"rotate": [{"value": -5.59, "curve": [0.313, -2.79, 0.625, 0]}, {"time": 0.8333, "curve": [1.25, 0, 2.083, -11.17]}, {"time": 2.5, "value": -11.17, "curve": [2.708, -11.17, 3.021, -8.38]}, {"time": 3.3333, "value": -5.59, "curve": [3.646, -2.79, 3.958, 0]}, {"time": 4.1667, "curve": [4.583, 0, 5.417, -11.17]}, {"time": 5.8333, "value": -11.17, "curve": [6.042, -11.17, 6.354, -8.38]}, {"time": 6.6667, "value": -5.59}]}, "Antler3": {"rotate": [{"value": -3.17, "curve": [0.217, -1.36, 0.417, 0]}, {"time": 0.5667, "curve": [0.983, 0, 1.817, -11.17]}, {"time": 2.2333, "value": -11.17, "curve": [2.501, -11.17, 2.947, -6.37]}, {"time": 3.3333, "value": -3.17, "curve": [3.55, -1.36, 3.75, 0]}, {"time": 3.9, "curve": [4.317, 0, 5.15, -11.17]}, {"time": 5.5667, "value": -11.17, "curve": [5.834, -11.17, 6.28, -6.37]}, {"time": 6.6667, "value": -3.17}]}, "Antler2": {"rotate": [{"value": -1.08, "curve": [0.099, -0.4, 0.189, 0]}, {"time": 0.2667, "curve": [0.692, 0, 1.542, -11.17]}, {"time": 1.9667, "value": -11.17, "curve": [2.299, -11.17, 2.909, -3.6]}, {"time": 3.3333, "value": -1.08, "curve": [3.432, -0.4, 3.523, 0]}, {"time": 3.6, "curve": [4.025, 0, 4.875, -11.17]}, {"time": 5.3, "value": -11.17, "curve": [5.632, -11.17, 6.242, -3.6]}, {"time": 6.6667, "value": -1.08}]}, "Antler1": {"rotate": [{"curve": [0.417, 0, 1.25, -11.17]}, {"time": 1.6667, "value": -11.17, "curve": [2.083, -11.17, 2.917, 0]}, {"time": 3.3333, "curve": [3.75, 0, 4.583, -11.17]}, {"time": 5, "value": -11.17, "curve": [5.417, -11.17, 6.25, 0]}, {"time": 6.6667}]}, "Antler10": {"rotate": [{"value": 5.71, "curve": [0.386, 3.43, 0.832, 0]}, {"time": 1.1, "curve": [1.525, 0, 2.375, 7.97]}, {"time": 2.8, "value": 7.97, "curve": [2.941, 7.97, 3.129, 7]}, {"time": 3.3333, "value": 5.71, "curve": [3.72, 3.43, 4.166, 0]}, {"time": 4.4333, "curve": [4.858, 0, 5.708, 7.97]}, {"time": 6.1333, "value": 7.97, "curve": [6.275, 7.97, 6.463, 7]}, {"time": 6.6667, "value": 5.71}]}, "Antler9": {"rotate": [{"value": 3.99, "curve": [0.313, 1.99, 0.625, 0]}, {"time": 0.8333, "curve": [1.25, 0, 2.083, 7.97]}, {"time": 2.5, "value": 7.97, "curve": [2.708, 7.97, 3.021, 5.98]}, {"time": 3.3333, "value": 3.99, "curve": [3.646, 1.99, 3.958, 0]}, {"time": 4.1667, "curve": [4.583, 0, 5.417, 7.97]}, {"time": 5.8333, "value": 7.97, "curve": [6.042, 7.97, 6.354, 5.98]}, {"time": 6.6667, "value": 3.99}]}, "Antler8": {"rotate": [{"value": 2.26, "curve": [0.217, 0.97, 0.417, 0]}, {"time": 0.5667, "curve": [0.983, 0, 1.817, 7.97]}, {"time": 2.2333, "value": 7.97, "curve": [2.501, 7.97, 2.947, 4.55]}, {"time": 3.3333, "value": 2.26, "curve": [3.55, 0.97, 3.75, 0]}, {"time": 3.9, "curve": [4.317, 0, 5.15, 7.97]}, {"time": 5.5667, "value": 7.97, "curve": [5.834, 7.97, 6.28, 4.55]}, {"time": 6.6667, "value": 2.26}]}, "Antler7": {"rotate": [{"value": 0.77, "curve": [0.099, 0.29, 0.189, 0]}, {"time": 0.2667, "curve": [0.692, 0, 1.542, 7.97]}, {"time": 1.9667, "value": 7.97, "curve": [2.299, 7.97, 2.909, 2.57]}, {"time": 3.3333, "value": 0.77, "curve": [3.432, 0.29, 3.523, 0]}, {"time": 3.6, "curve": [4.025, 0, 4.875, 7.97]}, {"time": 5.3, "value": 7.97, "curve": [5.632, 7.97, 6.242, 2.57]}, {"time": 6.6667, "value": 0.77}]}, "Antler6": {"rotate": [{"curve": [0.417, 0, 1.25, 7.97]}, {"time": 1.6667, "value": 7.97, "curve": [2.083, 7.97, 2.917, 0]}, {"time": 3.3333, "curve": [3.75, 0, 4.583, 7.97]}, {"time": 5, "value": 7.97, "curve": [5.417, 7.97, 6.25, 0]}, {"time": 6.6667}]}, "Hands_1": {"rotate": [{"value": -5.24, "curve": [0.036, -5.45, 0.07, -5.57]}, {"time": 0.1, "value": -5.57, "curve": [0.308, -5.57, 0.725, 4.08]}, {"time": 0.9333, "value": 4.08, "curve": [1.133, 4.08, 1.533, -11.11]}, {"time": 1.7333, "value": -11.11, "curve": [1.925, -11.11, 2.308, 0.31]}, {"time": 2.5, "value": 0.31, "curve": [2.704, 0.31, 3.087, -4.18]}, {"time": 3.3333, "value": -5.24, "curve": [3.37, -5.45, 3.403, -5.57]}, {"time": 3.4333, "value": -5.57, "curve": [3.642, -5.57, 4.058, 4.08]}, {"time": 4.2667, "value": 4.08, "curve": [4.467, 4.08, 4.867, -11.11]}, {"time": 5.0667, "value": -11.11, "curve": [5.258, -11.11, 5.642, 0.31]}, {"time": 5.8333, "value": 0.31, "curve": [6.037, 0.31, 6.421, -4.18]}, {"time": 6.6667, "value": -5.24}], "translate": [{"time": 0.1667}, {"time": 0.5, "x": 0.19, "y": 16.49}], "scale": [{"time": 0.1667}, {"time": 0.5, "x": 0.411}]}, "Hands_0": {"rotate": [{"value": 1.63, "curve": [0.127, -0.05, 0.246, -1.34]}, {"time": 0.3333, "value": -1.34, "curve": [0.483, -1.34, 0.783, 5.53]}, {"time": 0.9333, "value": 5.53, "curve": [1.133, 5.53, 1.533, 2.01]}, {"time": 1.7333, "value": 2.01, "curve": [1.983, 2.01, 2.483, 8.29]}, {"time": 2.7333, "value": 8.29, "curve": [2.88, 8.29, 3.12, 4.39]}, {"time": 3.3333, "value": 1.63, "curve": [3.461, -0.05, 3.579, -1.34]}, {"time": 3.6667, "value": -1.34, "curve": [3.817, -1.34, 4.117, 5.53]}, {"time": 4.2667, "value": 5.53, "curve": [4.467, 5.53, 4.867, 2.01]}, {"time": 5.0667, "value": 2.01, "curve": [5.317, 2.01, 5.817, 8.29]}, {"time": 6.0667, "value": 8.29, "curve": [6.213, 8.29, 6.454, 4.39]}, {"time": 6.6667, "value": 1.63}], "translate": [{"time": 0.1667}, {"time": 0.5, "x": -0.19, "y": -13.75}], "scale": [{"time": 0.1667}, {"time": 0.5, "x": 0.374}]}, "HairBit0": {"rotate": [{"value": 4.86, "curve": [0.127, 2.27, 0.248, 0]}, {"time": 0.3333, "curve": [0.558, 0, 1.008, 11.97]}, {"time": 1.2333, "value": 11.97, "curve": [1.425, 11.97, 1.808, 0]}, {"time": 2, "curve": [2.225, 0, 2.675, 11.97]}, {"time": 2.9, "value": 11.97, "curve": [3.007, 11.97, 3.174, 8.14]}, {"time": 3.3333, "value": 4.86, "curve": [3.46, 2.27, 3.582, 0]}, {"time": 3.6667, "curve": [3.892, 0, 4.342, 11.97]}, {"time": 4.5667, "value": 11.97, "curve": [4.758, 11.97, 5.142, 0]}, {"time": 5.3333, "curve": [5.558, 0, 6.008, 11.97]}, {"time": 6.2333, "value": 11.97, "curve": [6.34, 11.97, 6.508, 8.14]}, {"time": 6.6667, "value": 4.86}]}, "HairBit1": {"rotate": [{"value": 9.4, "curve": [0.192, 9.4, 0.575, 0]}, {"time": 0.7667, "curve": [0.992, 0, 1.442, 15.87]}, {"time": 1.6667, "value": 15.87, "curve": [1.867, 15.87, 2.267, -6.87]}, {"time": 2.4667, "value": -6.87, "curve": [2.683, -6.87, 3.117, 9.4]}, {"time": 3.3333, "value": 9.4, "curve": [3.525, 9.4, 3.908, 0]}, {"time": 4.1, "curve": [4.325, 0, 4.775, 15.87]}, {"time": 5, "value": 15.87, "curve": [5.2, 15.87, 5.6, -6.87]}, {"time": 5.8, "value": -6.87, "curve": [6.017, -6.87, 6.45, 9.4]}, {"time": 6.6667, "value": 9.4}]}, "NeckLeaf1": {"rotate": [{"value": -11.45, "curve": [0.222, -7.63, 0.505, 0]}, {"time": 0.6667, "curve": [0.917, 0, 1.417, -14.03]}, {"time": 1.6667, "value": -14.03, "curve": [1.867, -14.03, 2.267, 0]}, {"time": 2.4667, "curve": [2.633, 0, 2.967, -14.03]}, {"time": 3.1333, "value": -14.03, "curve": [3.189, -14.03, 3.257, -13]}, {"time": 3.3333, "value": -11.45, "curve": [3.556, -7.63, 3.838, 0]}, {"time": 4, "curve": [4.25, 0, 4.75, -14.03]}, {"time": 5, "value": -14.03, "curve": [5.2, -14.03, 5.6, 0]}, {"time": 5.8, "curve": [5.967, 0, 6.3, -14.03]}, {"time": 6.4667, "value": -14.03, "curve": [6.522, -14.03, 6.591, -13]}, {"time": 6.6667, "value": -11.45}]}, "NeckLeaf2": {"rotate": [{"curve": [0.167, 0, 0.5, -12.24]}, {"time": 0.6667, "value": -12.24, "curve": [0.892, -12.24, 1.342, 11.87]}, {"time": 1.5667, "value": 11.87, "curve": [1.817, 11.87, 2.317, -12.24]}, {"time": 2.5667, "value": -12.24, "curve": [2.758, -12.24, 3.142, 0]}, {"time": 3.3333, "curve": [3.5, 0, 3.833, -12.24]}, {"time": 4, "value": -12.24, "curve": [4.225, -12.24, 4.675, 11.87]}, {"time": 4.9, "value": 11.87, "curve": [5.15, 11.87, 5.65, -12.24]}, {"time": 5.9, "value": -12.24, "curve": [6.092, -12.24, 6.475, 0]}, {"time": 6.6667}]}, "NeckLeaf5": {"rotate": [{"value": 0.17, "curve": [0.255, 1.02, 0.703, 8.75]}, {"time": 0.9333, "value": 8.75, "curve": [1.133, 8.75, 1.533, 0]}, {"time": 1.7333, "curve": [1.9, 0, 2.233, 8.75]}, {"time": 2.4, "value": 8.75, "curve": [2.625, 8.75, 3.075, 0]}, {"time": 3.3, "curve": [3.311, 0, 3.322, 0.06]}, {"time": 3.3333, "value": 0.17, "curve": [3.588, 1.02, 4.036, 8.75]}, {"time": 4.2667, "value": 8.75, "curve": [4.467, 8.75, 4.867, 0]}, {"time": 5.0667, "curve": [5.233, 0, 5.567, 8.75]}, {"time": 5.7333, "value": 8.75, "curve": [5.958, 8.75, 6.408, 0]}, {"time": 6.6333, "curve": [6.644, 0, 6.655, 0.06]}, {"time": 6.6667, "value": 0.17}]}, "NeckLeaf3": {"rotate": [{"value": 12.29, "curve": [0.164, 17.11, 0.323, 21.44]}, {"time": 0.4333, "value": 21.44, "curve": [0.633, 21.44, 1.033, 0]}, {"time": 1.2333, "curve": [1.4, 0, 1.733, 21.44]}, {"time": 1.9, "value": 21.44, "curve": [2.125, 21.44, 2.575, 0]}, {"time": 2.8, "curve": [2.932, 0, 3.137, 6.51]}, {"time": 3.3333, "value": 12.29, "curve": [3.498, 17.11, 3.657, 21.44]}, {"time": 3.7667, "value": 21.44, "curve": [3.967, 21.44, 4.367, 0]}, {"time": 4.5667, "curve": [4.733, 0, 5.067, 21.44]}, {"time": 5.2333, "value": 21.44, "curve": [5.458, 21.44, 5.908, 0]}, {"time": 6.1333, "curve": [6.265, 0, 6.47, 6.51]}, {"time": 6.6667, "value": 12.29}]}, "NeckLeaf4": {"rotate": [{"value": 8.95, "curve": [0.178, 5.22, 0.378, 0]}, {"time": 0.5, "curve": [0.667, 0, 1, 20.86]}, {"time": 1.1667, "value": 20.86, "curve": [1.392, 20.86, 1.842, -5.18]}, {"time": 2.0667, "value": -5.18, "curve": [2.317, -5.18, 2.817, 13.04]}, {"time": 3.0667, "value": 13.04, "curve": [3.137, 13.04, 3.231, 11.25]}, {"time": 3.3333, "value": 8.95, "curve": [3.511, 5.22, 3.711, 0]}, {"time": 3.8333, "curve": [4, 0, 4.333, 20.86]}, {"time": 4.5, "value": 20.86, "curve": [4.725, 20.86, 5.175, -5.18]}, {"time": 5.4, "value": -5.18, "curve": [5.65, -5.18, 6.15, 13.04]}, {"time": 6.4, "value": 13.04, "curve": [6.47, 13.04, 6.565, 11.25]}, {"time": 6.6667, "value": 8.95}]}, "BodyBtm": {"scale": [{"curve": [0.417, 1, 1.25, 0.967, 0.417, 1, 1.25, 1.049]}, {"time": 1.6667, "x": 0.967, "y": 1.049, "curve": [2.083, 0.967, 2.917, 1, 2.083, 1.049, 2.917, 1]}, {"time": 3.3333, "curve": [3.75, 1, 4.583, 0.967, 3.75, 1, 4.583, 1.049]}, {"time": 5, "x": 0.967, "y": 1.049, "curve": [5.417, 0.967, 6.25, 1, 5.417, 1.049, 6.25, 1]}, {"time": 6.6667}]}, "BodyTop": {"scale": [{"x": 0.991, "y": 1.014, "curve": [0.217, 0.996, 0.417, 1, 0.217, 1.006, 0.417, 1]}, {"time": 0.5667, "curve": [0.983, 1, 1.817, 0.967, 0.983, 1, 1.817, 1.049]}, {"time": 2.2333, "x": 0.967, "y": 1.049, "curve": [2.501, 0.967, 2.947, 0.981, 2.501, 1.049, 2.947, 1.028]}, {"time": 3.3333, "x": 0.991, "y": 1.014, "curve": [3.55, 0.996, 3.75, 1, 3.55, 1.006, 3.75, 1]}, {"time": 3.9, "curve": [4.317, 1, 5.15, 0.967, 4.317, 1, 5.15, 1.049]}, {"time": 5.5667, "x": 0.967, "y": 1.049, "curve": [5.834, 0.967, 6.28, 0.981, 5.834, 1.049, 6.28, 1.028]}, {"time": 6.6667, "x": 0.991, "y": 1.014}]}, "Head": {"translate": [{"curve": [0.417, 0, 1.25, -32.85, 0.417, 0, 1.25, 0.37]}, {"time": 1.6667, "x": -32.85, "y": 0.37, "curve": [2.083, -32.85, 2.917, 0, 2.083, 0.37, 2.917, 0]}, {"time": 3.3333, "curve": [3.75, 0, 4.583, -32.85, 3.75, 0, 4.583, 0.37]}, {"time": 5, "x": -32.85, "y": 0.37, "curve": [5.417, -32.85, 6.25, 0, 5.417, 0.37, 6.25, 0]}, {"time": 6.6667}], "scale": [{"time": 1.0333, "curve": [1.058, 1, 1.108, 0.965, 1.058, 1, 1.108, 1.039]}, {"time": 1.1333, "x": 0.965, "y": 1.039, "curve": [1.15, 0.965, 1.183, 1, 1.15, 1.039, 1.183, 1]}, {"time": 1.2, "curve": [1.225, 1, 1.275, 0.965, 1.225, 1, 1.275, 1.039]}, {"time": 1.3, "x": 0.965, "y": 1.039, "curve": [1.325, 0.965, 1.375, 1, 1.325, 1.039, 1.375, 1]}, {"time": 1.4, "curve": "stepped"}, {"time": 2.4, "curve": [2.425, 1, 2.475, 0.965, 2.425, 1, 2.475, 1.039]}, {"time": 2.5, "x": 0.965, "y": 1.039, "curve": [2.517, 0.965, 2.55, 1, 2.517, 1.039, 2.55, 1]}, {"time": 2.5667, "curve": [2.592, 1, 2.642, 0.965, 2.592, 1, 2.642, 1.039]}, {"time": 2.6667, "x": 0.965, "y": 1.039, "curve": [2.692, 0.965, 2.742, 1, 2.692, 1.039, 2.742, 1]}, {"time": 2.7667, "curve": "stepped"}, {"time": 4.3667, "curve": [4.392, 1, 4.442, 0.965, 4.392, 1, 4.442, 1.039]}, {"time": 4.4667, "x": 0.965, "y": 1.039, "curve": [4.483, 0.965, 4.517, 1, 4.483, 1.039, 4.517, 1]}, {"time": 4.5333, "curve": [4.558, 1, 4.608, 0.965, 4.558, 1, 4.608, 1.039]}, {"time": 4.6333, "x": 0.965, "y": 1.039, "curve": [4.658, 0.965, 4.708, 1, 4.658, 1.039, 4.708, 1]}, {"time": 4.7333, "curve": "stepped"}, {"time": 5.7333, "curve": [5.758, 1, 5.808, 0.965, 5.758, 1, 5.808, 1.039]}, {"time": 5.8333, "x": 0.965, "y": 1.039, "curve": [5.85, 0.965, 5.883, 1, 5.85, 1.039, 5.883, 1]}, {"time": 5.9, "curve": [5.925, 1, 5.975, 0.965, 5.925, 1, 5.975, 1.039]}, {"time": 6, "x": 0.965, "y": 1.039, "curve": [6.025, 0.965, 6.075, 1, 6.025, 1.039, 6.075, 1]}, {"time": 6.1}]}, "MAIN": {"scale": [{"time": 2.4}, {"time": 2.6, "x": 0.947, "y": 1.042, "curve": [2.633, 0.947, 2.7, 1, 2.633, 1.042, 2.7, 1]}, {"time": 2.7333, "curve": [2.758, 1, 2.808, 0.947, 2.758, 1, 2.808, 1.042]}, {"time": 2.8333, "x": 0.947, "y": 1.042, "curve": [2.867, 0.947, 2.933, 1, 2.867, 1.042, 2.933, 1]}, {"time": 2.9667, "curve": [3, 1, 3.067, 0.947, 3, 1, 3.067, 1.042]}, {"time": 3.1, "x": 0.947, "y": 1.042, "curve": [3.133, 0.947, 3.2, 1, 3.133, 1.042, 3.2, 1]}, {"time": 3.2333, "curve": [3.258, 1, 3.308, 0.947, 3.258, 1, 3.308, 1.042]}, {"time": 3.3333, "x": 0.947, "y": 1.042, "curve": [3.367, 0.947, 3.433, 1, 3.367, 1.042, 3.433, 1]}, {"time": 3.4667, "curve": [3.5, 1, 3.567, 0.947, 3.5, 1, 3.567, 1.042]}, {"time": 3.6, "x": 0.947, "y": 1.042, "curve": [3.633, 0.947, 3.7, 1, 3.633, 1.042, 3.7, 1]}, {"time": 3.7333, "curve": [3.758, 1, 3.808, 0.947, 3.758, 1, 3.808, 1.042]}, {"time": 3.8333, "x": 0.947, "y": 1.042, "curve": [3.867, 0.947, 3.933, 1, 3.867, 1.042, 3.933, 1]}, {"time": 3.9667, "curve": [4, 1, 4.067, 0.947, 4, 1, 4.067, 1.042]}, {"time": 4.1, "x": 0.947, "y": 1.042, "curve": [4.133, 0.947, 4.2, 1, 4.133, 1.042, 4.2, 1]}, {"time": 4.2333, "curve": [4.258, 1, 4.308, 0.947, 4.258, 1, 4.308, 1.042]}, {"time": 4.3333, "x": 0.947, "y": 1.042, "curve": [4.367, 0.947, 4.433, 1, 4.367, 1.042, 4.433, 1]}, {"time": 4.4667, "curve": [4.5, 1, 4.567, 0.947, 4.5, 1, 4.567, 1.042]}, {"time": 4.6, "x": 0.947, "y": 1.042, "curve": [4.633, 0.947, 4.7, 1, 4.633, 1.042, 4.7, 1]}, {"time": 4.7333, "curve": [4.758, 1, 4.808, 0.947, 4.758, 1, 4.808, 1.042]}, {"time": 4.8333, "x": 0.947, "y": 1.042, "curve": [4.867, 0.947, 4.933, 1, 4.867, 1.042, 4.933, 1]}, {"time": 4.9667, "curve": [5, 1, 5.067, 0.947, 5, 1, 5.067, 1.042]}, {"time": 5.1, "x": 0.947, "y": 1.042, "curve": [5.133, 0.947, 5.2, 1, 5.133, 1.042, 5.2, 1]}, {"time": 5.2333, "curve": [5.258, 1, 5.308, 0.947, 5.258, 1, 5.308, 1.042]}, {"time": 5.3333, "x": 0.947, "y": 1.042, "curve": [5.367, 0.947, 5.433, 1, 5.367, 1.042, 5.433, 1]}, {"time": 5.4667, "curve": [5.483, 1, 5.517, 0.947, 5.483, 1, 5.517, 1.042]}, {"time": 5.5333, "x": 0.947, "y": 1.042, "curve": [5.55, 0.947, 5.583, 1, 5.55, 1.042, 5.583, 1]}, {"time": 5.6, "curve": [5.617, 1, 5.65, 0.947, 5.617, 1, 5.65, 1.042]}, {"time": 5.6667, "x": 0.947, "y": 1.042, "curve": [5.683, 0.947, 5.717, 1, 5.683, 1.042, 5.717, 1]}, {"time": 5.7333, "curve": [5.75, 1, 5.783, 0.947, 5.75, 1, 5.783, 1.042]}, {"time": 5.8, "x": 0.947, "y": 1.042, "curve": [5.817, 0.947, 5.85, 1, 5.817, 1.042, 5.85, 1]}, {"time": 5.8667, "curve": [5.883, 1, 5.917, 0.947, 5.883, 1, 5.917, 1.042]}, {"time": 5.9333, "x": 0.947, "y": 1.042, "curve": [5.95, 0.947, 5.983, 1, 5.95, 1.042, 5.983, 1]}, {"time": 6, "curve": [6.017, 1, 6.05, 0.947, 6.017, 1, 6.05, 1.042]}, {"time": 6.0667, "x": 0.947, "y": 1.042, "curve": [6.083, 0.947, 6.117, 1, 6.083, 1.042, 6.117, 1]}, {"time": 6.1333, "curve": [6.15, 1, 6.183, 0.947, 6.15, 1, 6.183, 1.042]}, {"time": 6.2, "x": 0.947, "y": 1.042, "curve": [6.217, 0.947, 6.25, 1, 6.217, 1.042, 6.25, 1]}, {"time": 6.2667, "curve": [6.283, 1, 6.317, 0.947, 6.283, 1, 6.317, 1.042]}, {"time": 6.3333, "x": 0.947, "y": 1.042, "curve": [6.35, 0.947, 6.383, 1, 6.35, 1.042, 6.383, 1]}, {"time": 6.4, "curve": [6.417, 1, 6.45, 0.947, 6.417, 1, 6.45, 1.042]}, {"time": 6.4667, "x": 0.947, "y": 1.042, "curve": [6.483, 0.947, 6.517, 1, 6.483, 1.042, 6.517, 1]}, {"time": 6.5333, "curve": [6.55, 1, 6.583, 0.947, 6.55, 1, 6.583, 1.042]}, {"time": 6.6, "x": 0.947, "y": 1.042, "curve": [6.617, 0.947, 6.65, 1, 6.617, 1.042, 6.65, 1]}, {"time": 6.6667, "curve": [6.683, 1, 6.717, 0.947, 6.683, 1, 6.717, 1.042]}, {"time": 6.7333, "x": 0.947, "y": 1.042, "curve": [6.75, 0.947, 6.783, 1, 6.75, 1.042, 6.783, 1]}, {"time": 6.8, "curve": [6.817, 1, 6.85, 0.947, 6.817, 1, 6.85, 1.042]}, {"time": 6.8667, "x": 0.947, "y": 1.042, "curve": [6.883, 0.947, 6.917, 1, 6.883, 1.042, 6.917, 1]}, {"time": 6.9333, "curve": [6.95, 1, 6.983, 0.947, 6.95, 1, 6.983, 1.042]}, {"time": 7, "x": 0.947, "y": 1.042, "curve": [7.017, 0.947, 7.05, 1, 7.017, 1.042, 7.05, 1]}, {"time": 7.0667, "curve": [7.083, 1, 7.117, 0.947, 7.083, 1, 7.117, 1.042]}, {"time": 7.1333, "x": 0.947, "y": 1.042, "curve": [7.15, 0.947, 7.183, 1, 7.15, 1.042, 7.183, 1]}, {"time": 7.2, "curve": [7.217, 1, 7.25, 0.947, 7.217, 1, 7.25, 1.042]}, {"time": 7.2667, "x": 0.947, "y": 1.042, "curve": [7.283, 0.947, 7.317, 1, 7.283, 1.042, 7.317, 1]}, {"time": 7.3333, "curve": [7.35, 1, 7.383, 0.947, 7.35, 1, 7.383, 1.042]}, {"time": 7.4, "x": 0.947, "y": 1.042, "curve": [7.425, 0.947, 7.475, 1, 7.425, 1.042, 7.475, 1]}, {"time": 7.5}]}}, "events": [{"time": 7.5, "name": "transform"}]}}}