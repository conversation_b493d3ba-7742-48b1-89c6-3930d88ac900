{"skeleton": {"hash": "ssekMICcnwU", "spine": "4.1.24", "x": -217.92, "y": 57.63, "width": 412.78, "height": 463.62, "images": "./images/", "audio": "C:/Users/<USER>/Desktop/咩咩启示录（Cult of the Lamb）/Sorcerer"}, "bones": [{"name": "root"}, {"name": "BODY_BTM", "parent": "root", "length": 65.69, "rotation": 99.54, "x": -4.64, "y": 80.11}, {"name": "BODY_TOP", "parent": "BODY_BTM", "length": 61.4, "rotation": -0.45, "x": 68.12, "y": 0.27}, {"name": "HEAD", "parent": "BODY_TOP", "length": 123.67, "rotation": 0.58, "x": 59.13, "y": -0.08}, {"name": "HORN_RIGHT", "parent": "HEAD", "x": 82.64, "y": -80.49}, {"name": "HORN_R", "parent": "HORN_RIGHT", "length": 49.57, "rotation": -88.25, "x": 0.18, "y": 0.49}, {"name": "HORN_LEFT", "parent": "HEAD", "x": 111.16, "y": 71.9}, {"name": "HORN_L", "parent": "HORN_LEFT", "length": 33.45, "rotation": 70.9, "x": 0.3, "y": 0.37}, {"name": "SHOULDER_LEFT", "parent": "BODY_TOP", "length": 48.17, "rotation": 95.91, "x": 107.42, "y": 46.68}, {"name": "ARM_LEFT", "parent": "SHOULDER_LEFT", "length": 39.27, "x": 48.69, "y": -0.08, "scaleX": 0.9764}, {"name": "HAND_LEFT", "parent": "ARM_LEFT", "length": 24.58, "rotation": -13.24, "x": 56.04, "y": -2.03}, {"name": "SLEEVE_LEFT", "parent": "ARM_LEFT", "x": 50.65, "y": 49.04}, {"name": "GOO", "parent": "BODY_BTM", "x": -6.97, "y": 0.85}, {"name": "GOO_1", "parent": "GOO", "rotation": -1.29, "x": 9.47, "y": 40.97}, {"name": "GOO_3", "parent": "GOO", "rotation": -1.29, "x": 4.4, "y": -68.07}, {"name": "GOO_2", "parent": "GOO", "rotation": -1.29, "x": 14.17, "y": -13.02}, {"name": "CAPE_CORNER_RIGHT", "parent": "BODY_BTM", "rotation": -1.29, "x": -5.08, "y": -110.43}, {"name": "CAPE_CORNER_LEFT", "parent": "BODY_BTM", "rotation": -1.29, "x": 15.67, "y": 77.9}, {"name": "NECKLACE", "parent": "BODY_TOP", "rotation": 0.58, "x": 62.25, "y": 10.14}, {"name": "NECK1", "parent": "NECKLACE", "length": 64.53, "rotation": -178.47, "x": -8.99, "y": 0.48}, {"name": "NECK2", "parent": "NECKLACE", "length": 54.24, "rotation": -161.4, "x": -4.7, "y": -40.11}, {"name": "NECK3", "parent": "NECKLACE", "length": 47.48, "rotation": -143.46, "x": 3.49, "y": -59.34}, {"name": "NECK4", "parent": "NECKLACE", "length": 48.5, "rotation": 163.71, "x": 3.4, "y": 25.4}, {"name": "NECK5", "parent": "NECKLACE", "length": 43.14, "rotation": 144.86, "x": 17.77, "y": 35.41}, {"name": "NECKLACE_HANDLE", "parent": "NECKLACE", "rotation": -0.58, "x": -72.01, "y": -2.62, "color": "ff3f00ff"}, {"name": "Necklace2", "parent": "NECKLACE", "rotation": -100.12, "x": -51.34, "y": -51.29, "color": "ff3f00ff"}, {"name": "Necklace3", "parent": "NECKLACE", "rotation": -100.12, "x": -28.37, "y": -91.83, "color": "ff3f00ff"}, {"name": "Necklace4", "parent": "NECKLACE", "rotation": -100.12, "x": -37.81, "y": 36.94, "color": "ff3f00ff"}, {"name": "Necklace5", "parent": "NECKLACE", "rotation": -100.12, "x": -13.16, "y": 56.37, "color": "ff3f00ff"}, {"name": "ARM_LEFT_HANDLE", "parent": "BODY_TOP", "rotation": -99.54, "x": 56.25, "y": 107.27, "color": "ff3f00ff"}, {"name": "SHOULDER_RIGHT", "parent": "BODY_TOP", "length": 48.17, "rotation": 63.59, "x": 89.76, "y": -58.98, "scaleX": -1}, {"name": "ARM_RIGHT", "parent": "SHOULDER_RIGHT", "length": 39.27, "x": 48.69, "y": -0.08}, {"name": "HAND_RIGHT", "parent": "ARM_RIGHT", "length": 24.58, "rotation": -8.13, "x": 56.32, "y": 0.75}, {"name": "SLEEVE_RIGHT", "parent": "ARM_RIGHT", "x": 50.65, "y": 49.04}, {"name": "ARM_RIGHT_HANDLE", "parent": "BODY_TOP", "rotation": -99.54, "x": 45.57, "y": -123.76, "color": "ff3f00ff"}, {"name": "FACE", "parent": "HEAD", "rotation": -9.78, "x": 88.96, "y": 5.82}, {"name": "BODY_HANDLE", "parent": "BODY_TOP", "rotation": 85.27, "x": 24.11, "y": 1.31}, {"name": "WEAPON", "parent": "HAND_LEFT", "length": 43.26, "rotation": -92.19, "x": 27.16, "y": 0.51, "transform": "noScale"}, {"name": "effects", "parent": "root", "x": 322.47, "y": 43.21, "scaleX": 1.4752, "scaleY": 1.4752}, {"name": "spawn_particles", "parent": "effects", "x": -27.79, "y": 36.22}, {"name": "spawn_particles2", "parent": "effects", "x": 11.53, "y": 65.07}, {"name": "fire", "parent": "HAND_RIGHT", "rotation": 170.49, "x": 19.52, "y": -1.97}, {"name": "fire2", "parent": "HAND_LEFT", "rotation": 177.3, "x": 25.14, "y": -18.78}, {"name": "white", "parent": "effects", "x": -234.7, "y": 76.74}, {"name": "HEAD_TOP", "parent": "HEAD", "length": 112.23, "rotation": -23.94, "x": 159.16, "y": -4.47}, {"name": "MedicRing", "parent": "HEAD", "rotation": -11.86, "x": 110.17, "y": -1.31}], "slots": [{"name": "Other/whiteball", "bone": "white"}, {"name": "spawn", "bone": "effects"}, {"name": "Other/SpawnParticles", "bone": "spawn_particles", "color": "ffffff87"}, {"name": "Other/SpawnParticles2", "bone": "spawn_particles2", "color": "ffffff87"}, {"name": "Other/SpawnParticles3", "bone": "spawn_particles2", "color": "ffffff59"}, {"name": "GoatCult/Weapon", "bone": "WEAPON", "dark": "000000"}, {"name": "Sorcerer/GoatCult/Hand5", "bone": "HAND_RIGHT", "dark": "000000"}, {"name": "GoatCult/Arm", "bone": "SHOULDER_LEFT", "dark": "000000", "attachment": "Arm"}, {"name": "GoatCult/Sleeve", "bone": "SLEEVE_LEFT", "dark": "000000", "attachment": "Sleeve"}, {"name": "GoatCult/Hand1", "bone": "HAND_LEFT", "dark": "000000", "attachment": "Hand1"}, {"name": "fire2", "bone": "fire2"}, {"name": "GoatCult/Arm2", "bone": "SHOULDER_RIGHT", "dark": "000000", "attachment": "Arm"}, {"name": "GoatCult/Sleeve2", "bone": "SLEEVE_RIGHT", "dark": "000000", "attachment": "Sleeve"}, {"name": "GoatCult/Hand2", "bone": "HAND_RIGHT", "dark": "000000", "attachment": "Hand1"}, {"name": "GoatCult/Goo6", "bone": "GOO", "dark": "000000"}, {"name": "GoatCult/Goo1", "bone": "GOO", "dark": "000000"}, {"name": "GoatCult/Goo2", "bone": "GOO", "dark": "000000"}, {"name": "GoatCult/Goo5", "bone": "GOO", "dark": "000000"}, {"name": "GoatCult/Goo3", "bone": "GOO", "dark": "000000"}, {"name": "GoatCult/Goo7", "bone": "GOO", "dark": "000000"}, {"name": "GoatCult/Goo4", "bone": "GOO", "dark": "000000"}, {"name": "GoatCult/HornLeft", "bone": "HORN_L", "dark": "000000"}, {"name": "Sorcerer/Cult/Medic_Ring", "bone": "MedicRing", "attachment": "Medic_Ring"}, {"name": "Cape_Back", "bone": "BODY_BTM", "dark": "000000", "attachment": "Sorcerer/Cult/Cape_Back"}, {"name": "Cape", "bone": "BODY_BTM", "dark": "000000", "attachment": "Cape"}, {"name": "Hood Btm", "bone": "BODY_TOP", "dark": "000000"}, {"name": "GoatCult/Necklace", "bone": "BODY_TOP", "dark": "000000", "attachment": "Necklace"}, {"name": "GoatCult/Hood", "bone": "HEAD", "dark": "000000", "attachment": "<PERSON>"}, {"name": "Eyes", "bone": "FACE", "attachment": "Sorcerer/Cult/Eyes"}, {"name": "GoatCult/HornRight", "bone": "HORN_R", "dark": "000000"}, {"name": "GoatCult/Mask", "bone": "HEAD", "dark": "000000"}, {"name": "fire", "bone": "fire"}], "ik": [{"name": "ARM_LEFT_HANDLE", "order": 5, "bones": ["SHOULDER_LEFT", "ARM_LEFT"], "target": "ARM_LEFT_HANDLE", "bendPositive": false}, {"name": "ARM_RIGHT_HANDLE", "order": 6, "bones": ["SHOULDER_RIGHT", "ARM_RIGHT"], "target": "ARM_RIGHT_HANDLE"}, {"name": "Necklace1", "bones": ["NECK1"], "target": "NECKLACE_HANDLE"}, {"name": "Necklace2", "order": 7, "bones": ["NECK2"], "target": "Necklace2"}, {"name": "Necklace3", "order": 1, "bones": ["NECK3"], "target": "Necklace3", "mix": 0.66}, {"name": "Necklace4", "order": 2, "bones": ["NECK4"], "target": "Necklace4", "mix": 0.85}, {"name": "Necklace5", "order": 3, "bones": ["NECK5"], "target": "Necklace5", "mix": 0.668}], "transform": [{"name": "HeadBack", "order": 4, "bones": ["MedicRing"], "target": "FACE", "mixRotate": 0, "mixX": -0.66, "mixScaleX": 0, "mixShearY": 0}], "skins": [{"name": "default", "attachments": {"Cape_Back": {"Sorcerer/Cult/Cape_Back": {"type": "mesh", "uvs": [0.82782, 0.12013, 1, 0.52604, 1, 0.68959, 0.94347, 0.68442, 0.9517, 0.78085, 0.92652, 0.87195, 0.89084, 0.80739, 0.85018, 0.64144, 0.86855, 0.79065, 0.87805, 0.93167, 0.83403, 0.95621, 0.79038, 0.81434, 0.76125, 0.6437, 0.75132, 0.82363, 0.70712, 0.70041, 0.7044, 0.83198, 0.70192, 0.95185, 0.68245, 0.95652, 0.64911, 0.84867, 0.61233, 0.72971, 0.59026, 0.89813, 0.53796, 0.74061, 0.52738, 0.86693, 0.51648, 1, 0.4721, 1, 0.4404, 0.88056, 0.40243, 0.73915, 0.36859, 0.76637, 0.30342, 0.58164, 0.28545, 0.76535, 0.27274, 0.92516, 0.24206, 0.92272, 0.21111, 0.81365, 0.18481, 0.70121, 0.17457, 0.81635, 0.15563, 1, 0.1133, 0.80772, 0.09363, 0.66402, 0.07877, 0.81992, 0.04351, 0.85638, 0.02322, 0.66882, 0, 0.4542, 0.06478, 0.24393, 0.30206, 0, 0.57823, 0], "triangles": [32, 33, 29, 30, 31, 32, 29, 30, 32, 29, 33, 28, 20, 21, 19, 21, 26, 28, 24, 25, 22, 23, 24, 22, 22, 25, 21, 21, 25, 26, 26, 27, 28, 44, 19, 21, 28, 44, 21, 42, 43, 28, 28, 43, 44, 12, 44, 0, 3, 6, 7, 4, 6, 3, 3, 7, 1, 3, 1, 2, 5, 6, 4, 7, 0, 1, 15, 17, 18, 12, 0, 7, 11, 7, 8, 16, 17, 15, 9, 10, 8, 10, 11, 8, 11, 12, 7, 12, 14, 19, 13, 14, 12, 14, 18, 19, 15, 18, 14, 44, 12, 19, 34, 36, 33, 35, 36, 34, 36, 37, 33, 38, 40, 37, 40, 41, 37, 39, 40, 38, 37, 41, 42, 33, 37, 28, 28, 37, 42], "vertices": [4, 17, 54.03, -152.08, 0.03196, 14, 69, -6.58, 0.00317, 16, 70.52, 36.67, 0.40012, 2, -1.26, -75.63, 0.56475, 4, 17, 8.35, -177.8, 0.00776, 14, 23.31, -32.3, 0.01334, 16, 24.84, 10.95, 0.58336, 2, -47.32, -100.68, 0.39553, 4, 17, -8.91, -176.15, 0.00106, 14, 6.05, -30.65, 0.03935, 16, 7.58, 12.6, 0.73019, 2, -64.55, -98.78, 0.22939, 3, 14, 7.54, -20.91, 0.09007, 16, 9.06, 22.34, 0.80293, 2, -62.93, -89.06, 0.107, 3, 14, -2.77, -21.36, 0.16874, 16, -1.25, 21.89, 0.79286, 2, -73.24, -89.36, 0.03841, 3, 14, -11.97, -16.08, 0.26907, 16, -10.44, 27.17, 0.72133, 2, -82.36, -83.95, 0.0096, 3, 14, -4.57, -10.55, 0.37727, 16, -3.04, 32.7, 0.62136, 2, -74.88, -78.53, 0.00137, 2, 14, 13.62, -5.18, 0.47944, 16, 15.14, 38.07, 0.52056, 2, 14, -2.43, -6.86, 0.56844, 16, -0.9, 36.39, 0.43156, 2, 14, -17.47, -7.08, 0.64419, 16, -15.94, 36.17, 0.35581, 2, 14, -19.33, 0.79, 0.71061, 16, -17.8, 44.05, 0.28939, 2, 14, -3.63, 6.92, 0.77069, 16, -2.11, 50.17, 0.22931, 2, 14, 14.86, 10.24, 0.82644, 16, 16.38, 53.49, 0.17356, 3, 14, -3.97, 13.77, 0.87662, 16, -2.44, 57.02, 0.12264, 15, -10.82, -28.05, 0.00074, 3, 14, 9.76, 20.11, 0.91606, 16, 11.28, 63.37, 0.07816, 15, 2.9, -21.7, 0.00578, 3, 14, -4.11, 21.66, 0.93161, 16, -2.59, 64.91, 0.04335, 15, -10.97, -20.16, 0.02504, 3, 14, -16.8, 22.62, 0.90494, 16, -15.28, 65.87, 0.01988, 15, -23.66, -19.2, 0.07517, 3, 14, -17.13, 24.73, 0.81968, 16, -15.61, 67.98, 0.00715, 15, -23.99, -17.09, 0.17317, 3, 14, -5.45, 27.4, 0.67511, 16, -3.93, 70.65, 0.0018, 15, -12.31, -14.42, 0.32309, 3, 14, 7.41, 30.09, 0.49277, 16, 8.93, 73.34, 0.00026, 15, 0.55, -11.73, 0.50696, 2, 14, -10.3, 33.17, 0.31065, 15, -17.16, -8.65, 0.68935, 2, 14, 6.94, 38.67, 0.16428, 15, 0.08, -3.15, 0.83572, 3, 14, -6.37, 40.52, 0.07023, 15, -13.23, -1.3, 0.92912, 13, -13.19, -69.23, 0.00065, 4, 17, -35.26, -102.32, 0.00013, 14, -20.3, 43.18, 0.02297, 15, -27.16, 1.36, 0.97178, 13, -27.12, -66.57, 0.00512, 4, 17, -34.53, -94.63, 0.0011, 14, -19.56, 50.87, 0.00519, 15, -26.42, 9.05, 0.97161, 13, -26.39, -58.88, 0.0221, 4, 17, -21.33, -89.77, 0.005, 14, -6.36, 55.73, 0.00064, 15, -13.22, 13.91, 0.92858, 13, -13.19, -54.02, 0.06578, 3, 17, -5.62, -83.36, 0.01569, 15, 2.49, 20.32, 0.83473, 13, 2.52, -47.61, 0.14957, 3, 17, -7.69, -75.25, 0.03777, 15, 0.42, 28.43, 0.68797, 13, 0.45, -39.5, 0.27426, 3, 17, 13.19, -63.37, 0.07429, 15, 21.3, 40.31, 0.50542, 13, 21.33, -27.63, 0.42029, 3, 17, -5.59, -55.94, 0.12572, 15, 2.52, 47.74, 0.32171, 13, 2.55, -20.19, 0.55257, 3, 17, -22, -50.11, 0.19112, 15, -13.89, 53.57, 0.17218, 13, -13.86, -14.36, 0.6367, 3, 17, -21.07, -43.51, 0.27013, 15, -12.96, 60.17, 0.07462, 13, -12.93, -7.76, 0.65524, 3, 17, -8.96, -38.58, 0.3615, 15, -0.85, 65.1, 0.02481, 13, -0.82, -2.83, 0.61368, 3, 17, 3.37, -34.91, 0.4596, 15, 11.48, 68.77, 0.00572, 13, 11.51, 0.84, 0.53468, 3, 17, -8.6, -31.9, 0.55439, 15, -0.49, 71.78, 0.00072, 13, -0.46, 3.84, 0.44488, 3, 17, -27.66, -26.76, 0.63712, 2, -81.11, 50.87, 0.00031, 13, -19.52, 8.99, 0.36257, 3, 17, -6.67, -21.37, 0.70522, 2, -60.05, 55.95, 0.00322, 13, 1.47, 14.38, 0.29157, 3, 17, 8.82, -19.41, 0.75752, 2, -44.53, 57.68, 0.01605, 13, 16.96, 16.33, 0.22643, 3, 17, -7.39, -15.26, 0.7838, 2, -60.67, 62.07, 0.05377, 13, 0.75, 20.48, 0.16243, 3, 17, -10.65, -8.79, 0.76279, 2, -63.84, 68.59, 0.13465, 13, -2.51, 26.96, 0.10256, 4, 17, 9.48, -7.17, 0.6764, 16, 25.97, 181.58, 0.00137, 2, -43.69, 69.91, 0.2678, 13, 17.62, 28.58, 0.05443, 4, 17, 32.51, -5.32, 0.53018, 16, 49, 183.43, 0.0096, 2, -20.63, 71.43, 0.43702, 13, 40.65, 30.43, 0.0232, 4, 17, 53.62, -18.66, 0.35711, 16, 70.11, 170.09, 0.03841, 2, 0.28, 57.78, 0.59702, 13, 61.76, 17.09, 0.00746, 4, 17, 75.43, -62.22, 0.20116, 16, 91.92, 126.53, 0.107, 2, 21.44, 13.9, 0.69021, 13, 83.57, -26.47, 0.00164, 5, 17, 70.85, -110.06, 0.09145, 14, 85.81, 35.44, 0.00043, 16, 87.34, 78.69, 0.23002, 2, 16.16, -33.86, 0.67792, 13, 78.99, -74.31, 0.00019], "hull": 45, "edges": [0, 88, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88], "width": 174, "height": 106}}, "fire": {"Other/Fire/Fire_0": {"x": -38.55, "y": 83.98, "rotation": 30.74, "width": 150, "height": 275}, "Other/Fire/Fire_1": {"x": -38.55, "y": 83.98, "rotation": 30.74, "width": 150, "height": 275}, "Other/Fire/Fire_2": {"x": -38.55, "y": 83.98, "rotation": 30.74, "width": 150, "height": 275}, "Other/Fire/Fire_3": {"x": -38.55, "y": 83.98, "rotation": 30.74, "width": 150, "height": 275}, "Other/Fire/Fire_4": {"x": -38.55, "y": 83.98, "rotation": 30.74, "width": 150, "height": 275}, "Other/Fire/Fire_5": {"x": -38.55, "y": 83.98, "rotation": 30.74, "width": 150, "height": 275}, "Other/Fire/Fire_6": {"x": -38.55, "y": 83.98, "rotation": 30.74, "width": 150, "height": 275}}, "fire2": {"Other/Fire/Fire_0": {"x": -4.8, "y": 75.05, "width": 150, "height": 275}, "Other/Fire/Fire_1": {"x": -4.8, "y": 75.05, "width": 150, "height": 275}, "Other/Fire/Fire_2": {"x": -4.8, "y": 75.05, "width": 150, "height": 275}, "Other/Fire/Fire_3": {"x": -4.8, "y": 75.05, "width": 150, "height": 275}, "Other/Fire/Fire_4": {"x": -4.8, "y": 75.05, "width": 150, "height": 275}, "Other/Fire/Fire_5": {"x": -4.8, "y": 75.05, "width": 150, "height": 275}, "Other/Fire/Fire_6": {"x": -4.8, "y": 75.05, "width": 150, "height": 275}}, "GoatCult/Weapon": {"Sorcerer/GoatCult/Weapon": {"x": 102.74, "y": 52.84, "scaleX": -1, "rotation": -90.41, "width": 232, "height": 341}}, "Other/SpawnParticles": {"Other/SpawnParticles": {"x": 1.36, "y": 14.05, "width": 108, "height": 131}}, "Other/SpawnParticles2": {"Other/SpawnParticles2": {"y": 0.5, "width": 137, "height": 131}}, "Other/SpawnParticles3": {"Other/SpawnParticles2": {"x": 1.36, "y": 19.48, "scaleX": 0.8156, "scaleY": 0.8156, "rotation": -104.26, "width": 137, "height": 131}}, "Other/whiteball": {"Other/whiteball": {"x": 0.5, "y": 0.5, "width": 111, "height": 111}}, "spawn": {"Other/Teleport_1": {"scaleX": 1.75, "width": 160, "height": 55}, "Other/Teleport_2": {"scaleX": 1.75, "width": 160, "height": 55}, "Other/Teleport_3": {"scaleX": 1.75, "width": 160, "height": 55}, "Other/Teleport_4": {"scaleX": 1.75, "width": 160, "height": 55}}}}, {"name": "Cult", "attachments": {"Cape": {"Cape": {"name": "Sorcerer/Cult/Cape", "type": "mesh", "uvs": [0.58318, 0.09229, 0.64241, 0.2041, 0.71262, 0.34684, 0.77862, 0.4977, 0.83257, 0.59261, 0.87764, 0.6858, 0.91818, 0.7696, 0.96477, 0.86263, 0.98996, 0.91094, 1, 0.96692, 0.88315, 0.96105, 0.76746, 0.97302, 0.63623, 0.92151, 0.56171, 0.98797, 0.43904, 0.90425, 0.37238, 0.93685, 0.26678, 0.93922, 0.18287, 0.96449, 0.10524, 1, 0, 1, 0, 0.90086, 1e-05, 0.79805, 1e-05, 0.68972, 1e-05, 0.5612, 1e-05, 0.40146, 0, 0.23989, 0, 0.14674, 0.14769, 0, 0.21896, 0, 0.41568, 0, 0.31936, 0.61261, 0.24507, 0.25458, 0.28089, 0.42722], "triangles": [21, 17, 20, 18, 19, 20, 17, 18, 20, 15, 30, 14, 30, 15, 16, 7, 10, 6, 10, 7, 8, 10, 8, 9, 10, 11, 6, 30, 12, 14, 4, 12, 30, 5, 12, 4, 6, 12, 5, 6, 11, 12, 21, 22, 30, 30, 16, 21, 16, 17, 21, 13, 14, 12, 31, 24, 25, 31, 1, 32, 32, 1, 2, 24, 31, 32, 23, 24, 32, 30, 32, 2, 30, 2, 3, 23, 32, 30, 22, 23, 30, 4, 30, 3, 31, 25, 26, 31, 28, 29, 27, 28, 31, 27, 31, 26, 0, 31, 29, 1, 31, 0], "vertices": [1, 3, 64.2, -65.79, 1, 2, 3, 37.65, -73.26, 0.464, 2, 97.52, -72.95, 0.536, 2, 3, 3.95, -81.72, 0.112, 2, 63.91, -81.76, 0.888, 3, 2, 28.66, -89.41, 0.78445, 1, 96.78, -89.15, 0.16755, 16, 99.72, 21.37, 0.048, 3, 2, 6.07, -96.72, 0.53715, 1, 74.18, -96.45, 0.33485, 16, 77.13, 14.06, 0.128, 3, 2, -15.85, -102.31, 0.5177, 1, 52.27, -102.05, 0.25792, 16, 55.21, 8.47, 0.22438, 3, 2, -35.56, -107.34, 0.14539, 1, 32.56, -107.08, 0.38664, 16, 35.5, 3.44, 0.46797, 2, 1, 10.62, -112.98, 0.296, 16, 13.57, -2.46, 0.704, 2, 1, -0.8, -116.24, 0.128, 16, 2.14, -5.73, 0.872, 1, 16, -10.45, -5.68, 1, 2, 1, -8.18, -93.02, 0.296, 16, -5.23, 17.5, 0.704, 2, 1, -6.46, -69.41, 0.568, 16, -3.48, 40.97, 0.432, 3, 1, 6.49, -44.86, 0.65504, 16, 9.26, 65.66, 0.26496, 36, -54.92, 82.47, 0.08, 5, 2, -75.51, -27.69, 0.10664, 1, -7.36, -27.22, 0.40604, 16, -4.49, 82.8, 0.06466, 36, -38.51, 97.54, 0.16666, 15, -14.5, -15.56, 0.256, 4, 2, -55.7, -6, 0.19854, 1, 12.41, -5.49, 0.18924, 36, -15.04, 79.55, 0.27622, 15, 4.75, 6.47, 0.336, 5, 2, -60.32, 8.51, 0.15781, 1, 7.72, 9.07, 0.22155, 36, -0.88, 85.59, 0.24435, 15, -0.36, 21.02, 0.336, 17, -8.98, -68.28, 0.04029, 5, 2, -55.04, 29.75, 0.07477, 1, 13.08, 30.16, 0.39252, 36, 20.71, 82.36, 0.17382, 15, 4.32, 42.48, 0.224, 17, -3.89, -46.83, 0.13489, 3, 1, 12.29, 47.54, 0.47597, 36, 38.17, 85.08, 0.112, 17, -4.63, -29.21, 0.41203, 2, 1, 9.48, 64.15, 0.248, 17, -7.48, -12.53, 0.752, 1, 17, -3.94, 8.54, 1, 2, 1, 34.72, 81.56, 0.36, 17, 17.77, 4.89, 0.64, 3, 2, -10.88, 77.51, 0.14515, 1, 57.23, 77.78, 0.50285, 17, 40.28, 1.1, 0.352, 3, 2, 12.83, 73.52, 0.336, 1, 80.95, 73.79, 0.504, 17, 63.99, -2.89, 0.16, 3, 2, 40.97, 68.79, 0.72019, 1, 109.09, 69.06, 0.24781, 17, 92.13, -7.62, 0.032, 2, 3, 17.44, 62.82, 0.16, 2, 75.94, 62.91, 0.84, 2, 3, 52.75, 56.51, 0.432, 2, 111.31, 56.97, 0.568, 1, 3, 73.11, 52.88, 1, 1, 3, 99.91, 17.64, 1, 1, 3, 97.37, 3.4, 1, 1, 3, 90.35, -35.92, 1, 3, 2, 9.02, 7.58, 0.33184, 1, 77.14, 7.92, 0.15616, 36, 3.97, 16.22, 0.512, 3, 3, 33.03, 8.84, 0.4224, 2, 92.07, 9.09, 0.1776, 36, 12.28, -66.45, 0.4, 2, 2, 51.52, 8.4, 0.52, 36, 8.25, -26.09, 0.48], "hull": 30, "edges": [0, 58, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58], "width": 200, "height": 219}}, "Eyes": {"Sorcerer/Cult/Eyes": {"x": 0.76, "y": -4.41, "rotation": -90, "width": 79, "height": 94}, "Sorcerer/Cult/Eyes_Open": {"x": 0.88, "y": -4.86, "rotation": -90, "width": 83, "height": 97}}, "GoatCult/Arm": {"Arm": {"name": "Sorcerer/Cult/Arm", "type": "mesh", "uvs": [0.996, 0.37489, 0.88334, 0.47524, 0.74956, 0.54215, 0.64772, 0.63993, 0.58582, 0.7197, 0.55819, 0.75276, 0.52991, 0.7866, 0.4121, 0.86637, 0.23438, 1, 0.06665, 1, 0.04735, 0.77092, 0.0354, 0.62911, 0.004, 0.25652, 0.004, 0, 0.18247, 1e-05, 0.31625, 0, 0.37813, 0.00308, 0.42807, 1e-05, 0.51793, 0, 0.65571, 0, 0.80946, 1e-05, 0.996, 0, 0.17243, 0.73109, 0.21994, 0.72179, 0.35477, 0.65009, 0.13716, 0.57797, 0.21092, 0.54811, 0.31744, 0.50924], "triangles": [9, 22, 8, 22, 23, 8, 8, 23, 7, 9, 10, 22, 10, 11, 22, 11, 25, 22, 22, 25, 23, 25, 26, 23, 23, 24, 7, 7, 24, 6, 24, 26, 27, 24, 23, 26, 6, 15, 5, 27, 6, 24, 11, 12, 25, 26, 25, 14, 27, 26, 14, 25, 13, 14, 14, 15, 27, 6, 27, 15, 25, 12, 13, 15, 16, 5, 17, 4, 16, 18, 3, 17, 3, 4, 17, 2, 18, 19, 2, 3, 18, 2, 20, 1, 2, 19, 20, 1, 20, 0, 20, 21, 0, 16, 4, 5], "vertices": [3, 8, -13.25, 38.77, 0.99847, 9, -72.22, 38.78, 0.0004, 10, -113.92, 38.78, 0.00113, 3, 8, 3.13, 44.38, 0.9786, 9, -55.84, 44.38, 0.01124, 10, -97.53, 44.38, 0.01016, 1, 8, 20.76, 46.15, 1, 2, 8, 37.04, 51.89, 0.712, 9, -21.93, 51.88, 0.288, 2, 8, 47.55, 57.28, 0.472, 9, -11.43, 57.27, 0.528, 2, 8, 51.68, 59.16, 0.336, 9, -8.62, 60.13, 0.664, 2, 8, 56.02, 61.66, 0.152, 9, -2.96, 61.65, 0.848, 2, 9, 21.04, 65.26, 0.84, 11, -27, 15.19, 0.16, 1, 11, -2.13, 21.76, 1, 1, 11, 18.08, 16.18, 1, 1, 11, 15.37, -6.13, 1, 3, 9, 61.74, 30.13, 0.52742, 10, 20.05, 30.13, 0.016, 11, 13.7, -19.94, 0.45658, 2, 9, 57.34, -6.15, 0.67106, 10, 15.65, -6.15, 0.32894, 2, 9, 50.71, -30.13, 0.76, 10, 9.02, -30.13, 0.24, 2, 9, 26.32, -23.39, 0.984, 10, -15.37, -23.39, 0.016, 2, 8, 65.57, -18.94, 0.28525, 9, 6.6, -18.98, 0.71475, 2, 8, 53.67, -16.65, 0.77012, 9, -5.3, -16.66, 0.22988, 2, 8, 46.58, -15.27, 0.86829, 9, -12.39, -15.28, 0.13171, 2, 8, 34.59, -12.27, 0.99802, 9, -24.38, -12.27, 0.00198, 1, 8, 17.98, -7.66, 1, 1, 8, -0.54, -2.51, 1, 1, 8, -23, 3.74, 1, 1, 11, -0.84, -5.66, 1, 1, 11, -7.58, -4.73, 1, 2, 9, 22.36, 43.13, 0.808, 11, -25.68, -6.94, 0.192, 3, 9, 47.94, 28.8, 0.42554, 10, 6.25, 28.8, 0.128, 11, -0.1, -21.27, 0.44646, 2, 9, 37.06, 28.8, 0.872, 11, -10.98, -21.27, 0.128, 2, 9, 23.22, 28.72, 0.968, 11, -24.82, -21.35, 0.032], "hull": 22, "edges": [0, 42, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42], "width": 116, "height": 95}}, "GoatCult/Arm2": {"Arm": {"name": "Sorcerer/Cult/Arm", "type": "mesh", "uvs": [0.996, 0.37489, 0.88334, 0.47524, 0.74956, 0.54215, 0.64772, 0.63993, 0.58582, 0.7197, 0.55819, 0.75276, 0.52991, 0.7866, 0.4121, 0.86637, 0.23438, 1, 0.06665, 1, 0.04735, 0.77092, 0.0354, 0.62911, 0.004, 0.25652, 0.004, 0, 0.18247, 1e-05, 0.31625, 0, 0.37813, 0.00308, 0.42807, 1e-05, 0.51793, 0, 0.65571, 0, 0.80946, 1e-05, 0.996, 0, 0.17243, 0.73109, 0.21994, 0.72179, 0.35477, 0.65009, 0.13716, 0.57797, 0.21092, 0.54811, 0.31744, 0.50924], "triangles": [9, 22, 8, 22, 23, 8, 8, 23, 7, 9, 10, 22, 10, 11, 22, 11, 25, 22, 22, 25, 23, 25, 26, 23, 23, 24, 7, 7, 24, 6, 24, 26, 27, 24, 23, 26, 6, 15, 5, 27, 6, 24, 11, 12, 25, 26, 25, 14, 27, 26, 14, 25, 13, 14, 14, 15, 27, 6, 27, 15, 25, 12, 13, 15, 16, 5, 17, 4, 16, 18, 3, 17, 3, 4, 17, 2, 18, 19, 2, 3, 18, 2, 20, 1, 2, 19, 20, 1, 20, 0, 20, 21, 0, 16, 4, 5], "vertices": [3, 30, -13.25, 38.77, 0.99847, 31, -72.22, 38.78, 0.0004, 32, -113.92, 38.78, 0.00113, 3, 30, 3.13, 44.38, 0.9786, 31, -55.84, 44.38, 0.01124, 32, -97.53, 44.38, 0.01016, 1, 30, 20.76, 46.15, 1, 2, 30, 37.04, 51.89, 0.712, 31, -21.93, 51.88, 0.288, 2, 30, 47.55, 57.28, 0.472, 31, -11.43, 57.27, 0.528, 2, 30, 51.68, 59.16, 0.336, 31, -8.62, 60.13, 0.664, 2, 30, 56.02, 61.66, 0.152, 31, -2.96, 61.65, 0.848, 2, 31, 21.04, 65.26, 0.84, 33, -27, 15.19, 0.16, 1, 33, -2.13, 21.76, 1, 1, 33, 18.08, 16.18, 1, 1, 33, 15.37, -6.13, 1, 3, 31, 61.74, 30.13, 0.52742, 32, 20.05, 30.13, 0.016, 33, 13.7, -19.94, 0.45658, 2, 31, 57.34, -6.15, 0.67106, 32, 15.65, -6.15, 0.32894, 2, 31, 50.71, -30.13, 0.76, 32, 9.02, -30.13, 0.24, 2, 31, 26.32, -23.39, 0.984, 32, -15.37, -23.39, 0.016, 2, 30, 65.57, -18.94, 0.28525, 31, 6.6, -18.98, 0.71475, 2, 30, 53.67, -16.65, 0.77012, 31, -5.3, -16.66, 0.22988, 2, 30, 46.58, -15.27, 0.86829, 31, -12.39, -15.28, 0.13171, 2, 30, 34.59, -12.27, 0.99802, 31, -24.38, -12.27, 0.00198, 1, 30, 17.98, -7.66, 1, 1, 30, -0.54, -2.51, 1, 1, 30, -23, 3.74, 1, 1, 33, -0.84, -5.66, 1, 1, 33, -7.58, -4.73, 1, 2, 31, 22.36, 43.13, 0.808, 33, -25.68, -6.94, 0.192, 3, 31, 47.94, 28.8, 0.42554, 32, 6.25, 28.8, 0.128, 33, -0.1, -21.27, 0.44646, 2, 31, 37.06, 28.8, 0.872, 33, -10.98, -21.27, 0.128, 2, 31, 23.22, 28.72, 0.968, 33, -24.82, -21.35, 0.032], "hull": 22, "edges": [0, 42, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42], "width": 116, "height": 95}}, "GoatCult/Hand1": {"Hand1": {"name": "Sorcerer/Cult/Hand1", "x": 33.98, "y": -9.14, "rotation": 164.55, "width": 68, "height": 64}, "Hand2": {"name": "Sorcerer/Cult/Hand2", "x": 36.64, "y": -6.4, "rotation": 164.55, "width": 74, "height": 65}, "Hand3": {"name": "Sorcerer/Cult/Hand3", "x": 43.45, "y": -11.79, "rotation": 164.55, "width": 88, "height": 69}, "Hand5": {"name": "Sorcerer/Cult/Hand4", "x": 29.33, "y": 6.11, "scaleY": -1, "rotation": 178.68, "width": 58, "height": 62}, "Hand4": {"name": "Sorcerer/Cult/Hand4", "x": 29.33, "y": 6.11, "scaleY": -1, "rotation": 178.68, "width": 58, "height": 62}}, "GoatCult/Hand2": {"Hand1": {"name": "Sorcerer/Cult/Hand1", "x": 36.1, "y": -5.45, "rotation": 165, "width": 68, "height": 64}, "Hand2": {"name": "Sorcerer/Cult/Hand2", "x": 38.66, "y": -5.5, "rotation": 165, "width": 74, "height": 65}, "Hand3": {"name": "Sorcerer/Cult/Hand3", "x": 45.02, "y": -9.95, "rotation": 165, "width": 88, "height": 69}, "Hand5": {"name": "Sorcerer/Cult/Hand4", "x": 31.78, "y": -1.61, "rotation": 167.53, "width": 58, "height": 62}, "Hand4": {"name": "Sorcerer/Cult/Hand4", "x": 31.78, "y": -1.61, "rotation": 167.53, "width": 58, "height": 62}}, "GoatCult/Hood": {"Hood": {"name": "Sorcerer/Cult/Hood", "type": "mesh", "uvs": [0.6277, 0.09714, 0.66063, 0.20737, 0.71679, 0.35503, 0.79744, 0.48806, 0.84098, 0.51499, 0.83968, 0.57414, 0.87225, 0.63194, 0.91004, 0.63545, 0.97136, 0.64511, 1, 0.65057, 1, 0.71506, 0.9427, 0.77207, 0.86925, 0.834, 0.68555, 0.94128, 0.51294, 1, 0.39806, 1, 0.20834, 0.92312, 0.06438, 0.80901, 0.02382, 0.75712, 0, 0.70125, 0, 0.66593, 0.05082, 0.65039, 0.11496, 0.63768, 0.12232, 0.57434, 0.16326, 0.54713, 0.2674, 0.35, 0.32559, 0.25281, 0.3383, 0.17442, 0.3963, 0.17323, 0.46538, 0.09019, 0.54193, 0, 0.6065, 0, 0.43469, 0.40559, 0.80234, 0.80606, 0.257, 0.63201, 0.63121, 0.61966, 0.43339, 0.94223, 0.67808, 0.86572, 0.25419, 0.85941, 0.14419, 0.77843, 0.11608, 0.69708, 0.30774, 0.35721, 0.36119, 0.27221, 0.42665, 0.18374, 0.49222, 0.10712, 0.56191, 0.04063, 0.58457, 0.09973, 0.62579, 0.21119, 0.68354, 0.36994, 0.75718, 0.50996, 0.8408, 0.64314, 0.19202, 0.56242], "triangles": [36, 34, 35, 34, 32, 35, 34, 51, 32, 35, 32, 48, 18, 19, 40, 19, 21, 40, 19, 20, 21, 10, 11, 7, 10, 7, 8, 8, 9, 10, 48, 32, 47, 41, 42, 32, 32, 42, 47, 15, 36, 14, 13, 14, 37, 16, 38, 15, 15, 38, 36, 38, 34, 36, 14, 36, 37, 36, 35, 37, 13, 33, 12, 13, 37, 33, 17, 39, 16, 16, 39, 38, 37, 35, 33, 38, 39, 34, 12, 33, 11, 17, 18, 39, 33, 50, 11, 50, 7, 11, 50, 6, 7, 33, 35, 50, 50, 35, 49, 18, 40, 39, 39, 40, 34, 22, 51, 40, 22, 23, 51, 23, 24, 51, 21, 22, 40, 51, 34, 40, 49, 5, 50, 50, 5, 6, 35, 48, 49, 5, 49, 4, 4, 49, 3, 51, 41, 32, 51, 25, 41, 51, 24, 25, 49, 48, 3, 48, 2, 3, 43, 47, 42, 48, 47, 2, 2, 47, 1, 42, 41, 26, 41, 25, 26, 42, 26, 28, 42, 28, 43, 28, 26, 27, 47, 43, 44, 44, 46, 47, 47, 0, 1, 47, 46, 0, 44, 43, 29, 43, 28, 29, 46, 44, 45, 44, 29, 45, 46, 45, 0, 45, 31, 0, 29, 30, 45, 45, 30, 31], "vertices": [2, 44, 104.62, -18.92, 0.752, 3, 247.19, -64.1, 0.248, 2, 44, 77.79, -33.11, 0.464, 3, 216.91, -66.22, 0.536, 2, 44, 42.5, -54.75, 0.256, 3, 175.88, -71.73, 0.744, 1, 3, 137.72, -83.35, 1, 1, 3, 128.92, -91.73, 1, 1, 3, 113.43, -88.66, 1, 1, 3, 96.95, -93.17, 1, 2, 3, 94.53, -101.37, 0.472, 5, 21.72, 11.04, 0.528, 1, 5, 34.69, 5.68, 1, 1, 5, 40.69, 2.93, 1, 1, 5, 37.15, -13.92, 1, 2, 3, 57.33, -102.19, 0.472, 5, 21.4, -26.17, 0.528, 1, 3, 43.95, -83.02, 1, 1, 3, 23.02, -37.29, 1, 1, 3, 14.42, 3.7, 1, 1, 3, 18.96, 29.14, 1, 1, 3, 46.67, 67.55, 1, 1, 3, 82.36, 94.09, 1, 2, 3, 97.6, 100.64, 0.488, 7, 22.28, 22.38, 0.512, 1, 7, 29.9, 8.48, 1, 1, 7, 31.37, -0.84, 1, 2, 3, 124.59, 89.65, 0.488, 7, 20.72, -6.72, 0.512, 1, 3, 125.39, 74.84, 1, 1, 3, 141.75, 70.24, 1, 1, 3, 147.28, 59.89, 1, 2, 44, 19.78, 43.79, 0.256, 3, 194.97, 27.57, 0.744, 2, 44, 48.1, 37.24, 0.464, 3, 218.22, 10.12, 0.536, 2, 44, 69.11, 39.43, 0.464, 3, 238.32, 3.63, 0.536, 2, 44, 72.52, 26.83, 0.464, 3, 236.34, -9.27, 0.536, 2, 44, 97.75, 17, 0.752, 3, 255.43, -28.47, 0.248, 1, 44, 125.23, 5.99, 1, 1, 44, 128.68, -8.12, 1, 1, 35, 85.71, 1.89, 1, 2, 3, 53.95, -69.51, 0.856, 35, -21.71, -80.18, 0.144, 1, 35, 25.5, 42.23, 1, 1, 35, 28.29, -41.98, 1, 2, 3, 32.75, 18.6, 0.856, 35, -57.57, 3.05, 0.144, 2, 3, 43.18, -39.19, 0.856, 35, -37.47, -52.13, 0.144, 2, 3, 61.6, 54.41, 0.856, 35, -35.21, 43.24, 0.144, 2, 3, 87.24, 74.97, 0.856, 35, -13.44, 67.85, 0.144, 2, 3, 109.73, 77.38, 0.94141, 35, 8.32, 74.05, 0.05859, 2, 44, 20.07, 34.52, 0.27023, 3, 191.48, 18.98, 0.72977, 2, 44, 44.97, 28.23, 0.464, 3, 211.71, 3.15, 0.536, 3, 44, 71.42, 19.53, 0.45763, 3, 232.38, -15.5, 0.52864, 35, 144.96, 3.34, 0.01373, 3, 44, 94.79, 10.06, 0.70322, 3, 249.92, -33.62, 0.29451, 35, 165.32, -11.54, 0.00228, 3, 44, 115.76, -0.96, 0.89371, 3, 264.64, -52.18, 0.10604, 35, 182.98, -27.33, 0.00025, 3, 44, 101.65, -9.66, 0.73805, 3, 248.21, -54.42, 0.26124, 35, 167.17, -32.33, 0.00072, 2, 44, 74.94, -25.74, 0.464, 3, 217.29, -58.32, 0.536, 2, 44, 36.85, -48.42, 0.256, 3, 173.28, -63.66, 0.744, 2, 44, 4.47, -73.4, 0.01341, 3, 133.56, -73.4, 0.98659, 1, 3, 95.25, -85.67, 1, 2, 44, -39.34, 46.8, 0.09497, 3, 142.12, 54.24, 0.90503], "hull": 32, "edges": [0, 62, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62], "width": 225, "height": 267}}, "GoatCult/Necklace": {"Necklace": {"name": "Sorcerer/Cult/Necklace", "type": "mesh", "uvs": [1, 0.1374, 1, 1, 0.65325, 1, 0.46808, 1, 0.37629, 0.5724, 0.28722, 0.4313, 0.33844, 0.13648, 0.30635, 0.07603, 0.28695, 0.27689, 0.20891, 0.45479, 1e-05, 0.38684, 0, 0.13278, 0.07403, 0, 0.28125, 0, 0.66393, 0, 0.76841, 0, 0.90281, 0, 0.62014, 0.58876, 0.71479, 0.43389, 0.63968, 0.12303, 0.69174, 0.10556, 0.70232, 0.33258, 0.76416, 0.46884, 1, 0.33581], "triangles": [7, 13, 14, 7, 12, 13, 8, 10, 11, 11, 12, 8, 7, 8, 12, 9, 10, 8, 18, 17, 19, 17, 18, 22, 14, 6, 7, 4, 6, 19, 17, 4, 19, 5, 6, 4, 3, 4, 17, 3, 17, 2, 2, 17, 22, 19, 6, 14, 19, 21, 18, 19, 14, 20, 20, 14, 15, 21, 20, 15, 19, 20, 21, 21, 15, 16, 21, 22, 18, 16, 0, 21, 0, 23, 21, 23, 22, 21, 22, 23, 1, 2, 22, 1], "vertices": [1, 20, 10.91, 24.31, 1, 2, 20, 78.53, 6.35, 0.98262, 19, 70.46, 63.9, 0.01738, 1, 19, 69.57, 17.75, 1, 1, 19, 66.83, -3.37, 1, 1, 19, 31.13, -9.39, 1, 1, 19, 18.48, -18.09, 1, 1, 19, -4.45, -9.18, 1, 1, 22, -0.22, 12.66, 1, 1, 22, 16.18, 13.63, 1, 1, 22, 32.05, 7.62, 1, 1, 22, 31.31, -17.01, 1, 1, 22, 11.13, -21, 1, 1, 22, -1.08, -14.74, 1, 1, 22, -5.7, 8.64, 1, 1, 19, -10.61, 29.36, 1, 1, 20, -6.65, 1.4, 1, 1, 20, -2.7, 16.35, 1, 1, 19, 36.04, 18.25, 1, 1, 19, 25, 30.65, 1, 1, 19, -1.08, 25.32, 1, 1, 20, -0.63, -9.31, 1, 1, 20, 17.46, -12.82, 1, 1, 20, 29.95, -8.76, 1, 1, 20, 26.45, 20.21, 1], "hull": 17, "edges": [0, 32, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32], "width": 115, "height": 81}}, "GoatCult/Sleeve": {"Sleeve": {"name": "Sorcerer/Cult/Sleeve", "type": "mesh", "uvs": [1, 0.21861, 1, 0.51484, 1, 0.75896, 1, 0.92353, 0.88987, 1, 0.24715, 1, 0.10987, 0.88513, 0, 0.69313, 0, 0.4545, 0.05813, 0.03518, 0.3983, 2e-05, 0.81499, 0], "triangles": [7, 1, 2, 6, 7, 2, 6, 2, 3, 5, 6, 3, 4, 5, 3, 8, 9, 10, 7, 8, 1, 10, 11, 0, 0, 8, 10, 8, 0, 1], "vertices": [1, 9, 30.72, 2.5, 1, 3, 9, 37.9, 28.49, 0.38221, 10, -3.79, 28.49, 0.19759, 11, -10.14, -21.58, 0.4202, 3, 9, 43.82, 49.9, 0.03675, 10, 2.13, 49.9, 0.17524, 11, -4.22, -0.17, 0.788, 3, 9, 47.81, 64.33, 0.00024, 10, 6.12, 64.33, 0.08, 11, -0.23, 14.26, 0.91976, 1, 11, 5.87, 19.8, 1, 2, 10, 37, 63.02, 0.01168, 11, 30.65, 12.95, 0.98832, 2, 10, 39.51, 51.48, 0.20649, 11, 33.16, 1.41, 0.79351, 2, 10, 39.09, 33.47, 0.46663, 11, 32.74, -16.6, 0.53337, 3, 9, 74.99, 12.54, 0.00016, 10, 33.3, 12.54, 0.80181, 11, 26.95, -37.53, 0.19802, 2, 9, 60.42, -22.9, 0.01748, 10, 21.66, -24.43, 0.98252, 2, 9, 48.9, -23.32, 0.61486, 10, 7.29, -23.03, 0.38514, 2, 9, 32.55, -18.64, 0.90135, 10, -9.14, -18.64, 0.09865], "hull": 12, "edges": [0, 22, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22], "width": 42, "height": 102}}, "GoatCult/Sleeve2": {"Sleeve": {"name": "Sorcerer/Cult/Sleeve", "type": "mesh", "uvs": [1, 0.21861, 1, 0.51484, 1, 0.75896, 1, 0.92353, 0.88987, 1, 0.24715, 1, 0.10987, 0.88513, 0, 0.69313, 0, 0.4545, 0, 0.13715, 0.29527, 1e-05, 0.81499, 0], "triangles": [7, 8, 1, 7, 1, 2, 6, 7, 2, 6, 2, 3, 5, 6, 3, 4, 5, 3, 0, 8, 9, 10, 11, 0, 0, 9, 10, 8, 0, 1], "vertices": [1, 31, 30.72, 2.5, 1, 3, 31, 37.9, 28.49, 0.40507, 32, -3.79, 28.49, 0.14959, 33, -10.14, -21.58, 0.44533, 3, 31, 43.82, 49.9, 0.04389, 32, 2.13, 49.9, 0.01524, 33, -4.22, -0.17, 0.94087, 2, 31, 47.81, 64.33, 0.00026, 33, -0.23, 14.26, 0.99974, 1, 33, 5.87, 19.8, 1, 2, 32, 37, 63.02, 0.01168, 33, 30.65, 12.95, 0.98832, 2, 32, 39.51, 51.48, 0.06249, 33, 33.16, 1.41, 0.93751, 2, 32, 39.09, 33.47, 0.30663, 33, 32.74, -16.6, 0.69337, 3, 31, 74.99, 12.54, 0.00016, 32, 33.3, 12.54, 0.80181, 33, 26.95, -37.53, 0.19802, 2, 31, 67.15, -15.19, 0.01748, 32, 26.06, -15.33, 0.98252, 2, 31, 54.33, -24.63, 0.61486, 32, 12.83, -23.67, 0.38514, 2, 31, 32.55, -18.64, 0.90135, 32, -9.14, -18.64, 0.09865], "hull": 12, "edges": [0, 22, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22], "width": 42, "height": 102}}, "Sorcerer/GoatCult/Hand5": {"Hand5": {"name": "Sorcerer/Cult/Hand5", "x": 31.92, "y": -6.85, "rotation": 176.35, "width": 46, "height": 63}}}}, {"name": "Cult_Medic", "attachments": {"Cape": {"Cape": {"name": "Sorcerer/Cult/Cape_Medic", "type": "mesh", "uvs": [0.58318, 0.09229, 0.64241, 0.2041, 0.71262, 0.34684, 0.77862, 0.4977, 0.83257, 0.59261, 0.87764, 0.6858, 0.91818, 0.7696, 0.96477, 0.86263, 0.98996, 0.91094, 1, 0.96692, 0.88315, 0.96105, 0.76746, 0.97302, 0.63623, 0.92151, 0.56171, 0.98797, 0.43904, 0.90425, 0.37238, 0.93685, 0.26678, 0.93922, 0.18287, 0.96449, 0.10524, 1, 0, 1, 0, 0.90086, 1e-05, 0.79805, 1e-05, 0.68972, 1e-05, 0.5612, 1e-05, 0.40146, 0, 0.23989, 0, 0.14674, 0.14769, 0, 0.21896, 0, 0.41568, 0, 0.31936, 0.61261, 0.24507, 0.25458, 0.28089, 0.42722], "triangles": [21, 17, 20, 18, 19, 20, 17, 18, 20, 15, 30, 14, 30, 15, 16, 7, 10, 6, 10, 7, 8, 10, 8, 9, 10, 11, 6, 30, 12, 14, 4, 12, 30, 5, 12, 4, 6, 12, 5, 6, 11, 12, 21, 22, 30, 30, 16, 21, 16, 17, 21, 13, 14, 12, 31, 24, 25, 31, 1, 32, 32, 1, 2, 24, 31, 32, 23, 24, 32, 30, 32, 2, 30, 2, 3, 23, 32, 30, 22, 23, 30, 4, 30, 3, 31, 25, 26, 31, 28, 29, 27, 28, 31, 27, 31, 26, 0, 31, 29, 1, 31, 0], "vertices": [1, 3, 64.2, -65.79, 1, 2, 3, 37.65, -73.26, 0.464, 2, 97.52, -72.95, 0.536, 2, 3, 3.95, -81.72, 0.112, 2, 63.91, -81.76, 0.888, 3, 2, 28.66, -89.41, 0.78445, 1, 96.78, -89.15, 0.16755, 16, 99.72, 21.37, 0.048, 3, 2, 6.07, -96.72, 0.53715, 1, 74.18, -96.45, 0.33485, 16, 77.13, 14.06, 0.128, 3, 2, -15.85, -102.31, 0.5177, 1, 52.27, -102.05, 0.25792, 16, 55.21, 8.47, 0.22438, 3, 2, -35.56, -107.34, 0.14539, 1, 32.56, -107.08, 0.38664, 16, 35.5, 3.44, 0.46797, 2, 1, 10.62, -112.98, 0.296, 16, 13.57, -2.46, 0.704, 2, 1, -0.8, -116.24, 0.128, 16, 2.14, -5.73, 0.872, 1, 16, -10.45, -5.68, 1, 2, 1, -8.18, -93.02, 0.296, 16, -5.23, 17.5, 0.704, 2, 1, -6.46, -69.41, 0.568, 16, -3.48, 40.97, 0.432, 3, 1, 6.49, -44.86, 0.65504, 16, 9.26, 65.66, 0.26496, 36, -54.92, 82.47, 0.08, 5, 2, -75.51, -27.69, 0.10664, 1, -7.36, -27.22, 0.40604, 16, -4.49, 82.8, 0.06466, 36, -38.51, 97.54, 0.16666, 15, -14.5, -15.56, 0.256, 4, 2, -55.7, -6, 0.19854, 1, 12.41, -5.49, 0.18924, 36, -15.04, 79.55, 0.27622, 15, 4.75, 6.47, 0.336, 5, 2, -60.32, 8.51, 0.15781, 1, 7.72, 9.07, 0.22155, 36, -0.88, 85.59, 0.24435, 15, -0.36, 21.02, 0.336, 17, -8.98, -68.28, 0.04029, 5, 2, -55.04, 29.75, 0.07477, 1, 13.08, 30.16, 0.39252, 36, 20.71, 82.36, 0.17382, 15, 4.32, 42.48, 0.224, 17, -3.89, -46.83, 0.13489, 3, 1, 12.29, 47.54, 0.47597, 36, 38.17, 85.08, 0.112, 17, -4.63, -29.21, 0.41203, 2, 1, 9.48, 64.15, 0.248, 17, -7.48, -12.53, 0.752, 1, 17, -3.94, 8.54, 1, 2, 1, 34.72, 81.56, 0.36, 17, 17.77, 4.89, 0.64, 3, 2, -10.88, 77.51, 0.14515, 1, 57.23, 77.78, 0.50285, 17, 40.28, 1.1, 0.352, 3, 2, 12.83, 73.52, 0.336, 1, 80.95, 73.79, 0.504, 17, 63.99, -2.89, 0.16, 3, 2, 40.97, 68.79, 0.72019, 1, 109.09, 69.06, 0.24781, 17, 92.13, -7.62, 0.032, 2, 3, 17.44, 62.82, 0.16, 2, 75.94, 62.91, 0.84, 2, 3, 52.75, 56.51, 0.432, 2, 111.31, 56.97, 0.568, 1, 3, 73.11, 52.88, 1, 1, 3, 99.91, 17.64, 1, 1, 3, 97.37, 3.4, 1, 1, 3, 90.35, -35.92, 1, 3, 2, 9.02, 7.58, 0.33184, 1, 77.14, 7.92, 0.15616, 36, 3.97, 16.22, 0.512, 3, 3, 33.03, 8.84, 0.4224, 2, 92.07, 9.09, 0.1776, 36, 12.28, -66.45, 0.4, 2, 2, 51.52, 8.4, 0.52, 36, 8.25, -26.09, 0.48], "hull": 30, "edges": [0, 58, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58], "width": 200, "height": 219}}, "Eyes": {"Sorcerer/Cult/Eyes": {"name": "Sorcerer/Cult/Eyes_Medic", "type": "mesh", "uvs": [0.63739, 0.75829, 1, 0.79327, 1, 0.89673, 0.62209, 0.90599, 0.63305, 1, 0.40149, 1, 0.39588, 0.90032, 0, 0.89481, 0, 0.80624, 0.39544, 0.7674, 0.45066, 0, 0.64009, 0], "triangles": [0, 10, 11, 3, 6, 9, 0, 9, 10, 3, 0, 1, 0, 3, 9, 2, 3, 1, 5, 6, 3, 5, 3, 4, 6, 7, 8, 6, 8, 9], "vertices": [1, 35, 8.37, -12.35, 1, 2, 35, 3.82, -38.82, 0.52, 3, 86.13, -33.09, 0.48, 2, 35, -9.63, -38.82, 0.52, 3, 72.88, -30.8, 0.48, 1, 35, -10.83, -11.24, 1, 2, 35, -23.05, -12.04, 0.52, 3, 64.2, -2.12, 0.48, 2, 35, -23.05, 4.87, 0.52, 3, 67.07, 14.53, 0.48, 1, 35, -10.1, 5.28, 1, 2, 35, -9.38, 34.18, 0.52, 3, 85.52, 41.1, 0.48, 2, 35, 2.13, 34.18, 0.52, 3, 96.87, 39.14, 0.48, 1, 35, 7.18, 5.31, 1, 3, 35, 106.95, 1.28, 0.21472, 3, 194.57, -11.08, 0.27328, 44, 35.04, 8.33, 0.512, 3, 35, 106.95, -12.55, 0.21472, 3, 192.22, -24.7, 0.27328, 44, 38.42, -5.08, 0.512], "hull": 12, "edges": [0, 22, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22], "width": 73, "height": 130}, "Sorcerer/Cult/Eyes_Open": {"name": "Sorcerer/Cult/Eyes_Open_Medic", "type": "mesh", "uvs": [0.63372, 0.75631, 0.98667, 0.79103, 0.98667, 0.8937, 0.61884, 0.90289, 0.6295, 0.99618, 0.40412, 0.99618, 0.39866, 0.89727, 0.01333, 0.89179, 0.01333, 0.8039, 0.39823, 0.76536, 0.45197, 0.00382, 0.63636, 0.00382], "triangles": [0, 10, 11, 3, 6, 9, 0, 9, 10, 3, 0, 1, 0, 3, 9, 2, 3, 1, 5, 6, 3, 5, 3, 4, 6, 7, 8, 6, 8, 9], "vertices": [1, 35, 8.37, -12.85, 1, 2, 35, 3.82, -39.32, 0.52, 3, 86.05, -33.58, 0.48, 2, 35, -9.63, -39.32, 0.52, 3, 72.79, -31.29, 0.48, 1, 35, -10.83, -11.74, 1, 2, 35, -23.05, -12.54, 0.52, 3, 64.11, -2.62, 0.48, 2, 35, -23.05, 4.37, 0.52, 3, 66.98, 14.04, 0.48, 1, 35, -10.1, 4.78, 1, 2, 35, -9.38, 33.68, 0.52, 3, 85.43, 40.6, 0.48, 2, 35, 2.13, 33.68, 0.52, 3, 96.78, 38.65, 0.48, 1, 35, 7.18, 4.81, 1, 2, 35, 106.95, 0.78, 0.44, 3, 194.48, -11.57, 0.56, 2, 35, 106.95, -13.05, 0.44, 3, 192.14, -25.2, 0.56], "hull": 12, "edges": [0, 22, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22], "width": 75, "height": 131}}, "GoatCult/Arm": {"Arm": {"name": "Sorcerer/Cult/Arm_Medic", "type": "mesh", "uvs": [0.996, 0.37489, 0.88334, 0.47524, 0.74956, 0.54215, 0.64772, 0.63993, 0.58582, 0.7197, 0.55819, 0.75276, 0.52991, 0.7866, 0.4121, 0.86637, 0.23438, 1, 0.06665, 1, 0.04735, 0.77092, 0.0354, 0.62911, 0.004, 0.25652, 0.004, 0, 0.18247, 1e-05, 0.31625, 0, 0.37813, 0.00308, 0.42807, 1e-05, 0.51793, 0, 0.65571, 0, 0.80946, 1e-05, 0.996, 0, 0.17243, 0.73109, 0.21994, 0.72179, 0.35477, 0.65009, 0.13716, 0.57797, 0.21092, 0.54811, 0.31744, 0.50924], "triangles": [9, 22, 8, 22, 23, 8, 8, 23, 7, 9, 10, 22, 10, 11, 22, 11, 25, 22, 22, 25, 23, 25, 26, 23, 23, 24, 7, 7, 24, 6, 24, 26, 27, 24, 23, 26, 6, 15, 5, 27, 6, 24, 11, 12, 25, 26, 25, 14, 27, 26, 14, 25, 13, 14, 14, 15, 27, 6, 27, 15, 25, 12, 13, 15, 16, 5, 17, 4, 16, 18, 3, 17, 3, 4, 17, 2, 18, 19, 2, 3, 18, 2, 20, 1, 2, 19, 20, 1, 20, 0, 20, 21, 0, 16, 4, 5], "vertices": [3, 8, -13.25, 38.77, 0.99847, 9, -72.22, 38.78, 0.0004, 10, -113.92, 38.78, 0.00113, 3, 8, 3.13, 44.38, 0.9786, 9, -55.84, 44.38, 0.01124, 10, -97.53, 44.38, 0.01016, 1, 8, 20.76, 46.15, 1, 2, 8, 37.04, 51.89, 0.712, 9, -21.93, 51.88, 0.288, 2, 8, 47.55, 57.28, 0.472, 9, -11.43, 57.27, 0.528, 2, 8, 51.68, 59.16, 0.336, 9, -8.62, 60.13, 0.664, 2, 8, 56.02, 61.66, 0.152, 9, -2.96, 61.65, 0.848, 2, 9, 21.04, 65.26, 0.84, 11, -27, 15.19, 0.16, 1, 11, -2.13, 21.76, 1, 1, 11, 18.08, 16.18, 1, 1, 11, 15.37, -6.13, 1, 3, 9, 61.74, 30.13, 0.52742, 10, 20.05, 30.13, 0.016, 11, 13.7, -19.94, 0.45658, 2, 9, 57.34, -6.15, 0.67106, 10, 15.65, -6.15, 0.32894, 2, 9, 50.71, -30.13, 0.76, 10, 9.02, -30.13, 0.24, 2, 9, 26.32, -23.39, 0.984, 10, -15.37, -23.39, 0.016, 2, 8, 65.57, -18.94, 0.28525, 9, 6.6, -18.98, 0.71475, 2, 8, 53.67, -16.65, 0.77012, 9, -5.3, -16.66, 0.22988, 2, 8, 46.58, -15.27, 0.86829, 9, -12.39, -15.28, 0.13171, 2, 8, 34.59, -12.27, 0.99802, 9, -24.38, -12.27, 0.00198, 1, 8, 17.98, -7.66, 1, 1, 8, -0.54, -2.51, 1, 1, 8, -23, 3.74, 1, 1, 11, -0.84, -5.66, 1, 1, 11, -7.58, -4.73, 1, 2, 9, 22.36, 43.13, 0.808, 11, -25.68, -6.94, 0.192, 3, 9, 47.94, 28.8, 0.42554, 10, 6.25, 28.8, 0.128, 11, -0.1, -21.27, 0.44646, 2, 9, 37.06, 28.8, 0.872, 11, -10.98, -21.27, 0.128, 2, 9, 23.22, 28.72, 0.968, 11, -24.82, -21.35, 0.032], "hull": 22, "edges": [0, 42, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42], "width": 116, "height": 95}}, "GoatCult/Arm2": {"Arm": {"name": "Sorcerer/Cult/Arm_Medic", "type": "mesh", "uvs": [0.996, 0.37489, 0.88334, 0.47524, 0.74956, 0.54215, 0.64772, 0.63993, 0.58582, 0.7197, 0.55819, 0.75276, 0.52991, 0.7866, 0.4121, 0.86637, 0.23438, 1, 0.06665, 1, 0.04735, 0.77092, 0.0354, 0.62911, 0.004, 0.25652, 0.004, 0, 0.18247, 1e-05, 0.31625, 0, 0.37813, 0.00308, 0.42807, 1e-05, 0.51793, 0, 0.65571, 0, 0.80946, 1e-05, 0.996, 0, 0.17243, 0.73109, 0.21994, 0.72179, 0.35477, 0.65009, 0.13716, 0.57797, 0.21092, 0.54811, 0.31744, 0.50924], "triangles": [9, 22, 8, 22, 23, 8, 8, 23, 7, 9, 10, 22, 10, 11, 22, 11, 25, 22, 22, 25, 23, 25, 26, 23, 23, 24, 7, 7, 24, 6, 24, 26, 27, 24, 23, 26, 6, 15, 5, 27, 6, 24, 11, 12, 25, 26, 25, 14, 27, 26, 14, 25, 13, 14, 14, 15, 27, 6, 27, 15, 25, 12, 13, 15, 16, 5, 17, 4, 16, 18, 3, 17, 3, 4, 17, 2, 18, 19, 2, 3, 18, 2, 20, 1, 2, 19, 20, 1, 20, 0, 20, 21, 0, 16, 4, 5], "vertices": [3, 30, -13.25, 38.77, 0.99847, 31, -72.22, 38.78, 0.0004, 32, -113.92, 38.78, 0.00113, 3, 30, 3.13, 44.38, 0.9786, 31, -55.84, 44.38, 0.01124, 32, -97.53, 44.38, 0.01016, 1, 30, 20.76, 46.15, 1, 2, 30, 37.04, 51.89, 0.712, 31, -21.93, 51.88, 0.288, 2, 30, 47.55, 57.28, 0.472, 31, -11.43, 57.27, 0.528, 2, 30, 51.68, 59.16, 0.336, 31, -8.62, 60.13, 0.664, 2, 30, 56.02, 61.66, 0.152, 31, -2.96, 61.65, 0.848, 2, 31, 21.04, 65.26, 0.84, 33, -27, 15.19, 0.16, 1, 33, -2.13, 21.76, 1, 1, 33, 18.08, 16.18, 1, 1, 33, 15.37, -6.13, 1, 3, 31, 61.74, 30.13, 0.52742, 32, 20.05, 30.13, 0.016, 33, 13.7, -19.94, 0.45658, 2, 31, 57.34, -6.15, 0.67106, 32, 15.65, -6.15, 0.32894, 2, 31, 50.71, -30.13, 0.76, 32, 9.02, -30.13, 0.24, 2, 31, 26.32, -23.39, 0.984, 32, -15.37, -23.39, 0.016, 2, 30, 65.57, -18.94, 0.28525, 31, 6.6, -18.98, 0.71475, 2, 30, 53.67, -16.65, 0.77012, 31, -5.3, -16.66, 0.22988, 2, 30, 46.58, -15.27, 0.86829, 31, -12.39, -15.28, 0.13171, 2, 30, 34.59, -12.27, 0.99802, 31, -24.38, -12.27, 0.00198, 1, 30, 17.98, -7.66, 1, 1, 30, -0.54, -2.51, 1, 1, 30, -23, 3.74, 1, 1, 33, -0.84, -5.66, 1, 1, 33, -7.58, -4.73, 1, 2, 31, 22.36, 43.13, 0.808, 33, -25.68, -6.94, 0.192, 3, 31, 47.94, 28.8, 0.42554, 32, 6.25, 28.8, 0.128, 33, -0.1, -21.27, 0.44646, 2, 31, 37.06, 28.8, 0.872, 33, -10.98, -21.27, 0.128, 2, 31, 23.22, 28.72, 0.968, 33, -24.82, -21.35, 0.032], "hull": 22, "edges": [0, 42, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42], "width": 116, "height": 95}}, "GoatCult/Hand1": {"Hand1": {"name": "Sorcerer/Cult/Hand1", "x": 33.98, "y": -9.14, "rotation": 164.55, "width": 68, "height": 64}, "Hand2": {"name": "Sorcerer/Cult/Hand2", "x": 36.64, "y": -6.4, "rotation": 164.55, "width": 74, "height": 65}, "Hand3": {"name": "Sorcerer/Cult/Hand3", "x": 43.45, "y": -11.79, "rotation": 164.55, "width": 88, "height": 69}, "Hand5": {"name": "Sorcerer/Cult/Hand4", "x": 29.33, "y": 6.11, "scaleY": -1, "rotation": 178.68, "width": 58, "height": 62}, "Hand4": {"name": "Sorcerer/Cult/Hand4", "x": 29.33, "y": 6.11, "scaleY": -1, "rotation": 178.68, "width": 58, "height": 62}}, "GoatCult/Hand2": {"Hand1": {"name": "Sorcerer/Cult/Hand1", "x": 36.1, "y": -5.45, "rotation": 165, "width": 68, "height": 64}, "Hand2": {"name": "Sorcerer/Cult/Hand2", "x": 38.66, "y": -5.5, "rotation": 165, "width": 74, "height": 65}, "Hand3": {"name": "Sorcerer/Cult/Hand3", "x": 45.02, "y": -9.95, "rotation": 165, "width": 88, "height": 69}, "Hand5": {"name": "Sorcerer/Cult/Hand4", "x": 31.78, "y": -1.61, "rotation": 167.53, "width": 58, "height": 62}, "Hand4": {"name": "Sorcerer/Cult/Hand4", "x": 31.78, "y": -1.61, "rotation": 167.53, "width": 58, "height": 62}}, "GoatCult/Hood": {"Hood": {"name": "Sorcerer/Cult/Hood_Medic", "type": "mesh", "uvs": [0.55214, 0.16916, 0.58929, 0.32008, 0.64908, 0.50569, 0.68892, 0.66243, 0.78987, 0.68249, 0.90807, 0.70597, 1, 0.72424, 1, 0.75537, 0.94259, 0.83304, 0.86533, 0.8944, 0.87885, 0.91325, 0.85046, 0.92576, 0.8262, 0.91438, 0.75535, 0.95924, 0.59451, 1, 0.46179, 1, 0.32246, 1, 0.20665, 0.95263, 0.1327, 0.9111, 0.11089, 0.9244, 0.06115, 0.88931, 0.0807, 0.87109, 0, 0.79093, 1e-05, 0.75864, 0.0926, 0.72962, 0.18965, 0.69921, 0.28361, 0.66976, 0.32933, 0.50358, 0.37583, 0.32511, 0.4166, 0.17274, 0.46282, 0, 0.51415, 0, 0.3932, 0.75574, 0.4841, 0.14939, 0.5391, 0.76302, 0.82042, 0.79145, 0.11541, 0.80481, 0.38285, 0.84249, 0.47308, 0.95715, 0.54894, 0.84964, 0.83037, 0.80654, 0.12866, 0.8231], "triangles": [34, 37, 32, 37, 34, 39, 22, 23, 36, 7, 8, 5, 5, 6, 7, 15, 16, 38, 38, 37, 39, 38, 16, 37, 26, 27, 32, 13, 14, 39, 39, 34, 35, 32, 33, 34, 3, 34, 2, 15, 38, 14, 14, 38, 39, 34, 33, 1, 34, 1, 2, 0, 1, 33, 32, 27, 28, 33, 32, 28, 33, 28, 29, 29, 30, 33, 33, 31, 0, 33, 30, 31, 37, 17, 41, 18, 19, 21, 19, 20, 21, 21, 41, 18, 17, 18, 41, 21, 36, 41, 21, 22, 36, 41, 36, 32, 32, 25, 26, 32, 36, 25, 23, 24, 36, 36, 24, 25, 40, 13, 39, 34, 3, 35, 12, 13, 40, 10, 11, 9, 11, 12, 9, 12, 40, 9, 9, 40, 8, 39, 35, 40, 8, 40, 5, 40, 35, 5, 3, 4, 35, 35, 4, 5, 16, 17, 37, 37, 41, 32], "vertices": [2, 44, 111.79, 3.01, 0.368, 3, 262.56, -47.08, 0.632, 2, 44, 71.5, -15.16, 0.304, 3, 218.36, -47.34, 0.696, 2, 44, 22.67, -40.47, 0.096, 3, 163.47, -50.66, 0.904, 1, 3, 117.51, -51.2, 1, 1, 3, 107.99, -71.65, 1, 2, 3, 96.85, -95.59, 0.52, 5, 16.01, 13.54, 0.48, 2, 3, 88.18, -114.21, 0.352, 5, 34.36, 4.3, 0.648, 2, 3, 79.35, -112.64, 0.352, 5, 32.52, -4.47, 0.648, 2, 3, 59.51, -96.5, 0.52, 5, 15.78, -23.81, 0.48, 1, 3, 45.05, -76.96, 1, 1, 3, 39.19, -78.88, 1, 1, 3, 36.72, -72.21, 1, 1, 3, 40.87, -67.63, 1, 1, 3, 30.84, -50.29, 1, 1, 3, 25.39, -14.03, 1, 1, 3, 30.43, 14.19, 1, 1, 3, 35.72, 43.82, 1, 1, 3, 53.55, 66.04, 1, 2, 3, 68.13, 79.67, 0.64, 7, -7.18, 43.36, 0.36, 2, 3, 65.19, 84.98, 0.64, 7, -3.13, 47.88, 0.36, 2, 3, 77.03, 93.78, 0.64, 7, 9.06, 39.58, 0.36, 2, 3, 81.45, 88.7, 0.64, 7, 5.71, 33.73, 0.36, 2, 3, 107.24, 101.8, 0.36, 7, 26.53, 13.65, 0.64, 2, 3, 116.39, 100.16, 0.36, 7, 27.98, 4.46, 0.64, 2, 3, 121.11, 79.01, 0.64, 7, 9.53, -6.91, 0.36, 1, 3, 126.04, 56.83, 1, 1, 3, 130.82, 35.36, 1, 2, 44, 6.77, 26.74, 0.096, 3, 176.2, 17.22, 0.904, 2, 44, 59.08, 29.26, 0.304, 3, 225.04, -1.7, 0.696, 2, 44, 103.8, 31.19, 0.368, 3, 266.69, -18.08, 0.632, 2, 44, 154.49, 33.37, 0.456, 3, 313.91, -36.66, 0.544, 2, 44, 157.14, 22.61, 0.456, 3, 311.96, -47.57, 0.544, 2, 3, 102.29, 16.41, 0.344, 35, 11.34, 12.7, 0.656, 2, 44, 113.81, 18.64, 0.368, 3, 270.74, -33.62, 0.632, 2, 3, 94.68, -14.25, 0.344, 35, 9.05, -18.81, 0.656, 1, 3, 75.94, -72.62, 1, 2, 3, 98.92, 77.96, 0.64, 7, 1.28, 13.71, 0.36, 2, 3, 78.08, 23, 0.344, 35, -13.63, 15.08, 0.656, 1, 3, 42.15, 9.62, 1, 2, 3, 69.75, -11.95, 0.344, 35, -15.91, -20.78, 0.656, 1, 3, 71.28, -73.98, 1, 2, 3, 93.23, 76.07, 0.64, 7, -2.37, 18.47, 0.36], "hull": 32, "edges": [0, 62, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62], "width": 216, "height": 288}}, "GoatCult/Sleeve": {"Sleeve": {"name": "Sorcerer/Cult/<PERSON>leeve_<PERSON>dic", "type": "mesh", "uvs": [1, 0.21861, 1, 0.51484, 1, 0.75896, 1, 0.92353, 0.88987, 1, 0.24715, 1, 0.10987, 0.88513, 0, 0.69313, 0, 0.4545, 0.05813, 0.03518, 0.3983, 2e-05, 0.81499, 0], "triangles": [7, 1, 2, 6, 7, 2, 6, 2, 3, 5, 6, 3, 4, 5, 3, 8, 9, 10, 7, 8, 1, 10, 11, 0, 0, 8, 10, 8, 0, 1], "vertices": [1, 9, 30.72, 2.5, 1, 3, 9, 37.9, 28.49, 0.38221, 10, -3.79, 28.49, 0.19759, 11, -10.14, -21.58, 0.4202, 3, 9, 43.82, 49.9, 0.03675, 10, 2.13, 49.9, 0.17524, 11, -4.22, -0.17, 0.788, 3, 9, 47.81, 64.33, 0.00024, 10, 6.12, 64.33, 0.08, 11, -0.23, 14.26, 0.91976, 1, 11, 5.87, 19.8, 1, 2, 10, 37, 63.02, 0.01168, 11, 30.65, 12.95, 0.98832, 2, 10, 39.51, 51.48, 0.20649, 11, 33.16, 1.41, 0.79351, 2, 10, 39.09, 33.47, 0.46663, 11, 32.74, -16.6, 0.53337, 3, 9, 74.99, 12.54, 0.00016, 10, 33.3, 12.54, 0.80181, 11, 26.95, -37.53, 0.19802, 2, 9, 60.42, -22.9, 0.01748, 10, 21.66, -24.43, 0.98252, 2, 9, 48.9, -23.32, 0.61486, 10, 7.29, -23.03, 0.38514, 2, 9, 32.55, -18.64, 0.90135, 10, -9.14, -18.64, 0.09865], "hull": 12, "edges": [0, 22, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22], "width": 42, "height": 102}}, "GoatCult/Sleeve2": {"Sleeve": {"name": "Sorcerer/Cult/<PERSON>leeve_<PERSON>dic", "type": "mesh", "uvs": [1, 0.21861, 1, 0.51484, 1, 0.75896, 1, 0.92353, 0.88987, 1, 0.24715, 1, 0.10987, 0.88513, 0, 0.69313, 0, 0.4545, 0, 0.13715, 0.29527, 1e-05, 0.81499, 0], "triangles": [7, 8, 1, 7, 1, 2, 6, 7, 2, 6, 2, 3, 5, 6, 3, 4, 5, 3, 0, 8, 9, 10, 11, 0, 0, 9, 10, 8, 0, 1], "vertices": [1, 31, 30.72, 2.5, 1, 3, 31, 37.9, 28.49, 0.40507, 32, -3.79, 28.49, 0.14959, 33, -10.14, -21.58, 0.44533, 3, 31, 43.82, 49.9, 0.04389, 32, 2.13, 49.9, 0.01524, 33, -4.22, -0.17, 0.94087, 2, 31, 47.81, 64.33, 0.00026, 33, -0.23, 14.26, 0.99974, 1, 33, 5.87, 19.8, 1, 2, 32, 37, 63.02, 0.01168, 33, 30.65, 12.95, 0.98832, 2, 32, 39.51, 51.48, 0.06249, 33, 33.16, 1.41, 0.93751, 2, 32, 39.09, 33.47, 0.30663, 33, 32.74, -16.6, 0.69337, 3, 31, 74.99, 12.54, 0.00016, 32, 33.3, 12.54, 0.80181, 33, 26.95, -37.53, 0.19802, 2, 31, 67.15, -15.19, 0.01748, 32, 26.06, -15.33, 0.98252, 2, 31, 54.33, -24.63, 0.61486, 32, 12.83, -23.67, 0.38514, 2, 31, 32.55, -18.64, 0.90135, 32, -9.14, -18.64, 0.09865], "hull": 12, "edges": [0, 22, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22], "width": 42, "height": 102}}, "Sorcerer/Cult/Medic_Ring": {"Medic_Ring": {"name": "Sorcerer/Cult/Medic_Ring", "x": 47.37, "y": -2.57, "rotation": -87.81, "width": 267, "height": 172}}, "Sorcerer/GoatCult/Hand5": {"Hand5": {"name": "Sorcerer/Cult/Hand5", "x": 31.92, "y": -6.85, "rotation": 176.35, "width": 46, "height": 63}}}}], "events": {"Fireball": {}, "Summon": {}, "Teleport": {}, "fire": {}}, "animations": {"attack": {"slots": {"GoatCult/Hand1": {"attachment": [{"time": 0.1333, "name": "Hand4"}, {"time": 1.4667, "name": "Hand2"}]}, "GoatCult/Hand2": {"attachment": [{"time": 0.1333}, {"time": 0.7, "name": "Hand4"}, {"time": 1.3667, "name": "Hand3"}]}, "GoatCult/Weapon": {"attachment": [{"time": 0.0333, "name": "Sorcerer/GoatCult/Weapon"}, {"time": 1.5333}]}, "Sorcerer/GoatCult/Hand5": {"attachment": [{"time": 0.8, "name": "Hand5"}, {"time": 1.3667}]}}, "bones": {"HAND_LEFT": {"rotate": [{"value": -7.34, "curve": "stepped"}, {"time": 0.6667, "value": -7.34, "curve": [0.675, -7.34, 0.687, 2.59]}, {"time": 0.7, "value": 14.51, "curve": [0.736, -11.98, 0.775, -45.09]}, {"time": 0.8, "value": -45.09, "curve": "stepped"}, {"time": 0.8333, "value": 32.42, "curve": "stepped"}, {"time": 1.4667, "value": 32.42, "curve": [1.517, 32.42, 1.617, -7.34]}, {"time": 1.6667, "value": -7.34}], "translate": [{"time": 0.6667, "curve": [0.7, 0, 0.767, 7.8, 0.7, 0, 0.767, 33.51]}, {"time": 0.8, "x": 7.8, "y": 33.51, "curve": "stepped"}, {"time": 0.8333, "x": -12.94, "y": 22.36, "curve": "stepped"}, {"time": 1.4667, "x": -12.94, "y": 22.36, "curve": [1.517, -12.94, 1.617, 0, 1.517, 22.36, 1.617, 0]}, {"time": 1.6667}], "scale": [{"time": 0.8, "curve": "stepped"}, {"time": 0.8333, "y": -1, "curve": "stepped"}, {"time": 1.4333, "y": -1, "curve": "stepped"}, {"time": 1.4667}]}, "HAND_RIGHT": {"rotate": [{"value": -7.2, "curve": [0.051, 26.23, 0.097, 50.11]}, {"time": 0.1333, "value": 50.11, "curve": "stepped"}, {"time": 0.6667, "value": 50.11, "curve": [0.675, 50.11, 0.692, -7.2]}, {"time": 0.7, "value": -7.2, "curve": [0.738, 22.96, 0.773, 44.51]}, {"time": 0.8, "value": 44.51, "curve": [0.808, 44.51, 0.825, 33.45]}, {"time": 0.8333, "value": 33.45, "curve": "stepped"}, {"time": 1.4667, "value": 33.45, "curve": [1.517, 33.45, 1.617, -7.2]}, {"time": 1.6667, "value": -7.2}], "translate": [{"time": 0.6667, "x": 9.07, "y": 67.85, "curve": [0.675, 9.07, 0.692, 1.91, 0.675, 67.85, 0.692, -1.66]}, {"time": 0.7, "x": 1.91, "y": -1.66, "curve": [0.725, 1.91, 0.775, -14.02, 0.725, -1.66, 0.775, -32.05]}, {"time": 0.8, "x": -14.02, "y": -32.05, "curve": [0.808, -14.02, 0.825, -10.97, 0.808, -32.05, 0.825, -4.29]}, {"time": 0.8333, "x": -10.97, "y": -4.29, "curve": "stepped"}, {"time": 1.4667, "x": -10.97, "y": -4.29, "curve": [1.517, -10.97, 1.617, 0, 1.517, -4.29, 1.617, 0]}, {"time": 1.6667}]}, "WEAPON": {"rotate": [{"time": 0.0333, "value": 97.18, "curve": [0.117, 85.16, 0.508, 84.63]}, {"time": 0.6667, "value": 84.63, "curve": "stepped"}, {"time": 0.7667, "value": 84.63, "curve": [0.775, 84.63, 0.792, 170.36]}, {"time": 0.8, "value": 170.36, "curve": "stepped"}, {"time": 1.3667, "value": 170.36, "curve": [1.384, 170.36, 1.408, 162.83]}, {"time": 1.4333, "value": 153.81, "curve": [1.47, 118.84, 1.509, 75.13]}, {"time": 1.5333, "value": 75.13}], "translate": [{"time": 0.6667, "curve": [0.7, 0, 0.767, 7.03, 0.7, 0, 0.767, 27.27]}, {"time": 0.8, "x": 7.03, "y": 27.27, "curve": "stepped"}, {"time": 1.3667, "x": 7.03, "y": 27.27, "curve": [1.408, 7.03, 1.492, 0, 1.408, 27.27, 1.492, 0]}, {"time": 1.5333}], "scale": [{"time": 0.0333, "x": 0.159, "y": 0.159, "curve": [0.05, 0.159, 0.083, 1, 0.05, 0.159, 0.083, 1]}, {"time": 0.1, "curve": [0.117, 1, 0.15, 1.208, 0.117, 1, 0.15, 1.263]}, {"time": 0.1667, "x": 1.208, "y": 1.263, "curve": [0.192, 1.208, 0.242, 0.748, 0.192, 1.263, 0.242, 1.177]}, {"time": 0.2667, "x": 0.748, "y": 1.177, "curve": [0.308, 0.748, 0.392, 1, 0.308, 1.177, 0.392, 1]}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.7667, "curve": [0.783, 1, 0.817, 1.208, 0.783, 1, 0.817, 1.656]}, {"time": 0.8333, "x": 1.208, "y": 1.656, "curve": [0.858, 1.208, 0.908, 1, 0.858, 1.656, 0.908, 0.787]}, {"time": 0.9333, "y": 0.787, "curve": [0.967, 1, 1.033, 1, 0.967, 0.787, 1.033, 1.347]}, {"time": 1.0667, "y": 1.347, "curve": [1.108, 1, 1.192, 1, 1.108, 1.347, 1.192, 1]}, {"time": 1.2333, "curve": "stepped"}, {"time": 1.3667, "curve": [1.408, 1, 1.492, 0.108, 1.408, 1, 1.492, 0.108]}, {"time": 1.5333, "x": 0.108, "y": 0.108}]}, "BODY_BTM": {"rotate": [{"value": -1.67, "curve": [0.025, 2.65, 0.05, 6.98]}, {"time": 0.0667, "value": 6.98, "curve": [0.167, 6.98, 0.067, 5.71]}, {"time": 0.4667, "value": 5.71, "curve": "stepped"}, {"time": 0.6667, "value": 5.71, "curve": [0.7, 5.71, 0.767, -25.02]}, {"time": 0.8, "value": -25.02, "curve": "stepped"}, {"time": 1.4667, "value": -25.02, "curve": [1.517, -25.02, 1.617, -1.67]}, {"time": 1.6667, "value": -1.67}], "translate": [{"curve": [0.017, 0, 0.05, 1.56, 0.017, 0, 0.05, -22.11]}, {"time": 0.0667, "x": 1.56, "y": -22.11, "curve": [0.167, 1.56, 0.067, 0, 0.167, -22.11, 0.067, 18]}, {"time": 0.4667, "y": 18, "curve": "stepped"}, {"time": 0.6667, "y": 18, "curve": [0.7, 0, 0.767, 0, 0.7, 18, 0.767, 0]}, {"time": 0.8, "curve": "stepped"}, {"time": 1.4667, "curve": [1.517, 0, 1.617, 14.22, 1.517, 0, 1.617, 55.71]}, {"time": 1.6667, "x": 14.22, "y": 55.71, "curve": [1.725, 14.22, 1.842, 0, 1.725, 55.71, 1.842, 0]}, {"time": 1.9}], "scale": [{"y": 1.015, "curve": [0.017, 1, 0.05, 0.79, 0.017, 1.015, 0.05, 1.211]}, {"time": 0.0667, "x": 0.79, "y": 1.211, "curve": [0.092, 0.79, 0.142, 1.312, 0.092, 1.211, 0.142, 0.884]}, {"time": 0.1667, "x": 1.312, "y": 0.884, "curve": [0.192, 1.312, 0.242, 0.947, 0.192, 0.884, 0.242, 1.158]}, {"time": 0.2667, "x": 0.947, "y": 1.158, "curve": [0.308, 0.947, 0.392, 1.138, 0.308, 1.158, 0.392, 0.96]}, {"time": 0.4333, "x": 1.138, "y": 0.96, "curve": "stepped"}, {"time": 0.6667, "x": 1.138, "y": 0.96, "curve": [0.683, 1.138, 0.717, 1, 0.683, 0.96, 0.717, 1]}, {"time": 0.7333, "curve": [0.75, 1, 0.783, 0.79, 0.75, 1, 0.783, 1.211]}, {"time": 0.8, "x": 0.79, "y": 1.211, "curve": [0.825, 0.79, 0.875, 1.312, 0.825, 1.211, 0.875, 0.884]}, {"time": 0.9, "x": 1.312, "y": 0.884, "curve": [0.925, 1.312, 0.975, 0.947, 0.925, 0.884, 0.975, 1.158]}, {"time": 1, "x": 0.947, "y": 1.158, "curve": [1.042, 0.947, 1.125, 1.138, 1.042, 1.158, 1.125, 0.96]}, {"time": 1.1667, "x": 1.138, "y": 0.96, "curve": "stepped"}, {"time": 1.4667, "x": 1.138, "y": 0.96, "curve": [1.517, 1.138, 1.617, 1, 1.517, 0.96, 1.617, 1]}, {"time": 1.6667}]}, "GOO_1": {"rotate": [{"value": -15.24, "curve": [0.203, -11.34, 0.434, -5.21]}, {"time": 0.6667, "value": 1.11}], "translate": [{"y": -4.63, "curve": [0.112, 2.27, 0.225, 4.67, 0.112, -14.47, 0.225, -24.86]}, {"time": 0.3, "x": 4.67, "y": -24.86, "curve": [0.392, 4.67, 0.575, 0, 0.392, -24.86, 0.575, -4.63]}, {"time": 0.6667, "y": -4.63, "curve": [0.7, 0, 0.767, 0, 0.7, -4.63, 0.767, 0]}, {"time": 0.8, "curve": [0.806, 1.19, 0.825, 1.84, 0.806, 17, 0.825, 26.23]}, {"time": 0.8333, "x": 1.84, "y": 26.23, "curve": [0.867, 1.84, 0.933, -1.38, 0.867, 26.23, 0.933, -57.83]}, {"time": 0.9667, "x": -1.38, "y": -57.83, "curve": [1.017, -1.38, 1.117, 2.1, 1.017, -57.83, 1.117, 18.57]}, {"time": 1.1667, "x": 2.1, "y": 18.57, "curve": [1.208, 2.1, 1.292, 0, 1.208, 18.57, 1.292, 0]}, {"time": 1.3333, "curve": [1.371, 0, 1.417, 0.71, 1.371, 0, 1.417, 10.17]}, {"time": 1.4667, "x": 1.84, "y": 26.23, "curve": [1.517, 1.84, 1.617, 2.1, 1.517, 26.23, 1.617, 18.57]}, {"time": 1.6667, "x": 2.1, "y": 18.57, "curve": [1.717, 2.1, 1.817, 0, 1.717, 18.57, 1.817, 0]}, {"time": 1.8667}]}, "GOO_2": {"rotate": [{"value": 2.37, "curve": [0.252, 2.86, 0.499, 3.32]}, {"time": 0.6667, "value": 3.32}], "translate": [{"x": 0.02, "y": -8.05, "curve": [0.087, 4.92, 0.166, 7.89, 0.087, 10.37, 0.166, 21.52]}, {"time": 0.2333, "x": 7.89, "y": 21.52, "curve": [0.342, 7.89, 0.558, 0.02, 0.342, 21.52, 0.558, -8.05]}, {"time": 0.6667, "x": 0.02, "y": -8.05, "curve": [0.7, 0.02, 0.767, -5.59, 0.7, -8.05, 0.767, 13.68]}, {"time": 0.8, "x": -5.59, "y": 13.68, "curve": [0.806, -1.97, 0.825, 0, 0.806, -0.62, 0.825, -8.39]}, {"time": 0.8333, "y": -8.39, "curve": [0.867, 0, 0.933, 3.17, 0.867, -8.39, 0.933, 44.73]}, {"time": 0.9667, "x": 3.17, "y": 44.73, "curve": [1.017, 3.17, 1.117, -3.59, 1.017, 44.73, 1.117, 3.43]}, {"time": 1.1667, "x": -3.59, "y": 3.43, "curve": [1.208, -3.59, 1.292, -5.59, 1.208, 3.43, 1.292, 13.68]}, {"time": 1.3333, "x": -5.59, "y": 13.68, "curve": [1.371, -5.59, 1.417, -3.42, 1.371, 13.68, 1.417, 5.12]}, {"time": 1.4667, "y": -8.39, "curve": [1.517, 0, 1.617, -3.59, 1.517, -8.39, 1.617, 3.43]}, {"time": 1.6667, "x": -3.59, "y": 3.43, "curve": [1.717, -3.59, 1.817, -5.59, 1.717, 3.43, 1.817, 13.68]}, {"time": 1.8667, "x": -5.59, "y": 13.68}]}, "GOO_3": {"rotate": [{"value": 4.3, "curve": [0.252, 3.02, 0.499, 1.82]}, {"time": 0.6667, "value": 1.82}], "translate": [{"y": -6.01, "curve": [0.14, 0.97, 0.273, 1.81, 0.14, 7.69, 0.273, 19.52]}, {"time": 0.3667, "x": 1.81, "y": 19.52, "curve": [0.442, 1.81, 0.592, 0, 0.442, 19.52, 0.592, -6.01]}, {"time": 0.6667, "y": -6.01, "curve": [0.7, 0, 0.767, 0, 0.7, -6.01, 0.767, 0]}, {"time": 0.8, "curve": [0.806, 0, 0.825, 0, 0.806, 20.99, 0.825, 32.4]}, {"time": 0.8333, "y": 32.4, "curve": [0.867, 0, 0.933, -1.08, 0.867, 32.4, 0.933, -40.51]}, {"time": 0.9667, "x": -1.08, "y": -40.51, "curve": [1.017, -1.08, 1.117, 2.55, 1.017, -40.51, 1.117, 15.26]}, {"time": 1.1667, "x": 2.55, "y": 15.26, "curve": [1.208, 2.55, 1.292, 0, 1.208, 15.26, 1.292, 0]}, {"time": 1.3333, "curve": [1.371, 0, 1.417, 0, 1.371, 0, 1.417, 12.56]}, {"time": 1.4667, "y": 32.4, "curve": [1.517, 0, 1.617, 2.55, 1.517, 32.4, 1.617, 15.26]}, {"time": 1.6667, "x": 2.55, "y": 15.26, "curve": [1.717, 2.55, 1.817, 0, 1.717, 15.26, 1.817, 0]}, {"time": 1.8667}]}, "NECKLACE_HANDLE": {"rotate": [{"time": 0.6667, "curve": [0.7, 0, 0.767, 16.55]}, {"time": 0.8, "value": 16.55, "curve": "stepped"}, {"time": 1.4667, "value": 16.55, "curve": [1.517, 16.55, 1.617, 0]}, {"time": 1.6667}], "translate": [{"y": 0.34, "curve": [0.012, 4.64, 0.025, 9.61, 0.012, -0.09, 0.025, -0.56]}, {"time": 0.0333, "x": 9.61, "y": -0.56, "curve": [0.058, 9.61, 0.108, 26.28, 0.058, -0.56, 0.108, 28.23]}, {"time": 0.1333, "x": 26.28, "y": 28.23, "curve": "stepped"}, {"time": 0.6667, "x": 26.28, "y": 28.23, "curve": [0.7, 26.28, 0.767, 9.61, 0.7, 28.23, 0.767, -0.56]}, {"time": 0.8, "x": 9.61, "y": -0.56}], "scale": [{"time": 0.0333, "curve": [0.058, 1, 0.108, 0.657, 0.058, 1, 0.108, 0.317]}, {"time": 0.1333, "x": 0.657, "y": 0.317, "curve": "stepped"}, {"time": 1.4667, "x": 0.657, "y": 0.317, "curve": [1.517, 0.657, 1.617, 1, 1.517, 0.317, 1.617, 1]}, {"time": 1.6667}]}, "NECKLACE": {"rotate": [{"time": 0.0333, "curve": [0.058, 0, 0.108, -13.75]}, {"time": 0.1333, "value": -13.75, "curve": "stepped"}, {"time": 0.6667, "value": -13.75, "curve": [0.7, -13.75, 0.767, 22.79]}, {"time": 0.8, "value": 22.79, "curve": "stepped"}, {"time": 1.4667, "value": 22.79, "curve": [1.517, 22.79, 1.617, 0]}, {"time": 1.6667}], "translate": [{"x": 3.65, "y": 2.07, "curve": [0.008, 3.65, 0.025, 0, 0.008, 2.07, 0.025, 0]}, {"time": 0.0333, "curve": [0.058, 0, 0.108, 69.62, 0.058, 0, 0.108, 6.79]}, {"time": 0.1333, "x": 69.62, "y": 6.79, "curve": "stepped"}, {"time": 0.6667, "x": 69.62, "y": 6.79, "curve": [0.7, 69.62, 0.767, 4.47, 0.7, 6.79, 0.767, -48.87]}, {"time": 0.8, "x": 4.47, "y": -48.87, "curve": "stepped"}, {"time": 1.4667, "x": 4.47, "y": -48.87, "curve": [1.517, 4.47, 1.617, 0, 1.517, -48.87, 1.617, 0]}, {"time": 1.6667}]}, "ARM_LEFT_HANDLE": {"rotate": [{"time": 0.6667, "curve": [0.683, 0, 0.717, 3.75]}, {"time": 0.7333, "value": 3.75, "curve": "stepped"}, {"time": 1.4667, "value": 3.75, "curve": [1.517, 3.75, 1.617, 0]}, {"time": 1.6667}], "translate": [{"x": 5.01, "y": 0.59, "curve": [0.009, 31.84, 0.04, 76.24, 0.009, -13.32, 0.04, -36.35]}, {"time": 0.0667, "x": 76.24, "y": -36.35, "curve": [0.087, 87.83, 0.111, 96.74, 0.087, -97.42, 0.111, -144.32]}, {"time": 0.1333, "x": 96.74, "y": -144.32, "curve": "stepped"}, {"time": 0.6667, "x": 96.74, "y": -144.32, "curve": [0.675, 96.74, 0.687, 82.34, 0.675, -144.32, 0.687, -75.31]}, {"time": 0.7, "x": 65.07, "y": 7.5, "curve": [0.711, 35.39, 0.722, 3.24, 0.711, 14.86, 0.722, 22.82]}, {"time": 0.7333, "x": -26.44, "y": 30.17, "curve": [0.745, -65.57, 0.756, -101.43, 0.745, -2.64, 0.756, -32.71]}, {"time": 0.7667, "x": -127.52, "y": -54.58, "curve": [0.779, -72.98, 0.791, -38.9, 0.779, -150.02, 0.791, -209.67]}, {"time": 0.8, "x": -38.9, "y": -209.67, "curve": [0.808, -38.9, 0.825, -37.44, 0.808, -209.67, 0.825, -195.29]}, {"time": 0.8333, "x": -37.44, "y": -195.29, "curve": [0.982, -37.44, 1.272, -44.85, 0.982, -195.29, 1.272, -212.52]}, {"time": 1.4333, "x": -45.52, "y": -214.09, "curve": "stepped"}, {"time": 1.4667, "x": -103.23, "y": -214.12, "curve": [1.484, -103.23, 1.508, -79.45, 1.484, -214.12, 1.508, -149.37]}, {"time": 1.5333, "x": -47.75, "y": -63.04, "curve": [1.544, -39.47, 1.555, -30.16, 1.544, -61.38, 1.555, -59.52]}, {"time": 1.5667, "x": -20.85, "y": -57.66, "curve": [1.604, 9.59, 1.642, 40.03, 1.604, -31.56, 1.642, -5.46]}, {"time": 1.6667, "x": 40.03, "y": -5.46, "curve": [1.725, 40.03, 1.842, 5.01, 1.725, -5.46, 1.842, 0.59]}, {"time": 1.9, "x": 5.01, "y": 0.59}]}, "ARM_RIGHT_HANDLE": {"translate": [{"x": 2.39, "y": 0.21, "curve": [0.013, -25.84, 0.024, -46.01, 0.013, 20.54, 0.024, 35.07]}, {"time": 0.0333, "x": -46.01, "y": 35.07, "curve": [0.042, -46.01, 0.058, -13.36, 0.042, 35.07, 0.058, 148.8]}, {"time": 0.0667, "x": -13.36, "y": 148.8, "curve": [0.091, 19, 0.114, 38.32, 0.091, 177.29, 0.114, 194.29]}, {"time": 0.1333, "x": 38.32, "y": 194.29, "curve": [0.203, 70.67, 0.508, 74.48, 0.203, 191.75, 0.508, 191.45]}, {"time": 0.6667, "x": 74.87, "y": 191.42, "curve": [0.678, 60.62, 0.689, 49.46, 0.678, 209.52, 0.689, 223.7]}, {"time": 0.7, "x": 40.81, "y": 234.69, "curve": [0.711, 4.23, 0.723, -21.7, 0.711, 225.56, 0.723, 219.08]}, {"time": 0.7333, "x": -39.4, "y": 214.66, "curve": [0.745, -76.59, 0.756, -102.25, 0.745, 187.75, 0.756, 169.19]}, {"time": 0.7667, "x": -118.6, "y": 157.35, "curve": [0.778, -44.66, 0.789, -12.77, 0.778, 28.97, 0.789, -26.42]}, {"time": 0.8, "x": -12.77, "y": -26.42, "curve": [0.808, -12.77, 0.825, -10.37, 0.808, -26.42, 0.825, -12.17]}, {"time": 0.8333, "x": -10.37, "y": -12.17, "curve": "stepped"}, {"time": 1.4667, "x": -10.37, "y": -12.17, "curve": [1.517, -10.37, 1.617, 50.4, 1.517, -12.17, 1.617, -6.04]}, {"time": 1.6667, "x": 50.4, "y": -6.04, "curve": [1.725, 50.4, 1.842, 2.39, 1.725, -6.04, 1.842, 0.21]}, {"time": 1.9, "x": 2.39, "y": 0.21}]}, "CAPE_CORNER_RIGHT": {"rotate": [{"value": 30.04, "curve": [0.034, 30.04, 0.082, 22.36]}, {"time": 0.1333, "value": 13.15, "curve": [0.203, 17.14, 0.284, 24.02]}, {"time": 0.3667, "value": 31.72, "curve": [0.439, 31.72, 0.567, 30.81]}, {"time": 0.6667, "value": 30.35, "curve": [0.692, 30.16, 0.715, 30.04]}, {"time": 0.7333, "value": 30.04, "curve": [0.75, 30.04, 0.783, -31.85]}, {"time": 0.8, "value": -31.85, "curve": [0.895, -31.85, 1.027, -45.5]}, {"time": 1.1667, "value": -61.89, "curve": [1.242, -61.89, 1.392, -31.85]}, {"time": 1.4667, "value": -31.85, "curve": [1.518, -31.85, 1.59, -3.72]}, {"time": 1.6667, "value": 30.04}], "translate": [{"x": 6.97, "y": -2.09, "curve": [0.013, 42.2, 0.025, 72.39, 0.013, -26.95, 0.025, -48.27]}, {"time": 0.0333, "x": 72.39, "y": -48.27, "curve": [0.058, 72.39, 0.108, -12.61, 0.058, -48.27, 0.108, 13.44]}, {"time": 0.1333, "x": -12.61, "y": 13.44, "curve": [0.222, 3.42, 0.307, 17.16, 0.222, -2.54, 0.307, -16.24]}, {"time": 0.3667, "x": 17.16, "y": -16.24, "curve": [0.408, 17.16, 0.492, 26.66, 0.408, -16.24, 0.492, -5.04]}, {"time": 0.5333, "x": 26.66, "y": -5.04, "curve": [0.569, 26.66, 0.616, 23.42, 0.569, -5.04, 0.616, -4.43]}, {"time": 0.6667, "x": 19.09, "y": -3.61, "curve": [0.714, 11.42, 0.768, -0.09, 0.714, 3.65, 0.768, 14.53]}, {"time": 0.8, "x": -0.09, "y": 14.53, "curve": [0.838, -0.04, 0.874, 0, 0.838, 6.71, 0.874, 0]}, {"time": 0.9, "curve": [0.983, 0, 1.15, 17.87, 0.983, 0, 1.15, -5.36]}, {"time": 1.2333, "x": 17.87, "y": -5.36, "curve": [1.291, 17.87, 1.381, 11.93, 1.291, -5.36, 1.381, -3.58]}, {"time": 1.4667, "x": 6.97, "y": -2.09}], "scale": [{"curve": [0.008, 1, 0.025, 2.339, 0.008, 1, 0.025, 1]}, {"time": 0.0333, "x": 2.339, "curve": [0.05, 2.339, 0.083, 1, 0.05, 1, 0.083, 1]}, {"time": 0.1}]}, "CAPE_CORNER_LEFT": {"rotate": [{"value": -8.13, "curve": [0.138, -8.54, 0.275, -8.95]}, {"time": 0.3667, "value": -8.95, "curve": [0.442, -12.74, 0.516, -16.25]}, {"time": 0.5667, "value": -16.25, "curve": [0.594, -16.25, 0.629, -15.06]}, {"time": 0.6667, "value": -13.26, "curve": [0.711, -11.55, 0.768, -8.13]}, {"time": 0.8, "value": -8.13, "curve": [0.85, -4.06, 0.9, 0]}, {"time": 0.9333, "curve": [1.017, 0, 1.183, -16.25]}, {"time": 1.2667, "value": -16.25, "curve": [1.317, -16.25, 1.392, -12.19]}, {"time": 1.4667, "value": -8.13}], "translate": [{"curve": [0.008, 0, 0.025, 74.97, 0.008, 0, 0.025, 48.58]}, {"time": 0.0333, "x": 74.97, "y": 48.58, "curve": [0.058, 74.97, 0.108, -13.44, 0.058, 48.58, 0.108, 0.25]}, {"time": 0.1333, "x": -13.44, "y": 0.25, "curve": [0.192, -13.44, 0.308, 25.97, 0.192, 0.25, 0.308, 9.21]}, {"time": 0.3667, "x": 25.97, "y": 9.21, "curve": [0.439, 25.97, 0.567, 11.85, 0.439, 9.21, 0.567, 4.2]}, {"time": 0.6667, "x": 4.79, "y": 1.7, "curve": [0.692, 1.92, 0.715, 0, 0.692, 0.68, 0.715, 0]}, {"time": 0.7333, "curve": "stepped"}, {"time": 0.8, "curve": [0.892, 0, 1.075, 12.36, 0.892, 0, 1.075, 14.88]}, {"time": 1.1667, "x": 12.36, "y": 14.88, "curve": [1.242, 12.36, 1.392, 0, 1.242, 14.88, 1.392, 0]}, {"time": 1.4667}], "scale": [{"curve": [0.008, 1, 0.025, 1.625, 0.008, 1, 0.025, 1]}, {"time": 0.0333, "x": 1.625, "curve": [0.05, 1.625, 0.083, 1, 0.05, 1, 0.083, 1]}, {"time": 0.1}]}, "SLEEVE_LEFT": {"rotate": [{"value": -27.05, "curve": [0.167, -27.05, 0.5, 0]}, {"time": 0.6667}], "translate": [{"x": 11.1, "curve": "stepped"}, {"time": 0.8, "x": 11.1, "curve": [0.812, -3.8, 0.824, -13.01, 0.812, 1.81, 0.824, 2.93]}, {"time": 0.8333, "x": -13.01, "y": 2.93, "curve": [0.85, -13.01, 0.883, 38.19, 0.85, 2.93, 0.883, 13.19]}, {"time": 0.9, "x": 38.19, "y": 13.19, "curve": [0.933, 38.19, 1, -13.38, 0.933, 13.19, 1, 27.71]}, {"time": 1.0333, "x": -13.38, "y": 27.71, "curve": [1.083, -13.38, 1.183, 5.25, 1.083, 27.71, 1.183, 23.81]}, {"time": 1.2333, "x": 5.25, "y": 23.81, "curve": [1.292, 5.25, 1.408, -16, 1.292, 23.81, 1.408, -0.27]}, {"time": 1.4667, "x": -16, "y": -0.27, "curve": [1.517, -16, 1.617, 11.1, 1.517, -0.27, 1.617, 0]}, {"time": 1.6667, "x": 11.1}]}, "SLEEVE_RIGHT": {"rotate": [{"value": -21.61, "curve": [0.167, -21.61, 0.5, 0]}, {"time": 0.6667}], "translate": [{"x": 7.6, "curve": "stepped"}, {"time": 0.6667, "x": 7.6, "curve": [0.699, -10.84, 0.767, -16.42, 0.699, 21.31, 0.767, 27.76]}, {"time": 0.8, "x": -16.42, "y": 27.76, "curve": [0.804, 26.3, 0.85, 50.98, 0.804, 7.47, 0.85, -4.25]}, {"time": 0.8667, "x": 50.98, "y": -4.25, "curve": [0.9, 50.98, 0.967, 2.53, 0.9, -4.25, 0.967, 22.82]}, {"time": 1, "x": 2.53, "y": 22.82, "curve": [1.042, 2.53, 1.125, 22.08, 1.042, 22.82, 1.125, 15.8]}, {"time": 1.1667, "x": 22.08, "y": 15.8, "curve": [1.183, 22.08, 1.217, 13.66, 1.183, 15.8, 1.217, 22.66]}, {"time": 1.2333, "x": 13.66, "y": 22.66, "curve": "stepped"}, {"time": 1.4667, "x": 13.66, "y": 22.66, "curve": [1.517, 13.66, 1.617, 7.6, 1.517, 22.66, 1.617, 0]}, {"time": 1.6667, "x": 7.6}]}, "HORN_R": {"rotate": [{"value": 12.05, "curve": [0.248, 6.23, 0.501, 0]}, {"time": 0.6667}]}, "HORN_L": {"rotate": [{"value": -13.44, "curve": [0.248, -6.95, 0.501, 0]}, {"time": 0.6667}]}, "HEAD": {"rotate": [{"value": 1.32, "curve": [0.042, 8.47, 0.088, 18.01]}, {"time": 0.1333, "value": 27.55, "curve": [0.239, -0.7, 0.533, -4.41]}, {"time": 0.6667, "value": -4.41, "curve": [0.7, -4.41, 0.667, 34.35]}, {"time": 0.8, "value": 34.35, "curve": [0.867, 34.35, 1, 1.98]}, {"time": 1.0667, "value": 1.98, "curve": "stepped"}, {"time": 1.4667, "value": 1.98, "curve": [1.517, 1.98, 1.617, 1.32]}, {"time": 1.6667, "value": 1.32}], "translate": [{"x": -5.85, "y": -0.18, "curve": [0.254, -8.48, 0.495, -10.67, 0.254, -0.28, 0.495, -0.35]}, {"time": 0.6667, "x": -10.67, "y": -0.35}]}, "FACE": {"rotate": [{"time": 0.1333, "value": 31.72, "curve": [0.267, 31.72, 0.533, -0.24]}, {"time": 0.6667, "value": -0.24, "curve": "stepped"}, {"time": 1.4667, "value": -0.24, "curve": [1.517, -0.24, 1.617, 0]}, {"time": 1.6667}], "translate": [{"x": 3.05, "y": -0.15, "curve": [0.048, -16.96, 0.101, -41.98, 0.048, 5.73, 0.101, 13.09]}, {"time": 0.1333, "x": -41.98, "y": 13.09, "curve": [0.204, -19.25, 0.533, -18.25, 0.204, 30.63, 0.533, 31.41]}, {"time": 0.6667, "x": -18.25, "y": 31.41, "curve": [0.7, -18.25, 0.667, -14.73, 0.7, 31.41, 0.667, 4.56]}, {"time": 0.8, "x": -14.73, "y": 4.56, "curve": [0.867, -14.73, 1, 9.07, 0.867, 4.56, 1, 3.05]}, {"time": 1.0667, "x": 9.07, "y": 3.05, "curve": "stepped"}, {"time": 1.4667, "x": 9.07, "y": 3.05, "curve": [1.5, 9.07, 1.567, -38.98, 1.5, 3.05, 1.567, 7.55]}, {"time": 1.6, "x": -38.98, "y": 7.55, "curve": [1.656, -13.73, 1.714, 15.02, 1.656, 10.76, 1.714, 14.42]}, {"time": 1.7667, "x": 35.59, "y": 17.04, "curve": [1.817, 15.24, 1.864, 1.37, 1.817, 3.57, 1.864, -5.61]}, {"time": 1.9, "x": 1.37, "y": -5.61}]}, "SHOULDER_RIGHT": {"rotate": [{"time": 0.0333, "value": -44.87, "curve": [0.058, -44.87, 0.108, -163.48]}, {"time": 0.1333, "value": -163.48, "curve": [0.267, -163.48, 0.533, -188.06]}, {"time": 0.6667, "value": -188.06, "curve": [0.675, -188.06, 0.692, -44.88]}, {"time": 0.7, "value": -44.88, "curve": [0.892, -44.88, 1.275, -35.25]}, {"time": 1.4667, "value": -35.25, "curve": [1.517, -35.25, 1.617, -44.88]}, {"time": 1.6667, "value": -44.88}], "translate": [{"time": 0.0333, "x": -11.94, "y": -27.17, "curve": [0.042, -11.94, 0.058, -55.08, 0.042, -27.17, 0.058, 8.4]}, {"time": 0.0667, "x": -55.08, "y": 8.4, "curve": [0.091, -59.14, 0.117, -64.21, 0.091, 44.53, 0.117, 89.7]}, {"time": 0.1333, "x": -64.21, "y": 89.7, "curve": [0.327, -48.21, 0.506, -43.76, 0.327, 100.64, 0.506, 103.69]}, {"time": 0.6667, "x": -43.76, "y": 103.69, "curve": [0.679, -52.13, 0.69, -54.47, 0.679, 73.5, 0.69, 65.09]}, {"time": 0.7, "x": -54.47, "y": 65.09, "curve": [0.708, -54.47, 0.725, -62.98, 0.708, 65.09, 0.725, 59.83]}, {"time": 0.7333, "x": -62.98, "y": 59.83, "curve": [0.742, -62.98, 0.754, -61.82, 0.742, 59.83, 0.754, 31.24]}, {"time": 0.7667, "x": -60.67, "y": 2.66, "curve": [0.779, -28.59, 0.792, 3.49, 0.779, -2.44, 0.792, -7.54]}, {"time": 0.8, "x": 3.49, "y": -7.54, "curve": "stepped"}, {"time": 1.4667, "x": 3.49, "y": -7.54, "curve": [1.517, 3.49, 1.617, 0, 1.517, -7.54, 1.617, 0]}, {"time": 1.6667}], "scale": [{"time": 0.0333, "curve": [0.058, 1, 0.108, 1.004, 0.058, 1, 0.108, 0.794]}, {"time": 0.1333, "x": 1.004, "y": 0.794, "curve": [0.258, 1.004, 0.508, 1.004, 0.258, 0.794, 0.508, 0.946]}, {"time": 0.6333, "x": 1.004, "y": 0.946, "curve": [0.659, 1.002, 0.682, 1, 0.659, 0.979, 0.682, 1]}, {"time": 0.7}]}, "SHOULDER_LEFT": {"rotate": [{"time": 0.6667, "value": -136.14, "curve": [0.708, -136.14, 0.792, -34.81]}, {"time": 0.8333, "value": -34.81, "curve": "stepped"}, {"time": 1.4667, "value": -34.81, "curve": [1.517, -34.81, 1.617, 70.28]}, {"time": 1.6667, "value": 70.28}], "translate": [{"time": 0.0333, "curve": [0.058, 0, 0.108, -44.22, 0.058, 0, 0.108, -20.81]}, {"time": 0.1333, "x": -44.22, "y": -20.81, "curve": "stepped"}, {"time": 0.6667, "x": -44.22, "y": -20.81, "curve": [0.683, -44.22, 0.708, -38.53, 0.683, -20.81, 0.708, -7.71]}, {"time": 0.7333, "x": -32.85, "y": 5.38}, {"time": 0.7667, "x": -54.05, "y": -10.43}, {"time": 0.8, "x": -78.41, "y": -32.13}, {"time": 0.8333, "x": -48.6, "y": -56.48, "curve": "stepped"}, {"time": 1.4333, "x": -48.6, "y": -56.48, "curve": "stepped"}, {"time": 1.4667, "x": -87.04, "y": -47.65, "curve": [1.484, -87.04, 1.508, -78.09, 1.484, -47.65, 1.508, -27.21]}, {"time": 1.5333, "x": -66.17, "y": 0.05, "curve": [1.58, -39.7, 1.634, 0, 1.58, 0.03, 1.634, 0]}, {"time": 1.6667}], "scale": [{"time": 0.6667, "curve": [0.7, 1, 0.767, 1.173, 0.7, 1, 0.767, 0.911]}, {"time": 0.8, "x": 1.173, "y": 0.911, "curve": "stepped"}, {"time": 0.8333, "x": -1.173, "y": 0.911, "curve": "stepped"}, {"time": 1.4333, "x": -1.173, "y": 0.911, "curve": "stepped"}, {"time": 1.4667}]}, "BODY_HANDLE": {"translate": [{"time": 0.0333, "curve": [0.058, 0, 0.108, 31.05, 0.058, 0, 0.108, 62.28]}, {"time": 0.1333, "x": 31.05, "y": 62.28, "curve": [0.204, 31.54, 0.533, 31.56, 0.204, 82.31, 0.533, 83.19]}, {"time": 0.6667, "x": 31.56, "y": 83.19, "curve": [0.7, 31.56, 0.767, -0.57, 0.7, 83.19, 0.767, -67.87]}, {"time": 0.8, "x": -0.57, "y": -67.87, "curve": "stepped"}, {"time": 1.4667, "x": -0.57, "y": -67.87, "curve": [1.517, -0.57, 1.617, 0, 1.517, -67.87, 1.617, 0]}, {"time": 1.6667}]}, "HORN_LEFT": {"translate": [{"time": 0.0333, "curve": [0.058, 0, 0.108, 17.61, 0.058, 0, 0.108, -52.51]}, {"time": 0.1333, "x": 17.61, "y": -52.51, "curve": "stepped"}, {"time": 0.6667, "x": 17.61, "y": -52.51, "curve": [0.7, 17.61, 0.767, 0, 0.7, -52.51, 0.767, 0]}, {"time": 0.8}]}, "HORN_RIGHT": {"translate": [{"time": 0.0333, "curve": [0.058, 0, 0.108, -1.84, 0.058, 0, 0.108, 12.98]}, {"time": 0.1333, "x": -1.84, "y": 12.98, "curve": "stepped"}, {"time": 0.6667, "x": -1.84, "y": 12.98, "curve": [0.7, -1.84, 0.767, 0, 0.7, 12.98, 0.767, 0]}, {"time": 0.8}]}, "BODY_TOP": {"rotate": [{"time": 0.0333, "curve": [0.058, 0, 0.108, -12.07]}, {"time": 0.1333, "value": -12.07, "curve": [0.215, -12.07, 0.347, -26.09]}, {"time": 0.4667, "value": -36.61, "curve": "stepped"}, {"time": 0.6667, "value": -36.61, "curve": [0.718, -2.42, 0.765, 24.93]}, {"time": 0.8, "value": 24.93, "curve": "stepped"}, {"time": 1.4667, "value": 24.93, "curve": [1.517, 24.93, 1.617, 0]}, {"time": 1.6667}]}, "ARM_RIGHT": {"rotate": [{"time": 0.0333, "value": 54.65, "curve": [0.058, 54.65, 0.108, 63.57]}, {"time": 0.1333, "value": 63.57, "curve": "stepped"}, {"time": 0.6667, "value": 63.57, "curve": [0.675, 63.57, 0.692, -78.1]}, {"time": 0.7, "value": -78.1, "curve": [0.892, -78.1, 1.275, -39.4]}, {"time": 1.4667, "value": -39.4, "curve": [1.517, -39.4, 1.617, -78.1]}, {"time": 1.6667, "value": -78.1}], "translate": [{"time": 0.1333, "x": 2.51, "y": 17.42, "curve": "stepped"}, {"time": 0.6667, "x": 2.51, "y": 17.42, "curve": [0.675, 2.51, 0.692, 0, 0.675, 17.42, 0.692, 0]}, {"time": 0.7}], "scale": [{"time": 0.1333, "x": 1.287, "y": 0.666, "curve": "stepped"}, {"time": 0.6667, "x": 1.287, "y": 0.666, "curve": [0.675, 1.287, 0.692, 1, 0.675, 0.666, 0.692, 1]}, {"time": 0.7}]}, "ARM_LEFT": {"rotate": [{"time": 0.5, "value": -0.09, "curve": "stepped"}, {"time": 0.6667, "value": -0.09, "curve": [0.867, -0.09, 1.267, -26.46]}, {"time": 1.4667, "value": -26.46, "curve": [1.517, -26.46, 1.617, -0.09]}, {"time": 1.6667, "value": -0.09}]}, "NECK1": {"rotate": [{"time": 0.6667, "value": -35.9, "curve": [0.867, -35.9, 1.267, 2.38]}, {"time": 1.4667, "value": 2.38}]}, "NECK2": {"rotate": [{"time": 0.6667, "value": -75.18, "curve": [0.867, -75.18, 1.267, -39.74]}, {"time": 1.4667, "value": -39.74, "curve": [1.517, -39.74, 1.617, 11.31]}, {"time": 1.6667, "value": 11.31}]}, "Necklace2": {"translate": [{"x": 1.89, "y": 11.25, "curve": [0.214, 1.35, 0.505, 0, 0.214, 8.04, 0.505, 0]}, {"time": 0.6667}]}, "Necklace4": {"translate": [{"x": 0.28, "y": 1.69, "curve": [0.25, 0.11, 0.477, 0, 0.25, 0.65, 0.477, 0]}, {"time": 0.6667}]}, "HEAD_TOP": {"rotate": [{"value": -4.72}]}}, "ik": {"ARM_LEFT_HANDLE": [{"time": 0.7, "bendPositive": false, "curve": "stepped"}, {"time": 0.7333, "curve": "stepped"}, {"time": 1.4333, "curve": "stepped"}, {"time": 1.4667, "bendPositive": false}], "ARM_RIGHT_HANDLE": [{"time": 0.0333, "bendPositive": false, "curve": [0.058, 1, 0.108, 0, 0.058, 0, 0.108, 0]}, {"time": 0.1333, "mix": 0, "bendPositive": false, "curve": "stepped"}, {"time": 0.6333, "mix": 0, "bendPositive": false, "curve": "stepped"}, {"time": 0.6667, "bendPositive": false, "curve": "stepped"}, {"time": 0.8, "bendPositive": false, "curve": "stepped"}, {"time": 0.8333}]}, "drawOrder": [{"time": 0.0333, "offsets": [{"slot": "GoatCult/Arm2", "offset": 15}, {"slot": "GoatCult/Sleeve2", "offset": 11}, {"slot": "GoatCult/Hand2", "offset": 12}]}, {"time": 0.7333, "offsets": [{"slot": "GoatCult/Weapon", "offset": 20}, {"slot": "GoatCult/Arm", "offset": 21}, {"slot": "GoatCult/Sleeve", "offset": 16}, {"slot": "GoatCult/Hand1", "offset": 17}, {"slot": "GoatCult/Arm2", "offset": 19}, {"slot": "GoatCult/Sleeve2", "offset": 15}, {"slot": "GoatCult/Hand2", "offset": 16}]}, {"time": 0.8, "offsets": [{"slot": "GoatCult/Weapon", "offset": 22}, {"slot": "Sorcerer/GoatCult/Hand5", "offset": 23}, {"slot": "GoatCult/Arm", "offset": 23}, {"slot": "GoatCult/Sleeve", "offset": 18}, {"slot": "GoatCult/Hand1", "offset": 19}, {"slot": "GoatCult/Arm2", "offset": -6}, {"slot": "GoatCult/Sleeve2", "offset": -10}, {"slot": "GoatCult/Hand2", "offset": -9}]}, {"time": 1.5667, "offsets": [{"slot": "GoatCult/Weapon", "offset": 24}, {"slot": "Sorcerer/GoatCult/Hand5", "offset": 24}, {"slot": "GoatCult/Arm", "offset": -5}, {"slot": "GoatCult/Sleeve", "offset": -8}, {"slot": "GoatCult/Hand1", "offset": -8}, {"slot": "GoatCult/Arm2", "offset": -3}, {"slot": "GoatCult/Sleeve2", "offset": -7}, {"slot": "GoatCult/Hand2", "offset": -6}]}]}, "attack-charge": {"slots": {"GoatCult/Hand1": {"attachment": [{"time": 0.1333, "name": "Hand4"}]}, "GoatCult/Hand2": {"attachment": [{"time": 0.1333}]}, "GoatCult/Weapon": {"attachment": [{"time": 0.0333, "name": "Sorcerer/GoatCult/Weapon"}]}}, "bones": {"HAND_LEFT": {"rotate": [{"value": -7.34}]}, "HAND_RIGHT": {"rotate": [{"value": -7.2, "curve": [0.051, 26.23, 0.097, 50.11]}, {"time": 0.1333, "value": 50.11}]}, "WEAPON": {"rotate": [{"time": 0.0333, "value": 97.18, "curve": [0.117, 72.37, 0.508, 71.27]}, {"time": 0.6667, "value": 71.27}], "scale": [{"time": 0.0333, "x": 0.159, "y": 0.159, "curve": [0.05, 0.159, 0.083, 1, 0.05, 0.159, 0.083, 1]}, {"time": 0.1, "curve": [0.117, 1, 0.15, 1.208, 0.117, 1, 0.15, 1.263]}, {"time": 0.1667, "x": 1.208, "y": 1.263, "curve": [0.192, 1.208, 0.242, 0.748, 0.192, 1.263, 0.242, 1.177]}, {"time": 0.2667, "x": 0.748, "y": 1.177, "curve": [0.308, 0.748, 0.392, 1, 0.308, 1.177, 0.392, 1]}, {"time": 0.4333}]}, "BODY_BTM": {"rotate": [{"value": -1.67, "curve": [0.025, 2.65, 0.05, 6.98]}, {"time": 0.0667, "value": 6.98, "curve": [0.167, 6.98, 0.067, 5.71]}, {"time": 0.4667, "value": 5.71}], "translate": [{"curve": [0.017, 0, 0.05, 1.56, 0.017, 0, 0.05, -22.11]}, {"time": 0.0667, "x": 1.56, "y": -22.11, "curve": [0.167, 1.56, 0.067, 0, 0.167, -22.11, 0.067, 18]}, {"time": 0.4667, "y": 18}], "scale": [{"y": 1.015, "curve": [0.017, 1, 0.05, 0.79, 0.017, 1.015, 0.05, 1.211]}, {"time": 0.0667, "x": 0.79, "y": 1.211, "curve": [0.092, 0.79, 0.142, 1.312, 0.092, 1.211, 0.142, 0.884]}, {"time": 0.1667, "x": 1.312, "y": 0.884, "curve": [0.192, 1.312, 0.242, 0.947, 0.192, 0.884, 0.242, 1.158]}, {"time": 0.2667, "x": 0.947, "y": 1.158, "curve": [0.308, 0.947, 0.392, 1.138, 0.308, 1.158, 0.392, 0.96]}, {"time": 0.4333, "x": 1.138, "y": 0.96}]}, "GOO_1": {"rotate": [{"value": -15.24, "curve": [0.203, -11.34, 0.434, -5.21]}, {"time": 0.6667, "value": 1.11}], "translate": [{"y": -4.63, "curve": [0.112, 2.27, 0.225, 4.67, 0.112, -14.47, 0.225, -24.86]}, {"time": 0.3, "x": 4.67, "y": -24.86, "curve": [0.392, 4.67, 0.575, 0, 0.392, -24.86, 0.575, -4.63]}, {"time": 0.6667, "y": -4.63}]}, "GOO_2": {"rotate": [{"value": 2.37, "curve": [0.252, 2.86, 0.499, 3.32]}, {"time": 0.6667, "value": 3.32}], "translate": [{"x": 0.02, "y": -8.05, "curve": [0.087, 4.92, 0.166, 7.89, 0.087, 10.37, 0.166, 21.52]}, {"time": 0.2333, "x": 7.89, "y": 21.52, "curve": [0.342, 7.89, 0.558, 0.02, 0.342, 21.52, 0.558, -8.05]}, {"time": 0.6667, "x": 0.02, "y": -8.05}]}, "GOO_3": {"rotate": [{"value": 4.3, "curve": [0.252, 3.02, 0.499, 1.82]}, {"time": 0.6667, "value": 1.82}], "translate": [{"y": -6.01, "curve": [0.14, 0.97, 0.273, 1.81, 0.14, 7.69, 0.273, 19.52]}, {"time": 0.3667, "x": 1.81, "y": 19.52, "curve": [0.442, 1.81, 0.592, 0, 0.442, 19.52, 0.592, -6.01]}, {"time": 0.6667, "y": -6.01}]}, "NECKLACE_HANDLE": {"translate": [{"y": 0.34, "curve": [0.012, 4.64, 0.025, 9.61, 0.012, -0.09, 0.025, -0.56]}, {"time": 0.0333, "x": 9.61, "y": -0.56, "curve": [0.058, 9.61, 0.108, 26.28, 0.058, -0.56, 0.108, 28.23]}, {"time": 0.1333, "x": 26.28, "y": 28.23}], "scale": [{"time": 0.0333, "curve": [0.058, 1, 0.108, 0.657, 0.058, 1, 0.108, 0.317]}, {"time": 0.1333, "x": 0.657, "y": 0.317}]}, "NECKLACE": {"rotate": [{"time": 0.0333, "curve": [0.058, 0, 0.108, -13.75]}, {"time": 0.1333, "value": -13.75}], "translate": [{"x": 3.65, "y": 2.07, "curve": [0.008, 3.65, 0.025, 0, 0.008, 2.07, 0.025, 0]}, {"time": 0.0333, "curve": [0.058, 0, 0.108, 69.62, 0.058, 0, 0.108, 6.79]}, {"time": 0.1333, "x": 69.62, "y": 6.79}]}, "ARM_LEFT_HANDLE": {"translate": [{"x": 5.01, "y": 0.59, "curve": [0.009, 31.84, 0.04, 76.24, 0.009, -13.32, 0.04, -36.35]}, {"time": 0.0667, "x": 76.24, "y": -36.35, "curve": [0.087, 87.83, 0.111, 96.74, 0.087, -97.42, 0.111, -144.32]}, {"time": 0.1333, "x": 96.74, "y": -144.32, "curve": [0.204, 84.99, 0.533, 80.54, 0.204, -146.07, 0.533, -146.73]}, {"time": 0.6667, "x": 80.54, "y": -146.73, "curve": "stepped"}, {"time": 1.3333, "x": 80.54, "y": -146.73}]}, "ARM_RIGHT_HANDLE": {"translate": [{"x": 2.39, "y": 0.21, "curve": [0.013, -25.84, 0.024, -46.01, 0.013, 20.54, 0.024, 35.07]}, {"time": 0.0333, "x": -46.01, "y": 35.07, "curve": [0.042, -46.01, 0.058, -13.36, 0.042, 35.07, 0.058, 148.8]}, {"time": 0.0667, "x": -13.36, "y": 148.8, "curve": [0.091, 19, 0.114, 38.32, 0.091, 177.29, 0.114, 194.29]}, {"time": 0.1333, "x": 38.32, "y": 194.29, "curve": [0.203, 70.67, 0.508, 74.48, 0.203, 191.75, 0.508, 191.45]}, {"time": 0.6667, "x": 74.87, "y": 191.42}]}, "CAPE_CORNER_RIGHT": {"rotate": [{"value": 30.04, "curve": [0.034, 30.04, 0.082, 22.36]}, {"time": 0.1333, "value": 13.15, "curve": [0.203, 17.14, 0.284, 24.02]}, {"time": 0.3667, "value": 31.72, "curve": [0.439, 31.72, 0.567, 30.81]}, {"time": 0.6667, "value": 30.35}], "translate": [{"x": 6.97, "y": -2.09, "curve": [0.013, 42.2, 0.025, 72.39, 0.013, -26.95, 0.025, -48.27]}, {"time": 0.0333, "x": 72.39, "y": -48.27, "curve": [0.058, 72.39, 0.108, -12.61, 0.058, -48.27, 0.108, 13.44]}, {"time": 0.1333, "x": -12.61, "y": 13.44, "curve": [0.222, 3.42, 0.307, 17.16, 0.222, -2.54, 0.307, -16.24]}, {"time": 0.3667, "x": 17.16, "y": -16.24, "curve": [0.408, 17.16, 0.492, 26.66, 0.408, -16.24, 0.492, -5.04]}, {"time": 0.5333, "x": 26.66, "y": -5.04, "curve": [0.569, 26.66, 0.616, 23.42, 0.569, -5.04, 0.616, -4.43]}, {"time": 0.6667, "x": 19.09, "y": -3.61}], "scale": [{"curve": [0.008, 1, 0.025, 2.339, 0.008, 1, 0.025, 1]}, {"time": 0.0333, "x": 2.339, "curve": [0.05, 2.339, 0.083, 1, 0.05, 1, 0.083, 1]}, {"time": 0.1}]}, "CAPE_CORNER_LEFT": {"rotate": [{"value": -8.13, "curve": [0.138, -8.54, 0.275, -8.95]}, {"time": 0.3667, "value": -8.95, "curve": [0.442, -12.74, 0.516, -16.25]}, {"time": 0.5667, "value": -16.25, "curve": [0.594, -16.25, 0.629, -15.06]}, {"time": 0.6667, "value": -13.26}], "translate": [{"curve": [0.008, 0, 0.025, 74.97, 0.008, 0, 0.025, 48.58]}, {"time": 0.0333, "x": 74.97, "y": 48.58, "curve": [0.058, 74.97, 0.108, -13.44, 0.058, 48.58, 0.108, 0.25]}, {"time": 0.1333, "x": -13.44, "y": 0.25, "curve": [0.192, -13.44, 0.308, 25.97, 0.192, 0.25, 0.308, 9.21]}, {"time": 0.3667, "x": 25.97, "y": 9.21, "curve": [0.439, 25.97, 0.567, 11.85, 0.439, 9.21, 0.567, 4.2]}, {"time": 0.6667, "x": 4.79, "y": 1.7}], "scale": [{"curve": [0.008, 1, 0.025, 1.625, 0.008, 1, 0.025, 1]}, {"time": 0.0333, "x": 1.625, "curve": [0.05, 1.625, 0.083, 1, 0.05, 1, 0.083, 1]}, {"time": 0.1}]}, "SLEEVE_LEFT": {"rotate": [{"value": -27.05, "curve": [0.167, -27.05, 0.5, 0]}, {"time": 0.6667}], "translate": [{"x": 11.1}]}, "SLEEVE_RIGHT": {"rotate": [{"value": -21.61, "curve": [0.167, -21.61, 0.5, 0]}, {"time": 0.6667}], "translate": [{"x": 7.6}]}, "HORN_R": {"rotate": [{"value": 12.05, "curve": [0.248, 6.23, 0.501, 0]}, {"time": 0.6667}]}, "HORN_L": {"rotate": [{"value": -13.44, "curve": [0.248, -6.95, 0.501, 0]}, {"time": 0.6667}]}, "HEAD": {"rotate": [{"value": 1.32, "curve": [0.042, 8.47, 0.088, 18.01]}, {"time": 0.1333, "value": 27.55, "curve": [0.239, -0.7, 0.533, -4.41]}, {"time": 0.6667, "value": -4.41}], "translate": [{"x": -5.85, "y": -0.18, "curve": [0.254, -8.48, 0.495, -10.67, 0.254, -0.28, 0.495, -0.35]}, {"time": 0.6667, "x": -10.67, "y": -0.35}]}, "FACE": {"rotate": [{"time": 0.1333, "value": 31.72, "curve": [0.267, 31.72, 0.533, -0.24]}, {"time": 0.6667, "value": -0.24}], "translate": [{"x": 3.05, "y": -0.15, "curve": [0.048, -16.96, 0.101, -41.98, 0.048, 5.73, 0.101, 13.09]}, {"time": 0.1333, "x": -41.98, "y": 13.09, "curve": [0.204, -19.25, 0.533, -18.25, 0.204, 30.63, 0.533, 31.41]}, {"time": 0.6667, "x": -18.25, "y": 31.41}]}, "SHOULDER_RIGHT": {"rotate": [{"time": 0.0333, "value": -44.87, "curve": [0.058, -44.87, 0.108, -163.48]}, {"time": 0.1333, "value": -163.48, "curve": [0.267, -163.48, 0.533, -188.06]}, {"time": 0.6667, "value": -188.06}], "translate": [{"time": 0.0333, "x": -11.94, "y": -27.17, "curve": [0.042, -11.94, 0.058, -55.08, 0.042, -27.17, 0.058, 8.4]}, {"time": 0.0667, "x": -55.08, "y": 8.4, "curve": [0.091, -59.14, 0.117, -64.21, 0.091, 44.53, 0.117, 89.7]}, {"time": 0.1333, "x": -64.21, "y": 89.7, "curve": [0.327, -48.21, 0.506, -43.76, 0.327, 100.64, 0.506, 103.69]}, {"time": 0.6667, "x": -43.76, "y": 103.69}], "scale": [{"time": 0.0333, "curve": [0.058, 1, 0.108, 1.004, 0.058, 1, 0.108, 0.998]}, {"time": 0.1333, "x": 1.004, "y": 0.998, "curve": [0.263, 1.004, 0.489, 1.004, 0.263, 0.998, 0.489, 0.963]}, {"time": 0.6667, "x": 1.004, "y": 0.946}]}, "SHOULDER_LEFT": {"rotate": [{"time": 0.6667, "value": -136.14}], "translate": [{"time": 0.0333, "curve": [0.058, 0, 0.108, -44.22, 0.058, 0, 0.108, -20.81]}, {"time": 0.1333, "x": -44.22, "y": -20.81}]}, "BODY_HANDLE": {"translate": [{"time": 0.0333, "curve": [0.058, 0, 0.108, 31.05, 0.058, 0, 0.108, 62.28]}, {"time": 0.1333, "x": 31.05, "y": 62.28, "curve": [0.204, 31.54, 0.533, 31.56, 0.204, 82.31, 0.533, 83.19]}, {"time": 0.6667, "x": 31.56, "y": 83.19}]}, "HORN_LEFT": {"translate": [{"time": 0.0333, "curve": [0.058, 0, 0.108, 17.61, 0.058, 0, 0.108, -52.51]}, {"time": 0.1333, "x": 17.61, "y": -52.51}]}, "HORN_RIGHT": {"translate": [{"time": 0.0333, "curve": [0.058, 0, 0.108, -1.84, 0.058, 0, 0.108, 12.98]}, {"time": 0.1333, "x": -1.84, "y": 12.98}]}, "BODY_TOP": {"rotate": [{"time": 0.0333, "curve": [0.058, 0, 0.108, -12.07]}, {"time": 0.1333, "value": -12.07, "curve": [0.215, -12.07, 0.347, -26.09]}, {"time": 0.4667, "value": -36.61}]}, "ARM_RIGHT": {"rotate": [{"time": 0.0333, "value": 54.65, "curve": [0.058, 54.65, 0.108, 63.57]}, {"time": 0.1333, "value": 63.57}], "translate": [{"time": 0.1333, "x": 2.51, "y": 17.42}], "scale": [{"time": 0.1333, "x": 1.287, "y": 0.666}]}, "ARM_LEFT": {"rotate": [{"time": 0.5, "value": -0.09}]}, "NECK1": {"rotate": [{"time": 0.6667, "value": -35.9}]}, "NECK2": {"rotate": [{"time": 0.6667, "value": -75.18}]}, "Necklace2": {"translate": [{"x": 1.89, "y": 11.25, "curve": [0.214, 1.35, 0.505, 0, 0.214, 8.04, 0.505, 0]}, {"time": 0.6667}]}, "Necklace4": {"translate": [{"x": 0.28, "y": 1.69, "curve": [0.25, 0.11, 0.477, 0, 0.25, 0.65, 0.477, 0]}, {"time": 0.6667}]}, "HEAD_TOP": {"rotate": [{"value": -4.72}]}}, "ik": {"ARM_RIGHT_HANDLE": [{"time": 0.0333, "bendPositive": false, "curve": [0.058, 1, 0.108, 0, 0.058, 0, 0.108, 0]}, {"time": 0.1333, "mix": 0, "bendPositive": false}]}, "drawOrder": [{"time": 0.0333, "offsets": [{"slot": "GoatCult/Arm2", "offset": 15}, {"slot": "GoatCult/Sleeve2", "offset": 11}, {"slot": "GoatCult/Hand2", "offset": 12}]}]}, "attack-impact": {"slots": {"GoatCult/Hand1": {"attachment": [{"name": "Hand4"}, {"time": 0.8, "name": "Hand2"}]}, "GoatCult/Hand2": {"attachment": [{}, {"time": 0.0333, "name": "Hand4"}, {"time": 0.7667, "name": "Hand3"}]}, "GoatCult/Weapon": {"attachment": [{"name": "Sorcerer/GoatCult/Weapon"}, {"time": 0.8667}]}, "Sorcerer/GoatCult/Hand5": {"attachment": [{"time": 0.1333, "name": "Hand5"}, {"time": 0.7667}]}}, "bones": {"HAND_LEFT": {"rotate": [{"value": -7.34, "curve": [0.009, -7.34, 0.021, 2.59]}, {"time": 0.0333, "value": 14.51, "curve": [0.07, -11.98, 0.109, -45.09]}, {"time": 0.1333, "value": -45.09, "curve": "stepped"}, {"time": 0.1667, "value": 32.42, "curve": "stepped"}, {"time": 0.8, "value": 32.42, "curve": [0.85, 32.42, 0.95, -7.34]}, {"time": 1, "value": -7.34}], "translate": [{"curve": [0.033, 0, 0.1, 7.8, 0.033, 0, 0.1, 33.51]}, {"time": 0.1333, "x": 7.8, "y": 33.51, "curve": "stepped"}, {"time": 0.1667, "x": -12.94, "y": 22.36, "curve": "stepped"}, {"time": 0.8, "x": -12.94, "y": 22.36, "curve": [0.85, -12.94, 0.95, 0, 0.85, 22.36, 0.95, 0]}, {"time": 1}], "scale": [{"time": 0.1333, "curve": "stepped"}, {"time": 0.1667, "y": -1, "curve": "stepped"}, {"time": 0.7667, "y": -1, "curve": "stepped"}, {"time": 0.8}]}, "HAND_RIGHT": {"rotate": [{"value": 50.11, "curve": [0.008, 50.11, 0.025, -7.2]}, {"time": 0.0333, "value": -7.2, "curve": [0.072, 22.96, 0.106, 44.51]}, {"time": 0.1333, "value": 44.51, "curve": [0.142, 44.51, 0.158, 33.45]}, {"time": 0.1667, "value": 33.45, "curve": "stepped"}, {"time": 0.8, "value": 33.45, "curve": [0.85, 33.45, 0.95, -7.2]}, {"time": 1, "value": -7.2}], "translate": [{"x": 9.07, "y": 67.85, "curve": [0.008, 9.07, 0.025, 1.91, 0.008, 67.85, 0.025, -1.66]}, {"time": 0.0333, "x": 1.91, "y": -1.66, "curve": [0.058, 1.91, 0.108, -14.02, 0.058, -1.66, 0.108, -32.05]}, {"time": 0.1333, "x": -14.02, "y": -32.05, "curve": [0.142, -14.02, 0.158, -10.97, 0.142, -32.05, 0.158, -4.29]}, {"time": 0.1667, "x": -10.97, "y": -4.29, "curve": "stepped"}, {"time": 0.8, "x": -10.97, "y": -4.29, "curve": [0.85, -10.97, 0.95, 0, 0.85, -4.29, 0.95, 0]}, {"time": 1}]}, "WEAPON": {"rotate": [{"value": 71.27, "curve": [0.025, 71.27, 0.075, 84.63]}, {"time": 0.1, "value": 84.63, "curve": [0.108, 84.63, 0.125, 170.36]}, {"time": 0.1333, "value": 170.36, "curve": "stepped"}, {"time": 0.7333, "value": 170.36, "curve": [0.742, 170.36, 0.754, 162.83]}, {"time": 0.7667, "value": 153.81, "curve": [0.803, 118.84, 0.842, 75.13]}, {"time": 0.8667, "value": 75.13}], "translate": [{"curve": [0.033, 0, 0.1, 7.03, 0.033, 0, 0.1, 27.27]}, {"time": 0.1333, "x": 7.03, "y": 27.27, "curve": "stepped"}, {"time": 0.7333, "x": 7.03, "y": 27.27, "curve": [0.767, 7.03, 0.833, 0, 0.767, 27.27, 0.833, 0]}, {"time": 0.8667}], "scale": [{"time": 0.1, "curve": [0.117, 1, 0.15, 1.208, 0.117, 1, 0.15, 1.656]}, {"time": 0.1667, "x": 1.208, "y": 1.656, "curve": [0.192, 1.208, 0.242, 1, 0.192, 1.656, 0.242, 0.787]}, {"time": 0.2667, "y": 0.787, "curve": [0.3, 1, 0.367, 1, 0.3, 0.787, 0.367, 1.347]}, {"time": 0.4, "y": 1.347, "curve": [0.442, 1, 0.525, 1, 0.442, 1.347, 0.525, 1]}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.7333, "curve": [0.767, 1, 0.833, 0.108, 0.767, 1, 0.833, 0.108]}, {"time": 0.8667, "x": 0.108, "y": 0.108}]}, "BODY_BTM": {"rotate": [{"value": 5.71, "curve": [0.033, 5.71, 0.1, -25.02]}, {"time": 0.1333, "value": -25.02, "curve": "stepped"}, {"time": 0.8, "value": -25.02, "curve": [0.85, -25.02, 0.95, -1.67]}, {"time": 1, "value": -1.67}], "translate": [{"y": 18, "curve": [0.025, -23.78, 0.05, -31.26, 0.025, -0.17, 0.05, -5.9]}, {"time": 0.0667, "x": -31.26, "y": -5.9, "curve": [0.111, -31.26, 0.17, -18.24, 0.111, -5.9, 0.17, -3.44]}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.8, "curve": [0.85, 0, 0.95, 14.22, 0.85, 0, 0.95, 55.71]}, {"time": 1, "x": 14.22, "y": 55.71, "curve": [1.058, 14.22, 1.175, 0, 1.058, 55.71, 1.175, 0]}, {"time": 1.2333}], "scale": [{"x": 1.138, "y": 0.96, "curve": [0.017, 1.138, 0.05, 1, 0.017, 0.96, 0.05, 1]}, {"time": 0.0667, "curve": [0.083, 1, 0.117, 0.79, 0.083, 1, 0.117, 1.211]}, {"time": 0.1333, "x": 0.79, "y": 1.211, "curve": [0.158, 0.79, 0.208, 1.312, 0.158, 1.211, 0.208, 0.884]}, {"time": 0.2333, "x": 1.312, "y": 0.884, "curve": [0.258, 1.312, 0.308, 0.947, 0.258, 0.884, 0.308, 1.158]}, {"time": 0.3333, "x": 0.947, "y": 1.158, "curve": [0.375, 0.947, 0.458, 1.138, 0.375, 1.158, 0.458, 0.96]}, {"time": 0.5, "x": 1.138, "y": 0.96, "curve": "stepped"}, {"time": 0.8, "x": 1.138, "y": 0.96, "curve": [0.85, 1.138, 0.95, 1, 0.85, 0.96, 0.95, 1]}, {"time": 1}]}, "GOO_1": {"rotate": [{"value": 1.11}], "translate": [{"y": -4.63, "curve": [0.033, 0, 0.1, 0, 0.033, -4.63, 0.1, 0]}, {"time": 0.1333, "curve": [0.139, 1.19, 0.158, 1.84, 0.139, 17, 0.158, 26.23]}, {"time": 0.1667, "x": 1.84, "y": 26.23, "curve": [0.2, 1.84, 0.267, -1.38, 0.2, 26.23, 0.267, -57.83]}, {"time": 0.3, "x": -1.38, "y": -57.83, "curve": [0.35, -1.38, 0.45, 2.1, 0.35, -57.83, 0.45, 18.57]}, {"time": 0.5, "x": 2.1, "y": 18.57, "curve": [0.575, 2.1, 0.725, 1.84, 0.575, 18.57, 0.725, 26.23]}, {"time": 0.8, "x": 1.84, "y": 26.23, "curve": [0.85, 1.84, 0.95, 2.1, 0.85, 26.23, 0.95, 18.57]}, {"time": 1, "x": 2.1, "y": 18.57, "curve": [1.05, 2.1, 1.15, 0, 1.05, 18.57, 1.15, 0]}, {"time": 1.2}]}, "GOO_2": {"rotate": [{"value": 3.32}], "translate": [{"x": 0.02, "y": -8.05, "curve": [0.033, 0.02, 0.1, -5.59, 0.033, -8.05, 0.1, 13.68]}, {"time": 0.1333, "x": -5.59, "y": 13.68, "curve": [0.139, -1.97, 0.158, 0, 0.139, -0.62, 0.158, -8.39]}, {"time": 0.1667, "y": -8.39, "curve": [0.2, 0, 0.267, 3.17, 0.2, -8.39, 0.267, 44.73]}, {"time": 0.3, "x": 3.17, "y": 44.73, "curve": [0.35, 3.17, 0.45, -3.59, 0.35, 44.73, 0.45, 3.43]}, {"time": 0.5, "x": -3.59, "y": 3.43, "curve": [0.575, -3.59, 0.725, 0, 0.575, 3.43, 0.725, -8.39]}, {"time": 0.8, "y": -8.39, "curve": [0.85, 0, 0.95, 21.24, 0.85, -8.39, 0.95, 5.83]}, {"time": 1, "x": 21.24, "y": 5.83, "curve": [1.05, 21.24, 1.15, -5.59, 1.05, 5.83, 1.15, 13.68]}, {"time": 1.2, "x": -5.59, "y": 13.68}]}, "GOO_3": {"rotate": [{"value": 1.82}], "translate": [{"y": -6.01, "curve": [0.033, 0, 0.1, 0, 0.033, -6.01, 0.1, 0]}, {"time": 0.1333, "curve": [0.139, 0, 0.158, 0, 0.139, 20.99, 0.158, 32.4]}, {"time": 0.1667, "y": 32.4, "curve": [0.2, 0, 0.267, -1.08, 0.2, 32.4, 0.267, -40.51]}, {"time": 0.3, "x": -1.08, "y": -40.51, "curve": [0.35, -1.08, 0.45, 2.55, 0.35, -40.51, 0.45, 15.26]}, {"time": 0.5, "x": 2.55, "y": 15.26, "curve": [0.575, 2.55, 0.725, 0, 0.575, 15.26, 0.725, 32.4]}, {"time": 0.8, "y": 32.4, "curve": [0.85, 0, 0.95, 4.6, 0.85, 32.4, 0.95, -3.69]}, {"time": 1, "x": 4.6, "y": -3.69, "curve": [1.05, 4.6, 1.15, 0, 1.05, -3.69, 1.15, 0]}, {"time": 1.2}]}, "NECKLACE_HANDLE": {"rotate": [{"curve": [0.033, 0, 0.1, 16.55]}, {"time": 0.1333, "value": 16.55, "curve": "stepped"}, {"time": 0.8, "value": 16.55, "curve": [0.85, 16.55, 0.95, 0]}, {"time": 1}], "translate": [{"x": 26.28, "y": 28.23, "curve": [0.033, 26.28, 0.1, 9.61, 0.033, 28.23, 0.1, -0.56]}, {"time": 0.1333, "x": 9.61, "y": -0.56, "curve": "stepped"}, {"time": 0.8333, "x": 9.61, "y": -0.56, "curve": [0.883, 9.61, 0.983, 25.02, 0.883, -0.56, 0.983, 22.31]}, {"time": 1.0333, "x": 25.02, "y": 22.31, "curve": [1.083, 25.02, 1.183, 9.61, 1.083, 22.31, 1.183, -0.56]}, {"time": 1.2333, "x": 9.61, "y": -0.56}], "scale": [{"x": 0.657, "y": 0.317, "curve": "stepped"}, {"time": 0.8, "x": 0.657, "y": 0.317, "curve": [0.85, 0.657, 0.95, 1, 0.85, 0.317, 0.95, 1]}, {"time": 1}]}, "NECKLACE": {"rotate": [{"value": -13.75, "curve": [0.033, -13.75, 0.1, 22.79]}, {"time": 0.1333, "value": 22.79, "curve": "stepped"}, {"time": 0.8, "value": 22.79, "curve": [0.85, 22.79, 0.95, 0]}, {"time": 1}], "translate": [{"x": 69.62, "y": 6.79, "curve": [0.033, 69.62, 0.1, 4.47, 0.033, 6.79, 0.1, -48.87]}, {"time": 0.1333, "x": 4.47, "y": -48.87, "curve": "stepped"}, {"time": 0.8, "x": 4.47, "y": -48.87, "curve": [0.85, 4.47, 0.95, 0, 0.85, -48.87, 0.95, 0]}, {"time": 1}]}, "ARM_LEFT_HANDLE": {"rotate": [{"curve": [0.017, 0, 0.05, 3.75]}, {"time": 0.0667, "value": 3.75, "curve": "stepped"}, {"time": 0.8, "value": 3.75, "curve": [0.85, 3.75, 0.95, 0]}, {"time": 1}], "translate": [{"x": 80.54, "y": -146.73, "curve": [0.009, 80.54, 0.021, 73.51, 0.009, -146.73, 0.021, -76.62]}, {"time": 0.0333, "x": 65.07, "y": 7.5, "curve": [0.044, 35.39, 0.056, 3.24, 0.044, 14.86, 0.056, 22.82]}, {"time": 0.0667, "x": -26.44, "y": 30.17, "curve": [0.078, -65.57, 0.09, -101.43, 0.078, -2.64, 0.09, -32.71]}, {"time": 0.1, "x": -127.52, "y": -54.58, "curve": [0.113, -72.98, 0.124, -38.9, 0.113, -150.02, 0.124, -209.67]}, {"time": 0.1333, "x": -38.9, "y": -209.67, "curve": [0.142, -38.9, 0.158, -37.44, 0.142, -209.67, 0.158, -195.29]}, {"time": 0.1667, "x": -37.44, "y": -195.29, "curve": [0.315, -37.44, 0.605, -44.85, 0.315, -195.29, 0.605, -212.52]}, {"time": 0.7667, "x": -45.52, "y": -214.09, "curve": "stepped"}, {"time": 0.8, "x": -103.23, "y": -214.12, "curve": [0.818, -103.23, 0.841, -79.45, 0.818, -214.12, 0.841, -149.37]}, {"time": 0.8667, "x": -47.75, "y": -63.04, "curve": [0.878, -39.47, 0.889, -30.16, 0.878, -61.38, 0.889, -59.52]}, {"time": 0.9, "x": -20.85, "y": -57.66, "curve": [0.938, 9.59, 0.975, 40.03, 0.938, -31.56, 0.975, -5.46]}, {"time": 1, "x": 40.03, "y": -5.46, "curve": [1.058, 40.03, 1.175, 5.01, 1.058, -5.46, 1.175, 0.59]}, {"time": 1.2333, "x": 5.01, "y": 0.59}]}, "ARM_RIGHT_HANDLE": {"translate": [{"x": 74.87, "y": 191.42, "curve": [0.011, 60.62, 0.022, 49.46, 0.011, 209.52, 0.022, 223.7]}, {"time": 0.0333, "x": 40.81, "y": 234.69, "curve": [0.045, 4.23, 0.056, -21.7, 0.045, 225.56, 0.056, 219.08]}, {"time": 0.0667, "x": -39.4, "y": 214.66, "curve": [0.078, -76.59, 0.089, -102.25, 0.078, 187.75, 0.089, 169.19]}, {"time": 0.1, "x": -118.6, "y": 157.35, "curve": [0.111, -44.66, 0.122, -12.77, 0.111, 28.97, 0.122, -26.42]}, {"time": 0.1333, "x": -12.77, "y": -26.42, "curve": [0.142, -12.77, 0.158, -10.37, 0.142, -26.42, 0.158, -12.17]}, {"time": 0.1667, "x": -10.37, "y": -12.17, "curve": "stepped"}, {"time": 0.8, "x": -10.37, "y": -12.17, "curve": [0.85, -10.37, 0.95, 50.4, 0.85, -12.17, 0.95, -6.04]}, {"time": 1, "x": 50.4, "y": -6.04, "curve": [1.058, 50.4, 1.175, 2.39, 1.058, -6.04, 1.175, 0.21]}, {"time": 1.2333, "x": 2.39, "y": 0.21}]}, "CAPE_CORNER_RIGHT": {"rotate": [{"value": 30.35, "curve": [0.025, 30.16, 0.048, 30.04]}, {"time": 0.0667, "value": 30.04, "curve": [0.083, 30.04, 0.117, -31.85]}, {"time": 0.1333, "value": -31.85, "curve": [0.228, -31.85, 0.36, -45.5]}, {"time": 0.5, "value": -61.89, "curve": [0.575, -61.89, 0.725, -31.85]}, {"time": 0.8, "value": -31.85, "curve": [0.852, -31.85, 0.924, -3.72]}, {"time": 1, "value": 30.04}], "translate": [{"x": 19.09, "y": -3.61, "curve": [0.047, 11.42, 0.101, -0.09, 0.047, 3.65, 0.101, 14.53]}, {"time": 0.1333, "x": -0.09, "y": 14.53, "curve": [0.171, -4.7, 0.208, -8.66, 0.171, 38.45, 0.208, 58.96]}, {"time": 0.2333, "x": -8.66, "y": 58.96, "curve": [0.317, -8.66, 0.483, 15.31, 0.317, 58.96, 0.483, 11.39]}, {"time": 0.5667, "x": 15.31, "y": 11.39, "curve": [0.624, 15.31, 0.715, 10.76, 0.624, 11.39, 0.715, 4.04]}, {"time": 0.8, "x": 6.97, "y": -2.09}]}, "CAPE_CORNER_LEFT": {"rotate": [{"value": -13.26, "curve": [0.044, -11.55, 0.101, -8.13]}, {"time": 0.1333, "value": -8.13, "curve": [0.183, -4.06, 0.233, 0]}, {"time": 0.2667, "curve": [0.35, 0, 0.517, -16.25]}, {"time": 0.6, "value": -16.25, "curve": [0.65, -16.25, 0.725, -12.19]}, {"time": 0.8, "value": -8.13}], "translate": [{"x": 4.79, "y": 1.7, "curve": [0.025, 1.92, 0.048, 0, 0.025, 0.68, 0.048, 0]}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.1333, "curve": [0.168, 0, 0.216, 13.46, 0.168, 0, 0.216, 17.42]}, {"time": 0.2667, "x": 30.6, "y": 39.59, "curve": [0.35, 22.92, 0.443, 12.36, 0.35, 29.19, 0.443, 14.88]}, {"time": 0.5, "x": 12.36, "y": 14.88, "curve": [0.575, 12.36, 0.725, 0, 0.575, 14.88, 0.725, 0]}, {"time": 0.8}]}, "SLEEVE_LEFT": {"translate": [{"x": 11.1, "curve": "stepped"}, {"time": 0.1333, "x": 11.1, "curve": [0.146, -3.8, 0.157, -13.01, 0.146, 1.81, 0.157, 2.93]}, {"time": 0.1667, "x": -13.01, "y": 2.93, "curve": [0.183, -13.01, 0.217, 38.19, 0.183, 2.93, 0.217, 13.19]}, {"time": 0.2333, "x": 38.19, "y": 13.19, "curve": [0.267, 38.19, 0.333, -13.38, 0.267, 13.19, 0.333, 27.71]}, {"time": 0.3667, "x": -13.38, "y": 27.71, "curve": [0.417, -13.38, 0.517, 5.25, 0.417, 27.71, 0.517, 23.81]}, {"time": 0.5667, "x": 5.25, "y": 23.81, "curve": [0.625, 5.25, 0.742, -3.24, 0.625, 23.81, 0.742, 21.96]}, {"time": 0.8, "x": -3.24, "y": 21.96, "curve": [0.85, -3.24, 0.95, 11.1, 0.85, 21.96, 0.95, 0]}, {"time": 1, "x": 11.1}]}, "SLEEVE_RIGHT": {"translate": [{"x": 7.6, "curve": [0.032, -10.84, 0.1, -16.42, 0.032, 21.31, 0.1, 27.76]}, {"time": 0.1333, "x": -16.42, "y": 27.76, "curve": [0.137, 26.3, 0.183, 50.98, 0.137, 7.47, 0.183, -4.25]}, {"time": 0.2, "x": 50.98, "y": -4.25, "curve": [0.233, 50.98, 0.3, 2.53, 0.233, -4.25, 0.3, 22.82]}, {"time": 0.3333, "x": 2.53, "y": 22.82, "curve": [0.375, 2.53, 0.458, 22.08, 0.375, 22.82, 0.458, 15.8]}, {"time": 0.5, "x": 22.08, "y": 15.8, "curve": [0.517, 22.08, 0.55, 13.66, 0.517, 15.8, 0.55, 22.66]}, {"time": 0.5667, "x": 13.66, "y": 22.66, "curve": "stepped"}, {"time": 0.8, "x": 13.66, "y": 22.66, "curve": [0.85, 13.66, 0.95, 7.6, 0.85, 22.66, 0.95, 0]}, {"time": 1, "x": 7.6}]}, "HEAD": {"rotate": [{"value": -4.41, "curve": [0.033, -4.41, 0, 34.35]}, {"time": 0.1333, "value": 34.35, "curve": [0.2, 34.35, 0.333, 1.98]}, {"time": 0.4, "value": 1.98, "curve": "stepped"}, {"time": 0.8, "value": 1.98, "curve": [0.85, 1.98, 0.95, 1.32]}, {"time": 1, "value": 1.32}], "translate": [{"x": -10.67, "y": -0.35}]}, "FACE": {"rotate": [{"value": -0.24, "curve": "stepped"}, {"time": 0.8, "value": -0.24, "curve": [0.85, -0.24, 0.95, 0]}, {"time": 1}], "translate": [{"x": -18.25, "y": 31.41, "curve": [0.033, -18.25, 0, -14.73, 0.033, 31.41, 0, 4.56]}, {"time": 0.1333, "x": -14.73, "y": 4.56, "curve": [0.2, -14.73, 0.333, 24.37, 0.2, 4.56, 0.333, -1.74]}, {"time": 0.4, "x": 24.37, "y": -1.74, "curve": [0.5, 24.37, 0.7, 15.35, 0.5, -1.74, 0.7, 5.88]}, {"time": 0.8, "x": 15.35, "y": 5.88, "curve": [0.833, 15.35, 0.9, -38.98, 0.833, 5.88, 0.9, 7.55]}, {"time": 0.9333, "x": -38.98, "y": 7.55, "curve": [0.989, -13.73, 1.048, 15.02, 0.989, 10.76, 1.048, 14.42]}, {"time": 1.1, "x": 35.59, "y": 17.04, "curve": [1.151, 15.24, 1.197, 1.37, 1.151, 3.57, 1.197, -5.61]}, {"time": 1.2333, "x": 1.37, "y": -5.61}]}, "SHOULDER_RIGHT": {"rotate": [{"value": 166.45, "curve": [0.008, 166.45, 0.025, 315.12]}, {"time": 0.0333, "value": 315.12, "curve": [0.225, 315.12, 0.608, 324.75]}, {"time": 0.8, "value": 324.75, "curve": [0.85, 324.75, 0.95, 315.12]}, {"time": 1, "value": 315.12}], "translate": [{"x": -43.76, "y": 103.69, "curve": [0.012, -52.13, 0.023, -54.47, 0.012, 73.5, 0.023, 65.09]}, {"time": 0.0333, "x": -54.47, "y": 65.09, "curve": [0.042, -54.47, 0.058, -62.98, 0.042, 65.09, 0.058, 59.83]}, {"time": 0.0667, "x": -62.98, "y": 59.83, "curve": [0.075, -62.98, 0.088, -61.82, 0.075, 59.83, 0.088, 31.24]}, {"time": 0.1, "x": -60.67, "y": 2.66, "curve": [0.113, -28.59, 0.125, 3.49, 0.113, -2.44, 0.125, -7.54]}, {"time": 0.1333, "x": 3.49, "y": -7.54, "curve": "stepped"}, {"time": 0.8, "x": 3.49, "y": -7.54, "curve": [0.85, 3.49, 0.95, 0, 0.85, -7.54, 0.95, 0]}, {"time": 1}], "scale": [{"x": 1.004, "y": 0.946, "curve": [0.013, 1.002, 0.024, 1, 0.013, 0.979, 0.024, 1]}, {"time": 0.0333}]}, "SHOULDER_LEFT": {"rotate": [{"value": -136.14, "curve": [0.042, -136.14, 0.125, -34.81]}, {"time": 0.1667, "value": -34.81, "curve": "stepped"}, {"time": 0.8, "value": -34.81, "curve": [0.85, -34.81, 0.95, 70.28]}, {"time": 1, "value": 70.28}], "translate": [{"x": -44.22, "y": -20.81, "curve": [0.017, -44.22, 0.042, -38.53, 0.017, -20.81, 0.042, -7.71]}, {"time": 0.0667, "x": -32.85, "y": 5.38}, {"time": 0.1, "x": -54.05, "y": -10.43}, {"time": 0.1333, "x": -78.41, "y": -32.13}, {"time": 0.1667, "x": -48.6, "y": -56.48, "curve": "stepped"}, {"time": 0.7667, "x": -48.6, "y": -56.48, "curve": "stepped"}, {"time": 0.8, "x": -87.04, "y": -47.65, "curve": [0.818, -87.04, 0.841, -78.09, 0.818, -47.65, 0.841, -27.21]}, {"time": 0.8667, "x": -66.17, "y": 0.05, "curve": [0.914, -39.7, 0.968, 0, 0.914, 0.03, 0.968, 0]}, {"time": 1}], "scale": [{"curve": [0.033, 1, 0.1, 1.173, 0.033, 1, 0.1, 0.911]}, {"time": 0.1333, "x": 1.173, "y": 0.911, "curve": "stepped"}, {"time": 0.1667, "x": -1.173, "y": 0.911, "curve": "stepped"}, {"time": 0.7667, "x": -1.173, "y": 0.911, "curve": "stepped"}, {"time": 0.8}]}, "BODY_HANDLE": {"translate": [{"x": 31.56, "y": 83.19, "curve": [0.033, 31.56, 0.1, -0.57, 0.033, 83.19, 0.1, -67.87]}, {"time": 0.1333, "x": -0.57, "y": -67.87, "curve": "stepped"}, {"time": 0.8, "x": -0.57, "y": -67.87, "curve": [0.85, -0.57, 0.95, 0, 0.85, -67.87, 0.95, 0]}, {"time": 1}]}, "HORN_LEFT": {"translate": [{"x": 17.61, "y": -52.51, "curve": [0.033, 17.61, 0.1, 0, 0.033, -52.51, 0.1, 0]}, {"time": 0.1333}]}, "HORN_RIGHT": {"translate": [{"x": -1.84, "y": 12.98, "curve": [0.033, -1.84, 0.1, 0, 0.033, 12.98, 0.1, 0]}, {"time": 0.1333}]}, "BODY_TOP": {"rotate": [{"value": -36.61, "curve": [0.051, -2.42, 0.099, 24.93]}, {"time": 0.1333, "value": 24.93, "curve": "stepped"}, {"time": 0.8, "value": 24.93, "curve": [0.85, 24.93, 0.95, 0]}, {"time": 1}]}, "ARM_RIGHT": {"rotate": [{"value": 64.53, "curve": [0.008, 64.53, 0.025, -78.1]}, {"time": 0.0333, "value": -78.1, "curve": [0.225, -78.1, 0.608, -39.4]}, {"time": 0.8, "value": -39.4, "curve": [0.85, -39.4, 0.95, -78.1]}, {"time": 1, "value": -78.1}], "translate": [{"x": 2.51, "y": 17.42, "curve": [0.008, 2.51, 0.025, 0, 0.008, 17.42, 0.025, 0]}, {"time": 0.0333}], "scale": [{"x": 1.287, "y": 0.666, "curve": [0.008, 1.287, 0.025, 1, 0.008, 0.666, 0.025, 1]}, {"time": 0.0333}]}, "ARM_LEFT": {"rotate": [{"value": -0.09, "curve": [0.2, -0.09, 0.6, -26.46]}, {"time": 0.8, "value": -26.46, "curve": [0.85, -26.46, 0.95, -0.09]}, {"time": 1, "value": -0.09}]}, "NECK1": {"rotate": [{"value": -35.9, "curve": [0.2, -35.9, 0.6, 2.38]}, {"time": 0.8, "value": 2.38}]}, "NECK2": {"rotate": [{"value": -75.18, "curve": [0.2, -75.18, 0.6, -39.74]}, {"time": 0.8, "value": -39.74, "curve": [0.85, -39.74, 0.95, 11.31]}, {"time": 1, "value": 11.31}]}, "NECK3": {"translate": [{"curve": [0.039, 0, 0.084, 2.21, 0.039, 0, 0.084, 6.2]}, {"time": 0.1333, "x": 5.89, "y": 16.53, "curve": "stepped"}, {"time": 0.8, "x": 5.89, "y": 16.53, "curve": [0.862, 4.42, 0.951, 0, 0.862, 12.4, 0.951, 0]}, {"time": 1}]}}, "ik": {"ARM_LEFT_HANDLE": [{"time": 0.0333, "bendPositive": false, "curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.7667, "curve": "stepped"}, {"time": 0.8, "bendPositive": false}], "ARM_RIGHT_HANDLE": [{"bendPositive": false, "curve": "stepped"}, {"time": 0.1333, "bendPositive": false, "curve": "stepped"}, {"time": 0.1667}]}, "drawOrder": [{"offsets": [{"slot": "GoatCult/Arm2", "offset": 15}, {"slot": "GoatCult/Sleeve2", "offset": 11}, {"slot": "GoatCult/Hand2", "offset": 12}]}, {"time": 0.0667, "offsets": [{"slot": "GoatCult/Weapon", "offset": 20}, {"slot": "GoatCult/Arm", "offset": 21}, {"slot": "GoatCult/Sleeve", "offset": 16}, {"slot": "GoatCult/Hand1", "offset": 17}, {"slot": "GoatCult/Arm2", "offset": 19}, {"slot": "GoatCult/Sleeve2", "offset": 15}, {"slot": "GoatCult/Hand2", "offset": 16}]}, {"time": 0.1333, "offsets": [{"slot": "GoatCult/Weapon", "offset": 22}, {"slot": "Sorcerer/GoatCult/Hand5", "offset": 23}, {"slot": "GoatCult/Arm", "offset": 23}, {"slot": "GoatCult/Sleeve", "offset": 18}, {"slot": "GoatCult/Hand1", "offset": 19}, {"slot": "GoatCult/Arm2", "offset": -6}, {"slot": "GoatCult/Sleeve2", "offset": -10}, {"slot": "GoatCult/Hand2", "offset": -9}]}, {"time": 0.9, "offsets": [{"slot": "GoatCult/Weapon", "offset": 24}, {"slot": "Sorcerer/GoatCult/Hand5", "offset": 24}, {"slot": "GoatCult/Arm", "offset": -5}, {"slot": "GoatCult/Sleeve", "offset": -8}, {"slot": "GoatCult/Hand1", "offset": -8}, {"slot": "GoatCult/Arm2", "offset": -3}, {"slot": "GoatCult/Sleeve2", "offset": -7}, {"slot": "GoatCult/Hand2", "offset": -6}]}]}, "hurt-back": {"slots": {"GoatCult/Hand1": {"attachment": [{"name": "Hand5"}]}, "GoatCult/Hand2": {"attachment": [{"name": "Hand5"}]}}, "bones": {"HAND_LEFT": {"rotate": [{"value": 13.24, "curve": [0.125, 13.24, 0.375, 25.98]}, {"time": 0.5, "value": 25.98}]}, "HAND_RIGHT": {"rotate": [{"value": -7.2, "curve": [0.125, -7.2, 0.375, -15.88]}, {"time": 0.5, "value": -15.88}]}, "BODY_TOP": {"rotate": [{"value": 23.21}], "translate": [{"curve": [0.125, 0, 0.375, -10.01, 0.125, 0, 0.375, 3.48]}, {"time": 0.5, "x": -10.01, "y": 3.48}]}, "BODY_BTM": {"rotate": [{"value": -1.67}], "translate": [{"x": 10.13, "y": -73.27, "curve": [0.011, -3.51, 0.05, -8.58, 0.011, -71, 0.05, -70.15]}, {"time": 0.0667, "x": -8.58, "y": -70.15, "curve": [0.083, -8.58, 0.117, 10.13, 0.083, -70.15, 0.117, -73.27]}, {"time": 0.1333, "x": 10.13, "y": -73.27}], "scale": [{"curve": [0.021, 1, 0.043, 0.858, 0.021, 1, 0.043, 1.041]}, {"time": 0.0667, "x": 0.592, "y": 1.116, "curve": [0.112, 0.646, 0.173, 0.862, 0.112, 1.096, 0.173, 1.012]}, {"time": 0.2333, "x": 1.078, "y": 0.928, "curve": [0.333, 1.039, 0.433, 1, 0.333, 0.964, 0.433, 1]}, {"time": 0.5}]}, "GOO_1": {"rotate": [{"value": 1.11}], "translate": [{"x": 31.57, "y": 3.6}]}, "NECKLACE_HANDLE": {"translate": [{"x": 16, "y": 17.06, "curve": [0.125, 16, 0.375, 21.15, 0.125, 17.06, 0.375, 22.43]}, {"time": 0.5, "x": 21.15, "y": 22.43}]}, "ARM_RIGHT_HANDLE": {"translate": [{"x": -101.62, "y": 104.08}]}, "CAPE_CORNER_RIGHT": {"rotate": [{"value": 30.04}], "translate": [{"x": -6.81, "y": -10.68}]}, "CAPE_CORNER_LEFT": {"rotate": [{"value": -8.13}], "translate": [{"x": -5.72, "y": -3.41}]}, "GOO_3": {"rotate": [{"value": -8.06}], "translate": [{"x": 5.61, "y": -10.99}]}, "SLEEVE_RIGHT": {"translate": [{"x": 4.1, "y": -15.03}]}, "GOO_2": {"rotate": [{"value": 36.63}], "translate": [{"x": 6.37, "y": -15.22}]}, "FACE": {"translate": [{"x": -18.55, "y": 5.93}, {"time": 0.1333, "x": -29.22, "y": 9.67}, {"time": 0.5333, "x": -18.55, "y": 5.93}], "scale": [{"x": 0.632}, {"time": 0.1333, "x": 0.479}, {"time": 0.5333, "x": 0.632}]}, "HEAD": {"rotate": [{"value": 8.18, "curve": [0.009, 42.77, 0.1, 50.78]}, {"time": 0.1333, "value": 50.78, "curve": [0.225, 50.78, 0.408, 8.18]}, {"time": 0.5, "value": 8.18}], "translate": [{"x": -34.46, "y": 12.52, "curve": [0.125, -34.46, 0.375, -40.55, 0.125, 12.52, 0.375, 18.55]}, {"time": 0.5, "x": -40.55, "y": 18.55}]}, "BODY_HANDLE": {"translate": [{"x": -33.39, "y": 1.2}]}, "NECK3": {"translate": [{"x": -9.13, "y": 10.18}]}, "SHOULDER_RIGHT": {"translate": [{"x": -88.54, "y": -12.82, "curve": [0.033, -88.54, 0.1, -82.07, 0.033, -12.82, 0.1, 11.72]}, {"time": 0.1333, "x": -82.07, "y": 11.72}]}, "SLEEVE_LEFT": {"translate": [{"x": 2.42, "y": -12.11, "curve": [0.175, -1.89, 0.343, -5.38, 0.175, -13.62, 0.343, -14.85]}, {"time": 0.5, "x": -7.8, "y": -15.69}]}, "NECKLACE": {"translate": [{"x": 3.65, "y": 2.07}]}, "Necklace3": {"translate": [{"x": -8.56, "y": 17.56}]}, "ARM_LEFT_HANDLE": {"translate": [{"x": -15.42, "y": -0.12}]}}, "ik": {"ARM_RIGHT_HANDLE": [{"bendPositive": false}]}, "drawOrder": [{"offsets": [{"slot": "GoatCult/Arm2", "offset": 14}, {"slot": "GoatCult/Sleeve2", "offset": 11}, {"slot": "GoatCult/Hand2", "offset": 11}]}, {"time": 0.1, "offsets": [{"slot": "GoatCult/Arm2", "offset": 20}, {"slot": "GoatCult/Sleeve2", "offset": 17}, {"slot": "GoatCult/Hand2", "offset": 17}]}, {"time": 0.5333, "offsets": [{"slot": "GoatCult/Arm2", "offset": 14}, {"slot": "GoatCult/Sleeve2", "offset": 11}, {"slot": "GoatCult/Hand2", "offset": 11}]}]}, "hurt-backOLD": {"slots": {"GoatCult/Hand1": {"attachment": [{"name": "Hand3"}, {"time": 0.3, "name": "Hand1"}]}, "GoatCult/Hand2": {"attachment": [{"name": "Hand3"}, {"time": 0.3, "name": "Hand1"}]}}, "bones": {"HAND_LEFT": {"rotate": [{"value": -7.34}]}, "HAND_RIGHT": {"rotate": [{"value": -7.2}]}, "BODY_BTM": {"rotate": [{"value": 5.5, "curve": [0.033, 5.5, 0.1, -6.73]}, {"time": 0.1333, "value": -6.73, "curve": [0.175, -6.73, 0.258, -1.67]}, {"time": 0.3, "value": -1.67}]}, "GOO_1": {"rotate": [{"value": -23.54, "curve": [0.003, -18.49, 0.05, -17.34]}, {"time": 0.0667, "value": -17.34, "curve": [0.125, -17.34, 0.242, 1.11]}, {"time": 0.3, "value": 1.11}], "translate": [{"x": 6.98, "y": 10.42, "curve": [0.003, 9.49, 0.05, 10.06, 0.003, 3.27, 0.05, 1.65]}, {"time": 0.0667, "x": 10.06, "y": 1.65, "curve": [0.125, 10.06, 0.242, 0, 0.125, 1.65, 0.242, -4.63]}, {"time": 0.3, "y": -4.63}]}, "GOO_2": {"rotate": [{"value": -21.33, "curve": [0.003, -16.27, 0.05, -15.13]}, {"time": 0.0667, "value": -15.13, "curve": [0.125, -15.13, 0.242, 3.32]}, {"time": 0.3, "value": 3.32}], "translate": [{"x": -9.26, "y": 5.29, "curve": [0.003, 7.05, 0.05, 10.75, 0.003, 10.6, 0.05, 11.81]}, {"time": 0.0667, "x": 10.75, "y": 11.81, "curve": [0.125, 10.75, 0.242, 0.02, 0.125, 11.81, 0.242, -8.05]}, {"time": 0.3, "x": 0.02, "y": -8.05}]}, "GOO_3": {"rotate": [{"value": -22.83, "curve": [0.003, -17.78, 0.05, -16.63]}, {"time": 0.0667, "value": -16.63, "curve": [0.125, -16.63, 0.242, 1.82]}, {"time": 0.3, "value": 1.82}], "translate": [{"x": -8.93, "y": 7.56, "curve": [0.003, -15.21, 0.05, -16.63, 0.003, 29.88, 0.05, 34.94]}, {"time": 0.0667, "x": -16.63, "y": 34.94, "curve": [0.125, -16.63, 0.242, 0, 0.125, 34.94, 0.242, -6.01]}, {"time": 0.3, "y": -6.01}]}, "NECKLACE_HANDLE": {"translate": [{"x": 27.61, "y": -12.6, "curve": [0.042, 27.61, 0.125, 23.45, 0.042, -12.6, 0.125, 30.37]}, {"time": 0.1667, "x": 23.45, "y": 30.37, "curve": [0.2, 23.45, 0.267, 9.61, 0.2, 30.37, 0.267, -0.56]}, {"time": 0.3, "x": 9.61, "y": -0.56}]}, "ARM_LEFT_HANDLE": {"translate": [{"x": -4.7, "y": 47.82, "curve": [0.017, -4.7, 0.05, 73.75, 0.017, 47.82, 0.05, -20.38]}, {"time": 0.0667, "x": 73.75, "y": -20.38, "curve": [0.125, 73.75, 0.242, 5.01, 0.125, -20.38, 0.242, 0.59]}, {"time": 0.3, "x": 5.01, "y": 0.59}]}, "ARM_RIGHT_HANDLE": {"translate": [{"x": 37, "y": 7.5, "curve": [0.017, 37, 0.041, 30.79, 0.017, 7.5, 0.041, -11.1]}, {"time": 0.0667, "x": 24, "y": -31.39, "curve": [0.125, 24, 0.242, 2.39, 0.125, -31.39, 0.242, 0.21]}, {"time": 0.3, "x": 2.39, "y": 0.21}]}, "CAPE_CORNER_RIGHT": {"rotate": [{"value": 21.87, "curve": [0.047, 19.55, 0.23, 18.99]}, {"time": 0.3, "value": 18.99}], "translate": [{"x": -26.35, "y": 31.58, "curve": [0.075, -26.35, 0.225, 0, 0.075, 31.58, 0.225, 0]}, {"time": 0.3}]}, "CAPE_CORNER_LEFT": {"rotate": [{"value": 37.3, "curve": [0.014, 0.27, 0.225, -8.13]}, {"time": 0.3, "value": -8.13}], "translate": [{"x": -2.99, "y": -4.92, "curve": [0.075, -2.99, 0.225, 0, 0.075, -4.92, 0.225, 0]}, {"time": 0.3}]}, "SLEEVE_LEFT": {"translate": [{"x": 11.1}]}, "SLEEVE_RIGHT": {"translate": [{"x": 7.6}]}, "HORN_R": {"rotate": [{"value": 30.46, "curve": [0.047, 5.89, 0.23, 0]}, {"time": 0.3}]}, "HORN_L": {"rotate": [{"value": -36.05, "curve": [0.047, -6.98, 0.23, 0]}, {"time": 0.3}]}, "HEAD": {"rotate": [{"value": 5.49, "curve": [0.025, 5.49, 0.075, 8.63]}, {"time": 0.1, "value": 8.63, "curve": [0.164, 6.54, 0.251, 1.32]}, {"time": 0.3, "value": 1.32}]}, "FACE": {"translate": [{"x": 97.87, "y": 19.72, "curve": [0.025, 97.87, 0.075, -33.19, 0.025, 19.72, 0.075, -28.41]}, {"time": 0.1, "x": -33.19, "y": -28.41, "curve": [0.15, -33.19, 0.25, 1.37, 0.15, -28.41, 0.25, -5.61]}, {"time": 0.3, "x": 1.37, "y": -5.61}]}, "BODY_TOP": {"rotate": [{"value": -30.99, "curve": [0.042, -30.99, 0.125, 18.73]}, {"time": 0.1667, "value": 18.73, "curve": [0.2, 18.73, 0.267, -2.24]}, {"time": 0.3, "value": -2.24}]}, "SHOULDER_LEFT": {"translate": [{"curve": [0.025, 0, 0.075, 10.52, 0.025, 0, 0.075, -32.79]}, {"time": 0.1, "x": 10.52, "y": -32.79, "curve": [0.15, 10.52, 0.25, 0, 0.15, -32.79, 0.25, 0]}, {"time": 0.3}]}, "root": {"translate": [{"x": -24.82, "curve": [0.017, -24.82, 0.05, 0, 0.017, 0, 0.05, 0]}, {"time": 0.0667}]}}}, "hurt-eyes": {"slots": {"Eyes": {"attachment": [{"name": "Sorcerer/Cult/Eyes_Open"}, {"time": 0.2667, "name": "Sorcerer/Cult/Eyes"}]}}}, "hurt-front": {"slots": {"GoatCult/Hand1": {"attachment": [{"name": "Hand3"}, {"time": 0.3667, "name": "Hand5"}]}, "GoatCult/Hand2": {"attachment": [{"name": "Hand3"}, {"time": 0.3667, "name": "Hand5"}]}}, "bones": {"HAND_LEFT": {"rotate": [{"value": 13.24}]}, "HAND_RIGHT": {"rotate": [{"value": -7.2}]}, "BODY_BTM": {"rotate": [{"value": -1.67}], "translate": [{"x": 10.13, "y": -73.27, "curve": [0.008, 10.13, 0.025, 33.5, 0.008, -73.27, 0.025, -70.04]}, {"time": 0.0333, "x": 33.5, "y": -70.04, "curve": [0.05, 33.5, 0.083, 10.13, 0.05, -70.04, 0.083, -73.27]}, {"time": 0.1, "x": 10.13, "y": -73.27}], "scale": [{"curve": [0.01, 1, 0.021, 1.101, 0.01, 1, 0.021, 0.916]}, {"time": 0.0333, "x": 1.284, "y": 0.763, "curve": [0.073, 1.179, 0.119, 0.979, 0.073, 0.854, 0.119, 1.029]}, {"time": 0.1667, "x": 0.727, "y": 1.249, "curve": [0.228, 0.727, 0.295, 0.861, 0.228, 1.249, 0.295, 1.12]}, {"time": 0.3667, "x": 1.106, "y": 0.885, "curve": [0.399, 1.106, 0.457, 1.04, 0.399, 0.885, 0.457, 0.957]}, {"time": 0.5, "x": 1.014, "y": 0.985}], "shear": [{"curve": [0.028, 0, 0.062, -5.12, 0.028, 0, 0.062, 0]}, {"time": 0.1, "x": -12.79, "curve": [0.156, -8.53, 0.226, 0, 0.156, 0, 0.226, 0]}, {"time": 0.2667}]}, "GOO_1": {"rotate": [{"value": 1.11}], "translate": [{"x": 31.57, "y": 3.6}]}, "GOO_2": {"rotate": [{"value": 36.63}], "translate": [{"x": 6.37, "y": -15.22}]}, "GOO_3": {"rotate": [{"value": -8.06}], "translate": [{"x": 5.61, "y": -10.99}]}, "NECKLACE_HANDLE": {"translate": [{"x": 16, "y": 17.06}]}, "NECKLACE": {"translate": [{"x": 3.65, "y": 2.07}]}, "ARM_LEFT_HANDLE": {"translate": [{"curve": [0.017, 0, 0.05, -26.76, 0.017, 0, 0.05, 1.22]}, {"time": 0.0667, "x": -26.76, "y": 1.22, "curve": [0.098, 44.64, 0.267, 81.09, 0.098, -18.83, 0.267, -29.07]}, {"time": 0.3333, "x": 81.09, "y": -29.07, "curve": [0.363, 81.09, 0.423, 50.92, 0.363, -29.07, 0.423, -21.45]}, {"time": 0.4667, "x": 10.69, "y": -11.3, "curve": [0.479, 7.55, 0.491, 4.2, 0.479, -9.95, 0.491, -8.52]}, {"time": 0.5, "x": 0.76, "y": -7.05}]}, "ARM_RIGHT_HANDLE": {"translate": [{"x": -101.62, "y": 104.08, "curve": [0, 33.12, 0.225, 57.83, 0, 9.36, 0.225, -8.01]}, {"time": 0.3, "x": 57.83, "y": -8.01, "curve": [0.332, 57.83, 0.381, 36.6, 0.332, -8.01, 0.381, -0.01]}, {"time": 0.4333, "x": 1.43, "y": 13.23, "curve": [0.444, -16.72, 0.455, -36.68, 0.444, 28.29, 0.455, 44.85]}, {"time": 0.4667, "x": -58.2, "y": 62.7, "curve": [0.478, -62.45, 0.49, -67.72, 0.478, 66.74, 0.49, 71.77]}, {"time": 0.5, "x": -73.5, "y": 77.28}]}, "CAPE_CORNER_RIGHT": {"rotate": [{"value": 30.04}], "translate": [{"x": -6.81, "y": -10.68}]}, "CAPE_CORNER_LEFT": {"rotate": [{"value": -8.13}], "translate": [{"x": -5.72, "y": -3.41}]}, "SLEEVE_LEFT": {"translate": [{"x": 2.42, "y": -12.11}]}, "SLEEVE_RIGHT": {"translate": [{"x": 4.1, "y": -15.03}]}, "HEAD": {"rotate": [{"value": 8.18, "curve": [0, -8.6, 0.1, -11.67]}, {"time": 0.1333, "value": -11.67, "curve": [0.221, -11.67, 0.429, -4.14]}, {"time": 0.5, "value": 4.01}], "translate": [{"x": -34.46, "y": 12.52, "curve": [0, -23.13, 0.1, -21.06, 0, 11.68, 0.1, 11.53]}, {"time": 0.1333, "x": -21.06, "y": 11.53, "curve": [0.221, -21.06, 0.429, -26.14, 0.221, 11.53, 0.429, 11.9]}, {"time": 0.5, "x": -31.64, "y": 12.31}]}, "FACE": {"translate": [{"x": -18.55, "y": 5.93}, {"time": 0.3, "x": 32.64, "y": -1.91, "curve": [0.35, 32.64, 0.45, -18.55, 0.35, -1.91, 0.45, 5.93]}, {"time": 0.5, "x": -18.55, "y": 5.93}], "scale": [{"x": 0.632}, {"time": 0.3, "curve": [0.35, 1, 0.45, 0.632, 0.35, 1, 0.45, 1]}, {"time": 0.5, "x": 0.632}]}, "BODY_HANDLE": {"translate": [{"x": -33.39, "y": 1.2}]}, "BODY_TOP": {"rotate": [{"value": 23.21, "curve": [0, -7.91, 0.225, -13.62]}, {"time": 0.3, "value": -13.62, "curve": [0.347, -13.62, 0.453, -1.89]}, {"time": 0.5, "value": 11.53}], "translate": [{"curve": [0, 6.48, 0.225, 7.67, 0, -13.4, 0.225, -15.86]}, {"time": 0.3, "x": 7.67, "y": -15.86, "curve": [0.347, 7.67, 0.453, 5.23, 0.347, -15.86, 0.453, -10.81]}, {"time": 0.5, "x": 2.43, "y": -5.03}]}, "SHOULDER_RIGHT": {"rotate": [{"time": 0.5, "value": -61.78}], "translate": [{"x": -88.54, "y": -12.82, "curve": [0, -37.4, 0.225, -28.03, 0, -7.31, 0.225, -6.3]}, {"time": 0.3, "x": -28.03, "y": -6.3, "curve": [0.338, -28.03, 0.416, -37.46, 0.338, -6.3, 0.416, -10.76]}, {"time": 0.4667, "x": -49.59, "y": -16.5, "curve": [0.479, -54.44, 0.491, -59.5, 0.479, -16.04, 0.491, -15.56]}, {"time": 0.5, "x": -64.67, "y": -15.08}]}, "NECK3": {"translate": [{"x": -9.13, "y": 10.18}]}, "Necklace3": {"translate": [{"x": -8.56, "y": 17.56}]}, "SHOULDER_LEFT": {"rotate": [{"time": 0.5, "value": 77.16}], "translate": [{"curve": [0, -24.14, 0.225, -28.56, 0, -4.41, 0.225, -5.22]}, {"time": 0.3, "x": -28.56, "y": -5.22, "curve": [0.347, -28.56, 0.453, -19.46, 0.347, -5.22, 0.453, -3.56]}, {"time": 0.5, "x": -9.06, "y": -1.66}]}, "ARM_LEFT": {"rotate": [{"time": 0.5, "value": -89.79}]}, "NECK1": {"rotate": [{"time": 0.5, "value": -18.07}]}, "NECK2": {"rotate": [{"time": 0.5, "value": -12.69}]}, "ARM_RIGHT": {"rotate": [{"time": 0.5, "value": 85.97}]}}, "ik": {"ARM_RIGHT_HANDLE": [{"bendPositive": false, "curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.4667, "bendPositive": false}]}, "drawOrder": [{"offsets": [{"slot": "GoatCult/Arm2", "offset": 14}, {"slot": "GoatCult/Sleeve2", "offset": 11}, {"slot": "GoatCult/Hand2", "offset": 11}]}]}, "hurt-frontOLD": {"slots": {"GoatCult/Hand1": {"attachment": [{"name": "Hand3"}, {"time": 0.3, "name": "Hand1"}]}, "GoatCult/Hand2": {"attachment": [{"name": "Hand3"}, {"time": 0.3, "name": "Hand1"}]}}, "bones": {"HAND_LEFT": {"rotate": [{"value": -7.34}]}, "HAND_RIGHT": {"rotate": [{"value": -7.2}]}, "BODY_BTM": {"rotate": [{"value": -27.99, "curve": [0.033, -27.99, 0.1, -6.73]}, {"time": 0.1333, "value": -6.73, "curve": [0.175, -6.73, 0.258, -1.67]}, {"time": 0.3, "value": -1.67}]}, "GOO_1": {"rotate": [{"value": 1.11, "curve": [0.003, -13.93, 0.05, -17.34]}, {"time": 0.0667, "value": -17.34, "curve": [0.125, -17.34, 0.242, 1.11]}, {"time": 0.3, "value": 1.11}], "translate": [{"y": -4.63, "curve": [0.003, 8.2, 0.05, 10.06, 0.003, 0.49, 0.05, 1.65]}, {"time": 0.0667, "x": 10.06, "y": 1.65, "curve": [0.125, 10.06, 0.242, 0, 0.125, 1.65, 0.242, -4.63]}, {"time": 0.3, "y": -4.63}]}, "GOO_2": {"rotate": [{"value": 3.32, "curve": [0.003, -11.72, 0.05, -15.13]}, {"time": 0.0667, "value": -15.13, "curve": [0.125, -15.13, 0.242, 3.32]}, {"time": 0.3, "value": 3.32}], "translate": [{"x": 0.02, "y": -8.05, "curve": [0.003, 8.76, 0.05, 10.75, 0.003, 8.13, 0.05, 11.81]}, {"time": 0.0667, "x": 10.75, "y": 11.81, "curve": [0.125, 10.75, 0.242, 0.02, 0.125, 11.81, 0.242, -8.05]}, {"time": 0.3, "x": 0.02, "y": -8.05}]}, "GOO_3": {"rotate": [{"value": 1.82, "curve": [0.003, -13.22, 0.05, -16.63]}, {"time": 0.0667, "value": -16.63, "curve": [0.125, -16.63, 0.242, 1.82]}, {"time": 0.3, "value": 1.82}], "translate": [{"y": -6.01, "curve": [0.003, -13.56, 0.05, -16.63, 0.003, 27.37, 0.05, 34.94]}, {"time": 0.0667, "x": -16.63, "y": 34.94, "curve": [0.125, -16.63, 0.242, 0, 0.125, 34.94, 0.242, -6.01]}, {"time": 0.3, "y": -6.01}]}, "NECKLACE_HANDLE": {"translate": [{"x": 12.68, "y": -31.85, "curve": [0.058, 12.68, 0.175, 17.72, 0.058, -31.85, 0.175, 21.05]}, {"time": 0.2333, "x": 17.72, "y": 21.05, "curve": [0.25, 17.72, 0.283, 9.61, 0.25, 21.05, 0.283, -0.56]}, {"time": 0.3, "x": 9.61, "y": -0.56}]}, "ARM_LEFT_HANDLE": {"translate": [{"x": -3.15, "y": -53.02, "curve": [0.017, -3.15, 0.05, 73.75, 0.017, -53.02, 0.05, -20.38]}, {"time": 0.0667, "x": 73.75, "y": -20.38, "curve": [0.125, 73.75, 0.242, 5.01, 0.125, -20.38, 0.242, 0.59]}, {"time": 0.3, "x": 5.01, "y": 0.59}]}, "ARM_RIGHT_HANDLE": {"translate": [{"x": 37, "y": 7.5, "curve": [0.017, 37, 0.041, 30.79, 0.017, 7.5, 0.041, -11.1]}, {"time": 0.0667, "x": 24, "y": -31.39, "curve": [0.125, 24, 0.242, 2.39, 0.125, -31.39, 0.242, 0.21]}, {"time": 0.3, "x": 2.39, "y": 0.21}]}, "CAPE_CORNER_RIGHT": {"rotate": [{"value": -39.74, "curve": [0.047, 7.62, 0.23, 18.99]}, {"time": 0.3, "value": 18.99}], "translate": [{"x": 7.87, "y": 60.12, "curve": [0.075, 7.87, 0.225, 0, 0.075, 60.12, 0.225, 0]}, {"time": 0.3}]}, "CAPE_CORNER_LEFT": {"rotate": [{"value": -8.13}], "translate": [{"x": -16.98, "y": 0.24, "curve": [0.075, -16.98, 0.225, 0, 0.075, 0.24, 0.225, 0]}, {"time": 0.3}]}, "SLEEVE_LEFT": {"translate": [{"x": -23.26, "y": 15.27, "curve": [0.009, 6.46, 0.046, 31.07, 0.009, 0.59, 0.046, -11.56]}, {"time": 0.1, "x": 51.38, "y": -21.59, "curve": [0.124, 14.96, 0.163, -7.95, 0.124, -12.45, 0.163, -6.7]}, {"time": 0.2, "x": -21.76, "y": -3.23, "curve": [0.239, 2.75, 0.277, 11.1, 0.239, -0.82, 0.277, 0]}, {"time": 0.3, "x": 11.1}]}, "SLEEVE_RIGHT": {"translate": [{"x": 7.6, "curve": [0.006, 15.82, 0.031, 22.62, 0.006, -3.68, 0.031, -6.74]}, {"time": 0.0667, "x": 28.24, "y": -9.25, "curve": [0.092, 4.61, 0.129, -11.5, 0.092, -6.2, 0.129, -4.13]}, {"time": 0.1667, "x": -22.21, "y": -2.75, "curve": [0.218, 0.27, 0.271, 7.6, 0.218, -0.67, 0.271, 0]}, {"time": 0.3, "x": 7.6}]}, "HORN_R": {"rotate": [{"value": 30.46, "curve": [0.047, 5.89, 0.23, 0]}, {"time": 0.3}]}, "HORN_L": {"rotate": [{"value": -36.05, "curve": [0.047, -6.98, 0.23, 0]}, {"time": 0.3}]}, "HEAD": {"rotate": [{"value": 5.49, "curve": [0.033, 5.49, 0.1, -17.21]}, {"time": 0.1333, "value": -17.21, "curve": [0.175, -17.21, 0.258, 1.32]}, {"time": 0.3, "value": 1.32}]}, "FACE": {"translate": [{"x": -62.28, "y": -42.41, "curve": [0.05, -62.28, 0.15, 48.6, 0.05, -42.41, 0.15, -16.45]}, {"time": 0.2, "x": 48.6, "y": -16.45, "curve": [0.225, 48.6, 0.275, 1.37, 0.225, -16.45, 0.275, -5.61]}, {"time": 0.3, "x": 1.37, "y": -5.61}]}, "BODY_TOP": {"rotate": [{"value": 31.09, "curve": [0.033, 31.09, 0.1, -2.24]}, {"time": 0.1333, "value": -2.24}]}, "SHOULDER_LEFT": {"translate": [{"curve": [0.025, 0, 0.075, 10.52, 0.025, 0, 0.075, -32.79]}, {"time": 0.1, "x": 10.52, "y": -32.79, "curve": [0.15, 10.52, 0.25, 0, 0.15, -32.79, 0.25, 0]}, {"time": 0.3}]}, "root": {"translate": [{"x": 19.71, "curve": [0.017, 19.71, 0.05, 0, 0.017, 0, 0.05, 0]}, {"time": 0.0667}]}}}, "idle": {"bones": {"BODY_BTM": {"rotate": [{"value": -1.67, "curve": [0.1, -1.01, 0.2, -0.35]}, {"time": 0.2667, "value": -0.35, "curve": [0.392, -0.35, 0.642, -2.99]}, {"time": 0.7667, "value": -2.99, "curve": [0.825, -2.99, 0.913, -2.33]}, {"time": 1, "value": -1.67}], "translate": [{"curve": [0.125, 0, 0.375, -4.66, 0.125, 0, 0.375, 29.32]}, {"time": 0.5, "x": -4.66, "y": 29.32, "curve": [0.625, -4.66, 0.875, 0, 0.625, 29.32, 0.875, 0]}, {"time": 1}], "scale": [{"y": 1.015, "curve": [0.125, 1, 0.375, 1.037, 0.125, 1.015, 0.375, 0.92]}, {"time": 0.5, "x": 1.037, "y": 0.92, "curve": [0.625, 1.037, 0.875, 1, 0.625, 0.92, 0.875, 1.015]}, {"time": 1, "y": 1.015}]}, "GOO_1": {"rotate": [{"value": -15.24, "curve": [0.091, -6.4, 0.195, 7.51]}, {"time": 0.3, "value": 21.86, "curve": [0.425, 21.86, 0.675, -27.83]}, {"time": 0.8, "value": -27.83, "curve": [0.857, -27.83, 0.925, -23.03]}, {"time": 1, "value": -15.24}], "translate": [{"y": -4.63, "curve": [0.099, 0, 0.2, 0, 0.099, -7.22, 0.2, -9.95]}, {"time": 0.2667, "y": -9.95, "curve": [0.392, 0, 0.642, 0, 0.392, -9.95, 0.642, 0]}, {"time": 0.7667, "curve": [0.825, 0, 0.912, 0, 0.825, 0, 0.912, -2.26]}, {"time": 1, "y": -4.63}]}, "GOO_2": {"rotate": [{"value": 2.37, "curve": [0.088, 6.86, 0.174, 11.08]}, {"time": 0.2333, "value": 11.08, "curve": [0.358, 11.08, 0.608, -8.03]}, {"time": 0.7333, "value": -8.03, "curve": [0.799, -8.03, 0.901, -2.65]}, {"time": 1, "value": 2.37}], "translate": [{"x": 0.02, "y": -8.05, "curve": [0.037, 0.01, 0.071, 0, 0.037, -9.23, 0.071, -9.95]}, {"time": 0.1, "y": -9.95, "curve": [0.233, 0, 0.5, 0.19, 0.233, -9.95, 0.5, 8.65]}, {"time": 0.6333, "x": 0.19, "y": 8.65, "curve": [0.722, 0.19, 0.885, 0.06, 0.722, 8.65, 0.885, -3.74]}, {"time": 1, "x": 0.02, "y": -8.05}]}, "GOO_3": {"rotate": [{"value": 4.3, "curve": [0.088, -0.46, 0.174, -4.92]}, {"time": 0.2333, "value": -4.92, "curve": [0.358, -4.92, 0.608, 15.31]}, {"time": 0.7333, "value": 15.31, "curve": [0.799, 15.31, 0.901, 9.62]}, {"time": 1, "value": 4.3}], "translate": [{"y": -6.01, "curve": [0.076, 0, 0.149, 0, 0.076, -8.13, 0.149, -9.95]}, {"time": 0.2, "y": -9.95, "curve": [0.325, 0, 0.575, 0, 0.325, -9.95, 0.575, 0]}, {"time": 0.7, "curve": [0.774, 0, 0.89, 0, 0.774, 0, 0.89, -3.26]}, {"time": 1, "y": -6.01}]}, "NECKLACE": {"translate": [{"x": 3.65, "y": 2.07}]}, "ARM_LEFT_HANDLE": {"translate": [{"x": 5.01, "y": 0.59, "curve": [0.032, 3.12, 0.139, 0, 0.032, 0.37, 0.139, 0]}, {"time": 0.2333, "curve": [0.35, 0, 0.583, 8.82, 0.35, 0, 0.583, 1.05]}, {"time": 0.7, "x": 8.82, "y": 1.05, "curve": [0.781, 8.82, 0.885, 7.23, 0.781, 1.05, 0.885, 0.86]}, {"time": 1, "x": 5.01, "y": 0.59}]}, "ARM_RIGHT_HANDLE": {"translate": [{"x": 2.39, "y": 0.21, "curve": [0.064, 1, 0.122, 0, 0.064, 0.09, 0.122, 0]}, {"time": 0.1667, "curve": [0.292, 0, 0.542, 6.41, 0.292, 0, 0.542, -0.97]}, {"time": 0.6667, "x": 6.41, "y": -0.97, "curve": [0.748, 6.41, 0.885, 3.9, 0.748, -0.97, 0.885, -0.23]}, {"time": 1, "x": 2.39, "y": 0.21}]}, "CAPE_CORNER_RIGHT": {"rotate": [{"value": 30.04, "curve": [0.026, 30.04, 0.062, 25.01]}, {"time": 0.1, "value": 18.99, "curve": [0.16, 10.55, 0.226, 0]}, {"time": 0.2667, "curve": [0.325, 0, 0.442, 30.04]}, {"time": 0.5, "value": 30.04, "curve": [0.567, 30.04, 0.7, 0]}, {"time": 0.7667, "curve": [0.825, 0, 0.942, 30.04]}, {"time": 1, "value": 30.04}], "translate": [{"x": 6.97, "y": -2.09, "curve": [0.038, 3.22, 0.074, 0, 0.038, -0.96, 0.074, 0]}, {"time": 0.1, "curve": [0.167, 0, 0.3, 26.66, 0.167, 0, 0.3, -5.04]}, {"time": 0.3667, "x": 26.66, "y": -5.04, "curve": [0.425, 26.66, 0.542, 0, 0.425, -5.04, 0.542, 0]}, {"time": 0.6, "curve": [0.667, 0, 0.8, 17.87, 0.667, 0, 0.8, -5.36]}, {"time": 0.8667, "x": 17.87, "y": -5.36, "curve": [0.899, 17.87, 0.951, 11.93, 0.899, -5.36, 0.951, -3.58]}, {"time": 1, "x": 6.97, "y": -2.09}]}, "CAPE_CORNER_LEFT": {"rotate": [{"value": -8.13, "curve": [0.038, -4.06, 0.075, 0]}, {"time": 0.1, "curve": [0.167, 0, 0.3, -16.25]}, {"time": 0.3667, "value": -16.25, "curve": [0.433, -16.25, 0.567, 0]}, {"time": 0.6333, "curve": [0.7, 0, 0.833, -16.25]}, {"time": 0.9, "value": -16.25, "curve": [0.925, -16.25, 0.963, -12.19]}, {"time": 1, "value": -8.13}], "translate": [{"curve": [0.067, 0, 0.2, 20.76, 0.067, 0, 0.2, 12.29]}, {"time": 0.2667, "x": 20.76, "y": 12.29, "curve": [0.325, 20.76, 0.442, 0, 0.325, 12.29, 0.442, 0]}, {"time": 0.5, "curve": [0.567, 0, 0.7, 12.36, 0.567, 0, 0.7, 14.88]}, {"time": 0.7667, "x": 12.36, "y": 14.88, "curve": [0.825, 12.36, 0.942, 0, 0.825, 14.88, 0.942, 0]}, {"time": 1}]}, "HAND_RIGHT": {"rotate": [{"value": -7.2, "curve": [0.115, -3, 0.219, 0]}, {"time": 0.3, "curve": [0.425, 0, 0.675, -14.9]}, {"time": 0.8, "value": -14.9, "curve": [0.868, -12.19, 0.936, -9.48]}, {"time": 1, "value": -7.2}]}, "HAND_LEFT": {"rotate": [{"value": -7.34, "curve": [0.106, -4.32, 0.227, 0]}, {"time": 0.3, "curve": [0.433, 0, 0.7, -10.56]}, {"time": 0.8333, "value": -10.56, "curve": [0.877, -10.56, 0.936, -9.16]}, {"time": 1, "value": -7.34}]}, "SLEEVE_LEFT": {"rotate": [{"value": -27.05, "curve": [0.125, -27.05, 0.375, 0]}, {"time": 0.5, "curve": [0.625, 0, 0.875, -27.05]}, {"time": 1, "value": -27.05}], "translate": [{"x": 11.1, "curve": [0.037, 12.88, 0.071, 13.99, 0.037, 0, 0.071, 0]}, {"time": 0.1, "x": 13.99, "curve": [0.217, 13.99, 0.45, -3.23, 0.217, 0, 0.45, 0]}, {"time": 0.5667, "x": -3.23, "curve": [0.672, -3.23, 0.862, 7.15, 0.672, 0, 0.862, 0]}, {"time": 1, "x": 11.1}]}, "SLEEVE_RIGHT": {"rotate": [{"value": -21.61, "curve": [0.125, -21.61, 0.375, 0]}, {"time": 0.5, "curve": [0.625, 0, 0.875, -21.61]}, {"time": 1, "value": -21.61}], "translate": [{"x": 7.6, "curve": [0.119, 3.31, 0.278, -6.85, 0.119, 0, 0.278, 0]}, {"time": 0.3667, "x": -6.85, "curve": [0.5, -6.85, 0.767, 9.28, 0.5, 0, 0.767, 0]}, {"time": 0.9, "x": 9.28, "curve": [0.928, 9.28, 0.962, 8.63, 0.928, 0, 0.962, 0]}, {"time": 1, "x": 7.6}]}, "HORN_R": {"rotate": [{"value": 12.05, "curve": [0.099, 6.23, 0.201, 0]}, {"time": 0.2667, "curve": [0.392, 0, 0.642, 22.15]}, {"time": 0.7667, "value": 22.15, "curve": [0.826, 22.15, 0.912, 17.26]}, {"time": 1, "value": 12.05}]}, "HORN_L": {"rotate": [{"value": -13.44, "curve": [0.099, -6.95, 0.201, 0]}, {"time": 0.2667, "curve": [0.392, 0, 0.642, -24.71]}, {"time": 0.7667, "value": -24.71, "curve": [0.826, -24.71, 0.912, -19.26]}, {"time": 1, "value": -13.44}]}, "HEAD": {"rotate": [{"value": 1.32, "curve": [0.084, 1.94, 0.175, 2.77]}, {"time": 0.2667, "value": 3.61, "curve": [0.392, 3.61, 0.642, 0]}, {"time": 0.7667, "curve": [0.831, 0, 0.911, 0.53]}, {"time": 1, "value": 1.32}], "translate": [{"x": -5.85, "y": -0.18, "curve": [0.076, -8.48, 0.148, -10.67, 0.076, -0.28, 0.148, -0.35]}, {"time": 0.2, "x": -10.67, "y": -0.35, "curve": [0.325, -10.67, 0.513, -4.12, 0.325, -0.35, 0.513, -0.12]}, {"time": 0.7, "x": 2.43, "y": 0.11, "curve": [0.774, 2.43, 0.891, -2.17, 0.774, 0.11, 0.891, -0.06]}, {"time": 1, "x": -5.85, "y": -0.18}]}, "FACE": {"translate": [{"x": 3.05, "y": -0.15, "curve": [0.109, 0, 0.226, -3.81, 0.109, 0.38, 0.226, 1.03]}, {"time": 0.3, "x": -3.81, "y": 1.03, "curve": [0.425, -3.81, 0.675, 7.04, 0.425, 1.03, 0.675, -0.83]}, {"time": 0.8, "x": 7.04, "y": -0.83, "curve": [0.852, 7.04, 0.924, 5.23, 0.852, -0.83, 0.924, -0.52]}, {"time": 1, "x": 3.05, "y": -0.15}]}, "HEAD_TOP": {"rotate": [{"value": -4.72, "curve": [0.088, -6.77, 0.174, -8.68]}, {"time": 0.2333, "value": -8.68, "curve": [0.358, -8.68, 0.608, 0]}, {"time": 0.7333, "curve": [0.799, 0, 0.901, -2.44]}, {"time": 1, "value": -4.72}]}, "GOO": {"translate": [{"curve": [0.125, 0, 0.375, 5.23, 0.125, 0, 0.375, 1.01]}, {"time": 0.5, "x": 5.23, "y": 1.01, "curve": [0.625, 5.23, 0.875, 0, 0.625, 1.01, 0.875, 0]}, {"time": 1}]}, "NECKLACE_HANDLE": {"translate": [{"y": 0.34, "curve": [0.099, -0.05, 0.201, -0.11, 0.099, -4.94, 0.201, -10.59]}, {"time": 0.2667, "x": -0.11, "y": -10.59, "curve": [0.392, -0.11, 0.642, 0.1, 0.392, -10.59, 0.642, 9.5]}, {"time": 0.7667, "x": 0.1, "y": 9.5, "curve": [0.826, 0.1, 0.912, 0.05, 0.826, 9.5, 0.912, 5.06]}, {"time": 1, "y": 0.34}]}, "Necklace4": {"translate": [{"x": 0.28, "y": 1.69, "curve": [0.038, 0.11, 0.072, 0, 0.038, 0.65, 0.072, 0]}, {"time": 0.1, "curve": [0.225, 0, 0.475, 2.22, 0.225, 0, 0.475, 13.18]}, {"time": 0.6, "x": 2.22, "y": 13.18, "curve": [0.681, 2.22, 0.816, 1.26, 0.681, 13.18, 0.816, 7.51]}, {"time": 0.9333, "x": 0.63, "y": 3.74, "curve": [0.956, 0.5, 0.979, 0.38, 0.956, 2.96, 0.979, 2.25]}, {"time": 1, "x": 0.28, "y": 1.69}]}, "Necklace2": {"translate": [{"x": 1.89, "y": 11.25, "curve": [0.128, 1.27, 0.303, -0.29, 0.128, 7.58, 0.303, -1.6]}, {"time": 0.4, "x": -0.29, "y": -1.6, "curve": [0.525, -0.29, 0.775, 2.22, 0.525, -1.6, 0.775, 13.18]}, {"time": 0.9, "x": 2.22, "y": 13.18, "curve": [0.928, 2.22, 0.963, 2.09, 0.928, 13.18, 0.963, 12.44]}, {"time": 1, "x": 1.89, "y": 11.25}]}}}, "idle2": {"slots": {"GoatCult/Hand1": {"attachment": [{"time": 0.2, "name": "Hand2"}, {"time": 0.7, "name": "Hand1"}]}, "GoatCult/Hand2": {"attachment": [{"time": 0.2, "name": "Hand2"}, {"time": 0.7, "name": "Hand1"}]}}, "bones": {"HAND_LEFT": {"rotate": [{"value": -7.34, "curve": [0.106, -4.32, 0.227, 0]}, {"time": 0.3, "curve": [0.433, 0, 0.7, -10.56]}, {"time": 0.8333, "value": -10.56, "curve": [0.877, -10.56, 0.936, -9.16]}, {"time": 1, "value": -7.34}]}, "HAND_RIGHT": {"rotate": [{"value": -7.2, "curve": [0.115, -3, 0.219, 0]}, {"time": 0.3, "curve": [0.425, 0, 0.675, -14.9]}, {"time": 0.8, "value": -14.9, "curve": [0.868, -12.19, 0.936, -9.48]}, {"time": 1, "value": -7.2}]}, "BODY_BTM": {"rotate": [{"value": -1.67, "curve": [0.1, -1.01, 0.2, -0.35]}, {"time": 0.2667, "value": -0.35, "curve": [0.392, -0.35, 0.642, -2.99]}, {"time": 0.7667, "value": -2.99, "curve": [0.825, -2.99, 0.913, -2.33]}, {"time": 1, "value": -1.67}], "translate": [{"curve": [0.125, 0, 0.375, -4.66, 0.125, 0, 0.375, 29.32]}, {"time": 0.5, "x": -4.66, "y": 29.32, "curve": [0.625, -4.66, 0.875, 0, 0.625, 29.32, 0.875, 0]}, {"time": 1}], "scale": [{"curve": [0.125, 1, 0.375, 1.022, 0.125, 1, 0.375, 0.966]}, {"time": 0.5, "x": 1.022, "y": 0.966, "curve": [0.625, 1.022, 0.875, 1, 0.625, 0.966, 0.875, 1]}, {"time": 1}]}, "GOO_1": {"rotate": [{"value": 1.11, "curve": [0.099, 2.44, 0.2, 3.85]}, {"time": 0.2667, "value": 3.85, "curve": [0.392, 3.85, 0.642, -1.28]}, {"time": 0.7667, "value": -1.28, "curve": [0.825, -1.28, 0.912, -0.11]}, {"time": 1, "value": 1.11}], "translate": [{"y": -4.63, "curve": [0.099, 0, 0.2, 0, 0.099, -7.22, 0.2, -9.95]}, {"time": 0.2667, "y": -9.95, "curve": [0.392, 0, 0.642, 0, 0.392, -9.95, 0.642, 0]}, {"time": 0.7667, "curve": [0.825, 0, 0.912, 0, 0.825, 0, 0.912, -2.26]}, {"time": 1, "y": -4.63}]}, "GOO_2": {"rotate": [{"value": 3.32, "curve": [0.037, 3.65, 0.071, 3.85]}, {"time": 0.1, "value": 3.85, "curve": [0.233, 3.85, 0.5, -1.28]}, {"time": 0.6333, "value": -1.28, "curve": [0.722, -1.28, 0.885, 2.14]}, {"time": 1, "value": 3.32}], "translate": [{"x": 0.02, "y": -8.05, "curve": [0.037, 0.01, 0.071, 0, 0.037, -9.23, 0.071, -9.95]}, {"time": 0.1, "y": -9.95, "curve": [0.233, 0, 0.5, 0.19, 0.233, -9.95, 0.5, 8.65]}, {"time": 0.6333, "x": 0.19, "y": 8.65, "curve": [0.722, 0.19, 0.885, 0.06, 0.722, 8.65, 0.885, -3.74]}, {"time": 1, "x": 0.02, "y": -8.05}]}, "GOO_3": {"rotate": [{"value": 1.82, "curve": [0.076, 2.91, 0.149, 3.85]}, {"time": 0.2, "value": 3.85, "curve": [0.325, 3.85, 0.575, -1.28]}, {"time": 0.7, "value": -1.28, "curve": [0.774, -1.28, 0.89, 0.4]}, {"time": 1, "value": 1.82}], "translate": [{"y": -6.01, "curve": [0.076, 0, 0.149, 0, 0.076, -8.13, 0.149, -9.95]}, {"time": 0.2, "y": -9.95, "curve": [0.325, 0, 0.575, 0, 0.325, -9.95, 0.575, 0]}, {"time": 0.7, "curve": [0.774, 0, 0.89, 0, 0.774, 0, 0.89, -3.26]}, {"time": 1, "y": -6.01}]}, "NECKLACE": {"translate": [{"x": 3.65, "y": 2.07}]}, "ARM_LEFT_HANDLE": {"translate": [{"x": 5.01, "y": 0.59, "curve": [0.032, 3.12, 0.139, 0, 0.032, 0.37, 0.139, 0]}, {"time": 0.2333, "curve": [0.35, 0, 0.583, 8.82, 0.35, 0, 0.583, 1.05]}, {"time": 0.7, "x": 8.82, "y": 1.05, "curve": [0.781, 8.82, 0.885, 7.23, 0.781, 1.05, 0.885, 0.86]}, {"time": 1, "x": 5.01, "y": 0.59}]}, "ARM_RIGHT_HANDLE": {"translate": [{"x": 2.39, "y": 0.21, "curve": [0.064, 1, 0.122, 0, 0.064, 0.09, 0.122, 0]}, {"time": 0.1667, "curve": [0.292, 0, 0.542, 6.41, 0.292, 0, 0.542, -0.97]}, {"time": 0.6667, "x": 6.41, "y": -0.97, "curve": [0.748, 6.41, 0.885, 3.9, 0.748, -0.97, 0.885, -0.23]}, {"time": 1, "x": 2.39, "y": 0.21}]}, "CAPE_CORNER_RIGHT": {"rotate": [{"value": 30.04, "curve": [0.026, 30.04, 0.062, 25.01]}, {"time": 0.1, "value": 18.99, "curve": [0.16, 10.55, 0.226, 0]}, {"time": 0.2667, "curve": [0.325, 0, 0.442, 30.04]}, {"time": 0.5, "value": 30.04, "curve": [0.567, 30.04, 0.7, 0]}, {"time": 0.7667, "curve": [0.825, 0, 0.942, 30.04]}, {"time": 1, "value": 30.04}], "translate": [{"x": 6.97, "y": -2.09, "curve": [0.038, 3.22, 0.074, 0, 0.038, -0.96, 0.074, 0]}, {"time": 0.1, "curve": [0.167, 0, 0.3, 26.66, 0.167, 0, 0.3, -5.04]}, {"time": 0.3667, "x": 26.66, "y": -5.04, "curve": [0.425, 26.66, 0.542, 0, 0.425, -5.04, 0.542, 0]}, {"time": 0.6, "curve": [0.667, 0, 0.8, 17.87, 0.667, 0, 0.8, -5.36]}, {"time": 0.8667, "x": 17.87, "y": -5.36, "curve": [0.899, 17.87, 0.951, 11.93, 0.899, -5.36, 0.951, -3.58]}, {"time": 1, "x": 6.97, "y": -2.09}]}, "CAPE_CORNER_LEFT": {"rotate": [{"value": -8.13, "curve": [0.038, -4.06, 0.075, 0]}, {"time": 0.1, "curve": [0.167, 0, 0.3, -16.25]}, {"time": 0.3667, "value": -16.25, "curve": [0.433, -16.25, 0.567, 0]}, {"time": 0.6333, "curve": [0.7, 0, 0.833, -16.25]}, {"time": 0.9, "value": -16.25, "curve": [0.925, -16.25, 0.963, -12.19]}, {"time": 1, "value": -8.13}], "translate": [{"curve": [0.067, 0, 0.2, 20.76, 0.067, 0, 0.2, 12.29]}, {"time": 0.2667, "x": 20.76, "y": 12.29, "curve": [0.325, 20.76, 0.442, 0, 0.325, 12.29, 0.442, 0]}, {"time": 0.5, "curve": [0.567, 0, 0.7, 12.36, 0.567, 0, 0.7, 14.88]}, {"time": 0.7667, "x": 12.36, "y": 14.88, "curve": [0.825, 12.36, 0.942, 0, 0.825, 14.88, 0.942, 0]}, {"time": 1}]}, "SLEEVE_LEFT": {"translate": [{"x": 11.1, "curve": [0.037, 12.88, 0.071, 13.99, 0.037, 0, 0.071, 0]}, {"time": 0.1, "x": 13.99, "curve": [0.217, 13.99, 0.45, -3.23, 0.217, 0, 0.45, 0]}, {"time": 0.5667, "x": -3.23, "curve": [0.672, -3.23, 0.862, 7.15, 0.672, 0, 0.862, 0]}, {"time": 1, "x": 11.1}]}, "SLEEVE_RIGHT": {"translate": [{"x": 7.6, "curve": [0.119, 3.31, 0.278, -6.85, 0.119, 0, 0.278, 0]}, {"time": 0.3667, "x": -6.85, "curve": [0.5, -6.85, 0.767, 9.28, 0.5, 0, 0.767, 0]}, {"time": 0.9, "x": 9.28, "curve": [0.928, 9.28, 0.962, 8.63, 0.928, 0, 0.962, 0]}, {"time": 1, "x": 7.6}]}, "HEAD": {"rotate": [{"value": 1.32, "curve": [0.084, 1.94, 0.175, 2.77]}, {"time": 0.2667, "value": 3.61, "curve": [0.392, 3.61, 0.642, 0]}, {"time": 0.7667, "curve": [0.831, 0, 0.911, 0.53]}, {"time": 1, "value": 1.32}], "translate": [{"x": -10.67, "y": -0.35, "curve": [0.125, -10.67, 0.375, 2.43, 0.125, -0.35, 0.375, 0.11]}, {"time": 0.5, "x": 2.43, "y": 0.11, "curve": [0.625, 2.43, 0.875, -10.67, 0.625, 0.11, 0.875, -0.35]}, {"time": 1, "x": -10.67, "y": -0.35}]}, "FACE": {"translate": [{"x": 1.37, "y": -5.61, "curve": [0.109, -2.67, 0.226, -7.73, 0.109, -0.89, 0.226, 5.02]}, {"time": 0.3, "x": -7.73, "y": 5.02, "curve": [0.425, -7.73, 0.675, 6.67, 0.425, 5.02, 0.675, -11.8]}, {"time": 0.8, "x": 6.67, "y": -11.8, "curve": [0.852, 6.67, 0.924, 4.26, 0.852, -11.8, 0.924, -8.99]}, {"time": 1, "x": 1.37, "y": -5.61}]}, "NECKLACE_HANDLE": {"translate": [{"x": 0.01, "y": 1.41, "curve": [0.076, -0.02, 0.148, -0.05, 0.076, -1.84, 0.148, -4.56]}, {"time": 0.2, "x": -0.05, "y": -4.56, "curve": [0.325, -0.05, 0.575, 0.12, 0.325, -4.56, 0.575, 11.66]}, {"time": 0.7, "x": 0.12, "y": 11.66, "curve": [0.774, 0.12, 0.891, 0.06, 0.774, 11.66, 0.891, 5.96]}, {"time": 1, "x": 0.01, "y": 1.41}]}}}, "notice-player": {"slots": {"GoatCult/Mask": {"attachment": [{}, {"time": 0.2}]}}, "bones": {"HEAD": {"rotate": [{"value": 1.32, "curve": [0.052, 0.96, 0.11, 0.48]}, {"time": 0.1667, "curve": [0.175, 0, 0.192, 1.8]}, {"time": 0.2, "value": 1.8, "curve": [0.275, 0.9, 0.35, 0]}, {"time": 0.4, "curve": [0.464, 0, 0.545, 0.53]}, {"time": 0.6333, "value": 1.32}], "translate": [{"x": -10.67, "y": -0.35, "curve": [0.042, -10.67, 0.125, 12.4, 0.042, -0.35, 0.125, -0.36]}, {"time": 0.1667, "x": 12.4, "y": -0.36, "curve": [0.175, 12.4, 0.192, 2.43, 0.175, -0.36, 0.192, 0.11]}, {"time": 0.2, "x": 2.43, "y": 0.11, "curve": [0.308, 2.43, 0.525, -10.67, 0.308, 0.11, 0.525, -0.35]}, {"time": 0.6333, "x": -10.67, "y": -0.35}]}, "BODY_BTM": {"rotate": [{"value": -1.67, "curve": [0.063, -0.84, 0.125, 0]}, {"time": 0.1667, "curve": [0.233, 0, 0.367, -2.99]}, {"time": 0.4333, "value": -2.99, "curve": [0.492, -2.99, 0.579, -2.33]}, {"time": 0.6667, "value": -1.67}], "translate": [{"curve": [0.042, 0, 0.125, -1.08, 0.042, 0, 0.125, 73.29]}, {"time": 0.1667, "x": -1.08, "y": 73.29, "curve": [0.233, -1.08, 0.367, 1.76, 0.233, 73.29, 0.367, -15.31]}, {"time": 0.4333, "x": 1.76, "y": -15.31, "curve": [0.492, 1.76, 0.608, 0, 0.492, -15.31, 0.608, 0]}, {"time": 0.6667}], "scale": [{"curve": [0.042, 1, 0.125, 1.246, 0.042, 1, 0.125, 0.811]}, {"time": 0.1667, "x": 1.246, "y": 0.811, "curve": [0.233, 1.246, 0.367, 0.746, 0.233, 0.811, 0.367, 1.089]}, {"time": 0.4333, "x": 0.746, "y": 1.089, "curve": [0.492, 0.746, 0.608, 1, 0.492, 1.089, 0.608, 1]}, {"time": 0.6667}]}, "ARM_LEFT_HANDLE": {"translate": [{"x": 5.01, "y": 0.59, "curve": [0.025, 5.01, 0.075, 127.16, 0.025, 0.59, 0.075, -29.77]}, {"time": 0.1, "x": 127.16, "y": -29.77, "curve": [0.117, 127.16, 0.15, 67.15, 0.117, -29.77, 0.15, 1.49]}, {"time": 0.1667, "x": 67.15, "y": 1.49, "curve": [0.192, 67.15, 0.242, 112.49, 0.192, 1.49, 0.242, 12.77]}, {"time": 0.2667, "x": 112.49, "y": 12.77, "curve": [0.303, 112.49, 0.349, 85.82, 0.303, 12.77, 0.349, 9.43]}, {"time": 0.4, "x": 45.81, "y": 4.42, "curve": [0.442, 54.04, 0.488, 65.01, 0.442, 4.8, 0.488, 5.3]}, {"time": 0.5333, "x": 75.98, "y": 5.8, "curve": [0.621, 40.5, 0.708, 5.01, 0.621, 3.2, 0.708, 0.59]}, {"time": 0.7667, "x": 5.01, "y": 0.59}]}, "ARM_RIGHT_HANDLE": {"translate": [{"x": 2.39, "y": 0.21, "curve": [0.042, 2.39, 0.125, 100.33, 0.042, 0.21, 0.125, -8.63]}, {"time": 0.1667, "x": 100.33, "y": -8.63, "curve": [0.183, 100.33, 0.217, 59.47, 0.183, -8.63, 0.217, -9.87]}, {"time": 0.2333, "x": 59.47, "y": -9.87, "curve": [0.267, 59.47, 0.333, 80.9, 0.267, -9.87, 0.333, -3.29]}, {"time": 0.3667, "x": 80.9, "y": -3.29, "curve": [0.403, 80.9, 0.449, 58.95, 0.403, -3.29, 0.449, -5.4]}, {"time": 0.5, "x": 26.01, "y": -8.56, "curve": [0.531, 32.72, 0.566, 41.67, 0.531, -5.53, 0.566, -1.49]}, {"time": 0.6, "x": 50.61, "y": 2.55, "curve": [0.663, 26.5, 0.725, 2.39, 0.663, 1.38, 0.725, 0.21]}, {"time": 0.7667, "x": 2.39, "y": 0.21}]}, "FACE": {"rotate": [{"time": 0.2, "value": 1.43}], "translate": [{"x": 1.37, "y": -5.61, "curve": [0.048, -4.04, 0.101, -10.81, 0.048, -1.27, 0.101, 4.15]}, {"time": 0.1333, "x": -10.81, "y": 4.15, "curve": [0.157, -5.07, 0.181, -2.26, 0.157, 0.45, 0.181, -1.37]}, {"time": 0.2, "x": -2.26, "y": -1.37, "curve": [0.297, 8.86, 0.401, 22.44, 0.297, -4.13, 0.401, -7.5]}, {"time": 0.4667, "x": 22.44, "y": -7.5, "curve": [0.518, 22.44, 0.59, 12.87, 0.518, -7.5, 0.59, -6.65]}, {"time": 0.6667, "x": 1.37, "y": -5.61}]}, "BODY_TOP": {"rotate": [{"value": 5.9, "curve": [0.042, 5.9, 0.125, -5.68]}, {"time": 0.1667, "value": -5.68}]}, "effects": {"translate": [{"x": -322.82, "y": -42.44}]}, "spawn_particles": {"translate": [{"y": 5.14, "curve": [0.033, 0, 0.137, 0, 0.033, 16.25, 0.137, 21.57]}, {"time": 0.2, "y": 21.57}]}, "GOO_2": {"rotate": [{"value": 3.32, "curve": [0.074, 0.9, 0.142, -0.56]}, {"time": 0.2, "value": -0.56, "curve": [0.238, -1, 0.272, -1.28]}, {"time": 0.3, "value": -1.28, "curve": [0.389, -1.28, 0.552, 2.14]}, {"time": 0.6667, "value": 3.32}], "translate": [{"x": 0.02, "y": -8.05, "curve": [0.012, -3.47, 0.024, -5.59, 0.012, 5.49, 0.024, 13.68]}, {"time": 0.0333, "x": -5.59, "y": 13.68, "curve": [0.039, -1.97, 0.058, 0, 0.039, -0.62, 0.058, -8.39]}, {"time": 0.0667, "y": -8.39, "curve": [0.1, 0, 0.167, 0.17, 0.1, -8.39, 0.167, 6.06]}, {"time": 0.2, "x": 0.17, "y": 6.06, "curve": [0.238, 0.18, 0.272, 0.19, 0.238, 7.65, 0.272, 8.65]}, {"time": 0.3, "x": 0.19, "y": 8.65, "curve": [0.389, 0.19, 0.552, 0.06, 0.389, 8.65, 0.552, -3.74]}, {"time": 0.6667, "x": 0.02, "y": -8.05}]}, "SLEEVE_LEFT": {"translate": [{"x": 11.1, "curve": [0.075, 3.06, 0.143, -1.9, 0.075, 0, 0.143, 0]}, {"time": 0.2, "x": -1.9, "curve": [0.224, -2.74, 0.247, -3.23, 0.224, 0, 0.247, 0]}, {"time": 0.2667, "x": -3.23, "curve": [0.364, -3.23, 0.54, 7.15, 0.364, 0, 0.54, 0]}, {"time": 0.6667, "x": 11.1}]}, "GOO_3": {"rotate": [{"value": 1.82, "curve": [0.076, 1.14, 0.149, 0.55]}, {"time": 0.2, "value": 0.55, "curve": [0.276, -0.45, 0.348, -1.28]}, {"time": 0.4, "value": -1.28, "curve": [0.466, -1.28, 0.569, 0.4]}, {"time": 0.6667, "value": 1.82}], "translate": [{"y": -6.01, "curve": [0.013, 0, 0.025, 0, 0.013, -2.79, 0.025, 0]}, {"time": 0.0333, "curve": [0.039, 0, 0.058, 0, 0.039, 20.99, 0.058, 32.4]}, {"time": 0.0667, "y": 32.4, "curve": [0.1, 0, 0.167, 0, 0.1, 32.4, 0.167, -3.55]}, {"time": 0.2, "y": -3.55, "curve": [0.276, 0, 0.348, 0, 0.276, -1.6, 0.348, 0]}, {"time": 0.4, "curve": [0.466, 0, 0.569, 0, 0.466, 0, 0.569, -3.26]}, {"time": 0.6667, "y": -6.01}]}, "GOO_1": {"rotate": [{"value": 1.11, "curve": [0.074, 1.27, 0.15, 1.43]}, {"time": 0.2, "value": 1.43, "curve": [0.299, 0.11, 0.4, -1.28]}, {"time": 0.4667, "value": -1.28, "curve": [0.517, -1.28, 0.591, -0.11]}, {"time": 0.6667, "value": 1.11}], "translate": [{"y": -4.63, "curve": [0.012, 0, 0.025, 0, 0.012, -2.38, 0.025, 0]}, {"time": 0.0333, "curve": [0.039, 1.19, 0.058, 1.84, 0.039, 17, 0.058, 26.23]}, {"time": 0.0667, "x": 1.84, "y": 26.23, "curve": [0.1, 1.84, 0.167, 0, 0.1, 26.23, 0.167, -5.26]}, {"time": 0.2, "y": -5.26, "curve": [0.299, 0, 0.4, 0, 0.299, -2.69, 0.4, 0]}, {"time": 0.4667, "curve": [0.517, 0, 0.591, 0, 0.517, 0, 0.591, -2.26]}, {"time": 0.6667, "y": -4.63}]}, "SLEEVE_RIGHT": {"translate": [{"x": 7.6, "curve": [0.065, 4.2, 0.151, -3.88, 0.065, 0, 0.151, 0]}, {"time": 0.2, "x": -3.88, "curve": [0.322, 0.51, 0.478, 9.28, 0.322, 0, 0.478, 0]}, {"time": 0.5667, "x": 9.28, "curve": [0.595, 9.28, 0.629, 8.63, 0.595, 0, 0.629, 0]}, {"time": 0.6667, "x": 7.6}]}, "CAPE_CORNER_LEFT": {"rotate": [{"value": -8.13, "curve": [0.075, -7.64, 0.15, -7.15]}, {"time": 0.2, "value": -7.15, "curve": [0.238, -3.42, 0.275, 0]}, {"time": 0.3, "curve": [0.367, 0, 0.5, -16.25]}, {"time": 0.5667, "value": -16.25, "curve": [0.592, -16.25, 0.629, -12.19]}, {"time": 0.6667, "value": -8.13}], "translate": [{"time": 0.2, "curve": [0.258, 0, 0.375, 12.36, 0.258, 0, 0.375, 14.88]}, {"time": 0.4333, "x": 12.36, "y": 14.88, "curve": [0.492, 12.36, 0.608, 0, 0.492, 14.88, 0.608, 0]}, {"time": 0.6667}]}, "CAPE_CORNER_RIGHT": {"rotate": [{"value": 30.04, "curve": "stepped"}, {"time": 0.2, "value": 30.04, "curve": [0.258, 30.04, 0.375, 0]}, {"time": 0.4333, "curve": [0.492, 0, 0.608, 30.04]}, {"time": 0.6667, "value": 30.04}], "translate": [{"x": 6.97, "y": -2.09, "curve": [0.076, 6.08, 0.149, 5.31, 0.076, 14.66, 0.149, 29.02]}, {"time": 0.2, "x": 5.31, "y": 29.02, "curve": [0.315, 12.35, 0.422, 17.87, 0.315, 9.77, 0.422, -5.36]}, {"time": 0.5, "x": 17.87, "y": -5.36, "curve": [0.533, 17.87, 0.585, 11.93, 0.533, -5.36, 0.585, -3.58]}, {"time": 0.6333, "x": 6.97, "y": -2.09}]}, "NECKLACE_HANDLE": {"translate": [{"x": 9.61, "y": -0.56, "curve": [0.06, -11.33, 0.126, -37.5, 0.06, -1.89, 0.126, -3.56]}, {"time": 0.1667, "x": -37.5, "y": -3.56, "curve": [0.233, -37.5, 0.367, 15.19, 0.233, -3.56, 0.367, -0.88]}, {"time": 0.4333, "x": 15.19, "y": -0.88, "curve": [0.485, 15.19, 0.557, 12.65, 0.485, -0.88, 0.557, -0.73]}, {"time": 0.6333, "x": 9.61, "y": -0.56}]}, "NECKLACE": {"translate": [{"x": 3.65, "y": 2.07}]}, "HAND_RIGHT": {"rotate": [{"value": -7.2, "curve": [0.076, -6.3, 0.146, -5.66]}, {"time": 0.2, "value": -5.66, "curve": [0.285, -9.82, 0.376, -14.9]}, {"time": 0.4333, "value": -14.9, "curve": [0.501, -12.19, 0.569, -9.48]}, {"time": 0.6333, "value": -7.2}]}, "HAND_LEFT": {"rotate": [{"value": -7.34, "curve": [0.071, -5.87, 0.151, -3.76]}, {"time": 0.2, "value": -3.76, "curve": [0.296, -6.75, 0.401, -10.56]}, {"time": 0.4667, "value": -10.56, "curve": [0.51, -10.56, 0.57, -9.16]}, {"time": 0.6333, "value": -7.34}]}, "root": {"rotate": [{"value": -0.11}]}, "SHOULDER_LEFT": {"rotate": [{"time": 0.2, "value": 70.23}], "translate": [{"x": -15.65, "y": 1.57, "curve": [0.05, -15.65, 0.15, 0, 0.05, 1.57, 0.15, 0]}, {"time": 0.2}]}, "ARM_LEFT": {"rotate": [{"time": 0.2, "value": -74.74}]}, "NECK1": {"rotate": [{"time": 0.2, "value": 1.9}]}, "NECK2": {"rotate": [{"time": 0.2, "value": 8.62}]}, "NECK3": {"translate": [{"x": -9.13, "y": 10.18, "curve": [0.05, -9.13, 0.15, 0, 0.05, 10.18, 0.15, 0]}, {"time": 0.2}]}, "Necklace3": {"translate": [{"x": -8.56, "y": 17.56, "curve": [0.05, -8.56, 0.15, 0, 0.05, 17.56, 0.15, 0]}, {"time": 0.2}]}, "SHOULDER_RIGHT": {"rotate": [{"time": 0.1667, "value": -43.34}], "translate": [{"x": -72.78, "y": -22.96, "curve": [0.035, -22.84, 0.125, 0.85, 0.035, -3.16, 0.125, 6.24]}, {"time": 0.1667, "x": 0.85, "y": 6.24}]}, "ARM_RIGHT": {"rotate": [{"time": 0.2, "value": -79.49}]}, "BODY_HANDLE": {"translate": [{"x": -33.39, "y": 1.2, "curve": [0.05, -33.39, 0.15, 0, 0.05, 1.2, 0.15, 0]}, {"time": 0.2}]}, "spawn_particles2": {"translate": [{"y": -19.52, "curve": [0.11, 0, 0.456, 0, 0.11, 9.65, 0.456, 23.63]}, {"time": 0.6667, "y": 23.63}]}}, "ik": {"ARM_RIGHT_HANDLE": [{"bendPositive": false, "curve": "stepped"}, {"time": 0.0333, "bendPositive": false, "curve": "stepped"}, {"time": 0.0667}]}, "drawOrder": [{"offsets": [{"slot": "GoatCult/Arm2", "offset": 14}, {"slot": "GoatCult/Sleeve2", "offset": 11}, {"slot": "GoatCult/Hand2", "offset": 11}]}]}, "projectile": {"slots": {"Eyes": {"attachment": [{"time": 0.3333, "name": "Sorcerer/Cult/Eyes_Open"}, {"time": 0.7333, "name": "Sorcerer/Cult/Eyes"}]}, "fire": {"attachment": [{"time": 0.1, "name": "Other/Fire/Fire_0"}, {"time": 0.1333, "name": "Other/Fire/Fire_1"}, {"time": 0.1667, "name": "Other/Fire/Fire_3"}, {"time": 0.2, "name": "Other/Fire/Fire_4"}, {"time": 0.2333, "name": "Other/Fire/Fire_5"}, {"time": 0.2667, "name": "Other/Fire/Fire_6"}, {"time": 0.3, "name": "Other/Fire/Fire_0"}, {"time": 0.3667, "name": "Other/Fire/Fire_1"}, {"time": 0.4, "name": "Other/Fire/Fire_2"}, {"time": 0.4667, "name": "Other/Fire/Fire_3"}, {"time": 0.5333, "name": "Other/Fire/Fire_4"}, {"time": 0.6, "name": "Other/Fire/Fire_5"}, {"time": 0.6667, "name": "Other/Fire/Fire_6"}, {"time": 0.7333}]}, "GoatCult/Hand1": {"attachment": [{"time": 0.3333, "name": "Hand2"}, {"time": 0.4333, "name": "Hand3"}]}, "GoatCult/Hand2": {"attachment": [{"time": 0.7667, "name": "Hand3"}]}}, "bones": {"FACE": {"translate": [{"x": 3.05, "y": -0.15, "curve": [0.254, 13.82, 0.528, 27.28, 0.254, -0.81, 0.528, -1.63]}, {"time": 0.7, "x": 27.28, "y": -1.63, "curve": [0.733, 27.28, 0.8, -20.55, 0.733, -1.63, 0.8, 6.33]}, {"time": 0.8333, "x": -20.55, "y": 6.33, "curve": [0.901, -22.69, 1.045, -23.64, 0.901, 6.22, 1.045, 6.18]}, {"time": 1.1333, "x": -23.64, "y": 6.18, "curve": [1.217, -23.64, 1.383, 3.05, 1.217, 6.18, 1.383, -0.15]}, {"time": 1.4667, "x": 3.05, "y": -0.15}], "scale": [{}, {"time": 0.7, "x": 1.104, "curve": [0.733, 1.104, 0.8, 0.541, 0.733, 1, 0.8, 1]}, {"time": 0.8333, "x": 0.541, "curve": [0.901, 0.504, 1.058, 0.487, 0.901, 1, 1.058, 1]}, {"time": 1.1333, "x": 0.487}, {"time": 1.4667}]}, "HAND_LEFT": {"rotate": [{"value": -7.34, "curve": "stepped"}, {"time": 0.7333, "value": -7.34, "curve": [0.884, 1.16, 1.021, 6.6]}, {"time": 1.1333, "value": 6.6, "curve": [1.217, 6.6, 1.383, -7.34]}, {"time": 1.4667, "value": -7.34}]}, "HAND_RIGHT": {"rotate": [{"value": -7.2, "curve": "stepped"}, {"time": 0.8, "value": -7.2, "curve": [0.849, -1.79, 1.031, 1.04]}, {"time": 1.1333, "value": 1.04, "curve": [1.217, 1.04, 1.383, -7.2]}, {"time": 1.4667, "value": -7.2}]}, "fire": {"scale": [{"time": 0.1, "x": 0.099, "y": 0.099, "curve": [0.142, 0.099, 0.225, 0.696, 0.142, 0.099, 0.225, 0.696]}, {"time": 0.2667, "x": 0.696, "y": 0.696}]}, "BODY_BTM": {"rotate": [{"value": -1.67, "curve": [0.1, -3.88, 0.2, -6.09]}, {"time": 0.2667, "value": -6.09, "curve": [0.369, -8.31, 0.508, -10.25]}, {"time": 0.7, "value": -10.25, "curve": [0.725, -10.25, 0.775, -27.99]}, {"time": 0.8, "value": -27.99, "curve": "stepped"}, {"time": 1.1333, "value": -27.99, "curve": [1.217, -27.99, 1.383, -1.67]}, {"time": 1.4667, "value": -1.67}], "translate": [{"curve": [0.067, 0, 0.2, 30.85, 0.067, 0, 0.2, 17.55]}, {"time": 0.2667, "x": 30.85, "y": 17.55, "curve": "stepped"}, {"time": 0.7, "x": 30.85, "y": 17.55, "curve": [0.725, 30.85, 0.775, 0, 0.725, 17.55, 0.775, 0]}, {"time": 0.8, "curve": [0.816, 19.3, 1.05, 23.67, 0.816, 0, 1.05, 0]}, {"time": 1.1333, "x": 23.67, "curve": [1.217, 23.67, 1.383, 0, 1.217, 0, 1.383, 0]}, {"time": 1.4667}], "scale": [{"y": 1.015, "curve": [0.2, 1, 0.6, 1, 0.2, 1.015, 0.6, 1]}, {"time": 0.8, "curve": "stepped"}, {"time": 1.1333, "curve": [1.217, 1, 1.383, 1, 1.217, 1, 1.383, 1.015]}, {"time": 1.4667, "y": 1.015}]}, "NECKLACE_HANDLE": {"translate": [{"y": 0.34, "curve": [0.062, 2.62, 0.125, 5.42, 0.062, -13.05, 0.125, -27.39]}, {"time": 0.1667, "x": 5.42, "y": -27.39, "curve": [0.275, 5.42, 0.492, 0.1, 0.275, -27.39, 0.492, 9.5]}, {"time": 0.6, "x": 0.1, "y": 9.5, "curve": [0.625, 0.1, 0.662, 0.05, 0.625, 9.5, 0.662, 5.06]}, {"time": 0.7, "y": 0.34, "curve": [0.7, 10.47, 0.95, 16.59, 0.7, 37.49, 0.95, 59.19]}, {"time": 1.0333, "x": 16.59, "y": 59.19, "curve": [1.142, 16.59, 1.358, 0, 1.142, 59.19, 1.358, 0.34]}, {"time": 1.4667, "y": 0.34}]}, "NECKLACE": {"translate": [{"x": 3.65, "y": 2.07, "curve": "stepped"}, {"time": 0.7, "x": 3.65, "y": 2.07, "curve": [0.734, 5.45, 0.767, 7.18, 0.734, 9.89, 0.767, 17.43]}, {"time": 0.8, "x": 8.83, "y": 24.58, "curve": "stepped"}, {"time": 1.1333, "x": 8.83, "y": 24.58, "curve": [1.261, 5.8, 1.377, 3.65, 1.261, 11.42, 1.377, 2.07]}, {"time": 1.4667, "x": 3.65, "y": 2.07}], "scale": [{"time": 0.7, "curve": [0.729, 1, 0.763, 1, 0.729, 1, 0.763, 0.967]}, {"time": 0.8, "y": 0.912}]}, "ARM_LEFT_HANDLE": {"translate": [{"x": 5.01, "y": 0.59, "curve": [0.037, -0.9, 0.159, -10.66, 0.037, -14.25, 0.159, -38.8]}, {"time": 0.2667, "x": -10.66, "y": -38.8, "curve": "stepped"}, {"time": 0.7, "x": -10.66, "y": -38.8, "curve": [0.725, -10.66, 0.775, -10.3, 0.725, -38.8, 0.775, -205.38]}, {"time": 0.8, "x": -10.3, "y": -205.38, "curve": [0.879, -5.64, 1.05, -2.78, 0.879, -206.39, 1.05, -207.01]}, {"time": 1.1333, "x": -2.78, "y": -207.01, "curve": [1.183, -2.78, 1.258, -24.78, 1.183, -207.01, 1.258, -143.38]}, {"time": 1.3333, "x": -46.78, "y": -79.76, "curve": [1.383, -20.89, 1.433, 5.01, 1.383, -39.58, 1.433, 0.59]}, {"time": 1.4667, "x": 5.01, "y": 0.59}]}, "ARM_RIGHT_HANDLE": {"translate": [{"x": 2.39, "y": 0.21, "curve": [0.267, 30.56, 0.512, 50.68, 0.267, -30.88, 0.512, -53.08]}, {"time": 0.7, "x": 50.68, "y": -53.08, "curve": [0.725, 50.68, 0.775, 16.24, 0.725, -53.08, 0.775, 288.94]}, {"time": 0.8, "x": 16.24, "y": 288.94, "curve": [0.883, 16.24, 1.05, 14.42, 0.883, 288.94, 1.05, 245.44]}, {"time": 1.1333, "x": 14.42, "y": 245.44, "curve": [1.183, 14.42, 1.258, 11.41, 1.183, 245.44, 1.258, 184.13]}, {"time": 1.3333, "x": 8.41, "y": 122.82, "curve": "stepped"}, {"time": 1.3667, "x": -5.18, "y": 34.4, "curve": [1.405, -0.85, 1.44, 2.39, 1.405, 14.86, 1.44, 0.21]}, {"time": 1.4667, "x": 2.39, "y": 0.21}]}, "CAPE_CORNER_RIGHT": {"rotate": [{"value": 30.04, "curve": [0.017, 30.04, 0.041, 25.01]}, {"time": 0.0667, "value": 18.99, "curve": [0.103, 10.55, 0.142, 0]}, {"time": 0.1667, "curve": [0.208, 0, 0.292, 30.04]}, {"time": 0.3333, "value": 30.04, "curve": [0.417, 30.04, 0.583, 0]}, {"time": 0.6667, "curve": [0.677, 0, 0.688, 0.38]}, {"time": 0.7, "value": 1.06, "curve": [0.892, 1.06, 0.821, 30.04]}, {"time": 1.4667, "value": 30.04}], "translate": [{"x": 6.97, "y": -2.09, "curve": [0.025, 3.22, 0.05, 0, 0.025, -0.96, 0.05, 0]}, {"time": 0.0667, "curve": [0.1, 0, 0.167, 26.66, 0.1, 0, 0.167, -5.04]}, {"time": 0.2, "x": 26.66, "y": -5.04, "curve": [0.258, 26.66, 0.375, 0, 0.258, -5.04, 0.375, 0]}, {"time": 0.4333, "curve": [0.498, 0, 0.606, 7.68, 0.498, 0, 0.606, -2.3]}, {"time": 0.7, "x": 12.8, "y": -3.84, "curve": [0.75, 12.8, 0.732, 4.13, 0.75, -3.84, 0.732, 76.21]}, {"time": 0.9, "x": 4.13, "y": 76.21, "curve": [1.37, 4.17, 1.325, 6.97, 1.37, 75.21, 1.325, -2.09]}, {"time": 1.4667, "x": 6.97, "y": -2.09}]}, "CAPE_CORNER_LEFT": {"rotate": [{"value": -8.13, "curve": [0.025, -4.06, 0.05, 0]}, {"time": 0.0667, "curve": [0.1, 0, 0.167, -16.25]}, {"time": 0.2, "value": -16.25, "curve": [0.267, -16.25, 0.4, 0]}, {"time": 0.4667, "curve": [0.524, 0, 0.615, -5.41]}, {"time": 0.7, "value": -9.92, "curve": [0.742, -9.92, 0.726, -16.25]}, {"time": 0.8667, "value": -16.25, "curve": [0.892, -16.25, 0.929, -12.19]}, {"time": 0.9667, "value": -8.13}], "translate": [{"curve": [0.042, 0, 0.125, 20.76, 0.042, 0, 0.125, 12.29]}, {"time": 0.1667, "x": 20.76, "y": 12.29, "curve": [0.208, 20.76, 0.292, 0, 0.208, 12.29, 0.292, 0]}, {"time": 0.3333, "curve": [0.417, 0, 0.583, 12.36, 0.417, 0, 0.583, 14.88]}, {"time": 0.6667, "x": 12.36, "y": 14.88, "curve": [0.677, 12.36, 0.688, 12.21, 0.677, 14.88, 0.688, 14.69]}, {"time": 0.7, "x": 11.92, "y": 14.35, "curve": [0.75, 11.92, 0.732, 11.46, 0.75, 14.35, 0.732, 13.16]}, {"time": 0.9, "x": 11.46, "y": 13.16, "curve": [1.37, 11.31, 1.325, 0, 1.37, 12.99, 1.325, 0]}, {"time": 1.4667}]}, "SLEEVE_LEFT": {"rotate": [{"value": -27.05, "curve": [0.283, -27.05, 0.85, 0]}, {"time": 1.1333, "curve": [1.217, 0, 1.383, -27.05]}, {"time": 1.4667, "value": -27.05}], "translate": [{"x": 11.1, "curve": [0.062, 19.93, 0.119, 25.38, 0.062, -2.81, 0.119, -4.55]}, {"time": 0.1667, "x": 25.38, "y": -4.55, "curve": [0.366, 18.92, 0.556, 14.03, 0.366, -2.49, 0.556, -0.93]}, {"time": 0.7333, "x": 11.1, "curve": [0.756, 9.44, 0.778, 7.87, 0.756, -6.47, 0.778, -12.6]}, {"time": 0.8, "x": 6.39, "y": -18.4, "curve": "stepped"}, {"time": 1.1333, "x": 6.39, "y": -18.4, "curve": [1.25, 9.47, 1.361, 11.1, 1.25, -6.37, 1.361, 0]}, {"time": 1.4667, "x": 11.1}]}, "SLEEVE_RIGHT": {"rotate": [{"value": -21.61, "curve": [0.067, -21.61, 0.2, 0]}, {"time": 0.2667}, {"time": 0.6667, "value": 22.29}, {"time": 0.8667, "curve": [0.892, 0, 0.942, -29.62]}, {"time": 0.9667, "value": -29.62, "curve": [1.033, -29.62, 1.167, 19.69]}, {"time": 1.2333, "value": 19.69, "curve": [1.292, 19.69, 1.408, -21.61]}, {"time": 1.4667, "value": -21.61}], "translate": [{"x": 7.6, "curve": [0.086, 1.32, 0.202, -13.6, 0.086, -1.46, 0.202, -4.93]}, {"time": 0.2667, "x": -13.6, "y": -4.93, "curve": [0.403, -12.15, 0.538, -10.7, 0.403, -1.74, 0.538, 1.45]}, {"time": 0.6667, "x": -9.49, "y": 4.11, "curve": "stepped"}, {"time": 0.7333, "x": -9.49, "y": 4.11, "curve": [0.756, -13.8, 0.778, -17.97, 0.756, 1.43, 0.778, -1.16]}, {"time": 0.8, "x": -21.98, "y": -3.65, "curve": [0.825, -21.98, 0.875, 22.71, 0.825, -3.65, 0.875, -5.28]}, {"time": 0.9, "x": 22.71, "y": -5.28, "curve": [0.958, 22.71, 1.075, -2.98, 0.958, -5.28, 1.075, 2.36]}, {"time": 1.1333, "x": -2.98, "y": 2.36, "curve": [1.183, -2.98, 1.283, 22.71, 1.183, 2.36, 1.283, -5.28]}, {"time": 1.3333, "x": 22.71, "y": -5.28, "curve": [1.367, 22.71, 1.433, 7.6, 1.367, -5.28, 1.433, 0]}, {"time": 1.4667, "x": 7.6}]}, "HORN_R": {"rotate": [{"value": 12.05, "curve": [0.26, 6.23, 0.526, 0]}, {"time": 0.7, "curve": [0.725, 0, 0.775, -27.88]}, {"time": 0.8, "value": -27.88, "curve": [0.833, -27.88, 0.9, 25.88]}, {"time": 0.9333, "value": 25.88, "curve": [0.975, 25.88, 1.058, -30.54]}, {"time": 1.1, "value": -30.54, "curve": [1.192, -30.54, 1.375, 12.05]}, {"time": 1.4667, "value": 12.05}]}, "HORN_L": {"rotate": [{"value": -13.44, "curve": [0.26, -6.95, 0.526, 0]}, {"time": 0.7, "curve": [0.725, 0, 0.775, 26.24]}, {"time": 0.8, "value": 26.24, "curve": [0.833, 26.24, 0.9, -31.44]}, {"time": 0.9333, "value": -31.44, "curve": [0.975, -31.44, 1.058, 30.55]}, {"time": 1.1, "value": 30.55, "curve": [1.192, 30.55, 1.375, -13.44]}, {"time": 1.4667, "value": -13.44}]}, "HEAD": {"rotate": [{"value": 1.32, "curve": [0.084, -1.12, 0.175, -4.37]}, {"time": 0.2667, "value": -7.63, "curve": "stepped"}, {"time": 0.7, "value": -7.63, "curve": [0.742, -7.63, 0.825, 5.39]}, {"time": 0.8667, "value": 5.39, "curve": [0.933, 5.39, 1.067, 1.32]}, {"time": 1.1333, "value": 1.32}], "translate": [{"x": -5.85, "y": -0.18, "curve": [0.267, -8.48, 0.52, -10.67, 0.267, -0.28, 0.52, -0.35]}, {"time": 0.7, "x": -10.67, "y": -0.35, "curve": "stepped"}, {"time": 1.1333, "x": -10.67, "y": -0.35, "curve": [1.217, -10.67, 1.383, -5.85, 1.217, -0.35, 1.383, -0.18]}, {"time": 1.4667, "x": -5.85, "y": -0.18}]}, "BODY_TOP": {"rotate": [{}, {"time": 0.2667, "value": -10.65, "curve": "stepped"}, {"time": 0.7, "value": -10.65, "curve": [0.725, -10.65, 0.775, 31.09]}, {"time": 0.8, "value": 31.09, "curve": "stepped"}, {"time": 1.1333, "value": 31.09, "curve": [1.217, 31.09, 1.383, 0]}, {"time": 1.4667}], "scale": [{"time": 0.2667, "curve": [0.375, 1, 0.592, 1.093, 0.375, 1, 0.592, 0.93]}, {"time": 0.7, "x": 1.093, "y": 0.93, "curve": [0.725, 1.093, 0.775, 1, 0.725, 0.93, 0.775, 1]}, {"time": 0.8, "curve": [0.825, 1, 0.875, 0.822, 0.825, 1, 0.875, 1.12]}, {"time": 0.9, "x": 0.822, "y": 1.12, "curve": [0.958, 0.822, 1.075, 1, 0.958, 1.12, 1.075, 1]}, {"time": 1.1333}]}, "SHOULDER_RIGHT": {"rotate": [{"time": 1.1333, "value": -164.21}], "translate": [{"curve": [0.067, 0, 0.2, -11.35, 0.067, 0, 0.2, -4.49]}, {"time": 0.2667, "x": -11.35, "y": -4.49, "curve": "stepped"}, {"time": 0.7, "x": -11.35, "y": -4.49, "curve": [0.709, -11.35, 0.721, -17.87, 0.709, -4.49, 0.721, 0.23]}, {"time": 0.7333, "x": -26.58, "y": 6.53, "curve": "stepped"}, {"time": 0.7667, "x": -16.48, "y": 29.59, "curve": [0.787, -16.48, 0.809, -12.5, 0.787, 29.59, 0.809, 29.42]}, {"time": 0.8333, "x": -5.49, "y": 29.11, "curve": "stepped"}, {"time": 1.1333, "x": -5.49, "y": 29.11, "curve": [1.192, -10.89, 1.284, -33.86, 1.192, 24.98, 1.284, 7.4]}, {"time": 1.3333, "x": -33.86, "y": 7.4, "curve": "stepped"}, {"time": 1.3667, "x": -56.93, "y": 12.67, "curve": [1.405, -26.88, 1.441, 0, 1.405, 5.98, 1.441, 0]}, {"time": 1.4667}], "scale": [{"curve": [0.067, 1, 0.2, 1.248, 0.067, 1, 0.2, 1]}, {"time": 0.2667, "x": 1.248, "curve": "stepped"}, {"time": 0.7333, "x": 1.248, "curve": "stepped"}, {"time": 0.7667, "x": -1.248, "curve": "stepped"}, {"time": 1.3333, "x": -1.248, "curve": "stepped"}, {"time": 1.3667}]}, "SHOULDER_LEFT": {"rotate": [{"time": 0.7, "value": 110.58, "curve": [0.808, 110.58, 1.025, 161.1]}, {"time": 1.1333, "value": 161.1, "curve": [1.217, 161.1, 1.383, 110.58]}, {"time": 1.4667, "value": 110.58}], "translate": [{"time": 0.7, "curve": [0.725, 0, 0.775, -42.47, 0.725, 0, 0.775, -63.49]}, {"time": 0.8, "x": -42.47, "y": -63.49, "curve": "stepped"}, {"time": 1.1333, "x": -42.47, "y": -63.49, "curve": [1.217, -42.47, 1.383, 0, 1.217, -63.49, 1.383, 0]}, {"time": 1.4667}]}, "ARM_LEFT": {"rotate": [{"time": 1.1333, "value": -0.09}]}, "NECK1": {"rotate": [{"time": 1.1333, "value": 2.38}]}, "NECK2": {"rotate": [{"time": 1.1333, "value": 11.31}]}, "BODY_HANDLE": {"translate": [{"time": 0.7333, "curve": [0.753, 0, 0.776, 3.48, 0.753, 0, 0.776, 16.14]}, {"time": 0.8, "x": 9.68, "y": 44.83, "curve": "stepped"}, {"time": 1.1333, "x": 9.68, "y": 44.83, "curve": [1.231, 7.92, 1.385, 0, 1.231, 36.68, 1.385, 0]}, {"time": 1.4667}]}, "GOO_3": {"rotate": [{"value": 4.3, "curve": "stepped"}, {"time": 0.1, "value": 4.3, "curve": [0.125, -0.46, 0.15, -4.92]}, {"time": 0.1667, "value": -4.92, "curve": "stepped"}, {"time": 0.2667, "value": -4.92, "curve": [0.333, -4.92, 0.467, 15.31]}, {"time": 0.5333, "value": 15.31, "curve": "stepped"}, {"time": 0.6333, "value": 15.31, "curve": [0.65, 15.31, 0.675, 9.62]}, {"time": 0.7, "value": 4.3, "curve": "stepped"}, {"time": 0.8, "value": 4.3, "curve": [0.825, -0.46, 0.85, -4.92]}, {"time": 0.8667, "value": -4.92, "curve": [0.958, -4.92, 1.142, 15.31]}, {"time": 1.2333, "value": 15.31, "curve": [1.291, 15.31, 1.38, 9.62]}, {"time": 1.4667, "value": 4.3}], "translate": [{"y": -6.01, "curve": "stepped"}, {"time": 0.1, "y": -6.01, "curve": [0.113, 0, 0.125, 0, 0.113, -8.13, 0.125, -9.95]}, {"time": 0.1333, "y": -9.95, "curve": "stepped"}, {"time": 0.2333, "y": -9.95, "curve": [0.325, 0, 0.508, 0, 0.325, -9.95, 0.508, 0]}, {"time": 0.6, "curve": [0.625, 0, 0.663, 0, 0.625, 0, 0.663, -3.26]}, {"time": 0.7, "y": -6.01, "curve": "stepped"}, {"time": 0.8, "y": -6.01, "curve": [0.813, 0, 0.825, 0, 0.813, -8.13, 0.825, -9.95]}, {"time": 0.8333, "y": -9.95, "curve": [0.925, 0, 1.108, 0, 0.925, -9.95, 1.108, 0]}, {"time": 1.2, "curve": [1.266, 0, 1.369, 0, 1.266, 0, 1.369, -3.26]}, {"time": 1.4667, "y": -6.01}]}, "GOO_2": {"rotate": [{"value": 2.37, "curve": "stepped"}, {"time": 0.1, "value": 2.37, "curve": [0.125, 6.86, 0.15, 11.08]}, {"time": 0.1667, "value": 11.08, "curve": "stepped"}, {"time": 0.2667, "value": 11.08, "curve": [0.333, 11.08, 0.467, -8.03]}, {"time": 0.5333, "value": -8.03, "curve": "stepped"}, {"time": 0.6333, "value": -8.03, "curve": [0.65, -8.03, 0.675, -2.65]}, {"time": 0.7, "value": 2.37, "curve": "stepped"}, {"time": 0.8, "value": 2.37, "curve": [0.825, 6.86, 0.85, 11.08]}, {"time": 0.8667, "value": 11.08, "curve": [0.958, 11.08, 1.142, -8.03]}, {"time": 1.2333, "value": -8.03, "curve": [1.291, -8.03, 1.38, -2.65]}, {"time": 1.4667, "value": 2.37}], "translate": [{"x": 0.02, "y": -8.05, "curve": [0.025, 0.01, 0.047, 0, 0.025, -9.23, 0.047, -9.95]}, {"time": 0.0667, "y": -9.95, "curve": [0.075, 0, 0.092, 0.02, 0.075, -9.95, 0.092, -8.05]}, {"time": 0.1, "x": 0.02, "y": -8.05, "curve": [0.125, 0.01, 0.147, 0, 0.125, -9.23, 0.147, -9.95]}, {"time": 0.1667, "y": -9.95, "curve": [0.233, 0, 0.367, 0.19, 0.233, -9.95, 0.367, 8.65]}, {"time": 0.4333, "x": 0.19, "y": 8.65, "curve": "stepped"}, {"time": 0.5333, "x": 0.19, "y": 8.65, "curve": [0.574, 0.19, 0.648, 0.06, 0.574, 8.65, 0.648, -3.74]}, {"time": 0.7, "x": 0.02, "y": -8.05, "curve": [0.725, 0.01, 0.747, 0, 0.725, -9.23, 0.747, -9.95]}, {"time": 0.7667, "y": -9.95, "curve": [0.775, 0, 0.792, 0.02, 0.775, -9.95, 0.792, -8.05]}, {"time": 0.8, "x": 0.02, "y": -8.05, "curve": [0.912, 0.13, 1.013, 0.19, 0.912, 2.35, 1.013, 8.65]}, {"time": 1.1, "x": 0.19, "y": 8.65, "curve": [1.189, 0.19, 1.352, 0.06, 1.189, 8.65, 1.352, -3.74]}, {"time": 1.4667, "x": 0.02, "y": -8.05}]}, "GOO": {"rotate": [{"curve": [0.033, 0, 0.1, -12.4]}, {"time": 0.1333, "value": -12.4, "curve": [0.267, -12.4, 0.533, 0]}, {"time": 0.6667, "curve": [0.733, 0, 0.867, -12.4]}, {"time": 0.9333, "value": -12.4, "curve": [1.067, -12.4, 1.333, 0]}, {"time": 1.4667}], "translate": [{"curve": [0.042, 0, 0.125, 8.11, 0.042, 0, 0.125, 29.68]}, {"time": 0.1667, "x": 8.11, "y": 29.68, "curve": [0.3, 8.11, 0.567, 0, 0.3, 29.68, 0.567, -12.94]}, {"time": 0.7, "y": -12.94, "curve": [0.767, 0, 0.9, 5.93, 0.767, -12.94, 0.9, 25.32]}, {"time": 0.9667, "x": 5.93, "y": 25.32, "curve": [1.092, 5.93, 1.342, 0, 1.092, 25.32, 1.342, 0]}, {"time": 1.4667}]}, "GOO_1": {"rotate": [{"value": -15.24, "curve": "stepped"}, {"time": 0.1, "value": -15.24, "curve": [0.13, -6.4, 0.165, 7.51]}, {"time": 0.2, "value": 21.86, "curve": "stepped"}, {"time": 0.3, "value": 21.86, "curve": [0.367, 21.86, 0.5, -27.83]}, {"time": 0.5667, "value": -27.83, "curve": "stepped"}, {"time": 0.6667, "value": -27.83, "curve": [0.676, -27.83, 0.688, -23.03]}, {"time": 0.7, "value": -15.24, "curve": "stepped"}, {"time": 0.8, "value": -15.24, "curve": [0.83, -6.4, 0.865, 7.51]}, {"time": 0.9, "value": 21.86, "curve": [0.992, 21.86, 1.175, -27.83]}, {"time": 1.2667, "value": -27.83, "curve": [1.324, -27.83, 1.392, -23.03]}, {"time": 1.4667, "value": -15.24}], "translate": [{"y": -4.63, "curve": "stepped"}, {"time": 0.1, "y": -4.63, "curve": [0.137, 0, 0.175, 0, 0.137, -7.22, 0.175, -9.95]}, {"time": 0.2, "y": -9.95, "curve": "stepped"}, {"time": 0.3, "y": -9.95, "curve": [0.358, 0, 0.475, 0, 0.358, -9.95, 0.475, 0]}, {"time": 0.5333, "curve": "stepped"}, {"time": 0.6333, "curve": [0.65, 0, 0.675, 0, 0.65, 0, 0.675, -2.26]}, {"time": 0.7, "y": -4.63, "curve": "stepped"}, {"time": 0.8, "y": -4.63, "curve": [0.837, 0, 0.875, 0, 0.837, -7.22, 0.875, -9.95]}, {"time": 0.9, "y": -9.95, "curve": [0.983, 0, 1.15, 0, 0.983, -9.95, 1.15, 0]}, {"time": 1.2333, "curve": [1.292, 0, 1.379, 0, 1.292, 0, 1.379, -2.26]}, {"time": 1.4667, "y": -4.63}]}, "Necklace4": {"translate": [{"x": 0.28, "y": 1.69, "curve": [0.025, 0.11, 0.048, 0, 0.025, 0.65, 0.048, 0]}, {"time": 0.0667, "curve": [0.15, 0, 0.317, 0.35, 0.15, 0, 0.317, -24.56]}, {"time": 0.4, "x": 0.35, "y": -24.56, "curve": [0.458, 0.35, 0.575, 0.63, 0.458, -24.56, 0.575, 3.74]}, {"time": 0.6333, "x": 0.63, "y": 3.74, "curve": [0.656, 0.5, 0.679, 0.38, 0.656, 2.96, 0.679, 2.25]}, {"time": 0.7, "x": 0.28, "y": 1.69, "curve": [0.701, 5.35, 1, 8.31, 0.701, 31.62, 1, 49.1]}, {"time": 1.1, "x": 8.31, "y": 49.1, "curve": [1.192, 8.31, 1.375, 0.28, 1.192, 49.1, 1.375, 1.69]}, {"time": 1.4667, "x": 0.28, "y": 1.69}]}, "Necklace2": {"translate": [{"x": 1.89, "y": 11.25, "curve": [0.085, 3.17, 0.202, 6.38, 0.085, 1.49, 0.202, -22.9]}, {"time": 0.2667, "x": 6.38, "y": -22.9, "curve": [0.35, 6.38, 0.517, 2.22, 0.35, -22.9, 0.517, 13.18]}, {"time": 0.6, "x": 2.22, "y": 13.18, "curve": [0.628, 2.22, 0.663, 2.09, 0.628, 13.18, 0.663, 12.44]}, {"time": 0.7, "x": 1.89, "y": 11.25, "curve": [0.7, 7.11, 0.9, 10.16, 0.7, 33.81, 0.9, 46.98]}, {"time": 0.9667, "x": 10.16, "y": 46.98, "curve": [1.092, 10.16, 1.342, 1.89, 1.092, 46.98, 1.342, 11.25]}, {"time": 1.4667, "x": 1.89, "y": 11.25}]}, "HEAD_TOP": {"rotate": [{"value": -4.72, "curve": [0.264, -2.29, 0.523, 0]}, {"time": 0.7, "curve": [0.717, 0, 0.75, -25.55]}, {"time": 0.7667, "value": -25.55, "curve": [0.8, -25.55, 0.867, 17.29]}, {"time": 0.9, "value": 17.29, "curve": [0.95, 17.29, 1.05, -11.37]}, {"time": 1.1, "value": -11.37, "curve": [1.192, -11.37, 1.375, -4.72]}, {"time": 1.4667, "value": -4.72}]}}, "ik": {"ARM_RIGHT_HANDLE": [{"time": 1.1333, "bendPositive": false, "curve": "stepped"}, {"time": 1.3667}]}, "attachments": {"Cult": {"GoatCult/Arm2": {"Arm": {"deform": [{"time": 0.8}, {"time": 0.8333, "vertices": [-9.7972, -7.62669, -9.79709, -7.62679, -7.83483, -9.63136, -4.02974, -1.86722, -4.02967, -1.86722, -3.50768, -2.72415, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.87975, -0.49661]}]}}}, "Cult_Medic": {"GoatCult/Arm2": {"Arm": {"deform": [{"time": 0.8}, {"time": 0.8333, "vertices": [-9.7972, -7.62669, -9.79709, -7.62679, -7.83483, -9.63136, -4.02974, -1.86722, -4.02967, -1.86722, -3.50768, -2.72415, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.87975, -0.49661]}]}}}}, "drawOrder": [{"time": 0.7333, "offsets": [{"slot": "GoatCult/Arm2", "offset": 19}, {"slot": "GoatCult/Sleeve2", "offset": 16}, {"slot": "GoatCult/Hand2", "offset": 16}]}, {"time": 1.3667, "offsets": [{"slot": "GoatCult/Arm2", "offset": -7}, {"slot": "GoatCult/Sleeve2", "offset": -10}, {"slot": "GoatCult/Hand2", "offset": -10}]}], "events": [{"time": 0.7333, "name": "Fireball"}]}, "projectile-big": {"slots": {"Cape": {"rgba2": [{"time": 0.6667, "light": "ffffffff", "dark": "883c15", "curve": [0.833, 1, 1.167, 1, 0.833, 1, 1.167, 1, 0.833, 1, 1.167, 1, 0.833, 1, 1.167, 1, 0.833, 0.53, 1.167, 0, 0.833, 0.24, 1.167, 0, 0.833, 0.08, 1.167, 0]}, {"time": 1.3333, "light": "ffffffff", "dark": "000000"}]}, "fire": {"attachment": [{"time": 0.6667, "name": "Other/Fire/Fire_0"}, {"time": 0.7333, "name": "Other/Fire/Fire_1"}, {"time": 0.8, "name": "Other/Fire/Fire_2"}, {"time": 0.8667, "name": "Other/Fire/Fire_3"}, {"time": 0.9333, "name": "Other/Fire/Fire_4"}, {"time": 1, "name": "Other/Fire/Fire_5"}, {"time": 1.0667, "name": "Other/Fire/Fire_6"}, {"time": 1.1333, "name": "Other/Fire/Fire_0"}, {"time": 1.2, "name": "Other/Fire/Fire_1"}, {"time": 1.2667, "name": "Other/Fire/Fire_2"}, {"time": 1.3333, "name": "Other/Fire/Fire_3"}, {"time": 1.4, "name": "Other/Fire/Fire_4"}, {"time": 1.4667, "name": "Other/Fire/Fire_5"}, {"time": 1.5333, "name": "Other/Fire/Fire_0"}, {"time": 1.6, "name": "Other/Fire/Fire_1"}, {"time": 1.7, "name": "Other/Fire/Fire_2"}, {"time": 1.7667, "name": "Other/Fire/Fire_3"}, {"time": 1.8333, "name": "Other/Fire/Fire_4"}, {"time": 1.9, "name": "Other/Fire/Fire_5"}, {"time": 1.9667, "name": "Other/Fire/Fire_6"}, {"time": 2.0667, "name": "Other/Fire/Fire_0"}, {"time": 2.1}]}, "fire2": {"attachment": [{"time": 0.6667, "name": "Other/Fire/Fire_3"}, {"time": 0.7333, "name": "Other/Fire/Fire_4"}, {"time": 0.8, "name": "Other/Fire/Fire_5"}, {"time": 0.8667, "name": "Other/Fire/Fire_6"}, {"time": 0.9333, "name": "Other/Fire/Fire_0"}, {"time": 1, "name": "Other/Fire/Fire_1"}, {"time": 1.0667, "name": "Other/Fire/Fire_2"}, {"time": 1.1333, "name": "Other/Fire/Fire_3"}, {"time": 1.2, "name": "Other/Fire/Fire_4"}, {"time": 1.2667, "name": "Other/Fire/Fire_5"}, {"time": 1.3333, "name": "Other/Fire/Fire_0"}, {"time": 1.4, "name": "Other/Fire/Fire_1"}, {"time": 1.4667, "name": "Other/Fire/Fire_2"}, {"time": 1.5333, "name": "Other/Fire/Fire_3"}, {"time": 1.6, "name": "Other/Fire/Fire_4"}, {"time": 1.7, "name": "Other/Fire/Fire_5"}, {"time": 1.7667, "name": "Other/Fire/Fire_6"}, {"time": 1.8333, "name": "Other/Fire/Fire_0"}, {"time": 1.9, "name": "Other/Fire/Fire_1"}, {"time": 1.9667, "name": "Other/Fire/Fire_2"}, {"time": 2.0333, "name": "Other/Fire/Fire_6"}, {"time": 2.0667, "name": "Other/Fire/Fire_0"}, {"time": 2.1}]}, "GoatCult/Arm": {"rgba2": [{"time": 0.6667, "light": "ffffffff", "dark": "883c15", "curve": [0.833, 1, 1.167, 1, 0.833, 1, 1.167, 1, 0.833, 1, 1.167, 1, 0.833, 1, 1.167, 1, 0.833, 0.53, 1.167, 0, 0.833, 0.24, 1.167, 0, 0.833, 0.08, 1.167, 0]}, {"time": 1.3333, "light": "ffffffff", "dark": "000000"}]}, "GoatCult/Arm2": {"rgba2": [{"time": 0.6667, "light": "ffffffff", "dark": "883c15", "curve": [0.833, 1, 1.167, 1, 0.833, 1, 1.167, 1, 0.833, 1, 1.167, 1, 0.833, 1, 1.167, 1, 0.833, 0.53, 1.167, 0, 0.833, 0.24, 1.167, 0, 0.833, 0.08, 1.167, 0]}, {"time": 1.3333, "light": "ffffffff", "dark": "000000"}]}, "GoatCult/Goo1": {"rgba2": [{"time": 0.6667, "light": "ffffffff", "dark": "883c15", "curve": [0.833, 1, 1.167, 1, 0.833, 1, 1.167, 1, 0.833, 1, 1.167, 1, 0.833, 1, 1.167, 1, 0.833, 0.53, 1.167, 0, 0.833, 0.24, 1.167, 0, 0.833, 0.08, 1.167, 0]}, {"time": 1.3333, "light": "ffffffff", "dark": "000000"}]}, "GoatCult/Goo2": {"rgba2": [{"time": 0.6667, "light": "ffffffff", "dark": "883c15", "curve": [0.833, 1, 1.167, 1, 0.833, 1, 1.167, 1, 0.833, 1, 1.167, 1, 0.833, 1, 1.167, 1, 0.833, 0.53, 1.167, 0, 0.833, 0.24, 1.167, 0, 0.833, 0.08, 1.167, 0]}, {"time": 1.3333, "light": "ffffffff", "dark": "000000"}]}, "GoatCult/Goo3": {"rgba2": [{"time": 0.6667, "light": "ffffffff", "dark": "883c15", "curve": [0.833, 1, 1.167, 1, 0.833, 1, 1.167, 1, 0.833, 1, 1.167, 1, 0.833, 1, 1.167, 1, 0.833, 0.53, 1.167, 0, 0.833, 0.24, 1.167, 0, 0.833, 0.08, 1.167, 0]}, {"time": 1.3333, "light": "ffffffff", "dark": "000000"}]}, "GoatCult/Goo4": {"rgba2": [{"time": 0.6667, "light": "ffffffff", "dark": "883c15", "curve": [0.833, 1, 1.167, 1, 0.833, 1, 1.167, 1, 0.833, 1, 1.167, 1, 0.833, 1, 1.167, 1, 0.833, 0.53, 1.167, 0, 0.833, 0.24, 1.167, 0, 0.833, 0.08, 1.167, 0]}, {"time": 1.3333, "light": "ffffffff", "dark": "000000"}]}, "GoatCult/Goo5": {"rgba2": [{"time": 0.6667, "light": "ffffffff", "dark": "883c15", "curve": [0.833, 1, 1.167, 1, 0.833, 1, 1.167, 1, 0.833, 1, 1.167, 1, 0.833, 1, 1.167, 1, 0.833, 0.53, 1.167, 0, 0.833, 0.24, 1.167, 0, 0.833, 0.08, 1.167, 0]}, {"time": 1.3333, "light": "ffffffff", "dark": "000000"}]}, "GoatCult/Goo6": {"rgba2": [{"time": 0.6667, "light": "ffffffff", "dark": "883c15", "curve": [0.833, 1, 1.167, 1, 0.833, 1, 1.167, 1, 0.833, 1, 1.167, 1, 0.833, 1, 1.167, 1, 0.833, 0.53, 1.167, 0, 0.833, 0.24, 1.167, 0, 0.833, 0.08, 1.167, 0]}, {"time": 1.3333, "light": "ffffffff", "dark": "000000"}]}, "GoatCult/Goo7": {"rgba2": [{"time": 0.6667, "light": "ffffffff", "dark": "883c15", "curve": [0.833, 1, 1.167, 1, 0.833, 1, 1.167, 1, 0.833, 1, 1.167, 1, 0.833, 1, 1.167, 1, 0.833, 0.53, 1.167, 0, 0.833, 0.24, 1.167, 0, 0.833, 0.08, 1.167, 0]}, {"time": 1.3333, "light": "ffffffff", "dark": "000000"}]}, "GoatCult/Hand1": {"rgba2": [{"time": 0.6667, "light": "ffffffff", "dark": "883c15", "curve": [0.833, 1, 1.167, 1, 0.833, 1, 1.167, 1, 0.833, 1, 1.167, 1, 0.833, 1, 1.167, 1, 0.833, 0.53, 1.167, 0, 0.833, 0.24, 1.167, 0, 0.833, 0.08, 1.167, 0]}, {"time": 1.3333, "light": "ffffffff", "dark": "000000"}], "attachment": [{"time": 2.0667, "name": "Hand3"}]}, "GoatCult/Hand2": {"rgba2": [{"time": 0.6667, "light": "ffffffff", "dark": "883c15", "curve": [0.833, 1, 1.167, 1, 0.833, 1, 1.167, 1, 0.833, 1, 1.167, 1, 0.833, 1, 1.167, 1, 0.833, 0.53, 1.167, 0, 0.833, 0.24, 1.167, 0, 0.833, 0.08, 1.167, 0]}, {"time": 1.3333, "light": "ffffffff", "dark": "000000"}], "attachment": [{"time": 2.0667, "name": "Hand3"}]}, "GoatCult/Hood": {"rgba2": [{"time": 0.6667, "light": "ffffffff", "dark": "883c15", "curve": [0.833, 1, 1.167, 1, 0.833, 1, 1.167, 1, 0.833, 1, 1.167, 1, 0.833, 1, 1.167, 1, 0.833, 0.53, 1.167, 0, 0.833, 0.24, 1.167, 0, 0.833, 0.08, 1.167, 0]}, {"time": 1.3333, "light": "ffffffff", "dark": "000000"}]}, "GoatCult/HornLeft": {"rgba2": [{"time": 0.6667, "light": "ffffffff", "dark": "883c15", "curve": [0.735, 1, 0.832, 1, 0.735, 1, 0.832, 1, 0.735, 1, 0.832, 1, 0.735, 1, 0.832, 1, 0.735, 0.53, 0.832, 0.44, 0.735, 0.24, 0.832, 0.2, 0.735, 0.08, 0.832, 0.07]}, {"time": 0.9333, "light": "ffffffff", "dark": "56260d", "curve": [1.078, 1, 1.235, 1, 1.078, 1, 1.235, 1, 1.078, 1, 1.235, 1, 1.078, 1, 1.235, 1, 1.078, 0.19, 1.235, 0, 1.078, 0.08, 1.235, 0, 1.078, 0.03, 1.235, 0]}, {"time": 1.3333, "light": "ffffffff", "dark": "000000"}]}, "GoatCult/HornRight": {"rgba2": [{"time": 0.6667, "light": "ffffffff", "dark": "883c15", "curve": [0.833, 1, 1.167, 1, 0.833, 1, 1.167, 1, 0.833, 1, 1.167, 1, 0.833, 1, 1.167, 1, 0.833, 0.53, 1.167, 0, 0.833, 0.24, 1.167, 0, 0.833, 0.08, 1.167, 0]}, {"time": 1.3333, "light": "ffffffff", "dark": "000000"}]}, "GoatCult/Mask": {"rgba2": [{"time": 0.6667, "light": "ffffffff", "dark": "883b14", "curve": [0.833, 1, 1.167, 1, 0.833, 1, 1.167, 1, 0.833, 1, 1.167, 1, 0.833, 1, 1.167, 1, 0.833, 0.53, 1.167, 0, 0.833, 0.23, 1.167, 0, 0.833, 0.08, 1.167, 0]}, {"time": 1.3333, "light": "ffffffff", "dark": "000000"}], "attachment": [{"time": 0.6}, {"time": 1.5333}]}, "GoatCult/Necklace": {"rgba2": [{"time": 0.6667, "light": "ffffffff", "dark": "883c15", "curve": [0.833, 1, 1.167, 1, 0.833, 1, 1.167, 1, 0.833, 1, 1.167, 1, 0.833, 1, 1.167, 1, 0.833, 0.53, 1.167, 0, 0.833, 0.24, 1.167, 0, 0.833, 0.08, 1.167, 0]}, {"time": 1.3333, "light": "ffffffff", "dark": "000000"}]}, "GoatCult/Sleeve": {"rgba2": [{"time": 0.6667, "light": "ffffffff", "dark": "883c15", "curve": [0.833, 1, 1.167, 1, 0.833, 1, 1.167, 1, 0.833, 1, 1.167, 1, 0.833, 1, 1.167, 1, 0.833, 0.53, 1.167, 0, 0.833, 0.24, 1.167, 0, 0.833, 0.08, 1.167, 0]}, {"time": 1.3333, "light": "ffffffff", "dark": "000000"}]}, "GoatCult/Sleeve2": {"rgba2": [{"time": 0.6667, "light": "ffffffff", "dark": "883c15", "curve": [0.833, 1, 1.167, 1, 0.833, 1, 1.167, 1, 0.833, 1, 1.167, 1, 0.833, 1, 1.167, 1, 0.833, 0.53, 1.167, 0, 0.833, 0.24, 1.167, 0, 0.833, 0.08, 1.167, 0]}, {"time": 1.3333, "light": "ffffffff", "dark": "000000"}]}, "Other/SpawnParticles": {"rgba": [{"time": 0.6667, "color": "ffffff00", "curve": [0.678, 1, 0.712, 1, 0.678, 1, 0.712, 1, 0.678, 1, 0.712, 1, 0.678, 0.18, 0.712, 0.27]}, {"time": 0.7333, "color": "ffffff44", "curve": [0.883, 1, 1.183, 1, 0.883, 1, 1.183, 1, 0.883, 1, 1.183, 1, 0.883, 0.27, 1.183, 0]}, {"time": 1.3333, "color": "ffffff00"}], "attachment": [{"time": 0.6667, "name": "Other/SpawnParticles"}]}, "Other/SpawnParticles2": {"rgba": [{"time": 0.6667, "color": "ffffff00", "curve": [0.678, 1, 0.712, 1, 0.678, 1, 0.712, 1, 0.678, 1, 0.712, 1, 0.678, 0.18, 0.712, 0.27]}, {"time": 0.7333, "color": "ffffff44", "curve": [0.883, 1, 1.183, 1, 0.883, 1, 1.183, 1, 0.883, 1, 1.183, 1, 0.883, 0.27, 1.183, 0]}, {"time": 1.3333, "color": "ffffff00"}], "attachment": [{"time": 0.6667, "name": "Other/SpawnParticles2"}]}, "Other/SpawnParticles3": {"rgba": [{"time": 0.6667, "color": "ffffff00", "curve": [0.678, 1, 0.712, 1, 0.678, 1, 0.712, 1, 0.678, 1, 0.712, 1, 0.678, 0.18, 0.712, 0.27]}, {"time": 0.7333, "color": "ffffff44", "curve": [0.883, 1, 1.183, 1, 0.883, 1, 1.183, 1, 0.883, 1, 1.183, 1, 0.883, 0.27, 1.183, 0]}, {"time": 1.3333, "color": "ffffff00"}], "attachment": [{"time": 0.6667, "name": "Other/SpawnParticles2"}]}, "Other/whiteball": {"rgba": [{"time": 0.6667, "color": "ff9a0026", "curve": [0.708, 1, 0.792, 1, 0.708, 0.6, 0.792, 0.6, 0.708, 0, 0.792, 0, 0.708, 0.15, 0.792, 0]}, {"time": 0.8333, "color": "ff9a0000"}], "attachment": [{"time": 0.6667, "name": "Other/whiteball"}]}}, "bones": {"BODY_BTM": {"rotate": [{"value": -1.67, "curve": [0.188, -11.76, 0.375, -21.85]}, {"time": 0.5, "value": -21.85, "curve": [0.542, -21.85, 0.625, 1.38]}, {"time": 0.6667, "value": 1.38, "curve": "stepped"}, {"time": 2.0333, "value": 1.38, "curve": [2.042, 1.38, 2.058, -6.53]}, {"time": 2.0667, "value": -6.53, "curve": [2.225, -6.53, 2.542, -27.99]}, {"time": 2.7, "value": -27.99, "curve": [2.758, -27.99, 2.875, -1.67]}, {"time": 2.9333, "value": -1.67}], "translate": [{"curve": [0.075, 0, 0.225, 0, 0.075, 0, 0.225, 27.49]}, {"time": 0.3, "y": 27.49, "curve": [0.403, 0, 0.504, 0, 0.403, 17.38, 0.504, 7.9]}, {"time": 0.6, "curve": [0.803, 0, 1.3, 0, 0.803, 35.54, 1.3, 57.78]}, {"time": 1.5333, "y": 57.78, "curve": [1.658, 0, 1.908, 0, 1.658, 57.78, 1.908, 9.9]}, {"time": 2.0333, "y": 9.9, "curve": [2.042, 0, 2.058, 4.03, 2.042, 9.9, 2.058, 0]}, {"time": 2.0667, "x": 4.03, "curve": [2.175, 4.03, 2.392, 46.77, 2.175, 0, 2.392, 0]}, {"time": 2.5, "x": 46.77, "curve": [2.55, 46.77, 2.65, 23.67, 2.55, 0, 2.65, 0]}, {"time": 2.7, "x": 23.67, "curve": [2.758, 23.67, 2.875, 0, 2.758, 0, 2.875, 0]}, {"time": 2.9333}], "scale": [{"y": 1.015, "curve": [0.075, 1, 0.225, 1.091, 0.075, 1.015, 0.225, 0.892]}, {"time": 0.3, "x": 1.091, "y": 0.892, "curve": [0.35, 1.091, 0.45, 1, 0.35, 0.892, 0.45, 1]}, {"time": 0.5, "curve": [0.525, 1, 0.575, 0.652, 0.525, 1, 0.575, 1.342]}, {"time": 0.6, "x": 0.652, "y": 1.342, "curve": [0.625, 0.652, 0.675, 1.135, 0.625, 1.342, 0.675, 0.903]}, {"time": 0.7, "x": 1.135, "y": 0.903, "curve": [0.725, 1.135, 0.775, 1, 0.725, 0.903, 0.775, 1]}, {"time": 0.8, "curve": [0.817, 1, 0.85, 1.012, 0.817, 1, 0.85, 0.961]}, {"time": 0.8667, "x": 1.012, "y": 0.961, "curve": [0.883, 1.012, 0.917, 1, 0.883, 0.961, 0.917, 1]}, {"time": 0.9333, "curve": [0.95, 1, 0.983, 1.012, 0.95, 1, 0.983, 0.961]}, {"time": 1, "x": 1.012, "y": 0.961, "curve": [1.017, 1.012, 1.05, 1, 1.017, 0.961, 1.05, 1]}, {"time": 1.0667, "curve": [1.083, 1, 1.117, 1.012, 1.083, 1, 1.117, 0.961]}, {"time": 1.1333, "x": 1.012, "y": 0.961, "curve": [1.15, 1.012, 1.183, 1, 1.15, 0.961, 1.183, 1]}, {"time": 1.2, "curve": [1.217, 1, 1.25, 1.012, 1.217, 1, 1.25, 0.961]}, {"time": 1.2667, "x": 1.012, "y": 0.961, "curve": [1.283, 1.012, 1.317, 1, 1.283, 0.961, 1.317, 1]}, {"time": 1.3333, "curve": [1.35, 1, 1.383, 1.012, 1.35, 1, 1.383, 0.961]}, {"time": 1.4, "x": 1.012, "y": 0.961, "curve": [1.417, 1.012, 1.45, 1, 1.417, 0.961, 1.45, 1]}, {"time": 1.4667, "curve": "stepped"}, {"time": 1.5333, "curve": [1.658, 1, 1.908, 1.052, 1.658, 1, 1.908, 0.949]}, {"time": 2.0333, "x": 1.052, "y": 0.949, "curve": [2.042, 1.052, 2.058, 0.652, 2.042, 0.949, 2.058, 1.342]}, {"time": 2.0667, "x": 0.652, "y": 1.342, "curve": [2.092, 0.652, 2.142, 1.135, 2.092, 1.342, 2.142, 0.903]}, {"time": 2.1667, "x": 1.135, "y": 0.903, "curve": [2.217, 1.135, 2.317, 1, 2.217, 0.903, 2.317, 1]}, {"time": 2.3667, "curve": "stepped"}, {"time": 2.7, "curve": [2.758, 1, 2.875, 1, 2.758, 1, 2.875, 1.015]}, {"time": 2.9333, "y": 1.015}]}, "SHOULDER_LEFT": {"rotate": [{"time": 1.5333, "value": 77.66, "curve": "stepped"}, {"time": 2.0333, "value": 77.66, "curve": "stepped"}, {"time": 2.0667, "value": 161.1}], "translate": [{"time": 0.5, "curve": [0.542, 0, 0.625, -32.28, 0.542, 0, 0.625, -4.32]}, {"time": 0.6667, "x": -32.28, "y": -4.32, "curve": "stepped"}, {"time": 1.5333, "x": -32.28, "y": -4.32, "curve": [1.575, -32.28, 1.658, -18.24, 1.575, -4.32, 1.658, -26.17]}, {"time": 1.7, "x": -18.24, "y": -26.17, "curve": "stepped"}, {"time": 2.0333, "x": -18.24, "y": -26.17, "curve": "stepped"}, {"time": 2.0667, "x": -17.18, "y": -3.7}]}, "SHOULDER_RIGHT": {"rotate": [{"time": 1.5333, "value": -42.53, "curve": "stepped"}, {"time": 2.0333, "value": -42.53, "curve": "stepped"}, {"time": 2.0667, "value": -164.21}], "translate": [{"time": 0.5, "curve": [0.542, 0, 0.625, -25.46, 0.542, 0, 0.625, -1.5]}, {"time": 0.6667, "x": -25.46, "y": -1.5, "curve": "stepped"}, {"time": 1.5333, "x": -25.46, "y": -1.5, "curve": [1.575, -25.46, 1.658, -7.14, 1.575, -1.5, 1.658, 19.48]}, {"time": 1.7, "x": -7.14, "y": 19.48, "curve": "stepped"}, {"time": 2.0333, "x": -7.14, "y": 19.48, "curve": "stepped"}, {"time": 2.0667, "x": -28.21, "y": 50.62, "curve": "stepped"}, {"time": 2.7, "x": -28.21, "y": 50.62, "curve": [2.739, -28.21, 2.769, -30.89, 2.739, 50.62, 2.769, 30.07]}, {"time": 2.8, "x": -33.86, "y": 7.4, "curve": [2.811, -41.71, 2.821, -49.93, 2.811, 9.19, 2.821, 11.07]}, {"time": 2.8333, "x": -56.93, "y": 12.67, "curve": [2.871, -26.88, 2.908, 0, 2.871, 5.98, 2.908, 0]}, {"time": 2.9333}], "scale": [{"time": 2.0333, "curve": "stepped"}, {"time": 2.0667, "x": -1, "curve": "stepped"}, {"time": 2.8, "x": -1, "curve": "stepped"}, {"time": 2.8333}]}, "HAND_LEFT": {"rotate": [{"value": -7.34, "curve": [0.177, -7.29, 0.378, -7.21]}, {"time": 0.5, "value": -7.21, "curve": [0.542, -7.21, 0.625, -7.34]}, {"time": 0.6667, "value": -7.34, "curve": "stepped"}, {"time": 1.5333, "value": -7.34, "curve": [1.55, -7.34, 1.575, 31.01]}, {"time": 1.6, "value": 69.36, "curve": [1.623, 78.52, 1.647, 87.68]}, {"time": 1.6667, "value": 92.77, "curve": [1.679, 91.25, 1.69, 90.34]}, {"time": 1.7, "value": 90.34, "curve": "stepped"}, {"time": 2.0333, "value": 90.34, "curve": [2.043, 73.6, 2.059, 6.6]}, {"time": 2.0667, "value": 6.6, "curve": "stepped"}, {"time": 2.7, "value": 6.6, "curve": [2.758, 6.6, 2.875, -7.34]}, {"time": 2.9333, "value": -7.34}], "translate": [{"time": 1.5333, "curve": [1.575, 0, 1.658, 29.59, 1.575, 0, 1.658, 0.1]}, {"time": 1.7, "x": 29.59, "y": 0.1, "curve": "stepped"}, {"time": 2.0333, "x": 29.59, "y": 0.1, "curve": [2.043, 23.67, 2.059, 0, 2.043, 0.08, 2.059, 0]}, {"time": 2.0667}]}, "HAND_RIGHT": {"rotate": [{"value": -7.2, "curve": [0.191, -7.12, 0.366, -7.06]}, {"time": 0.5, "value": -7.06, "curve": [0.542, -7.06, 0.625, -7.2]}, {"time": 0.6667, "value": -7.2, "curve": "stepped"}, {"time": 1.5333, "value": -7.2, "curve": [1.55, -7.2, 1.575, 40.67]}, {"time": 1.6, "value": 88.53, "curve": [1.623, 90.91, 1.647, 93.29]}, {"time": 1.6667, "value": 94.61, "curve": [1.679, 73.63, 1.69, 61.03]}, {"time": 1.7, "value": 61.03, "curve": "stepped"}, {"time": 2.0333, "value": 61.03, "curve": "stepped"}, {"time": 2.0667, "value": 1.04, "curve": "stepped"}, {"time": 2.7, "value": 1.04, "curve": [2.758, 1.04, 2.875, -7.2]}, {"time": 2.9333, "value": -7.2}], "translate": [{"time": 1.5333, "curve": [1.575, 0, 1.658, 8.79, 1.575, 0, 1.658, 8.43]}, {"time": 1.7, "x": 8.79, "y": 8.43, "curve": "stepped"}, {"time": 2.0333, "x": 8.79, "y": 8.43, "curve": "stepped"}, {"time": 2.0667}]}, "HEAD": {"rotate": [{"value": 1.32, "curve": [0.157, 4.22, 0.329, 8.08]}, {"time": 0.5, "value": 11.95, "curve": [0.542, 11.95, 0.625, -3.88]}, {"time": 0.6667, "value": -3.88, "curve": "stepped"}, {"time": 1.5333, "value": -3.88, "curve": [1.658, -3.88, 1.908, 7.32]}, {"time": 2.0333, "value": 7.32, "curve": [2.055, -26.52, 2.158, -32.72]}, {"time": 2.2, "value": -32.72, "curve": [2.383, -32.72, 2.75, 1.32]}, {"time": 2.9333, "value": 1.32}], "translate": [{"x": -5.85, "y": -0.18, "curve": [0.191, -14.5, 0.371, -21.71, 0.191, -0.1, 0.371, -0.02]}, {"time": 0.5, "x": -21.71, "y": -0.02, "curve": [0.542, -21.71, 0.625, -10.67, 0.542, -0.02, 0.625, -0.35]}, {"time": 0.6667, "x": -10.67, "y": -0.35, "curve": "stepped"}, {"time": 2.0333, "x": -10.67, "y": -0.35, "curve": [2.055, 4.01, 2.158, 6.7, 2.055, 7.47, 2.158, 8.9]}, {"time": 2.2, "x": 6.7, "y": 8.9, "curve": [2.383, 6.7, 2.75, -5.85, 2.383, 8.9, 2.75, -0.18]}, {"time": 2.9333, "x": -5.85, "y": -0.18}]}, "HORN_L": {"rotate": [{"value": -13.44, "curve": [0.248, -6.95, 0.501, 0]}, {"time": 0.6667, "curve": "stepped"}, {"time": 2.7, "curve": [2.758, 0, 2.875, -13.44]}, {"time": 2.9333, "value": -13.44}]}, "HORN_R": {"rotate": [{"value": 12.05, "curve": [0.248, 6.23, 0.501, 0]}, {"time": 0.6667, "curve": "stepped"}, {"time": 2.7, "curve": [2.758, 0, 2.875, 12.05]}, {"time": 2.9333, "value": 12.05}]}, "BODY_TOP": {"rotate": [{}, {"time": 0.5, "value": 18.02, "curve": [0.542, 18.02, 0.625, -10.92]}, {"time": 0.6667, "value": -10.92, "curve": "stepped"}, {"time": 2.0333, "value": -10.92, "curve": [2.042, -10.92, 2.058, 31.09]}, {"time": 2.0667, "value": 31.09, "curve": "stepped"}, {"time": 2.7, "value": 31.09, "curve": [2.758, 31.09, 2.875, 0]}, {"time": 2.9333}]}, "SLEEVE_LEFT": {"rotate": [{"value": -27.05, "curve": [0.383, -27.05, 1.15, 0]}, {"time": 1.5333, "curve": "stepped"}, {"time": 2.7, "curve": [2.758, 0, 2.875, -27.05]}, {"time": 2.9333, "value": -27.05}], "translate": [{"x": 11.1, "curve": "stepped"}, {"time": 0.6667, "x": 11.1, "curve": [0.683, 11.1, 0.717, 32.71, 0.683, 0, 0.717, -5.13]}, {"time": 0.7333, "x": 32.71, "y": -5.13, "curve": [0.758, 32.71, 0.808, 21.65, 0.758, -5.13, 0.808, -3.79]}, {"time": 0.8333, "x": 21.65, "y": -3.79, "curve": [0.883, 21.65, 0.983, 32.71, 0.883, -3.79, 0.983, -5.13]}, {"time": 1.0333, "x": 32.71, "y": -5.13, "curve": [1.1, 32.71, 1.233, 11.1, 1.1, -5.13, 1.233, 0]}, {"time": 1.3, "x": 11.1, "curve": "stepped"}, {"time": 2.0333, "x": 11.1, "curve": [2.042, 11.1, 2.058, 17.89, 2.042, 0, 2.058, -3.45]}, {"time": 2.0667, "x": 17.89, "y": -3.45, "curve": [2.092, 17.89, 2.142, -16.88, 2.092, -3.45, 2.142, -7.08]}, {"time": 2.1667, "x": -16.88, "y": -7.08, "curve": [2.192, -16.88, 2.242, 12.22, 2.192, -7.08, 2.242, -10.38]}, {"time": 2.2667, "x": 12.22, "y": -10.38, "curve": [2.308, 12.22, 2.392, -9.44, 2.308, -10.38, 2.392, -10.43]}, {"time": 2.4333, "x": -9.44, "y": -10.43, "curve": [2.483, -9.44, 2.583, -1.13, 2.483, -10.43, 2.583, -8.36]}, {"time": 2.6333, "x": -1.13, "y": -8.36, "curve": [2.65, -1.13, 2.683, 6.39, 2.65, -8.36, 2.683, -18.4]}, {"time": 2.7, "x": 6.39, "y": -18.4, "curve": [2.782, 9.47, 2.86, 11.1, 2.782, -6.37, 2.86, 0]}, {"time": 2.9333, "x": 11.1}]}, "SLEEVE_RIGHT": {"rotate": [{"value": -21.61, "curve": [0.383, -21.61, 1.15, 0]}, {"time": 1.5333, "curve": "stepped"}, {"time": 2.7, "curve": [2.758, 0, 2.875, -21.61]}, {"time": 2.9333, "value": -21.61}], "translate": [{"x": 7.6, "curve": "stepped"}, {"time": 0.6667, "x": 7.6, "curve": [0.683, 7.6, 0.717, 23.94, 0.683, 0, 0.717, -5.45]}, {"time": 0.7333, "x": 23.94, "y": -5.45, "curve": [0.758, 23.94, 0.808, 6.59, 0.758, -5.45, 0.808, 0.53]}, {"time": 0.8333, "x": 6.59, "y": 0.53, "curve": [0.883, 6.59, 0.983, 23.94, 0.883, 0.53, 0.983, -5.45]}, {"time": 1.0333, "x": 23.94, "y": -5.45, "curve": [1.1, 23.94, 1.233, 7.6, 1.1, -5.45, 1.233, 0]}, {"time": 1.3, "x": 7.6, "curve": "stepped"}, {"time": 2.0333, "x": 7.6, "curve": [2.042, 7.6, 2.058, 22.49, 2.042, 0, 2.058, -3.91]}, {"time": 2.0667, "x": 22.49, "y": -3.91, "curve": [2.092, 22.49, 2.142, -15.89, 2.092, -3.91, 2.142, -1.65]}, {"time": 2.1667, "x": -15.89, "y": -1.65, "curve": [2.192, -15.89, 2.242, 22.71, 2.192, -1.65, 2.242, -5.28]}, {"time": 2.2667, "x": 22.71, "y": -5.28, "curve": [2.308, 22.71, 2.392, -2.98, 2.308, -5.28, 2.392, 2.36]}, {"time": 2.4333, "x": -2.98, "y": 2.36, "curve": [2.483, -2.98, 2.583, 7.6, 2.483, 2.36, 2.583, 0]}, {"time": 2.6333, "x": 7.6}]}, "spawn_particles": {"translate": [{"time": 0.6667, "y": 5.14, "curve": [0.777, 0, 1.123, 0, 0.777, 24.5, 1.123, 33.77]}, {"time": 1.3333, "y": 33.77, "curve": [1.517, 0, 1.883, 0, 1.517, 33.77, 1.883, 0]}, {"time": 2.0667}]}, "spawn_particles2": {"translate": [{"time": 0.6667, "y": -19.52, "curve": [0.777, 0, 1.123, 0, 0.777, 17.9, 1.123, 35.83]}, {"time": 1.3333, "y": 35.83}]}, "white": {"translate": [{"time": 0.6667, "x": 235.85, "y": 31.18}], "scale": [{"time": 0.6667, "x": 1.894, "y": 1.894, "curve": [0.697, 2.614, 0.792, 2.844, 0.697, 2.614, 0.792, 2.844]}, {"time": 0.8333, "x": 2.844, "y": 2.844}]}, "fire": {"rotate": [{"time": 1.5333, "curve": [1.658, 0, 1.908, 40.57]}, {"time": 2.0333, "value": 40.57, "curve": "stepped"}, {"time": 2.0667, "value": -47.19, "curve": [2.225, -47.19, 2.542, 0]}, {"time": 2.7}], "scale": [{"time": 0.6667, "x": 0.144, "y": 0.144, "curve": [0.71, 0.851, 0.817, 1.136, 0.71, 0.851, 0.817, 1.136]}, {"time": 0.8667, "x": 1.136, "y": 1.136, "curve": [0.925, 1.136, 1.042, 1, 0.925, 1.136, 1.042, 1]}, {"time": 1.1, "curve": "stepped"}, {"time": 1.5333, "curve": [1.658, 1, 1.908, 1.529, 1.658, 1, 1.908, 1.529]}, {"time": 2.0333, "x": 1.529, "y": 1.529, "curve": [2.042, 1.529, 2.058, 0.696, 2.042, 1.529, 2.058, 1.563]}, {"time": 2.0667, "x": 0.696, "y": 1.563, "curve": [2.077, 0.696, 2.088, 0.641, 2.077, 1.563, 2.088, 1.298]}, {"time": 2.1, "x": 0.537, "y": 0.795, "curve": [2.263, 0.552, 2.552, 0.696, 2.263, 0.786, 2.552, 0.696]}, {"time": 2.7, "x": 0.696, "y": 0.696}]}, "fire2": {"rotate": [{"time": 1.5333, "curve": [1.658, 0, 1.908, -15.73]}, {"time": 2.0333, "value": -15.73, "curve": [2.042, -15.73, 2.058, -41.07]}, {"time": 2.0667, "value": -41.07}], "scale": [{"time": 0.6667, "x": 0.181, "y": 0.181, "curve": [0.71, 0.966, 0.817, 1.282, 0.71, 0.966, 0.817, 1.282]}, {"time": 0.8667, "x": 1.282, "y": 1.282, "curve": [0.925, 1.282, 1.042, 1, 0.925, 1.282, 1.042, 1]}, {"time": 1.1, "curve": "stepped"}, {"time": 1.5333, "curve": [1.658, 1, 1.908, 1.618, 1.658, 1, 1.908, 1.618]}, {"time": 2.0333, "x": 1.618, "y": 1.618, "curve": [2.042, 1.618, 2.058, 0.902, 2.042, 1.618, 2.058, 1.618]}, {"time": 2.0667, "x": 0.902, "y": 1.618, "curve": [2.075, 0.902, 2.092, 0.701, 2.075, 1.618, 2.092, 0.759]}, {"time": 2.1, "x": 0.701, "y": 0.759}]}, "GOO_1": {"rotate": [{"value": -15.24, "curve": [0.152, -11.34, 0.325, -5.21]}, {"time": 0.5, "value": 1.11, "curve": "stepped"}, {"time": 2.0333, "value": 1.11, "curve": [2.042, 1.11, 2.058, -1.28]}, {"time": 2.0667, "value": -1.28, "curve": [2.249, -11.23, 2.717, -15.24]}, {"time": 2.9333, "value": -15.24}], "translate": [{"y": -4.63, "curve": [0.199, 0, 0.401, 0, 0.199, -2.38, 0.401, 0]}, {"time": 0.5333, "curve": [0.55, 1.19, 0.608, 1.84, 0.55, 17, 0.608, 26.23]}, {"time": 0.6333, "x": 1.84, "y": 26.23, "curve": [0.683, 1.84, 0.783, -1.38, 0.683, 26.23, 0.783, -57.83]}, {"time": 0.8333, "x": -1.38, "y": -57.83, "curve": [0.908, -1.38, 1.058, 2.1, 0.908, -57.83, 1.058, 18.57]}, {"time": 1.1333, "x": 2.1, "y": 18.57, "curve": [1.233, 2.1, 1.433, 0, 1.233, 18.57, 1.433, 0]}, {"time": 1.5333, "curve": "stepped"}, {"time": 2.0667, "curve": [2.165, 2.97, 2.417, 4.16, 2.165, 22.12, 2.417, 31.01]}, {"time": 2.5333, "x": 4.16, "y": 31.01, "curve": [2.633, 4.16, 2.833, 0, 2.633, 31.01, 2.833, -4.63]}, {"time": 2.9333, "y": -4.63}]}, "GOO_2": {"rotate": [{"value": 2.37, "curve": [0.189, 2.86, 0.374, 3.32]}, {"time": 0.5, "value": 3.32, "curve": "stepped"}, {"time": 2.0333, "value": 3.32, "curve": [2.042, 3.32, 2.058, 0.24]}, {"time": 2.0667, "value": 0.24, "curve": [2.249, 1.76, 2.717, 2.37]}, {"time": 2.9333, "value": 2.37}], "translate": [{"x": 0.02, "y": -8.05, "curve": [0.198, -3.47, 0.379, -5.59, 0.198, 5.49, 0.379, 13.68]}, {"time": 0.5333, "x": -5.59, "y": 13.68, "curve": [0.55, -1.97, 0.608, 0, 0.55, -0.62, 0.608, -8.39]}, {"time": 0.6333, "y": -8.39, "curve": [0.683, 0, 0.783, 3.17, 0.683, -8.39, 0.783, 44.73]}, {"time": 0.8333, "x": 3.17, "y": 44.73, "curve": [0.908, 3.17, 1.058, -3.59, 0.908, 44.73, 1.058, 3.43]}, {"time": 1.1333, "x": -3.59, "y": 3.43, "curve": [1.233, -3.59, 1.433, -5.59, 1.233, 3.43, 1.433, 13.68]}, {"time": 1.5333, "x": -5.59, "y": 13.68, "curve": "stepped"}, {"time": 2.0333, "x": -5.59, "y": 13.68, "curve": [2.043, -5.59, 2.054, -3.37, 2.043, 13.68, 2.054, 9.59]}, {"time": 2.0667, "x": 0.14, "y": 3.13, "curve": [2.186, 5.29, 2.492, 7.36, 2.186, 47.76, 2.492, 65.71]}, {"time": 2.6333, "x": 7.36, "y": 65.71, "curve": [2.708, 7.36, 2.858, 0.02, 2.708, 65.71, 2.858, -8.05]}, {"time": 2.9333, "x": 0.02, "y": -8.05}]}, "GOO_3": {"rotate": [{"value": 4.3, "curve": [0.189, 3.02, 0.374, 1.82]}, {"time": 0.5, "value": 1.82, "curve": "stepped"}, {"time": 2.0333, "value": 1.82, "curve": [2.258, 1.82, 2.708, 4.3]}, {"time": 2.9333, "value": 4.3}], "translate": [{"y": -6.01, "curve": [0.203, 0, 0.397, 0, 0.203, -2.79, 0.397, 0]}, {"time": 0.5333, "curve": [0.55, 0, 0.608, 0, 0.55, 20.99, 0.608, 32.4]}, {"time": 0.6333, "y": 32.4, "curve": [0.683, 0, 0.783, -1.08, 0.683, 32.4, 0.783, -40.51]}, {"time": 0.8333, "x": -1.08, "y": -40.51, "curve": [0.908, -1.08, 1.058, 2.55, 0.908, -40.51, 1.058, 15.26]}, {"time": 1.1333, "x": 2.55, "y": 15.26, "curve": [1.233, 2.55, 1.433, 0, 1.233, 15.26, 1.433, 0]}, {"time": 1.5333, "curve": "stepped"}, {"time": 2.0333, "curve": [2.287, 0, 2.595, 0, 2.287, 0, 2.595, -2.33]}, {"time": 2.9333, "y": -6.01}]}, "NECKLACE_HANDLE": {"translate": [{"y": 0.34, "curve": [0.248, 4.64, 0.501, 9.61, 0.248, -0.09, 0.501, -0.56]}, {"time": 0.6667, "x": 9.61, "y": -0.56, "curve": [0.683, 9.61, 0.717, 22.64, 0.683, -0.56, 0.717, -2]}, {"time": 0.7333, "x": 22.64, "y": -2, "curve": [0.75, 22.64, 0.783, 9.61, 0.75, -2, 0.783, -0.56]}, {"time": 0.8, "x": 9.61, "y": -0.56, "curve": [0.817, 9.61, 0.85, 22.64, 0.817, -0.56, 0.85, -2]}, {"time": 0.8667, "x": 22.64, "y": -2, "curve": [0.883, 22.64, 0.917, 9.61, 0.883, -2, 0.917, -0.56]}, {"time": 0.9333, "x": 9.61, "y": -0.56, "curve": [0.95, 9.61, 0.983, 22.64, 0.95, -0.56, 0.983, -2]}, {"time": 1, "x": 22.64, "y": -2, "curve": [1.033, 22.64, 1.1, 9.61, 1.033, -2, 1.1, -0.56]}, {"time": 1.1333, "x": 9.61, "y": -0.56, "curve": [1.167, 9.61, 1.233, 17.39, 1.167, -0.56, 1.233, -2.35]}, {"time": 1.2667, "x": 17.39, "y": -2.35, "curve": [1.308, 17.39, 1.392, 9.61, 1.308, -2.35, 1.392, -0.56]}, {"time": 1.4333, "x": 9.61, "y": -0.56, "curve": "stepped"}, {"time": 2.0333, "x": 9.61, "y": -0.56, "curve": [2.042, 9.61, 2.058, -17.57, 2.042, -0.56, 2.058, -5.62]}, {"time": 2.0667, "x": -17.57, "y": -5.62, "curve": [2.225, -17.57, 2.542, 27.77, 2.225, -5.62, 2.542, 54.19]}, {"time": 2.7, "x": 27.77, "y": 54.19, "curve": [2.758, 27.77, 2.875, 0, 2.758, 54.19, 2.875, 0.34]}, {"time": 2.9333, "y": 0.34}]}, "NECKLACE": {"translate": [{"x": 3.65, "y": 2.07, "curve": "stepped"}, {"time": 2.0333, "x": 3.65, "y": 2.07, "curve": [2.042, 3.65, 2.058, 8.83, 2.042, 2.07, 2.058, 24.58]}, {"time": 2.0667, "x": 8.83, "y": 24.58, "curve": "stepped"}, {"time": 2.7, "x": 8.83, "y": 24.58, "curve": [2.789, 5.8, 2.87, 3.65, 2.789, 11.42, 2.87, 2.07]}, {"time": 2.9333, "x": 3.65, "y": 2.07}], "scale": [{"time": 2.0333, "curve": [2.042, 1, 2.058, 1, 2.042, 1, 2.058, 0.912]}, {"time": 2.0667, "y": 0.912, "curve": "stepped"}, {"time": 2.7, "y": 0.912, "curve": [2.772, 1, 2.877, 1, 2.772, 0.933, 2.877, 1]}, {"time": 2.9333}]}, "ARM_LEFT_HANDLE": {"translate": [{"x": 5.01, "y": 0.59, "curve": [0.069, 50.55, 0.299, 125.9, 0.069, -5.75, 0.299, -16.25]}, {"time": 0.5, "x": 125.9, "y": -16.25, "curve": [0.542, 125.9, 0.625, -33.5, 0.542, -16.25, 0.625, -10.43]}, {"time": 0.6667, "x": -33.5, "y": -10.43, "curve": "stepped"}, {"time": 1.5333, "x": -33.5, "y": -10.43, "curve": [1.575, -33.5, 1.658, 110.76, 1.575, -10.43, 1.658, -45.78]}, {"time": 1.7, "x": 110.76, "y": -45.78, "curve": "stepped"}, {"time": 2.0333, "x": 110.76, "y": -45.78, "curve": "stepped"}, {"time": 2.0667, "x": 43.73, "y": 70.93, "curve": "stepped"}, {"time": 2.6667, "x": 43.73, "y": 70.93, "curve": [2.733, 43.73, 2.867, 5.01, 2.733, 70.93, 2.867, 0.59]}, {"time": 2.9333, "x": 5.01, "y": 0.59}]}, "ARM_RIGHT_HANDLE": {"translate": [{"x": 2.39, "y": 0.21, "curve": [0.191, 62.32, 0.366, 105.12, 0.191, 3.4, 0.366, 5.69]}, {"time": 0.5, "x": 105.12, "y": 5.69, "curve": [0.542, 105.12, 0.625, -21.72, 0.542, 5.69, 0.625, -3.08]}, {"time": 0.6667, "x": -21.72, "y": -3.08, "curve": "stepped"}, {"time": 1.5333, "x": -21.72, "y": -3.08, "curve": [1.575, -21.72, 1.658, 120.85, 1.575, -3.08, 1.658, 51.15]}, {"time": 1.7, "x": 120.85, "y": 51.15, "curve": "stepped"}, {"time": 2.0333, "x": 120.85, "y": 51.15, "curve": "stepped"}, {"time": 2.0667, "x": 14.6, "y": 215.02, "curve": [2.225, 14.6, 2.463, 14.51, 2.225, 215.02, 2.463, 230.23]}, {"time": 2.7, "x": 14.42, "y": 245.44, "curve": [2.733, 14.42, 2.783, 11.41, 2.733, 245.44, 2.783, 184.13]}, {"time": 2.8333, "x": 8.41, "y": 122.82, "curve": "stepped"}, {"time": 2.8667, "x": -5.18, "y": 34.4, "curve": [2.878, -3.16, 2.89, -1.4, 2.878, 25.31, 2.89, 17.35]}, {"time": 2.9, "x": -0.14, "y": 11.67, "curve": "stepped"}, {"time": 2.9333, "x": 2.39, "y": 0.21}]}, "CAPE_CORNER_RIGHT": {"rotate": [{"value": 30.04, "curve": [0.155, 30.04, 0.371, 29.48]}, {"time": 0.6, "value": 28.82, "curve": [0.65, 28.82, 0.724, 40.69]}, {"time": 0.8, "value": 53.35, "curve": [0.822, 41.7, 0.845, 29.83]}, {"time": 0.8667, "value": 18.72, "curve": [0.892, 37.78, 0.916, 53.35]}, {"time": 0.9333, "value": 53.35, "curve": [0.956, 41.7, 0.978, 29.83]}, {"time": 1, "value": 18.72, "curve": [1.025, 37.78, 1.049, 53.35]}, {"time": 1.0667, "value": 53.35, "curve": [1.089, 41.7, 1.111, 29.83]}, {"time": 1.1333, "value": 18.72, "curve": [1.159, 37.78, 1.183, 53.35]}, {"time": 1.2, "value": 53.35, "curve": "stepped"}, {"time": 1.4667, "value": 53.35, "curve": [1.608, 53.35, 1.892, 30.04]}, {"time": 2.0333, "value": 30.04, "curve": [2.042, 30.04, 2.054, 16.86]}, {"time": 2.0667, "value": 1.06, "curve": [2.315, 5.52, 2.721, 30.04]}, {"time": 2.9333, "value": 30.04}], "translate": [{"x": 6.97, "y": -2.09, "curve": [0.228, 27.01, 0.446, 44.18, 0.228, -10.59, 0.446, -17.87]}, {"time": 0.6, "x": 44.18, "y": -17.87, "curve": [0.65, 44.18, 0.75, -10.33, 0.65, -17.87, 0.75, 23.77]}, {"time": 0.8, "x": -10.33, "y": 23.77, "curve": [0.821, -8.12, 0.844, -5.2, 0.821, 22.49, 0.844, 20.79]}, {"time": 0.8667, "x": -2.11, "y": 19, "curve": [0.891, -5.95, 0.917, -10.33, 0.891, 21.23, 0.917, 23.77]}, {"time": 0.9333, "x": -10.33, "y": 23.77, "curve": [0.954, -8.12, 0.977, -5.2, 0.954, 22.49, 0.977, 20.79]}, {"time": 1, "x": -2.11, "y": 19, "curve": [1.025, -5.95, 1.05, -10.33, 1.025, 21.23, 1.05, 23.77]}, {"time": 1.0667, "x": -10.33, "y": 23.77, "curve": [1.088, -8.12, 1.11, -5.2, 1.088, 22.49, 1.11, 20.79]}, {"time": 1.1333, "x": -2.11, "y": 19, "curve": [1.158, -5.95, 1.184, -10.33, 1.158, 21.23, 1.184, 23.77]}, {"time": 1.2, "x": -10.33, "y": 23.77, "curve": [1.241, -11.25, 1.4, -12.17, 1.241, 32.9, 1.4, 41.96]}, {"time": 1.4667, "x": -12.17, "y": 41.96, "curve": [1.608, -12.17, 1.892, 36.16, 1.608, 41.96, 1.892, -13.32]}, {"time": 2.0333, "x": 36.16, "y": -13.32, "curve": [2.046, 23.58, 2.058, 12.8, 2.046, -8.22, 2.058, -3.84]}, {"time": 2.0667, "x": 12.8, "y": -3.84, "curve": [2.114, 0, 2.41, -0.22, 2.114, 58.22, 2.41, 59.31]}, {"time": 2.5333, "x": -0.22, "y": 59.31, "curve": [2.633, -0.22, 2.833, 6.97, 2.633, 59.31, 2.833, -2.09]}, {"time": 2.9333, "x": 6.97, "y": -2.09}]}, "CAPE_CORNER_LEFT": {"rotate": [{"value": -8.13, "curve": [0.225, -18.17, 0.45, -28.2]}, {"time": 0.6, "value": -28.2, "curve": [0.65, -28.2, 0.724, -28]}, {"time": 0.8, "value": -27.78, "curve": [0.822, -27.77, 0.845, -27.75]}, {"time": 0.8667, "value": -27.73, "curve": [0.892, -27.76, 0.916, -27.78]}, {"time": 0.9333, "value": -27.78, "curve": [0.956, -27.77, 0.978, -27.75]}, {"time": 1, "value": -27.73, "curve": [1.025, -27.76, 1.049, -27.78]}, {"time": 1.0667, "value": -27.78, "curve": [1.089, -27.77, 1.111, -27.75]}, {"time": 1.1333, "value": -27.73, "curve": [1.159, -27.76, 1.183, -27.78]}, {"time": 1.2, "value": -27.78, "curve": "stepped"}, {"time": 1.4667, "value": -27.78, "curve": [1.608, -27.78, 1.892, -8.13]}, {"time": 2.0333, "value": -8.13, "curve": [2.046, -9.02, 2.058, -9.92]}, {"time": 2.0667, "value": -9.92, "curve": [2.13, -13.33, 2.191, -16.25]}, {"time": 2.2333, "value": -16.25, "curve": [2.275, -16.25, 2.338, -12.19]}, {"time": 2.4, "value": -8.13}], "translate": [{"curve": [0.15, 0, 0.45, 47.64, 0.15, 0, 0.45, 22.55]}, {"time": 0.6, "x": 47.64, "y": 22.55, "curve": [0.65, 47.64, 0.75, -6.06, 0.65, 22.55, 0.75, -9.42]}, {"time": 0.8, "x": -6.06, "y": -9.42, "curve": [0.818, -6.06, 0.841, -2.38, 0.818, -9.42, 0.841, -1.72]}, {"time": 0.8667, "x": 2.97, "y": 9.48, "curve": [0.889, -0.22, 0.917, -6.06, 0.889, 2.81, 0.917, -9.42]}, {"time": 0.9333, "x": -6.06, "y": -9.42, "curve": [0.952, -6.06, 0.975, -2.92, 0.952, -9.42, 0.975, -4.52]}, {"time": 1, "x": 1.64, "y": 2.6, "curve": [1.023, -1.08, 1.051, -6.06, 1.023, -1.64, 1.051, -9.42]}, {"time": 1.0667, "x": -6.06, "y": -9.42, "curve": [1.085, -6.06, 1.108, -2.92, 1.085, -9.42, 1.108, -4.52]}, {"time": 1.1333, "x": 1.64, "y": 2.6, "curve": [1.156, -1.08, 1.184, -6.06, 1.156, -1.64, 1.184, -9.42]}, {"time": 1.2, "x": -6.06, "y": -9.42, "curve": [1.241, -9.15, 1.4, -12.21, 1.241, -11.51, 1.4, -13.59]}, {"time": 1.4667, "x": -12.21, "y": -13.59, "curve": [1.608, -12.21, 1.892, 52.22, 1.608, -13.59, 1.892, 26.87]}, {"time": 2.0333, "x": 52.22, "y": 26.87, "curve": [2.042, 52.22, 2.058, 11.92, 2.042, 26.87, 2.058, 14.35]}, {"time": 2.0667, "x": 11.92, "y": 14.35, "curve": [2.162, 10.09, 2.318, 0, 2.162, 12.15, 2.318, 0]}, {"time": 2.4}]}, "FACE": {"translate": [{"x": 3.05, "y": -0.15, "curve": [0.193, -6.55, 0.403, -18.55, 0.193, 2.55, 0.403, 5.93]}, {"time": 0.5333, "x": -18.55, "y": 5.93, "curve": [0.575, -18.55, 0.658, -6.48, 0.575, 5.93, 0.658, -1.68]}, {"time": 0.7, "x": -6.48, "y": -1.68, "curve": [0.715, 35.47, 1.15, 45.05, 0.715, -0.82, 1.15, -0.63]}, {"time": 1.3, "x": 45.05, "y": -0.63, "curve": [1.483, 45.05, 1.85, 1.37, 1.483, -0.63, 1.85, -5.61]}, {"time": 2.0333, "x": 1.37, "y": -5.61, "curve": [2.075, 1.37, 2.087, -18.55, 2.075, -5.61, 2.087, 5.93]}, {"time": 2.2, "x": -18.55, "y": 5.93, "curve": "stepped"}, {"time": 2.5, "x": -18.55, "y": 5.93, "curve": [2.608, -18.55, 2.825, 3.05, 2.608, 5.93, 2.825, -0.15]}, {"time": 2.9333, "x": 3.05, "y": -0.15}], "scale": [{}, {"time": 0.5333, "x": 0.632, "y": 1.195, "curve": [0.575, 0.632, 0.658, 1, 0.575, 1.195, 0.658, 1]}, {"time": 0.7, "curve": "stepped"}, {"time": 2.0333}, {"time": 2.2, "x": 0.632, "curve": "stepped"}, {"time": 2.5, "x": 0.632, "curve": [2.608, 0.632, 2.825, 1, 2.608, 1, 2.825, 1]}, {"time": 2.9333}]}, "root": {"translate": [{"time": 0.6333, "curve": [0.65, 0, 0.683, -25.99, 0.65, 0, 0.683, 0]}, {"time": 0.7, "x": -25.99, "curve": [0.717, -25.99, 0.742, -7.5, 0.717, 0, 0.742, 0]}, {"time": 0.7667, "x": 11, "curve": [0.792, 2.2, 0.817, -6.6, 0.792, 0, 0.817, 0]}, {"time": 0.8333, "x": -6.6, "curve": [0.85, -6.6, 0.875, 0, 0.85, 0, 0.875, 0]}, {"time": 0.9, "x": 6.6, "curve": [0.925, 3.3, 0.95, 0, 0.925, 0, 0.95, 0]}, {"time": 0.9667, "curve": [0.983, 0, 1.008, 2.2, 0.983, 0, 1.008, 0]}, {"time": 1.0333, "x": 4.4, "curve": [1.058, 2.2, 1.083, 0, 1.058, 0, 1.083, 0]}, {"time": 1.1, "curve": [1.117, 0, 1.142, 1.65, 1.117, 0, 1.142, 0]}, {"time": 1.1667, "x": 3.3, "curve": [1.192, 1.65, 1.217, 0, 1.192, 0, 1.217, 0]}, {"time": 1.2333}]}, "ARM_LEFT": {"rotate": [{"time": 1.5333, "value": -73.81, "curve": "stepped"}, {"time": 2.0333, "value": -73.81, "curve": [2.042, -73.81, 2.058, -0.09]}, {"time": 2.0667, "value": -0.09}]}, "NECK1": {"rotate": [{"time": 1.5333, "value": 2.38}]}, "NECK2": {"rotate": [{"time": 1.5333, "value": 11.31}]}, "Necklace2": {"translate": [{"x": 1.89, "y": 11.25, "curve": [0.491, 1.35, 1.161, 0, 0.491, 8.04, 1.161, 0]}, {"time": 1.5333, "curve": "stepped"}, {"time": 2.7, "curve": [2.758, 0, 2.875, 1.89, 2.758, 0, 2.875, 11.25]}, {"time": 2.9333, "x": 1.89, "y": 11.25}]}, "Necklace4": {"translate": [{"x": 0.28, "y": 1.69, "curve": [0.575, 0.11, 1.098, 0, 0.575, 0.65, 1.098, 0]}, {"time": 1.5333, "curve": "stepped"}, {"time": 2.7, "curve": [2.758, 0, 2.875, 0.28, 2.758, 0, 2.875, 1.69]}, {"time": 2.9333, "x": 0.28, "y": 1.69}]}, "ARM_RIGHT": {"rotate": [{"time": 1.5333, "value": -76.72, "curve": "stepped"}, {"time": 2.0333, "value": -76.72, "curve": [2.042, -76.72, 2.058, 0]}, {"time": 2.0667}]}, "BODY_HANDLE": {"translate": [{"time": 2.0333, "curve": [2.042, 0, 2.058, 9.68, 2.042, 0, 2.058, 44.83]}, {"time": 2.0667, "x": 9.68, "y": 44.83, "curve": "stepped"}, {"time": 2.7, "x": 9.68, "y": 44.83, "curve": [2.768, 7.92, 2.876, 0, 2.768, 36.68, 2.876, 0]}, {"time": 2.9333}]}, "effects": {"translate": [{"time": 0.6667, "x": -333.93, "y": -8}]}, "HEAD_TOP": {"rotate": [{"value": -4.72}]}}, "drawOrder": [{"time": 0.5, "offsets": [{"slot": "Other/SpawnParticles", "offset": 26}, {"slot": "Other/SpawnParticles2", "offset": 26}, {"slot": "Other/SpawnParticles3", "offset": 26}]}, {"time": 1.6667, "offsets": [{"slot": "Other/SpawnParticles", "offset": 23}, {"slot": "Other/SpawnParticles2", "offset": 23}, {"slot": "Other/SpawnParticles3", "offset": 23}, {"slot": "GoatCult/Arm2", "offset": 17}, {"slot": "GoatCult/Sleeve2", "offset": 17}, {"slot": "GoatCult/Hand2", "offset": 17}]}, {"time": 2.1, "offsets": [{"slot": "GoatCult/Arm2", "offset": 19}, {"slot": "GoatCult/Sleeve2", "offset": 16}, {"slot": "GoatCult/Hand2", "offset": 16}]}, {"time": 2.8667, "offsets": [{"slot": "GoatCult/Arm2", "offset": -7}, {"slot": "GoatCult/Sleeve2", "offset": -10}, {"slot": "GoatCult/Hand2", "offset": -10}]}], "events": [{"time": 2.1, "name": "fire"}]}, "projectile-charge": {"slots": {"fire": {"attachment": [{"time": 0.2667, "name": "Other/Fire/Fire_0"}, {"time": 0.3333, "name": "Other/Fire/Fire_1"}, {"time": 0.4, "name": "Other/Fire/Fire_2"}, {"time": 0.4667, "name": "Other/Fire/Fire_3"}, {"time": 0.5333, "name": "Other/Fire/Fire_4"}, {"time": 0.6, "name": "Other/Fire/Fire_5"}, {"time": 0.6667, "name": "Other/Fire/Fire_6"}, {"time": 0.7333, "name": "Other/Fire/Fire_0"}, {"time": 0.8, "name": "Other/Fire/Fire_1"}, {"time": 0.8667, "name": "Other/Fire/Fire_2"}, {"time": 0.9333, "name": "Other/Fire/Fire_3"}, {"time": 1, "name": "Other/Fire/Fire_4"}, {"time": 1.0667, "name": "Other/Fire/Fire_5"}, {"time": 1.1333, "name": "Other/Fire/Fire_6"}]}, "GoatCult/Hand1": {"attachment": [{"name": "Hand3"}]}}, "bones": {"HAND_LEFT": {"rotate": [{"value": -7.34}]}, "fire": {"scale": [{"time": 0.2667, "x": 0.099, "y": 0.099, "curve": [0.367, 0.099, 0.567, 0.696, 0.367, 0.099, 0.567, 0.696]}, {"time": 0.6667, "x": 0.696, "y": 0.696}]}, "BODY_BTM": {"rotate": [{"value": -1.67, "curve": [0.25, -3.88, 0.5, -6.09]}, {"time": 0.6667, "value": -6.09, "curve": [0.785, -8.31, 0.945, -10.25]}, {"time": 1.1667, "value": -10.25}], "translate": [{"curve": [0.167, 0, 0.5, 30.85, 0.167, 0, 0.5, 17.55]}, {"time": 0.6667, "x": 30.85, "y": 17.55}], "scale": [{"y": 1.015}]}, "GOO_1": {"rotate": [{"value": -15.24, "curve": "stepped"}, {"time": 0.1667, "value": -15.24, "curve": [0.217, -6.4, 0.275, 7.51]}, {"time": 0.3333, "value": 21.86, "curve": "stepped"}, {"time": 0.5, "value": 21.86, "curve": [0.608, 21.86, 0.825, -27.83]}, {"time": 0.9333, "value": -27.83, "curve": "stepped"}, {"time": 1.1, "value": -27.83, "curve": [1.119, -27.83, 1.142, -23.03]}, {"time": 1.1667, "value": -15.24}], "translate": [{"y": -4.63, "curve": "stepped"}, {"time": 0.1667, "y": -4.63, "curve": [0.229, 0, 0.292, 0, 0.229, -7.22, 0.292, -9.95]}, {"time": 0.3333, "y": -9.95, "curve": "stepped"}, {"time": 0.5, "y": -9.95, "curve": [0.592, 0, 0.775, 0, 0.592, -9.95, 0.775, 0]}, {"time": 0.8667, "curve": "stepped"}, {"time": 1.0333, "curve": [1.067, 0, 1.116, 0, 1.067, 0, 1.116, -2.26]}, {"time": 1.1667, "y": -4.63}]}, "GOO_2": {"rotate": [{"value": 2.37, "curve": "stepped"}, {"time": 0.1667, "value": 2.37, "curve": [0.204, 6.86, 0.241, 11.08]}, {"time": 0.2667, "value": 11.08, "curve": "stepped"}, {"time": 0.4333, "value": 11.08, "curve": [0.542, 11.08, 0.758, -8.03]}, {"time": 0.8667, "value": -8.03, "curve": "stepped"}, {"time": 1.0333, "value": -8.03, "curve": [1.066, -8.03, 1.117, -2.65]}, {"time": 1.1667, "value": 2.37}], "translate": [{"x": 0.02, "y": -8.05, "curve": [0.037, 0.01, 0.071, 0, 0.037, -9.23, 0.071, -9.95]}, {"time": 0.1, "y": -9.95, "curve": [0.117, 0, 0.15, 0.02, 0.117, -9.95, 0.15, -8.05]}, {"time": 0.1667, "x": 0.02, "y": -8.05, "curve": [0.204, 0.01, 0.238, 0, 0.204, -9.23, 0.238, -9.95]}, {"time": 0.2667, "y": -9.95, "curve": [0.375, 0, 0.592, 0.19, 0.375, -9.95, 0.592, 8.65]}, {"time": 0.7, "x": 0.19, "y": 8.65, "curve": "stepped"}, {"time": 0.8667, "x": 0.19, "y": 8.65, "curve": [0.94, 0.19, 1.073, 0.06, 0.94, 8.65, 1.073, -3.74]}, {"time": 1.1667, "x": 0.02, "y": -8.05}]}, "GOO_3": {"rotate": [{"value": 4.3, "curve": "stepped"}, {"time": 0.1667, "value": 4.3, "curve": [0.204, -0.46, 0.241, -4.92]}, {"time": 0.2667, "value": -4.92, "curve": "stepped"}, {"time": 0.4333, "value": -4.92, "curve": [0.542, -4.92, 0.758, 15.31]}, {"time": 0.8667, "value": 15.31, "curve": "stepped"}, {"time": 1.0333, "value": 15.31, "curve": [1.066, 15.31, 1.117, 9.62]}, {"time": 1.1667, "value": 4.3}], "translate": [{"y": -6.01, "curve": "stepped"}, {"time": 0.1667, "y": -6.01, "curve": [0.192, 0, 0.216, 0, 0.192, -8.13, 0.216, -9.95]}, {"time": 0.2333, "y": -9.95, "curve": "stepped"}, {"time": 0.4, "y": -9.95, "curve": [0.508, 0, 0.725, 0, 0.508, -9.95, 0.725, 0]}, {"time": 0.8333, "curve": "stepped"}, {"time": 1, "curve": [1.041, 0, 1.106, 0, 1.041, 0, 1.106, -3.26]}, {"time": 1.1667, "y": -6.01}]}, "NECKLACE_HANDLE": {"translate": [{"y": 0.34, "curve": [0.05, -0.05, 0.1, -0.11, 0.05, -4.94, 0.1, -10.59]}, {"time": 0.1333, "x": -0.11, "y": -10.59, "curve": [0.2, -0.11, 0.333, 0.1, 0.2, -10.59, 0.333, 9.5]}, {"time": 0.4, "x": 0.1, "y": 9.5, "curve": [0.442, 0.1, 0.504, 0.05, 0.442, 9.5, 0.504, 5.06]}, {"time": 0.5667, "y": 0.34, "curve": [0.629, -0.05, 0.692, -0.11, 0.629, -4.94, 0.692, -10.59]}, {"time": 0.7333, "x": -0.11, "y": -10.59, "curve": [0.8, -0.11, 0.933, 0.1, 0.8, -10.59, 0.933, 9.5]}, {"time": 1, "x": 0.1, "y": 9.5, "curve": [1.042, 0.1, 1.104, 0.05, 1.042, 9.5, 1.104, 5.06]}, {"time": 1.1667, "y": 0.34}]}, "NECKLACE": {"translate": [{"x": 3.65, "y": 2.07}]}, "ARM_LEFT_HANDLE": {"translate": [{"x": 5.01, "y": 0.59, "curve": [0.092, -0.9, 0.398, -10.66, 0.092, -14.25, 0.398, -38.8]}, {"time": 0.6667, "x": -10.66, "y": -38.8}]}, "ARM_RIGHT_HANDLE": {"translate": [{"x": 2.39, "y": 0.21, "curve": [0.445, 30.56, 0.853, 50.68, 0.445, -30.88, 0.853, -53.08]}, {"time": 1.1667, "x": 50.68, "y": -53.08}]}, "CAPE_CORNER_RIGHT": {"rotate": [{"value": 30.04, "curve": [0.026, 30.04, 0.062, 25.01]}, {"time": 0.1, "value": 18.99, "curve": [0.16, 10.55, 0.226, 0]}, {"time": 0.2667, "curve": [0.342, 0, 0.492, 30.04]}, {"time": 0.5667, "value": 30.04, "curve": [0.7, 30.04, 0.967, 0]}, {"time": 1.1, "curve": [1.12, 0, 1.143, 0.38]}, {"time": 1.1667, "value": 1.06}], "translate": [{"x": 6.97, "y": -2.09, "curve": [0.038, 3.22, 0.074, 0, 0.038, -0.96, 0.074, 0]}, {"time": 0.1, "curve": [0.158, 0, 0.275, 26.66, 0.158, 0, 0.275, -5.04]}, {"time": 0.3333, "x": 26.66, "y": -5.04, "curve": [0.425, 26.66, 0.608, 0, 0.425, -5.04, 0.608, 0]}, {"time": 0.7, "curve": [0.814, 0, 1.003, 7.68, 0.814, 0, 1.003, -2.3]}, {"time": 1.1667, "x": 12.8, "y": -3.84}]}, "CAPE_CORNER_LEFT": {"rotate": [{"value": -8.13, "curve": [0.063, -4.06, 0.125, 0]}, {"time": 0.1667, "curve": [0.267, 0, 0.467, -16.25]}, {"time": 0.5667, "value": -16.25, "curve": [0.658, -16.25, 0.842, 0]}, {"time": 0.9333, "curve": [0.991, 0, 1.081, -5.41]}, {"time": 1.1667, "value": -9.92}], "translate": [{"curve": [0.1, 0, 0.3, 20.76, 0.1, 0, 0.3, 12.29]}, {"time": 0.4, "x": 20.76, "y": 12.29, "curve": [0.492, 20.76, 0.675, 0, 0.492, 12.29, 0.675, 0]}, {"time": 0.7667, "curve": [0.858, 0, 1.042, 12.36, 0.858, 0, 1.042, 14.88]}, {"time": 1.1333, "x": 12.36, "y": 14.88, "curve": [1.144, 12.36, 1.155, 12.21, 1.144, 14.88, 1.155, 14.69]}, {"time": 1.1667, "x": 11.92, "y": 14.35}]}, "HAND_RIGHT": {"rotate": [{"value": -7.2}]}, "SLEEVE_LEFT": {"rotate": [{"value": -27.05}], "translate": [{"x": 11.1, "curve": [0.175, 19.93, 0.333, 25.38, 0.175, -2.81, 0.333, -4.55]}, {"time": 0.4667, "x": 25.38, "y": -4.55}]}, "SLEEVE_RIGHT": {"rotate": [{"value": -21.61}], "translate": [{"x": 7.6, "curve": [0.216, 1.32, 0.505, -13.6, 0.216, -1.46, 0.505, -4.93]}, {"time": 0.6667, "x": -13.6, "y": -4.93}]}, "HORN_R": {"rotate": [{"value": 12.05}]}, "HORN_L": {"rotate": [{"value": -13.44}]}, "HEAD": {"rotate": [{"value": 1.32, "curve": [0.21, -1.12, 0.438, -4.37]}, {"time": 0.6667, "value": -7.63}], "translate": [{"x": -5.85, "y": -0.18, "curve": [0.445, -8.48, 0.866, -10.67, 0.445, -0.28, 0.866, -0.35]}, {"time": 1.1667, "x": -10.67, "y": -0.35}]}, "FACE": {"translate": [{"x": 3.05, "y": -0.15, "curve": [0.423, 16.2, 0.881, 32.64, 0.423, -0.93, 0.881, -1.91]}, {"time": 1.1667, "x": 32.64, "y": -1.91}]}, "BODY_TOP": {"rotate": [{}, {"time": 0.6667, "value": -10.65}]}, "SHOULDER_RIGHT": {"translate": [{"curve": [0.167, 0, 0.5, -11.35, 0.167, 0, 0.5, -4.49]}, {"time": 0.6667, "x": -11.35, "y": -4.49}], "scale": [{"curve": [0.167, 1, 0.5, 1.248, 0.167, 1, 0.5, 1]}, {"time": 0.6667, "x": 1.248}]}, "SHOULDER_LEFT": {"rotate": [{"time": 1.1667, "value": 110.58}]}, "Necklace2": {"translate": [{"x": 1.89, "y": 11.25, "curve": [0.075, 1.27, 0.177, -0.29, 0.075, 7.58, 0.177, -1.6]}, {"time": 0.2333, "x": -0.29, "y": -1.6, "curve": [0.3, -0.29, 0.433, 2.22, 0.3, -1.6, 0.433, 13.18]}, {"time": 0.5, "x": 2.22, "y": 13.18, "curve": [0.519, 2.22, 0.542, 2.09, 0.519, 13.18, 0.542, 12.44]}, {"time": 0.5667, "x": 1.89, "y": 11.25, "curve": [0.641, 1.27, 0.743, -0.29, 0.641, 7.58, 0.743, -1.6]}, {"time": 0.8, "x": -0.29, "y": -1.6, "curve": [0.867, -0.29, 1, 2.22, 0.867, -1.6, 1, 13.18]}, {"time": 1.0667, "x": 2.22, "y": 13.18, "curve": [1.095, 2.22, 1.129, 2.09, 1.095, 13.18, 1.129, 12.44]}, {"time": 1.1667, "x": 1.89, "y": 11.25}]}, "Necklace4": {"translate": [{"x": 0.28, "y": 1.69, "curve": [0.025, 0.11, 0.048, 0, 0.025, 0.65, 0.048, 0]}, {"time": 0.0667, "curve": [0.133, 0, 0.267, 2.22, 0.133, 0, 0.267, 13.18]}, {"time": 0.3333, "x": 2.22, "y": 13.18, "curve": [0.382, 2.22, 0.463, 1.26, 0.382, 13.18, 0.463, 7.51]}, {"time": 0.5333, "x": 0.63, "y": 3.74, "curve": [0.545, 0.5, 0.556, 0.38, 0.545, 2.96, 0.556, 2.25]}, {"time": 0.5667, "x": 0.28, "y": 1.69, "curve": [0.592, 0.11, 0.614, 0, 0.592, 0.65, 0.614, 0]}, {"time": 0.6333, "curve": [0.7, 0, 0.833, 2.22, 0.7, 0, 0.833, 13.18]}, {"time": 0.9, "x": 2.22, "y": 13.18, "curve": [0.949, 2.22, 1.03, 1.26, 0.949, 13.18, 1.03, 7.51]}, {"time": 1.1, "x": 0.63, "y": 3.74, "curve": [1.123, 0.5, 1.146, 0.38, 1.123, 2.96, 1.146, 2.25]}, {"time": 1.1667, "x": 0.28, "y": 1.69}]}, "GOO": {"rotate": [{"curve": [0.058, 0, 0.175, -12.4]}, {"time": 0.2333, "value": -12.4, "curve": [0.45, -12.4, 0.883, 0]}, {"time": 1.1}], "translate": [{"curve": [0.067, 0, 0.2, 8.11, 0.067, 0, 0.2, 29.68]}, {"time": 0.2667, "x": 8.11, "y": 29.68, "curve": [0.492, 8.11, 0.942, 0, 0.492, 29.68, 0.942, -12.94]}, {"time": 1.1667, "y": -12.94}]}, "HEAD_TOP": {"rotate": [{"value": -4.72}]}}}, "projectile-impact": {"slots": {"fire": {"attachment": [{"name": "Other/Fire/Fire_6"}, {"time": 0.0333}]}, "GoatCult/Hand1": {"attachment": [{"time": 0.2, "name": "Hand3"}]}, "GoatCult/Hand2": {"attachment": [{"time": 0.1333, "name": "Hand3"}]}}, "bones": {"HAND_LEFT": {"rotate": [{"value": -7.34, "curve": "stepped"}, {"time": 0.0333, "value": -7.34, "curve": [0.259, 1.16, 0.465, 6.6]}, {"time": 0.6333, "value": 6.6, "curve": [0.692, 6.6, 0.808, -7.34]}, {"time": 0.8667, "value": -7.34}]}, "HAND_RIGHT": {"rotate": [{"value": -7.2, "curve": "stepped"}, {"time": 0.1, "value": -7.2, "curve": [0.179, -1.79, 0.469, 1.04]}, {"time": 0.6333, "value": 1.04, "curve": [0.692, 1.04, 0.808, -7.2]}, {"time": 0.8667, "value": -7.2}]}, "fire": {"scale": [{"time": 0.6333, "x": 0.696, "y": 0.696}]}, "BODY_BTM": {"rotate": [{"value": -10.25, "curve": [0.025, -10.25, 0.075, -27.99]}, {"time": 0.1, "value": -27.99, "curve": "stepped"}, {"time": 0.6333, "value": -27.99, "curve": [0.692, -27.99, 0.808, -1.67]}, {"time": 0.8667, "value": -1.67}], "translate": [{"x": 30.85, "y": 17.55, "curve": [0.025, 30.85, 0.075, 0, 0.025, 17.55, 0.075, 0]}, {"time": 0.1, "curve": [0.125, 19.3, 0.5, 23.67, 0.125, 0, 0.5, 0]}, {"time": 0.6333, "x": 23.67, "curve": [0.692, 23.67, 0.808, 0, 0.692, 0, 0.808, 0]}, {"time": 0.8667}], "scale": [{"time": 0.6333, "curve": [0.692, 1, 0.808, 1, 0.692, 1, 0.808, 1.015]}, {"time": 0.8667, "y": 1.015}]}, "GOO_1": {"rotate": [{"value": -1.28, "curve": [0.076, -18.18, 0.199, -31.21]}, {"time": 0.3333, "value": -40.83, "curve": [0.522, -22.99, 0.74, -15.24]}, {"time": 0.8667, "value": -15.24}], "translate": [{"curve": [0.069, 2.1, 0.214, 3.31, 0.069, 15.67, 0.214, 24.65]}, {"time": 0.3333, "x": 3.83, "y": 28.51, "curve": [0.385, 4.06, 0.431, 4.16, 0.385, 30.22, 0.431, 31.01]}, {"time": 0.4667, "x": 4.16, "y": 31.01, "curve": [0.567, 4.16, 0.767, 0, 0.567, 31.01, 0.767, -4.63]}, {"time": 0.8667, "y": -4.63}]}, "GOO_2": {"rotate": [{"value": 0.24, "curve": [0.076, -16.47, 0.199, -29.33]}, {"time": 0.3333, "value": -38.84, "curve": [0.522, -10.12, 0.74, 2.37]}, {"time": 0.8667, "value": 2.37}], "translate": [{"x": 0.14, "y": 3.13, "curve": [0.07, 3.17, 0.205, 5.13, 0.07, 29.38, 0.205, 46.39]}, {"time": 0.3333, "x": 6.23, "y": 55.95, "curve": [0.423, 7.01, 0.508, 7.36, 0.423, 62.66, 0.508, 65.71]}, {"time": 0.5667, "x": 7.36, "y": 65.71, "curve": [0.642, 7.36, 0.792, 0.02, 0.642, 65.71, 0.792, -8.05]}, {"time": 0.8667, "x": 0.02, "y": -8.05}]}, "GOO_3": {"rotate": [{"value": -0.93, "curve": [0.076, -17.74, 0.199, -30.67]}, {"time": 0.3333, "value": -40.24, "curve": [0.522, -9.2, 0.74, 4.3]}, {"time": 0.8667, "value": 4.3}], "translate": [{"y": -0.67, "curve": [0.069, 3.27, 0.226, 4.92, 0.069, 24.99, 0.226, 37.87]}, {"time": 0.3333, "x": 5.38, "y": 41.45, "curve": [0.358, 5.49, 0.381, 5.55, 0.358, 42.38, 0.381, 42.83]}, {"time": 0.4, "x": 5.55, "y": 42.83, "curve": [0.517, 5.55, 0.75, 0, 0.517, 42.83, 0.75, -6.01]}, {"time": 0.8667, "y": -6.01}]}, "NECKLACE_HANDLE": {"translate": [{"x": 31.23, "y": 0.38, "curve": [0.019, 31.2, 0.067, 31.18, 0.019, 18.77, 0.067, 33.62]}, {"time": 0.1333, "x": 31.16, "y": 45.61, "curve": [0.261, 28.09, 0.521, 27.77, 0.261, 53.38, 0.521, 54.19]}, {"time": 0.6333, "x": 27.77, "y": 54.19, "curve": [0.692, 27.77, 0.808, 0, 0.692, 54.19, 0.808, 0.34]}, {"time": 0.8667, "y": 0.34}]}, "NECKLACE": {"translate": [{"x": 3.65, "y": 2.07, "curve": [0.034, 5.45, 0.067, 7.18, 0.034, 9.89, 0.067, 17.43]}, {"time": 0.1, "x": 8.83, "y": 24.58, "curve": "stepped"}, {"time": 0.6333, "x": 8.83, "y": 24.58, "curve": [0.722, 5.8, 0.804, 3.65, 0.722, 11.42, 0.804, 2.07]}, {"time": 0.8667, "x": 3.65, "y": 2.07}], "scale": [{"curve": [0.029, 1, 0.063, 1, 0.029, 1, 0.063, 0.967]}, {"time": 0.1, "y": 0.912}]}, "ARM_LEFT_HANDLE": {"translate": [{"x": -10.66, "y": -38.8, "curve": [0.025, -10.66, 0.075, -16.49, 0.025, -38.8, 0.075, -187.17]}, {"time": 0.1, "x": -16.49, "y": -187.17, "curve": [0.226, -8, 0.5, -2.78, 0.226, -199.45, 0.5, -207.01]}, {"time": 0.6333, "x": -2.78, "y": -207.01, "curve": [0.667, -2.78, 0.717, -24.78, 0.667, -207.01, 0.717, -143.38]}, {"time": 0.7667, "x": -46.78, "y": -79.76, "curve": [0.804, -20.89, 0.842, 5.01, 0.804, -39.58, 0.842, 0.59]}, {"time": 0.8667, "x": 5.01, "y": 0.59}]}, "ARM_RIGHT_HANDLE": {"translate": [{"x": 50.68, "y": -53.08, "curve": [0.009, 50.68, 0.021, 46.49, 0.009, -53.08, 0.021, -11.48]}, {"time": 0.0333, "x": 40.9, "y": 43.98, "curve": "stepped"}, {"time": 0.0667, "x": 26.05, "y": 191.56, "curve": [0.079, 20.44, 0.091, 16.24, 0.079, 247.21, 0.091, 288.94]}, {"time": 0.1, "x": 16.24, "y": 288.94, "curve": [0.233, 16.24, 0.5, 14.42, 0.233, 288.94, 0.5, 245.44]}, {"time": 0.6333, "x": 14.42, "y": 245.44, "curve": [0.658, 14.42, 0.696, 11.41, 0.658, 245.44, 0.696, 184.13]}, {"time": 0.7333, "x": 8.41, "y": 122.82, "curve": "stepped"}, {"time": 0.7667, "x": -5.18, "y": 34.4, "curve": [0.805, -0.85, 0.84, 2.39, 0.805, 14.86, 0.84, 0.21]}, {"time": 0.8667, "x": 2.39, "y": 0.21}]}, "CAPE_CORNER_RIGHT": {"rotate": [{"value": 1.06, "curve": [0.047, -1.88, 0.106, -9.89]}, {"time": 0.1667, "value": -18.63, "curve": [0.425, 4.16, 0.694, 30.04]}, {"time": 0.8667, "value": 30.04}], "translate": [{"x": 12.8, "y": -3.84, "curve": [0.017, 2.27, 0.099, -0.87, 0.017, 44.66, 0.099, 59.13]}, {"time": 0.1667, "x": -1.8, "y": 63.43, "curve": [0.283, -0.31, 0.39, -0.22, 0.283, 59.53, 0.39, 59.31]}, {"time": 0.4667, "x": -0.22, "y": 59.31, "curve": [0.567, -0.22, 0.767, 6.97, 0.567, 59.31, 0.767, -2.09]}, {"time": 0.8667, "x": 6.97, "y": -2.09}]}, "CAPE_CORNER_LEFT": {"rotate": [{"value": -9.92, "curve": [0.063, -25.7, 0.124, -39.22]}, {"time": 0.1667, "value": -39.22, "curve": [0.342, -23.67, 0.517, -8.13]}, {"time": 0.6333, "value": -8.13}], "translate": [{"x": 11.92, "y": 14.35, "curve": [0.048, 12.82, 0.126, 17.72, 0.048, 13.2, 0.126, 6.82]}, {"time": 0.1667, "x": 17.72, "y": 6.82, "curve": [0.283, 17.72, 0.517, 0, 0.283, 6.82, 0.517, 0]}, {"time": 0.6333}]}, "SLEEVE_LEFT": {"rotate": [{"time": 0.6333, "curve": [0.692, 0, 0.808, -27.05]}, {"time": 0.8667, "value": -27.05}], "translate": [{"x": 25.38, "y": -4.55, "curve": [0.012, 18.92, 0.023, 14.03, 0.012, -2.49, 0.023, -0.93]}, {"time": 0.0333, "x": 11.1, "curve": [0.056, 9.44, 0.078, 7.87, 0.056, -6.47, 0.078, -12.6]}, {"time": 0.1, "x": 6.39, "y": -18.4, "curve": "stepped"}, {"time": 0.6333, "x": 6.39, "y": -18.4, "curve": [0.715, 9.47, 0.793, 11.1, 0.715, -6.37, 0.793, 0]}, {"time": 0.8667, "x": 11.1}]}, "SLEEVE_RIGHT": {"rotate": [{"time": 0.6333, "curve": [0.692, 0, 0.808, -21.61]}, {"time": 0.8667, "value": -21.61}], "translate": [{"x": 6.4, "y": -0.28, "curve": [0.011, 6.81, 0.022, 7.2, 0.011, -0.18, 0.022, -0.09]}, {"time": 0.0333, "x": 7.6, "curve": [0.056, -2.64, 0.078, -12.52, 0.056, -1.26, 0.078, -2.48]}, {"time": 0.1, "x": -21.98, "y": -3.65, "curve": [0.118, 2.47, 0.175, 22.71, 0.118, -4.54, 0.175, -5.28]}, {"time": 0.2, "x": 22.71, "y": -5.28, "curve": [0.242, 22.71, 0.325, -2.98, 0.242, -5.28, 0.325, 2.36]}, {"time": 0.3667, "x": -2.98, "y": 2.36, "curve": [0.417, -2.98, 0.517, 7.6, 0.417, 2.36, 0.517, 0]}, {"time": 0.5667, "x": 7.6}]}, "HORN_R": {"rotate": [{"curve": [0.025, 0, 0.075, -27.88]}, {"time": 0.1, "value": -27.88, "curve": [0.133, -27.88, 0.2, 25.88]}, {"time": 0.2333, "value": 25.88, "curve": [0.275, 25.88, 0.358, -30.54]}, {"time": 0.4, "value": -30.54, "curve": [0.458, -30.54, 0.575, 0]}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.6667, "curve": [0.717, 0, 0.817, 12.05]}, {"time": 0.8667, "value": 12.05}]}, "HORN_L": {"rotate": [{"curve": [0.025, 0, 0.075, 26.24]}, {"time": 0.1, "value": 26.24, "curve": [0.133, 26.24, 0.2, -31.44]}, {"time": 0.2333, "value": -31.44, "curve": [0.275, -31.44, 0.358, 30.55]}, {"time": 0.4, "value": 30.55, "curve": [0.458, 30.55, 0.575, 0]}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.6667, "curve": [0.717, 0, 0.817, -13.44]}, {"time": 0.8667, "value": -13.44}]}, "HEAD": {"rotate": [{"value": -7.63, "curve": [0.001, 3.18, 0.45, 5.39]}, {"time": 0.6, "value": 5.39, "curve": [0.667, 5.39, 0.8, 1.32]}, {"time": 0.8667, "value": 1.32}], "translate": [{"x": -10.67, "y": -0.35, "curve": [0.001, -6.67, 0.65, -5.85, 0.001, -0.21, 0.65, -0.18]}, {"time": 0.8667, "x": -5.85, "y": -0.18}]}, "FACE": {"translate": [{"x": 32.64, "y": -1.91, "curve": [0.033, 32.64, 0.1, -11.65, 0.033, -1.91, 0.1, 4.74]}, {"time": 0.1333, "x": -11.65, "y": 4.74, "curve": [0.317, -16.01, 0.486, -18.55, 0.317, 5.49, 0.486, 5.93]}, {"time": 0.6333, "x": -18.55, "y": 5.93, "curve": [0.692, -18.55, 0.808, 3.05, 0.692, 5.93, 0.808, -0.15]}, {"time": 0.8667, "x": 3.05, "y": -0.15}], "scale": [{"curve": [0.033, 1, 0.1, 0.632, 0.033, 1, 0.1, 1]}, {"time": 0.1333, "x": 0.632, "curve": "stepped"}, {"time": 0.6333, "x": 0.632, "curve": [0.692, 0.632, 0.808, 1, 0.692, 1, 0.808, 1]}, {"time": 0.8667}]}, "BODY_TOP": {"rotate": [{"value": -10.65, "curve": [0.025, -10.65, 0.075, 31.09]}, {"time": 0.1, "value": 31.09, "curve": "stepped"}, {"time": 0.6333, "value": 31.09, "curve": [0.692, 31.09, 0.808, 0]}, {"time": 0.8667}]}, "SHOULDER_RIGHT": {"rotate": [{"value": 25.61, "curve": [0.158, 25.61, 0.475, 195.79]}, {"time": 0.6333, "value": 195.79}], "translate": [{"x": -11.35, "y": -4.49, "curve": [0.009, -11.35, 0.021, -17.87, 0.009, -4.49, 0.021, 0.23]}, {"time": 0.0333, "x": -26.58, "y": 6.53, "curve": "stepped"}, {"time": 0.0667, "x": -30.08, "y": 41.94, "curve": [0.087, -30.08, 0.109, -29.4, 0.087, 41.94, 0.109, 45.08]}, {"time": 0.1333, "x": -28.21, "y": 50.62, "curve": "stepped"}, {"time": 0.6333, "x": -28.21, "y": 50.62, "curve": [0.672, -28.21, 0.702, -30.89, 0.672, 50.62, 0.702, 30.07]}, {"time": 0.7333, "x": -33.86, "y": 7.4, "curve": "stepped"}, {"time": 0.7667, "x": -56.93, "y": 12.67, "curve": [0.805, -26.88, 0.841, 0, 0.805, 5.98, 0.841, 0]}, {"time": 0.8667}], "scale": [{"curve": [0.008, 1, 0.025, 1.248, 0.008, 1, 0.025, 1]}, {"time": 0.0333, "x": 1.248, "curve": "stepped"}, {"time": 0.0667, "x": -1.248, "curve": "stepped"}, {"time": 0.7333, "x": -1.248, "curve": "stepped"}, {"time": 0.7667}]}, "SHOULDER_LEFT": {"rotate": [{"value": 110.58, "curve": [0.158, 110.58, 0.475, 161.1]}, {"time": 0.6333, "value": 161.1, "curve": [0.692, 161.1, 0.808, 110.58]}, {"time": 0.8667, "value": 110.58}], "translate": [{"curve": [0.025, 0, 0.075, -42.47, 0.025, 0, 0.075, -63.49]}, {"time": 0.1, "x": -42.47, "y": -63.49, "curve": "stepped"}, {"time": 0.6333, "x": -42.47, "y": -63.49, "curve": [0.692, -42.47, 0.808, 0, 0.692, -63.49, 0.808, 0]}, {"time": 0.8667}]}, "ARM_LEFT": {"rotate": [{"value": -83.8, "curve": [0.158, -83.8, 0.475, -0.09]}, {"time": 0.6333, "value": -0.09}]}, "NECK1": {"rotate": [{"value": 3.36, "curve": [0.158, 3.36, 0.475, 2.38]}, {"time": 0.6333, "value": 2.38}]}, "NECK2": {"rotate": [{"value": 35.47, "curve": [0.158, 35.47, 0.475, 11.31]}, {"time": 0.6333, "value": 11.31}]}, "Necklace2": {"translate": [{"time": 0.6333, "curve": [0.692, 0, 0.808, 1.89, 0.692, 0, 0.808, 11.25]}, {"time": 0.8667, "x": 1.89, "y": 11.25}]}, "Necklace4": {"translate": [{"time": 0.6333, "curve": [0.692, 0, 0.808, 0.28, 0.692, 0, 0.808, 1.69]}, {"time": 0.8667, "x": 0.28, "y": 1.69}]}, "ARM_RIGHT": {"rotate": [{"value": -29.99, "curve": [0.158, -29.99, 0.475, 0]}, {"time": 0.6333}]}, "BODY_HANDLE": {"translate": [{"time": 0.0333, "curve": [0.053, 0, 0.076, 3.48, 0.053, 0, 0.076, 16.14]}, {"time": 0.1, "x": 9.68, "y": 44.83, "curve": "stepped"}, {"time": 0.6333, "x": 9.68, "y": 44.83, "curve": [0.702, 7.92, 0.81, 0, 0.702, 36.68, 0.81, 0]}, {"time": 0.8667}]}, "HEAD_TOP": {"rotate": [{"curve": [0.017, 0, 0.05, -25.55]}, {"time": 0.0667, "value": -25.55, "curve": [0.1, -25.55, 0.167, 17.29]}, {"time": 0.2, "value": 17.29, "curve": [0.25, 17.29, 0.35, -11.37]}, {"time": 0.4, "value": -11.37, "curve": [0.467, -11.37, 0.6, 1.67]}, {"time": 0.6667, "value": 1.67, "curve": [0.717, 1.67, 0.817, -4.72]}, {"time": 0.8667, "value": -4.72}]}}, "drawOrder": [{"time": 0.0333, "offsets": [{"slot": "GoatCult/Arm2", "offset": 19}, {"slot": "GoatCult/Sleeve2", "offset": 16}, {"slot": "GoatCult/Hand2", "offset": 16}]}, {"time": 0.8, "offsets": [{"slot": "GoatCult/Arm2", "offset": -7}, {"slot": "GoatCult/Sleeve2", "offset": -10}, {"slot": "GoatCult/Hand2", "offset": -10}]}]}, "run": {"bones": {"BODY_BTM": {"rotate": [{"value": 4.83, "curve": [0.113, 5.49, 0.225, 6.16]}, {"time": 0.3, "value": 6.16, "curve": [0.442, 6.16, 0.725, 3.51]}, {"time": 0.8667, "value": 3.51, "curve": [0.942, 3.51, 1.054, 4.17]}, {"time": 1.1667, "value": 4.83}], "translate": [{"x": -11.36, "curve": [0.142, -11.36, 0.425, -15.65, 0.142, 0, 0.425, 31.72]}, {"time": 0.5667, "x": -15.65, "y": 31.72, "curve": [0.717, -15.65, 1.017, -11.36, 0.717, 31.72, 1.017, 0]}, {"time": 1.1667, "x": -11.36}], "scale": [{"curve": [0.142, 1, 0.425, 1.022, 0.142, 1, 0.425, 0.966]}, {"time": 0.5667, "x": 1.022, "y": 0.966, "curve": [0.717, 1.022, 1.017, 1, 0.717, 0.966, 1.017, 1]}, {"time": 1.1667}]}, "NECKLACE": {"translate": [{"y": 6.54}]}, "ARM_LEFT_HANDLE": {"translate": [{"x": 2.6, "y": -13.71, "curve": [0.037, 0.72, 0.159, -2.4, 0.037, -13.93, 0.159, -14.3]}, {"time": 0.2667, "x": -2.4, "y": -14.3, "curve": [0.408, -2.4, 0.692, 6.42, 0.408, -14.3, 0.692, -13.25]}, {"time": 0.8333, "x": 6.42, "y": -13.25, "curve": [0.923, 6.42, 1.039, 4.83, 0.923, -13.25, 1.039, -13.44]}, {"time": 1.1667, "x": 2.6, "y": -13.71}]}, "ARM_RIGHT_HANDLE": {"translate": [{"x": 3.98, "y": -8.33, "curve": [0.064, 2.58, 0.122, 1.58, 0.064, -8.45, 0.122, -8.54]}, {"time": 0.1667, "x": 1.58, "y": -8.54, "curve": [0.317, 1.58, 0.617, 7.99, 0.317, -8.54, 0.617, -9.51]}, {"time": 0.7667, "x": 7.99, "y": -9.51, "curve": [0.864, 7.99, 1.029, 5.48, 0.864, -9.51, 1.029, -8.78]}, {"time": 1.1667, "x": 3.98, "y": -8.33}]}, "HAND_RIGHT": {"rotate": [{"value": -3.7, "curve": [0.127, -1.54, 0.244, 0]}, {"time": 0.3333, "curve": [0.483, 0, 0.783, -11.4]}, {"time": 0.9333, "value": -11.4, "curve": [1.013, -8.69, 1.092, -5.98]}, {"time": 1.1667, "value": -3.7}]}, "HAND_LEFT": {"rotate": [{"value": -7.34, "curve": [0.13, -4.32, 0.277, 0]}, {"time": 0.3667, "curve": [0.517, 0, 0.817, -10.56]}, {"time": 0.9667, "value": -10.56, "curve": [1.019, -10.56, 1.09, -9.16]}, {"time": 1.1667, "value": -7.34}]}, "HORN_R": {"rotate": [{"value": 12.05, "curve": [0.111, 6.23, 0.226, 0]}, {"time": 0.3, "curve": [0.45, 0, 0.75, 22.15]}, {"time": 0.9, "value": 22.15, "curve": [0.967, 22.15, 1.066, 17.26]}, {"time": 1.1667, "value": 12.05}]}, "HORN_L": {"rotate": [{"value": -13.44, "curve": [0.111, -6.95, 0.226, 0]}, {"time": 0.3, "curve": [0.45, 0, 0.75, -24.71]}, {"time": 0.9, "value": -24.71, "curve": [0.967, -24.71, 1.066, -19.26]}, {"time": 1.1667, "value": -13.44}]}, "HEAD": {"rotate": [{"value": 1.32, "curve": [0.094, 1.94, 0.197, 2.77]}, {"time": 0.3, "value": 3.61, "curve": [0.442, 3.61, 0.725, 0]}, {"time": 0.8667, "curve": [0.949, 0, 1.053, 0.53]}, {"time": 1.1667, "value": 1.32}], "translate": [{"x": -10.67, "y": -0.35, "curve": [0.142, -10.67, 0.425, 2.43, 0.142, -0.35, 0.425, 0.11]}, {"time": 0.5667, "x": 2.43, "y": 0.11, "curve": [0.717, 2.43, 1.017, -10.67, 0.717, 0.11, 1.017, -0.35]}, {"time": 1.1667, "x": -10.67, "y": -0.35}]}, "BODY_TOP": {"rotate": [{"value": -8.69}]}, "BODY_HANDLE": {"translate": [{"x": 1.86, "y": 22.44}]}, "FACE": {"translate": [{"x": 3.05, "y": -0.15, "curve": [0.133, 0, 0.277, -3.81, 0.133, 0.38, 0.277, 1.03]}, {"time": 0.3667, "x": -3.81, "y": 1.03, "curve": [0.508, -3.81, 0.792, 7.04, 0.508, 1.03, 0.792, -0.83]}, {"time": 0.9333, "x": 7.04, "y": -0.83, "curve": [0.993, 7.04, 1.078, 5.23, 0.993, -0.83, 1.078, -0.52]}, {"time": 1.1667, "x": 3.05, "y": -0.15}]}, "GOO_2": {"rotate": [{"value": 2.37, "curve": [0.101, 6.86, 0.199, 11.08]}, {"time": 0.2667, "value": 11.08, "curve": [0.417, 11.08, 0.717, -8.03]}, {"time": 0.8667, "value": -8.03, "curve": [0.941, -8.03, 1.055, -2.65]}, {"time": 1.1667, "value": 2.37}], "translate": [{"x": 0.02, "y": -8.05, "curve": [0.037, 0.01, 0.071, 0, 0.037, -9.23, 0.071, -9.95]}, {"time": 0.1, "y": -9.95, "curve": [0.258, 0, 0.575, 0.19, 0.258, -9.95, 0.575, 8.65]}, {"time": 0.7333, "x": 0.19, "y": 8.65, "curve": [0.839, 0.19, 1.031, 0.06, 0.839, 8.65, 1.031, -3.74]}, {"time": 1.1667, "x": 0.02, "y": -8.05}]}, "CAPE_CORNER_LEFT": {"rotate": [{"value": -8.13, "curve": [0.038, -4.06, 0.075, 0]}, {"time": 0.1, "curve": [0.183, 0, 0.35, -16.25]}, {"time": 0.4333, "value": -16.25, "curve": [0.508, -16.25, 0.658, 0]}, {"time": 0.7333, "curve": [0.808, 0, 0.958, -16.25]}, {"time": 1.0333, "value": -16.25, "curve": [1.067, -16.25, 1.117, -12.19]}, {"time": 1.1667, "value": -8.13}], "translate": [{"curve": [0.075, 0, 0.225, 20.76, 0.075, 0, 0.225, 12.29]}, {"time": 0.3, "x": 20.76, "y": 12.29, "curve": [0.367, 20.76, 0.5, 0, 0.367, 12.29, 0.5, 0]}, {"time": 0.5667, "curve": [0.65, 0, 0.817, 12.36, 0.65, 0, 0.817, 14.88]}, {"time": 0.9, "x": 12.36, "y": 14.88, "curve": [0.967, 12.36, 1.1, 0, 0.967, 14.88, 1.1, 0]}, {"time": 1.1667}]}, "Necklace4": {"translate": [{"x": 0.28, "y": 1.69, "curve": [0.038, 0.11, 0.072, 0, 0.038, 0.65, 0.072, 0]}, {"time": 0.1, "curve": [0.25, 0, 0.55, 2.22, 0.25, 0, 0.55, 13.18]}, {"time": 0.7, "x": 2.22, "y": 13.18, "curve": [0.789, 2.22, 0.938, 1.26, 0.789, 13.18, 0.938, 7.51]}, {"time": 1.0667, "x": 0.63, "y": 3.74, "curve": [1.101, 0.5, 1.135, 0.38, 1.101, 2.96, 1.135, 2.25]}, {"time": 1.1667, "x": 0.28, "y": 1.69}]}, "GOO_3": {"rotate": [{"value": 4.3, "curve": [0.101, -0.46, 0.199, -4.92]}, {"time": 0.2667, "value": -4.92, "curve": [0.417, -4.92, 0.717, 15.31]}, {"time": 0.8667, "value": 15.31, "curve": [0.941, 15.31, 1.055, 9.62]}, {"time": 1.1667, "value": 4.3}], "translate": [{"y": -6.01, "curve": [0.089, 0, 0.174, 0, 0.089, -8.13, 0.174, -9.95]}, {"time": 0.2333, "y": -9.95, "curve": [0.375, 0, 0.658, 0, 0.375, -9.95, 0.658, 0]}, {"time": 0.8, "curve": [0.89, 0, 1.033, 0, 0.89, 0, 1.033, -3.26]}, {"time": 1.1667, "y": -6.01}]}, "SLEEVE_LEFT": {"rotate": [{"value": -27.05, "curve": [0.142, -27.05, 0.425, 0]}, {"time": 0.5667, "curve": [0.717, 0, 1.017, -27.05]}, {"time": 1.1667, "value": -27.05}], "translate": [{"x": 11.1, "curve": [0.037, 12.88, 0.071, 13.99, 0.037, 0, 0.071, 0]}, {"time": 0.1, "x": 13.99, "curve": [0.242, 13.99, 0.525, -3.23, 0.242, 0, 0.525, 0]}, {"time": 0.6667, "x": -3.23, "curve": [0.788, -3.23, 1.008, 7.15, 0.788, 0, 1.008, 0]}, {"time": 1.1667, "x": 11.1}]}, "SLEEVE_RIGHT": {"rotate": [{"value": -21.61, "curve": [0.142, -21.61, 0.425, 0]}, {"time": 0.5667, "curve": [0.717, 0, 1.017, -21.61]}, {"time": 1.1667, "value": -21.61}], "translate": [{"x": 7.6, "curve": [0.14, 3.31, 0.328, -6.85, 0.14, 0, 0.328, 0]}, {"time": 0.4333, "x": -6.85, "curve": [0.583, -6.85, 0.883, 9.28, 0.583, 0, 0.883, 0]}, {"time": 1.0333, "x": 9.28, "curve": [1.071, 9.28, 1.117, 8.63, 1.071, 0, 1.117, 0]}, {"time": 1.1667, "x": 7.6}]}, "GOO_1": {"rotate": [{"value": -15.24, "curve": [0.102, -6.4, 0.217, 7.51]}, {"time": 0.3333, "value": 21.86, "curve": [0.483, 21.86, 0.783, -27.83]}, {"time": 0.9333, "value": -27.83, "curve": [1, -27.83, 1.079, -23.03]}, {"time": 1.1667, "value": -15.24}], "translate": [{"y": -4.63, "curve": [0.112, 0, 0.225, 0, 0.112, -7.22, 0.225, -9.95]}, {"time": 0.3, "y": -9.95, "curve": [0.45, 0, 0.75, 0, 0.45, -9.95, 0.75, 0]}, {"time": 0.9, "curve": [0.967, 0, 1.066, 0, 0.967, 0, 1.066, -2.26]}, {"time": 1.1667, "y": -4.63}]}, "CAPE_CORNER_RIGHT": {"rotate": [{"value": 30.04, "curve": [0.026, 30.04, 0.062, 25.01]}, {"time": 0.1, "value": 18.99, "curve": [0.173, 10.55, 0.251, 0]}, {"time": 0.3, "curve": [0.367, 0, 0.5, 30.04]}, {"time": 0.5667, "value": 30.04, "curve": [0.65, 30.04, 0.817, 0]}, {"time": 0.9, "curve": [0.967, 0, 1.1, 30.04]}, {"time": 1.1667, "value": 30.04}], "translate": [{"x": 6.97, "y": -2.09, "curve": [0.038, 3.22, 0.074, 0, 0.038, -0.96, 0.074, 0]}, {"time": 0.1, "curve": [0.183, 0, 0.35, 26.66, 0.183, 0, 0.35, -5.04]}, {"time": 0.4333, "x": 26.66, "y": -5.04, "curve": [0.5, 26.66, 0.633, 0, 0.5, -5.04, 0.633, 0]}, {"time": 0.7, "curve": [0.775, 0, 0.925, 17.87, 0.775, 0, 0.925, -5.36]}, {"time": 1, "x": 17.87, "y": -5.36, "curve": [1.041, 17.87, 1.106, 11.93, 1.041, -5.36, 1.106, -3.58]}, {"time": 1.1667, "x": 6.97, "y": -2.09}]}, "GOO": {"translate": [{"curve": [0.142, 0, 0.425, 5.23, 0.142, 0, 0.425, 1.01]}, {"time": 0.5667, "x": 5.23, "y": 1.01, "curve": [0.717, 5.23, 1.017, 0, 0.717, 1.01, 1.017, 0]}, {"time": 1.1667}]}, "NECKLACE_HANDLE": {"translate": [{"y": 0.34, "curve": [0.111, -0.05, 0.226, -0.11, 0.111, -4.94, 0.226, -10.59]}, {"time": 0.3, "x": -0.11, "y": -10.59, "curve": [0.45, -0.11, 0.75, 0.1, 0.45, -10.59, 0.75, 9.5]}, {"time": 0.9, "x": 0.1, "y": 9.5, "curve": [0.967, 0.1, 1.066, 0.05, 0.967, 9.5, 1.066, 5.06]}, {"time": 1.1667, "y": 0.34}]}, "Necklace2": {"translate": [{"x": 1.89, "y": 11.25, "curve": [0.15, 1.27, 0.353, -0.29, 0.15, 7.58, 0.353, -1.6]}, {"time": 0.4667, "x": -0.29, "y": -1.6, "curve": [0.608, -0.29, 0.892, 2.22, 0.608, -1.6, 0.892, 13.18]}, {"time": 1.0333, "x": 2.22, "y": 13.18, "curve": [1.071, 2.22, 1.117, 2.09, 1.071, 13.18, 1.117, 12.44]}, {"time": 1.1667, "x": 1.89, "y": 11.25}]}, "HEAD_TOP": {"rotate": [{"value": -4.72, "curve": [0.101, -6.77, 0.199, -8.68]}, {"time": 0.2667, "value": -8.68, "curve": [0.408, -8.68, 0.692, 0]}, {"time": 0.8333, "curve": [0.916, 0, 1.043, -2.44]}, {"time": 1.1667, "value": -4.72}]}}}, "sleeping": {"slots": {"GoatCult/Hand1": {"attachment": [{"name": "Hand5"}]}, "GoatCult/Hand2": {"attachment": [{"name": "Hand5"}]}, "GoatCult/Mask": {"attachment": [{}]}}, "bones": {"HAND_LEFT": {"rotate": [{"value": 13.24, "curve": [0.333, 13.24, 1, 25.98]}, {"time": 1.3333, "value": 25.98, "curve": [1.667, 25.98, 2.333, 13.24]}, {"time": 2.6667, "value": 13.24}]}, "HAND_RIGHT": {"rotate": [{"value": -7.2, "curve": [0.333, -7.2, 1, -15.88]}, {"time": 1.3333, "value": -15.88, "curve": [1.667, -15.88, 2.333, -7.2]}, {"time": 2.6667, "value": -7.2}]}, "HEAD": {"rotate": [{"value": 0.12, "curve": [0.239, -3.42, 0.474, -6.74]}, {"time": 0.6333, "value": -6.74, "curve": [0.967, -6.74, 1.633, 8.31]}, {"time": 1.9667, "value": 8.31, "curve": [2.14, 8.31, 2.407, 4.07]}, {"time": 2.6667, "value": 0.12}], "translate": [{"x": -34.46, "y": 12.52, "curve": [0.333, -34.46, 1, -40.55, 0.333, 12.52, 1, 18.55]}, {"time": 1.3333, "x": -40.55, "y": 18.55, "curve": [1.667, -40.55, 2.333, -34.46, 1.667, 18.55, 2.333, 12.52]}, {"time": 2.6667, "x": -34.46, "y": 12.52}]}, "BODY_BTM": {"rotate": [{"value": -1.67}], "translate": [{"x": 10.13, "y": -73.27}]}, "GOO_1": {"rotate": [{"value": 1.11}], "translate": [{"x": 31.57, "y": 3.6}]}, "GOO_2": {"rotate": [{"value": 36.63}], "translate": [{"x": 6.37, "y": -15.22}]}, "GOO_3": {"rotate": [{"value": -8.06}], "translate": [{"x": 5.61, "y": -10.99}]}, "NECKLACE_HANDLE": {"translate": [{"x": 13.01, "y": 11.75, "curve": [0.25, 12.22, 0.5, 11.42, 0.25, 13.02, 0.5, 14.28]}, {"time": 0.6667, "x": 11.42, "y": 14.28, "curve": [1, 11.42, 1.667, 14.6, 1, 14.28, 1.667, 9.21]}, {"time": 2, "x": 14.6, "y": 9.21, "curve": [2.167, 14.6, 2.417, 13.81, 2.167, 9.21, 2.417, 10.48]}, {"time": 2.6667, "x": 13.01, "y": 11.75}]}, "NECKLACE": {"translate": [{"x": -10.17, "y": 7.67}]}, "ARM_LEFT_HANDLE": {"translate": [{"x": -41, "y": -31.46, "curve": [0.333, -41, 1, -35.01, 0.333, -31.46, 1, -35.14]}, {"time": 1.3333, "x": -35.01, "y": -35.14, "curve": [1.667, -35.01, 2.333, -41, 1.667, -35.14, 2.333, -31.46]}, {"time": 2.6667, "x": -41, "y": -31.46}]}, "ARM_RIGHT_HANDLE": {"translate": [{"x": -102.17, "y": 47.59, "curve": [0.333, -102.17, 1, -94.07, 0.333, 47.59, 1, 41.76]}, {"time": 1.3333, "x": -94.07, "y": 41.76, "curve": [1.667, -94.07, 2.333, -102.17, 1.667, 41.76, 2.333, 47.59]}, {"time": 2.6667, "x": -102.17, "y": 47.59}]}, "CAPE_CORNER_RIGHT": {"rotate": [{"value": 30.04}], "translate": [{"x": -6.81, "y": -10.68}]}, "CAPE_CORNER_LEFT": {"rotate": [{"value": -8.13}], "translate": [{"x": -5.72, "y": -3.41}]}, "SLEEVE_LEFT": {"translate": [{"x": 2.42, "y": -12.11, "curve": [0.468, -1.89, 0.914, -5.38, 0.468, -13.62, 0.914, -14.85]}, {"time": 1.3333, "x": -7.8, "y": -15.69, "curve": [1.812, -1.22, 2.259, 2.42, 1.812, -13.38, 2.259, -12.11]}, {"time": 2.6667, "x": 2.42, "y": -12.11}]}, "SLEEVE_RIGHT": {"translate": [{"x": 4.1, "y": -15.03}]}, "FACE": {"translate": [{"x": -18.55, "y": 5.93}], "scale": [{"x": 0.632}]}, "BODY_HANDLE": {"translate": [{"x": -33.39, "y": 1.2}]}, "BODY_TOP": {"rotate": [{"value": 5.9}], "translate": [{"curve": [0.333, 0, 1, -10.01, 0.333, 0, 1, 3.48]}, {"time": 1.3333, "x": -10.01, "y": 3.48, "curve": [1.667, -10.01, 2.333, 0, 1.667, 3.48, 2.333, 0]}, {"time": 2.6667}]}, "SHOULDER_RIGHT": {"translate": [{"x": -72.78, "y": -22.96, "curve": [0.333, -72.78, 1, -69.26, 0.333, -22.96, 1, -21.65]}, {"time": 1.3333, "x": -69.26, "y": -21.65, "curve": [1.667, -69.26, 2.333, -72.78, 1.667, -21.65, 2.333, -22.96]}, {"time": 2.6667, "x": -72.78, "y": -22.96}]}, "NECK3": {"translate": [{"x": -9.13, "y": 10.18}]}, "Necklace3": {"translate": [{"x": -8.56, "y": 17.56}]}, "SHOULDER_LEFT": {"translate": [{"x": -15.65, "y": 1.57}]}, "Necklace2": {"translate": [{"x": -13.27, "y": 26.65}]}, "HEAD_TOP": {"rotate": [{"value": -4.72, "curve": [0.113, -6.77, 0.224, -8.68]}, {"time": 0.3, "value": -8.68, "curve": [0.467, -8.68, 0.8, 0]}, {"time": 0.9667, "curve": [1.058, 0, 1.197, -2.44]}, {"time": 1.3333, "value": -4.72, "curve": [1.459, -6.77, 1.583, -8.68]}, {"time": 1.6667, "value": -8.68, "curve": [1.833, -8.68, 2.167, 0]}, {"time": 2.3333, "curve": [2.416, 0, 2.543, -2.44]}, {"time": 2.6667, "value": -4.72}]}}, "ik": {"ARM_RIGHT_HANDLE": [{"bendPositive": false}]}, "drawOrder": [{"offsets": [{"slot": "GoatCult/Arm2", "offset": 14}, {"slot": "GoatCult/Sleeve2", "offset": 11}, {"slot": "GoatCult/Hand2", "offset": 11}]}]}, "spawn-in2": {"bones": {"NECKLACE_HANDLE": {"translate": [{"curve": [0.028, -25.35, 0.114, -37.5, 0.028, -2.41, 0.114, -3.56]}, {"time": 0.1667, "x": -37.5, "y": -3.56, "curve": [0.233, -37.5, 0.367, 15.19, 0.233, -3.56, 0.367, -0.88]}, {"time": 0.4333, "x": 15.19, "y": -0.88, "curve": [0.485, 15.19, 0.557, 12.65, 0.485, -0.88, 0.557, -0.73]}, {"time": 0.6333, "x": 9.61, "y": -0.56}]}, "HAND_LEFT": {"rotate": [{"time": 0.2, "value": -3.76, "curve": [0.296, -6.75, 0.401, -10.56]}, {"time": 0.4667, "value": -10.56, "curve": [0.51, -10.56, 0.57, -9.16]}, {"time": 0.6333, "value": -7.34}]}, "root": {"rotate": [{"value": -0.11}]}, "spawn_particles2": {"translate": [{"y": -19.52, "curve": [0.11, 0, 0.456, 0, 0.11, 9.65, 0.456, 23.63]}, {"time": 0.6667, "y": 23.63}]}, "FACE": {"translate": [{"x": -294.55, "y": 11.5, "curve": [0.024, -160.75, 0.077, -66.78, 0.024, 8.04, 0.077, 5.6]}, {"time": 0.1333, "x": -10.81, "y": 4.15, "curve": [0.157, -5.07, 0.181, -2.26, 0.157, 0.45, 0.181, -1.37]}, {"time": 0.2, "x": -2.26, "y": -1.37, "curve": [0.297, 8.86, 0.401, 22.44, 0.297, -4.13, 0.401, -7.5]}, {"time": 0.4667, "x": 22.44, "y": -7.5, "curve": [0.518, 22.44, 0.59, 12.87, 0.518, -7.5, 0.59, -6.65]}, {"time": 0.6667, "x": 1.37, "y": -5.61}]}, "BODY_TOP": {"rotate": [{"value": 23.84, "curve": [0.028, 3.89, 0.114, -5.68]}, {"time": 0.1667, "value": -5.68}]}, "BODY_BTM": {"rotate": [{"value": -8.53, "curve": [0.028, -2.76, 0.114, 0]}, {"time": 0.1667, "curve": [0.233, 0, 0.367, -2.99]}, {"time": 0.4333, "value": -2.99, "curve": [0.492, -2.99, 0.579, -2.33]}, {"time": 0.6667, "value": -1.67}], "translate": [{"x": 0.46, "y": -25.95, "curve": [0.028, -0.58, 0.114, -1.08, 0.028, 41.14, 0.114, 73.29]}, {"time": 0.1667, "x": -1.08, "y": 73.29, "curve": [0.233, -1.08, 0.367, 1.76, 0.233, 73.29, 0.367, -15.31]}, {"time": 0.4333, "x": 1.76, "y": -15.31, "curve": [0.492, 1.76, 0.608, 0, 0.492, -15.31, 0.608, 0]}, {"time": 0.6667}], "scale": [{"x": 0.082, "curve": [0.028, 0.869, 0.114, 1.246, 0.028, 0.872, 0.114, 0.811]}, {"time": 0.1667, "x": 1.246, "y": 0.811, "curve": [0.233, 1.246, 0.367, 0.746, 0.233, 0.811, 0.367, 1.089]}, {"time": 0.4333, "x": 0.746, "y": 1.089, "curve": [0.492, 0.746, 0.608, 1, 0.492, 1.089, 0.608, 1]}, {"time": 0.6667}]}, "CAPE_CORNER_RIGHT": {"rotate": [{"time": 0.2, "value": 30.04, "curve": [0.258, 30.04, 0.375, 0]}, {"time": 0.4333, "curve": [0.492, 0, 0.608, 30.04]}, {"time": 0.6667, "value": 30.04}], "translate": [{"curve": [0.05, 0, 0.15, 5.31, 0.05, 0, 0.15, 29.02]}, {"time": 0.2, "x": 5.31, "y": 29.02, "curve": [0.315, 12.35, 0.422, 17.87, 0.315, 9.77, 0.422, -5.36]}, {"time": 0.5, "x": 17.87, "y": -5.36, "curve": [0.533, 17.87, 0.585, 11.93, 0.533, -5.36, 0.585, -3.58]}, {"time": 0.6333, "x": 6.97, "y": -2.09}]}, "HEAD": {"rotate": [{"value": 23.66, "curve": [0.028, 7.66, 0.114, 0]}, {"time": 0.1667, "curve": [0.175, 0, 0.192, 1.8]}, {"time": 0.2, "value": 1.8, "curve": [0.275, 0.9, 0.35, 0]}, {"time": 0.4, "curve": [0.464, 0, 0.545, 0.53]}, {"time": 0.6333, "value": 1.32}], "translate": [{"x": -205.63, "y": 74.76, "curve": [0.028, -58.23, 0.114, 12.4, 0.028, 23.97, 0.114, -0.36]}, {"time": 0.1667, "x": 12.4, "y": -0.36, "curve": [0.175, 12.4, 0.192, 2.43, 0.175, -0.36, 0.192, 0.11]}, {"time": 0.2, "x": 2.43, "y": 0.11, "curve": [0.308, 2.43, 0.525, -10.67, 0.308, 0.11, 0.525, -0.35]}, {"time": 0.6333, "x": -10.67, "y": -0.35}]}, "spawn_particles": {"translate": [{"y": 5.14, "curve": [0.033, 0, 0.137, 0, 0.033, 16.25, 0.137, 21.57]}, {"time": 0.2, "y": 21.57}]}, "effects": {"translate": [{"x": -322.82, "y": -42.44}]}, "ARM_RIGHT_HANDLE": {"translate": [{"x": -48.54, "y": 36.61, "curve": [0.017, 52.11, 0.068, 100.33, 0.017, 6.03, 0.068, -8.63]}, {"time": 0.1, "x": 100.33, "y": -8.63, "curve": [0.242, 100.33, 0.525, 2.39, 0.242, -8.63, 0.525, 0.21]}, {"time": 0.6667, "x": 2.39, "y": 0.21}]}, "CAPE_CORNER_LEFT": {"rotate": [{"time": 0.2, "value": -7.15, "curve": [0.238, -3.42, 0.275, 0]}, {"time": 0.3, "curve": [0.367, 0, 0.5, -16.25]}, {"time": 0.5667, "value": -16.25, "curve": [0.592, -16.25, 0.629, -12.19]}, {"time": 0.6667, "value": -8.13}], "translate": [{"time": 0.2, "curve": [0.258, 0, 0.375, 12.36, 0.258, 0, 0.375, 14.88]}, {"time": 0.4333, "x": 12.36, "y": 14.88, "curve": [0.492, 12.36, 0.608, 0, 0.492, 14.88, 0.608, 0]}, {"time": 0.6667}]}, "GOO_1": {"rotate": [{"value": 1.11, "curve": [0.074, 1.27, 0.15, 1.43]}, {"time": 0.2, "value": 1.43, "curve": [0.299, 0.11, 0.4, -1.28]}, {"time": 0.4667, "value": -1.28, "curve": [0.517, -1.28, 0.591, -0.11]}, {"time": 0.6667, "value": 1.11}], "translate": [{"time": 0.0333, "curve": [0.039, 1.19, 0.058, 1.84, 0.039, 17, 0.058, 26.23]}, {"time": 0.0667, "x": 1.84, "y": 26.23, "curve": [0.1, 1.84, 0.167, 0, 0.1, 26.23, 0.167, -5.26]}, {"time": 0.2, "y": -5.26, "curve": [0.299, 0, 0.4, 0, 0.299, -2.69, 0.4, 0]}, {"time": 0.4667, "curve": [0.517, 0, 0.591, 0, 0.517, 0, 0.591, -2.26]}, {"time": 0.6667, "y": -4.63}]}, "GOO_3": {"rotate": [{"value": 1.82, "curve": [0.076, 1.14, 0.149, 0.55]}, {"time": 0.2, "value": 0.55, "curve": [0.276, -0.45, 0.348, -1.28]}, {"time": 0.4, "value": -1.28, "curve": [0.466, -1.28, 0.569, 0.4]}, {"time": 0.6667, "value": 1.82}], "translate": [{"time": 0.0333, "curve": [0.039, 0, 0.058, 0, 0.039, 20.99, 0.058, 32.4]}, {"time": 0.0667, "y": 32.4, "curve": [0.1, 0, 0.167, 0, 0.1, 32.4, 0.167, -3.55]}, {"time": 0.2, "y": -3.55, "curve": [0.276, 0, 0.348, 0, 0.276, -1.6, 0.348, 0]}, {"time": 0.4, "curve": [0.466, 0, 0.569, 0, 0.466, 0, 0.569, -3.26]}, {"time": 0.6667, "y": -6.01}]}, "GOO_2": {"rotate": [{"value": 3.32, "curve": [0.074, 0.9, 0.142, -0.56]}, {"time": 0.2, "value": -0.56, "curve": [0.238, -1, 0.272, -1.28]}, {"time": 0.3, "value": -1.28, "curve": [0.389, -1.28, 0.552, 2.14]}, {"time": 0.6667, "value": 3.32}], "translate": [{"time": 0.0333, "x": -5.59, "y": 13.68, "curve": [0.039, -1.97, 0.058, 0, 0.039, -0.62, 0.058, -8.39]}, {"time": 0.0667, "y": -8.39, "curve": [0.1, 0, 0.167, 0.17, 0.1, -8.39, 0.167, 6.06]}, {"time": 0.2, "x": 0.17, "y": 6.06, "curve": [0.238, 0.18, 0.272, 0.19, 0.238, 7.65, 0.272, 8.65]}, {"time": 0.3, "x": 0.19, "y": 8.65, "curve": [0.389, 0.19, 0.552, 0.06, 0.389, 8.65, 0.552, -3.74]}, {"time": 0.6667, "x": 0.02, "y": -8.05}]}, "ARM_LEFT_HANDLE": {"translate": [{"x": -44.39, "y": -49.45, "curve": [0.028, 50.26, 0.114, 95.61, 0.028, -5, 0.114, 16.29]}, {"time": 0.1667, "x": 95.61, "y": 16.29, "curve": [0.292, 95.61, 0.542, 5.01, 0.292, 16.29, 0.542, 0.59]}, {"time": 0.6667, "x": 5.01, "y": 0.59}]}, "SLEEVE_LEFT": {"translate": [{"time": 0.2, "x": -1.9, "curve": [0.224, -2.74, 0.247, -3.23, 0.224, 0, 0.247, 0]}, {"time": 0.2667, "x": -3.23, "curve": [0.364, -3.23, 0.54, 7.15, 0.364, 0, 0.54, 0]}, {"time": 0.6667, "x": 11.1}]}, "HAND_RIGHT": {"rotate": [{"time": 0.2, "value": -5.66, "curve": [0.285, -9.82, 0.376, -14.9]}, {"time": 0.4333, "value": -14.9, "curve": [0.501, -12.19, 0.569, -9.48]}, {"time": 0.6333, "value": -7.2}]}, "SHOULDER_RIGHT": {"rotate": [{"time": 0.2, "value": -43.34}]}, "SLEEVE_RIGHT": {"translate": [{"time": 0.2, "x": -3.88, "curve": [0.322, 0.51, 0.478, 9.28, 0.322, 0, 0.478, 0]}, {"time": 0.5667, "x": 9.28, "curve": [0.595, 9.28, 0.629, 8.63, 0.595, 0, 0.629, 0]}, {"time": 0.6667, "x": 7.6}]}, "ARM_RIGHT": {"rotate": [{"time": 0.2, "value": -79.49}]}, "NECKLACE": {"translate": [{"time": 0.2, "x": 3.65, "y": 2.07}]}, "NECK2": {"rotate": [{"time": 0.2, "value": 8.62}]}, "ARM_LEFT": {"rotate": [{"time": 0.2, "value": -74.74}]}, "SHOULDER_LEFT": {"rotate": [{"time": 0.2, "value": 70.23}]}, "NECK1": {"rotate": [{"time": 0.2, "value": 1.9}]}}, "drawOrder": [{"offsets": [{"slot": "Other/SpawnParticles", "offset": 26}, {"slot": "Other/SpawnParticles2", "offset": 26}, {"slot": "Other/SpawnParticles3", "offset": 26}]}]}, "stunned": {"slots": {"GoatCult/Hand1": {"attachment": [{"name": "Hand5"}]}, "GoatCult/Hand2": {"attachment": [{"name": "Hand5"}]}}, "bones": {"HAND_LEFT": {"rotate": [{"value": 13.24, "curve": [0.125, 13.24, 0.375, 25.98]}, {"time": 0.5, "value": 25.98, "curve": [0.625, 25.98, 0.875, 13.24]}, {"time": 1, "value": 13.24, "curve": [1.125, 13.24, 1.375, 25.98]}, {"time": 1.5, "value": 25.98, "curve": [1.625, 25.98, 1.875, 13.24]}, {"time": 2, "value": 13.24}]}, "HAND_RIGHT": {"rotate": [{"value": -7.2, "curve": [0.125, -7.2, 0.375, -15.88]}, {"time": 0.5, "value": -15.88, "curve": [0.625, -15.88, 0.875, -7.2]}, {"time": 1, "value": -7.2, "curve": [1.125, -7.2, 1.375, -15.88]}, {"time": 1.5, "value": -15.88, "curve": [1.625, -15.88, 1.875, -7.2]}, {"time": 2, "value": -7.2}]}, "BODY_BTM": {"rotate": [{"value": -1.67}], "translate": [{"x": 10.13, "y": -73.27}], "scale": [{"curve": [0.004, 0.719, 0.05, 0.592, 0.004, 1.08, 0.05, 1.116]}, {"time": 0.0667, "x": 0.592, "y": 1.116, "curve": [0.112, 0.646, 0.173, 0.862, 0.112, 1.096, 0.173, 1.012]}, {"time": 0.2333, "x": 1.078, "y": 0.928, "curve": [0.333, 1.039, 0.433, 1, 0.333, 0.964, 0.433, 1]}, {"time": 0.5}]}, "GOO_1": {"rotate": [{"value": 1.11}], "translate": [{"x": 31.57, "y": 3.6}]}, "GOO_2": {"rotate": [{"value": 36.63}], "translate": [{"x": 6.37, "y": -15.22}]}, "GOO_3": {"rotate": [{"value": -8.06}], "translate": [{"x": 5.61, "y": -10.99}]}, "NECKLACE_HANDLE": {"translate": [{"x": 16, "y": 17.06, "curve": [0.017, 16, 0.05, 47.41, 0.017, 17.06, 0.05, 73.31]}, {"time": 0.0667, "x": 47.41, "y": 73.31, "curve": [0.125, 47.41, 0.242, 4.21, 0.125, 73.31, 0.242, 13.25]}, {"time": 0.3, "x": 4.21, "y": 13.25, "curve": [0.392, 4.21, 0.575, 20.19, 0.392, 13.25, 0.575, 38.18]}, {"time": 0.6667, "x": 20.19, "y": 38.18, "curve": [0.75, 20.19, 0.917, 16.72, 0.75, 38.18, 0.917, 25.96]}, {"time": 1, "x": 16.72, "y": 25.96, "curve": [1.125, 16.72, 1.375, 24.8, 1.125, 25.96, 1.375, 34.22]}, {"time": 1.5, "x": 24.8, "y": 34.22, "curve": [1.625, 24.8, 1.875, 16.72, 1.625, 34.22, 1.875, 25.96]}, {"time": 2, "x": 16.72, "y": 25.96}]}, "NECKLACE": {"translate": [{"x": -15.75, "y": 12.4}]}, "ARM_LEFT_HANDLE": {"translate": [{"x": -15.42, "y": -0.12, "curve": [0.125, -15.42, 0.375, -12.65, 0.125, -0.12, 0.375, -8.39]}, {"time": 0.5, "x": -12.65, "y": -8.39, "curve": [0.625, -12.65, 0.875, -15.42, 0.625, -8.39, 0.875, -0.12]}, {"time": 1, "x": -15.42, "y": -0.12, "curve": [1.125, -15.42, 1.375, -12.65, 1.125, -0.12, 1.375, -8.39]}, {"time": 1.5, "x": -12.65, "y": -8.39, "curve": [1.625, -12.65, 1.875, -15.42, 1.625, -8.39, 1.875, -0.12]}, {"time": 2, "x": -15.42, "y": -0.12}]}, "ARM_RIGHT_HANDLE": {"translate": [{"x": -101.62, "y": 104.08, "curve": [0.125, -101.62, 0.375, -99.78, 0.125, 104.08, 0.375, 94.39]}, {"time": 0.5, "x": -99.78, "y": 94.39, "curve": [0.625, -99.78, 0.875, -101.62, 0.625, 94.39, 0.875, 104.08]}, {"time": 1, "x": -101.62, "y": 104.08, "curve": [1.125, -101.62, 1.375, -99.78, 1.125, 104.08, 1.375, 94.39]}, {"time": 1.5, "x": -99.78, "y": 94.39, "curve": [1.625, -99.78, 1.875, -101.62, 1.625, 94.39, 1.875, 104.08]}, {"time": 2, "x": -101.62, "y": 104.08}]}, "CAPE_CORNER_RIGHT": {"rotate": [{"value": 30.04}], "translate": [{"x": -6.81, "y": -10.68, "curve": [0.022, -12.77, 0.045, -18.67, 0.022, -8.34, 0.045, -6.03]}, {"time": 0.0667, "x": -24.49, "y": -3.74, "curve": [0.1, -18.47, 0.134, -12.56, 0.1, -6.11, 0.134, -8.42]}, {"time": 0.1667, "x": -6.81, "y": -10.68}]}, "CAPE_CORNER_LEFT": {"rotate": [{"value": -8.13}], "translate": [{"x": -5.72, "y": -3.41}]}, "SLEEVE_LEFT": {"translate": [{"x": 2.42, "y": -12.11, "curve": [0.175, -1.89, 0.343, -5.38, 0.175, -13.62, 0.343, -14.85]}, {"time": 0.5, "x": -7.8, "y": -15.69, "curve": [0.679, -1.22, 0.847, 2.42, 0.679, -13.38, 0.847, -12.11]}, {"time": 1, "x": 2.42, "y": -12.11, "curve": [1.175, -1.89, 1.343, -5.38, 1.175, -13.62, 1.343, -14.85]}, {"time": 1.5, "x": -7.8, "y": -15.69, "curve": [1.679, -1.22, 1.847, 2.42, 1.679, -13.38, 1.847, -12.11]}, {"time": 2, "x": 2.42, "y": -12.11}]}, "SLEEVE_RIGHT": {"translate": [{"x": 4.1, "y": -15.03}]}, "HORN_R": {"rotate": [{"curve": [0.021, 0, 0.043, 10.43]}, {"time": 0.0667, "value": 29.9, "curve": [0.134, 21.06, 0.214, -2.17]}, {"time": 0.3, "value": -32.03, "curve": [0.541, -20.02, 0.83, 0]}, {"time": 1}]}, "HEAD": {"rotate": [{"value": 8.18, "curve": [0.016, 29.62, 0.174, 34.59]}, {"time": 0.2333, "value": 34.59, "curve": [0.358, 34.59, 0.608, 16.36]}, {"time": 0.7333, "value": 16.36, "curve": [0.799, 16.36, 0.901, 12.13]}, {"time": 1, "value": 8.18, "curve": [1.088, 4.64, 1.174, 1.32]}, {"time": 1.2333, "value": 1.32, "curve": [1.358, 1.32, 1.608, 16.36]}, {"time": 1.7333, "value": 16.36, "curve": [1.799, 16.36, 1.901, 12.13]}, {"time": 2, "value": 8.18}], "translate": [{"x": -45.03, "y": 21.2, "curve": [0.125, -45.03, 0.375, -40.55, 0.125, 21.2, 0.375, 18.55]}, {"time": 0.5, "x": -40.55, "y": 18.55, "curve": [0.625, -40.55, 0.875, -34.46, 0.625, 18.55, 0.875, 12.52]}, {"time": 1, "x": -34.46, "y": 12.52, "curve": [1.125, -34.46, 1.375, -40.55, 1.125, 12.52, 1.375, 18.55]}, {"time": 1.5, "x": -40.55, "y": 18.55, "curve": [1.625, -40.55, 1.875, -34.46, 1.625, 18.55, 1.875, 12.52]}, {"time": 2, "x": -34.46, "y": 12.52}]}, "FACE": {"translate": [{"x": 32.64, "y": -1.91, "curve": [0.025, 32.64, 0.075, -18.55, 0.025, -1.91, 0.075, 5.93]}, {"time": 0.1, "x": -18.55, "y": 5.93}], "scale": [{"curve": [0.025, 1, 0.075, 0.632, 0.025, 1, 0.075, 1]}, {"time": 0.1, "x": 0.632}]}, "BODY_HANDLE": {"translate": [{"x": -33.39, "y": 1.2}]}, "BODY_TOP": {"rotate": [{"value": 23.21}], "translate": [{"curve": [0.125, 0, 0.375, -10.01, 0.125, 0, 0.375, 3.48]}, {"time": 0.5, "x": -10.01, "y": 3.48, "curve": [0.625, -10.01, 0.875, 0, 0.625, 3.48, 0.875, 0]}, {"time": 1, "curve": [1.125, 0, 1.375, -10.01, 1.125, 0, 1.375, 3.48]}, {"time": 1.5, "x": -10.01, "y": 3.48, "curve": [1.625, -10.01, 1.875, 0, 1.625, 3.48, 1.875, 0]}, {"time": 2}]}, "SHOULDER_RIGHT": {"translate": [{"x": -88.54, "y": -12.82}]}, "NECK3": {"translate": [{"x": -9.13, "y": 10.18}]}, "Necklace3": {"translate": [{"x": -8.56, "y": 17.56}]}, "HEAD_TOP": {"rotate": [{"curve": [0.025, 0, 0.075, 25.29]}, {"time": 0.1, "value": 25.29, "curve": [0.125, 25.29, 0.175, -34.67]}, {"time": 0.2, "value": -34.67, "curve": [0.25, -34.67, 0.35, 0]}, {"time": 0.4, "curve": [0.467, 0, 0.6, -13.16]}, {"time": 0.6667, "value": -13.16, "curve": [0.75, -13.16, 0.917, -4.72]}, {"time": 1, "value": -4.72, "curve": [1.058, -4.72, 1.175, -8.68]}, {"time": 1.2333, "value": -8.68, "curve": [1.358, -8.68, 1.608, 0]}, {"time": 1.7333, "curve": [1.799, 0, 1.901, -2.44]}, {"time": 2, "value": -4.72}]}, "Necklace2": {"translate": [{"curve": [0.017, 0, 0.05, -6.92, 0.017, 0, 0.05, 55.37]}, {"time": 0.0667, "x": -6.92, "y": 55.37, "curve": [0.125, -6.92, 0.242, -10.12, 0.125, 55.37, 0.242, -9.71]}, {"time": 0.3, "x": -10.12, "y": -9.71, "curve": [0.392, -10.12, 0.575, 12.36, 0.392, -9.71, 0.575, 47.36]}, {"time": 0.6667, "x": 12.36, "y": 47.36, "curve": [0.75, 12.36, 0.917, 16.72, 0.75, 47.36, 0.917, 25.96]}, {"time": 1, "x": 16.72, "y": 25.96, "curve": [1.125, 16.72, 1.375, 3.51, 1.125, 25.96, 1.375, 59.72]}, {"time": 1.5, "x": 3.51, "y": 59.72, "curve": [1.625, 3.51, 1.875, 16.72, 1.625, 59.72, 1.875, 25.96]}, {"time": 2, "x": 16.72, "y": 25.96}]}, "Necklace4": {"translate": [{"curve": [0.017, 0, 0.05, -6.92, 0.017, 0, 0.05, 55.37]}, {"time": 0.0667, "x": -6.92, "y": 55.37, "curve": [0.125, -6.92, 0.242, -11.79, 0.125, 55.37, 0.242, 2.44]}, {"time": 0.3, "x": -11.79, "y": 2.44, "curve": [0.392, -11.79, 0.575, 11.07, 0.392, 2.44, 0.575, 25.97]}, {"time": 0.6667, "x": 11.07, "y": 25.97, "curve": [0.75, 11.07, 0.917, -4.86, 0.75, 25.97, 0.917, -15.73]}, {"time": 1, "x": -4.86, "y": -15.73, "curve": [1.125, -4.86, 1.375, 7.94, 1.125, -15.73, 1.375, 15.03]}, {"time": 1.5, "x": 7.94, "y": 15.03, "curve": [1.625, 7.94, 1.875, 0, 1.625, 15.03, 1.875, 0]}, {"time": 2}]}, "SHOULDER_LEFT": {"translate": [{"x": -5.86, "y": 0.76}]}}, "ik": {"ARM_RIGHT_HANDLE": [{"bendPositive": false}]}, "drawOrder": [{"offsets": [{"slot": "GoatCult/Arm2", "offset": 14}, {"slot": "GoatCult/Sleeve2", "offset": 11}, {"slot": "GoatCult/Hand2", "offset": 11}]}]}, "stunnedOLD": {"slots": {"GoatCult/Hand1": {"attachment": [{"name": "Hand3"}]}, "GoatCult/Hand2": {"attachment": [{"name": "Hand3"}]}}, "bones": {"HAND_LEFT": {"rotate": [{"value": -7.34}]}, "HAND_RIGHT": {"rotate": [{"value": -7.2}]}, "HEAD": {"rotate": [{"value": 20.23, "curve": [0.092, 20.23, 0.275, 8.63]}, {"time": 0.3667, "value": 8.63, "curve": [0.559, 11.85, 0.821, 19.9]}, {"time": 0.9667, "value": 19.9}]}, "BODY_TOP": {"rotate": [{"value": -30.99, "curve": [0.158, -30.99, 0.475, 18.73]}, {"time": 0.6333, "value": 18.73, "curve": [0.717, 18.73, 0.883, -30.99]}, {"time": 0.9667, "value": -30.99}]}, "FACE": {"rotate": [{"value": 14.41}], "translate": [{"x": 97.87, "y": 19.72, "curve": [0.092, 97.87, 0.275, -33.19, 0.092, 19.72, 0.275, -28.41]}, {"time": 0.3667, "x": -33.19, "y": -28.41, "curve": [0.517, -33.19, 0.817, 97.87, 0.517, -28.41, 0.817, 19.72]}, {"time": 0.9667, "x": 97.87, "y": 19.72}]}, "SHOULDER_LEFT": {"translate": [{"curve": [0.092, 0, 0.275, 10.52, 0.092, 0, 0.275, -32.79]}, {"time": 0.3667, "x": 10.52, "y": -32.79, "curve": [0.517, 10.52, 0.817, 0, 0.517, -32.79, 0.817, 0]}, {"time": 0.9667}]}, "BODY_BTM": {"rotate": [{"value": 5.5, "curve": [0.125, 5.5, 0.375, -6.73]}, {"time": 0.5, "value": -6.73, "curve": [0.617, -6.73, 0.85, 5.5]}, {"time": 0.9667, "value": 5.5}]}, "GOO_1": {"rotate": [{"value": -23.54, "curve": [0.013, -18.49, 0.2, -17.34]}, {"time": 0.2667, "value": -17.34, "curve": [0.442, -17.34, 0.792, -23.54]}, {"time": 0.9667, "value": -23.54}], "translate": [{"x": 6.98, "y": 10.42, "curve": [0.013, 9.49, 0.2, 10.06, 0.013, 3.27, 0.2, 1.65]}, {"time": 0.2667, "x": 10.06, "y": 1.65, "curve": [0.442, 10.06, 0.792, 6.98, 0.442, 1.65, 0.792, 10.42]}, {"time": 0.9667, "x": 6.98, "y": 10.42}]}, "GOO_2": {"rotate": [{"value": -21.33, "curve": [0.013, -16.27, 0.2, -15.13]}, {"time": 0.2667, "value": -15.13, "curve": [0.442, -15.13, 0.792, -21.33]}, {"time": 0.9667, "value": -21.33}], "translate": [{"x": -9.26, "y": 5.29, "curve": [0.013, 7.05, 0.2, 10.75, 0.013, 10.6, 0.2, 11.81]}, {"time": 0.2667, "x": 10.75, "y": 11.81, "curve": [0.442, 10.75, 0.792, -9.26, 0.442, 11.81, 0.792, 5.29]}, {"time": 0.9667, "x": -9.26, "y": 5.29}]}, "GOO_3": {"rotate": [{"value": -22.83, "curve": [0.013, -17.78, 0.2, -16.63]}, {"time": 0.2667, "value": -16.63, "curve": [0.442, -16.63, 0.792, -22.83]}, {"time": 0.9667, "value": -22.83}], "translate": [{"x": -8.93, "y": 7.56, "curve": [0.013, -15.21, 0.2, -16.63, 0.013, 29.88, 0.2, 34.94]}, {"time": 0.2667, "x": -16.63, "y": 34.94, "curve": [0.442, -16.63, 0.792, -8.93, 0.442, 34.94, 0.792, 7.56]}, {"time": 0.9667, "x": -8.93, "y": 7.56}]}, "NECKLACE_HANDLE": {"translate": [{"x": 27.61, "y": -12.6, "curve": [0.158, 27.61, 0.475, 23.45, 0.158, -12.6, 0.475, 30.37]}, {"time": 0.6333, "x": 23.45, "y": 30.37, "curve": [0.717, 23.45, 0.883, 27.61, 0.717, 30.37, 0.883, -12.6]}, {"time": 0.9667, "x": 27.61, "y": -12.6}]}, "ARM_LEFT_HANDLE": {"translate": [{"x": -4.7, "y": 47.82, "curve": [0.067, -4.7, 0.2, 73.75, 0.067, 47.82, 0.2, -20.38]}, {"time": 0.2667, "x": 73.75, "y": -20.38, "curve": [0.442, 73.75, 0.792, -4.7, 0.442, -20.38, 0.792, 47.82]}, {"time": 0.9667, "x": -4.7, "y": 47.82}]}, "ARM_RIGHT_HANDLE": {"translate": [{"x": 37, "y": 7.5, "curve": [0.068, 37, 0.166, 30.79, 0.068, 7.5, 0.166, -11.1]}, {"time": 0.2667, "x": 24, "y": -31.39, "curve": [0.442, 24, 0.792, 37, 0.442, -31.39, 0.792, 7.5]}, {"time": 0.9667, "x": 37, "y": 7.5}]}, "CAPE_CORNER_RIGHT": {"rotate": [{"value": 21.87}], "translate": [{"x": -26.35, "y": 31.58}]}, "CAPE_CORNER_LEFT": {"rotate": [{"value": 37.3}], "translate": [{"x": -2.99, "y": -4.92}]}, "SLEEVE_LEFT": {"translate": [{"x": 11.1}]}, "SLEEVE_RIGHT": {"translate": [{"x": 7.6}]}, "HORN_R": {"rotate": [{"value": 30.79, "curve": [0.151, 30.52, 0.742, 30.46]}, {"time": 0.9667, "value": 30.46}]}, "HORN_L": {"rotate": [{"value": -21.64}]}, "root": {"translate": [{"x": -24.82, "curve": [0.067, -24.82, 0.2, 0, 0.067, 0, 0.2, 0]}, {"time": 0.2667, "curve": [0.442, 0, 0.792, -24.82, 0.442, 0, 0.792, 0]}, {"time": 0.9667, "x": -24.82}]}, "HORN_LEFT": {"rotate": [{"value": 14.41}]}, "WEAPON": {"rotate": [{"value": 14.41}]}, "HORN_RIGHT": {"rotate": [{"value": 0.34}]}}}, "summon": {"slots": {"Cape": {"rgba2": [{"time": 1.3, "light": "ffffffff", "dark": "c55920", "curve": [1.467, 1, 1.8, 1, 1.467, 1, 1.8, 1, 1.467, 1, 1.8, 1, 1.467, 1, 1.8, 1, 1.467, 0.77, 1.8, 0, 1.467, 0.35, 1.8, 0, 1.467, 0.13, 1.8, 0]}, {"time": 1.9667, "light": "ffffffff", "dark": "000000"}]}, "Cape_Back": {"rgba2": [{"time": 1.3, "light": "ffffffff", "dark": "c55920", "curve": [1.467, 1, 1.8, 1, 1.467, 1, 1.8, 1, 1.467, 1, 1.8, 1, 1.467, 1, 1.8, 1, 1.467, 0.77, 1.8, 0, 1.467, 0.35, 1.8, 0, 1.467, 0.13, 1.8, 0]}, {"time": 1.9667, "light": "ffffffff", "dark": "000000"}]}, "GoatCult/Arm": {"rgba2": [{"time": 1.3, "light": "ffffffff", "dark": "c55920", "curve": [1.467, 1, 1.8, 1, 1.467, 1, 1.8, 1, 1.467, 1, 1.8, 1, 1.467, 1, 1.8, 1, 1.467, 0.77, 1.8, 0, 1.467, 0.35, 1.8, 0, 1.467, 0.13, 1.8, 0]}, {"time": 1.9667, "light": "ffffffff", "dark": "000000"}]}, "GoatCult/Arm2": {"rgba2": [{"time": 1.3, "light": "ffffffff", "dark": "c55920", "curve": [1.467, 1, 1.8, 1, 1.467, 1, 1.8, 1, 1.467, 1, 1.8, 1, 1.467, 1, 1.8, 1, 1.467, 0.77, 1.8, 0, 1.467, 0.35, 1.8, 0, 1.467, 0.13, 1.8, 0]}, {"time": 1.9667, "light": "ffffffff", "dark": "000000"}]}, "GoatCult/Goo1": {"rgba2": [{"time": 1.3, "light": "ffffffff", "dark": "c55920", "curve": [1.467, 1, 1.8, 1, 1.467, 1, 1.8, 1, 1.467, 1, 1.8, 1, 1.467, 1, 1.8, 1, 1.467, 0.77, 1.8, 0, 1.467, 0.35, 1.8, 0, 1.467, 0.13, 1.8, 0]}, {"time": 1.9667, "light": "ffffffff", "dark": "000000"}]}, "GoatCult/Goo2": {"rgba2": [{"time": 1.3, "light": "ffffffff", "dark": "c55920", "curve": [1.467, 1, 1.8, 1, 1.467, 1, 1.8, 1, 1.467, 1, 1.8, 1, 1.467, 1, 1.8, 1, 1.467, 0.77, 1.8, 0, 1.467, 0.35, 1.8, 0, 1.467, 0.13, 1.8, 0]}, {"time": 1.9667, "light": "ffffffff", "dark": "000000"}]}, "GoatCult/Goo3": {"rgba2": [{"time": 1.3, "light": "ffffffff", "dark": "c55920", "curve": [1.467, 1, 1.8, 1, 1.467, 1, 1.8, 1, 1.467, 1, 1.8, 1, 1.467, 1, 1.8, 1, 1.467, 0.77, 1.8, 0, 1.467, 0.35, 1.8, 0, 1.467, 0.13, 1.8, 0]}, {"time": 1.9667, "light": "ffffffff", "dark": "000000"}]}, "GoatCult/Goo4": {"rgba2": [{"time": 1.3, "light": "ffffffff", "dark": "c55920", "curve": [1.467, 1, 1.8, 1, 1.467, 1, 1.8, 1, 1.467, 1, 1.8, 1, 1.467, 1, 1.8, 1, 1.467, 0.77, 1.8, 0, 1.467, 0.35, 1.8, 0, 1.467, 0.13, 1.8, 0]}, {"time": 1.9667, "light": "ffffffff", "dark": "000000"}]}, "GoatCult/Goo5": {"rgba2": [{"time": 1.3, "light": "ffffffff", "dark": "c55920", "curve": [1.467, 1, 1.8, 1, 1.467, 1, 1.8, 1, 1.467, 1, 1.8, 1, 1.467, 1, 1.8, 1, 1.467, 0.77, 1.8, 0, 1.467, 0.35, 1.8, 0, 1.467, 0.13, 1.8, 0]}, {"time": 1.9667, "light": "ffffffff", "dark": "000000"}]}, "GoatCult/Goo6": {"rgba2": [{"time": 1.3, "light": "ffffffff", "dark": "c55920", "curve": [1.467, 1, 1.8, 1, 1.467, 1, 1.8, 1, 1.467, 1, 1.8, 1, 1.467, 1, 1.8, 1, 1.467, 0.77, 1.8, 0, 1.467, 0.35, 1.8, 0, 1.467, 0.13, 1.8, 0]}, {"time": 1.9667, "light": "ffffffff", "dark": "000000"}]}, "GoatCult/Goo7": {"rgba2": [{"time": 1.3, "light": "ffffffff", "dark": "c55920", "curve": [1.467, 1, 1.8, 1, 1.467, 1, 1.8, 1, 1.467, 1, 1.8, 1, 1.467, 1, 1.8, 1, 1.467, 0.77, 1.8, 0, 1.467, 0.35, 1.8, 0, 1.467, 0.13, 1.8, 0]}, {"time": 1.9667, "light": "ffffffff", "dark": "000000"}]}, "GoatCult/Hand1": {"rgba2": [{"time": 1.3, "light": "ffffffff", "dark": "c55920", "curve": [1.467, 1, 1.8, 1, 1.467, 1, 1.8, 1, 1.467, 1, 1.8, 1, 1.467, 1, 1.8, 1, 1.467, 0.77, 1.8, 0, 1.467, 0.35, 1.8, 0, 1.467, 0.13, 1.8, 0]}, {"time": 1.9667, "light": "ffffffff", "dark": "000000"}], "attachment": [{"time": 0.5, "name": "Hand5"}, {"time": 1.3, "name": "Hand2"}]}, "GoatCult/Hand2": {"rgba2": [{"time": 1.3, "light": "ffffffff", "dark": "c55920", "curve": [1.467, 1, 1.8, 1, 1.467, 1, 1.8, 1, 1.467, 1, 1.8, 1, 1.467, 1, 1.8, 1, 1.467, 0.77, 1.8, 0, 1.467, 0.35, 1.8, 0, 1.467, 0.13, 1.8, 0]}, {"time": 1.9667, "light": "ffffffff", "dark": "000000"}], "attachment": [{"time": 0.5, "name": "Hand5"}, {"time": 1.3, "name": "Hand2"}]}, "GoatCult/Hood": {"rgba2": [{"time": 1.3, "light": "ffffffff", "dark": "c55920", "curve": [1.467, 1, 1.8, 1, 1.467, 1, 1.8, 1, 1.467, 1, 1.8, 1, 1.467, 1, 1.8, 1, 1.467, 0.77, 1.8, 0, 1.467, 0.35, 1.8, 0, 1.467, 0.13, 1.8, 0]}, {"time": 1.9667, "light": "ffffffff", "dark": "000000"}]}, "GoatCult/HornLeft": {"rgba2": [{"time": 1.3, "light": "ffffffff", "dark": "c55920", "curve": [1.472, 1, 1.712, 1, 1.472, 1, 1.712, 1, 1.472, 1, 1.712, 1, 1.472, 1, 1.712, 1, 1.472, 0.77, 1.712, 0.42, 1.472, 0.35, 1.712, 0.19, 1.472, 0.13, 1.712, 0.07]}, {"time": 1.9667, "light": "ffffffff", "dark": "000000"}]}, "GoatCult/HornRight": {"rgba2": [{"time": 1.3, "light": "ffffffff", "dark": "c55920", "curve": [1.467, 1, 1.8, 1, 1.467, 1, 1.8, 1, 1.467, 1, 1.8, 1, 1.467, 1, 1.8, 1, 1.467, 0.77, 1.8, 0, 1.467, 0.35, 1.8, 0, 1.467, 0.13, 1.8, 0]}, {"time": 1.9667, "light": "ffffffff", "dark": "000000"}]}, "GoatCult/Mask": {"rgba2": [{"time": 1.3, "light": "ffffffff", "dark": "c55920", "curve": [1.467, 1, 1.8, 1, 1.467, 1, 1.8, 1, 1.467, 1, 1.8, 1, 1.467, 1, 1.8, 1, 1.467, 0.77, 1.8, 0, 1.467, 0.35, 1.8, 0, 1.467, 0.13, 1.8, 0]}, {"time": 1.9667, "light": "ffffffff", "dark": "000000"}], "attachment": [{"time": 1.2333}, {"time": 2.1667}]}, "GoatCult/Necklace": {"rgba2": [{"time": 1.3, "light": "ffffffff", "dark": "c55920", "curve": [1.467, 1, 1.8, 1, 1.467, 1, 1.8, 1, 1.467, 1, 1.8, 1, 1.467, 1, 1.8, 1, 1.467, 0.77, 1.8, 0, 1.467, 0.35, 1.8, 0, 1.467, 0.13, 1.8, 0]}, {"time": 1.9667, "light": "ffffffff", "dark": "000000"}]}, "GoatCult/Sleeve": {"rgba2": [{"time": 1.3, "light": "ffffffff", "dark": "c55920", "curve": [1.467, 1, 1.8, 1, 1.467, 1, 1.8, 1, 1.467, 1, 1.8, 1, 1.467, 1, 1.8, 1, 1.467, 0.77, 1.8, 0, 1.467, 0.35, 1.8, 0, 1.467, 0.13, 1.8, 0]}, {"time": 1.9667, "light": "ffffffff", "dark": "000000"}]}, "GoatCult/Sleeve2": {"rgba2": [{"time": 1.3, "light": "ffffffff", "dark": "c55920", "curve": [1.467, 1, 1.8, 1, 1.467, 1, 1.8, 1, 1.467, 1, 1.8, 1, 1.467, 1, 1.8, 1, 1.467, 0.77, 1.8, 0, 1.467, 0.35, 1.8, 0, 1.467, 0.13, 1.8, 0]}, {"time": 1.9667, "light": "ffffffff", "dark": "000000"}]}, "Hood Btm": {"rgba2": [{"time": 1.3, "light": "ffffffff", "dark": "c55920", "curve": [1.467, 1, 1.8, 1, 1.467, 1, 1.8, 1, 1.467, 1, 1.8, 1, 1.467, 1, 1.8, 1, 1.467, 0.77, 1.8, 0, 1.467, 0.35, 1.8, 0, 1.467, 0.13, 1.8, 0]}, {"time": 1.9667, "light": "ffffffff", "dark": "000000"}]}, "Other/SpawnParticles": {"rgba": [{"time": 1.3, "color": "ffffff00", "curve": [1.311, 1, 1.346, 1, 1.311, 1, 1.346, 1, 1.311, 1, 1.346, 1, 1.311, 0.18, 1.346, 0.27]}, {"time": 1.3667, "color": "ffffff44", "curve": [1.517, 1, 1.817, 1, 1.517, 1, 1.817, 1, 1.517, 1, 1.817, 1, 1.517, 0.27, 1.817, 0]}, {"time": 1.9667, "color": "ffffff00"}]}, "Other/SpawnParticles2": {"rgba": [{"time": 1.3, "color": "ffffff00", "curve": [1.311, 1, 1.346, 1, 1.311, 1, 1.346, 1, 1.311, 1, 1.346, 1, 1.311, 0.18, 1.346, 0.27]}, {"time": 1.3667, "color": "ffffff44", "curve": [1.517, 1, 1.817, 1, 1.517, 1, 1.817, 1, 1.517, 1, 1.817, 1, 1.517, 0.27, 1.817, 0]}, {"time": 1.9667, "color": "ffffff00"}]}, "Other/SpawnParticles3": {"rgba": [{"time": 1.3, "color": "ffffff00", "curve": [1.311, 1, 1.346, 1, 1.311, 1, 1.346, 1, 1.311, 1, 1.346, 1, 1.311, 0.18, 1.346, 0.27]}, {"time": 1.3667, "color": "ffffff44", "curve": [1.517, 1, 1.817, 1, 1.517, 1, 1.817, 1, 1.517, 1, 1.817, 1, 1.517, 0.27, 1.817, 0]}, {"time": 1.9667, "color": "ffffff00"}]}, "Other/whiteball": {"rgba": [{"time": 1.3, "color": "ff9a0082", "curve": [1.392, 1, 1.575, 1, 1.392, 0.6, 1.575, 0.6, 1.392, 0, 1.575, 0, 1.392, 0.51, 1.575, 0]}, {"time": 1.6667, "color": "ff9a0000"}]}, "Sorcerer/GoatCult/Hand5": {"rgba2": [{"time": 1.3, "light": "ffffffff", "dark": "c55920"}]}}, "bones": {"BODY_BTM": {"rotate": [{"value": -1.67, "curve": [0.425, -11.76, 0.85, -21.85]}, {"time": 1.1333, "value": -21.85, "curve": [1.175, -21.85, 1.258, 1.38]}, {"time": 1.3, "value": 1.38, "curve": "stepped"}, {"time": 2.1667, "value": 1.38, "curve": [2.242, 1.38, 2.392, -1.67]}, {"time": 2.4667, "value": -1.67}], "translate": [{"curve": [0.15, 0, 0.45, 0, 0.15, 0, 0.45, 27.49]}, {"time": 0.6, "y": 27.49, "curve": [0.818, 0, 1.031, 0, 0.818, 17.38, 1.031, 7.9]}, {"time": 1.2333, "curve": [1.393, 0, 1.783, 0, 1.393, 35.54, 1.783, 57.78]}, {"time": 1.9667, "y": 57.78, "curve": [2.092, 0, 2.342, 0, 2.092, 57.78, 2.342, 0]}, {"time": 2.4667}], "scale": [{"y": 1.015, "curve": [0.15, 1, 0.45, 1.091, 0.15, 1.015, 0.45, 0.892]}, {"time": 0.6, "x": 1.091, "y": 0.892, "curve": [0.733, 1.091, 1, 1, 0.733, 0.892, 1, 1]}, {"time": 1.1333, "curve": [1.158, 1, 1.208, 0.514, 1.158, 1, 1.208, 1.342]}, {"time": 1.2333, "x": 0.514, "y": 1.342, "curve": [1.258, 0.514, 1.308, 1.295, 1.258, 1.342, 1.308, 0.776]}, {"time": 1.3333, "x": 1.295, "y": 0.776, "curve": [1.375, 1.295, 1.458, 0.963, 1.375, 0.776, 1.458, 1.027]}, {"time": 1.5, "x": 0.963, "y": 1.027, "curve": [1.533, 0.963, 1.6, 1.149, 1.533, 1.027, 1.6, 0.907]}, {"time": 1.6333, "x": 1.149, "y": 0.907, "curve": [1.667, 1.149, 1.733, 1.012, 1.667, 0.907, 1.733, 0.961]}, {"time": 1.7667, "x": 1.012, "y": 0.961, "curve": [1.8, 1.012, 1.867, 1.057, 1.8, 0.961, 1.867, 0.907]}, {"time": 1.9, "x": 1.057, "y": 0.907, "curve": [1.967, 1.057, 2.1, 1, 1.967, 0.907, 2.1, 1]}, {"time": 2.1667, "curve": [2.242, 1, 2.392, 1, 2.242, 1, 2.392, 1.015]}, {"time": 2.4667, "y": 1.015}]}, "SHOULDER_LEFT": {"rotate": [{"time": 2.1667, "value": 77.66}], "translate": [{"time": 1.1333, "curve": [1.175, 0, 1.258, -32.28, 1.175, 0, 1.258, -4.32]}, {"time": 1.3, "x": -32.28, "y": -4.32, "curve": "stepped"}, {"time": 2.1667, "x": -32.28, "y": -4.32, "curve": [2.242, -32.28, 2.392, 0, 2.242, -4.32, 2.392, 0]}, {"time": 2.4667}]}, "SHOULDER_RIGHT": {"rotate": [{"time": 2.1667, "value": -42.53}], "translate": [{"time": 1.1333, "curve": [1.175, 0, 1.258, -25.46, 1.175, 0, 1.258, -1.5]}, {"time": 1.3, "x": -25.46, "y": -1.5, "curve": "stepped"}, {"time": 2.1667, "x": -25.46, "y": -1.5, "curve": [2.242, -25.46, 2.392, 0, 2.242, -1.5, 2.392, 0]}, {"time": 2.4667}]}, "GOO": {"translate": [{"curve": [0.125, 0, 0.375, 5.23, 0.125, 0, 0.375, 1.01]}, {"time": 0.5, "x": 5.23, "y": 1.01, "curve": [0.625, 5.23, 0.875, 0, 0.625, 1.01, 0.875, 0]}, {"time": 1}]}, "HAND_LEFT": {"rotate": [{"value": -7.34, "curve": [0.402, -7.29, 0.857, -7.21]}, {"time": 1.1333, "value": -7.21, "curve": [1.175, -7.21, 1.258, -7.34]}, {"time": 1.3, "value": -7.34}]}, "HAND_RIGHT": {"rotate": [{"value": -7.2, "curve": [0.433, -7.12, 0.829, -7.06]}, {"time": 1.1333, "value": -7.06, "curve": [1.175, -7.06, 1.258, -7.2]}, {"time": 1.3, "value": -7.2}]}, "HEAD": {"rotate": [{"value": 1.32, "curve": [0.356, 4.22, 0.745, 8.08]}, {"time": 1.1333, "value": 11.95, "curve": [1.175, 11.95, 1.258, -3.88]}, {"time": 1.3, "value": -3.88, "curve": "stepped"}, {"time": 2.1667, "value": -3.88, "curve": [2.242, -3.88, 2.392, 1.32]}, {"time": 2.4667, "value": 1.32}], "translate": [{"x": -5.85, "y": -0.18, "curve": [0.432, -14.5, 0.841, -21.71, 0.432, -0.1, 0.841, -0.02]}, {"time": 1.1333, "x": -21.71, "y": -0.02, "curve": [1.175, -21.71, 1.258, -10.67, 1.175, -0.02, 1.258, -0.35]}, {"time": 1.3, "x": -10.67, "y": -0.35, "curve": "stepped"}, {"time": 2.1667, "x": -10.67, "y": -0.35, "curve": [2.242, -10.67, 2.392, -5.85, 2.242, -0.35, 2.392, -0.18]}, {"time": 2.4667, "x": -5.85, "y": -0.18}]}, "HORN_L": {"rotate": [{"value": -13.44, "curve": [0.099, -6.95, 0.201, 0]}, {"time": 0.2667, "curve": [0.392, 0, 0.642, -24.71]}, {"time": 0.7667, "value": -24.71, "curve": [0.826, -24.71, 0.912, -19.26]}, {"time": 1, "value": -13.44, "curve": [1.111, -6.95, 1.226, 0]}, {"time": 1.3, "curve": [1.325, 0, 1.375, 26.24]}, {"time": 1.4, "value": 26.24, "curve": [1.433, 26.24, 1.5, -51.46]}, {"time": 1.5333, "value": -51.46, "curve": [1.575, -51.46, 1.658, 23.54]}, {"time": 1.7, "value": 23.54, "curve": [1.767, 23.54, 1.9, 0]}, {"time": 1.9667, "curve": [2.092, 0, 2.342, -13.44]}, {"time": 2.4667, "value": -13.44}]}, "HORN_R": {"rotate": [{"value": 12.05, "curve": [0.099, 6.23, 0.201, 0]}, {"time": 0.2667, "curve": [0.392, 0, 0.642, 22.15]}, {"time": 0.7667, "value": 22.15, "curve": [0.826, 22.15, 0.912, 17.26]}, {"time": 1, "value": 12.05, "curve": [1.111, 6.23, 1.226, 0]}, {"time": 1.3, "curve": [1.325, 0, 1.375, -27.88]}, {"time": 1.4, "value": -27.88, "curve": [1.433, -27.88, 1.5, 37.82]}, {"time": 1.5333, "value": 37.82, "curve": [1.575, 37.82, 1.658, -19.33]}, {"time": 1.7, "value": -19.33, "curve": [1.767, -19.33, 1.9, 0]}, {"time": 1.9667, "curve": [2.092, 0, 2.342, 12.05]}, {"time": 2.4667, "value": 12.05}]}, "BODY_TOP": {"rotate": [{}, {"time": 1.1333, "value": 18.02, "curve": [1.175, 18.02, 1.258, -10.92]}, {"time": 1.3, "value": -10.92, "curve": "stepped"}, {"time": 2.1667, "value": -10.92, "curve": [2.242, -10.92, 2.392, 0]}, {"time": 2.4667}]}, "SLEEVE_LEFT": {"rotate": [{"value": -27.05, "curve": [0.542, -27.05, 1.625, 0]}, {"time": 2.1667, "curve": [2.242, 0, 2.392, -27.05]}, {"time": 2.4667, "value": -27.05}], "translate": [{"x": 11.1, "curve": "stepped"}, {"time": 1.3, "x": 11.1, "curve": [1.317, 11.1, 1.35, 32.71, 1.317, 0, 1.35, -5.13]}, {"time": 1.3667, "x": 32.71, "y": -5.13, "curve": [1.392, 32.71, 1.442, 21.65, 1.392, -5.13, 1.442, -3.79]}, {"time": 1.4667, "x": 21.65, "y": -3.79, "curve": [1.517, 21.65, 1.617, 32.71, 1.517, -3.79, 1.617, -5.13]}, {"time": 1.6667, "x": 32.71, "y": -5.13, "curve": [1.733, 32.71, 1.867, 11.1, 1.733, -5.13, 1.867, 0]}, {"time": 1.9333, "x": 11.1}]}, "SLEEVE_RIGHT": {"rotate": [{"value": -21.61, "curve": [0.542, -21.61, 1.625, 0]}, {"time": 2.1667, "curve": [2.242, 0, 2.392, -21.61]}, {"time": 2.4667, "value": -21.61}], "translate": [{"x": 7.6, "curve": "stepped"}, {"time": 1.3, "x": 7.6, "curve": [1.317, 7.6, 1.35, 23.94, 1.317, 0, 1.35, -5.45]}, {"time": 1.3667, "x": 23.94, "y": -5.45, "curve": [1.392, 23.94, 1.442, 6.59, 1.392, -5.45, 1.442, 0.53]}, {"time": 1.4667, "x": 6.59, "y": 0.53, "curve": [1.517, 6.59, 1.617, 23.94, 1.517, 0.53, 1.617, -5.45]}, {"time": 1.6667, "x": 23.94, "y": -5.45, "curve": [1.733, 23.94, 1.867, 7.6, 1.733, -5.45, 1.867, 0]}, {"time": 1.9333, "x": 7.6}]}, "spawn_particles": {"translate": [{"time": 1.3, "y": 5.14, "curve": [1.41, 0, 1.756, 0, 1.41, 24.5, 1.756, 33.77]}, {"time": 1.9667, "y": 33.77}]}, "spawn_particles2": {"translate": [{"time": 1.3, "y": -19.52, "curve": [1.41, 0, 1.756, 0, 1.41, 17.9, 1.756, 35.83]}, {"time": 1.9667, "y": 35.83}]}, "white": {"translate": [{"time": 1.3, "x": 235.85, "y": 31.18}], "scale": [{"time": 1.3, "x": 1.894, "y": 1.894, "curve": [1.368, 2.614, 1.575, 2.844, 1.368, 2.614, 1.575, 2.844]}, {"time": 1.6667, "x": 2.844, "y": 2.844}]}, "GOO_1": {"rotate": [{"value": -15.24, "curve": [0.091, -6.4, 0.195, 7.51]}, {"time": 0.3, "value": 21.86, "curve": [0.425, 21.86, 0.675, -27.83]}, {"time": 0.8, "value": -27.83, "curve": [0.857, -27.83, 0.925, -23.03]}, {"time": 1, "value": -15.24, "curve": [1.041, -11.34, 1.087, -5.21]}, {"time": 1.1333, "value": 1.11, "curve": "stepped"}, {"time": 2.1667, "value": 1.11, "curve": [2.242, 1.11, 2.392, -15.24]}, {"time": 2.4667, "value": -15.24}], "translate": [{"y": -4.63, "curve": [0.099, 0, 0.2, 0, 0.099, -7.22, 0.2, -9.95]}, {"time": 0.2667, "y": -9.95, "curve": [0.392, 0, 0.642, 0, 0.392, -9.95, 0.642, 0]}, {"time": 0.7667, "curve": [0.825, 0, 0.912, 0, 0.825, 0, 0.912, -2.26]}, {"time": 1, "y": -4.63, "curve": [1.062, 0, 1.125, 0, 1.062, -2.38, 1.125, 0]}, {"time": 1.1667, "curve": [1.184, 1.19, 1.242, 1.84, 1.184, 17, 1.242, 26.23]}, {"time": 1.2667, "x": 1.84, "y": 26.23, "curve": [1.317, 1.84, 1.417, -1.38, 1.317, 26.23, 1.417, -57.83]}, {"time": 1.4667, "x": -1.38, "y": -57.83, "curve": [1.542, -1.38, 1.692, 2.1, 1.542, -57.83, 1.692, 18.57]}, {"time": 1.7667, "x": 2.1, "y": 18.57, "curve": [1.867, 2.1, 2.067, 0, 1.867, 18.57, 2.067, 0]}, {"time": 2.1667, "curve": [2.242, 0, 2.392, 0, 2.242, 0, 2.392, -4.63]}, {"time": 2.4667, "y": -4.63}]}, "GOO_2": {"rotate": [{"value": 2.37, "curve": [0.088, 6.86, 0.174, 11.08]}, {"time": 0.2333, "value": 11.08, "curve": [0.358, 11.08, 0.608, -8.03]}, {"time": 0.7333, "value": -8.03, "curve": [0.799, -8.03, 0.901, -2.65]}, {"time": 1, "value": 2.37, "curve": [1.05, 2.86, 1.1, 3.32]}, {"time": 1.1333, "value": 3.32, "curve": "stepped"}, {"time": 2.1667, "value": 3.32, "curve": [2.242, 3.32, 2.392, 2.37]}, {"time": 2.4667, "value": 2.37}], "translate": [{"x": 0.02, "y": -8.05, "curve": [0.037, 0.01, 0.071, 0, 0.037, -9.23, 0.071, -9.95]}, {"time": 0.1, "y": -9.95, "curve": [0.233, 0, 0.5, 0.19, 0.233, -9.95, 0.5, 8.65]}, {"time": 0.6333, "x": 0.19, "y": 8.65, "curve": [0.722, 0.19, 0.885, 0.06, 0.722, 8.65, 0.885, -3.74]}, {"time": 1, "x": 0.02, "y": -8.05, "curve": [1.099, 0.01, 1.19, 0, 1.099, -8.26, 1.19, -8.39]}, {"time": 1.2667, "y": -8.39, "curve": [1.317, 0, 1.417, 3.17, 1.317, -8.39, 1.417, 44.73]}, {"time": 1.4667, "x": 3.17, "y": 44.73, "curve": [1.542, 3.17, 1.692, -3.59, 1.542, 44.73, 1.692, 3.43]}, {"time": 1.7667, "x": -3.59, "y": 3.43, "curve": [1.867, -3.59, 2.067, -5.59, 1.867, 3.43, 2.067, 13.68]}, {"time": 2.1667, "x": -5.59, "y": 13.68, "curve": [2.242, -5.59, 2.392, 0.02, 2.242, 13.68, 2.392, -8.05]}, {"time": 2.4667, "x": 0.02, "y": -8.05}]}, "GOO_3": {"rotate": [{"value": 4.3, "curve": [0.088, -0.46, 0.174, -4.92]}, {"time": 0.2333, "value": -4.92, "curve": [0.358, -4.92, 0.608, 15.31]}, {"time": 0.7333, "value": 15.31, "curve": [0.799, 15.31, 0.901, 9.62]}, {"time": 1, "value": 4.3, "curve": [1.05, 3.02, 1.1, 1.82]}, {"time": 1.1333, "value": 1.82, "curve": "stepped"}, {"time": 2.1667, "value": 1.82, "curve": [2.242, 1.82, 2.392, 4.3]}, {"time": 2.4667, "value": 4.3}], "translate": [{"y": -6.01, "curve": [0.076, 0, 0.149, 0, 0.076, -8.13, 0.149, -9.95]}, {"time": 0.2, "y": -9.95, "curve": [0.325, 0, 0.575, 0, 0.325, -9.95, 0.575, 0]}, {"time": 0.7, "curve": [0.774, 0, 0.89, 0, 0.774, 0, 0.89, -3.26]}, {"time": 1, "y": -6.01, "curve": [1.063, 0, 1.124, 0, 1.063, -2.79, 1.124, 0]}, {"time": 1.1667, "curve": [1.184, 0, 1.242, 0, 1.184, 20.99, 1.242, 32.4]}, {"time": 1.2667, "y": 32.4, "curve": [1.317, 0, 1.417, -1.08, 1.317, 32.4, 1.417, -40.51]}, {"time": 1.4667, "x": -1.08, "y": -40.51, "curve": [1.542, -1.08, 1.692, 2.55, 1.542, -40.51, 1.692, 15.26]}, {"time": 1.7667, "x": 2.55, "y": 15.26, "curve": [1.867, 2.55, 2.067, 0, 1.867, 15.26, 2.067, 0]}, {"time": 2.1667, "curve": [2.242, 0, 2.392, 0, 2.242, 0, 2.392, -6.01]}, {"time": 2.4667, "y": -6.01}]}, "NECKLACE_HANDLE": {"translate": [{"y": 0.34, "curve": [0.099, -0.05, 0.201, -0.11, 0.099, -4.94, 0.201, -10.59]}, {"time": 0.2667, "x": -0.11, "y": -10.59, "curve": [0.392, -0.11, 0.642, 0.1, 0.392, -10.59, 0.642, 9.5]}, {"time": 0.7667, "x": 0.1, "y": 9.5, "curve": [0.868, 0.1, 1.016, 0.05, 0.868, 9.5, 1.016, 5.06]}, {"time": 1.1667, "y": 0.34, "curve": [1.216, 7.73, 1.267, 16, 1.216, 8.41, 1.267, 17.06]}, {"time": 1.3, "x": 16, "y": 17.06, "curve": [1.317, 16, 1.35, 10.59, 1.317, 17.06, 1.35, 46.67]}, {"time": 1.3667, "x": 10.59, "y": 46.67, "curve": [1.442, 10.59, 1.592, 16.22, 1.442, 46.67, 1.592, -24.46]}, {"time": 1.6667, "x": 16.22, "y": -24.46, "curve": [1.758, 16.22, 1.942, 9.17, 1.758, -24.46, 1.942, 17.24]}, {"time": 2.0333, "x": 9.17, "y": 17.24, "curve": [2.142, 9.17, 2.358, 0, 2.142, 17.24, 2.358, 0.34]}, {"time": 2.4667, "y": 0.34}]}, "NECKLACE": {"translate": [{"x": 3.65, "y": 2.07}]}, "ARM_LEFT_HANDLE": {"translate": [{"x": 5.01, "y": 0.59, "curve": [0.156, 50.55, 0.677, 125.9, 0.156, -5.75, 0.677, -16.25]}, {"time": 1.1333, "x": 125.9, "y": -16.25, "curve": [1.175, 125.9, 1.258, -33.5, 1.175, -16.25, 1.258, -10.43]}, {"time": 1.3, "x": -33.5, "y": -10.43, "curve": [1.317, -33.5, 1.35, -28.89, 1.317, -10.43, 1.35, -11.2]}, {"time": 1.3667, "x": -28.89, "y": -11.2, "curve": [1.383, -28.89, 1.417, -33.5, 1.383, -11.2, 1.417, -10.43]}, {"time": 1.4333, "x": -33.5, "y": -10.43, "curve": [1.45, -33.5, 1.483, -28.89, 1.45, -10.43, 1.483, -11.2]}, {"time": 1.5, "x": -28.89, "y": -11.2, "curve": [1.517, -28.89, 1.55, -33.5, 1.517, -11.2, 1.55, -10.43]}, {"time": 1.5667, "x": -33.5, "y": -10.43, "curve": [1.583, -33.5, 1.617, -28.89, 1.583, -10.43, 1.617, -11.2]}, {"time": 1.6333, "x": -28.89, "y": -11.2, "curve": [1.65, -28.89, 1.683, -33.5, 1.65, -11.2, 1.683, -10.43]}, {"time": 1.7, "x": -33.5, "y": -10.43, "curve": [1.717, -33.5, 1.75, -28.89, 1.717, -10.43, 1.75, -11.2]}, {"time": 1.7667, "x": -28.89, "y": -11.2, "curve": [1.783, -28.89, 1.817, -33.5, 1.783, -11.2, 1.817, -10.43]}, {"time": 1.8333, "x": -33.5, "y": -10.43, "curve": [1.85, -33.5, 1.883, -28.89, 1.85, -10.43, 1.883, -11.2]}, {"time": 1.9, "x": -28.89, "y": -11.2, "curve": [1.917, -28.89, 1.95, -33.5, 1.917, -11.2, 1.95, -10.43]}, {"time": 1.9667, "x": -33.5, "y": -10.43, "curve": [2.092, -33.5, 2.342, 5.01, 2.092, -10.43, 2.342, 0.59]}, {"time": 2.4667, "x": 5.01, "y": 0.59}]}, "ARM_RIGHT_HANDLE": {"translate": [{"x": 2.39, "y": 0.21, "curve": [0.433, 62.32, 0.829, 105.12, 0.433, 3.4, 0.829, 5.69]}, {"time": 1.1333, "x": 105.12, "y": 5.69, "curve": [1.175, 105.12, 1.258, -21.72, 1.175, 5.69, 1.258, -3.08]}, {"time": 1.3, "x": -21.72, "y": -3.08, "curve": [1.317, -21.72, 1.35, -15.45, 1.317, -3.08, 1.35, -4.13]}, {"time": 1.3667, "x": -15.45, "y": -4.13, "curve": [1.383, -15.45, 1.417, -21.72, 1.383, -4.13, 1.417, -3.08]}, {"time": 1.4333, "x": -21.72, "y": -3.08, "curve": [1.45, -21.72, 1.483, -15.45, 1.45, -3.08, 1.483, -4.13]}, {"time": 1.5, "x": -15.45, "y": -4.13, "curve": [1.517, -15.45, 1.55, -21.72, 1.517, -4.13, 1.55, -3.08]}, {"time": 1.5667, "x": -21.72, "y": -3.08, "curve": [1.583, -21.72, 1.617, -15.45, 1.583, -3.08, 1.617, -4.13]}, {"time": 1.6333, "x": -15.45, "y": -4.13, "curve": [1.65, -15.45, 1.683, -21.72, 1.65, -4.13, 1.683, -3.08]}, {"time": 1.7, "x": -21.72, "y": -3.08, "curve": [1.717, -21.72, 1.75, -15.45, 1.717, -3.08, 1.75, -4.13]}, {"time": 1.7667, "x": -15.45, "y": -4.13, "curve": [1.783, -15.45, 1.817, -21.72, 1.783, -4.13, 1.817, -3.08]}, {"time": 1.8333, "x": -21.72, "y": -3.08, "curve": [1.85, -21.72, 1.883, -15.45, 1.85, -3.08, 1.883, -4.13]}, {"time": 1.9, "x": -15.45, "y": -4.13, "curve": [1.917, -15.45, 1.95, -21.72, 1.917, -4.13, 1.95, -3.08]}, {"time": 1.9667, "x": -21.72, "y": -3.08, "curve": "stepped"}, {"time": 2.0333, "x": -21.72, "y": -3.08, "curve": [2.142, -21.72, 2.358, 2.39, 2.142, -3.08, 2.358, 0.21]}, {"time": 2.4667, "x": 2.39, "y": 0.21}]}, "CAPE_CORNER_RIGHT": {"rotate": [{"value": 30.04, "curve": [0.026, 30.04, 0.062, 25.01]}, {"time": 0.1, "value": 18.99, "curve": [0.16, 10.55, 0.226, 0]}, {"time": 0.2667, "curve": [0.325, 0, 0.442, 30.04]}, {"time": 0.5, "value": 30.04, "curve": [0.567, 30.04, 0.7, 0]}, {"time": 0.7667, "curve": [0.883, 0, 1.117, 28.82]}, {"time": 1.2333, "value": 28.82, "curve": [1.284, 28.82, 1.358, 40.69]}, {"time": 1.4333, "value": 53.35, "curve": [1.456, 41.7, 1.478, 29.83]}, {"time": 1.5, "value": 18.72, "curve": [1.525, 37.78, 1.549, 53.35]}, {"time": 1.5667, "value": 53.35, "curve": [1.589, 41.7, 1.611, 29.83]}, {"time": 1.6333, "value": 18.72, "curve": [1.659, 37.78, 1.683, 53.35]}, {"time": 1.7, "value": 53.35, "curve": [1.722, 41.7, 1.745, 29.83]}, {"time": 1.7667, "value": 18.72, "curve": [1.792, 37.78, 1.816, 53.35]}, {"time": 1.8333, "value": 53.35, "curve": "stepped"}, {"time": 2.1, "value": 53.35, "curve": [2.192, 53.35, 2.375, 30.04]}, {"time": 2.4667, "value": 30.04}], "translate": [{"x": 6.97, "y": -2.09, "curve": [0.038, 3.22, 0.074, 0, 0.038, -0.96, 0.074, 0]}, {"time": 0.1, "curve": [0.167, 0, 0.3, 26.66, 0.167, 0, 0.3, -5.04]}, {"time": 0.3667, "x": 26.66, "y": -5.04, "curve": [0.425, 26.66, 0.542, 0, 0.425, -5.04, 0.542, 0]}, {"time": 0.6, "curve": [0.667, 0, 0.8, 17.87, 0.667, 0, 0.8, -5.36]}, {"time": 0.8667, "x": 17.87, "y": -5.36, "curve": [0.957, 17.87, 1.099, 32.22, 0.957, -5.36, 1.099, -12.19]}, {"time": 1.2333, "x": 44.18, "y": -17.87, "curve": [1.283, 44.18, 1.383, -10.33, 1.283, -17.87, 1.383, 23.77]}, {"time": 1.4333, "x": -10.33, "y": 23.77, "curve": [1.454, -8.12, 1.477, -5.2, 1.454, 22.49, 1.477, 20.79]}, {"time": 1.5, "x": -2.11, "y": 19, "curve": [1.525, -5.95, 1.55, -10.33, 1.525, 21.23, 1.55, 23.77]}, {"time": 1.5667, "x": -10.33, "y": 23.77, "curve": [1.588, -8.12, 1.61, -5.2, 1.588, 22.49, 1.61, 20.79]}, {"time": 1.6333, "x": -2.11, "y": 19, "curve": [1.658, -5.95, 1.684, -10.33, 1.658, 21.23, 1.684, 23.77]}, {"time": 1.7, "x": -10.33, "y": 23.77, "curve": [1.721, -8.12, 1.744, -5.2, 1.721, 22.49, 1.744, 20.79]}, {"time": 1.7667, "x": -2.11, "y": 19, "curve": [1.791, -5.95, 1.817, -10.33, 1.791, 21.23, 1.817, 23.77]}, {"time": 1.8333, "x": -10.33, "y": 23.77, "curve": [1.874, -11.25, 2.033, -12.17, 1.874, 32.9, 2.033, 41.96]}, {"time": 2.1, "x": -12.17, "y": 41.96, "curve": [2.192, -12.17, 2.375, 6.97, 2.192, 41.96, 2.375, -2.09]}, {"time": 2.4667, "x": 6.97, "y": -2.09}]}, "CAPE_CORNER_LEFT": {"rotate": [{"value": -8.13, "curve": [0.038, -4.06, 0.075, 0]}, {"time": 0.1, "curve": [0.167, 0, 0.3, -16.25]}, {"time": 0.3667, "value": -16.25, "curve": [0.433, -16.25, 0.567, 0]}, {"time": 0.6333, "curve": [0.7, 0, 0.833, -16.25]}, {"time": 0.9, "value": -16.25, "curve": [0.983, -16.25, 1.108, -22.23]}, {"time": 1.2333, "value": -28.2, "curve": [1.284, -28.2, 1.358, -28]}, {"time": 1.4333, "value": -27.78, "curve": [1.456, -27.77, 1.478, -27.75]}, {"time": 1.5, "value": -27.73, "curve": [1.525, -27.76, 1.549, -27.78]}, {"time": 1.5667, "value": -27.78, "curve": [1.589, -27.77, 1.611, -27.75]}, {"time": 1.6333, "value": -27.73, "curve": [1.659, -27.76, 1.683, -27.78]}, {"time": 1.7, "value": -27.78, "curve": [1.722, -27.77, 1.745, -27.75]}, {"time": 1.7667, "value": -27.73, "curve": [1.792, -27.76, 1.816, -27.78]}, {"time": 1.8333, "value": -27.78, "curve": "stepped"}, {"time": 2.1, "value": -27.78, "curve": [2.192, -27.78, 2.375, -8.13]}, {"time": 2.4667, "value": -8.13}], "translate": [{"curve": [0.067, 0, 0.2, 20.76, 0.067, 0, 0.2, 12.29]}, {"time": 0.2667, "x": 20.76, "y": 12.29, "curve": [0.325, 20.76, 0.442, 0, 0.325, 12.29, 0.442, 0]}, {"time": 0.5, "curve": [0.567, 0, 0.7, 12.36, 0.567, 0, 0.7, 14.88]}, {"time": 0.7667, "x": 12.36, "y": 14.88, "curve": [0.883, 12.36, 1.117, 47.64, 0.883, 14.88, 1.117, 22.55]}, {"time": 1.2333, "x": 47.64, "y": 22.55, "curve": [1.283, 47.64, 1.383, -6.06, 1.283, 22.55, 1.383, -9.42]}, {"time": 1.4333, "x": -6.06, "y": -9.42, "curve": [1.452, -6.06, 1.475, -2.38, 1.452, -9.42, 1.475, -1.72]}, {"time": 1.5, "x": 2.97, "y": 9.48, "curve": [1.523, -0.22, 1.551, -6.06, 1.523, 2.81, 1.551, -9.42]}, {"time": 1.5667, "x": -6.06, "y": -9.42, "curve": [1.585, -6.06, 1.608, -2.92, 1.585, -9.42, 1.608, -4.52]}, {"time": 1.6333, "x": 1.64, "y": 2.6, "curve": [1.656, -1.08, 1.684, -6.06, 1.656, -1.64, 1.684, -9.42]}, {"time": 1.7, "x": -6.06, "y": -9.42, "curve": [1.718, -6.06, 1.741, -2.92, 1.718, -9.42, 1.741, -4.52]}, {"time": 1.7667, "x": 1.64, "y": 2.6, "curve": [1.789, -1.08, 1.817, -6.06, 1.789, -1.64, 1.817, -9.42]}, {"time": 1.8333, "x": -6.06, "y": -9.42, "curve": [1.874, -9.15, 2.033, -12.21, 1.874, -11.51, 2.033, -13.59]}, {"time": 2.1, "x": -12.21, "y": -13.59, "curve": [2.192, -12.21, 2.375, 0, 2.192, -13.59, 2.375, 0]}, {"time": 2.4667}]}, "FACE": {"translate": [{"x": 3.05, "y": -0.15, "curve": [0.411, -6.55, 0.856, -18.55, 0.411, 2.55, 0.856, 5.93]}, {"time": 1.1333, "x": -18.55, "y": 5.93, "curve": [1.175, -18.55, 1.258, -6.48, 1.175, 5.93, 1.258, -1.68]}, {"time": 1.3, "x": -6.48, "y": -1.68, "curve": [1.315, 35.47, 1.75, 45.05, 1.315, -0.82, 1.75, -0.63]}, {"time": 1.9, "x": 45.05, "y": -0.63, "curve": [2.042, 45.05, 2.325, 3.05, 2.042, -0.63, 2.325, -0.15]}, {"time": 2.4667, "x": 3.05, "y": -0.15}], "scale": [{}, {"time": 1.1333, "x": 0.632, "y": 1.195, "curve": [1.175, 0.632, 1.258, 1, 1.175, 1.195, 1.258, 1]}, {"time": 1.3}]}, "fire": {"scale": [{"time": 1.3, "x": 0.144, "y": 0.144, "curve": [1.343, 0.851, 1.45, 1.136, 1.343, 0.851, 1.45, 1.136]}, {"time": 1.5, "x": 1.136, "y": 1.136, "curve": [1.558, 1.136, 1.675, 1, 1.558, 1.136, 1.675, 1]}, {"time": 1.7333}]}, "fire2": {"scale": [{"time": 1.3, "x": 0.181, "y": 0.181, "curve": [1.343, 0.966, 1.45, 1.282, 1.343, 0.966, 1.45, 1.282]}, {"time": 1.5, "x": 1.282, "y": 1.282, "curve": [1.558, 1.282, 1.675, 1, 1.558, 1.282, 1.675, 1]}, {"time": 1.7333}]}, "root": {"translate": [{"time": 1.2667, "curve": [1.283, 0, 1.317, -25.99, 1.283, 0, 1.317, 0]}, {"time": 1.3333, "x": -25.99, "curve": [1.35, -25.99, 1.375, -7.5, 1.35, 0, 1.375, 0]}, {"time": 1.4, "x": 11, "curve": [1.425, 2.2, 1.45, -6.6, 1.425, 0, 1.45, 0]}, {"time": 1.4667, "x": -6.6, "curve": [1.483, -6.6, 1.508, 0, 1.483, 0, 1.508, 0]}, {"time": 1.5333, "x": 6.6, "curve": [1.558, 3.3, 1.583, 0, 1.558, 0, 1.583, 0]}, {"time": 1.6, "curve": [1.617, 0, 1.642, 2.2, 1.617, 0, 1.642, 0]}, {"time": 1.6667, "x": 4.4, "curve": [1.692, 2.2, 1.717, 0, 1.692, 0, 1.717, 0]}, {"time": 1.7333, "curve": [1.75, 0, 1.775, 1.65, 1.75, 0, 1.775, 0]}, {"time": 1.8, "x": 3.3, "curve": [1.825, 1.65, 1.85, 0, 1.825, 0, 1.85, 0]}, {"time": 1.8667}]}, "ARM_LEFT": {"rotate": [{"time": 1.3, "value": -73.81}]}, "NECK1": {"rotate": [{"time": 2.1667, "value": 2.38}]}, "NECK2": {"rotate": [{"time": 2.1667, "value": 11.31}]}, "Necklace2": {"translate": [{"x": 1.89, "y": 11.25, "curve": [0.128, 1.27, 0.303, -0.29, 0.128, 7.58, 0.303, -1.6]}, {"time": 0.4, "x": -0.29, "y": -1.6, "curve": [0.525, -0.29, 0.775, 2.22, 0.525, -1.6, 0.775, 13.18]}, {"time": 0.9, "x": 2.22, "y": 13.18, "curve": [0.928, 2.22, 0.963, 2.09, 0.928, 13.18, 0.963, 12.44]}, {"time": 1, "x": 1.89, "y": 11.25, "curve": [1.096, 1.35, 1.227, 0, 1.096, 8.04, 1.227, 0]}, {"time": 1.3, "curve": [1.317, 0, 1.35, -6.92, 1.317, 0, 1.35, 55.37]}, {"time": 1.3667, "x": -6.92, "y": 55.37, "curve": [1.458, -6.92, 1.642, 5.75, 1.458, 55.37, 1.642, -5.76]}, {"time": 1.7333, "x": 5.75, "y": -5.76, "curve": [1.825, 5.75, 2.008, 10.32, 1.825, -5.76, 2.008, 28.4]}, {"time": 2.1, "x": 10.32, "y": 28.4, "curve": [2.192, 10.32, 2.375, 1.89, 2.192, 28.4, 2.375, 11.25]}, {"time": 2.4667, "x": 1.89, "y": 11.25}]}, "Necklace4": {"translate": [{"x": 0.28, "y": 1.69, "curve": [0.038, 0.11, 0.072, 0, 0.038, 0.65, 0.072, 0]}, {"time": 0.1, "curve": [0.225, 0, 0.475, 2.22, 0.225, 0, 0.475, 13.18]}, {"time": 0.6, "x": 2.22, "y": 13.18, "curve": [0.681, 2.22, 0.816, 1.26, 0.681, 13.18, 0.816, 7.51]}, {"time": 0.9333, "x": 0.63, "y": 3.74, "curve": [0.956, 0.5, 0.979, 0.38, 0.956, 2.96, 0.979, 2.25]}, {"time": 1, "x": 0.28, "y": 1.69, "curve": [1.113, 0.11, 1.215, 0, 1.113, 0.65, 1.215, 0]}, {"time": 1.3, "curve": [1.317, 0, 1.35, -6.92, 1.317, 0, 1.35, 55.37]}, {"time": 1.3667, "x": -6.92, "y": 55.37, "curve": [1.425, -6.92, 1.542, 0.22, 1.425, 55.37, 1.542, -37.31]}, {"time": 1.6, "x": 0.22, "y": -37.31, "curve": [1.692, 0.22, 1.875, 9.03, 1.692, -37.31, 1.875, 6.01]}, {"time": 1.9667, "x": 9.03, "y": 6.01, "curve": [2.092, 9.03, 2.342, 0.28, 2.092, 6.01, 2.342, 1.69]}, {"time": 2.4667, "x": 0.28, "y": 1.69}]}, "ARM_RIGHT": {"rotate": [{"time": 1.3, "value": -76.72}]}, "effects": {"translate": [{"time": 1.3, "x": -333.93, "y": -8}]}, "HEAD_TOP": {"rotate": [{"value": -4.72, "curve": [0.088, -6.77, 0.174, -8.68]}, {"time": 0.2333, "value": -8.68, "curve": [0.358, -8.68, 0.608, 0]}, {"time": 0.7333, "curve": [0.799, 0, 0.901, -2.44]}, {"time": 1, "value": -4.72, "curve": [1.088, -2.29, 1.174, 0]}, {"time": 1.2333, "curve": [1.258, 0, 1.308, 25.29]}, {"time": 1.3333, "value": 25.29, "curve": [1.375, 25.29, 1.458, -34.67]}, {"time": 1.5, "value": -34.67, "curve": [1.567, -34.67, 1.7, 0]}, {"time": 1.7667, "curve": [1.85, 0, 2.017, -13.16]}, {"time": 2.1, "value": -13.16, "curve": [2.192, -13.16, 2.375, -4.72]}, {"time": 2.4667, "value": -4.72}]}}, "drawOrder": [{"time": 1.1333, "offsets": [{"slot": "Other/SpawnParticles", "offset": 26}, {"slot": "Other/SpawnParticles2", "offset": 26}, {"slot": "Other/SpawnParticles3", "offset": 26}]}], "events": [{"time": 1.3, "name": "<PERSON><PERSON><PERSON>"}]}, "teleport": {"slots": {"Cape": {"rgba2": [{"time": 0.1333, "light": "ffffffff", "dark": "000000", "curve": [0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0]}, {"time": 0.2333, "light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 0.6333, "light": "ffffff00", "dark": "000000", "curve": [0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 0, 0.708, 1, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0]}, {"time": 0.7333, "light": "ffffffff", "dark": "000000"}]}, "Cape_Back": {"rgba2": [{"time": 0.1333, "light": "ffffffff", "dark": "000000", "curve": [0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0]}, {"time": 0.2333, "light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 0.6333, "light": "ffffff00", "dark": "000000", "curve": [0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 0, 0.708, 1, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0]}, {"time": 0.7333, "light": "ffffffff", "dark": "000000"}]}, "Eyes": {"rgba": [{"time": 0.1333, "color": "ffffffff", "curve": [0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 0]}, {"time": 0.2333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6333, "color": "ffffff00", "curve": [0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 0, 0.708, 1]}, {"time": 0.7333, "color": "ffffffff"}]}, "GoatCult/Arm": {"rgba2": [{"time": 0.1333, "light": "ffffffff", "dark": "000000", "curve": [0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0]}, {"time": 0.2333, "light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 0.6333, "light": "ffffff00", "dark": "000000", "curve": [0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 0, 0.708, 1, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0]}, {"time": 0.7333, "light": "ffffffff", "dark": "000000"}]}, "GoatCult/Arm2": {"rgba2": [{"time": 0.1333, "light": "ffffffff", "dark": "000000", "curve": [0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0]}, {"time": 0.2333, "light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 0.6333, "light": "ffffff00", "dark": "000000", "curve": [0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 0, 0.708, 1, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0]}, {"time": 0.7333, "light": "ffffffff", "dark": "000000"}]}, "GoatCult/Goo1": {"rgba2": [{"time": 0.1333, "light": "ffffffff", "dark": "000000", "curve": [0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0]}, {"time": 0.2333, "light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 0.6333, "light": "ffffff00", "dark": "000000", "curve": [0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 0, 0.708, 1, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0]}, {"time": 0.7333, "light": "ffffffff", "dark": "000000"}]}, "GoatCult/Goo2": {"rgba2": [{"time": 0.1333, "light": "ffffffff", "dark": "000000", "curve": [0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0]}, {"time": 0.2333, "light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 0.6333, "light": "ffffff00", "dark": "000000", "curve": [0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 0, 0.708, 1, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0]}, {"time": 0.7333, "light": "ffffffff", "dark": "000000"}]}, "GoatCult/Goo3": {"rgba2": [{"time": 0.1333, "light": "ffffffff", "dark": "000000", "curve": [0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0]}, {"time": 0.2333, "light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 0.6333, "light": "ffffff00", "dark": "000000", "curve": [0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 0, 0.708, 1, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0]}, {"time": 0.7333, "light": "ffffffff", "dark": "000000"}]}, "GoatCult/Goo4": {"rgba2": [{"time": 0.1333, "light": "ffffffff", "dark": "000000", "curve": [0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0]}, {"time": 0.2333, "light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 0.6333, "light": "ffffff00", "dark": "000000", "curve": [0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 0, 0.708, 1, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0]}, {"time": 0.7333, "light": "ffffffff", "dark": "000000"}]}, "GoatCult/Goo5": {"rgba2": [{"time": 0.1333, "light": "ffffffff", "dark": "000000", "curve": [0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0]}, {"time": 0.2333, "light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 0.6333, "light": "ffffff00", "dark": "000000", "curve": [0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 0, 0.708, 1, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0]}, {"time": 0.7333, "light": "ffffffff", "dark": "000000"}]}, "GoatCult/Goo6": {"rgba2": [{"time": 0.1333, "light": "ffffffff", "dark": "000000", "curve": [0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0]}, {"time": 0.2333, "light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 0.6333, "light": "ffffff00", "dark": "000000", "curve": [0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 0, 0.708, 1, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0]}, {"time": 0.7333, "light": "ffffffff", "dark": "000000"}]}, "GoatCult/Goo7": {"rgba2": [{"time": 0.1333, "light": "ffffffff", "dark": "000000", "curve": [0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0]}, {"time": 0.2333, "light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 0.6333, "light": "ffffff00", "dark": "000000", "curve": [0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 0, 0.708, 1, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0]}, {"time": 0.7333, "light": "ffffffff", "dark": "000000"}]}, "GoatCult/Hand1": {"rgba2": [{"time": 0.1333, "light": "ffffffff", "dark": "000000", "curve": [0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0]}, {"time": 0.2333, "light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 0.6333, "light": "ffffff00", "dark": "000000", "curve": [0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 0, 0.708, 1, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0]}, {"time": 0.7333, "light": "ffffffff", "dark": "000000"}]}, "GoatCult/Hand2": {"rgba2": [{"time": 0.1333, "light": "ffffffff", "dark": "000000", "curve": [0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0]}, {"time": 0.2333, "light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 0.6333, "light": "ffffff00", "dark": "000000", "curve": [0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 0, 0.708, 1, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0]}, {"time": 0.7333, "light": "ffffffff", "dark": "000000"}]}, "GoatCult/Hood": {"rgba2": [{"time": 0.1333, "light": "ffffffff", "dark": "000000", "curve": [0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0]}, {"time": 0.2333, "light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 0.6333, "light": "ffffff00", "dark": "000000", "curve": [0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 0, 0.708, 1, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0]}, {"time": 0.7333, "light": "ffffffff", "dark": "000000"}]}, "GoatCult/HornLeft": {"rgba2": [{"time": 0.1333, "light": "ffffffff", "dark": "000000", "curve": [0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0]}, {"time": 0.2333, "light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 0.6333, "light": "ffffff00", "dark": "000000", "curve": [0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 0, 0.708, 1, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0]}, {"time": 0.7333, "light": "ffffffff", "dark": "000000"}]}, "GoatCult/HornRight": {"rgba2": [{"time": 0.1333, "light": "ffffffff", "dark": "000000", "curve": [0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0]}, {"time": 0.2333, "light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 0.6333, "light": "ffffff00", "dark": "000000", "curve": [0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 0, 0.708, 1, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0]}, {"time": 0.7333, "light": "ffffffff", "dark": "000000"}]}, "GoatCult/Mask": {"rgba2": [{"time": 0.1333, "light": "ffffffff", "dark": "000000", "curve": [0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0]}, {"time": 0.2333, "light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 0.6333, "light": "ffffff00", "dark": "000000", "curve": [0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 0, 0.708, 1, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0]}, {"time": 0.7333, "light": "ffffffff", "dark": "000000"}], "attachment": [{"time": 0.0667}, {"time": 0.6667}]}, "GoatCult/Necklace": {"rgba2": [{"time": 0.1333, "light": "ffffffff", "dark": "000000", "curve": [0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0]}, {"time": 0.2333, "light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 0.6333, "light": "ffffff00", "dark": "000000", "curve": [0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 0, 0.708, 1, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0]}, {"time": 0.7333, "light": "ffffffff", "dark": "000000"}]}, "GoatCult/Sleeve": {"rgba2": [{"time": 0.1333, "light": "ffffffff", "dark": "000000", "curve": [0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0]}, {"time": 0.2333, "light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 0.6333, "light": "ffffff00", "dark": "000000", "curve": [0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 0, 0.708, 1, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0]}, {"time": 0.7333, "light": "ffffffff", "dark": "000000"}]}, "GoatCult/Sleeve2": {"rgba2": [{"time": 0.1333, "light": "ffffffff", "dark": "000000", "curve": [0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0]}, {"time": 0.2333, "light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 0.6333, "light": "ffffff00", "dark": "000000", "curve": [0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 0, 0.708, 1, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0]}, {"time": 0.7333, "light": "ffffffff", "dark": "000000"}]}, "GoatCult/Weapon": {"rgba2": [{"time": 0.1333, "light": "ffffffff", "dark": "000000", "curve": [0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0]}, {"time": 0.2333, "light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 0.6333, "light": "ffffff00", "dark": "000000", "curve": [0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 0, 0.708, 1, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0]}, {"time": 0.7333, "light": "ffffffff", "dark": "000000"}]}, "Hood Btm": {"rgba2": [{"time": 0.1333, "light": "ffffffff", "dark": "000000", "curve": [0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0]}, {"time": 0.2333, "light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 0.6333, "light": "ffffff00", "dark": "000000", "curve": [0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 0, 0.708, 1, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0]}, {"time": 0.7333, "light": "ffffffff", "dark": "000000"}]}, "Other/SpawnParticles": {"rgba": [{"time": 0.1, "color": "ffffff00", "curve": [0.106, 1, 0.123, 1, 0.106, 1, 0.123, 1, 0.106, 1, 0.123, 1, 0.106, 0.48, 0.123, 0.71]}, {"time": 0.1333, "color": "ffffffb6", "curve": [0.225, 1, 0.408, 1, 0.225, 1, 0.408, 1, 0.225, 1, 0.408, 1, 0.225, 0.71, 0.408, 0]}, {"time": 0.5, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00", "curve": [0.684, 1, 0.706, 1, 0.684, 1, 0.706, 1, 0.684, 1, 0.706, 1, 0.684, 0.27, 0.706, 0.52]}, {"time": 0.7333, "color": "ffffffc0", "curve": [0.823, 1, 0.991, 1, 0.823, 1, 0.991, 1, 0.823, 1, 0.991, 1, 0.823, 0.24, 0.991, 0]}, {"time": 1.1, "color": "ffffff00"}]}, "Other/SpawnParticles2": {"rgba": [{"time": 0.1, "color": "ffffff00", "curve": [0.106, 1, 0.123, 1, 0.106, 1, 0.123, 1, 0.106, 1, 0.123, 1, 0.106, 0.48, 0.123, 0.71]}, {"time": 0.1333, "color": "ffffffb6", "curve": [0.225, 1, 0.408, 1, 0.225, 1, 0.408, 1, 0.225, 1, 0.408, 1, 0.225, 0.71, 0.408, 0]}, {"time": 0.5, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00", "curve": [0.678, 1, 0.712, 1, 0.678, 1, 0.712, 1, 0.678, 1, 0.712, 1, 0.678, 0.51, 0.712, 0.75]}, {"time": 0.7333, "color": "ffffffc0", "curve": [0.825, 1, 1.008, 1, 0.825, 1, 1.008, 1, 0.825, 1, 1.008, 1, 0.825, 0.75, 1.008, 0]}, {"time": 1.1, "color": "ffffff00"}]}, "Other/SpawnParticles3": {"rgba": [{"time": 0.1, "color": "ffffff00", "curve": [0.106, 1, 0.123, 1, 0.106, 1, 0.123, 1, 0.106, 1, 0.123, 1, 0.106, 0.48, 0.123, 0.71]}, {"time": 0.1333, "color": "ffffffb6", "curve": [0.225, 1, 0.408, 1, 0.225, 1, 0.408, 1, 0.225, 1, 0.408, 1, 0.225, 0.71, 0.408, 0]}, {"time": 0.5, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00", "curve": [0.678, 1, 0.712, 1, 0.678, 1, 0.712, 1, 0.678, 1, 0.712, 1, 0.678, 0.51, 0.712, 0.75]}, {"time": 0.7333, "color": "ffffffc0", "curve": [0.825, 1, 1.008, 1, 0.825, 1, 1.008, 1, 0.825, 1, 1.008, 1, 0.825, 0.75, 1.008, 0]}, {"time": 1.1, "color": "ffffff00"}]}, "Sorcerer/Cult/Medic_Ring": {"rgba": [{"time": 0.1333, "color": "ffffffff"}, {"time": 0.2333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6333, "color": "ffffff00"}, {"time": 0.7333, "color": "ffffffff"}]}, "Sorcerer/GoatCult/Hand5": {"rgba2": [{"time": 0.1333, "light": "ffffffff", "dark": "000000", "curve": [0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0]}, {"time": 0.2333, "light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 0.6333, "light": "ffffff00", "dark": "000000", "curve": [0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 0, 0.708, 1, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0]}, {"time": 0.7333, "light": "ffffffff", "dark": "000000"}]}}, "bones": {"BODY_BTM": {"rotate": [{"value": -1.67, "curve": [0.013, -0.84, 0.025, 0]}, {"time": 0.0333, "curve": [0.083, 0, 0.183, -8.53]}, {"time": 0.2333, "value": -8.53, "curve": "stepped"}, {"time": 0.6667, "value": -8.53, "curve": [0.689, -2.76, 0.758, 0]}, {"time": 0.8, "curve": [0.9, 0, 1.1, -1.67]}, {"time": 1.2, "value": -1.67}], "translate": [{"curve": [0.017, 0, 0.05, 2.2, 0.017, 0, 0.05, 30.91]}, {"time": 0.0667, "x": 2.2, "y": 30.91, "curve": [0.118, 2.91, 0.193, 5.05, 0.118, 17.2, 0.193, -23.91]}, {"time": 0.2333, "x": 5.05, "y": -23.91, "curve": [0.305, 1.95, 0.53, 0.46, 0.305, -25.29, 0.53, -25.95]}, {"time": 0.6667, "x": 0.46, "y": -25.95, "curve": [0.689, -0.58, 0.758, -1.08, 0.689, 41.14, 0.758, 73.29]}, {"time": 0.8, "x": -1.08, "y": 73.29, "curve": [0.9, -1.08, 1.1, 0, 0.9, 73.29, 1.1, 0]}, {"time": 1.2}], "scale": [{"y": 1.015, "curve": [0.008, 1, 0.025, 1.246, 0.008, 1.015, 0.025, 0.811]}, {"time": 0.0333, "x": 1.246, "y": 0.811, "curve": [0.083, 1.246, 0.183, 0.082, 0.083, 0.811, 0.183, 1]}, {"time": 0.2333, "x": 0.082, "curve": "stepped"}, {"time": 0.6667, "x": 0.082, "curve": [0.689, 0.869, 0.758, 1.246, 0.689, 0.842, 0.758, 0.767]}, {"time": 0.8, "x": 1.246, "y": 0.767, "curve": [0.9, 1.246, 1.1, 1, 0.9, 0.767, 1.1, 1.015]}, {"time": 1.2, "y": 1.015}]}, "FACE": {"translate": [{"x": 3.05, "y": -0.15, "curve": [0.024, 21.27, 0.05, 44.04, 0.024, -2.54, 0.05, -5.54]}, {"time": 0.0667, "x": 44.04, "y": -5.54, "curve": [0.296, -0.27, 0.512, -37.2, 0.296, 1.96, 0.512, 8.21]}, {"time": 0.6667, "x": -37.2, "y": 8.21, "curve": [0.676, 11.67, 0.886, 35.08, 0.676, -0.92, 0.886, -5.31]}, {"time": 0.9667, "x": 35.08, "y": -5.31, "curve": [1.025, 35.08, 1.142, 3.05, 1.025, -5.31, 1.142, -0.15]}, {"time": 1.2, "x": 3.05, "y": -0.15}], "scale": [{"time": 0.6667, "x": 0.57}, {"time": 0.7333}]}, "SHOULDER_LEFT": {"rotate": [{"time": 0.8333, "value": 70.23}]}, "SHOULDER_RIGHT": {"rotate": [{"time": 0.8333, "value": -43.34}]}, "HAND_LEFT": {"rotate": [{"value": -7.34, "curve": [0.295, -5.87, 0.63, -3.76]}, {"time": 0.8333, "value": -3.76, "curve": [0.906, -6.75, 0.984, -10.56]}, {"time": 1.0333, "value": -10.56, "curve": [1.068, -10.56, 1.116, -9.16]}, {"time": 1.1667, "value": -7.34}]}, "HAND_RIGHT": {"rotate": [{"value": -7.2, "curve": [0.318, -6.3, 0.609, -5.66]}, {"time": 0.8333, "value": -5.66, "curve": [0.894, -9.82, 0.959, -14.9]}, {"time": 1, "value": -14.9, "curve": [1.057, -12.19, 1.113, -9.48]}, {"time": 1.1667, "value": -7.2}]}, "HEAD": {"rotate": [{"value": 1.32, "curve": [0.031, 0.96, 0.066, 0.48]}, {"time": 0.1, "curve": [0.133, 0, 0.183, -0.08]}, {"time": 0.2333, "value": -0.15, "curve": [0.396, 11.75, 0.558, 23.66]}, {"time": 0.6667, "value": 23.66, "curve": [0.689, 7.66, 0.758, 0]}, {"time": 0.8, "curve": [0.808, 0, 0.825, 1.8]}, {"time": 0.8333, "value": 1.8, "curve": [0.896, 0.9, 0.958, 0]}, {"time": 1, "curve": [1.046, 0, 1.103, 0.53]}, {"time": 1.1667, "value": 1.32}], "translate": [{"x": -5.85, "y": -0.18, "curve": [0.038, 4.1, 0.074, 12.4, 0.038, -0.28, 0.074, -0.36]}, {"time": 0.1, "x": 12.4, "y": -0.36, "curve": [0.133, 12.4, 0.2, -53.86, 0.133, -0.36, 0.2, 28.4]}, {"time": 0.2333, "x": -53.86, "y": 28.4, "curve": [0.342, -53.86, 0.558, -205.63, 0.342, 28.4, 0.558, 74.76]}, {"time": 0.6667, "x": -205.63, "y": 74.76, "curve": [0.689, -58.23, 0.758, 12.4, 0.689, 23.97, 0.758, -0.36]}, {"time": 0.8, "x": 12.4, "y": -0.36, "curve": [0.808, 12.4, 0.825, 2.43, 0.808, -0.36, 0.825, 0.11]}, {"time": 0.8333, "x": 2.43, "y": 0.11, "curve": [0.875, 2.43, 0.938, 6.78, 0.875, 0.11, 0.938, -1.78]}, {"time": 1, "x": 11.13, "y": -3.66, "curve": [1.057, 3.46, 1.113, -4.22, 1.057, -2.5, 1.113, -1.33]}, {"time": 1.1667, "x": -10.67, "y": -0.35, "curve": [1.175, -10.67, 1.188, -8.26, 1.175, -0.35, 1.188, -0.27]}, {"time": 1.2, "x": -5.85, "y": -0.18}]}, "HORN_L": {"rotate": [{"value": -13.44, "curve": [0.322, -6.95, 0.652, 0]}, {"time": 0.8667, "curve": [0.875, 0, 0.892, 26.24]}, {"time": 0.9, "value": 26.24, "curve": [0.933, 26.24, 1, -51.46]}, {"time": 1.0333, "value": -51.46, "curve": [1.075, -51.46, 1.158, -13.44]}, {"time": 1.2, "value": -13.44}]}, "HORN_R": {"rotate": [{"value": 12.05, "curve": [0.322, 6.23, 0.652, 0]}, {"time": 0.8667, "curve": [0.875, 0, 0.892, -27.88]}, {"time": 0.9, "value": -27.88, "curve": [0.933, -27.88, 1, 37.82]}, {"time": 1.0333, "value": 37.82, "curve": [1.075, 37.82, 1.158, 12.05]}, {"time": 1.2, "value": 12.05}]}, "BODY_TOP": {"rotate": [{}, {"time": 0.2333, "value": 11.76, "curve": [0.305, 19.93, 0.53, 23.84]}, {"time": 0.6667, "value": 23.84, "curve": [0.689, 3.89, 0.758, -5.68]}, {"time": 0.8, "value": -5.68, "curve": [0.808, -5.68, 0.825, 0]}, {"time": 0.8333}]}, "SLEEVE_LEFT": {"rotate": [{"value": -27.05, "curve": [0.208, -27.05, 0.625, 0]}, {"time": 0.8333, "curve": [0.925, 0, 1.108, -27.05]}, {"time": 1.2, "value": -27.05}], "translate": [{"x": 11.1, "curve": [0.312, 3.06, 0.595, -1.9, 0.312, 0, 0.595, 0]}, {"time": 0.8333, "x": -1.9, "curve": [0.858, -2.74, 0.88, -3.23, 0.858, 0, 0.88, 0]}, {"time": 0.9, "x": -3.23, "curve": [0.973, -3.23, 1.105, 7.15, 0.973, 0, 1.105, 0]}, {"time": 1.2, "x": 11.1}]}, "SLEEVE_RIGHT": {"rotate": [{"value": -21.61, "curve": [0.208, -21.61, 0.625, 0]}, {"time": 0.8333, "curve": [0.925, 0, 1.108, -21.61]}, {"time": 1.2, "value": -21.61}], "translate": [{"x": 7.6, "curve": [0.269, 4.2, 0.631, -3.88, 0.269, 0, 0.631, 0]}, {"time": 0.8333, "x": -3.88, "curve": [0.933, 0.51, 1.061, 9.28, 0.933, 0, 1.061, 0]}, {"time": 1.1333, "x": 9.28, "curve": [1.152, 9.28, 1.175, 8.63, 1.152, 0, 1.175, 0]}, {"time": 1.2, "x": 7.6}]}, "spawn_particles": {"translate": [{"time": 0.1, "y": 21.57, "curve": [0.2, 0, 0.4, 0, 0.2, 21.57, 0.4, 5.14]}, {"time": 0.5, "y": 5.14, "curve": "stepped"}, {"time": 0.6667, "y": 5.14, "curve": [0.755, 0, 1.032, 0, 0.755, 16.25, 1.032, 21.57]}, {"time": 1.2, "y": 21.57}]}, "spawn_particles2": {"translate": [{"time": 0.1, "y": 23.63, "curve": [0.2, 0, 0.4, 0, 0.2, 23.63, 0.4, -19.52]}, {"time": 0.5, "y": -19.52, "curve": "stepped"}, {"time": 0.6667, "y": -19.52, "curve": [0.755, 0, 1.032, 0, 0.755, 9.65, 1.032, 23.63]}, {"time": 1.2, "y": 23.63}]}, "Necklace2": {"translate": [{"x": 1.89, "y": 11.25, "curve": [0.246, 1.35, 0.581, 0, 0.246, 8.04, 0.581, 0]}, {"time": 0.7667, "curve": [0.783, 0, 0.817, -6.92, 0.783, 0, 0.817, 55.37]}, {"time": 0.8333, "x": -6.92, "y": 55.37, "curve": [0.875, -6.92, 0.958, 5.75, 0.875, 55.37, 0.958, -5.76]}, {"time": 1, "x": 5.75, "y": -5.76, "curve": [1.05, 5.75, 1.15, 1.89, 1.05, -5.76, 1.15, 11.25]}, {"time": 1.2, "x": 1.89, "y": 11.25}]}, "Necklace4": {"translate": [{"x": 0.28, "y": 1.69, "curve": [0.288, 0.11, 0.549, 0, 0.288, 0.65, 0.549, 0]}, {"time": 0.7667, "curve": [0.783, 0, 0.817, -6.92, 0.783, 0, 0.817, 55.37]}, {"time": 0.8333, "x": -6.92, "y": 55.37, "curve": [0.858, -6.92, 0.908, 0.22, 0.858, 55.37, 0.908, -37.31]}, {"time": 0.9333, "x": 0.22, "y": -37.31, "curve": [1, 0.22, 1.133, 0.28, 1, -37.31, 1.133, 1.69]}, {"time": 1.2, "x": 0.28, "y": 1.69}]}, "CAPE_CORNER_RIGHT": {"rotate": [{"value": 30.04, "curve": [0.052, 30.04, 0.107, 33.8]}, {"time": 0.1667, "value": 40.72, "curve": [0.33, 27.89, 0.526, -4.86]}, {"time": 0.7333, "value": -44.62, "curve": [0.766, -20.41, 0.8, 4.56]}, {"time": 0.8333, "value": 30.04, "curve": [0.875, 30.04, 0.958, 0]}, {"time": 1, "curve": [1.05, 0, 1.15, 30.04]}, {"time": 1.2, "value": 30.04}], "translate": [{"x": 6.97, "y": -2.09, "curve": [0.056, 6.29, 0.112, 5.64, 0.056, -8.03, 0.112, -13.77]}, {"time": 0.1667, "x": 5.04, "y": -19.02, "curve": [0.358, 2.12, 0.533, 0, 0.358, -8.01, 0.533, 0]}, {"time": 0.6667, "curve": [0.684, 0, 0.708, -5.72, 0.684, 0, 0.708, 38.48]}, {"time": 0.7333, "x": -12.58, "y": 84.65, "curve": [0.77, -4.63, 0.809, 5.31, 0.77, 59.93, 0.809, 29.02]}, {"time": 0.8333, "x": 5.31, "y": 29.02, "curve": [0.923, 12.35, 1.006, 17.87, 0.923, 9.77, 1.006, -5.36]}, {"time": 1.0667, "x": 17.87, "y": -5.36, "curve": [1.091, 17.87, 1.13, 11.93, 1.091, -5.36, 1.13, -3.58]}, {"time": 1.1667, "x": 6.97, "y": -2.09}]}, "CAPE_CORNER_LEFT": {"rotate": [{"value": -8.13, "curve": [0.056, -11.07, 0.112, -14.02]}, {"time": 0.1667, "value": -16.84, "curve": [0.376, 17.63, 0.576, 47.22]}, {"time": 0.7333, "value": 55.32, "curve": [0.768, 14.54, 0.802, -7.15]}, {"time": 0.8333, "value": -7.15, "curve": [0.859, -3.42, 0.883, 0]}, {"time": 0.9, "curve": [0.958, 0, 1.075, -16.25]}, {"time": 1.1333, "value": -16.25, "curve": [1.15, -16.25, 1.175, -12.19]}, {"time": 1.2, "value": -8.13}], "translate": [{"curve": [0.046, 0, 0.103, 0.01, 0.046, 0, 0.103, 16.88]}, {"time": 0.1667, "x": 0.04, "y": 42.2, "curve": [0.333, 0.02, 0.545, 0, 0.333, 28.13, 0.545, 0]}, {"time": 0.6667, "curve": [0.684, 0, 0.708, -13.99, 0.684, 0, 0.708, -24.14]}, {"time": 0.7333, "x": -30.77, "y": -53.1, "curve": [0.77, -17.1, 0.809, 0, 0.77, -29.5, 0.809, 0]}, {"time": 0.8333, "curve": [0.875, 0, 0.958, 12.36, 0.875, 0, 0.958, 14.88]}, {"time": 1, "x": 12.36, "y": 14.88, "curve": [1.05, 12.36, 1.15, 0, 1.05, 14.88, 1.15, 0]}, {"time": 1.2}]}, "NECKLACE_HANDLE": {"translate": [{"y": 0.34, "curve": [0.285, 7.73, 0.576, 16, 0.285, 8.41, 0.576, 17.06]}, {"time": 0.7667, "x": 16, "y": 17.06, "curve": [0.783, 16, 0.817, 10.59, 0.783, 17.06, 0.817, 46.67]}, {"time": 0.8333, "x": 10.59, "y": 46.67, "curve": [0.925, 10.59, 1.108, 0, 0.925, 46.67, 1.108, 0.34]}, {"time": 1.2, "y": 0.34}]}, "GOO_1": {"rotate": [{"value": -15.24, "curve": [0.203, -11.34, 0.434, -5.21]}, {"time": 0.6667, "value": 1.11, "curve": [0.729, 1.27, 0.792, 1.43]}, {"time": 0.8333, "value": 1.43, "curve": [0.908, 0.11, 0.984, -1.28]}, {"time": 1.0333, "value": -1.28, "curve": [1.075, -1.28, 1.137, -8.08]}, {"time": 1.2, "value": -15.24}], "translate": [{"y": -4.63, "curve": [0.056, -1, 0.112, -2.02, 0.056, 2.65, 0.112, 10.02]}, {"time": 0.1667, "x": -2.99, "y": 17.04, "curve": [0.37, -1.36, 0.563, 0, 0.37, 7.75, 0.563, 0]}, {"time": 0.7, "curve": [0.706, 0.34, 0.725, 0.52, 0.706, -12.01, 0.725, -18.53]}, {"time": 0.7333, "x": 0.52, "y": -18.53, "curve": [0.758, 0.52, 0.808, 0, 0.758, -18.53, 0.808, -5.26]}, {"time": 0.8333, "y": -5.26, "curve": [0.908, 0, 0.984, 0, 0.908, -2.69, 0.984, 0]}, {"time": 1.0333, "curve": [1.075, 0, 1.137, 0, 1.075, 0, 1.137, -2.26]}, {"time": 1.2, "y": -4.63}]}, "GOO_2": {"rotate": [{"value": 2.37, "curve": [0.252, 2.86, 0.499, 3.32]}, {"time": 0.6667, "value": 3.32, "curve": [0.729, 0.9, 0.785, -0.56]}, {"time": 0.8333, "value": -0.56, "curve": [0.858, -1, 0.881, -1.28]}, {"time": 0.9, "value": -1.28, "curve": [0.973, -1.28, 1.106, 1.43]}, {"time": 1.2, "value": 2.37}], "translate": [{"x": 0.02, "y": -8.05, "curve": [0.26, -3.47, 0.498, -5.59, 0.26, 5.49, 0.498, 13.68]}, {"time": 0.7, "x": -5.59, "y": 13.68, "curve": [0.706, -1.97, 0.725, 0, 0.706, -0.62, 0.725, -8.39]}, {"time": 0.7333, "y": -8.39, "curve": [0.758, 0, 0.808, 0.17, 0.758, -8.39, 0.808, 6.06]}, {"time": 0.8333, "x": 0.17, "y": 6.06, "curve": [0.858, 0.18, 0.881, 0.19, 0.858, 7.65, 0.881, 8.65]}, {"time": 0.9, "x": 0.19, "y": 8.65, "curve": [0.973, 0.19, 1.106, 0.06, 0.973, 8.65, 1.106, -3.74]}, {"time": 1.2, "x": 0.02, "y": -8.05}]}, "GOO_3": {"rotate": [{"value": 4.3, "curve": [0.252, 3.02, 0.499, 1.82]}, {"time": 0.6667, "value": 1.82, "curve": [0.73, 1.14, 0.791, 0.55]}, {"time": 0.8333, "value": 0.55, "curve": [0.897, -0.45, 0.957, -1.28]}, {"time": 1, "value": -1.28, "curve": [1.049, -1.28, 1.127, 1.75]}, {"time": 1.2, "value": 4.3}], "translate": [{"y": -6.01, "curve": [0.266, 0, 0.521, 0, 0.266, -2.79, 0.521, 0]}, {"time": 0.7, "curve": [0.706, 0, 0.725, 0, 0.706, 20.99, 0.725, 32.4]}, {"time": 0.7333, "y": 32.4, "curve": [0.758, 0, 0.808, 0, 0.758, 32.4, 0.808, -3.55]}, {"time": 0.8333, "y": -3.55, "curve": [0.897, 0, 0.957, 0, 0.897, -1.6, 0.957, 0]}, {"time": 1, "curve": [1.049, 0, 1.127, 0, 1.049, 0, 1.127, -3.26]}, {"time": 1.2, "y": -6.01}]}, "HEAD_TOP": {"rotate": [{"value": -4.72, "curve": [0.063, 8.17, 0.125, 20.26]}, {"time": 0.1667, "value": 20.26}, {"time": 0.8, "curve": [0.825, 0, 0.875, 25.29]}, {"time": 0.9, "value": 25.29, "curve": [0.925, 25.29, 0.975, -34.67]}, {"time": 1, "value": -34.67, "curve": [1.05, -34.67, 1.15, -4.72]}, {"time": 1.2, "value": -4.72}]}, "NECKLACE": {"translate": [{"x": 3.65, "y": 2.07}]}, "ARM_LEFT_HANDLE": {"translate": [{"x": 5.01, "y": 0.59, "curve": [0.014, 39.14, 0.06, 95.61, 0.014, 6.51, 0.06, 16.29]}, {"time": 0.1, "x": 95.61, "y": 16.29, "curve": [0.133, 95.61, 0.2, 152.55, 0.133, 16.29, 0.2, -89.45]}, {"time": 0.2333, "x": 152.55, "y": -89.45, "curve": [0.305, -214.84, 0.53, -390.88, 0.305, 35.88, 0.53, 95.94]}, {"time": 0.6667, "x": -390.88, "y": 95.94, "curve": [0.679, -241.62, 0.705, -131.24, 0.679, 37.51, 0.705, -5.71]}, {"time": 0.7333, "x": -58.17, "y": -34.32, "curve": [0.792, -58.17, 0.908, 70.28, 0.792, -34.32, 0.908, 17.85]}, {"time": 0.9667, "x": 70.28, "y": 17.85, "curve": [1.025, 70.28, 1.137, 54.65, 1.025, 17.85, 1.137, 13.72]}, {"time": 1.2, "x": 5.01, "y": 0.59}]}, "ARM_RIGHT_HANDLE": {"translate": [{"x": 2.39, "y": 0.21, "curve": [0.038, 29.76, 0.073, 49.31, 0.038, -2.38, 0.073, -4.23]}, {"time": 0.1, "x": 49.31, "y": -4.23, "curve": [0.116, 49.31, 0.145, 85.36, 0.116, -4.23, 0.145, 3.88]}, {"time": 0.1667, "x": 99.78, "y": 7.12, "curve": [0.192, 8.51, 0.214, -48.54, 0.192, 25.27, 0.214, 36.61]}, {"time": 0.2333, "x": -48.54, "y": 36.61, "curve": [0.305, -282.78, 0.53, -395.03, 0.305, 134.9, 0.53, 182]}, {"time": 0.6667, "x": -395.03, "y": 182, "curve": [0.679, -241.38, 0.705, -127.75, 0.679, 117.34, 0.705, 69.52]}, {"time": 0.7333, "x": -52.53, "y": 37.86, "curve": [0.792, -52.53, 0.908, 63.15, 0.792, 37.86, 0.908, -15.34]}, {"time": 0.9667, "x": 63.15, "y": -15.34, "curve": [1.024, 63.15, 1.137, 48.6, 1.024, -15.34, 1.137, -11.61]}, {"time": 1.2, "x": 2.39, "y": 0.21}]}, "effects": {"translate": [{"x": -322.82, "y": -42.44, "curve": [0.029, -322.82, 0.063, -297.8, 0.029, -42.44, 0.063, -42.44]}, {"time": 0.1, "x": -255.25, "y": -42.44, "curve": [0.263, -247.33, 0.503, -220.94, 0.263, -42.44, 0.503, -42.44]}, {"time": 0.6333, "x": -220.94, "y": -42.44, "curve": [0.644, -244.45, 0.659, -322.82, 0.644, -42.44, 0.659, -42.44]}, {"time": 0.6667, "x": -322.82, "y": -42.44}]}, "root": {"rotate": [{"time": 0.6667, "value": -0.11}]}, "ARM_RIGHT": {"rotate": [{"time": 0.8333, "value": -79.49}]}, "ARM_LEFT": {"rotate": [{"time": 0.8333, "value": -74.74}]}, "NECK1": {"rotate": [{"time": 0.8333, "value": 1.9}]}, "NECK2": {"rotate": [{"time": 0.8333, "value": 8.62}]}}, "drawOrder": [{"offsets": [{"slot": "Other/SpawnParticles", "offset": 26}, {"slot": "Other/SpawnParticles2", "offset": 26}, {"slot": "Other/SpawnParticles3", "offset": 26}]}], "events": [{"time": 0.5333, "name": "Teleport"}]}, "teleport-in": {"slots": {"Other/SpawnParticles": {"rgba": [{"color": "ffffff00", "curve": [0.011, 1, 0.046, 1, 0.011, 1, 0.046, 1, 0.011, 1, 0.046, 1, 0.011, 0.48, 0.046, 0.71]}, {"time": 0.0667, "color": "ffffffb6"}], "attachment": [{"name": "Other/SpawnParticles"}]}, "Other/SpawnParticles2": {"rgba": [{"color": "ffffff00", "curve": [0.011, 1, 0.046, 1, 0.011, 1, 0.046, 1, 0.011, 1, 0.046, 1, 0.011, 0.48, 0.046, 0.71]}, {"time": 0.0667, "color": "ffffffb6", "curve": [0.217, 1, 0.517, 1, 0.217, 1, 0.517, 1, 0.217, 1, 0.517, 1, 0.217, 0.71, 0.517, 0]}, {"time": 0.6667, "color": "ffffff00"}], "attachment": [{"name": "Other/SpawnParticles2"}]}, "Other/SpawnParticles3": {"rgba": [{"color": "ffffff00", "curve": [0.011, 1, 0.046, 1, 0.011, 1, 0.046, 1, 0.011, 1, 0.046, 1, 0.011, 0.48, 0.046, 0.71]}, {"time": 0.0667, "color": "ffffffb6", "curve": [0.217, 1, 0.517, 1, 0.217, 1, 0.517, 1, 0.217, 1, 0.517, 1, 0.217, 0.71, 0.517, 0]}, {"time": 0.6667, "color": "ffffff00"}], "attachment": [{"name": "Other/SpawnParticles2"}]}, "spawn": {"attachment": [{"name": "Other/Teleport_1"}, {"time": 0.1, "name": "Other/Teleport_2"}]}}, "bones": {"spawn_particles": {"translate": [{"y": 5.14, "curve": [0.033, 0, 0.137, 0, 0.033, 16.25, 0.137, 21.57]}, {"time": 0.2, "y": 21.57}]}, "spawn_particles2": {"translate": [{"y": -19.52, "curve": [0.11, 0, 0.456, 0, 0.11, 9.65, 0.456, 23.63]}, {"time": 0.6667, "y": 23.63}]}, "effects": {"translate": [{"x": -322.82, "y": -42.44}]}, "BODY_BTM": {"rotate": [{"value": -8.53, "curve": [0.028, -2.76, 0.114, 0]}, {"time": 0.1667, "curve": [0.233, 0, 0.367, -2.99]}, {"time": 0.4333, "value": -2.99, "curve": [0.492, -2.99, 0.579, -2.33]}, {"time": 0.6667, "value": -1.67}], "translate": [{"x": 0.46, "y": -25.95, "curve": [0.028, -0.58, 0.114, -1.08, 0.028, 41.14, 0.114, 73.29]}, {"time": 0.1667, "x": -1.08, "y": 73.29, "curve": [0.233, -1.08, 0.367, 1.76, 0.233, 73.29, 0.367, -15.31]}, {"time": 0.4333, "x": 1.76, "y": -15.31, "curve": [0.492, 1.76, 0.608, 0, 0.492, -15.31, 0.608, 0]}, {"time": 0.6667}], "scale": [{"x": 0.082, "curve": [0.028, 0.869, 0.114, 1.246, 0.028, 0.872, 0.114, 0.811]}, {"time": 0.1667, "x": 1.246, "y": 0.811, "curve": [0.233, 1.246, 0.367, 0.746, 0.233, 0.811, 0.367, 1.089]}, {"time": 0.4333, "x": 0.746, "y": 1.089, "curve": [0.492, 0.746, 0.608, 1, 0.492, 1.089, 0.608, 1]}, {"time": 0.6667}]}, "ARM_LEFT_HANDLE": {"translate": [{"x": -44.39, "y": -49.45, "curve": [0.028, 50.26, 0.114, 95.61, 0.028, -5, 0.114, 16.29]}, {"time": 0.1667, "x": 95.61, "y": 16.29, "curve": [0.292, 95.61, 0.542, 5.01, 0.292, 16.29, 0.542, 0.59]}, {"time": 0.6667, "x": 5.01, "y": 0.59}]}, "ARM_RIGHT_HANDLE": {"translate": [{"x": -48.54, "y": 36.61, "curve": [0.017, 52.11, 0.068, 100.33, 0.017, 6.03, 0.068, -8.63]}, {"time": 0.1, "x": 100.33, "y": -8.63, "curve": [0.242, 100.33, 0.525, 2.39, 0.242, -8.63, 0.525, 0.21]}, {"time": 0.6667, "x": 2.39, "y": 0.21}]}, "FACE": {"translate": [{"x": -294.55, "y": 11.5, "curve": [0.024, -160.75, 0.077, -66.78, 0.024, 8.04, 0.077, 5.6]}, {"time": 0.1333, "x": -10.81, "y": 4.15, "curve": [0.157, -5.07, 0.181, -2.26, 0.157, 0.45, 0.181, -1.37]}, {"time": 0.2, "x": -2.26, "y": -1.37, "curve": [0.297, 8.86, 0.401, 22.44, 0.297, -4.13, 0.401, -7.5]}, {"time": 0.4667, "x": 22.44, "y": -7.5, "curve": [0.518, 22.44, 0.59, 12.87, 0.518, -7.5, 0.59, -6.65]}, {"time": 0.6667, "x": 1.37, "y": -5.61}]}, "BODY_TOP": {"rotate": [{"value": 23.84, "curve": [0.028, 3.89, 0.114, -5.68]}, {"time": 0.1667, "value": -5.68}]}, "GOO_2": {"rotate": [{"value": 3.32, "curve": [0.074, 0.9, 0.142, -0.56]}, {"time": 0.2, "value": -0.56, "curve": [0.238, -1, 0.272, -1.28]}, {"time": 0.3, "value": -1.28, "curve": [0.389, -1.28, 0.552, 2.14]}, {"time": 0.6667, "value": 3.32}], "translate": [{"time": 0.0333, "x": -5.59, "y": 13.68, "curve": [0.039, -1.97, 0.058, 0, 0.039, -0.62, 0.058, -8.39]}, {"time": 0.0667, "y": -8.39, "curve": [0.1, 0, 0.167, 0.17, 0.1, -8.39, 0.167, 6.06]}, {"time": 0.2, "x": 0.17, "y": 6.06, "curve": [0.238, 0.18, 0.272, 0.19, 0.238, 7.65, 0.272, 8.65]}, {"time": 0.3, "x": 0.19, "y": 8.65, "curve": [0.389, 0.19, 0.552, 0.06, 0.389, 8.65, 0.552, -3.74]}, {"time": 0.6667, "x": 0.02, "y": -8.05}]}, "SLEEVE_LEFT": {"translate": [{"time": 0.2, "x": -1.9, "curve": [0.224, -2.74, 0.247, -3.23, 0.224, 0, 0.247, 0]}, {"time": 0.2667, "x": -3.23, "curve": [0.364, -3.23, 0.54, 7.15, 0.364, 0, 0.54, 0]}, {"time": 0.6667, "x": 11.1}]}, "GOO_3": {"rotate": [{"value": 1.82, "curve": [0.076, 1.14, 0.149, 0.55]}, {"time": 0.2, "value": 0.55, "curve": [0.276, -0.45, 0.348, -1.28]}, {"time": 0.4, "value": -1.28, "curve": [0.466, -1.28, 0.569, 0.4]}, {"time": 0.6667, "value": 1.82}], "translate": [{"time": 0.0333, "curve": [0.039, 0, 0.058, 0, 0.039, 20.99, 0.058, 32.4]}, {"time": 0.0667, "y": 32.4, "curve": [0.1, 0, 0.167, 0, 0.1, 32.4, 0.167, -3.55]}, {"time": 0.2, "y": -3.55, "curve": [0.276, 0, 0.348, 0, 0.276, -1.6, 0.348, 0]}, {"time": 0.4, "curve": [0.466, 0, 0.569, 0, 0.466, 0, 0.569, -3.26]}, {"time": 0.6667, "y": -6.01}]}, "GOO_1": {"rotate": [{"value": 1.11, "curve": [0.074, 1.27, 0.15, 1.43]}, {"time": 0.2, "value": 1.43, "curve": [0.299, 0.11, 0.4, -1.28]}, {"time": 0.4667, "value": -1.28, "curve": [0.517, -1.28, 0.591, -0.11]}, {"time": 0.6667, "value": 1.11}], "translate": [{"time": 0.0333, "curve": [0.039, 1.19, 0.058, 1.84, 0.039, 17, 0.058, 26.23]}, {"time": 0.0667, "x": 1.84, "y": 26.23, "curve": [0.1, 1.84, 0.167, 0, 0.1, 26.23, 0.167, -5.26]}, {"time": 0.2, "y": -5.26, "curve": [0.299, 0, 0.4, 0, 0.299, -2.69, 0.4, 0]}, {"time": 0.4667, "curve": [0.517, 0, 0.591, 0, 0.517, 0, 0.591, -2.26]}, {"time": 0.6667, "y": -4.63}]}, "SLEEVE_RIGHT": {"translate": [{"time": 0.2, "x": -3.88, "curve": [0.322, 0.51, 0.478, 9.28, 0.322, 0, 0.478, 0]}, {"time": 0.5667, "x": 9.28, "curve": [0.595, 9.28, 0.629, 8.63, 0.595, 0, 0.629, 0]}, {"time": 0.6667, "x": 7.6}]}, "CAPE_CORNER_LEFT": {"rotate": [{"time": 0.2, "value": -7.15, "curve": [0.238, -3.42, 0.275, 0]}, {"time": 0.3, "curve": [0.367, 0, 0.5, -16.25]}, {"time": 0.5667, "value": -16.25, "curve": [0.592, -16.25, 0.629, -12.19]}, {"time": 0.6667, "value": -8.13}], "translate": [{"time": 0.2, "curve": [0.258, 0, 0.375, 12.36, 0.258, 0, 0.375, 14.88]}, {"time": 0.4333, "x": 12.36, "y": 14.88, "curve": [0.492, 12.36, 0.608, 0, 0.492, 14.88, 0.608, 0]}, {"time": 0.6667}]}, "CAPE_CORNER_RIGHT": {"rotate": [{"time": 0.2, "value": 30.04, "curve": [0.258, 30.04, 0.375, 0]}, {"time": 0.4333, "curve": [0.492, 0, 0.608, 30.04]}, {"time": 0.6667, "value": 30.04}], "translate": [{"curve": [0.05, 0, 0.15, 5.31, 0.05, 0, 0.15, 29.02]}, {"time": 0.2, "x": 5.31, "y": 29.02, "curve": [0.315, 12.35, 0.422, 17.87, 0.315, 9.77, 0.422, -5.36]}, {"time": 0.5, "x": 17.87, "y": -5.36, "curve": [0.533, 17.87, 0.585, 11.93, 0.533, -5.36, 0.585, -3.58]}, {"time": 0.6333, "x": 6.97, "y": -2.09}]}, "NECKLACE_HANDLE": {"translate": [{"curve": [0.028, -25.35, 0.114, -37.5, 0.028, -2.41, 0.114, -3.56]}, {"time": 0.1667, "x": -37.5, "y": -3.56, "curve": [0.233, -37.5, 0.367, 15.19, 0.233, -3.56, 0.367, -0.88]}, {"time": 0.4333, "x": 15.19, "y": -0.88, "curve": [0.485, 15.19, 0.557, 12.65, 0.485, -0.88, 0.557, -0.73]}, {"time": 0.6333, "x": 9.61, "y": -0.56}]}, "HEAD": {"rotate": [{"value": 23.66, "curve": [0.028, 7.66, 0.114, 0]}, {"time": 0.1667, "curve": [0.175, 0, 0.192, 1.8]}, {"time": 0.2, "value": 1.8, "curve": [0.275, 0.9, 0.35, 0]}, {"time": 0.4, "curve": [0.464, 0, 0.545, 0.53]}, {"time": 0.6333, "value": 1.32}], "translate": [{"x": -205.63, "y": 74.76, "curve": [0.028, -58.23, 0.114, 12.4, 0.028, 23.97, 0.114, -0.36]}, {"time": 0.1667, "x": 12.4, "y": -0.36, "curve": [0.175, 12.4, 0.192, 2.43, 0.175, -0.36, 0.192, 0.11]}, {"time": 0.2, "x": 2.43, "y": 0.11, "curve": [0.308, 2.43, 0.525, -10.67, 0.308, 0.11, 0.525, -0.35]}, {"time": 0.6333, "x": -10.67, "y": -0.35}]}, "NECKLACE": {"translate": [{"time": 0.2, "x": 3.65, "y": 2.07}]}, "HAND_RIGHT": {"rotate": [{"time": 0.2, "value": -5.66, "curve": [0.285, -9.82, 0.376, -14.9]}, {"time": 0.4333, "value": -14.9, "curve": [0.501, -12.19, 0.569, -9.48]}, {"time": 0.6333, "value": -7.2}]}, "HAND_LEFT": {"rotate": [{"time": 0.2, "value": -3.76, "curve": [0.296, -6.75, 0.401, -10.56]}, {"time": 0.4667, "value": -10.56, "curve": [0.51, -10.56, 0.57, -9.16]}, {"time": 0.6333, "value": -7.34}]}, "root": {"rotate": [{"value": -0.11}]}, "SHOULDER_LEFT": {"rotate": [{"time": 0.2, "value": 70.23}]}, "ARM_LEFT": {"rotate": [{"time": 0.2, "value": -74.74}]}, "NECK1": {"rotate": [{"time": 0.2, "value": 1.9}]}, "NECK2": {"rotate": [{"time": 0.2, "value": 8.62}]}, "SHOULDER_RIGHT": {"rotate": [{"time": 0.2, "value": -43.34}]}, "ARM_RIGHT": {"rotate": [{"time": 0.2, "value": -79.49}]}}, "drawOrder": [{"offsets": [{"slot": "Other/SpawnParticles", "offset": 26}, {"slot": "Other/SpawnParticles2", "offset": 26}, {"slot": "Other/SpawnParticles3", "offset": 26}]}]}, "teleport-out": {"slots": {"Cape": {"rgba2": [{"time": 0.2333, "light": "ffffffff", "dark": "000000", "curve": [0.275, 1, 0.358, 1, 0.275, 1, 0.358, 1, 0.275, 1, 0.358, 1, 0.275, 1, 0.358, 0, 0.275, 0, 0.358, 0, 0.275, 0, 0.358, 0, 0.275, 0, 0.358, 0]}, {"time": 0.4, "light": "ffffff00", "dark": "000000"}]}, "GoatCult/Arm": {"rgba2": [{"time": 0.2333, "light": "ffffffff", "dark": "000000", "curve": [0.275, 1, 0.358, 1, 0.275, 1, 0.358, 1, 0.275, 1, 0.358, 1, 0.275, 1, 0.358, 0, 0.275, 0, 0.358, 0, 0.275, 0, 0.358, 0, 0.275, 0, 0.358, 0]}, {"time": 0.4, "light": "ffffff00", "dark": "000000"}]}, "GoatCult/Arm2": {"rgba2": [{"time": 0.2333, "light": "ffffffff", "dark": "000000", "curve": [0.275, 1, 0.358, 1, 0.275, 1, 0.358, 1, 0.275, 1, 0.358, 1, 0.275, 1, 0.358, 0, 0.275, 0, 0.358, 0, 0.275, 0, 0.358, 0, 0.275, 0, 0.358, 0]}, {"time": 0.4, "light": "ffffff00", "dark": "000000"}]}, "GoatCult/Goo1": {"rgba2": [{"time": 0.2333, "light": "ffffffff", "dark": "000000", "curve": [0.275, 1, 0.358, 1, 0.275, 1, 0.358, 1, 0.275, 1, 0.358, 1, 0.275, 1, 0.358, 0, 0.275, 0, 0.358, 0, 0.275, 0, 0.358, 0, 0.275, 0, 0.358, 0]}, {"time": 0.4, "light": "ffffff00", "dark": "000000"}]}, "GoatCult/Goo2": {"rgba2": [{"time": 0.2333, "light": "ffffffff", "dark": "000000", "curve": [0.275, 1, 0.358, 1, 0.275, 1, 0.358, 1, 0.275, 1, 0.358, 1, 0.275, 1, 0.358, 0, 0.275, 0, 0.358, 0, 0.275, 0, 0.358, 0, 0.275, 0, 0.358, 0]}, {"time": 0.4, "light": "ffffff00", "dark": "000000"}]}, "GoatCult/Goo3": {"rgba2": [{"time": 0.2333, "light": "ffffffff", "dark": "000000", "curve": [0.275, 1, 0.358, 1, 0.275, 1, 0.358, 1, 0.275, 1, 0.358, 1, 0.275, 1, 0.358, 0, 0.275, 0, 0.358, 0, 0.275, 0, 0.358, 0, 0.275, 0, 0.358, 0]}, {"time": 0.4, "light": "ffffff00", "dark": "000000"}]}, "GoatCult/Goo4": {"rgba2": [{"time": 0.2333, "light": "ffffffff", "dark": "000000", "curve": [0.275, 1, 0.358, 1, 0.275, 1, 0.358, 1, 0.275, 1, 0.358, 1, 0.275, 1, 0.358, 0, 0.275, 0, 0.358, 0, 0.275, 0, 0.358, 0, 0.275, 0, 0.358, 0]}, {"time": 0.4, "light": "ffffff00", "dark": "000000"}]}, "GoatCult/Goo5": {"rgba2": [{"time": 0.2333, "light": "ffffffff", "dark": "000000", "curve": [0.275, 1, 0.358, 1, 0.275, 1, 0.358, 1, 0.275, 1, 0.358, 1, 0.275, 1, 0.358, 0, 0.275, 0, 0.358, 0, 0.275, 0, 0.358, 0, 0.275, 0, 0.358, 0]}, {"time": 0.4, "light": "ffffff00", "dark": "000000"}]}, "GoatCult/Goo6": {"rgba2": [{"time": 0.2333, "light": "ffffffff", "dark": "000000", "curve": [0.275, 1, 0.358, 1, 0.275, 1, 0.358, 1, 0.275, 1, 0.358, 1, 0.275, 1, 0.358, 0, 0.275, 0, 0.358, 0, 0.275, 0, 0.358, 0, 0.275, 0, 0.358, 0]}, {"time": 0.4, "light": "ffffff00", "dark": "000000"}]}, "GoatCult/Goo7": {"rgba2": [{"time": 0.2333, "light": "ffffffff", "dark": "000000", "curve": [0.275, 1, 0.358, 1, 0.275, 1, 0.358, 1, 0.275, 1, 0.358, 1, 0.275, 1, 0.358, 0, 0.275, 0, 0.358, 0, 0.275, 0, 0.358, 0, 0.275, 0, 0.358, 0]}, {"time": 0.4, "light": "ffffff00", "dark": "000000"}]}, "GoatCult/Hand1": {"rgba2": [{"time": 0.2333, "light": "ffffffff", "dark": "000000", "curve": [0.275, 1, 0.358, 1, 0.275, 1, 0.358, 1, 0.275, 1, 0.358, 1, 0.275, 1, 0.358, 0, 0.275, 0, 0.358, 0, 0.275, 0, 0.358, 0, 0.275, 0, 0.358, 0]}, {"time": 0.4, "light": "ffffff00", "dark": "000000"}]}, "GoatCult/Hand2": {"rgba2": [{"time": 0.2333, "light": "ffffffff", "dark": "000000", "curve": [0.275, 1, 0.358, 1, 0.275, 1, 0.358, 1, 0.275, 1, 0.358, 1, 0.275, 1, 0.358, 0, 0.275, 0, 0.358, 0, 0.275, 0, 0.358, 0, 0.275, 0, 0.358, 0]}, {"time": 0.4, "light": "ffffff00", "dark": "000000"}]}, "GoatCult/Hood": {"rgba2": [{"time": 0.2333, "light": "ffffffff", "dark": "000000", "curve": [0.275, 1, 0.358, 1, 0.275, 1, 0.358, 1, 0.275, 1, 0.358, 1, 0.275, 1, 0.358, 0, 0.275, 0, 0.358, 0, 0.275, 0, 0.358, 0, 0.275, 0, 0.358, 0]}, {"time": 0.4, "light": "ffffff00", "dark": "000000"}]}, "GoatCult/HornLeft": {"rgba2": [{"time": 0.2333, "light": "ffffffff", "dark": "000000", "curve": [0.275, 1, 0.358, 1, 0.275, 1, 0.358, 1, 0.275, 1, 0.358, 1, 0.275, 1, 0.358, 0, 0.275, 0, 0.358, 0, 0.275, 0, 0.358, 0, 0.275, 0, 0.358, 0]}, {"time": 0.4, "light": "ffffff00", "dark": "000000"}]}, "GoatCult/HornRight": {"rgba2": [{"time": 0.2333, "light": "ffffffff", "dark": "000000", "curve": [0.275, 1, 0.358, 1, 0.275, 1, 0.358, 1, 0.275, 1, 0.358, 1, 0.275, 1, 0.358, 0, 0.275, 0, 0.358, 0, 0.275, 0, 0.358, 0, 0.275, 0, 0.358, 0]}, {"time": 0.4, "light": "ffffff00", "dark": "000000"}]}, "GoatCult/Mask": {"rgba2": [{"time": 0.2333, "light": "ffffffff", "dark": "000000", "curve": [0.275, 1, 0.358, 1, 0.275, 1, 0.358, 1, 0.275, 1, 0.358, 1, 0.275, 1, 0.358, 0, 0.275, 0, 0.358, 0, 0.275, 0, 0.358, 0, 0.275, 0, 0.358, 0]}, {"time": 0.4, "light": "ffffff00", "dark": "000000"}], "attachment": [{"time": 0.1333}]}, "GoatCult/Necklace": {"rgba2": [{"time": 0.2333, "light": "ffffffff", "dark": "000000", "curve": [0.275, 1, 0.358, 1, 0.275, 1, 0.358, 1, 0.275, 1, 0.358, 1, 0.275, 1, 0.358, 0, 0.275, 0, 0.358, 0, 0.275, 0, 0.358, 0, 0.275, 0, 0.358, 0]}, {"time": 0.4, "light": "ffffff00", "dark": "000000"}]}, "GoatCult/Sleeve": {"rgba2": [{"time": 0.2333, "light": "ffffffff", "dark": "000000", "curve": [0.275, 1, 0.358, 1, 0.275, 1, 0.358, 1, 0.275, 1, 0.358, 1, 0.275, 1, 0.358, 0, 0.275, 0, 0.358, 0, 0.275, 0, 0.358, 0, 0.275, 0, 0.358, 0]}, {"time": 0.4, "light": "ffffff00", "dark": "000000"}]}, "GoatCult/Sleeve2": {"rgba2": [{"time": 0.2333, "light": "ffffffff", "dark": "000000", "curve": [0.275, 1, 0.358, 1, 0.275, 1, 0.358, 1, 0.275, 1, 0.358, 1, 0.275, 1, 0.358, 0, 0.275, 0, 0.358, 0, 0.275, 0, 0.358, 0, 0.275, 0, 0.358, 0]}, {"time": 0.4, "light": "ffffff00", "dark": "000000"}]}, "GoatCult/Weapon": {"rgba2": [{"time": 0.2333, "light": "ffffffff", "dark": "000000", "curve": [0.275, 1, 0.358, 1, 0.275, 1, 0.358, 1, 0.275, 1, 0.358, 1, 0.275, 1, 0.358, 0, 0.275, 0, 0.358, 0, 0.275, 0, 0.358, 0, 0.275, 0, 0.358, 0]}, {"time": 0.4, "light": "ffffff00", "dark": "000000"}]}, "Other/SpawnParticles": {"rgba": [{"time": 0.1667, "color": "ffffff00", "curve": [0.178, 1, 0.212, 1, 0.178, 1, 0.212, 1, 0.178, 1, 0.212, 1, 0.178, 0.48, 0.212, 0.71]}, {"time": 0.2333, "color": "ffffffb6", "curve": [0.383, 1, 0.683, 1, 0.383, 1, 0.683, 1, 0.383, 1, 0.683, 1, 0.383, 0.71, 0.683, 0]}, {"time": 0.8333, "color": "ffffff00"}], "attachment": [{"time": 0.1667, "name": "Other/SpawnParticles"}]}, "Other/SpawnParticles2": {"rgba": [{"time": 0.1667, "color": "ffffff00", "curve": [0.178, 1, 0.212, 1, 0.178, 1, 0.212, 1, 0.178, 1, 0.212, 1, 0.178, 0.48, 0.212, 0.71]}, {"time": 0.2333, "color": "ffffffb6", "curve": [0.383, 1, 0.683, 1, 0.383, 1, 0.683, 1, 0.383, 1, 0.683, 1, 0.383, 0.71, 0.683, 0]}, {"time": 0.8333, "color": "ffffff00"}], "attachment": [{"time": 0.1667, "name": "Other/SpawnParticles2"}]}, "Other/SpawnParticles3": {"rgba": [{"time": 0.1667, "color": "ffffff00", "curve": [0.178, 1, 0.212, 1, 0.178, 1, 0.212, 1, 0.178, 1, 0.212, 1, 0.178, 0.48, 0.212, 0.71]}, {"time": 0.2333, "color": "ffffffb6", "curve": [0.383, 1, 0.683, 1, 0.383, 1, 0.683, 1, 0.383, 1, 0.683, 1, 0.383, 0.71, 0.683, 0]}, {"time": 0.8333, "color": "ffffff00"}], "attachment": [{"time": 0.1667, "name": "Other/SpawnParticles2"}]}, "Sorcerer/GoatCult/Hand5": {"rgba2": [{"time": 0.2333, "light": "ffffffff", "dark": "000000", "curve": [0.275, 1, 0.358, 1, 0.275, 1, 0.358, 1, 0.275, 1, 0.358, 1, 0.275, 1, 0.358, 0, 0.275, 0, 0.358, 0, 0.275, 0, 0.358, 0, 0.275, 0, 0.358, 0]}, {"time": 0.4, "light": "ffffff00", "dark": "000000"}]}, "spawn": {"attachment": [{"time": 0.1667, "name": "Other/Teleport_1"}, {"time": 0.2667, "name": "Other/Teleport_2"}, {"time": 0.3667, "name": "Other/Teleport_3"}, {"time": 0.4667, "name": "Other/Teleport_4"}, {"time": 0.5667}]}}, "bones": {"BODY_BTM": {"rotate": [{"value": -1.67, "curve": [0.025, -0.84, 0.05, 0]}, {"time": 0.0667, "curve": [0.15, 0, 0.317, -8.53]}, {"time": 0.4, "value": -8.53}], "translate": [{"curve": [0.017, 0, 0.05, -4.53, 0.017, 0, 0.05, 26.95]}, {"time": 0.0667, "x": -4.53, "y": 26.95, "curve": [0.15, -4.53, 0.317, 0.46, 0.15, 26.95, 0.317, -25.95]}, {"time": 0.4, "x": 0.46, "y": -25.95}], "scale": [{"curve": [0.017, 1, 0.05, 1.246, 0.017, 1, 0.05, 0.811]}, {"time": 0.0667, "x": 1.246, "y": 0.811, "curve": [0.15, 1.246, 0.317, 0.082, 0.15, 0.811, 0.317, 1]}, {"time": 0.4, "x": 0.082}]}, "HAND_LEFT": {"rotate": [{"value": -7.34}]}, "HAND_RIGHT": {"rotate": [{"value": -7.2}]}, "HEAD": {"rotate": [{"value": 1.32, "curve": [0.052, 0.96, 0.11, 0.48]}, {"time": 0.1667, "curve": [0.225, 0, 0.342, -16.33]}, {"time": 0.4, "value": -16.33}], "translate": [{"x": -10.67, "y": -0.35, "curve": [0.042, -10.67, 0.125, 12.4, 0.042, -0.35, 0.125, -0.36]}, {"time": 0.1667, "x": 12.4, "y": -0.36, "curve": [0.225, 12.4, 0.342, -205.63, 0.225, -0.36, 0.342, 74.76]}, {"time": 0.4, "x": -205.63, "y": 74.76}]}, "BODY_TOP": {"rotate": [{"time": 0.1667, "value": -5.68, "curve": [0.225, -5.68, 0.342, 23.84]}, {"time": 0.4, "value": 23.84}]}, "SLEEVE_LEFT": {"translate": [{"x": 11.1}]}, "SLEEVE_RIGHT": {"translate": [{"x": 7.6}]}, "spawn_particles": {"translate": [{"time": 0.1667, "y": 21.57, "curve": [0.333, 0, 0.667, 0, 0.333, 21.57, 0.667, 5.14]}, {"time": 0.8333, "y": 5.14}]}, "spawn_particles2": {"translate": [{"time": 0.1667, "y": 23.63, "curve": [0.333, 0, 0.667, 0, 0.333, 23.63, 0.667, -19.52]}, {"time": 0.8333, "y": -19.52}]}, "effects": {"translate": [{"x": -322.82, "y": -42.44}]}, "CAPE_CORNER_RIGHT": {"rotate": [{"value": 30.04}], "translate": [{"x": 6.97, "y": -2.09}]}, "CAPE_CORNER_LEFT": {"rotate": [{"value": -8.13}]}, "GOO_1": {"rotate": [{"value": 1.11}], "translate": [{"y": -4.63}]}, "GOO_2": {"rotate": [{"value": 3.32}], "translate": [{"x": 0.02, "y": -8.05}]}, "GOO_3": {"rotate": [{"value": 1.82}], "translate": [{"y": -6.01}]}, "NECKLACE_HANDLE": {"translate": [{"x": 9.61, "y": -0.56}]}, "NECKLACE": {"translate": [{"x": 3.65, "y": 2.07}]}, "ARM_LEFT_HANDLE": {"translate": [{"x": 5.01, "y": 0.59, "curve": [0.023, 39.14, 0.1, 95.61, 0.023, 6.51, 0.1, 16.29]}, {"time": 0.1667, "x": 95.61, "y": 16.29, "curve": [0.225, 95.61, 0.342, 152.55, 0.225, 16.29, 0.342, -89.45]}, {"time": 0.4, "x": 152.55, "y": -89.45}]}, "ARM_RIGHT_HANDLE": {"translate": [{"x": 2.39, "y": 0.21, "curve": [0.064, 29.76, 0.122, 49.31, 0.064, -2.38, 0.122, -4.23]}, {"time": 0.1667, "x": 49.31, "y": -4.23, "curve": [0.199, 49.31, 0.257, 85.36, 0.199, -4.23, 0.257, 3.88]}, {"time": 0.3, "x": 99.78, "y": 7.12, "curve": [0.338, 8.51, 0.372, -48.54, 0.338, 25.27, 0.372, 36.61]}, {"time": 0.4, "x": -48.54, "y": 36.61}]}, "FACE": {"translate": [{"x": 1.37, "y": -5.61, "curve": [0.044, 15.46, 0.089, 30.73, 0.044, -10.29, 0.089, -15.35]}, {"time": 0.1333, "x": 44.81, "y": -20.03}]}}, "drawOrder": [{"offsets": [{"slot": "Other/SpawnParticles", "offset": 26}, {"slot": "Other/SpawnParticles2", "offset": 26}, {"slot": "Other/SpawnParticles3", "offset": 26}]}]}, "teleport2": {"slots": {"Cape": {"rgba2": [{"time": 0.1333, "light": "ffffffff", "dark": "000000", "curve": [0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0]}, {"time": 0.2333, "light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 0.6333, "light": "ffffff00", "dark": "000000", "curve": [0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 0, 0.708, 1, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0]}, {"time": 0.7333, "light": "ffffffff", "dark": "000000"}]}, "Cape_Back": {"rgba2": [{"time": 0.1333, "light": "ffffffff", "dark": "000000", "curve": [0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0]}, {"time": 0.2333, "light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 0.6333, "light": "ffffff00", "dark": "000000", "curve": [0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 0, 0.708, 1, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0]}, {"time": 0.7333, "light": "ffffffff", "dark": "000000"}]}, "Eyes": {"rgba": [{"time": 0.1333, "color": "ffffffff", "curve": [0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 0]}, {"time": 0.2333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6333, "color": "ffffff00", "curve": [0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 0, 0.708, 1]}, {"time": 0.7333, "color": "ffffffff"}]}, "GoatCult/Arm": {"rgba2": [{"time": 0.1333, "light": "ffffffff", "dark": "000000", "curve": [0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0]}, {"time": 0.2333, "light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 0.6333, "light": "ffffff00", "dark": "000000", "curve": [0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 0, 0.708, 1, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0]}, {"time": 0.7333, "light": "ffffffff", "dark": "000000"}]}, "GoatCult/Arm2": {"rgba2": [{"time": 0.1333, "light": "ffffffff", "dark": "000000", "curve": [0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0]}, {"time": 0.2333, "light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 0.6333, "light": "ffffff00", "dark": "000000", "curve": [0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 0, 0.708, 1, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0]}, {"time": 0.7333, "light": "ffffffff", "dark": "000000"}]}, "GoatCult/Goo1": {"rgba2": [{"time": 0.1333, "light": "ffffffff", "dark": "000000", "curve": [0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0]}, {"time": 0.2333, "light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 0.6333, "light": "ffffff00", "dark": "000000", "curve": [0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 0, 0.708, 1, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0]}, {"time": 0.7333, "light": "ffffffff", "dark": "000000"}]}, "GoatCult/Goo2": {"rgba2": [{"time": 0.1333, "light": "ffffffff", "dark": "000000", "curve": [0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0]}, {"time": 0.2333, "light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 0.6333, "light": "ffffff00", "dark": "000000", "curve": [0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 0, 0.708, 1, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0]}, {"time": 0.7333, "light": "ffffffff", "dark": "000000"}]}, "GoatCult/Goo3": {"rgba2": [{"time": 0.1333, "light": "ffffffff", "dark": "000000", "curve": [0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0]}, {"time": 0.2333, "light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 0.6333, "light": "ffffff00", "dark": "000000", "curve": [0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 0, 0.708, 1, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0]}, {"time": 0.7333, "light": "ffffffff", "dark": "000000"}]}, "GoatCult/Goo4": {"rgba2": [{"time": 0.1333, "light": "ffffffff", "dark": "000000", "curve": [0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0]}, {"time": 0.2333, "light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 0.6333, "light": "ffffff00", "dark": "000000", "curve": [0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 0, 0.708, 1, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0]}, {"time": 0.7333, "light": "ffffffff", "dark": "000000"}]}, "GoatCult/Goo5": {"rgba2": [{"time": 0.1333, "light": "ffffffff", "dark": "000000", "curve": [0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0]}, {"time": 0.2333, "light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 0.6333, "light": "ffffff00", "dark": "000000", "curve": [0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 0, 0.708, 1, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0]}, {"time": 0.7333, "light": "ffffffff", "dark": "000000"}]}, "GoatCult/Goo6": {"rgba2": [{"time": 0.1333, "light": "ffffffff", "dark": "000000", "curve": [0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0]}, {"time": 0.2333, "light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 0.6333, "light": "ffffff00", "dark": "000000", "curve": [0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 0, 0.708, 1, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0]}, {"time": 0.7333, "light": "ffffffff", "dark": "000000"}]}, "GoatCult/Goo7": {"rgba2": [{"time": 0.1333, "light": "ffffffff", "dark": "000000", "curve": [0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0]}, {"time": 0.2333, "light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 0.6333, "light": "ffffff00", "dark": "000000", "curve": [0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 0, 0.708, 1, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0]}, {"time": 0.7333, "light": "ffffffff", "dark": "000000"}]}, "GoatCult/Hand1": {"rgba2": [{"time": 0.1333, "light": "ffffffff", "dark": "000000", "curve": [0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0]}, {"time": 0.2333, "light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 0.6333, "light": "ffffff00", "dark": "000000", "curve": [0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 0, 0.708, 1, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0]}, {"time": 0.7333, "light": "ffffffff", "dark": "000000"}]}, "GoatCult/Hand2": {"rgba2": [{"time": 0.1333, "light": "ffffffff", "dark": "000000", "curve": [0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0]}, {"time": 0.2333, "light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 0.6333, "light": "ffffff00", "dark": "000000", "curve": [0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 0, 0.708, 1, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0]}, {"time": 0.7333, "light": "ffffffff", "dark": "000000"}]}, "GoatCult/Hood": {"rgba2": [{"time": 0.1333, "light": "ffffffff", "dark": "000000", "curve": [0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0]}, {"time": 0.2333, "light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 0.6333, "light": "ffffff00", "dark": "000000", "curve": [0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 0, 0.708, 1, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0]}, {"time": 0.7333, "light": "ffffffff", "dark": "000000"}]}, "GoatCult/HornLeft": {"rgba2": [{"time": 0.1333, "light": "ffffffff", "dark": "000000", "curve": [0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0]}, {"time": 0.2333, "light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 0.6333, "light": "ffffff00", "dark": "000000", "curve": [0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 0, 0.708, 1, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0]}, {"time": 0.7333, "light": "ffffffff", "dark": "000000"}]}, "GoatCult/HornRight": {"rgba2": [{"time": 0.1333, "light": "ffffffff", "dark": "000000", "curve": [0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0]}, {"time": 0.2333, "light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 0.6333, "light": "ffffff00", "dark": "000000", "curve": [0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 0, 0.708, 1, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0]}, {"time": 0.7333, "light": "ffffffff", "dark": "000000"}]}, "GoatCult/Mask": {"rgba2": [{"time": 0.1333, "light": "ffffffff", "dark": "000000", "curve": [0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0]}, {"time": 0.2333, "light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 0.6333, "light": "ffffff00", "dark": "000000", "curve": [0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 0, 0.708, 1, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0]}, {"time": 0.7333, "light": "ffffffff", "dark": "000000"}], "attachment": [{"time": 0.0667}, {"time": 0.6667}]}, "GoatCult/Necklace": {"rgba2": [{"time": 0.1333, "light": "ffffffff", "dark": "000000", "curve": [0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0]}, {"time": 0.2333, "light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 0.6333, "light": "ffffff00", "dark": "000000", "curve": [0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 0, 0.708, 1, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0]}, {"time": 0.7333, "light": "ffffffff", "dark": "000000"}]}, "GoatCult/Sleeve": {"rgba2": [{"time": 0.1333, "light": "ffffffff", "dark": "000000", "curve": [0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0]}, {"time": 0.2333, "light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 0.6333, "light": "ffffff00", "dark": "000000", "curve": [0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 0, 0.708, 1, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0]}, {"time": 0.7333, "light": "ffffffff", "dark": "000000"}]}, "GoatCult/Sleeve2": {"rgba2": [{"time": 0.1333, "light": "ffffffff", "dark": "000000", "curve": [0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0]}, {"time": 0.2333, "light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 0.6333, "light": "ffffff00", "dark": "000000", "curve": [0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 0, 0.708, 1, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0]}, {"time": 0.7333, "light": "ffffffff", "dark": "000000"}]}, "GoatCult/Weapon": {"rgba2": [{"time": 0.1333, "light": "ffffffff", "dark": "000000", "curve": [0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0]}, {"time": 0.2333, "light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 0.6333, "light": "ffffff00", "dark": "000000", "curve": [0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 0, 0.708, 1, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0]}, {"time": 0.7333, "light": "ffffffff", "dark": "000000"}]}, "Hood Btm": {"rgba2": [{"time": 0.1333, "light": "ffffffff", "dark": "000000", "curve": [0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0]}, {"time": 0.2333, "light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 0.6333, "light": "ffffff00", "dark": "000000", "curve": [0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 0, 0.708, 1, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0]}, {"time": 0.7333, "light": "ffffffff", "dark": "000000"}]}, "Other/SpawnParticles": {"rgba": [{"time": 0.1, "color": "ffffff00", "curve": [0.106, 1, 0.123, 1, 0.106, 1, 0.123, 1, 0.106, 1, 0.123, 1, 0.106, 0.48, 0.123, 0.71]}, {"time": 0.1333, "color": "ffffffb6", "curve": [0.225, 1, 0.408, 1, 0.225, 1, 0.408, 1, 0.225, 1, 0.408, 1, 0.225, 0.71, 0.408, 0]}, {"time": 0.5, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00", "curve": [0.684, 1, 0.706, 1, 0.684, 1, 0.706, 1, 0.684, 1, 0.706, 1, 0.684, 0.27, 0.706, 0.52]}, {"time": 0.7333, "color": "ffffffc0", "curve": [0.823, 1, 0.991, 1, 0.823, 1, 0.991, 1, 0.823, 1, 0.991, 1, 0.823, 0.24, 0.991, 0]}, {"time": 1.1, "color": "ffffff00"}]}, "Other/SpawnParticles2": {"rgba": [{"time": 0.1, "color": "ffffff00", "curve": [0.106, 1, 0.123, 1, 0.106, 1, 0.123, 1, 0.106, 1, 0.123, 1, 0.106, 0.48, 0.123, 0.71]}, {"time": 0.1333, "color": "ffffffb6", "curve": [0.225, 1, 0.408, 1, 0.225, 1, 0.408, 1, 0.225, 1, 0.408, 1, 0.225, 0.71, 0.408, 0]}, {"time": 0.5, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00", "curve": [0.678, 1, 0.712, 1, 0.678, 1, 0.712, 1, 0.678, 1, 0.712, 1, 0.678, 0.51, 0.712, 0.75]}, {"time": 0.7333, "color": "ffffffc0", "curve": [0.825, 1, 1.008, 1, 0.825, 1, 1.008, 1, 0.825, 1, 1.008, 1, 0.825, 0.75, 1.008, 0]}, {"time": 1.1, "color": "ffffff00"}]}, "Other/SpawnParticles3": {"rgba": [{"time": 0.1, "color": "ffffff00", "curve": [0.106, 1, 0.123, 1, 0.106, 1, 0.123, 1, 0.106, 1, 0.123, 1, 0.106, 0.48, 0.123, 0.71]}, {"time": 0.1333, "color": "ffffffb6", "curve": [0.225, 1, 0.408, 1, 0.225, 1, 0.408, 1, 0.225, 1, 0.408, 1, 0.225, 0.71, 0.408, 0]}, {"time": 0.5, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00", "curve": [0.678, 1, 0.712, 1, 0.678, 1, 0.712, 1, 0.678, 1, 0.712, 1, 0.678, 0.51, 0.712, 0.75]}, {"time": 0.7333, "color": "ffffffc0", "curve": [0.825, 1, 1.008, 1, 0.825, 1, 1.008, 1, 0.825, 1, 1.008, 1, 0.825, 0.75, 1.008, 0]}, {"time": 1.1, "color": "ffffff00"}]}, "Sorcerer/Cult/Medic_Ring": {"rgba": [{"time": 0.1333, "color": "ffffffff"}, {"time": 0.2333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6333, "color": "ffffff00"}, {"time": 0.7333, "color": "ffffffff"}]}, "Sorcerer/GoatCult/Hand5": {"rgba2": [{"time": 0.1333, "light": "ffffffff", "dark": "000000", "curve": [0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 1, 0.158, 1, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0, 0.158, 0, 0.208, 0]}, {"time": 0.2333, "light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 0.6333, "light": "ffffff00", "dark": "000000", "curve": [0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 1, 0.708, 1, 0.658, 0, 0.708, 1, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0, 0.658, 0, 0.708, 0]}, {"time": 0.7333, "light": "ffffffff", "dark": "000000"}]}}, "bones": {"BODY_BTM": {"rotate": [{"value": -1.67, "curve": [0.013, -0.84, 0.025, 0]}, {"time": 0.0333, "curve": [0.083, 0, 0.183, -8.53]}, {"time": 0.2333, "value": -8.53, "curve": "stepped"}, {"time": 0.6667, "value": -8.53, "curve": [0.689, -2.76, 0.758, 0]}, {"time": 0.8, "curve": [0.85, 0, 0.95, -2.99]}, {"time": 1, "value": -2.99, "curve": [1.05, -2.99, 1.125, -2.33]}, {"time": 1.2, "value": -1.67}], "translate": [{"curve": [0.017, 0, 0.05, 2.2, 0.017, 0, 0.05, 30.91]}, {"time": 0.0667, "x": 2.2, "y": 30.91, "curve": [0.118, 2.91, 0.193, 5.05, 0.118, 17.2, 0.193, -23.91]}, {"time": 0.2333, "x": 5.05, "y": -23.91, "curve": [0.305, 1.95, 0.53, 0.46, 0.305, -25.29, 0.53, -25.95]}, {"time": 0.6667, "x": 0.46, "y": -25.95, "curve": [0.689, -0.58, 0.758, -1.08, 0.689, 41.14, 0.758, 73.29]}, {"time": 0.8, "x": -1.08, "y": 73.29, "curve": [0.85, -1.08, 0.95, 1.76, 0.85, 73.29, 0.95, -15.31]}, {"time": 1, "x": 1.76, "y": -15.31, "curve": [1.05, 1.76, 1.15, 0, 1.05, -15.31, 1.15, 0]}, {"time": 1.2}], "scale": [{"y": 1.015, "curve": [0.008, 1, 0.025, 1.246, 0.008, 1.015, 0.025, 0.811]}, {"time": 0.0333, "x": 1.246, "y": 0.811, "curve": [0.083, 1.246, 0.183, 0.082, 0.083, 0.811, 0.183, 1]}, {"time": 0.2333, "x": 0.082, "curve": "stepped"}, {"time": 0.6667, "x": 0.082, "curve": [0.689, 0.869, 0.758, 1.246, 0.689, 0.872, 0.758, 0.811]}, {"time": 0.8, "x": 1.246, "y": 0.811, "curve": [0.85, 1.246, 0.95, 0.599, 0.85, 0.811, 0.95, 1.236]}, {"time": 1, "x": 0.599, "y": 1.236, "curve": [1.05, 0.599, 1.15, 1, 1.05, 1.236, 1.15, 1.015]}, {"time": 1.2, "y": 1.015}]}, "FACE": {"translate": [{"x": 3.05, "y": -0.15, "curve": [0.024, 21.61, 0.05, 44.81, 0.024, -8.98, 0.05, -20.03]}, {"time": 0.0667, "x": 44.81, "y": -20.03, "curve": "stepped"}, {"time": 0.6667, "x": 44.81, "y": -20.03, "curve": [0.692, 8.66, 0.716, -21.47, 0.692, -10.59, 0.716, -2.72]}, {"time": 0.7333, "x": -21.47, "y": -2.72, "curve": [0.81, -7.38, 0.89, 7.89, 0.81, -3.57, 0.89, -4.49]}, {"time": 0.9667, "x": 21.98, "y": -5.34, "curve": [1.044, 15.72, 1.124, 8.85, 1.044, -3.62, 1.124, -1.74]}, {"time": 1.2, "x": 3.05, "y": -0.15}], "scale": [{"time": 0.6667, "x": 0.57}, {"time": 0.7333}]}, "SHOULDER_LEFT": {"rotate": [{"time": 0.8333, "value": 70.23}]}, "SHOULDER_RIGHT": {"rotate": [{"time": 0.8333, "value": -43.34}]}, "HAND_LEFT": {"rotate": [{"value": -7.34, "curve": [0.295, -5.87, 0.63, -3.76]}, {"time": 0.8333, "value": -3.76, "curve": [0.906, -6.75, 0.984, -10.56]}, {"time": 1.0333, "value": -10.56, "curve": [1.068, -10.56, 1.116, -9.16]}, {"time": 1.1667, "value": -7.34}]}, "HAND_RIGHT": {"rotate": [{"value": -7.2, "curve": [0.318, -6.3, 0.609, -5.66]}, {"time": 0.8333, "value": -5.66, "curve": [0.894, -9.82, 0.959, -14.9]}, {"time": 1, "value": -14.9, "curve": [1.057, -12.19, 1.113, -9.48]}, {"time": 1.1667, "value": -7.2}]}, "HEAD": {"rotate": [{"value": 1.32, "curve": [0.031, 0.96, 0.066, 0.48]}, {"time": 0.1, "curve": [0.133, 0, 0.2, -16.33]}, {"time": 0.2333, "value": -16.33, "curve": [0.305, 10.7, 0.53, 23.66]}, {"time": 0.6667, "value": 23.66, "curve": [0.689, 7.66, 0.758, 0]}, {"time": 0.8, "curve": [0.808, 0, 0.825, 1.8]}, {"time": 0.8333, "value": 1.8, "curve": [0.896, 0.9, 0.958, 0]}, {"time": 1, "curve": [1.046, 0, 1.103, 0.53]}, {"time": 1.1667, "value": 1.32}], "translate": [{"x": -5.85, "y": -0.18, "curve": [0.038, 4.1, 0.074, 12.4, 0.038, -0.28, 0.074, -0.36]}, {"time": 0.1, "x": 12.4, "y": -0.36, "curve": [0.133, 12.4, 0.2, -205.63, 0.133, -0.36, 0.2, 74.76]}, {"time": 0.2333, "x": -205.63, "y": 74.76, "curve": "stepped"}, {"time": 0.6667, "x": -205.63, "y": 74.76, "curve": [0.689, -58.23, 0.758, 12.4, 0.689, 23.97, 0.758, -0.36]}, {"time": 0.8, "x": 12.4, "y": -0.36, "curve": [0.808, 12.4, 0.825, 2.43, 0.808, -0.36, 0.825, 0.11]}, {"time": 0.8333, "x": 2.43, "y": 0.11, "curve": [0.875, 2.43, 0.938, -0.84, 0.875, 0.11, 0.938, -0.01]}, {"time": 1, "x": -4.12, "y": -0.12, "curve": [1.023, -19.19, 1.045, -34.27, 1.023, 1.42, 1.045, 2.96]}, {"time": 1.0667, "x": -46.93, "y": 4.25, "curve": [1.105, -25.78, 1.14, -10.67, 1.105, 1.57, 1.14, -0.35]}, {"time": 1.1667, "x": -10.67, "y": -0.35, "curve": [1.175, -10.67, 1.188, -8.26, 1.175, -0.35, 1.188, -0.27]}, {"time": 1.2, "x": -5.85, "y": -0.18}]}, "HORN_L": {"rotate": [{"value": -13.44, "curve": [0.322, -6.95, 0.652, 0]}, {"time": 0.8667, "curve": [0.875, 0, 0.892, 26.24]}, {"time": 0.9, "value": 26.24, "curve": [0.933, 26.24, 1, -51.46]}, {"time": 1.0333, "value": -51.46, "curve": [1.075, -51.46, 1.158, -13.44]}, {"time": 1.2, "value": -13.44}]}, "HORN_R": {"rotate": [{"value": 12.05, "curve": [0.322, 6.23, 0.652, 0]}, {"time": 0.8667, "curve": [0.875, 0, 0.892, -27.88]}, {"time": 0.9, "value": -27.88, "curve": [0.933, -27.88, 1, 37.82]}, {"time": 1.0333, "value": 37.82, "curve": [1.075, 37.82, 1.158, 12.05]}, {"time": 1.2, "value": 12.05}]}, "BODY_TOP": {"rotate": [{}, {"time": 0.1, "value": -5.68, "curve": [0.133, -5.68, 0.2, 23.84]}, {"time": 0.2333, "value": 23.84, "curve": "stepped"}, {"time": 0.6667, "value": 23.84, "curve": [0.689, 3.89, 0.758, -5.68]}, {"time": 0.8, "value": -5.68, "curve": [0.808, -5.68, 0.825, 0]}, {"time": 0.8333}]}, "SLEEVE_LEFT": {"rotate": [{"value": -27.05, "curve": [0.208, -27.05, 0.625, 0]}, {"time": 0.8333, "curve": [0.925, 0, 1.108, -27.05]}, {"time": 1.2, "value": -27.05}], "translate": [{"x": 11.1, "curve": [0.312, 3.06, 0.595, -1.9, 0.312, 0, 0.595, 0]}, {"time": 0.8333, "x": -1.9, "curve": [0.858, -2.74, 0.88, -3.23, 0.858, 0, 0.88, 0]}, {"time": 0.9, "x": -3.23, "curve": [0.973, -3.23, 1.105, 7.15, 0.973, 0, 1.105, 0]}, {"time": 1.2, "x": 11.1}]}, "SLEEVE_RIGHT": {"rotate": [{"value": -21.61, "curve": [0.208, -21.61, 0.625, 0]}, {"time": 0.8333, "curve": [0.925, 0, 1.108, -21.61]}, {"time": 1.2, "value": -21.61}], "translate": [{"x": 7.6, "curve": [0.269, 4.2, 0.631, -3.88, 0.269, 0, 0.631, 0]}, {"time": 0.8333, "x": -3.88, "curve": [0.933, 0.51, 1.061, 9.28, 0.933, 0, 1.061, 0]}, {"time": 1.1333, "x": 9.28, "curve": [1.152, 9.28, 1.175, 8.63, 1.152, 0, 1.175, 0]}, {"time": 1.2, "x": 7.6}]}, "spawn_particles": {"translate": [{"time": 0.1, "y": 21.57, "curve": [0.2, 0, 0.4, 0, 0.2, 21.57, 0.4, 5.14]}, {"time": 0.5, "y": 5.14, "curve": "stepped"}, {"time": 0.6667, "y": 5.14, "curve": [0.755, 0, 1.032, 0, 0.755, 16.25, 1.032, 21.57]}, {"time": 1.2, "y": 21.57}]}, "spawn_particles2": {"translate": [{"time": 0.1, "y": 23.63, "curve": [0.2, 0, 0.4, 0, 0.2, 23.63, 0.4, -19.52]}, {"time": 0.5, "y": -19.52, "curve": "stepped"}, {"time": 0.6667, "y": -19.52, "curve": [0.755, 0, 1.032, 0, 0.755, 9.65, 1.032, 23.63]}, {"time": 1.2, "y": 23.63}]}, "CAPE_CORNER_RIGHT": {"rotate": [{"value": 30.04, "curve": "stepped"}, {"time": 0.8333, "value": 30.04, "curve": [0.875, 30.04, 0.958, 0]}, {"time": 1, "curve": [1.05, 0, 1.15, 30.04]}, {"time": 1.2, "value": 30.04}], "translate": [{"x": 6.97, "y": -2.09, "curve": [0.254, 3.22, 0.496, 0, 0.254, -0.96, 0.496, 0]}, {"time": 0.6667, "curve": [0.708, 0, 0.792, 5.31, 0.708, 0, 0.792, 29.02]}, {"time": 0.8333, "x": 5.31, "y": 29.02, "curve": [0.923, 12.35, 1.006, 17.87, 0.923, 9.77, 1.006, -5.36]}, {"time": 1.0667, "x": 17.87, "y": -5.36, "curve": [1.091, 17.87, 1.13, 11.93, 1.091, -5.36, 1.13, -3.58]}, {"time": 1.1667, "x": 6.97, "y": -2.09}]}, "CAPE_CORNER_LEFT": {"rotate": [{"value": -8.13, "curve": [0.313, -7.64, 0.625, -7.15]}, {"time": 0.8333, "value": -7.15, "curve": [0.859, -3.42, 0.883, 0]}, {"time": 0.9, "curve": [0.958, 0, 1.075, -16.25]}, {"time": 1.1333, "value": -16.25, "curve": [1.15, -16.25, 1.175, -12.19]}, {"time": 1.2, "value": -8.13}], "translate": [{"time": 0.8333, "curve": [0.875, 0, 0.958, 12.36, 0.875, 0, 0.958, 14.88]}, {"time": 1, "x": 12.36, "y": 14.88, "curve": [1.05, 12.36, 1.15, 0, 1.05, 14.88, 1.15, 0]}, {"time": 1.2}]}, "GOO_1": {"rotate": [{"value": -15.24, "curve": [0.203, -11.34, 0.434, -5.21]}, {"time": 0.6667, "value": 1.11, "curve": [0.729, 1.27, 0.792, 1.43]}, {"time": 0.8333, "value": 1.43, "curve": [0.908, 0.11, 0.984, -1.28]}, {"time": 1.0333, "value": -1.28, "curve": [1.075, -1.28, 1.137, -8.08]}, {"time": 1.2, "value": -15.24}], "translate": [{"y": -4.63, "curve": [0.261, 0, 0.526, 0, 0.261, -2.38, 0.526, 0]}, {"time": 0.7, "curve": [0.706, 1.19, 0.725, 1.84, 0.706, 17, 0.725, 26.23]}, {"time": 0.7333, "x": 1.84, "y": 26.23, "curve": [0.758, 1.84, 0.808, 0, 0.758, 26.23, 0.808, -5.26]}, {"time": 0.8333, "y": -5.26, "curve": [0.908, 0, 0.984, 0, 0.908, -2.69, 0.984, 0]}, {"time": 1.0333, "curve": [1.075, 0, 1.137, 0, 1.075, 0, 1.137, -2.26]}, {"time": 1.2, "y": -4.63}]}, "GOO_2": {"rotate": [{"value": 2.37, "curve": [0.252, 2.86, 0.499, 3.32]}, {"time": 0.6667, "value": 3.32, "curve": [0.729, 0.9, 0.785, -0.56]}, {"time": 0.8333, "value": -0.56, "curve": [0.858, -1, 0.881, -1.28]}, {"time": 0.9, "value": -1.28, "curve": [0.973, -1.28, 1.106, 1.43]}, {"time": 1.2, "value": 2.37}], "translate": [{"x": 0.02, "y": -8.05, "curve": [0.26, -3.47, 0.498, -5.59, 0.26, 5.49, 0.498, 13.68]}, {"time": 0.7, "x": -5.59, "y": 13.68, "curve": [0.706, -1.97, 0.725, 0, 0.706, -0.62, 0.725, -8.39]}, {"time": 0.7333, "y": -8.39, "curve": [0.758, 0, 0.808, 0.17, 0.758, -8.39, 0.808, 6.06]}, {"time": 0.8333, "x": 0.17, "y": 6.06, "curve": [0.858, 0.18, 0.881, 0.19, 0.858, 7.65, 0.881, 8.65]}, {"time": 0.9, "x": 0.19, "y": 8.65, "curve": [0.973, 0.19, 1.106, 0.06, 0.973, 8.65, 1.106, -3.74]}, {"time": 1.2, "x": 0.02, "y": -8.05}]}, "GOO_3": {"rotate": [{"value": 4.3, "curve": [0.252, 3.02, 0.499, 1.82]}, {"time": 0.6667, "value": 1.82, "curve": [0.73, 1.14, 0.791, 0.55]}, {"time": 0.8333, "value": 0.55, "curve": [0.897, -0.45, 0.957, -1.28]}, {"time": 1, "value": -1.28, "curve": [1.049, -1.28, 1.127, 1.75]}, {"time": 1.2, "value": 4.3}], "translate": [{"y": -6.01, "curve": [0.266, 0, 0.521, 0, 0.266, -2.79, 0.521, 0]}, {"time": 0.7, "curve": [0.706, 0, 0.725, 0, 0.706, 20.99, 0.725, 32.4]}, {"time": 0.7333, "y": 32.4, "curve": [0.758, 0, 0.808, 0, 0.758, 32.4, 0.808, -3.55]}, {"time": 0.8333, "y": -3.55, "curve": [0.897, 0, 0.957, 0, 0.897, -1.6, 0.957, 0]}, {"time": 1, "curve": [1.049, 0, 1.127, 0, 1.049, 0, 1.127, -3.26]}, {"time": 1.2, "y": -6.01}]}, "NECKLACE_HANDLE": {"translate": [{"y": 0.34, "curve": [0.285, 7.73, 0.576, 16, 0.285, 8.41, 0.576, 17.06]}, {"time": 0.7667, "x": 16, "y": 17.06, "curve": [0.783, 16, 0.817, 10.59, 0.783, 17.06, 0.817, 46.67]}, {"time": 0.8333, "x": 10.59, "y": 46.67, "curve": [0.867, 10.59, 0.933, 16.22, 0.867, 46.67, 0.933, -24.46]}, {"time": 0.9667, "x": 16.22, "y": -24.46, "curve": [1.025, 16.22, 1.142, 0, 1.025, -24.46, 1.142, 0.34]}, {"time": 1.2, "y": 0.34}]}, "NECKLACE": {"translate": [{"x": 3.65, "y": 2.07}]}, "ARM_LEFT_HANDLE": {"translate": [{"x": 5.01, "y": 0.59, "curve": [0.014, 39.14, 0.06, 95.61, 0.014, 6.51, 0.06, 16.29]}, {"time": 0.1, "x": 95.61, "y": 16.29, "curve": [0.133, 95.61, 0.2, 152.55, 0.133, 16.29, 0.2, -89.45]}, {"time": 0.2333, "x": 152.55, "y": -89.45, "curve": [0.305, -214.84, 0.53, -390.88, 0.305, 35.88, 0.53, 95.94]}, {"time": 0.6667, "x": -390.88, "y": 95.94, "curve": [0.679, -210.55, 0.705, -77.18, 0.679, 51.22, 0.705, 18.15]}, {"time": 0.7333, "x": 11.1, "y": -3.74, "curve": [0.756, 80.26, 0.781, 114.11, 0.756, 4.17, 0.781, 8.04]}, {"time": 0.8, "x": 114.11, "y": 8.04, "curve": [0.85, 114.11, 0.946, 83.17, 0.85, 8.04, 0.946, 1.96]}, {"time": 1, "x": -15.09, "y": -17.33, "curve": [1.075, -5.04, 1.15, 5.01, 1.075, -8.37, 1.15, 0.59]}, {"time": 1.2, "x": 5.01, "y": 0.59}]}, "ARM_RIGHT_HANDLE": {"translate": [{"x": 2.39, "y": 0.21, "curve": [0.038, 29.76, 0.073, 49.31, 0.038, -2.38, 0.073, -4.23]}, {"time": 0.1, "x": 49.31, "y": -4.23, "curve": [0.116, 49.31, 0.145, 85.36, 0.116, -4.23, 0.145, 3.88]}, {"time": 0.1667, "x": 99.78, "y": 7.12, "curve": [0.192, 8.51, 0.214, -48.54, 0.192, 25.27, 0.214, 36.61]}, {"time": 0.2333, "x": -48.54, "y": 36.61, "curve": [0.305, -282.78, 0.53, -395.03, 0.305, 134.9, 0.53, 182]}, {"time": 0.6667, "x": -395.03, "y": 182, "curve": [0.679, -218.11, 0.705, -87.27, 0.679, 102.91, 0.705, 44.42]}, {"time": 0.7333, "x": -0.66, "y": 5.7, "curve": [0.756, 69.01, 0.781, 103.11, 0.756, -4.19, 0.781, -9.02]}, {"time": 0.8, "x": 103.11, "y": -9.02, "curve": [0.85, 103.11, 0.946, 73.99, 0.85, -9.02, 0.946, -1.89]}, {"time": 1, "x": -18.51, "y": 20.77, "curve": [1.076, -7.67, 1.149, 2.39, 1.076, 10.11, 1.149, 0.21]}, {"time": 1.2, "x": 2.39, "y": 0.21}]}, "effects": {"translate": [{"x": -322.82, "y": -42.44, "curve": [0.029, -322.82, 0.063, -297.8, 0.029, -42.44, 0.063, -42.44]}, {"time": 0.1, "x": -255.25, "y": -42.44, "curve": [0.263, -247.33, 0.503, -220.94, 0.263, -42.44, 0.503, -42.44]}, {"time": 0.6333, "x": -220.94, "y": -42.44, "curve": [0.644, -244.45, 0.659, -322.82, 0.644, -42.44, 0.659, -42.44]}, {"time": 0.6667, "x": -322.82, "y": -42.44}]}, "root": {"rotate": [{"time": 0.6667, "value": -0.11}]}, "ARM_LEFT": {"rotate": [{"time": 0.8333, "value": -74.74}]}, "Necklace2": {"translate": [{"x": 1.89, "y": 11.25, "curve": [0.246, 1.35, 0.581, 0, 0.246, 8.04, 0.581, 0]}, {"time": 0.7667, "curve": [0.783, 0, 0.817, -6.92, 0.783, 0, 0.817, 55.37]}, {"time": 0.8333, "x": -6.92, "y": 55.37, "curve": [0.875, -6.92, 0.958, 5.75, 0.875, 55.37, 0.958, -5.76]}, {"time": 1, "x": 5.75, "y": -5.76, "curve": [1.05, 5.75, 1.15, 1.89, 1.05, -5.76, 1.15, 11.25]}, {"time": 1.2, "x": 1.89, "y": 11.25}]}, "NECK1": {"rotate": [{"time": 0.8333, "value": 1.9}]}, "Necklace4": {"translate": [{"x": 0.28, "y": 1.69, "curve": [0.288, 0.11, 0.549, 0, 0.288, 0.65, 0.549, 0]}, {"time": 0.7667, "curve": [0.783, 0, 0.817, -6.92, 0.783, 0, 0.817, 55.37]}, {"time": 0.8333, "x": -6.92, "y": 55.37, "curve": [0.858, -6.92, 0.908, 0.22, 0.858, 55.37, 0.908, -37.31]}, {"time": 0.9333, "x": 0.22, "y": -37.31, "curve": [1, 0.22, 1.133, 0.28, 1, -37.31, 1.133, 1.69]}, {"time": 1.2, "x": 0.28, "y": 1.69}]}, "NECK2": {"rotate": [{"time": 0.8333, "value": 8.62}]}, "ARM_RIGHT": {"rotate": [{"time": 0.8333, "value": -79.49}]}, "HEAD_TOP": {"rotate": [{"value": -4.72, "curve": [0.063, 8.17, 0.125, 20.26]}, {"time": 0.1667, "value": 20.26}, {"time": 0.8, "curve": [0.825, 0, 0.875, 25.29]}, {"time": 0.9, "value": 25.29, "curve": [0.925, 25.29, 0.975, -34.67]}, {"time": 1, "value": -34.67, "curve": [1.05, -34.67, 1.15, -4.72]}, {"time": 1.2, "value": -4.72}]}}, "drawOrder": [{"offsets": [{"slot": "Other/SpawnParticles", "offset": 26}, {"slot": "Other/SpawnParticles2", "offset": 26}, {"slot": "Other/SpawnParticles3", "offset": 26}]}], "events": [{"time": 0.5333, "name": "Teleport"}]}, "wake-up": {"slots": {"GoatCult/Hand1": {"attachment": [{"name": "Hand5"}, {"time": 0.3333, "name": "Hand1"}]}, "GoatCult/Hand2": {"attachment": [{"name": "Hand5"}, {"time": 0.3333, "name": "Hand1"}]}, "GoatCult/Mask": {"attachment": [{}, {"time": 0.2}]}}, "bones": {"HAND_LEFT": {"rotate": [{"value": 13.24, "curve": [0.05, 13.24, 0.15, -3.76]}, {"time": 0.2, "value": -3.76, "curve": [0.296, -6.75, 0.401, -10.56]}, {"time": 0.4667, "value": -10.56, "curve": [0.51, -10.56, 0.57, -9.16]}, {"time": 0.6333, "value": -7.34}]}, "HAND_RIGHT": {"rotate": [{"value": -7.2, "curve": [0.05, -7.2, 0.15, -5.66]}, {"time": 0.2, "value": -5.66, "curve": [0.285, -9.82, 0.376, -14.9]}, {"time": 0.4333, "value": -14.9, "curve": [0.501, -12.19, 0.569, -9.48]}, {"time": 0.6333, "value": -7.2}]}, "HEAD": {"rotate": [{"value": -17.51, "curve": [0.063, -8.47, 0.125, 0]}, {"time": 0.1667, "curve": [0.175, 0, 0.192, 1.8]}, {"time": 0.2, "value": 1.8, "curve": [0.275, 0.9, 0.35, 0]}, {"time": 0.4, "curve": [0.464, 0, 0.545, 0.53]}, {"time": 0.6333, "value": 1.32}], "translate": [{"x": -34.46, "y": 12.52, "curve": [0.042, -34.46, 0.125, 12.4, 0.042, 12.52, 0.125, -0.36]}, {"time": 0.1667, "x": 12.4, "y": -0.36, "curve": [0.175, 12.4, 0.192, 2.43, 0.175, -0.36, 0.192, 0.11]}, {"time": 0.2, "x": 2.43, "y": 0.11, "curve": [0.308, 2.43, 0.525, -10.67, 0.308, 0.11, 0.525, -0.35]}, {"time": 0.6333, "x": -10.67, "y": -0.35}]}, "BODY_BTM": {"rotate": [{"value": -1.67, "curve": [0.063, -0.84, 0.125, 0]}, {"time": 0.1667, "curve": [0.233, 0, 0.367, -2.99]}, {"time": 0.4333, "value": -2.99, "curve": [0.492, -2.99, 0.579, -2.33]}, {"time": 0.6667, "value": -1.67}], "translate": [{"x": 10.13, "y": -73.27, "curve": [0.042, 10.13, 0.125, -1.08, 0.042, -73.27, 0.125, 73.29]}, {"time": 0.1667, "x": -1.08, "y": 73.29, "curve": [0.233, -1.08, 0.367, 1.76, 0.233, 73.29, 0.367, -15.31]}, {"time": 0.4333, "x": 1.76, "y": -15.31, "curve": [0.492, 1.76, 0.608, 0, 0.492, -15.31, 0.608, 0]}, {"time": 0.6667}], "scale": [{"x": 0.684, "y": 1.103, "curve": [0.042, 0.684, 0.125, 1.246, 0.042, 1.103, 0.125, 0.811]}, {"time": 0.1667, "x": 1.246, "y": 0.811, "curve": [0.233, 1.246, 0.367, 0.746, 0.233, 0.811, 0.367, 1.089]}, {"time": 0.4333, "x": 0.746, "y": 1.089, "curve": [0.492, 0.746, 0.608, 1, 0.492, 1.089, 0.608, 1]}, {"time": 0.6667}]}, "ARM_LEFT_HANDLE": {"translate": [{"x": -41, "y": -31.46, "curve": [0.025, -41, 0.064, 41.01, 0.025, -31.46, 0.064, -26.61]}, {"time": 0.1, "x": 106.62, "y": -22.74, "curve": [0.125, 100.62, 0.149, 95.61, 0.125, -1.45, 0.149, 16.29]}, {"time": 0.1667, "x": 95.61, "y": 16.29, "curve": [0.292, 95.61, 0.542, 5.01, 0.292, 16.29, 0.542, 0.59]}, {"time": 0.6667, "x": 5.01, "y": 0.59}]}, "ARM_RIGHT_HANDLE": {"translate": [{"x": -102.17, "y": 47.59, "curve": [0.025, -102.17, 0.075, 100.33, 0.025, 47.59, 0.075, -8.63]}, {"time": 0.1, "x": 100.33, "y": -8.63, "curve": [0.12, 100.33, 0.142, 101.06, 0.12, -8.63, 0.142, -9.3]}, {"time": 0.1667, "x": 102.33, "y": -10.48, "curve": [0.314, 83.3, 0.544, 2.39, 0.314, -8.45, 0.544, 0.21]}, {"time": 0.6667, "x": 2.39, "y": 0.21}]}, "FACE": {"translate": [{"x": -52.45, "y": -10.74, "curve": [0.033, -52.45, 0.1, -10.81, 0.033, -10.74, 0.1, 4.15]}, {"time": 0.1333, "x": -10.81, "y": 4.15, "curve": [0.157, -5.07, 0.181, -2.26, 0.157, 0.45, 0.181, -1.37]}, {"time": 0.2, "x": -2.26, "y": -1.37, "curve": [0.297, 8.86, 0.401, 22.44, 0.297, -4.13, 0.401, -7.5]}, {"time": 0.4667, "x": 22.44, "y": -7.5, "curve": [0.518, 22.44, 0.59, 12.87, 0.518, -7.5, 0.59, -6.65]}, {"time": 0.6667, "x": 1.37, "y": -5.61}]}, "BODY_TOP": {"rotate": [{"value": 5.9, "curve": [0.042, 5.9, 0.125, -5.68]}, {"time": 0.1667, "value": -5.68}]}, "effects": {"translate": [{"x": -322.82, "y": -42.44}]}, "spawn_particles": {"translate": [{"y": 5.14, "curve": [0.033, 0, 0.137, 0, 0.033, 16.25, 0.137, 21.57]}, {"time": 0.2, "y": 21.57}]}, "GOO_2": {"rotate": [{"value": 36.63, "curve": [0.05, 36.63, 0.15, -0.56]}, {"time": 0.2, "value": -0.56, "curve": [0.238, -1, 0.272, -1.28]}, {"time": 0.3, "value": -1.28, "curve": [0.389, -1.28, 0.552, 2.14]}, {"time": 0.6667, "value": 3.32}], "translate": [{"x": 6.37, "y": -15.22, "curve": [0.008, 6.37, 0.025, -5.59, 0.008, -15.22, 0.025, 13.68]}, {"time": 0.0333, "x": -5.59, "y": 13.68, "curve": [0.039, -1.97, 0.058, 0, 0.039, -0.62, 0.058, -8.39]}, {"time": 0.0667, "y": -8.39, "curve": [0.1, 0, 0.167, 0.17, 0.1, -8.39, 0.167, 6.06]}, {"time": 0.2, "x": 0.17, "y": 6.06, "curve": [0.238, 0.18, 0.272, 0.19, 0.238, 7.65, 0.272, 8.65]}, {"time": 0.3, "x": 0.19, "y": 8.65, "curve": [0.389, 0.19, 0.552, 0.06, 0.389, 8.65, 0.552, -3.74]}, {"time": 0.6667, "x": 0.02, "y": -8.05}]}, "SLEEVE_LEFT": {"translate": [{"x": 2.42, "y": -12.11, "curve": [0.07, 0.6, 0.137, -0.88, 0.07, -6.99, 0.137, -2.86]}, {"time": 0.2, "x": -1.9, "curve": [0.224, -2.74, 0.247, -3.23, 0.224, 0, 0.247, 0]}, {"time": 0.2667, "x": -3.23, "curve": [0.364, -3.23, 0.54, 7.15, 0.364, 0, 0.54, 0]}, {"time": 0.6667, "x": 11.1}]}, "GOO_3": {"rotate": [{"value": -8.06, "curve": [0.05, -8.06, 0.15, 0.55]}, {"time": 0.2, "value": 0.55, "curve": [0.276, -0.45, 0.348, -1.28]}, {"time": 0.4, "value": -1.28, "curve": [0.466, -1.28, 0.569, 0.4]}, {"time": 0.6667, "value": 1.82}], "translate": [{"x": 5.61, "y": -10.99, "curve": [0.013, 2.6, 0.025, 0, 0.013, -5.09, 0.025, 0]}, {"time": 0.0333, "curve": [0.039, 0, 0.058, 0, 0.039, 20.99, 0.058, 32.4]}, {"time": 0.0667, "y": 32.4, "curve": [0.1, 0, 0.167, 0, 0.1, 32.4, 0.167, -3.55]}, {"time": 0.2, "y": -3.55, "curve": [0.276, 0, 0.348, 0, 0.276, -1.6, 0.348, 0]}, {"time": 0.4, "curve": [0.466, 0, 0.569, 0, 0.466, 0, 0.569, -3.26]}, {"time": 0.6667, "y": -6.01}]}, "GOO_1": {"rotate": [{"value": 1.11, "curve": [0.074, 1.27, 0.15, 1.43]}, {"time": 0.2, "value": 1.43, "curve": [0.299, 0.11, 0.4, -1.28]}, {"time": 0.4667, "value": -1.28, "curve": [0.517, -1.28, 0.591, -0.11]}, {"time": 0.6667, "value": 1.11}], "translate": [{"x": 31.57, "y": 3.6, "curve": [0.008, 31.57, 0.025, 0, 0.008, 3.6, 0.025, 0]}, {"time": 0.0333, "curve": [0.039, 1.19, 0.058, 1.84, 0.039, 17, 0.058, 26.23]}, {"time": 0.0667, "x": 1.84, "y": 26.23, "curve": [0.1, 1.84, 0.167, 0, 0.1, 26.23, 0.167, -5.26]}, {"time": 0.2, "y": -5.26, "curve": [0.299, 0, 0.4, 0, 0.299, -2.69, 0.4, 0]}, {"time": 0.4667, "curve": [0.517, 0, 0.591, 0, 0.517, 0, 0.591, -2.26]}, {"time": 0.6667, "y": -4.63}]}, "SLEEVE_RIGHT": {"translate": [{"x": 4.1, "y": -15.03, "curve": [0.065, 1.74, 0.151, -3.88, 0.065, -10.58, 0.151, 0]}, {"time": 0.2, "x": -3.88, "curve": [0.322, 0.51, 0.478, 9.28, 0.322, 0, 0.478, 0]}, {"time": 0.5667, "x": 9.28, "curve": [0.595, 9.28, 0.629, 8.63, 0.595, 0, 0.629, 0]}, {"time": 0.6667, "x": 7.6}]}, "CAPE_CORNER_LEFT": {"rotate": [{"value": -8.13, "curve": [0.075, -7.64, 0.15, -7.15]}, {"time": 0.2, "value": -7.15, "curve": [0.238, -3.42, 0.275, 0]}, {"time": 0.3, "curve": [0.367, 0, 0.5, -16.25]}, {"time": 0.5667, "value": -16.25, "curve": [0.592, -16.25, 0.629, -12.19]}, {"time": 0.6667, "value": -8.13}], "translate": [{"x": -5.72, "y": -3.41, "curve": [0.05, -5.72, 0.15, 0, 0.05, -3.41, 0.15, 0]}, {"time": 0.2, "curve": [0.258, 0, 0.375, 12.36, 0.258, 0, 0.375, 14.88]}, {"time": 0.4333, "x": 12.36, "y": 14.88, "curve": [0.492, 12.36, 0.608, 0, 0.492, 14.88, 0.608, 0]}, {"time": 0.6667}]}, "CAPE_CORNER_RIGHT": {"rotate": [{"value": 30.04, "curve": "stepped"}, {"time": 0.2, "value": 30.04, "curve": [0.258, 30.04, 0.375, 0]}, {"time": 0.4333, "curve": [0.492, 0, 0.608, 30.04]}, {"time": 0.6667, "value": 30.04}], "translate": [{"x": -6.81, "y": -10.68, "curve": [0.067, -2.73, 0.134, 1.32, 0.067, 2.7, 0.134, 15.94]}, {"time": 0.2, "x": 5.31, "y": 29.02, "curve": [0.315, 12.35, 0.422, 17.87, 0.315, 9.77, 0.422, -5.36]}, {"time": 0.5, "x": 17.87, "y": -5.36, "curve": [0.533, 17.87, 0.585, 11.93, 0.533, -5.36, 0.585, -3.58]}, {"time": 0.6333, "x": 6.97, "y": -2.09}]}, "NECKLACE_HANDLE": {"translate": [{"x": 12.5, "y": -4.48, "curve": [0.063, -12.5, 0.125, -37.5, 0.063, -4.02, 0.125, -3.56]}, {"time": 0.1667, "x": -37.5, "y": -3.56, "curve": [0.233, -37.5, 0.367, 15.19, 0.233, -3.56, 0.367, -0.88]}, {"time": 0.4333, "x": 15.19, "y": -0.88, "curve": [0.485, 15.19, 0.557, 12.65, 0.485, -0.88, 0.557, -0.73]}, {"time": 0.6333, "x": 9.61, "y": -0.56}]}, "NECKLACE": {"translate": [{"x": 3.65, "y": 2.07}]}, "root": {"rotate": [{"value": -0.11}]}, "SHOULDER_LEFT": {"rotate": [{"time": 0.2, "value": 70.23}], "translate": [{"x": -15.65, "y": 1.57, "curve": [0.05, -15.65, 0.15, 0, 0.05, 1.57, 0.15, 0]}, {"time": 0.2}]}, "ARM_LEFT": {"rotate": [{"time": 0.2, "value": -74.74}]}, "NECK1": {"rotate": [{"time": 0.2, "value": 1.9}]}, "NECK2": {"rotate": [{"time": 0.2, "value": 8.62}]}, "NECK3": {"translate": [{"x": -9.13, "y": 10.18, "curve": [0.05, -9.13, 0.15, 0, 0.05, 10.18, 0.15, 0]}, {"time": 0.2}]}, "Necklace3": {"translate": [{"x": -8.56, "y": 17.56, "curve": [0.05, -8.56, 0.15, 0, 0.05, 17.56, 0.15, 0]}, {"time": 0.2}]}, "SHOULDER_RIGHT": {"rotate": [{"time": 0.1667, "value": -43.34}], "translate": [{"x": -72.78, "y": -22.96, "curve": [0.035, -22.84, 0.125, 0.85, 0.035, -3.16, 0.125, 6.24]}, {"time": 0.1667, "x": 0.85, "y": 6.24}]}, "ARM_RIGHT": {"rotate": [{"time": 0.2, "value": -79.49}]}, "BODY_HANDLE": {"translate": [{"x": -33.39, "y": 1.2, "curve": [0.05, -33.39, 0.15, 0, 0.05, 1.2, 0.15, 0]}, {"time": 0.2}]}, "spawn_particles2": {"translate": [{"y": -19.52, "curve": [0.11, 0, 0.456, 0, 0.11, 9.65, 0.456, 23.63]}, {"time": 0.6667, "y": 23.63}]}}, "ik": {"ARM_RIGHT_HANDLE": [{"bendPositive": false, "curve": "stepped"}, {"time": 0.2, "bendPositive": false, "curve": "stepped"}, {"time": 0.2333}]}, "drawOrder": [{"offsets": [{"slot": "GoatCult/Arm2", "offset": 14}, {"slot": "GoatCult/Sleeve2", "offset": 11}, {"slot": "GoatCult/Hand2", "offset": 11}]}]}}}