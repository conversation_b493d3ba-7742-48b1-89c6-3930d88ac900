{"skeleton": {"hash": "onsPgr2X08Q", "spine": "4.1.24", "x": -112.36, "y": -3.5, "width": 253.92, "height": 294.09, "images": "./images/", "audio": "C:/Users/<USER>/Desktop/咩咩启示录（Cult of the Lamb）/MidasNPC"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "length": 23.05, "rotation": 90.24, "x": 0.89, "y": 2.06}, {"name": "bone2", "parent": "bone", "length": 23.05, "x": 23.05}, {"name": "bone3", "parent": "bone2", "length": 23.05, "x": 23.05}, {"name": "bone4", "parent": "bone3", "length": 23.05, "x": 23.05}, {"name": "Head", "parent": "bone4", "length": 90.62, "x": 21.1, "y": 4.74}, {"name": "Mouth", "parent": "Head", "x": 19.34, "y": 7.4}, {"name": "Eyes", "parent": "Head", "x": 60.7, "y": 4.46}, {"name": "Crown", "parent": "Head", "length": 49.28, "rotation": 1.58, "x": 114.23, "y": -7.98}, {"name": "ArmRight", "parent": "bone3", "length": 20.09, "rotation": 89.76, "x": -0.99, "y": -48.26}, {"name": "ArmLeft", "parent": "bone3", "length": 16.83, "rotation": -92.93, "x": -0.45, "y": 79.39}, {"name": "ArmLeft2", "parent": "ArmLeft", "length": 16.83, "x": 16.83}, {"name": "ArmLeft3", "parent": "ArmLeft2", "length": 16.83, "x": 16.83}, {"name": "ArmRight2", "parent": "ArmRight", "length": 20.09, "x": 20.09}, {"name": "ArmRight3", "parent": "ArmRight2", "length": 20.09, "x": 20.09}], "slots": [{"name": "root", "bone": "root", "attachment": "root"}, {"name": "Body", "bone": "bone", "attachment": "BODY"}, {"name": "<PERSON><PERSON>", "bone": "bone4", "attachment": "<PERSON><PERSON>"}, {"name": "ArmLeft", "bone": "ArmLeft", "attachment": "ArmLeft"}, {"name": "ArmRight", "bone": "ArmRight", "attachment": "ArmRight"}, {"name": "Head", "bone": "Head", "attachment": "HEAD"}, {"name": "Eyes", "bone": "Eyes", "attachment": "Eyes"}, {"name": "Smile", "bone": "Mouth", "attachment": "Smile"}, {"name": "Nose", "bone": "Eyes", "attachment": "Nose"}, {"name": "Crown", "bone": "Crown", "attachment": "Crown"}], "skins": [{"name": "default", "attachments": {"ArmLeft": {"ArmLeft": {"type": "mesh", "uvs": [1, 1, 0.75, 1, 0.5, 1, 0.25, 1, 0, 1, 0, 0, 0.25, 0, 0.5, 0, 0.75, 0, 1, 0], "triangles": [4, 5, 6, 3, 4, 6, 3, 6, 7, 2, 3, 7, 2, 7, 8, 1, 2, 8, 1, 8, 9, 0, 1, 9], "vertices": [1, 12, 28.75, -23.89, 1, 3, 12, 7.78, -24.87, 0.88643, 11, 24.6, -24.87, 0.09989, 10, 41.43, -24.87, 0.01368, 3, 12, -13.2, -25.86, 0.27865, 11, 3.63, -25.86, 0.36421, 10, 20.46, -25.86, 0.35714, 3, 12, -34.18, -26.84, 0.01915, 11, -17.35, -26.84, 0.07749, 10, -0.52, -26.84, 0.90336, 2, 11, -38.33, -27.82, 0.00018, 10, -21.5, -27.82, 0.99982, 1, 10, -23.7, 19.13, 1, 2, 11, -19.55, 20.11, 0.02756, 10, -2.72, 20.11, 0.97244, 3, 12, -15.4, 21.09, 0.06838, 11, 1.43, 21.09, 0.49142, 10, 18.26, 21.09, 0.4402, 3, 12, 5.57, 22.08, 0.73836, 11, 22.4, 22.08, 0.25064, 10, 39.23, 22.08, 0.01099, 2, 12, 26.55, 23.06, 0.99856, 11, 43.38, 23.06, 0.00144], "hull": 10, "edges": [0, 18, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18], "width": 76, "height": 42}}, "ArmRight": {"ArmRight": {"type": "mesh", "uvs": [1, 1, 0.75, 1, 0.5, 1, 0.25, 1, 0, 1, 0, 0, 0.25, 0, 0.5, 0, 0.75, 0, 1, 0], "triangles": [4, 5, 6, 3, 4, 6, 3, 6, 7, 2, 3, 7, 2, 7, 8, 1, 2, 8, 1, 8, 9, 0, 1, 9], "vertices": [2, 9, -14.57, 26.78, 0.99999, 13, -34.66, 26.78, 1e-05, 3, 9, 7.18, 26.78, 0.8862, 13, -12.91, 26.78, 0.11378, 14, -33.01, 26.78, 3e-05, 3, 9, 28.93, 26.78, 0.31718, 13, 8.84, 26.78, 0.56954, 14, -11.26, 26.78, 0.11328, 3, 9, 50.68, 26.78, 0.01606, 13, 30.59, 26.78, 0.32375, 14, 10.49, 26.78, 0.66018, 2, 13, 52.34, 26.78, 0.08044, 14, 32.24, 26.78, 0.91956, 2, 13, 52.34, -20.22, 0.00074, 14, 32.24, -20.22, 0.99926, 2, 13, 30.59, -20.22, 0.23881, 14, 10.49, -20.22, 0.76119, 3, 9, 28.93, -20.22, 0.05816, 13, 8.84, -20.22, 0.89029, 14, -11.26, -20.22, 0.05155, 2, 9, 7.18, -20.22, 0.79625, 13, -12.91, -20.22, 0.20375, 2, 9, -14.57, -20.22, 0.99923, 13, -34.66, -20.22, 0.00077], "hull": 10, "edges": [0, 18, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18], "width": 78, "height": 42}}, "Body": {"BODY": {"type": "mesh", "uvs": [0.90308, 0.29301, 0.9004, 0.48789, 0.91842, 0.65741, 0.99622, 0.63227, 0.99615, 0.90143, 0.94616, 0.98437, 0.74366, 0.98694, 0.54116, 0.98951, 0.13615, 0.99465, 0.00049, 0.98187, 0.01128, 0.68546, 0.00218, 0.30669, 0.22384, 0.09343, 0.35996, 0, 0.38434, 0, 0.60905, 0.01071, 0.75607, 0.15186, 0.22639, 0.56498, 0.75151, 0.53448, 0.44245, 0.31435, 0.61898, 0.51746, 0.36702, 0.69958, 0.49162, 0.63118], "triangles": [19, 13, 14, 12, 13, 19, 22, 19, 20, 19, 17, 12, 19, 15, 20, 18, 20, 16, 20, 15, 16, 0, 18, 16, 19, 14, 15, 7, 21, 22, 21, 17, 19, 21, 19, 22, 10, 11, 17, 11, 12, 17, 9, 10, 8, 8, 17, 21, 8, 10, 17, 7, 20, 6, 7, 22, 20, 6, 2, 5, 6, 18, 2, 18, 1, 2, 6, 20, 18, 5, 2, 4, 4, 2, 3, 0, 1, 18, 7, 8, 21], "vertices": [4, 1, 80.69, -117.27, 0.35006, 2, 57.64, -117.27, 0.20385, 3, 34.59, -117.27, 0.02641, 4, 11.54, -117.27, 0.41968, 4, 1, 56.53, -116.48, 0.41956, 2, 33.48, -116.48, 0.21796, 3, 10.43, -116.48, 0.01923, 4, -12.62, -116.48, 0.34324, 4, 1, 35.49, -120.99, 0.52525, 2, 12.44, -120.99, 0.2278, 3, -10.61, -120.99, 0.00553, 4, -33.66, -120.99, 0.24141, 4, 1, 38.52, -140.84, 0.55538, 2, 15.47, -140.84, 0.22834, 3, -7.58, -140.84, 0.00086, 4, -30.63, -140.84, 0.21541, 3, 1, 5.15, -140.68, 0.56998, 2, -17.9, -140.68, 0.22823, 4, -64, -140.68, 0.20179, 1, 1, -5.08, -127.89, 1, 4, 1, -5.18, -76.25, 0.62292, 2, -28.23, -76.25, 0.22409, 3, -51.28, -76.25, 0.00057, 4, -74.33, -76.25, 0.15242, 1, 1, -5.28, -24.61, 1, 1, 1, -5.48, 78.67, 1, 1, 1, -3.74, 113.26, 1, 4, 1, 33, 110.35, 0.17078, 2, 9.95, 110.35, 0.63551, 3, -13.1, 110.35, 0.06649, 4, -36.15, 110.35, 0.12722, 4, 1, 79.98, 112.47, 0.07493, 2, 56.93, 112.47, 0.55131, 3, 33.88, 112.47, 0.09923, 4, 10.83, 112.47, 0.27453, 4, 1, 106.18, 55.83, 0.00851, 2, 83.13, 55.83, 0.26345, 3, 60.08, 55.83, 0.09044, 4, 37.03, 55.83, 0.6376, 3, 2, 94.57, 21.07, 0.04721, 3, 71.52, 21.07, 0.00787, 4, 48.47, 21.07, 0.94492, 3, 2, 94.54, 14.85, 0.02846, 3, 71.49, 14.85, 0.00214, 4, 48.44, 14.85, 0.96939, 4, 1, 116.02, -42.44, 0.0539, 2, 92.97, -42.44, 0.04126, 3, 69.92, -42.44, 0.02169, 4, 46.87, -42.44, 0.88315, 4, 1, 98.35, -79.85, 0.21409, 2, 75.3, -79.85, 0.15273, 3, 52.26, -79.85, 0.04328, 4, 29.21, -79.85, 0.5899, 4, 1, 47.7, 55.43, 0.11918, 2, 24.66, 55.43, 0.55758, 3, 1.61, 55.43, 0.14325, 4, -21.44, 55.43, 0.17999, 4, 1, 50.91, -78.49, 0.38575, 2, 27.87, -78.49, 0.2322, 3, 4.82, -78.49, 0.04195, 4, -18.23, -78.49, 0.3401, 2, 2, 55.5, 0.2, 5e-05, 4, 9.4, 0.2, 0.99995, 4, 1, 53.17, -44.7, 0.24327, 2, 30.12, -44.7, 0.28321, 3, 7.07, -44.7, 0.14507, 4, -15.98, -44.7, 0.32845, 4, 1, 30.86, 19.64, 0.19995, 2, 7.81, 19.64, 0.71401, 3, -15.24, 19.64, 0.07566, 4, -38.29, 19.64, 0.01038, 4, 1, 39.21, -12.17, 0.08214, 2, 16.16, -12.17, 0.78659, 3, -6.89, -12.17, 0.10074, 4, -29.94, -12.17, 0.03053], "hull": 17, "edges": [0, 32, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32], "width": 230, "height": 112}, "BodyCentre": {"type": "mesh", "uvs": [0.62496, 0, 0.75443, 0.08127, 0.97016, 0.27388, 0.98682, 0.3256, 1, 0.6875, 1, 0.96325, 0.97867, 1, 0.97212, 0.99335, 0.69602, 0.99667, 0.41993, 1, 0.03285, 1, 0, 0.96969, 0.00517, 0.74261, 0.01033, 0.51554, 0, 0.37528, 0, 0.30115, 0.32958, 0.00921, 0.45964, 0, 0.80083, 0.52555, 0.18616, 0.50217, 0.51005, 0.3048, 0.37978, 0.53038, 0.20496, 0.75617, 0.62613, 0.60123, 0.81934, 0.76169], "triangles": [20, 17, 0, 16, 19, 15, 20, 21, 16, 20, 16, 17, 19, 16, 21, 1, 20, 0, 23, 20, 1, 21, 20, 23, 19, 14, 15, 13, 14, 19, 18, 1, 2, 18, 2, 3, 23, 1, 18, 18, 3, 4, 22, 19, 21, 24, 18, 4, 23, 18, 24, 7, 24, 4, 12, 13, 19, 22, 12, 19, 5, 7, 4, 10, 12, 22, 11, 12, 10, 6, 7, 5, 8, 23, 24, 8, 24, 7, 9, 21, 23, 9, 23, 8, 22, 21, 9, 10, 22, 9], "vertices": [4, 1, 117.1, -33.33, 0.00459, 2, 94.05, -33.33, 0.00578, 3, 71, -33.33, 0.17248, 4, 47.96, -33.33, 0.81715, 4, 1, 107.06, -63.32, 0.04109, 2, 84.01, -63.32, 0.05239, 3, 60.96, -63.32, 0.35609, 4, 37.91, -63.32, 0.55042, 4, 1, 83.35, -113.27, 0.14384, 2, 60.3, -113.27, 0.14949, 3, 37.25, -113.27, 0.43991, 4, 14.2, -113.27, 0.26676, 4, 1, 77.02, -117.11, 0.15107, 2, 53.97, -117.11, 0.15476, 3, 30.92, -117.11, 0.43896, 4, 7.87, -117.11, 0.25521, 4, 1, 32.86, -119.98, 0.25104, 2, 9.81, -119.98, 0.21473, 3, -13.24, -119.98, 0.39665, 4, -36.29, -119.98, 0.13759, 4, 1, -0.78, -119.84, 0.30448, 2, -23.83, -119.84, 0.23902, 3, -46.88, -119.84, 0.36653, 4, -69.93, -119.84, 0.08997, 4, 1, -5.25, -114.87, 0.30659, 2, -28.3, -114.87, 0.23981, 3, -51.34, -114.87, 0.36493, 4, -74.39, -114.87, 0.08867, 4, 1, -4.43, -113.35, 0.30703, 2, -27.48, -113.35, 0.23995, 3, -50.53, -113.35, 0.36453, 4, -73.58, -113.35, 0.08848, 4, 1, -4.56, -49.3, 0.52101, 2, -27.61, -49.3, 0.25788, 3, -50.66, -49.3, 0.18954, 4, -73.71, -49.3, 0.03156, 4, 1, -4.69, 14.76, 0.92605, 2, -27.74, 14.76, 0.0402, 3, -50.79, 14.76, 0.03371, 4, -73.84, 14.76, 4e-05, 4, 1, -4.31, 104.56, 0.37054, 2, -27.36, 104.56, 0.14397, 3, -50.41, 104.56, 0.37599, 4, -73.46, 104.56, 0.1095, 4, 1, -0.58, 112.17, 0.36586, 2, -23.63, 112.17, 0.14258, 3, -46.68, 112.17, 0.37865, 4, -69.73, 112.17, 0.11292, 4, 1, 27.12, 110.85, 0.31055, 2, 4.07, 110.85, 0.1297, 3, -18.98, 110.85, 0.39211, 4, -42.03, 110.85, 0.16764, 4, 1, 54.82, 109.53, 0.20663, 2, 31.77, 109.53, 0.09562, 3, 8.72, 109.53, 0.40296, 4, -14.33, 109.53, 0.29478, 4, 1, 71.94, 111.86, 0.16055, 2, 48.89, 111.86, 0.0759, 3, 25.84, 111.86, 0.39887, 4, 2.79, 111.86, 0.36468, 4, 1, 80.98, 111.82, 0.15084, 2, 57.93, 111.82, 0.07153, 3, 34.88, 111.82, 0.39631, 4, 11.84, 111.82, 0.38132, 4, 1, 116.27, 35.2, 0.00475, 2, 93.22, 35.2, 0.00055, 3, 70.17, 35.2, 0.07252, 4, 47.12, 35.2, 0.92219, 1, 4, 48.12, 5.03, 1, 4, 1, 52.81, -73.86, 0.17668, 2, 29.76, -73.86, 0.186, 3, 6.71, -73.86, 0.41854, 4, -16.34, -73.86, 0.21878, 4, 1, 56.27, 68.73, 0.17072, 2, 33.22, 68.73, 0.10825, 3, 10.18, 68.73, 0.40075, 4, -12.87, 68.73, 0.32027, 2, 3, 33.93, -6.51, 0.10105, 4, 10.88, -6.51, 0.89895, 4, 1, 52.64, 23.83, 0.05723, 2, 29.59, 23.83, 0.1816, 3, 6.54, 23.83, 0.60875, 4, -16.51, 23.83, 0.15243, 4, 1, 25.27, 64.5, 0.35447, 2, 2.22, 64.5, 0.17639, 3, -20.83, 64.5, 0.35037, 4, -43.88, 64.5, 0.11877, 4, 1, 43.75, -33.29, 0.14885, 2, 20.7, -33.29, 0.31, 3, -2.35, -33.29, 0.42071, 4, -25.4, -33.29, 0.12044, 4, 1, 23.98, -78.03, 0.30259, 2, 0.94, -78.03, 0.24342, 3, -22.11, -78.03, 0.34895, 4, -45.16, -78.03, 0.10503], "hull": 18, "edges": [0, 34, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34], "width": 209, "height": 110}}, "Collar": {"Collar": {"x": 18.86, "y": -4.51, "rotation": -90.24, "width": 179, "height": 42}}, "Crown": {"Crown": {"x": 39.56, "y": -0.04, "rotation": -91.83, "width": 87, "height": 89}}, "Eyes": {"Eyes": {"path": "Eyes_0", "x": 1.61, "y": -1.99, "rotation": -90.25, "width": 161, "height": 62}, "Eyes2": {"path": "Eyes_1", "x": 1.61, "y": -1.99, "rotation": -90.25, "width": 161, "height": 63}, "Eyes_Closed": {"x": 1.61, "y": -1.99, "rotation": -90.25, "width": 163, "height": 65}, "Eyes_Closing": {"x": 1.61, "y": -1.99, "rotation": -90.25, "width": 163, "height": 65}, "Eyes_Suss": {"x": 1.61, "y": -1.99, "rotation": -90.25, "width": 163, "height": 65}}, "Head": {"HEAD": {"x": 77.78, "y": -4.75, "rotation": -90.25, "width": 190, "height": 186}}, "Nose": {"Nose": {"x": 4.12, "y": -0.5, "rotation": -90.25, "width": 22, "height": 42}}, "root": {"root": {"type": "clipping", "end": "root", "vertexCount": 5, "vertices": [-159.85, -2.32, 151.33, -5.34, 301.77, 282.7, -9.7, 452.04, -272.79, 264.55], "color": "3a3acdff"}}, "Smile": {"MOUTH_OPEN": {"x": 1.73, "y": -5.8, "rotation": -90.25, "width": 85, "height": 46}, "Smile": {"x": 7.47, "y": -4.78, "rotation": -90.25, "width": 77, "height": 46}, "SmileSmall": {"x": -2.01, "y": -3.55, "rotation": -92.52, "width": 49, "height": 20}}}}], "animations": {"animation": {"slots": {"Eyes": {"attachment": [{"name": "Eyes2"}, {"time": 0.1667, "name": "Eyes"}, {"time": 0.3333, "name": "Eyes2"}, {"time": 0.5333, "name": "Eyes_Closing"}, {"time": 0.5667, "name": "Eyes_Closed"}, {"time": 0.6333, "name": "Eyes_Closing"}, {"time": 0.6667, "name": "Eyes"}, {"time": 0.7667, "name": "Eyes2"}, {"time": 0.8333, "name": "Eyes"}, {"time": 1, "name": "Eyes2"}, {"time": 1.1667, "name": "Eyes"}, {"time": 1.3333, "name": "Eyes2"}, {"time": 1.5333, "name": "Eyes"}, {"time": 1.6667, "name": "Eyes2"}, {"time": 1.8333, "name": "Eyes"}, {"time": 2, "name": "Eyes2"}, {"time": 2.1667, "name": "Eyes"}, {"time": 2.3333, "name": "Eyes2"}, {"time": 2.4333, "name": "Eyes_Closing"}, {"time": 2.4667, "name": "Eyes_Closed"}, {"time": 2.5667, "name": "Eyes_Closing"}, {"time": 2.6, "name": "Eyes"}, {"time": 2.6667, "name": "Eyes2"}, {"time": 2.8333, "name": "Eyes"}, {"time": 3, "name": "Eyes2"}, {"time": 3.1667, "name": "Eyes"}, {"time": 3.3333, "name": "Eyes2"}, {"time": 3.5333, "name": "Eyes"}, {"time": 3.6667, "name": "Eyes2"}, {"time": 3.8333, "name": "Eyes"}, {"time": 4, "name": "Eyes2"}]}, "root": {"attachment": [{}]}}, "bones": {"Eyes": {"translate": [{"x": -6.06, "y": 0.02, "curve": [0.044, -4.56, 0.078, -2.97, 0.044, 0.02, 0.078, 0.01]}, {"time": 0.1333, "x": -1.6, "y": 0.01, "curve": [0.157, -0.98, 0.193, -0.49, 0.157, 0, 0.193, 0]}, {"time": 0.2667, "x": -0.39, "curve": [0.4, -0.39, 0.667, -8, 0.4, 0, 0.667, 0.03]}, {"time": 0.8, "x": -8, "y": 0.03, "curve": [0.923, -7.8, 0.968, -6.98, 0.923, 0.03, 0.968, 0.03]}, {"time": 1, "x": -6.06, "y": 0.02, "curve": [1.044, -4.56, 1.078, -2.97, 1.044, 0.02, 1.078, 0.01]}, {"time": 1.1333, "x": -1.6, "y": 0.01, "curve": [1.157, -0.98, 1.193, -0.49, 1.157, 0, 1.193, 0]}, {"time": 1.2667, "x": -0.39, "curve": [1.4, -0.39, 1.667, -8, 1.4, 0, 1.667, 0.03]}, {"time": 1.8, "x": -8, "y": 0.03, "curve": [1.923, -7.8, 1.968, -6.98, 1.923, 0.03, 1.968, 0.03]}, {"time": 2, "x": -6.06, "y": 0.02, "curve": [2.044, -4.56, 2.078, -2.97, 2.044, 0.02, 2.078, 0.01]}, {"time": 2.1333, "x": -1.6, "y": 0.01, "curve": [2.157, -0.98, 2.193, -0.49, 2.157, 0, 2.193, 0]}, {"time": 2.2667, "x": -0.39, "curve": [2.4, -0.39, 2.667, -8, 2.4, 0, 2.667, 0.03]}, {"time": 2.8, "x": -8, "y": 0.03, "curve": [2.923, -7.8, 2.968, -6.98, 2.923, 0.03, 2.968, 0.03]}, {"time": 3, "x": -6.06, "y": 0.02, "curve": [3.044, -4.56, 3.078, -2.97, 3.044, 0.02, 3.078, 0.01]}, {"time": 3.1333, "x": -1.6, "y": 0.01, "curve": [3.157, -0.98, 3.193, -0.49, 3.157, 0, 3.193, 0]}, {"time": 3.2667, "x": -0.39, "curve": [3.4, -0.39, 3.667, -8, 3.4, 0, 3.667, 0.03]}, {"time": 3.8, "x": -8, "y": 0.03, "curve": [3.923, -7.8, 3.968, -6.98, 3.923, 0.03, 3.968, 0.03]}, {"time": 4, "x": -6.06, "y": 0.02}], "scale": [{"time": 1.2, "x": 0.927, "y": 1.06, "curve": "stepped"}, {"time": 1.2333, "y": -1}, {"time": 1.2667, "x": 1.042, "y": -0.949}, {"time": 1.4, "y": -1, "curve": "stepped"}, {"time": 3.0333, "y": -1}, {"time": 3.1333, "x": 1.042, "y": -0.949}, {"time": 3.1667, "y": -1, "curve": "stepped"}, {"time": 3.2}]}, "bone": {"scale": [{"x": 1.02, "y": 0.992, "curve": [0.297, 1.017, 0.242, 0.983, 0.297, 0.995, 0.242, 1.046]}, {"time": 0.4667, "x": 0.982, "y": 1.047, "curve": [0.806, 0.984, 0.744, 1.019, 0.806, 1.044, 0.744, 0.993]}, {"time": 1, "x": 1.02, "y": 0.992, "curve": [1.297, 1.017, 1.242, 0.983, 1.297, 0.995, 1.242, 1.046]}, {"time": 1.4667, "x": 0.982, "y": 1.047, "curve": [1.806, 0.984, 1.744, 1.019, 1.806, 1.044, 1.744, 0.993]}, {"time": 2, "x": 1.02, "y": 0.992, "curve": [2.297, 1.017, 2.242, 0.983, 2.297, 0.995, 2.242, 1.046]}, {"time": 2.4667, "x": 0.982, "y": 1.047, "curve": [2.806, 0.984, 2.744, 1.019, 2.806, 1.044, 2.744, 0.993]}, {"time": 3, "x": 1.02, "y": 0.992, "curve": [3.297, 1.017, 3.242, 0.983, 3.297, 0.995, 3.242, 1.046]}, {"time": 3.4667, "x": 0.982, "y": 1.047, "curve": [3.806, 0.984, 3.744, 1.019, 3.806, 1.044, 3.744, 0.993]}, {"time": 4, "x": 1.02, "y": 0.992}]}, "Head": {"rotate": [{"curve": [0.025, -0.48, 0.049, -0.81]}, {"time": 0.0667, "value": -0.81, "curve": [0.133, -0.81, 0.267, 2.76]}, {"time": 0.3333, "value": 2.76, "curve": [0.4, 2.76, 0.533, -0.81]}, {"time": 0.6, "value": -0.81, "curve": [0.658, -0.81, 0.775, 2.76]}, {"time": 0.8333, "value": 2.76, "curve": [0.866, 2.76, 0.921, 1]}, {"time": 0.9667, "curve": "stepped"}, {"time": 1, "curve": [1.025, -0.48, 1.049, -0.81]}, {"time": 1.0667, "value": -0.81, "curve": [1.133, -0.81, 1.267, 2.76]}, {"time": 1.3333, "value": 2.76, "curve": [1.4, 2.76, 1.533, -0.81]}, {"time": 1.6, "value": -0.81, "curve": [1.658, -0.81, 1.775, 2.76]}, {"time": 1.8333, "value": 2.76, "curve": [1.866, 2.76, 1.921, 1]}, {"time": 1.9667, "curve": "stepped"}, {"time": 2, "curve": [2.025, -0.48, 2.049, -0.81]}, {"time": 2.0667, "value": -0.81, "curve": [2.133, -0.81, 2.267, 2.76]}, {"time": 2.3333, "value": 2.76, "curve": [2.4, 2.76, 2.533, -0.81]}, {"time": 2.6, "value": -0.81, "curve": [2.658, -0.81, 2.775, 2.76]}, {"time": 2.8333, "value": 2.76, "curve": [2.866, 2.76, 2.921, 1]}, {"time": 2.9667, "curve": "stepped"}, {"time": 3, "curve": [3.025, -0.48, 3.049, -0.81]}, {"time": 3.0667, "value": -0.81, "curve": [3.133, -0.81, 3.267, 2.76]}, {"time": 3.3333, "value": 2.76, "curve": [3.4, 2.76, 3.533, -0.81]}, {"time": 3.6, "value": -0.81, "curve": [3.658, -0.81, 3.775, 2.76]}, {"time": 3.8333, "value": 2.76, "curve": [3.874, 2.76, 3.943, 1]}, {"time": 4}], "translate": [{"x": -1.6, "y": 0.01, "curve": [0.03, -0.98, 0.075, -0.49, 0.03, 0, 0.075, 0]}, {"time": 0.1667, "x": -0.39, "curve": [0.292, -0.39, 0.542, -8, 0.292, 0, 0.542, 0.03]}, {"time": 0.6667, "x": -8, "y": 0.03, "curve": [0.896, -7.43, 0.891, -4, 0.896, 0.03, 0.891, 0.02]}, {"time": 0.9667, "x": -1.6, "y": 0.01, "curve": "stepped"}, {"time": 1, "x": -1.6, "y": 0.01, "curve": [1.03, -0.98, 1.075, -0.49, 1.03, 0, 1.075, 0]}, {"time": 1.1667, "x": -0.39, "curve": [1.292, -0.39, 1.542, -8, 1.292, 0, 1.542, 0.03]}, {"time": 1.6667, "x": -8, "y": 0.03, "curve": [1.896, -7.43, 1.891, -4, 1.896, 0.03, 1.891, 0.02]}, {"time": 1.9667, "x": -1.6, "y": 0.01, "curve": "stepped"}, {"time": 2, "x": -1.6, "y": 0.01, "curve": [2.03, -0.98, 2.075, -0.49, 2.03, 0, 2.075, 0]}, {"time": 2.1667, "x": -0.39, "curve": [2.292, -0.39, 2.542, -8, 2.292, 0, 2.542, 0.03]}, {"time": 2.6667, "x": -8, "y": 0.03, "curve": [2.896, -7.43, 2.891, -4, 2.896, 0.03, 2.891, 0.02]}, {"time": 2.9667, "x": -1.6, "y": 0.01, "curve": "stepped"}, {"time": 3, "x": -1.6, "y": 0.01, "curve": [3.03, -0.98, 3.075, -0.49, 3.03, 0, 3.075, 0]}, {"time": 3.1667, "x": -0.39, "curve": [3.292, -0.39, 3.542, -8, 3.292, 0, 3.542, 0.03]}, {"time": 3.6667, "x": -8, "y": 0.03, "curve": [3.922, -7.43, 3.916, -4, 3.922, 0.03, 3.916, 0.02]}, {"time": 4, "x": -1.6, "y": 0.01}]}, "ArmRight": {"translate": [{"x": -2.66, "y": 0.01, "curve": [0.066, -0.25, 0.081, 2.47, 0.066, 0, 0.081, -0.01]}, {"time": 0.2667, "x": 2.84, "y": -0.01, "curve": [0.4, 2.84, 0.667, -4.23, 0.4, -0.01, 0.667, 0.02]}, {"time": 0.8, "x": -4.23, "y": 0.02, "curve": [0.92, -4.05, 0.968, -3.39, 0.92, 0.02, 0.968, 0.01]}, {"time": 1, "x": -2.66, "y": 0.01, "curve": [1.066, -0.25, 1.081, 2.47, 1.066, 0, 1.081, -0.01]}, {"time": 1.2667, "x": 2.84, "y": -0.01, "curve": [1.4, 2.84, 1.667, -4.23, 1.4, -0.01, 1.667, 0.02]}, {"time": 1.8, "x": -4.23, "y": 0.02, "curve": [1.92, -4.05, 1.968, -3.39, 1.92, 0.02, 1.968, 0.01]}, {"time": 2, "x": -2.66, "y": 0.01, "curve": [2.066, -0.25, 2.081, 2.47, 2.066, 0, 2.081, -0.01]}, {"time": 2.2667, "x": 2.84, "y": -0.01, "curve": [2.4, 2.84, 2.667, -4.23, 2.4, -0.01, 2.667, 0.02]}, {"time": 2.8, "x": -4.23, "y": 0.02, "curve": [2.92, -4.05, 2.968, -3.39, 2.92, 0.02, 2.968, 0.01]}, {"time": 3, "x": -2.66, "y": 0.01, "curve": [3.066, -0.25, 3.081, 2.47, 3.066, 0, 3.081, -0.01]}, {"time": 3.2667, "x": 2.84, "y": -0.01, "curve": [3.4, 2.84, 3.667, -4.23, 3.4, -0.01, 3.667, 0.02]}, {"time": 3.8, "x": -4.23, "y": 0.02, "curve": [3.92, -4.05, 3.968, -3.39, 3.92, 0.02, 3.968, 0.01]}, {"time": 4, "x": -2.66, "y": 0.01}]}, "ArmLeft": {"translate": [{"x": -2.66, "y": 0.01, "curve": [0.066, -0.25, 0.081, 2.47, 0.066, 0, 0.081, -0.01]}, {"time": 0.2667, "x": 2.84, "y": -0.01, "curve": [0.4, 2.84, 0.667, -4.23, 0.4, -0.01, 0.667, 0.02]}, {"time": 0.8, "x": -4.23, "y": 0.02, "curve": [0.92, -4.05, 0.968, -3.39, 0.92, 0.02, 0.968, 0.01]}, {"time": 1, "x": -2.66, "y": 0.01, "curve": [1.066, -0.25, 1.081, 2.47, 1.066, 0, 1.081, -0.01]}, {"time": 1.2667, "x": 2.84, "y": -0.01, "curve": [1.4, 2.84, 1.667, -4.23, 1.4, -0.01, 1.667, 0.02]}, {"time": 1.8, "x": -4.23, "y": 0.02, "curve": [1.92, -4.05, 1.968, -3.39, 1.92, 0.02, 1.968, 0.01]}, {"time": 2, "x": -2.66, "y": 0.01, "curve": [2.066, -0.25, 2.081, 2.47, 2.066, 0, 2.081, -0.01]}, {"time": 2.2667, "x": 2.84, "y": -0.01, "curve": [2.4, 2.84, 2.667, -4.23, 2.4, -0.01, 2.667, 0.02]}, {"time": 2.8, "x": -4.23, "y": 0.02, "curve": [2.92, -4.05, 2.968, -3.39, 2.92, 0.02, 2.968, 0.01]}, {"time": 3, "x": -2.66, "y": 0.01, "curve": [3.066, -0.25, 3.081, 2.47, 3.066, 0, 3.081, -0.01]}, {"time": 3.2667, "x": 2.84, "y": -0.01, "curve": [3.4, 2.84, 3.667, -4.23, 3.4, -0.01, 3.667, 0.02]}, {"time": 3.8, "x": -4.23, "y": 0.02, "curve": [3.92, -4.05, 3.968, -3.39, 3.92, 0.02, 3.968, 0.01]}, {"time": 4, "x": -2.66, "y": 0.01}]}, "bone2": {"rotate": [{"value": 0.39, "curve": [0.147, 0.29, 0.074, -0.8]}, {"time": 0.2333, "value": -0.88, "curve": [0.422, -0.78, 0.328, 0.31]}, {"time": 0.5333, "value": 0.39, "curve": [0.68, 0.29, 0.607, -0.8]}, {"time": 0.7667, "value": -0.88, "curve": [0.892, -0.78, 0.83, 0.31]}, {"time": 0.9667, "value": 0.39, "curve": "stepped"}, {"time": 1, "value": 0.39, "curve": [1.147, 0.29, 1.074, -0.8]}, {"time": 1.2333, "value": -0.88, "curve": [1.422, -0.78, 1.328, 0.31]}, {"time": 1.5333, "value": 0.39, "curve": [1.68, 0.29, 1.607, -0.8]}, {"time": 1.7667, "value": -0.88, "curve": [1.892, -0.78, 1.83, 0.31]}, {"time": 1.9667, "value": 0.39, "curve": "stepped"}, {"time": 2, "value": 0.39, "curve": [2.147, 0.29, 2.074, -0.8]}, {"time": 2.2333, "value": -0.88, "curve": [2.422, -0.78, 2.328, 0.31]}, {"time": 2.5333, "value": 0.39, "curve": [2.68, 0.29, 2.607, -0.8]}, {"time": 2.7667, "value": -0.88, "curve": [2.892, -0.78, 2.83, 0.31]}, {"time": 2.9667, "value": 0.39, "curve": "stepped"}, {"time": 3, "value": 0.39, "curve": [3.147, 0.29, 3.074, -0.8]}, {"time": 3.2333, "value": -0.88, "curve": [3.422, -0.78, 3.328, 0.31]}, {"time": 3.5333, "value": 0.39, "curve": [3.68, 0.29, 3.607, -0.8]}, {"time": 3.7667, "value": -0.88, "curve": [3.913, -0.78, 3.84, 0.31]}, {"time": 4, "value": 0.39}]}, "bone3": {"rotate": [{"value": -0.71, "curve": [0.055, -0.31, 0.049, 0.33]}, {"time": 0.2, "value": 0.39, "curve": [0.258, 0.39, 0.375, -0.88]}, {"time": 0.4333, "value": -0.88, "curve": [0.622, -0.78, 0.528, 0.31]}, {"time": 0.7333, "value": 0.39, "curve": [0.88, 0.3, 0.807, -0.63]}, {"time": 0.9667, "value": -0.71, "curve": "stepped"}, {"time": 1, "value": -0.71, "curve": [1.055, -0.31, 1.049, 0.33]}, {"time": 1.2, "value": 0.39, "curve": [1.258, 0.39, 1.375, -0.88]}, {"time": 1.4333, "value": -0.88, "curve": [1.622, -0.78, 1.528, 0.31]}, {"time": 1.7333, "value": 0.39, "curve": [1.88, 0.3, 1.807, -0.63]}, {"time": 1.9667, "value": -0.71, "curve": "stepped"}, {"time": 2, "value": -0.71, "curve": [2.055, -0.31, 2.049, 0.33]}, {"time": 2.2, "value": 0.39, "curve": [2.258, 0.39, 2.375, -0.88]}, {"time": 2.4333, "value": -0.88, "curve": [2.622, -0.78, 2.528, 0.31]}, {"time": 2.7333, "value": 0.39, "curve": [2.88, 0.3, 2.807, -0.63]}, {"time": 2.9667, "value": -0.71, "curve": "stepped"}, {"time": 3, "value": -0.71, "curve": [3.055, -0.31, 3.049, 0.33]}, {"time": 3.2, "value": 0.39, "curve": [3.258, 0.39, 3.375, -0.88]}, {"time": 3.4333, "value": -0.88, "curve": [3.622, -0.78, 3.528, 0.31]}, {"time": 3.7333, "value": 0.39, "curve": [3.901, 0.3, 3.817, -0.63]}, {"time": 4, "value": -0.71}]}, "bone4": {"rotate": [{"value": -0.88, "curve": [0.168, -0.78, 0.084, 0.31]}, {"time": 0.2667, "value": 0.39, "curve": [0.333, 0.39, 0.467, -0.88]}, {"time": 0.5333, "value": -0.88, "curve": [0.701, -0.78, 0.617, 0.31]}, {"time": 0.8, "value": 0.39, "curve": [0.905, 0.29, 0.853, -0.8]}, {"time": 0.9667, "value": -0.88, "curve": "stepped"}, {"time": 1, "value": -0.88, "curve": [1.168, -0.78, 1.084, 0.31]}, {"time": 1.2667, "value": 0.39, "curve": [1.333, 0.39, 1.467, -0.88]}, {"time": 1.5333, "value": -0.88, "curve": [1.701, -0.78, 1.617, 0.31]}, {"time": 1.8, "value": 0.39, "curve": [1.905, 0.29, 1.853, -0.8]}, {"time": 1.9667, "value": -0.88, "curve": "stepped"}, {"time": 2, "value": -0.88, "curve": [2.168, -0.78, 2.084, 0.31]}, {"time": 2.2667, "value": 0.39, "curve": [2.333, 0.39, 2.467, -0.88]}, {"time": 2.5333, "value": -0.88, "curve": [2.701, -0.78, 2.617, 0.31]}, {"time": 2.8, "value": 0.39, "curve": [2.905, 0.29, 2.853, -0.8]}, {"time": 2.9667, "value": -0.88, "curve": "stepped"}, {"time": 3, "value": -0.88, "curve": [3.168, -0.78, 3.084, 0.31]}, {"time": 3.2667, "value": 0.39, "curve": [3.333, 0.39, 3.467, -0.88]}, {"time": 3.5333, "value": -0.88, "curve": [3.701, -0.78, 3.617, 0.31]}, {"time": 3.8, "value": 0.39, "curve": [3.926, 0.29, 3.863, -0.8]}, {"time": 4, "value": -0.88}]}}}, "enter": {"slots": {"Eyes": {"attachment": [{"name": "Eyes_Closed"}, {"time": 0.1333, "name": "Eyes"}, {"time": 0.4333, "name": "Eyes_Closing"}, {"time": 1.3333, "name": "Eyes"}, {"time": 1.8667, "name": "Eyes2"}]}, "root": {"attachment": [{"time": 0.8667}]}, "Smile": {"attachment": [{"time": 0.1333, "name": "MOUTH_OPEN"}, {"time": 0.4333, "name": "Smile"}]}}, "bones": {"Eyes": {"translate": [{"time": 0.5}, {"time": 0.7, "x": -6.06, "y": 0.02, "curve": [0.799, -4.56, 0.875, -2.97, 0.799, 0.02, 0.875, 0.01]}, {"time": 1, "x": -1.6, "y": 0.01, "curve": [1.024, -0.98, 1.06, -0.49, 1.024, 0, 1.06, 0]}, {"time": 1.1333, "x": -0.39, "curve": [1.267, -0.39, 1.533, -8, 1.267, 0, 1.533, 0.03]}, {"time": 1.6667, "x": -8, "y": 0.03, "curve": [1.79, -7.8, 1.835, -6.98, 1.79, 0.03, 1.835, 0.03]}, {"time": 1.8667, "x": -6.06, "y": 0.02}]}, "bone": {"translate": [{"y": -312.06, "curve": [0.024, -0.07, 0.1, -0.13, 0.024, -121.33, 0.1, 50.47]}, {"time": 0.1333, "x": -0.13, "y": 50.47, "curve": [0.175, -0.13, 0.28, -0.07, 0.175, 50.47, 0.28, 29.09]}, {"time": 0.3, "y": -1.22}], "scale": [{"curve": [0.033, 1, 0.1, 1.392, 0.033, 1, 0.1, 0.779]}, {"time": 0.1333, "x": 1.392, "y": 0.779, "curve": [0.175, 1.392, 0.258, 1, 0.175, 0.779, 0.258, 1]}, {"time": 0.3, "curve": [0.325, 1, 0.375, 0.844, 0.325, 1, 0.375, 1.296]}, {"time": 0.4, "x": 0.844, "y": 1.296, "curve": [0.425, 0.844, 0.475, 1.222, 0.425, 1.296, 0.475, 0.917]}, {"time": 0.5, "x": 1.222, "y": 0.917, "curve": [0.542, 1.222, 0.625, 0.953, 0.542, 0.917, 0.625, 1.069]}, {"time": 0.6667, "x": 0.953, "y": 1.069, "curve": [0.717, 0.953, 0.817, 1.02, 0.717, 1.069, 0.817, 0.992]}, {"time": 0.8667, "x": 1.02, "y": 0.992, "curve": [1.164, 1.017, 1.109, 0.983, 1.164, 0.995, 1.109, 1.046]}, {"time": 1.3333, "x": 0.982, "y": 1.047, "curve": [1.673, 0.984, 1.61, 1.019, 1.673, 1.044, 1.61, 0.993]}, {"time": 1.8667, "x": 1.02, "y": 0.992}]}, "Head": {"rotate": [{"time": 0.8667, "curve": [0.892, -0.48, 0.915, -0.81]}, {"time": 0.9333, "value": -0.81, "curve": [1, -0.81, 1.133, 2.76]}, {"time": 1.2, "value": 2.76, "curve": [1.267, 2.76, 1.4, -0.81]}, {"time": 1.4667, "value": -0.81, "curve": [1.525, -0.81, 1.642, 2.76]}, {"time": 1.7, "value": 2.76, "curve": [1.732, 2.76, 1.788, 1]}, {"time": 1.8333}], "translate": [{"x": -15.67, "curve": [0.067, -15.67, 0.2, 10.98, 0.067, 0, 0.2, 0]}, {"time": 0.2667, "x": 10.98, "curve": [0.292, 10.98, 0.342, -41.64, 0.292, 0, 0.342, 0]}, {"time": 0.3667, "x": -41.64, "curve": [0.392, -41.64, 0.442, 0, 0.392, 0, 0.442, 0]}, {"time": 0.4667}, {"time": 0.8667, "x": -1.6, "y": 0.01, "curve": [0.896, -0.98, 0.942, -0.49, 0.896, 0, 0.942, 0]}, {"time": 1.0333, "x": -0.39, "curve": [1.158, -0.39, 1.408, -8, 1.158, 0, 1.408, 0.03]}, {"time": 1.5333, "x": -8, "y": 0.03, "curve": [1.763, -7.43, 1.758, -4, 1.763, 0.03, 1.758, 0.02]}, {"time": 1.8333, "x": -1.6, "y": 0.01}]}, "ArmLeft": {"rotate": [{"value": 154.19, "curve": "stepped"}, {"time": 0.3, "value": 154.19, "curve": [0.333, 154.19, 0.4, 0]}, {"time": 0.4333}], "translate": [{"x": 7.79, "y": 12.48, "curve": "stepped"}, {"time": 0.3, "x": 7.79, "y": 12.48, "curve": [0.333, 7.79, 0.4, -13.11, 0.333, 12.48, 0.4, 0.67]}, {"time": 0.4333, "x": -13.11, "y": 0.67, "curve": [0.458, -13.11, 0.508, 9.4, 0.458, 0.67, 0.508, -0.48]}, {"time": 0.5333, "x": 9.4, "y": -0.48, "curve": [0.567, 9.4, 0.633, 0, 0.567, -0.48, 0.633, 0]}, {"time": 0.6667}, {"time": 0.7333, "x": -2.66, "y": 0.01, "curve": [0.767, -2.66, 0.833, 2.24, 0.767, 0.01, 0.833, 7.98]}, {"time": 0.8667, "x": 2.24, "y": 7.98, "curve": [0.9, 2.24, 0.967, 2.84, 0.9, 7.98, 0.967, -0.01]}, {"time": 1, "x": 2.84, "y": -0.01, "curve": [1.033, 2.84, 1.1, 2.22, 1.033, -0.01, 1.1, 7.65]}, {"time": 1.1333, "x": 2.22, "y": 7.65, "curve": [1.158, 2.22, 1.208, 0.47, 1.158, 7.65, 1.208, 0]}, {"time": 1.2333, "x": 0.47, "curve": [1.342, 0.47, 1.558, -4.23, 1.342, 0, 1.558, 0.02]}, {"time": 1.6667, "x": -4.23, "y": 0.02, "curve": [1.786, -4.05, 1.835, -3.39, 1.786, 0.02, 1.835, 0.01]}, {"time": 1.8667, "x": -2.66, "y": 0.01}]}, "ArmLeft3": {"rotate": [{"value": -32.79, "curve": "stepped"}, {"time": 0.1667, "value": -32.79, "curve": [0.225, -32.79, 0.342, 0]}, {"time": 0.4}]}, "ArmLeft2": {"rotate": [{"value": -25.27, "curve": "stepped"}, {"time": 0.1667, "value": -25.27, "curve": [0.225, -25.27, 0.342, 0]}, {"time": 0.4}]}, "ArmRight3": {"rotate": [{"value": 44.13, "curve": "stepped"}, {"time": 0.1667, "value": 44.13, "curve": [0.225, 44.13, 0.342, 0]}, {"time": 0.4}]}, "ArmRight2": {"rotate": [{"value": 30.11, "curve": "stepped"}, {"time": 0.1667, "value": 30.11, "curve": [0.225, 30.11, 0.342, 0]}, {"time": 0.4}]}, "ArmRight": {"rotate": [{"value": -175.82, "curve": "stepped"}, {"time": 0.3, "value": -175.82, "curve": [0.333, -175.82, 0.4, 0]}, {"time": 0.4333}], "translate": [{"x": 6.75, "y": -36.23, "curve": "stepped"}, {"time": 0.3, "x": 6.75, "y": -36.23, "curve": [0.333, 6.75, 0.4, -14.32, 0.333, -36.23, 0.4, 0.06]}, {"time": 0.4333, "x": -14.32, "y": 0.06, "curve": [0.458, -14.32, 0.508, 8.76, 0.458, 0.06, 0.508, -0.04]}, {"time": 0.5333, "x": 8.76, "y": -0.04, "curve": [0.567, 8.76, 0.633, 0, 0.567, -0.04, 0.633, 0]}, {"time": 0.6667}, {"time": 0.7333, "x": -2.66, "y": 0.01, "curve": [0.767, -2.66, 0.833, 1.78, 0.767, 0.01, 0.833, -12.59]}, {"time": 0.8667, "x": 1.78, "y": -12.59, "curve": [0.9, 1.78, 0.967, 2.84, 0.9, -12.59, 0.967, -0.01]}, {"time": 1, "x": 2.84, "y": -0.01, "curve": [1.033, 2.84, 1.1, 1.8, 1.033, -0.01, 1.1, -8.2]}, {"time": 1.1333, "x": 1.8, "y": -8.2, "curve": [1.158, 1.8, 1.208, 0.47, 1.158, -8.2, 1.208, 0]}, {"time": 1.2333, "x": 0.47, "curve": [1.342, 0.47, 1.558, -4.23, 1.342, 0, 1.558, 0.02]}, {"time": 1.6667, "x": -4.23, "y": 0.02, "curve": [1.786, -4.05, 1.835, -3.39, 1.786, 0.02, 1.835, 0.01]}, {"time": 1.8667, "x": -2.66, "y": 0.01}]}, "bone2": {"rotate": [{"curve": [0.033, 0, 0.1, -4.98]}, {"time": 0.1333, "value": -4.98, "curve": [0.175, -4.98, 0.258, 0]}, {"time": 0.3, "curve": [0.325, 0, 0.375, 3.15]}, {"time": 0.4, "value": 3.15, "curve": [0.433, 3.15, 0.5, 0]}, {"time": 0.5333}, {"time": 0.8667, "value": 0.39, "curve": [1.013, 0.29, 0.94, -0.8]}, {"time": 1.1, "value": -0.88, "curve": [1.288, -0.78, 1.195, 0.31]}, {"time": 1.4, "value": 0.39, "curve": [1.547, 0.29, 1.474, -0.8]}, {"time": 1.6333, "value": -0.88, "curve": [1.759, -0.78, 1.696, 0.31]}, {"time": 1.8333, "value": 0.39}]}, "bone3": {"rotate": [{"curve": [0.033, 0, 0.1, -4.98]}, {"time": 0.1333, "value": -4.98, "curve": [0.175, -4.98, 0.258, 0]}, {"time": 0.3, "curve": [0.325, 0, 0.375, 3.15]}, {"time": 0.4, "value": 3.15, "curve": [0.433, 3.15, 0.5, 0]}, {"time": 0.5333}, {"time": 0.8667, "value": -0.71, "curve": [0.922, -0.31, 0.915, 0.33]}, {"time": 1.0667, "value": 0.39, "curve": [1.125, 0.39, 1.242, -0.88]}, {"time": 1.3, "value": -0.88, "curve": [1.488, -0.78, 1.395, 0.31]}, {"time": 1.6, "value": 0.39, "curve": [1.747, 0.3, 1.674, -0.63]}, {"time": 1.8333, "value": -0.71}]}, "bone4": {"rotate": [{"curve": [0.033, 0, 0.1, -4.98]}, {"time": 0.1333, "value": -4.98, "curve": [0.175, -4.98, 0.258, 0]}, {"time": 0.3, "curve": [0.325, 0, 0.375, 3.15]}, {"time": 0.4, "value": 3.15, "curve": [0.433, 3.15, 0.5, 0]}, {"time": 0.5333}, {"time": 0.8667, "value": -0.88, "curve": [1.034, -0.78, 0.951, 0.31]}, {"time": 1.1333, "value": 0.39, "curve": [1.2, 0.39, 1.333, -0.88]}, {"time": 1.4, "value": -0.88, "curve": [1.568, -0.78, 1.484, 0.31]}, {"time": 1.6667, "value": 0.39, "curve": [1.771, 0.29, 1.719, -0.8]}, {"time": 1.8333, "value": -0.88}]}}}, "exit": {"slots": {"Eyes": {"attachment": [{"name": "Eyes_Closed"}]}, "Smile": {"attachment": [{"time": 0.0667, "name": "MOUTH_OPEN"}]}}, "bones": {"bone": {"translate": [{"y": -1.22, "curve": "stepped"}, {"time": 0.3667, "y": -1.22, "curve": [0.425, 0, 0.571, 0, 0.425, -1.22, 0.571, -192.22]}, {"time": 0.6, "y": -312.06}], "scale": [{"curve": [0.05, 1, 0.038, 1.304, 0.05, 1, 0.038, 0.831]}, {"time": 0.2, "x": 1.304, "y": 0.831, "curve": [0.25, 1.304, 0.37, 1.111, 0.25, 0.831, 0.37, 0.963]}, {"time": 0.4, "x": 0.828, "y": 1.157}]}, "ArmLeft": {"rotate": [{"curve": [0.017, 0, 0.013, 154.19]}, {"time": 0.0667, "value": 154.19}], "translate": [{"curve": [0.017, 0, 0.013, 7.79, 0.017, 0, 0.013, 12.48]}, {"time": 0.0667, "x": 7.79, "y": 12.48}]}, "ArmLeft3": {"rotate": [{"curve": [0.017, 0, 0.013, -32.79]}, {"time": 0.0667, "value": -32.79}]}, "ArmLeft2": {"rotate": [{"curve": [0.017, 0, 0.013, -25.27]}, {"time": 0.0667, "value": -25.27}]}, "ArmRight3": {"rotate": [{"curve": [0.017, 0, 0.013, 44.13]}, {"time": 0.0667, "value": 44.13}]}, "ArmRight2": {"rotate": [{"curve": [0.017, 0, 0.013, 30.11]}, {"time": 0.0667, "value": 30.11}]}, "ArmRight": {"rotate": [{"curve": [0.017, 0, 0.013, -175.82]}, {"time": 0.0667, "value": -175.82}], "translate": [{"curve": [0.017, 0, 0.013, 6.75, 0.017, 0, 0.013, -36.23]}, {"time": 0.0667, "x": 6.75, "y": -36.23}]}, "bone2": {"rotate": [{"curve": [0.05, 0, 0.038, -5.99]}, {"time": 0.2, "value": -5.99, "curve": [0.267, -5.99, 0.426, -3.71]}, {"time": 0.4667, "value": -0.38}]}, "bone3": {"rotate": [{"curve": [0.05, 0, 0.038, -5.99]}, {"time": 0.2, "value": -5.99, "curve": [0.267, -5.99, 0.426, -3.71]}, {"time": 0.4667, "value": -0.38}]}, "bone4": {"rotate": [{"curve": [0.05, 0, 0.038, -5.99]}, {"time": 0.2, "value": -5.99, "curve": [0.267, -5.99, 0.426, -3.71]}, {"time": 0.4667, "value": -0.38}]}, "Head": {"rotate": [{"curve": [0.058, 0, 0.045, -6.83]}, {"time": 0.2333, "value": -6.83, "curve": [0.292, -6.83, 0.433, -5.05]}, {"time": 0.4667, "value": 1.41}]}}}, "idle": {"slots": {"Eyes": {"attachment": [{"name": "Eyes2"}, {"time": 0.1667, "name": "Eyes"}, {"time": 0.3333, "name": "Eyes2"}, {"time": 0.5333, "name": "Eyes_Closing"}, {"time": 0.5667, "name": "Eyes_Closed"}, {"time": 0.6333, "name": "Eyes_Closing"}, {"time": 0.6667, "name": "Eyes"}, {"time": 0.7667, "name": "Eyes2"}, {"time": 0.8333, "name": "Eyes"}, {"time": 1, "name": "Eyes2"}, {"time": 1.1667, "name": "Eyes"}, {"time": 1.3333, "name": "Eyes2"}, {"time": 1.5333, "name": "Eyes"}, {"time": 1.6667, "name": "Eyes2"}, {"time": 1.8333, "name": "Eyes"}, {"time": 2, "name": "Eyes2"}, {"time": 2.1667, "name": "Eyes"}, {"time": 2.3333, "name": "Eyes2"}, {"time": 2.4333, "name": "Eyes_Closing"}, {"time": 2.4667, "name": "Eyes_Closed"}, {"time": 2.5667, "name": "Eyes_Closing"}, {"time": 2.6, "name": "Eyes"}, {"time": 2.6667, "name": "Eyes2"}, {"time": 2.8333, "name": "Eyes"}, {"time": 3, "name": "Eyes2"}, {"time": 3.1667, "name": "Eyes"}, {"time": 3.3333, "name": "Eyes2"}, {"time": 3.5333, "name": "Eyes"}, {"time": 3.6667, "name": "Eyes2"}, {"time": 3.8333, "name": "Eyes"}, {"time": 4, "name": "Eyes2"}]}, "root": {"attachment": [{}]}}, "bones": {"Eyes": {"translate": [{"x": -6.06, "y": 0.02, "curve": [0.044, -4.56, 0.078, -2.97, 0.044, 0.02, 0.078, 0.01]}, {"time": 0.1333, "x": -1.6, "y": 0.01, "curve": [0.157, -0.98, 0.193, -0.49, 0.157, 0, 0.193, 0]}, {"time": 0.2667, "x": -0.39, "curve": [0.4, -0.39, 0.667, -8, 0.4, 0, 0.667, 0.03]}, {"time": 0.8, "x": -8, "y": 0.03, "curve": [0.923, -7.8, 0.968, -6.98, 0.923, 0.03, 0.968, 0.03]}, {"time": 1, "x": -6.06, "y": 0.02, "curve": [1.044, -4.56, 1.078, -2.97, 1.044, 0.02, 1.078, 0.01]}, {"time": 1.1333, "x": -1.6, "y": 0.01, "curve": [1.157, -0.98, 1.193, -0.49, 1.157, 0, 1.193, 0]}, {"time": 1.2667, "x": -0.39, "curve": [1.4, -0.39, 1.667, -8, 1.4, 0, 1.667, 0.03]}, {"time": 1.8, "x": -8, "y": 0.03, "curve": [1.923, -7.8, 1.968, -6.98, 1.923, 0.03, 1.968, 0.03]}, {"time": 2, "x": -6.06, "y": 0.02, "curve": [2.044, -4.56, 2.078, -2.97, 2.044, 0.02, 2.078, 0.01]}, {"time": 2.1333, "x": -1.6, "y": 0.01, "curve": [2.157, -0.98, 2.193, -0.49, 2.157, 0, 2.193, 0]}, {"time": 2.2667, "x": -0.39, "curve": [2.4, -0.39, 2.667, -8, 2.4, 0, 2.667, 0.03]}, {"time": 2.8, "x": -8, "y": 0.03, "curve": [2.923, -7.8, 2.968, -6.98, 2.923, 0.03, 2.968, 0.03]}, {"time": 3, "x": -6.06, "y": 0.02, "curve": [3.044, -4.56, 3.078, -2.97, 3.044, 0.02, 3.078, 0.01]}, {"time": 3.1333, "x": -1.6, "y": 0.01, "curve": [3.157, -0.98, 3.193, -0.49, 3.157, 0, 3.193, 0]}, {"time": 3.2667, "x": -0.39, "curve": [3.4, -0.39, 3.667, -8, 3.4, 0, 3.667, 0.03]}, {"time": 3.8, "x": -8, "y": 0.03, "curve": [3.923, -7.8, 3.968, -6.98, 3.923, 0.03, 3.968, 0.03]}, {"time": 4, "x": -6.06, "y": 0.02}], "scale": [{"time": 1.2, "x": 0.927, "y": 1.06, "curve": "stepped"}, {"time": 1.2333, "y": -1}, {"time": 1.2667, "x": 1.042, "y": -0.949}, {"time": 1.4, "y": -1, "curve": "stepped"}, {"time": 3.0333, "y": -1}, {"time": 3.1333, "x": 1.042, "y": -0.949}, {"time": 3.1667, "y": -1, "curve": "stepped"}, {"time": 3.2}]}, "bone": {"scale": [{"x": 1.02, "y": 0.992, "curve": [0.297, 1.017, 0.242, 0.983, 0.297, 0.995, 0.242, 1.046]}, {"time": 0.4667, "x": 0.982, "y": 1.047, "curve": [0.806, 0.984, 0.744, 1.019, 0.806, 1.044, 0.744, 0.993]}, {"time": 1, "x": 1.02, "y": 0.992, "curve": [1.297, 1.017, 1.242, 0.983, 1.297, 0.995, 1.242, 1.046]}, {"time": 1.4667, "x": 0.982, "y": 1.047, "curve": [1.806, 0.984, 1.744, 1.019, 1.806, 1.044, 1.744, 0.993]}, {"time": 2, "x": 1.02, "y": 0.992, "curve": [2.297, 1.017, 2.242, 0.983, 2.297, 0.995, 2.242, 1.046]}, {"time": 2.4667, "x": 0.982, "y": 1.047, "curve": [2.806, 0.984, 2.744, 1.019, 2.806, 1.044, 2.744, 0.993]}, {"time": 3, "x": 1.02, "y": 0.992, "curve": [3.297, 1.017, 3.242, 0.983, 3.297, 0.995, 3.242, 1.046]}, {"time": 3.4667, "x": 0.982, "y": 1.047, "curve": [3.806, 0.984, 3.744, 1.019, 3.806, 1.044, 3.744, 0.993]}, {"time": 4, "x": 1.02, "y": 0.992}]}, "Head": {"rotate": [{"curve": [0.025, -0.48, 0.049, -0.81]}, {"time": 0.0667, "value": -0.81, "curve": [0.133, -0.81, 0.267, 2.76]}, {"time": 0.3333, "value": 2.76, "curve": [0.4, 2.76, 0.533, -0.81]}, {"time": 0.6, "value": -0.81, "curve": [0.658, -0.81, 0.775, 2.76]}, {"time": 0.8333, "value": 2.76, "curve": [0.866, 2.76, 0.921, 1]}, {"time": 0.9667, "curve": "stepped"}, {"time": 1, "curve": [1.025, -0.48, 1.049, -0.81]}, {"time": 1.0667, "value": -0.81, "curve": [1.133, -0.81, 1.267, 2.76]}, {"time": 1.3333, "value": 2.76, "curve": [1.4, 2.76, 1.533, -0.81]}, {"time": 1.6, "value": -0.81, "curve": [1.658, -0.81, 1.775, 2.76]}, {"time": 1.8333, "value": 2.76, "curve": [1.866, 2.76, 1.921, 1]}, {"time": 1.9667, "curve": "stepped"}, {"time": 2, "curve": [2.025, -0.48, 2.049, -0.81]}, {"time": 2.0667, "value": -0.81, "curve": [2.133, -0.81, 2.267, 2.76]}, {"time": 2.3333, "value": 2.76, "curve": [2.4, 2.76, 2.533, -0.81]}, {"time": 2.6, "value": -0.81, "curve": [2.658, -0.81, 2.775, 2.76]}, {"time": 2.8333, "value": 2.76, "curve": [2.866, 2.76, 2.921, 1]}, {"time": 2.9667, "curve": "stepped"}, {"time": 3, "curve": [3.025, -0.48, 3.049, -0.81]}, {"time": 3.0667, "value": -0.81, "curve": [3.133, -0.81, 3.267, 2.76]}, {"time": 3.3333, "value": 2.76, "curve": [3.4, 2.76, 3.533, -0.81]}, {"time": 3.6, "value": -0.81, "curve": [3.658, -0.81, 3.775, 2.76]}, {"time": 3.8333, "value": 2.76, "curve": [3.874, 2.76, 3.943, 1]}, {"time": 4}], "translate": [{"x": -1.6, "y": 0.01, "curve": [0.03, -0.98, 0.075, -0.49, 0.03, 0, 0.075, 0]}, {"time": 0.1667, "x": -0.39, "curve": [0.292, -0.39, 0.542, -8, 0.292, 0, 0.542, 0.03]}, {"time": 0.6667, "x": -8, "y": 0.03, "curve": [0.896, -7.43, 0.891, -4, 0.896, 0.03, 0.891, 0.02]}, {"time": 0.9667, "x": -1.6, "y": 0.01, "curve": "stepped"}, {"time": 1, "x": -1.6, "y": 0.01, "curve": [1.03, -0.98, 1.075, -0.49, 1.03, 0, 1.075, 0]}, {"time": 1.1667, "x": -0.39, "curve": [1.292, -0.39, 1.542, -8, 1.292, 0, 1.542, 0.03]}, {"time": 1.6667, "x": -8, "y": 0.03, "curve": [1.896, -7.43, 1.891, -4, 1.896, 0.03, 1.891, 0.02]}, {"time": 1.9667, "x": -1.6, "y": 0.01, "curve": "stepped"}, {"time": 2, "x": -1.6, "y": 0.01, "curve": [2.03, -0.98, 2.075, -0.49, 2.03, 0, 2.075, 0]}, {"time": 2.1667, "x": -0.39, "curve": [2.292, -0.39, 2.542, -8, 2.292, 0, 2.542, 0.03]}, {"time": 2.6667, "x": -8, "y": 0.03, "curve": [2.896, -7.43, 2.891, -4, 2.896, 0.03, 2.891, 0.02]}, {"time": 2.9667, "x": -1.6, "y": 0.01, "curve": "stepped"}, {"time": 3, "x": -1.6, "y": 0.01, "curve": [3.03, -0.98, 3.075, -0.49, 3.03, 0, 3.075, 0]}, {"time": 3.1667, "x": -0.39, "curve": [3.292, -0.39, 3.542, -8, 3.292, 0, 3.542, 0.03]}, {"time": 3.6667, "x": -8, "y": 0.03, "curve": [3.922, -7.43, 3.916, -4, 3.922, 0.03, 3.916, 0.02]}, {"time": 4, "x": -1.6, "y": 0.01}]}, "ArmRight": {"translate": [{"x": -2.66, "y": 0.01, "curve": [0.066, -0.25, 0.081, 2.47, 0.066, 0, 0.081, -0.01]}, {"time": 0.2667, "x": 2.84, "y": -0.01, "curve": [0.4, 2.84, 0.667, -4.23, 0.4, -0.01, 0.667, 0.02]}, {"time": 0.8, "x": -4.23, "y": 0.02, "curve": [0.92, -4.05, 0.968, -3.39, 0.92, 0.02, 0.968, 0.01]}, {"time": 1, "x": -2.66, "y": 0.01, "curve": [1.066, -0.25, 1.081, 2.47, 1.066, 0, 1.081, -0.01]}, {"time": 1.2667, "x": 2.84, "y": -0.01, "curve": [1.4, 2.84, 1.667, -4.23, 1.4, -0.01, 1.667, 0.02]}, {"time": 1.8, "x": -4.23, "y": 0.02, "curve": [1.92, -4.05, 1.968, -3.39, 1.92, 0.02, 1.968, 0.01]}, {"time": 2, "x": -2.66, "y": 0.01, "curve": [2.066, -0.25, 2.081, 2.47, 2.066, 0, 2.081, -0.01]}, {"time": 2.2667, "x": 2.84, "y": -0.01, "curve": [2.4, 2.84, 2.667, -4.23, 2.4, -0.01, 2.667, 0.02]}, {"time": 2.8, "x": -4.23, "y": 0.02, "curve": [2.92, -4.05, 2.968, -3.39, 2.92, 0.02, 2.968, 0.01]}, {"time": 3, "x": -2.66, "y": 0.01, "curve": [3.066, -0.25, 3.081, 2.47, 3.066, 0, 3.081, -0.01]}, {"time": 3.2667, "x": 2.84, "y": -0.01, "curve": [3.4, 2.84, 3.667, -4.23, 3.4, -0.01, 3.667, 0.02]}, {"time": 3.8, "x": -4.23, "y": 0.02, "curve": [3.92, -4.05, 3.968, -3.39, 3.92, 0.02, 3.968, 0.01]}, {"time": 4, "x": -2.66, "y": 0.01}]}, "ArmLeft": {"translate": [{"x": -2.66, "y": 0.01, "curve": [0.066, -0.25, 0.081, 2.47, 0.066, 0, 0.081, -0.01]}, {"time": 0.2667, "x": 2.84, "y": -0.01, "curve": [0.4, 2.84, 0.667, -4.23, 0.4, -0.01, 0.667, 0.02]}, {"time": 0.8, "x": -4.23, "y": 0.02, "curve": [0.92, -4.05, 0.968, -3.39, 0.92, 0.02, 0.968, 0.01]}, {"time": 1, "x": -2.66, "y": 0.01, "curve": [1.066, -0.25, 1.081, 2.47, 1.066, 0, 1.081, -0.01]}, {"time": 1.2667, "x": 2.84, "y": -0.01, "curve": [1.4, 2.84, 1.667, -4.23, 1.4, -0.01, 1.667, 0.02]}, {"time": 1.8, "x": -4.23, "y": 0.02, "curve": [1.92, -4.05, 1.968, -3.39, 1.92, 0.02, 1.968, 0.01]}, {"time": 2, "x": -2.66, "y": 0.01, "curve": [2.066, -0.25, 2.081, 2.47, 2.066, 0, 2.081, -0.01]}, {"time": 2.2667, "x": 2.84, "y": -0.01, "curve": [2.4, 2.84, 2.667, -4.23, 2.4, -0.01, 2.667, 0.02]}, {"time": 2.8, "x": -4.23, "y": 0.02, "curve": [2.92, -4.05, 2.968, -3.39, 2.92, 0.02, 2.968, 0.01]}, {"time": 3, "x": -2.66, "y": 0.01, "curve": [3.066, -0.25, 3.081, 2.47, 3.066, 0, 3.081, -0.01]}, {"time": 3.2667, "x": 2.84, "y": -0.01, "curve": [3.4, 2.84, 3.667, -4.23, 3.4, -0.01, 3.667, 0.02]}, {"time": 3.8, "x": -4.23, "y": 0.02, "curve": [3.92, -4.05, 3.968, -3.39, 3.92, 0.02, 3.968, 0.01]}, {"time": 4, "x": -2.66, "y": 0.01}]}, "bone2": {"rotate": [{"value": 0.39, "curve": [0.147, 0.29, 0.074, -0.8]}, {"time": 0.2333, "value": -0.88, "curve": [0.422, -0.78, 0.328, 0.31]}, {"time": 0.5333, "value": 0.39, "curve": [0.68, 0.29, 0.607, -0.8]}, {"time": 0.7667, "value": -0.88, "curve": [0.892, -0.78, 0.83, 0.31]}, {"time": 0.9667, "value": 0.39, "curve": "stepped"}, {"time": 1, "value": 0.39, "curve": [1.147, 0.29, 1.074, -0.8]}, {"time": 1.2333, "value": -0.88, "curve": [1.422, -0.78, 1.328, 0.31]}, {"time": 1.5333, "value": 0.39, "curve": [1.68, 0.29, 1.607, -0.8]}, {"time": 1.7667, "value": -0.88, "curve": [1.892, -0.78, 1.83, 0.31]}, {"time": 1.9667, "value": 0.39, "curve": "stepped"}, {"time": 2, "value": 0.39, "curve": [2.147, 0.29, 2.074, -0.8]}, {"time": 2.2333, "value": -0.88, "curve": [2.422, -0.78, 2.328, 0.31]}, {"time": 2.5333, "value": 0.39, "curve": [2.68, 0.29, 2.607, -0.8]}, {"time": 2.7667, "value": -0.88, "curve": [2.892, -0.78, 2.83, 0.31]}, {"time": 2.9667, "value": 0.39, "curve": "stepped"}, {"time": 3, "value": 0.39, "curve": [3.147, 0.29, 3.074, -0.8]}, {"time": 3.2333, "value": -0.88, "curve": [3.422, -0.78, 3.328, 0.31]}, {"time": 3.5333, "value": 0.39, "curve": [3.68, 0.29, 3.607, -0.8]}, {"time": 3.7667, "value": -0.88, "curve": [3.913, -0.78, 3.84, 0.31]}, {"time": 4, "value": 0.39}]}, "bone3": {"rotate": [{"value": -0.71, "curve": [0.055, -0.31, 0.049, 0.33]}, {"time": 0.2, "value": 0.39, "curve": [0.258, 0.39, 0.375, -0.88]}, {"time": 0.4333, "value": -0.88, "curve": [0.622, -0.78, 0.528, 0.31]}, {"time": 0.7333, "value": 0.39, "curve": [0.88, 0.3, 0.807, -0.63]}, {"time": 0.9667, "value": -0.71, "curve": "stepped"}, {"time": 1, "value": -0.71, "curve": [1.055, -0.31, 1.049, 0.33]}, {"time": 1.2, "value": 0.39, "curve": [1.258, 0.39, 1.375, -0.88]}, {"time": 1.4333, "value": -0.88, "curve": [1.622, -0.78, 1.528, 0.31]}, {"time": 1.7333, "value": 0.39, "curve": [1.88, 0.3, 1.807, -0.63]}, {"time": 1.9667, "value": -0.71, "curve": "stepped"}, {"time": 2, "value": -0.71, "curve": [2.055, -0.31, 2.049, 0.33]}, {"time": 2.2, "value": 0.39, "curve": [2.258, 0.39, 2.375, -0.88]}, {"time": 2.4333, "value": -0.88, "curve": [2.622, -0.78, 2.528, 0.31]}, {"time": 2.7333, "value": 0.39, "curve": [2.88, 0.3, 2.807, -0.63]}, {"time": 2.9667, "value": -0.71, "curve": "stepped"}, {"time": 3, "value": -0.71, "curve": [3.055, -0.31, 3.049, 0.33]}, {"time": 3.2, "value": 0.39, "curve": [3.258, 0.39, 3.375, -0.88]}, {"time": 3.4333, "value": -0.88, "curve": [3.622, -0.78, 3.528, 0.31]}, {"time": 3.7333, "value": 0.39, "curve": [3.901, 0.3, 3.817, -0.63]}, {"time": 4, "value": -0.71}]}, "bone4": {"rotate": [{"value": -0.88, "curve": [0.168, -0.78, 0.084, 0.31]}, {"time": 0.2667, "value": 0.39, "curve": [0.333, 0.39, 0.467, -0.88]}, {"time": 0.5333, "value": -0.88, "curve": [0.701, -0.78, 0.617, 0.31]}, {"time": 0.8, "value": 0.39, "curve": [0.905, 0.29, 0.853, -0.8]}, {"time": 0.9667, "value": -0.88, "curve": "stepped"}, {"time": 1, "value": -0.88, "curve": [1.168, -0.78, 1.084, 0.31]}, {"time": 1.2667, "value": 0.39, "curve": [1.333, 0.39, 1.467, -0.88]}, {"time": 1.5333, "value": -0.88, "curve": [1.701, -0.78, 1.617, 0.31]}, {"time": 1.8, "value": 0.39, "curve": [1.905, 0.29, 1.853, -0.8]}, {"time": 1.9667, "value": -0.88, "curve": "stepped"}, {"time": 2, "value": -0.88, "curve": [2.168, -0.78, 2.084, 0.31]}, {"time": 2.2667, "value": 0.39, "curve": [2.333, 0.39, 2.467, -0.88]}, {"time": 2.5333, "value": -0.88, "curve": [2.701, -0.78, 2.617, 0.31]}, {"time": 2.8, "value": 0.39, "curve": [2.905, 0.29, 2.853, -0.8]}, {"time": 2.9667, "value": -0.88, "curve": "stepped"}, {"time": 3, "value": -0.88, "curve": [3.168, -0.78, 3.084, 0.31]}, {"time": 3.2667, "value": 0.39, "curve": [3.333, 0.39, 3.467, -0.88]}, {"time": 3.5333, "value": -0.88, "curve": [3.701, -0.78, 3.617, 0.31]}, {"time": 3.8, "value": 0.39, "curve": [3.926, 0.29, 3.863, -0.8]}, {"time": 4, "value": -0.88}]}}}, "talk": {"slots": {"Eyes": {"attachment": [{"name": "Eyes2"}, {"time": 0.1667, "name": "Eyes"}, {"time": 0.3333, "name": "Eyes2"}, {"time": 0.5333, "name": "Eyes_Closing"}, {"time": 0.5667, "name": "Eyes_Closed"}, {"time": 0.6333, "name": "Eyes_Closing"}, {"time": 0.6667, "name": "Eyes"}, {"time": 0.7667, "name": "Eyes2"}, {"time": 0.8333, "name": "Eyes"}, {"time": 1, "name": "Eyes2"}, {"time": 1.1667, "name": "Eyes"}, {"time": 1.3333, "name": "Eyes2"}, {"time": 1.5333, "name": "Eyes"}, {"time": 1.6667, "name": "Eyes2"}, {"time": 1.8333, "name": "Eyes"}, {"time": 2, "name": "Eyes_Suss"}, {"time": 3.3333, "name": "Eyes2"}, {"time": 3.5333, "name": "Eyes"}, {"time": 3.6667, "name": "Eyes2"}, {"time": 3.8333, "name": "Eyes"}, {"time": 4, "name": "Eyes2"}]}, "root": {"attachment": [{}]}, "Smile": {"attachment": [{"name": "MOUTH_OPEN"}, {"time": 0.1667, "name": "Smile"}, {"time": 0.3333, "name": "MOUTH_OPEN"}, {"time": 0.5, "name": "Smile"}, {"time": 0.6667, "name": "MOUTH_OPEN"}, {"time": 0.8333, "name": "Smile"}, {"time": 1, "name": "MOUTH_OPEN"}, {"time": 1.1667, "name": "Smile"}, {"time": 1.3333, "name": "MOUTH_OPEN"}, {"time": 1.5, "name": "Smile"}, {"time": 1.6667, "name": "MOUTH_OPEN"}, {"time": 1.8333, "name": "Smile"}, {"time": 2, "name": "MOUTH_OPEN"}, {"time": 2.1667, "name": "Smile"}, {"time": 2.3333, "name": "MOUTH_OPEN"}, {"time": 2.5, "name": "Smile"}, {"time": 2.6667, "name": "MOUTH_OPEN"}, {"time": 2.8333, "name": "Smile"}, {"time": 3, "name": "MOUTH_OPEN"}, {"time": 3.1667, "name": "Smile"}, {"time": 3.3333, "name": "MOUTH_OPEN"}, {"time": 3.5, "name": "Smile"}, {"time": 3.6667, "name": "MOUTH_OPEN"}, {"time": 3.8333, "name": "Smile"}]}}, "bones": {"Eyes": {"translate": [{"x": -6.06, "y": 0.02, "curve": [0.044, -4.56, 0.078, -2.97, 0.044, 0.02, 0.078, 0.01]}, {"time": 0.1333, "x": -1.6, "y": 0.01, "curve": [0.157, -0.98, 0.193, -0.49, 0.157, 0, 0.193, 0]}, {"time": 0.2667, "x": -0.39, "curve": [0.4, -0.39, 0.667, -8, 0.4, 0, 0.667, 0.03]}, {"time": 0.8, "x": -8, "y": 0.03, "curve": [0.923, -7.8, 0.968, -6.98, 0.923, 0.03, 0.968, 0.03]}, {"time": 1, "x": -6.06, "y": 0.02, "curve": [1.044, -4.56, 1.078, -2.97, 1.044, 0.02, 1.078, 0.01]}, {"time": 1.1333, "x": -1.6, "y": 0.01, "curve": [1.157, -0.98, 1.193, -0.49, 1.157, 0, 1.193, 0]}, {"time": 1.2667, "x": -0.39, "curve": [1.4, -0.39, 1.667, -8, 1.4, 0, 1.667, 0.03]}, {"time": 1.8, "x": -8, "y": 0.03, "curve": [1.923, -7.8, 1.968, -6.98, 1.923, 0.03, 1.968, 0.03]}, {"time": 2, "x": -6.06, "y": 0.02, "curve": [2.044, -4.56, 2.078, -2.97, 2.044, 0.02, 2.078, 0.01]}, {"time": 2.1333, "x": -1.6, "y": 0.01, "curve": [2.157, -0.98, 2.193, -0.49, 2.157, 0, 2.193, 0]}, {"time": 2.2667, "x": -0.39, "curve": [2.4, -0.39, 2.667, -8, 2.4, 0, 2.667, 0.03]}, {"time": 2.8, "x": -8, "y": 0.03, "curve": [2.923, -7.8, 2.968, -6.98, 2.923, 0.03, 2.968, 0.03]}, {"time": 3, "x": -6.06, "y": 0.02, "curve": [3.044, -4.56, 3.078, -2.97, 3.044, 0.02, 3.078, 0.01]}, {"time": 3.1333, "x": -1.6, "y": 0.01, "curve": [3.157, -0.98, 3.193, -0.49, 3.157, 0, 3.193, 0]}, {"time": 3.2667, "x": -0.39, "curve": [3.4, -0.39, 3.667, -8, 3.4, 0, 3.667, 0.03]}, {"time": 3.8, "x": -8, "y": 0.03, "curve": [3.923, -7.8, 3.968, -6.98, 3.923, 0.03, 3.968, 0.03]}, {"time": 4, "x": -6.06, "y": 0.02}]}, "bone": {"scale": [{"x": 1.02, "y": 0.992, "curve": [0.297, 1.017, 0.242, 0.983, 0.297, 0.995, 0.242, 1.046]}, {"time": 0.4667, "x": 0.982, "y": 1.047, "curve": [0.806, 0.984, 0.744, 1.019, 0.806, 1.044, 0.744, 0.993]}, {"time": 1, "x": 1.02, "y": 0.992, "curve": [1.297, 1.017, 1.242, 0.983, 1.297, 0.995, 1.242, 1.046]}, {"time": 1.4667, "x": 0.982, "y": 1.047, "curve": [1.806, 0.984, 1.744, 1.019, 1.806, 1.044, 1.744, 0.993]}, {"time": 2, "x": 1.02, "y": 0.992, "curve": [2.297, 1.017, 2.242, 0.983, 2.297, 0.995, 2.242, 1.046]}, {"time": 2.4667, "x": 0.982, "y": 1.047, "curve": [2.806, 0.984, 2.744, 1.019, 2.806, 1.044, 2.744, 0.993]}, {"time": 3, "x": 1.02, "y": 0.992, "curve": [3.297, 1.017, 3.242, 0.983, 3.297, 0.995, 3.242, 1.046]}, {"time": 3.4667, "x": 0.982, "y": 1.047, "curve": [3.806, 0.984, 3.744, 1.019, 3.806, 1.044, 3.744, 0.993]}, {"time": 4, "x": 1.02, "y": 0.992}]}, "Head": {"rotate": [{"curve": [0.025, -0.48, 0.049, -0.81]}, {"time": 0.0667, "value": -0.81, "curve": [0.133, -0.81, 0.267, 2.76]}, {"time": 0.3333, "value": 2.76, "curve": [0.4, 2.76, 0.533, -0.81]}, {"time": 0.6, "value": -0.81, "curve": [0.658, -0.81, 0.775, 2.76]}, {"time": 0.8333, "value": 2.76, "curve": [0.866, 2.76, 0.921, 1]}, {"time": 0.9667, "curve": "stepped"}, {"time": 1, "curve": [1.025, -0.48, 1.049, -0.81]}, {"time": 1.0667, "value": -0.81, "curve": [1.133, -0.81, 1.267, 2.76]}, {"time": 1.3333, "value": 2.76, "curve": [1.4, 2.76, 1.533, -0.81]}, {"time": 1.6, "value": -0.81, "curve": [1.658, -0.81, 1.775, 2.76]}, {"time": 1.8333, "value": 2.76, "curve": [1.866, 2.76, 1.921, 1]}, {"time": 1.9667, "curve": "stepped"}, {"time": 2, "curve": [2.025, -0.48, 2.049, -0.81]}, {"time": 2.0667, "value": -0.81, "curve": [2.133, -0.81, 2.267, 2.76]}, {"time": 2.3333, "value": 2.76, "curve": [2.4, 2.76, 2.533, -0.81]}, {"time": 2.6, "value": -0.81, "curve": [2.658, -0.81, 2.775, 2.76]}, {"time": 2.8333, "value": 2.76, "curve": [2.866, 2.76, 2.921, 1]}, {"time": 2.9667, "curve": "stepped"}, {"time": 3, "curve": [3.025, -0.48, 3.049, -0.81]}, {"time": 3.0667, "value": -0.81, "curve": [3.133, -0.81, 3.267, 2.76]}, {"time": 3.3333, "value": 2.76, "curve": [3.4, 2.76, 3.533, -0.81]}, {"time": 3.6, "value": -0.81, "curve": [3.658, -0.81, 3.775, 2.76]}, {"time": 3.8333, "value": 2.76, "curve": [3.874, 2.76, 3.943, 1]}, {"time": 4}], "translate": [{"x": -1.6, "y": 0.01, "curve": [0.03, -0.98, 0.075, -0.49, 0.03, 0, 0.075, 0]}, {"time": 0.1667, "x": -0.39, "curve": [0.292, -0.39, 0.542, -8, 0.292, 0, 0.542, 0.03]}, {"time": 0.6667, "x": -8, "y": 0.03, "curve": [0.896, -7.43, 0.891, -4, 0.896, 0.03, 0.891, 0.02]}, {"time": 0.9667, "x": -1.6, "y": 0.01, "curve": "stepped"}, {"time": 1, "x": -1.6, "y": 0.01, "curve": [1.03, -0.98, 1.075, -0.49, 1.03, 0, 1.075, 0]}, {"time": 1.1667, "x": -0.39, "curve": [1.292, -0.39, 1.542, -8, 1.292, 0, 1.542, 0.03]}, {"time": 1.6667, "x": -8, "y": 0.03, "curve": [1.896, -7.43, 1.891, -4, 1.896, 0.03, 1.891, 0.02]}, {"time": 1.9667, "x": -1.6, "y": 0.01, "curve": "stepped"}, {"time": 2, "x": -1.6, "y": 0.01, "curve": [2.03, -0.98, 2.075, -0.49, 2.03, 0, 2.075, 0]}, {"time": 2.1667, "x": -0.39, "curve": [2.292, -0.39, 2.542, -8, 2.292, 0, 2.542, 0.03]}, {"time": 2.6667, "x": -8, "y": 0.03, "curve": [2.896, -7.43, 2.891, -4, 2.896, 0.03, 2.891, 0.02]}, {"time": 2.9667, "x": -1.6, "y": 0.01, "curve": "stepped"}, {"time": 3, "x": -1.6, "y": 0.01, "curve": [3.03, -0.98, 3.075, -0.49, 3.03, 0, 3.075, 0]}, {"time": 3.1667, "x": -0.39, "curve": [3.292, -0.39, 3.542, -8, 3.292, 0, 3.542, 0.03]}, {"time": 3.6667, "x": -8, "y": 0.03, "curve": [3.922, -7.43, 3.916, -4, 3.922, 0.03, 3.916, 0.02]}, {"time": 4, "x": -1.6, "y": 0.01}]}, "ArmRight": {"translate": [{"x": -2.66, "y": 0.01, "curve": [0.066, -0.25, 0.081, 2.47, 0.066, 0, 0.081, -0.01]}, {"time": 0.2667, "x": 2.84, "y": -0.01, "curve": [0.4, 2.84, 0.667, -4.23, 0.4, -0.01, 0.667, 0.02]}, {"time": 0.8, "x": -4.23, "y": 0.02, "curve": [0.92, -4.05, 0.968, -3.39, 0.92, 0.02, 0.968, 0.01]}, {"time": 1, "x": -2.66, "y": 0.01, "curve": [1.066, -0.25, 1.081, 2.47, 1.066, 0, 1.081, -0.01]}, {"time": 1.2667, "x": 2.84, "y": -0.01, "curve": [1.4, 2.84, 1.667, -4.23, 1.4, -0.01, 1.667, 0.02]}, {"time": 1.8, "x": -4.23, "y": 0.02, "curve": [1.92, -4.05, 1.968, -3.39, 1.92, 0.02, 1.968, 0.01]}, {"time": 2, "x": -2.66, "y": 0.01, "curve": [2.066, -0.25, 2.081, 2.47, 2.066, 0, 2.081, -0.01]}, {"time": 2.2667, "x": 2.84, "y": -0.01, "curve": [2.4, 2.84, 2.667, -4.23, 2.4, -0.01, 2.667, 0.02]}, {"time": 2.8, "x": -4.23, "y": 0.02, "curve": [2.92, -4.05, 2.968, -3.39, 2.92, 0.02, 2.968, 0.01]}, {"time": 3, "x": -2.66, "y": 0.01, "curve": [3.066, -0.25, 3.081, 2.47, 3.066, 0, 3.081, -0.01]}, {"time": 3.2667, "x": 2.84, "y": -0.01, "curve": [3.4, 2.84, 3.667, -4.23, 3.4, -0.01, 3.667, 0.02]}, {"time": 3.8, "x": -4.23, "y": 0.02, "curve": [3.92, -4.05, 3.968, -3.39, 3.92, 0.02, 3.968, 0.01]}, {"time": 4, "x": -2.66, "y": 0.01}]}, "ArmLeft": {"translate": [{"x": -2.66, "y": 0.01, "curve": [0.066, -0.25, 0.081, 2.47, 0.066, 0, 0.081, -0.01]}, {"time": 0.2667, "x": 2.84, "y": -0.01, "curve": [0.4, 2.84, 0.667, -4.23, 0.4, -0.01, 0.667, 0.02]}, {"time": 0.8, "x": -4.23, "y": 0.02, "curve": [0.92, -4.05, 0.968, -3.39, 0.92, 0.02, 0.968, 0.01]}, {"time": 1, "x": -2.66, "y": 0.01, "curve": [1.066, -0.25, 1.081, 2.47, 1.066, 0, 1.081, -0.01]}, {"time": 1.2667, "x": 2.84, "y": -0.01, "curve": [1.4, 2.84, 1.667, -4.23, 1.4, -0.01, 1.667, 0.02]}, {"time": 1.8, "x": -4.23, "y": 0.02, "curve": [1.92, -4.05, 1.968, -3.39, 1.92, 0.02, 1.968, 0.01]}, {"time": 2, "x": -2.66, "y": 0.01, "curve": [2.066, -0.25, 2.081, 2.47, 2.066, 0, 2.081, -0.01]}, {"time": 2.2667, "x": 2.84, "y": -0.01, "curve": [2.4, 2.84, 2.667, -4.23, 2.4, -0.01, 2.667, 0.02]}, {"time": 2.8, "x": -4.23, "y": 0.02, "curve": [2.92, -4.05, 2.968, -3.39, 2.92, 0.02, 2.968, 0.01]}, {"time": 3, "x": -2.66, "y": 0.01, "curve": [3.066, -0.25, 3.081, 2.47, 3.066, 0, 3.081, -0.01]}, {"time": 3.2667, "x": 2.84, "y": -0.01, "curve": [3.4, 2.84, 3.667, -4.23, 3.4, -0.01, 3.667, 0.02]}, {"time": 3.8, "x": -4.23, "y": 0.02, "curve": [3.92, -4.05, 3.968, -3.39, 3.92, 0.02, 3.968, 0.01]}, {"time": 4, "x": -2.66, "y": 0.01}]}, "bone2": {"rotate": [{"value": 0.39, "curve": [0.147, 0.29, 0.074, -0.8]}, {"time": 0.2333, "value": -0.88, "curve": [0.422, -0.78, 0.328, 0.31]}, {"time": 0.5333, "value": 0.39, "curve": [0.68, 0.29, 0.607, -0.8]}, {"time": 0.7667, "value": -0.88, "curve": [0.892, -0.78, 0.83, 0.31]}, {"time": 0.9667, "value": 0.39, "curve": "stepped"}, {"time": 1, "value": 0.39, "curve": [1.147, 0.29, 1.074, -0.8]}, {"time": 1.2333, "value": -0.88, "curve": [1.422, -0.78, 1.328, 0.31]}, {"time": 1.5333, "value": 0.39, "curve": [1.68, 0.29, 1.607, -0.8]}, {"time": 1.7667, "value": -0.88, "curve": [1.892, -0.78, 1.83, 0.31]}, {"time": 1.9667, "value": 0.39, "curve": "stepped"}, {"time": 2, "value": 0.39, "curve": [2.147, 0.29, 2.074, -0.8]}, {"time": 2.2333, "value": -0.88, "curve": [2.422, -0.78, 2.328, 0.31]}, {"time": 2.5333, "value": 0.39, "curve": [2.68, 0.29, 2.607, -0.8]}, {"time": 2.7667, "value": -0.88, "curve": [2.892, -0.78, 2.83, 0.31]}, {"time": 2.9667, "value": 0.39, "curve": "stepped"}, {"time": 3, "value": 0.39, "curve": [3.147, 0.29, 3.074, -0.8]}, {"time": 3.2333, "value": -0.88, "curve": [3.422, -0.78, 3.328, 0.31]}, {"time": 3.5333, "value": 0.39, "curve": [3.68, 0.29, 3.607, -0.8]}, {"time": 3.7667, "value": -0.88, "curve": [3.913, -0.78, 3.84, 0.31]}, {"time": 4, "value": 0.39}]}, "bone3": {"rotate": [{"value": -0.71, "curve": [0.055, -0.31, 0.049, 0.33]}, {"time": 0.2, "value": 0.39, "curve": [0.258, 0.39, 0.375, -0.88]}, {"time": 0.4333, "value": -0.88, "curve": [0.622, -0.78, 0.528, 0.31]}, {"time": 0.7333, "value": 0.39, "curve": [0.88, 0.3, 0.807, -0.63]}, {"time": 0.9667, "value": -0.71, "curve": "stepped"}, {"time": 1, "value": -0.71, "curve": [1.055, -0.31, 1.049, 0.33]}, {"time": 1.2, "value": 0.39, "curve": [1.258, 0.39, 1.375, -0.88]}, {"time": 1.4333, "value": -0.88, "curve": [1.622, -0.78, 1.528, 0.31]}, {"time": 1.7333, "value": 0.39, "curve": [1.88, 0.3, 1.807, -0.63]}, {"time": 1.9667, "value": -0.71, "curve": "stepped"}, {"time": 2, "value": -0.71, "curve": [2.055, -0.31, 2.049, 0.33]}, {"time": 2.2, "value": 0.39, "curve": [2.258, 0.39, 2.375, -0.88]}, {"time": 2.4333, "value": -0.88, "curve": [2.622, -0.78, 2.528, 0.31]}, {"time": 2.7333, "value": 0.39, "curve": [2.88, 0.3, 2.807, -0.63]}, {"time": 2.9667, "value": -0.71, "curve": "stepped"}, {"time": 3, "value": -0.71, "curve": [3.055, -0.31, 3.049, 0.33]}, {"time": 3.2, "value": 0.39, "curve": [3.258, 0.39, 3.375, -0.88]}, {"time": 3.4333, "value": -0.88, "curve": [3.622, -0.78, 3.528, 0.31]}, {"time": 3.7333, "value": 0.39, "curve": [3.901, 0.3, 3.817, -0.63]}, {"time": 4, "value": -0.71}]}, "bone4": {"rotate": [{"value": -0.88, "curve": [0.168, -0.78, 0.084, 0.31]}, {"time": 0.2667, "value": 0.39, "curve": [0.333, 0.39, 0.467, -0.88]}, {"time": 0.5333, "value": -0.88, "curve": [0.701, -0.78, 0.617, 0.31]}, {"time": 0.8, "value": 0.39, "curve": [0.905, 0.29, 0.853, -0.8]}, {"time": 0.9667, "value": -0.88, "curve": "stepped"}, {"time": 1, "value": -0.88, "curve": [1.168, -0.78, 1.084, 0.31]}, {"time": 1.2667, "value": 0.39, "curve": [1.333, 0.39, 1.467, -0.88]}, {"time": 1.5333, "value": -0.88, "curve": [1.701, -0.78, 1.617, 0.31]}, {"time": 1.8, "value": 0.39, "curve": [1.905, 0.29, 1.853, -0.8]}, {"time": 1.9667, "value": -0.88, "curve": "stepped"}, {"time": 2, "value": -0.88, "curve": [2.168, -0.78, 2.084, 0.31]}, {"time": 2.2667, "value": 0.39, "curve": [2.333, 0.39, 2.467, -0.88]}, {"time": 2.5333, "value": -0.88, "curve": [2.701, -0.78, 2.617, 0.31]}, {"time": 2.8, "value": 0.39, "curve": [2.905, 0.29, 2.853, -0.8]}, {"time": 2.9667, "value": -0.88, "curve": "stepped"}, {"time": 3, "value": -0.88, "curve": [3.168, -0.78, 3.084, 0.31]}, {"time": 3.2667, "value": 0.39, "curve": [3.333, 0.39, 3.467, -0.88]}, {"time": 3.5333, "value": -0.88, "curve": [3.701, -0.78, 3.617, 0.31]}, {"time": 3.8, "value": 0.39, "curve": [3.926, 0.29, 3.863, -0.8]}, {"time": 4, "value": -0.88}]}}}, "warning": {"slots": {"Body": {"attachment": [{"name": "BodyCentre"}]}, "Eyes": {"attachment": [{"name": "Eyes_Closed"}]}, "root": {"attachment": [{"time": 1.9}]}, "Smile": {"attachment": [{"name": "SmileSmall"}]}}, "bones": {"bone": {"scale": [{"x": 1.02, "y": 0.992, "curve": [0.297, 1.017, 0.242, 0.983, 0.297, 0.995, 0.242, 1.046]}, {"time": 0.4667, "x": 0.982, "y": 1.047, "curve": [0.806, 0.984, 0.744, 1.019, 0.806, 1.044, 0.744, 0.993]}, {"time": 1, "x": 1.02, "y": 0.992, "curve": [1.297, 1.017, 1.242, 0.983, 1.297, 0.995, 1.242, 1.046]}, {"time": 1.4667, "x": 0.982, "y": 1.047, "curve": [1.806, 0.984, 1.744, 1.019, 1.806, 1.044, 1.744, 0.993]}, {"time": 2, "x": 1.02, "y": 0.992, "curve": [2.297, 1.017, 2.242, 0.983, 2.297, 0.995, 2.242, 1.046]}, {"time": 2.4667, "x": 0.982, "y": 1.047, "curve": [2.806, 0.984, 2.744, 1.019, 2.806, 1.044, 2.744, 0.993]}, {"time": 3, "x": 1.02, "y": 0.992, "curve": [3.297, 1.017, 3.242, 0.983, 3.297, 0.995, 3.242, 1.046]}, {"time": 3.4667, "x": 0.982, "y": 1.047, "curve": [3.806, 0.984, 3.744, 1.019, 3.806, 1.044, 3.744, 0.993]}, {"time": 4, "x": 1.02, "y": 0.992}]}, "Eyes": {"translate": [{"x": -6.06, "y": 0.02, "curve": [0.044, -4.56, 0.078, -2.97, 0.044, 0.02, 0.078, 0.01]}, {"time": 0.1333, "x": -1.6, "y": 0.01, "curve": [0.157, -0.98, 0.193, -0.49, 0.157, 0, 0.193, 0]}, {"time": 0.2667, "x": -0.39, "curve": [0.4, -0.39, 0.667, -8, 0.4, 0, 0.667, 0.03]}, {"time": 0.8, "x": -8, "y": 0.03, "curve": [0.923, -7.8, 0.968, -6.98, 0.923, 0.03, 0.968, 0.03]}, {"time": 1, "x": -6.06, "y": 0.02, "curve": [1.044, -4.56, 1.078, -2.97, 1.044, 0.02, 1.078, 0.01]}, {"time": 1.1333, "x": -1.6, "y": 0.01, "curve": [1.157, -0.98, 1.193, -0.49, 1.157, 0, 1.193, 0]}, {"time": 1.2667, "x": -0.39, "curve": [1.4, -0.39, 1.667, -8, 1.4, 0, 1.667, 0.03]}, {"time": 1.8, "x": -8, "y": 0.03, "curve": [1.923, -7.8, 1.968, -6.98, 1.923, 0.03, 1.968, 0.03]}, {"time": 2, "x": -6.06, "y": 0.02, "curve": [2.044, -4.56, 2.078, -2.97, 2.044, 0.02, 2.078, 0.01]}, {"time": 2.1333, "x": -1.6, "y": 0.01, "curve": [2.157, -0.98, 2.193, -0.49, 2.157, 0, 2.193, 0]}, {"time": 2.2667, "x": -0.39, "curve": [2.4, -0.39, 2.667, -8, 2.4, 0, 2.667, 0.03]}, {"time": 2.8, "x": -8, "y": 0.03, "curve": [2.923, -7.8, 2.968, -6.98, 2.923, 0.03, 2.968, 0.03]}, {"time": 3, "x": -6.06, "y": 0.02, "curve": [3.044, -4.56, 3.078, -2.97, 3.044, 0.02, 3.078, 0.01]}, {"time": 3.1333, "x": -1.6, "y": 0.01, "curve": [3.157, -0.98, 3.193, -0.49, 3.157, 0, 3.193, 0]}, {"time": 3.2667, "x": -0.39, "curve": [3.4, -0.39, 3.667, -8, 3.4, 0, 3.667, 0.03]}, {"time": 3.8, "x": -8, "y": 0.03, "curve": [3.923, -7.8, 3.968, -6.98, 3.923, 0.03, 3.968, 0.03]}, {"time": 4, "x": -6.06, "y": 0.02}]}, "Head": {"rotate": [{"curve": [0.025, -0.48, 0.049, -0.81]}, {"time": 0.0667, "value": -0.81, "curve": [0.133, -0.81, 0.267, 2.76]}, {"time": 0.3333, "value": 2.76, "curve": [0.4, 2.76, 0.533, -0.81]}, {"time": 0.6, "value": -0.81, "curve": [0.658, -0.81, 0.775, 2.76]}, {"time": 0.8333, "value": 2.76, "curve": [0.866, 2.76, 0.921, 1]}, {"time": 0.9667, "curve": "stepped"}, {"time": 1, "curve": [1.025, -0.48, 1.049, -0.81]}, {"time": 1.0667, "value": -0.81, "curve": [1.133, -0.81, 1.267, 2.76]}, {"time": 1.3333, "value": 2.76, "curve": [1.4, 2.76, 1.533, -0.81]}, {"time": 1.6, "value": -0.81, "curve": [1.658, -0.81, 1.775, 2.76]}, {"time": 1.8333, "value": 2.76, "curve": [1.866, 2.76, 1.921, 1]}, {"time": 1.9667, "curve": "stepped"}, {"time": 2, "curve": [2.025, -0.48, 2.049, -0.81]}, {"time": 2.0667, "value": -0.81, "curve": [2.133, -0.81, 2.267, 2.76]}, {"time": 2.3333, "value": 2.76, "curve": [2.4, 2.76, 2.533, -0.81]}, {"time": 2.6, "value": -0.81, "curve": [2.658, -0.81, 2.775, 2.76]}, {"time": 2.8333, "value": 2.76, "curve": [2.866, 2.76, 2.921, 1]}, {"time": 2.9667, "curve": "stepped"}, {"time": 3, "curve": [3.025, -0.48, 3.049, -0.81]}, {"time": 3.0667, "value": -0.81, "curve": [3.133, -0.81, 3.267, 2.76]}, {"time": 3.3333, "value": 2.76, "curve": [3.4, 2.76, 3.533, -0.81]}, {"time": 3.6, "value": -0.81, "curve": [3.658, -0.81, 3.775, 2.76]}, {"time": 3.8333, "value": 2.76, "curve": [3.874, 2.76, 3.943, 1]}, {"time": 4}], "translate": [{"x": 3.67, "y": -4.48, "curve": [0.03, 4.29, 0.075, 4.77, 0.03, -4.48, 0.075, -4.48]}, {"time": 0.1667, "x": 4.88, "y": -4.48, "curve": [0.292, 4.88, 0.542, -2.74, 0.292, -4.48, 0.542, -4.45]}, {"time": 0.6667, "x": -2.74, "y": -4.45, "curve": [0.896, -2.16, 0.891, 1.26, 0.896, -4.46, 0.891, -4.47]}, {"time": 0.9667, "x": 3.67, "y": -4.48, "curve": "stepped"}, {"time": 1, "x": 3.67, "y": -4.48, "curve": [1.03, 4.29, 1.075, 4.77, 1.03, -4.48, 1.075, -4.48]}, {"time": 1.1667, "x": 4.88, "y": -4.48, "curve": [1.292, 4.88, 1.542, -2.74, 1.292, -4.48, 1.542, -4.45]}, {"time": 1.6667, "x": -2.74, "y": -4.45, "curve": [1.896, -2.16, 1.891, 1.26, 1.896, -4.46, 1.891, -4.47]}, {"time": 1.9667, "x": 3.67, "y": -4.48, "curve": "stepped"}, {"time": 2, "x": 3.67, "y": -4.48, "curve": [2.03, 4.29, 2.075, 4.77, 2.03, -4.48, 2.075, -4.48]}, {"time": 2.1667, "x": 4.88, "y": -4.48, "curve": [2.292, 4.88, 2.542, -2.74, 2.292, -4.48, 2.542, -4.45]}, {"time": 2.6667, "x": -2.74, "y": -4.45, "curve": [2.896, -2.16, 2.891, 1.26, 2.896, -4.46, 2.891, -4.47]}, {"time": 2.9667, "x": 3.67, "y": -4.48, "curve": "stepped"}, {"time": 3, "x": 3.67, "y": -4.48, "curve": [3.03, 4.29, 3.075, 4.77, 3.03, -4.48, 3.075, -4.48]}, {"time": 3.1667, "x": 4.88, "y": -4.48, "curve": [3.292, 4.88, 3.542, -2.74, 3.292, -4.48, 3.542, -4.45]}, {"time": 3.6667, "x": -2.74, "y": -4.45, "curve": [3.922, -2.16, 3.916, 1.26, 3.922, -4.46, 3.916, -4.47]}, {"time": 4, "x": 3.67, "y": -4.48}]}, "ArmRight": {"rotate": [{"value": -175.82, "curve": [0.214, -175.33, 0.12, -172.29]}, {"time": 0.3333, "value": -171.92, "curve": [0.547, -172.41, 0.453, -175.45]}, {"time": 0.6667, "value": -175.82, "curve": [0.88, -175.33, 0.786, -172.29]}, {"time": 1, "value": -171.92, "curve": [1.214, -172.41, 1.12, -175.45]}, {"time": 1.3333, "value": -175.82, "curve": [1.547, -175.33, 1.453, -172.29]}, {"time": 1.6667, "value": -171.92, "curve": [1.88, -172.41, 1.786, -175.45]}, {"time": 2, "value": -175.82, "curve": [2.214, -175.33, 2.12, -172.29]}, {"time": 2.3333, "value": -171.92, "curve": [2.547, -172.41, 2.453, -175.45]}, {"time": 2.6667, "value": -175.82, "curve": [2.88, -175.33, 2.786, -172.29]}, {"time": 3, "value": -171.92, "curve": [3.214, -172.41, 3.12, -175.45]}, {"time": 3.3333, "value": -175.82, "curve": [3.547, -175.33, 3.453, -172.29]}, {"time": 3.6667, "value": -171.92, "curve": [3.88, -172.41, 3.786, -175.45]}, {"time": 4, "value": -175.82}], "translate": [{"x": 6.75, "y": -36.23}]}, "ArmLeft": {"rotate": [{"value": 154.19, "curve": [0.207, 152.01, 0.105, 142.62]}, {"time": 0.3333, "value": 141.17, "curve": [0.54, 143.36, 0.438, 152.74]}, {"time": 0.6667, "value": 154.19, "curve": [0.873, 152.01, 0.772, 142.62]}, {"time": 1, "value": 141.17, "curve": [1.207, 143.36, 1.105, 152.74]}, {"time": 1.3333, "value": 154.19, "curve": [1.54, 152.01, 1.438, 142.62]}, {"time": 1.6667, "value": 141.17, "curve": [1.873, 143.36, 1.772, 152.74]}, {"time": 2, "value": 154.19, "curve": [2.207, 152.01, 2.105, 142.62]}, {"time": 2.3333, "value": 141.17, "curve": [2.54, 143.36, 2.438, 152.74]}, {"time": 2.6667, "value": 154.19, "curve": [2.873, 152.01, 2.772, 142.62]}, {"time": 3, "value": 141.17, "curve": [3.207, 143.36, 3.105, 152.74]}, {"time": 3.3333, "value": 154.19, "curve": [3.54, 152.01, 3.438, 142.62]}, {"time": 3.6667, "value": 141.17, "curve": [3.873, 143.36, 3.772, 152.74]}, {"time": 4, "value": 154.19}], "translate": [{"x": 7.79, "y": 12.48}]}, "bone2": {"rotate": [{"value": 0.39, "curve": [0.147, 0.29, 0.074, -0.8]}, {"time": 0.2333, "value": -0.88, "curve": [0.422, -0.78, 0.328, 0.31]}, {"time": 0.5333, "value": 0.39, "curve": [0.68, 0.29, 0.607, -0.8]}, {"time": 0.7667, "value": -0.88, "curve": [0.892, -0.78, 0.83, 0.31]}, {"time": 0.9667, "value": 0.39, "curve": "stepped"}, {"time": 1, "value": 0.39, "curve": [1.147, 0.29, 1.074, -0.8]}, {"time": 1.2333, "value": -0.88, "curve": [1.422, -0.78, 1.328, 0.31]}, {"time": 1.5333, "value": 0.39, "curve": [1.68, 0.29, 1.607, -0.8]}, {"time": 1.7667, "value": -0.88, "curve": [1.892, -0.78, 1.83, 0.31]}, {"time": 1.9667, "value": 0.39, "curve": "stepped"}, {"time": 2, "value": 0.39, "curve": [2.147, 0.29, 2.074, -0.8]}, {"time": 2.2333, "value": -0.88, "curve": [2.422, -0.78, 2.328, 0.31]}, {"time": 2.5333, "value": 0.39, "curve": [2.68, 0.29, 2.607, -0.8]}, {"time": 2.7667, "value": -0.88, "curve": [2.892, -0.78, 2.83, 0.31]}, {"time": 2.9667, "value": 0.39, "curve": "stepped"}, {"time": 3, "value": 0.39, "curve": [3.147, 0.29, 3.074, -0.8]}, {"time": 3.2333, "value": -0.88, "curve": [3.422, -0.78, 3.328, 0.31]}, {"time": 3.5333, "value": 0.39, "curve": [3.68, 0.29, 3.607, -0.8]}, {"time": 3.7667, "value": -0.88, "curve": [3.913, -0.78, 3.84, 0.31]}, {"time": 4, "value": 0.39}]}, "bone3": {"rotate": [{"value": -0.71, "curve": [0.055, -0.31, 0.049, 0.33]}, {"time": 0.2, "value": 0.39, "curve": [0.258, 0.39, 0.375, -0.88]}, {"time": 0.4333, "value": -0.88, "curve": [0.622, -0.78, 0.528, 0.31]}, {"time": 0.7333, "value": 0.39, "curve": [0.88, 0.3, 0.807, -0.63]}, {"time": 0.9667, "value": -0.71, "curve": "stepped"}, {"time": 1, "value": -0.71, "curve": [1.055, -0.31, 1.049, 0.33]}, {"time": 1.2, "value": 0.39, "curve": [1.258, 0.39, 1.375, -0.88]}, {"time": 1.4333, "value": -0.88, "curve": [1.622, -0.78, 1.528, 0.31]}, {"time": 1.7333, "value": 0.39, "curve": [1.88, 0.3, 1.807, -0.63]}, {"time": 1.9667, "value": -0.71, "curve": "stepped"}, {"time": 2, "value": -0.71, "curve": [2.055, -0.31, 2.049, 0.33]}, {"time": 2.2, "value": 0.39, "curve": [2.258, 0.39, 2.375, -0.88]}, {"time": 2.4333, "value": -0.88, "curve": [2.622, -0.78, 2.528, 0.31]}, {"time": 2.7333, "value": 0.39, "curve": [2.88, 0.3, 2.807, -0.63]}, {"time": 2.9667, "value": -0.71, "curve": "stepped"}, {"time": 3, "value": -0.71, "curve": [3.055, -0.31, 3.049, 0.33]}, {"time": 3.2, "value": 0.39, "curve": [3.258, 0.39, 3.375, -0.88]}, {"time": 3.4333, "value": -0.88, "curve": [3.622, -0.78, 3.528, 0.31]}, {"time": 3.7333, "value": 0.39, "curve": [3.901, 0.3, 3.817, -0.63]}, {"time": 4, "value": -0.71}]}, "bone4": {"rotate": [{"value": -0.88, "curve": [0.168, -0.78, 0.084, 0.31]}, {"time": 0.2667, "value": 0.39, "curve": [0.333, 0.39, 0.467, -0.88]}, {"time": 0.5333, "value": -0.88, "curve": [0.701, -0.78, 0.617, 0.31]}, {"time": 0.8, "value": 0.39, "curve": [0.905, 0.29, 0.853, -0.8]}, {"time": 0.9667, "value": -0.88, "curve": "stepped"}, {"time": 1, "value": -0.88, "curve": [1.168, -0.78, 1.084, 0.31]}, {"time": 1.2667, "value": 0.39, "curve": [1.333, 0.39, 1.467, -0.88]}, {"time": 1.5333, "value": -0.88, "curve": [1.701, -0.78, 1.617, 0.31]}, {"time": 1.8, "value": 0.39, "curve": [1.905, 0.29, 1.853, -0.8]}, {"time": 1.9667, "value": -0.88, "curve": "stepped"}, {"time": 2, "value": -0.88, "curve": [2.168, -0.78, 2.084, 0.31]}, {"time": 2.2667, "value": 0.39, "curve": [2.333, 0.39, 2.467, -0.88]}, {"time": 2.5333, "value": -0.88, "curve": [2.701, -0.78, 2.617, 0.31]}, {"time": 2.8, "value": 0.39, "curve": [2.905, 0.29, 2.853, -0.8]}, {"time": 2.9667, "value": -0.88, "curve": "stepped"}, {"time": 3, "value": -0.88, "curve": [3.168, -0.78, 3.084, 0.31]}, {"time": 3.2667, "value": 0.39, "curve": [3.333, 0.39, 3.467, -0.88]}, {"time": 3.5333, "value": -0.88, "curve": [3.701, -0.78, 3.617, 0.31]}, {"time": 3.8, "value": 0.39, "curve": [3.926, 0.29, 3.863, -0.8]}, {"time": 4, "value": -0.88}]}, "ArmRight2": {"rotate": [{"value": 30.11, "curve": [0.264, 31.76, 0.159, 43.18]}, {"time": 0.3333, "value": 44.13, "curve": [0.598, 42.48, 0.492, 31.06]}, {"time": 0.6667, "value": 30.11, "curve": [0.931, 31.76, 0.825, 43.18]}, {"time": 1, "value": 44.13, "curve": [1.264, 42.48, 1.159, 31.06]}, {"time": 1.3333, "value": 30.11, "curve": [1.598, 31.76, 1.492, 43.18]}, {"time": 1.6667, "value": 44.13, "curve": [1.931, 42.48, 1.825, 31.06]}, {"time": 2, "value": 30.11, "curve": [2.264, 31.76, 2.159, 43.18]}, {"time": 2.3333, "value": 44.13, "curve": [2.598, 42.48, 2.492, 31.06]}, {"time": 2.6667, "value": 30.11, "curve": [2.931, 31.76, 2.825, 43.18]}, {"time": 3, "value": 44.13, "curve": [3.264, 42.48, 3.159, 31.06]}, {"time": 3.3333, "value": 30.11, "curve": [3.598, 31.76, 3.492, 43.18]}, {"time": 3.6667, "value": 44.13, "curve": [3.931, 42.48, 3.825, 31.06]}, {"time": 4, "value": 30.11}]}, "ArmRight3": {"rotate": [{"value": 44.13, "curve": [0.264, 42.48, 0.159, 31.06]}, {"time": 0.3333, "value": 30.11, "curve": [0.598, 31.76, 0.492, 43.18]}, {"time": 0.6667, "value": 44.13, "curve": [0.931, 42.48, 0.825, 31.06]}, {"time": 1, "value": 30.11, "curve": [1.264, 31.76, 1.159, 43.18]}, {"time": 1.3333, "value": 44.13, "curve": [1.598, 42.48, 1.492, 31.06]}, {"time": 1.6667, "value": 30.11, "curve": [1.931, 31.76, 1.825, 43.18]}, {"time": 2, "value": 44.13, "curve": [2.264, 42.48, 2.159, 31.06]}, {"time": 2.3333, "value": 30.11, "curve": [2.598, 31.76, 2.492, 43.18]}, {"time": 2.6667, "value": 44.13, "curve": [2.931, 42.48, 2.825, 31.06]}, {"time": 3, "value": 30.11, "curve": [3.264, 31.76, 3.159, 43.18]}, {"time": 3.3333, "value": 44.13, "curve": [3.598, 42.48, 3.492, 31.06]}, {"time": 3.6667, "value": 30.11, "curve": [3.931, 31.76, 3.825, 43.18]}, {"time": 4, "value": 44.13}]}, "ArmLeft2": {"rotate": [{"value": -25.27, "curve": [0.028, -22.8, 0.058, -20.41]}, {"time": 0.1667, "value": -19.77, "curve": [0.25, -19.77, 0.417, -32.79]}, {"time": 0.5, "value": -32.79, "curve": [0.707, -30.6, 0.605, -21.22]}, {"time": 0.8333, "value": -19.77, "curve": [1.04, -21.95, 0.938, -31.34]}, {"time": 1.1667, "value": -32.79, "curve": [1.373, -30.6, 1.272, -21.22]}, {"time": 1.5, "value": -19.77, "curve": [1.707, -21.95, 1.605, -31.34]}, {"time": 1.8333, "value": -32.79, "curve": [2.04, -30.6, 1.938, -21.22]}, {"time": 2.1667, "value": -19.77, "curve": [2.373, -21.95, 2.272, -31.34]}, {"time": 2.5, "value": -32.79, "curve": [2.707, -30.6, 2.605, -21.22]}, {"time": 2.8333, "value": -19.77, "curve": [3.04, -21.95, 2.938, -31.34]}, {"time": 3.1667, "value": -32.79, "curve": [3.373, -30.6, 3.272, -21.22]}, {"time": 3.5, "value": -19.77, "curve": [3.707, -21.95, 3.605, -31.34]}, {"time": 3.8333, "value": -32.79, "curve": [3.942, -31.58, 3.97, -28.37]}, {"time": 4, "value": -25.27}]}, "ArmLeft3": {"rotate": [{"value": -32.79, "curve": [0.207, -30.6, 0.105, -21.22]}, {"time": 0.3333, "value": -19.77, "curve": [0.417, -19.77, 0.583, -32.79]}, {"time": 0.6667, "value": -32.79, "curve": [0.873, -30.6, 0.772, -21.22]}, {"time": 1, "value": -19.77, "curve": [1.207, -21.95, 1.105, -31.34]}, {"time": 1.3333, "value": -32.79, "curve": [1.54, -30.6, 1.438, -21.22]}, {"time": 1.6667, "value": -19.77, "curve": [1.873, -21.95, 1.772, -31.34]}, {"time": 2, "value": -32.79, "curve": [2.207, -30.6, 2.105, -21.22]}, {"time": 2.3333, "value": -19.77, "curve": [2.54, -21.95, 2.438, -31.34]}, {"time": 2.6667, "value": -32.79, "curve": [2.873, -30.6, 2.772, -21.22]}, {"time": 3, "value": -19.77, "curve": [3.207, -21.95, 3.105, -31.34]}, {"time": 3.3333, "value": -32.79, "curve": [3.54, -30.6, 3.438, -21.22]}, {"time": 3.6667, "value": -19.77, "curve": [3.873, -21.95, 3.772, -31.34]}, {"time": 4, "value": -32.79}]}}}}}