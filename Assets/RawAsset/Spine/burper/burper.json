{"skeleton": {"hash": "vzUaeDZEGkM", "spine": "4.1.24", "images": "./images/", "audio": "C:/Users/<USER>/Desktop/咩咩启示录（Cult of the Lamb）/burper"}, "bones": [{"name": "root"}, {"name": "Main", "parent": "root", "y": -0.74}, {"name": "Body", "parent": "Main", "length": 37.38, "rotation": 89.98, "x": 0.31, "y": 30.47}, {"name": "Face", "parent": "Body", "rotation": -89.98, "x": 66.02, "y": 2.58}, {"name": "EyeRight", "parent": "Face", "x": 27.5, "y": 21.57}, {"name": "EyeLeft", "parent": "Face", "x": -30.38, "y": 21.57, "scaleX": -1}, {"name": "MouthBtm", "parent": "Face", "x": -0.51, "y": -37.22}, {"name": "MouthTop", "parent": "Face", "x": -1.13, "y": -21.15}, {"name": "LegRight", "parent": "Body", "length": 22.9, "rotation": -56.48, "x": 18.14, "y": -55.06, "scaleX": 1.1973, "scaleY": 1.1973}, {"name": "LegRightBtm", "parent": "LegRight", "length": 36.47, "rotation": -121.85, "x": 25.87, "scaleX": 1.1973, "scaleY": 1.1973}, {"name": "FootRight", "parent": "Main", "length": 15.13, "rotation": -11.15, "x": 80.23, "y": 14.65, "scaleX": 1.1973, "scaleY": 1.1973, "color": "ff3f00ff"}, {"name": "LegLeft", "parent": "Body", "length": 22.9, "rotation": -116.2, "x": 19.54, "y": 42.72, "scaleX": -1.1973, "scaleY": 1.1973}, {"name": "LegLeftBtm", "parent": "LegLeft", "length": 36.47, "rotation": -116.97, "x": 25.87, "scaleX": 1.1973, "scaleY": 1.1973}, {"name": "FootLeft", "parent": "Main", "length": 15.13, "rotation": 11.42, "x": -62.42, "y": 15.1, "scaleX": -1.1973, "scaleY": 1.1973, "color": "ff3f00ff"}, {"name": "ArmRight", "parent": "Face", "length": 22.89, "rotation": -86.33, "x": 67.27, "y": -56.37}, {"name": "ArmLeft", "parent": "Face", "length": 21.49, "rotation": -90, "x": -59.69, "y": -50.41}, {"name": "TwigRight", "parent": "Body", "length": 16.21, "rotation": -54.04, "x": 46.72, "y": -58.69}, {"name": "TwigLeft", "parent": "Body", "length": 20.8, "rotation": 52.06, "x": 42.1, "y": 58.08}, {"name": "BurperMouth", "parent": "Face", "x": 0.97, "y": -4.17}, {"name": "Horn", "parent": "Body", "length": 28.56, "rotation": 0.02, "x": 97.23, "y": 0.4}, {"name": "CheekRight", "parent": "BurperMouth", "rotation": -0.47, "x": 38.88, "y": -6.26}, {"name": "CheekLeft", "parent": "BurperMouth", "rotation": -0.47, "x": -38.08, "y": -6.26}], "slots": [{"name": "Burper/Horns", "bone": "Horn", "attachment": "Horns"}, {"name": "images/PawsBack", "bone": "Face", "attachment": "PawsBack"}, {"name": "EyeRightBack", "bone": "EyeRight", "attachment": "images/Eye"}, {"name": "EyeLeftBack", "bone": "EyeLeft", "attachment": "images/Eye"}, {"name": "LegRightBehind", "bone": "LegRight", "attachment": "LegRightBehind"}, {"name": "LegLeftBehind", "bone": "LegLeft", "attachment": "LegLeftBehind"}, {"name": "Body", "bone": "Body", "attachment": "Body"}, {"name": "LegRight", "bone": "LegRight", "attachment": "LegRight"}, {"name": "LegLeft", "bone": "LegLeft", "attachment": "LegLeft"}, {"name": "EyeRight", "bone": "EyeRight", "attachment": "images/Eye"}, {"name": "EyeLeft", "bone": "EyeLeft", "attachment": "images/Eye"}, {"name": "images/Mouth", "bone": "Face"}, {"name": "images/Paws", "bone": "Face", "attachment": "Paws"}, {"name": "images/Nose", "bone": "Face"}, {"name": "Burper/Mouth_Shut", "bone": "BurperMouth", "attachment": "Burper/Mouth_Shut"}, {"name": "Burper/CheekBall1", "bone": "CheekRight", "attachment": "CheekBall1"}, {"name": "Burper/CheekBall2", "bone": "CheekLeft", "attachment": "CheekBall1"}], "ik": [{"name": "FootLeft", "order": 1, "bones": ["LegLeft", "LegLeftBtm"], "target": "FootLeft"}, {"name": "FootRight", "bones": ["LegRight", "LegRightBtm"], "target": "FootRight", "bendPositive": false}], "transform": [{"name": "MouthBtm", "order": 2, "bones": ["MouthTop"], "target": "MouthBtm", "mixRotate": 0, "mixX": -0.941, "mixScaleX": 0, "mixShearY": 0}], "skins": [{"name": "burper-back", "attachments": {"Body": {"Body": {"name": "Bur<PERSON>/Body_Back", "type": "mesh", "uvs": [0.73531, 0.07534, 0.88455, 0.12876, 0.88696, 0.30867, 1, 0.45485, 1, 0.68818, 0.94232, 0.8934, 0.73049, 1, 0.24424, 1, 0.04926, 0.8428, 0, 0.66751, 0, 0.39906, 0.08055, 0.30024, 0.08055, 0.14562, 0.2298, 0.08097, 0.29479, 0, 0.68235, 0], "triangles": [11, 12, 13, 11, 13, 2, 14, 15, 0, 0, 13, 14, 13, 0, 2, 2, 0, 1, 9, 10, 11, 7, 8, 9, 2, 4, 6, 4, 2, 3, 2, 7, 11, 7, 9, 11, 6, 7, 2, 5, 6, 4], "vertices": [2, 2, 99.37, -41.63, 0.12625, 3, 44.22, 33.34, 0.87375, 2, 2, 93.75, -64.14, 0.26168, 3, 66.73, 27.71, 0.73832, 2, 2, 66.41, -68.17, 0.50748, 3, 70.75, 0.37, 0.49252, 2, 2, 49.53, -85.85, 0.71828, 3, 88.43, -16.51, 0.28172, 2, 2, 11.74, -85.86, 0.90201, 3, 88.43, -54.3, 0.09799, 2, 2, -17.61, -76.23, 0.97507, 3, 78.79, -83.64, 0.02493, 2, 2, -32.86, -40.86, 0.99974, 3, 43.42, -98.89, 0.00026, 2, 2, -32.88, 40.34, 0.99324, 3, -37.79, -98.89, 0.00676, 2, 2, -10.41, 72.91, 0.91794, 3, -70.35, -76.41, 0.08206, 2, 2, 14.65, 81.14, 0.81604, 3, -78.57, -51.34, 0.18396, 2, 2, 53.19, 75.19, 0.4941, 3, -72.6, -12.81, 0.5059, 2, 2, 67.98, 68.91, 0.31349, 3, -66.33, 1.98, 0.68651, 2, 2, 93.71, 68.92, 0.14033, 3, -66.33, 27.71, 0.85967, 2, 2, 98.54, 42.79, 0.05166, 3, -40.2, 32.53, 0.94834, 2, 2, 110.12, 31.94, 0.00632, 3, -29.34, 44.11, 0.99368, 2, 2, 110.14, -32.78, 0.04888, 3, 35.38, 44.11, 0.95112], "hull": 16, "edges": [0, 30, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30], "width": 167, "height": 143}}, "Burper/Horns": {"Horns": {"name": "Burper/Horns", "x": 24.78, "y": 1.21, "rotation": -90, "width": 119, "height": 67}}, "EyeLeftBack": {"images/Eye": {"name": "Bur<PERSON>/Eye_Back", "x": 0.5, "y": 0.5, "width": 54, "height": 51}, "images/Eye_Closed": {"x": 0.39, "y": -2.53, "scaleX": -0.8691, "rotation": -100.23, "width": 25, "height": 31}}, "EyeRightBack": {"images/Eye": {"name": "Bur<PERSON>/Eye_Back", "x": 0.5, "y": 0.5, "width": 54, "height": 51}}, "images/PawsBack": {"PawsBack": {"name": "Burper/Paws", "type": "mesh", "uvs": [1, 0.37533, 0.97166, 0.76748, 0.94911, 1, 0.78574, 1, 0.2185, 1, 0.05269, 1, 0.02196, 0.62546, 0, 0.24984, 0, 0, 0.2105, 0, 0.78065, 0, 1, 0, 0.20469, 0.61751, 0.20323, 0.23394, 0.79301, 0.29303, 0.79301, 0.68732], "triangles": [7, 8, 13, 14, 12, 13, 6, 7, 13, 12, 6, 13, 5, 6, 12, 4, 12, 14, 5, 12, 4, 14, 13, 10, 9, 10, 13, 13, 8, 9, 14, 10, 11, 14, 11, 0, 15, 14, 0, 1, 15, 0, 15, 4, 14, 3, 4, 15, 2, 3, 15, 1, 2, 15], "vertices": [2, 14, 0.65, 31.9, 0.632, 2, 18.57, -56.58, 0.368, 2, 14, 21.08, 17.13, 0.856, 2, -2.77, -43.16, 0.144, 1, 14, 33.07, 7, 1, 2, 14, 31.36, -19.83, 0.99815, 2, -15.4, -6.93, 0.00185, 2, 2, -21.72, 22.1, 0.05193, 15, 38.33, 14.11, 0.94807, 1, 15, 38.52, -11.85, 1, 2, 2, -0.06, 59.61, 0.24, 15, 16.66, -23.39, 0.76, 2, 2, 21.81, 69.28, 0.464, 15, -5.21, -33.06, 0.536, 2, 2, 36.15, 71.87, 0.56, 15, -19.56, -35.65, 0.44, 2, 2, 36.46, 39.3, 0.64, 15, -19.86, -3.07, 0.36, 2, 14, -21.21, 7.68, 0.36, 2, 38.82, -31, 0.64, 2, 14, -18.89, 43.8, 0.36, 2, 38.83, -67.2, 0.64, 2, 2, 0.34, 29.04, 0.22442, 15, 16.27, 7.18, 0.77558, 2, 2, 22.71, 35.75, 0.464, 15, -6.1, 0.47, 0.536, 2, 14, -5.63, 1.83, 0.536, 2, 22.9, -26.17, 0.464, 2, 14, 14.96, -10.13, 0.808, 2, 1.58, -15.56, 0.192], "hull": 12, "edges": [0, 22, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22], "width": 165, "height": 56}}, "LegLeft": {"LegLeft": {"name": "Burper/Leg", "type": "mesh", "uvs": [0.84583, 0.18737, 0.90983, 0.34823, 0.90983, 0.3928, 0.90183, 0.45137, 0.87783, 0.51823, 0.82183, 0.77537, 0.79783, 0.82679, 1, 0.92965, 1, 1, 0, 1, 0, 0.89879, 0, 0.84908, 0, 0.51651, 0, 0.47708, 0, 0.43594, 0.004, 0.38937, 0, 0.23537, 0, 0, 0.52183, 0], "triangles": [9, 6, 8, 6, 7, 8, 9, 10, 6, 10, 11, 6, 6, 11, 5, 5, 11, 12, 5, 12, 4, 14, 3, 4, 4, 12, 13, 4, 13, 14, 14, 15, 3, 3, 15, 2, 2, 15, 1, 1, 16, 0, 1, 15, 16, 16, 18, 0, 16, 17, 18], "vertices": [2, 11, 6.71, 15.96, 0.96777, 12, -24.24, 15.96, 0.03223, 2, 11, 21.35, 18.07, 0.52547, 12, -9.61, 18.07, 0.47453, 2, 11, 25.51, 18.07, 0.38285, 12, -5.45, 18.07, 0.61715, 2, 11, 30.88, 17.8, 0.21407, 12, -0.08, 17.8, 0.78593, 2, 11, 36.72, 17.01, 0.07688, 12, 5.77, 17.01, 0.92312, 2, 12, 25.22, 15.16, 0.856, 13, 14.44, 7.51, 0.144, 2, 12, 26.48, 14.37, 0.32444, 13, 13.72, 6.21, 0.67556, 2, 12, 32.86, 21.04, 0.02232, 13, 20.76, 0.24, 0.97768, 1, 13, 21.08, -5.05, 1, 1, 13, -11.56, -11.92, 1, 1, 13, -12.02, -4.33, 1, 2, 12, 31.63, -11.96, 0.888, 13, -12.25, -0.51, 0.112, 2, 11, 36.48, -11.96, 0.09737, 12, 5.53, -11.96, 0.90263, 2, 11, 32.81, -11.96, 0.22319, 12, 1.85, -11.96, 0.77681, 2, 11, 28.6, -11.96, 0.42764, 12, -2.35, -11.96, 0.57236, 2, 11, 23.75, -11.82, 0.6765, 12, -7.21, -11.82, 0.3235, 1, 11, 10.24, -11.96, 1, 1, 11, -7.88, -11.96, 1, 1, 11, -7.88, 5.26, 1], "hull": 19, "edges": [0, 36, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36], "width": 46, "height": 105}}, "LegRight": {"LegRight": {"name": "Burper/Leg", "type": "mesh", "uvs": [0.84583, 0.18737, 0.90983, 0.34823, 0.90983, 0.3928, 0.90183, 0.45137, 0.87783, 0.51823, 0.82183, 0.77537, 0.79783, 0.82679, 1, 0.92965, 1, 1, 0, 1, 0, 0.89879, 0, 0.84908, 0, 0.51651, 0, 0.47708, 0, 0.43594, 0.004, 0.38937, 0, 0.23537, 0, 0, 0.52183, 0], "triangles": [10, 11, 6, 9, 10, 6, 6, 7, 8, 9, 6, 8, 3, 15, 2, 14, 15, 3, 4, 13, 14, 4, 12, 13, 14, 3, 4, 5, 12, 4, 5, 11, 12, 6, 11, 5, 16, 17, 18, 16, 18, 0, 1, 15, 16, 1, 16, 0, 2, 15, 1], "vertices": [2, 8, 6.71, 15.96, 0.96777, 9, -24.24, 15.96, 0.03223, 2, 8, 21.35, 18.07, 0.52547, 9, -9.61, 18.07, 0.47453, 2, 8, 25.51, 18.07, 0.38285, 9, -5.45, 18.07, 0.61715, 2, 8, 30.88, 17.8, 0.21407, 9, -0.08, 17.8, 0.78593, 2, 8, 36.72, 17.01, 0.07688, 9, 5.77, 17.01, 0.92312, 2, 9, 25.22, 15.16, 0.904, 10, 14.44, 7.51, 0.096, 2, 9, 26.48, 14.37, 0.32444, 10, 13.72, 6.21, 0.67556, 2, 9, 32.86, 21.04, 0.02232, 10, 20.76, 0.24, 0.97768, 1, 10, 21.08, -5.05, 1, 1, 10, -11.56, -11.92, 1, 1, 10, -12.02, -4.33, 1, 2, 9, 31.63, -11.96, 0.936, 10, -12.25, -0.51, 0.064, 2, 8, 36.48, -11.96, 0.09737, 9, 5.53, -11.96, 0.90263, 2, 8, 32.81, -11.96, 0.22319, 9, 1.85, -11.96, 0.77681, 2, 8, 28.6, -11.96, 0.42764, 9, -2.35, -11.96, 0.57236, 2, 8, 23.75, -11.82, 0.6765, 9, -7.21, -11.82, 0.3235, 1, 8, 10.24, -11.96, 1, 1, 8, -7.88, -11.96, 1, 1, 8, -7.88, 5.26, 1], "hull": 19, "edges": [0, 36, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36], "width": 46, "height": 105}}}}, {"name": "burper-back-variant", "attachments": {"Body": {"Body": {"name": "Burper/Body_Back_Poison", "type": "<PERSON><PERSON><PERSON>", "skin": "burper-back", "parent": "Body", "width": 167, "height": 143}}, "Burper/Horns": {"Horns": {"name": "Burper/<PERSON>s_Poison", "x": 24.78, "y": 1.21, "rotation": -90, "width": 119, "height": 67}}, "EyeLeftBack": {"images/Eye": {"name": "Bur<PERSON>/Eye_Back", "x": 0.5, "y": 0.5, "width": 54, "height": 51}, "images/Eye_Closed": {"x": 0.39, "y": -2.53, "scaleX": -0.8691, "rotation": -100.23, "width": 25, "height": 31}}, "EyeRightBack": {"images/Eye": {"name": "Bur<PERSON>/Eye_Back", "x": 0.5, "y": 0.5, "width": 54, "height": 51}}, "images/PawsBack": {"PawsBack": {"name": "B<PERSON><PERSON>/Pa<PERSON>_<PERSON>ison", "type": "<PERSON><PERSON><PERSON>", "skin": "burper-back", "parent": "PawsBack", "width": 165, "height": 56}}, "LegLeft": {"LegLeft": {"name": "Burper/Leg_<PERSON>ison", "type": "<PERSON><PERSON><PERSON>", "skin": "burper-back", "parent": "LegLeft", "width": 46, "height": 105}}, "LegRight": {"LegRight": {"name": "Burper/Leg_<PERSON>ison", "type": "<PERSON><PERSON><PERSON>", "skin": "burper-back", "parent": "LegRight", "width": 46, "height": 105}}}}, {"name": "burper-front", "attachments": {"Body": {"Body": {"name": "Burper/Body", "type": "mesh", "uvs": [0.73531, 0.07534, 0.88455, 0.12876, 0.88696, 0.30867, 1, 0.45485, 1, 0.68818, 0.94232, 0.8934, 0.73049, 1, 0.24424, 1, 0.04926, 0.8428, 0, 0.66751, 0, 0.39906, 0.08055, 0.30024, 0.08055, 0.14562, 0.2298, 0.08097, 0.29479, 0, 0.68235, 0], "triangles": [2, 0, 1, 13, 0, 2, 0, 13, 14, 14, 15, 0, 11, 13, 2, 11, 12, 13, 5, 6, 4, 6, 7, 2, 7, 9, 11, 2, 7, 11, 4, 2, 3, 2, 4, 6, 7, 8, 9, 9, 10, 11], "vertices": [2, 2, 99.37, -41.63, 0.12625, 3, 44.22, 33.34, 0.87375, 2, 2, 93.75, -64.14, 0.26168, 3, 66.73, 27.71, 0.73832, 2, 2, 66.41, -68.17, 0.50748, 3, 70.75, 0.37, 0.49252, 2, 2, 49.53, -85.85, 0.71828, 3, 88.43, -16.51, 0.28172, 2, 2, 11.74, -85.86, 0.90201, 3, 88.43, -54.3, 0.09799, 2, 2, -17.61, -76.23, 0.97507, 3, 78.79, -83.64, 0.02493, 2, 2, -32.86, -40.86, 0.99974, 3, 43.42, -98.89, 0.00026, 2, 2, -32.88, 40.34, 0.99324, 3, -37.79, -98.89, 0.00676, 2, 2, -10.41, 72.91, 0.91794, 3, -70.35, -76.41, 0.08206, 2, 2, 14.65, 81.14, 0.81604, 3, -78.57, -51.34, 0.18396, 2, 2, 53.19, 75.19, 0.4941, 3, -72.6, -12.81, 0.5059, 2, 2, 67.98, 68.91, 0.31349, 3, -66.33, 1.98, 0.68651, 2, 2, 93.71, 68.92, 0.14033, 3, -66.33, 27.71, 0.85967, 2, 2, 98.54, 42.79, 0.05166, 3, -40.2, 32.53, 0.94834, 2, 2, 110.12, 31.94, 0.00632, 3, -29.34, 44.11, 0.99368, 2, 2, 110.14, -32.78, 0.04888, 3, 35.38, 44.11, 0.95112], "hull": 16, "edges": [0, 30, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30], "width": 167, "height": 143}}, "Burper/CheekBall1": {"CheekBall1": {"name": "Burper/CheekBall1", "x": -0.49, "y": 0.48, "rotation": 0.47, "width": 49, "height": 46}}, "Burper/CheekBall2": {"CheekBall1": {"name": "Burper/CheekBall1", "x": -0.49, "y": 0.48, "rotation": 0.47, "width": 49, "height": 46}}, "Burper/Horns": {"Horns": {"name": "Burper/Horns", "x": 24.78, "y": 1.21, "rotation": -90, "width": 119, "height": 67}}, "Burper/Mouth_Shut": {"Burper/Mouth_Open": {"x": 1.3, "y": -21.6, "width": 115, "height": 80}, "Burper/Mouth_Shut": {"x": 0.5, "width": 45, "height": 29}}, "EyeLeft": {"images/Eye": {"name": "Burper/Eye", "x": 7.64, "y": 0.5, "width": 54, "height": 51}, "images/Eye_Closed": {"x": 1.19, "y": -2.53, "scaleX": -0.8691, "rotation": -100.23, "width": 25, "height": 31}}, "EyeRight": {"images/Eye": {"name": "Burper/Eye", "x": 9.07, "y": 0.5, "width": 54, "height": 51}, "images/Eye_Closed": {"x": 5.99, "y": -3.32, "scaleY": -1.0106, "rotation": 69.33, "width": 25, "height": 31}}, "images/Paws": {"Paws": {"name": "Burper/Paws", "type": "mesh", "uvs": [1, 0.37533, 0.97166, 0.76748, 0.94911, 1, 0.78574, 1, 0.2185, 1, 0.05269, 1, 0.02196, 0.62546, 0, 0.24984, 0, 0, 0.2105, 0, 0.78065, 0, 1, 0, 0.20469, 0.61751, 0.20323, 0.23394, 0.79301, 0.29303, 0.79301, 0.68732], "triangles": [7, 8, 13, 14, 12, 13, 6, 7, 13, 12, 6, 13, 5, 6, 12, 4, 12, 14, 5, 12, 4, 14, 13, 10, 9, 10, 13, 13, 8, 9, 14, 10, 11, 14, 11, 0, 15, 14, 0, 1, 15, 0, 15, 4, 14, 3, 4, 15, 2, 3, 15, 1, 2, 15], "vertices": [2, 14, 15.2, 33.85, 0.632, 2, 4.17, -59.46, 0.368, 2, 14, 35.63, 19.08, 0.856, 2, -17.17, -46.04, 0.144, 1, 14, 47.63, 8.95, 1, 2, 14, 45.91, -17.88, 0.99815, 2, -29.8, -9.82, 0.00185, 2, 2, -36.12, 19.22, 0.05193, 15, 52.73, 16.99, 0.94807, 1, 15, 52.92, -8.97, 1, 2, 2, -14.46, 56.73, 0.24, 15, 31.06, -20.51, 0.76, 2, 2, 7.41, 66.4, 0.464, 15, 9.19, -30.18, 0.536, 2, 2, 21.75, 68.99, 0.56, 15, -5.16, -32.77, 0.44, 2, 2, 22.06, 36.41, 0.64, 15, -5.46, -0.19, 0.36, 2, 14, -6.65, 9.63, 0.36, 2, 24.42, -33.89, 0.64, 2, 14, -4.34, 45.75, 0.36, 2, 24.43, -70.08, 0.64, 2, 2, -14.06, 26.16, 0.22442, 15, 30.67, 10.06, 0.77558, 2, 2, 8.31, 32.86, 0.464, 15, 8.3, 3.35, 0.536, 2, 14, 8.92, 3.79, 0.536, 2, 8.5, -29.06, 0.464, 2, 14, 29.52, -8.18, 0.808, 2, -12.82, -18.44, 0.192], "hull": 12, "edges": [0, 22, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22], "width": 165, "height": 56}}, "LegLeftBehind": {"LegLeftBehind": {"name": "Burper/Leg", "type": "mesh", "uvs": [0.84583, 0.18737, 0.90983, 0.34823, 0.90983, 0.3928, 0.90183, 0.45137, 0.87783, 0.51823, 0.82183, 0.77537, 0.79783, 0.82679, 1, 0.92965, 1, 1, 0, 1, 0, 0.89879, 0, 0.84908, 0, 0.51651, 0, 0.47708, 0, 0.43594, 0.004, 0.38937, 0, 0.23537, 0, 0, 0.52183, 0], "triangles": [9, 6, 8, 6, 7, 8, 9, 10, 6, 10, 11, 6, 6, 11, 5, 5, 11, 12, 5, 12, 4, 14, 3, 4, 4, 12, 13, 4, 13, 14, 14, 15, 3, 3, 15, 2, 2, 15, 1, 1, 16, 0, 1, 15, 16, 16, 18, 0, 16, 17, 18], "vertices": [2, 11, 6.71, 15.96, 0.96777, 12, -24.24, 15.96, 0.03223, 2, 11, 21.35, 18.07, 0.52547, 12, -9.61, 18.07, 0.47453, 2, 11, 25.51, 18.07, 0.38285, 12, -5.45, 18.07, 0.61715, 2, 11, 30.88, 17.8, 0.21407, 12, -0.08, 17.8, 0.78593, 2, 11, 36.72, 17.01, 0.07688, 12, 5.77, 17.01, 0.92312, 2, 12, 25.22, 15.16, 0.856, 13, 14.44, 7.51, 0.144, 2, 12, 26.48, 14.37, 0.32444, 13, 13.72, 6.21, 0.67556, 2, 12, 32.86, 21.04, 0.02232, 13, 20.76, 0.24, 0.97768, 1, 13, 21.08, -5.05, 1, 1, 13, -11.56, -11.92, 1, 1, 13, -12.02, -4.33, 1, 2, 12, 31.63, -11.96, 0.888, 13, -12.25, -0.51, 0.112, 2, 11, 36.48, -11.96, 0.09737, 12, 5.53, -11.96, 0.90263, 2, 11, 32.81, -11.96, 0.22319, 12, 1.85, -11.96, 0.77681, 2, 11, 28.6, -11.96, 0.42764, 12, -2.35, -11.96, 0.57236, 2, 11, 23.75, -11.82, 0.6765, 12, -7.21, -11.82, 0.3235, 1, 11, 10.24, -11.96, 1, 1, 11, -7.88, -11.96, 1, 1, 11, -7.88, 5.26, 1], "hull": 19, "edges": [0, 36, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36], "width": 46, "height": 105}}, "LegRightBehind": {"LegRightBehind": {"name": "Burper/Leg", "type": "mesh", "uvs": [0.84583, 0.18737, 0.90983, 0.34823, 0.90983, 0.3928, 0.90183, 0.45137, 0.87783, 0.51823, 0.82183, 0.77537, 0.79783, 0.82679, 1, 0.92965, 1, 1, 0, 1, 0, 0.89879, 0, 0.84908, 0, 0.51651, 0, 0.47708, 0, 0.43594, 0.004, 0.38937, 0, 0.23537, 0, 0, 0.52183, 0], "triangles": [10, 11, 6, 9, 10, 6, 6, 7, 8, 9, 6, 8, 3, 15, 2, 14, 15, 3, 4, 13, 14, 4, 12, 13, 14, 3, 4, 5, 12, 4, 5, 11, 12, 6, 11, 5, 16, 17, 18, 16, 18, 0, 1, 15, 16, 1, 16, 0, 2, 15, 1], "vertices": [2, 8, 6.71, 15.96, 0.96777, 9, -24.24, 15.96, 0.03223, 2, 8, 21.35, 18.07, 0.52547, 9, -9.61, 18.07, 0.47453, 2, 8, 25.51, 18.07, 0.38285, 9, -5.45, 18.07, 0.61715, 2, 8, 30.88, 17.8, 0.21407, 9, -0.08, 17.8, 0.78593, 2, 8, 36.72, 17.01, 0.07688, 9, 5.77, 17.01, 0.92312, 2, 9, 25.22, 15.16, 0.904, 10, 14.44, 7.51, 0.096, 2, 9, 26.48, 14.37, 0.32444, 10, 13.72, 6.21, 0.67556, 2, 9, 32.86, 21.04, 0.02232, 10, 20.76, 0.24, 0.97768, 1, 10, 21.08, -5.05, 1, 1, 10, -11.56, -11.92, 1, 1, 10, -12.02, -4.33, 1, 2, 9, 31.63, -11.96, 0.936, 10, -12.25, -0.51, 0.064, 2, 8, 36.48, -11.96, 0.09737, 9, 5.53, -11.96, 0.90263, 2, 8, 32.81, -11.96, 0.22319, 9, 1.85, -11.96, 0.77681, 2, 8, 28.6, -11.96, 0.42764, 9, -2.35, -11.96, 0.57236, 2, 8, 23.75, -11.82, 0.6765, 9, -7.21, -11.82, 0.3235, 1, 8, 10.24, -11.96, 1, 1, 8, -7.88, -11.96, 1, 1, 8, -7.88, 5.26, 1], "hull": 19, "edges": [0, 36, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36], "width": 46, "height": 105}}}}, {"name": "burper-front-variant", "attachments": {"Body": {"Body": {"name": "Burper/Body_Poison", "type": "<PERSON><PERSON><PERSON>", "skin": "burper-front", "parent": "Body", "width": 167, "height": 143}}, "Burper/CheekBall1": {"CheekBall1": {"name": "Burper/CheekBall1_Poison", "x": -0.49, "y": 0.48, "rotation": 0.47, "width": 49, "height": 46}}, "Burper/CheekBall2": {"CheekBall1": {"name": "Burper/CheekBall1_Poison", "x": -0.49, "y": 0.48, "rotation": 0.47, "width": 49, "height": 46}}, "Burper/Horns": {"Horns": {"name": "Burper/<PERSON>s_Poison", "x": 24.78, "y": 1.21, "rotation": -90, "width": 119, "height": 67}}, "Burper/Mouth_Shut": {"Burper/Mouth_Open": {"x": 1.3, "y": -21.6, "width": 115, "height": 80}, "Burper/Mouth_Shut": {"x": 0.5, "width": 45, "height": 29}}, "EyeLeft": {"images/Eye": {"name": "Burper/Eye", "x": 7.64, "y": 0.5, "width": 54, "height": 51}, "images/Eye_Closed": {"x": 1.19, "y": -2.53, "scaleX": -0.8691, "rotation": -100.23, "width": 25, "height": 31}}, "EyeRight": {"images/Eye": {"name": "Burper/Eye", "x": 9.07, "y": 0.5, "width": 54, "height": 51}, "images/Eye_Closed": {"x": 5.99, "y": -3.32, "scaleY": -1.0106, "rotation": 69.33, "width": 25, "height": 31}}, "images/Paws": {"Paws": {"name": "B<PERSON><PERSON>/Pa<PERSON>_<PERSON>ison", "type": "<PERSON><PERSON><PERSON>", "skin": "burper-front", "parent": "Paws", "width": 165, "height": 56}}, "LegLeftBehind": {"LegLeftBehind": {"name": "Burper/Leg_<PERSON>ison", "type": "<PERSON><PERSON><PERSON>", "skin": "burper-front", "parent": "LegLeftBehind", "width": 46, "height": 105}}, "LegRightBehind": {"LegRightBehind": {"name": "Burper/Leg_<PERSON>ison", "type": "<PERSON><PERSON><PERSON>", "skin": "burper-front", "parent": "LegRightBehind", "width": 46, "height": 105}}}}], "events": {"lay": {}}, "animations": {"hit": {"slots": {"EyeLeft": {"attachment": [{"name": "images/Eye_Closed"}, {"time": 0.1667, "name": "images/Eye"}]}, "EyeLeftBack": {"attachment": [{"name": "images/Eye_Closed"}, {"time": 0.1667, "name": "images/Eye"}]}, "EyeRight": {"attachment": [{"name": "images/Eye_Closed"}, {"time": 0.1667, "name": "images/Eye"}]}, "EyeRightBack": {"attachment": [{}, {"time": 0.1667, "name": "images/Eye"}]}}, "bones": {"Body": {"rotate": [{}, {"time": 0.0333, "value": -25.06}, {"time": 0.1, "value": 10.15}, {"time": 0.2667}], "translate": [{}, {"time": 0.0667, "x": -3.26, "y": 30}, {"time": 0.1333}]}, "TwigRight": {"rotate": [{}, {"time": 0.0667, "value": -91.8}, {"time": 0.2}], "translate": [{}, {"time": 0.0667, "x": -14.76, "y": -7.09}, {"time": 0.2}]}, "Main": {"scale": [{"x": 1.325, "y": 0.663, "curve": [0.017, 1.325, 0.05, 0.739, 0.017, 0.663, 0.05, 1.525]}, {"time": 0.0667, "x": 0.739, "y": 1.525, "curve": [0.092, 0.739, 0.142, 1.193, 0.092, 1.525, 0.142, 0.783]}, {"time": 0.1667, "x": 1.193, "y": 0.783, "curve": [0.2, 1.193, 0.267, 1, 0.2, 0.783, 0.267, 1]}, {"time": 0.3}]}, "ArmRight": {"rotate": [{"value": 46.74, "curve": [0.025, 46.74, 0.075, -35.82]}, {"time": 0.1, "value": -35.82, "curve": [0.125, -35.82, 0.175, 0]}, {"time": 0.2}]}, "ArmLeft": {"rotate": [{"value": -46.48, "curve": [0.025, -46.48, 0.075, 38.61]}, {"time": 0.1, "value": 38.61, "curve": [0.125, 38.61, 0.175, 0]}, {"time": 0.2}]}, "Face": {"translate": [{"x": 16.86, "y": -1.2}, {"time": 0.0667, "x": -7.96, "y": -0.8}, {"time": 0.2}]}, "MouthBtm": {"translate": [{"y": 8.43}, {"time": 0.0667, "y": -12.65}, {"time": 0.1667, "x": 1.01, "y": 10.76}, {"time": 0.3}]}, "TwigLeft": {"rotate": [{}, {"time": 0.0667, "value": 83.61}, {"time": 0.2}], "translate": [{}, {"time": 0.0667, "x": -13.29, "y": 1.01}, {"time": 0.2}]}, "FootLeft": {"translate": [{"x": 7.14}]}, "LegLeft": {"translate": [{"y": -7.94}]}}}, "idle": {"bones": {"Body": {"translate": [{"x": 1.32, "y": 3.22, "curve": [0.083, 1.32, 0.25, 0, 0.083, 3.22, 0.25, -4.3]}, {"time": 0.3333, "y": -4.3, "curve": [0.417, 0, 0.583, 1.32, 0.417, -4.3, 0.583, 3.22]}, {"time": 0.6667, "x": 1.32, "y": 3.22, "curve": [0.75, 1.32, 0.917, 0, 0.75, 3.22, 0.917, -4.3]}, {"time": 1, "y": -4.3, "curve": [1.083, 0, 1.25, 1.32, 1.083, -4.3, 1.25, 3.22]}, {"time": 1.3333, "x": 1.32, "y": 3.22}]}, "Face": {"translate": [{"x": -2.15, "curve": [0.063, 0, 0.125, 2.15, 0.063, 0, 0.125, 0]}, {"time": 0.1667, "x": 2.15, "curve": [0.25, 2.15, 0.417, -6.44, 0.25, 0, 0.417, 0]}, {"time": 0.5, "x": -6.44, "curve": [0.542, -6.44, 0.604, -4.3, 0.542, 0, 0.604, 0]}, {"time": 0.6667, "x": -2.15, "curve": [0.729, 0, 0.792, 2.15, 0.729, 0, 0.792, 0]}, {"time": 0.8333, "x": 2.15, "curve": [0.917, 2.15, 1.083, -6.44, 0.917, 0, 1.083, 0]}, {"time": 1.1667, "x": -6.44, "curve": [1.208, -6.44, 1.271, -4.3, 1.208, 0, 1.271, 0]}, {"time": 1.3333, "x": -2.15}]}, "MouthBtm": {"translate": [{"y": -6.44, "curve": [0.083, 0, 0.25, 0, 0.083, -6.44, 0.25, 4.3]}, {"time": 0.3333, "y": 4.3, "curve": [0.417, 0, 0.583, 0, 0.417, 4.3, 0.583, -6.44]}, {"time": 0.6667, "y": -6.44, "curve": [0.75, 0, 0.917, 0, 0.75, -6.44, 0.917, 4.3]}, {"time": 1, "y": 4.3, "curve": [1.083, 0, 1.25, 0, 1.083, 4.3, 1.25, -6.44]}, {"time": 1.3333, "y": -6.44}]}, "EyeRight": {"scale": [{"time": 1.1333, "curve": [1.148, 1, 1.208, 1, 1.148, 0.476, 1.208, 0.287]}, {"time": 1.2333, "y": 0.287, "curve": [1.258, 1, 1.333, 1, 1.258, 0.287, 1.333, 0.564]}, {"time": 1.3333}]}, "EyeLeft": {"scale": [{"time": 1.1333, "curve": [1.148, 1, 1.208, 1, 1.148, 0.476, 1.208, 0.287]}, {"time": 1.2333, "y": 0.287, "curve": [1.258, 1, 1.333, 1, 1.258, 0.287, 1.333, 0.564]}, {"time": 1.3333}]}, "Main": {"scale": [{"x": 0.968, "y": 1.048, "curve": [0.083, 0.968, 0.25, 1.055, 0.083, 1.048, 0.25, 0.913]}, {"time": 0.3333, "x": 1.055, "y": 0.913, "curve": [0.417, 1.055, 0.583, 0.968, 0.417, 0.913, 0.583, 1.048]}, {"time": 0.6667, "x": 0.968, "y": 1.048, "curve": [0.75, 0.968, 0.917, 1.055, 0.75, 1.048, 0.917, 0.913]}, {"time": 1, "x": 1.055, "y": 0.913, "curve": [1.083, 1.055, 1.25, 0.968, 1.083, 0.913, 1.25, 1.048]}, {"time": 1.3333, "x": 0.968, "y": 1.048}]}, "FootLeft": {"translate": [{"x": 7.14, "curve": [0.083, 7.14, 0.25, 13.89, 0.083, 0, 0.25, 0]}, {"time": 0.3333, "x": 13.89, "curve": [0.417, 13.89, 0.583, 7.14, 0.417, 0, 0.583, 0]}, {"time": 0.6667, "x": 7.14, "curve": [0.75, 7.14, 0.875, 10.51, 0.75, 0, 0.875, 0]}, {"time": 1, "x": 13.89, "curve": [1.083, 13.89, 1.25, 7.14, 1.083, 0, 1.25, 0]}, {"time": 1.3333, "x": 7.14}]}, "FootRight": {"translate": [{"curve": [0.083, 0, 0.25, -5.25, 0.083, 0, 0.25, 0]}, {"time": 0.3333, "x": -5.25, "curve": [0.417, -5.25, 0.583, 0, 0.417, 0, 0.583, 0]}, {"time": 0.6667, "curve": [0.75, 0, 0.917, -5.25, 0.75, 0, 0.917, 0]}, {"time": 1, "x": -5.25, "curve": [1.083, -5.25, 1.25, 0, 1.083, 0, 1.25, 0]}, {"time": 1.3333}]}, "TwigRight": {"rotate": [{"value": 15.64, "curve": [0.051, 28.34, 0.099, 38.93]}, {"time": 0.1333, "value": 38.93, "curve": [0.217, 38.93, 0.383, -24.38]}, {"time": 0.4667, "value": -24.38, "curve": [0.55, -24.38, 0.717, 38.93]}, {"time": 0.8, "value": 38.93, "curve": [0.883, 38.93, 1.05, -24.38]}, {"time": 1.1333, "value": -24.38, "curve": [1.182, -24.38, 1.261, -2.15]}, {"time": 1.3333, "value": 15.64}]}, "TwigLeft": {"rotate": [{"value": -4.61, "curve": [0.051, -12.51, 0.099, -19.09]}, {"time": 0.1333, "value": -19.09, "curve": [0.217, -19.09, 0.383, 20.28]}, {"time": 0.4667, "value": 20.28, "curve": [0.55, 20.28, 0.717, -19.09]}, {"time": 0.8, "value": -19.09, "curve": [0.883, -19.09, 1.05, 20.28]}, {"time": 1.1333, "value": 20.28, "curve": [1.182, 20.28, 1.261, 6.45]}, {"time": 1.3333, "value": -4.61}]}, "LegLeft": {"translate": [{"y": -7.94}]}, "Horn": {"translate": [{"x": -2.35, "curve": [0.063, -1.17, 0.125, 0, 0.063, 0, 0.125, 0]}, {"time": 0.1667, "curve": [0.25, 0, 0.417, -4.69, 0.25, 0, 0.417, 0]}, {"time": 0.5, "x": -4.69, "curve": [0.583, -4.69, 0.75, 0, 0.583, 0, 0.75, 0]}, {"time": 0.8333, "curve": [0.917, 0, 1.083, -4.69, 0.917, 0, 1.083, 0]}, {"time": 1.1667, "x": -4.69, "curve": [1.208, -4.69, 1.271, -3.52, 1.208, 0, 1.271, 0]}, {"time": 1.3333, "x": -2.35}]}, "ArmRight": {"translate": [{"y": -4.44}, {"time": 0.1667, "x": -1.55, "y": -6.45}, {"time": 0.3333, "x": -2.36, "y": 1.02}, {"time": 0.5, "x": -1.61, "y": 2.08}, {"time": 0.6667, "y": -4.44}, {"time": 0.8333, "x": -1.55, "y": -6.45}, {"time": 1, "x": -2.36, "y": 1.02}, {"time": 1.1667, "x": -1.61, "y": 2.08}, {"time": 1.3333, "y": -4.44}]}, "ArmLeft": {"translate": [{"y": -4.44}, {"time": 0.1667, "x": 0.92, "y": -6.45}, {"time": 0.3333, "y": 1.02}, {"time": 0.5, "x": 0.85, "y": 2.08}, {"time": 0.6667, "y": -4.44}, {"time": 0.8333, "x": 0.92, "y": -6.45}, {"time": 1, "y": 1.02}, {"time": 1.1667, "x": 0.85, "y": 2.08}, {"time": 1.3333, "y": -4.44}]}}}, "jump": {"bones": {"Body": {"translate": [{"curve": [0.008, 0, 0.02, 0.31, 0.008, 0, 0.02, -2.35]}, {"time": 0.0333, "x": 0.85, "y": -6.52, "curve": [0.06, 0.69, 0.091, 0.37, 0.06, 10.56, 0.091, 42.56]}, {"time": 0.1, "y": 80.78, "curve": [0.15, 0, 0.272, 0, 0.15, 80.78, 0.272, 73.58]}, {"time": 0.3, "y": 64.04, "curve": [0.333, 0, 0.415, 0, 0.333, 64.04, 0.415, 36.48]}, {"time": 0.4333}]}, "Face": {"translate": [{"x": -6.68, "y": 2.85, "curve": [0.027, -12.85, 0.075, -13.6, 0.027, 2.57, 0.075, 2.53]}, {"time": 0.1, "x": -13.6, "y": 2.53, "curve": [0.142, -13.6, 0.225, 30.2, 0.142, 2.53, 0.225, -0.34]}, {"time": 0.2667, "x": 30.2, "y": -0.34, "curve": [0.308, 30.2, 0.416, 18.54, 0.308, -0.34, 0.416, 1.24]}, {"time": 0.4333, "x": 6.57, "y": 2.85}]}, "ArmLeft": {"rotate": [{"value": -3.15, "curve": [0.016, 43.97, 0.075, 62.85]}, {"time": 0.1, "value": 62.85, "curve": [0.254, 62.24, 0.275, -54.96]}, {"time": 0.3333, "value": -54.96}, {"time": 0.4333, "value": -3.15}], "translate": [{"curve": [0.016, 7.06, 0.075, 9.88, 0.016, -8.33, 0.075, -11.67]}, {"time": 0.1, "x": 9.88, "y": -11.67, "curve": [0.254, 9.8, 0.275, -6.72, 0.254, -11.66, 0.275, -9.99]}, {"time": 0.3333, "x": -6.72, "y": -9.99}, {"time": 0.4333}]}, "ArmRight": {"rotate": [{"value": -0.32, "curve": [0.016, -46.71, 0.075, -65.3]}, {"time": 0.1, "value": -65.3, "curve": [0.254, -64.71, 0.275, 48.39]}, {"time": 0.3333, "value": 48.39}, {"time": 0.4333, "value": -0.32}], "translate": [{"curve": [0.016, -7.88, 0.075, -11.03, 0.016, -5.72, 0.075, -8.01]}, {"time": 0.1, "x": -11.03, "y": -8.01, "curve": [0.254, -10.92, 0.275, 10.76, 0.254, -8.01, 0.275, -9.16]}, {"time": 0.3333, "x": 10.76, "y": -9.16}, {"time": 0.4333}]}, "FootLeft": {"rotate": [{"time": 0.1, "curve": [0.3, 0, 0.25, 123.03]}, {"time": 0.3, "value": 123.03, "curve": [0.317, 123.03, 0.35, 30.25]}, {"time": 0.3667, "value": 30.25}, {"time": 0.4333, "value": -83.23}], "translate": [{"x": 7.14, "curve": [0.016, 7.14, 0.043, 44.53, 0.016, 0, 0.043, -4.24]}, {"time": 0.0667, "x": 69.47, "y": -7.07, "curve": [0.079, 80.91, 0.091, 89.49, 0.079, 2, 0.091, 8.79]}, {"time": 0.1, "x": 89.49, "y": 8.79, "curve": [0.3, 89.49, 0.25, 65.62, 0.3, 8.79, 0.25, 42.26]}, {"time": 0.3, "x": 65.62, "y": 42.26, "curve": [0.317, 65.62, 0.35, 29.98, 0.317, 42.26, 0.35, 32.8]}, {"time": 0.3667, "x": 29.98, "y": 32.8}, {"time": 0.4333, "x": -9.68, "y": 22.93}]}, "FootRight": {"rotate": [{"time": 0.1, "curve": [0.3, 0, 0.25, -81.71]}, {"time": 0.3, "value": -81.71, "curve": [0.316, -81.71, 0.342, -48.86]}, {"time": 0.3667, "value": -22.58, "curve": [0.392, 37.11, 0.416, 86.84]}, {"time": 0.4333, "value": 86.84}], "translate": [{}, {"time": 0.0667, "x": 10.17, "y": -8.55}, {"time": 0.1, "x": 26.38, "y": 8.85, "curve": [0.3, 26.38, 0.25, -28.26, 0.3, 8.85, 0.25, 50.44]}, {"time": 0.3, "x": -28.26, "y": 50.44, "curve": [0.317, -28.26, 0.35, -8.03, 0.317, 50.44, 0.35, 33.67]}, {"time": 0.3667, "x": -8.03, "y": 33.67}, {"time": 0.4333, "x": 15.7, "y": 30.85}]}, "LegLeft": {"rotate": [{"time": 0.4333, "value": -19.8}], "translate": [{"y": -7.94}, {"time": 0.1, "x": -9, "y": -36.75, "curve": [0.15, -9, 0.3, -10.99, 0.15, -36.75, 0.3, -28.96]}, {"time": 0.3, "x": -13.9, "y": -17.6}, {"time": 0.3667, "x": 0.01, "y": -20.43}]}, "LegRight": {"rotate": [{"time": 0.4333, "value": 49.78}], "translate": [{}, {"time": 0.1, "x": -10.8, "y": 3.01, "curve": [0.15, -10.8, 0.3, -10.32, 0.15, 3.01, 0.3, 1.79]}, {"time": 0.3, "x": -9.63}]}, "MouthBtm": {"translate": [{"y": 9.98, "curve": "stepped"}, {"time": 0.1333, "y": 9.98, "curve": [0.183, 0, 0.333, 0, 0.183, 9.98, 0.333, -0.82]}, {"time": 0.3333, "y": -8.98}]}, "Main": {"scale": [{"curve": [0.008, 1, 0.025, 1.321, 0.008, 1, 0.025, 0.677]}, {"time": 0.0333, "x": 1.321, "y": 0.677, "curve": [0.058, 1.321, 0.108, 0.667, 0.058, 0.677, 0.108, 1.188]}, {"time": 0.1333, "x": 0.667, "y": 1.188, "curve": [0.158, 0.667, 0.208, 1, 0.158, 1.188, 0.208, 1]}, {"time": 0.2333}]}, "TwigLeft": {"rotate": [{"value": -4.61, "curve": [0.051, 32.52, 0.099, 63.47]}, {"time": 0.1333, "value": 63.47, "curve": [0.183, 63.47, 0.333, 7.81]}, {"time": 0.3333, "value": -22.39}], "translate": [{}, {"time": 0.1333, "x": -13.74, "y": 8.36}, {"time": 0.3333}, {"time": 0.4333, "x": 10.23, "y": 2.79}]}, "TwigRight": {"rotate": [{"value": 15.64, "curve": [0.051, -28.57, 0.099, -65.41]}, {"time": 0.1333, "value": -65.41, "curve": [0.183, -65.41, 0.333, -4.62]}, {"time": 0.3333, "value": 28.36}], "translate": [{}, {"time": 0.1333, "x": -13.73, "y": -9.56}, {"time": 0.3333}, {"time": 0.4333, "x": 10.23, "y": 2.79}]}, "LegRightBtm": {"rotate": [{"time": 0.4333, "value": 26.12}]}, "LegLeftBtm": {"rotate": [{"time": 0.4333, "value": 41.97}]}}}, "jump-combined": {"bones": {"Body": {"translate": [{"curve": [0.008, 0, 0.02, 0.31, 0.008, 0, 0.02, -2.35]}, {"time": 0.0333, "x": 0.85, "y": -6.52, "curve": [0.06, 0.69, 0.091, 0.37, 0.06, 10.56, 0.091, 42.56]}, {"time": 0.1, "y": 80.78, "curve": [0.15, 0, 0.272, 0, 0.15, 80.78, 0.272, 73.58]}, {"time": 0.3, "y": 64.04, "curve": [0.333, 0, 0.415, 0, 0.333, 64.04, 0.415, 36.48]}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.4667, "curve": [0.472, -0.54, 0.517, -0.93, 0.472, -7.22, 0.517, -12.39]}, {"time": 0.5333, "x": -0.93, "y": -12.39, "curve": [0.593, -0.88, 0.608, 2.22, 0.593, -11.98, 0.608, 13.8]}, {"time": 0.6333, "x": 2.22, "y": 13.8, "curve": [0.675, 2.22, 0.758, 0.36, 0.675, 13.8, 0.758, -7.43]}, {"time": 0.8, "x": 0.36, "y": -7.43, "curve": [0.842, 0.36, 0.925, 1.32, 0.842, -7.43, 0.925, 3.22]}, {"time": 0.9667, "x": 1.32, "y": 3.22}], "scale": [{"time": 0.4667, "curve": [0.472, 0.773, 0.517, 0.611, 0.472, 1.275, 0.517, 1.473]}, {"time": 0.5333, "x": 0.611, "y": 1.473, "curve": [0.593, 0.622, 0.608, 1.28, 0.593, 1.464, 0.608, 0.911]}, {"time": 0.6333, "x": 1.28, "y": 0.911, "curve": [0.675, 1.28, 0.758, 0.823, 0.675, 0.911, 0.758, 1.112]}, {"time": 0.8, "x": 0.823, "y": 1.112}, {"time": 0.9667}]}, "Face": {"translate": [{"x": -6.68, "y": 2.85, "curve": [0.027, -12.85, 0.075, -13.6, 0.027, 2.57, 0.075, 2.53]}, {"time": 0.1, "x": -13.6, "y": 2.53, "curve": [0.142, -13.6, 0.225, 30.2, 0.142, 2.53, 0.225, -0.34]}, {"time": 0.2667, "x": 30.2, "y": -0.34, "curve": [0.308, 30.2, 0.416, 18.54, 0.308, -0.34, 0.416, 1.24]}, {"time": 0.4333, "x": 6.57, "y": 2.85, "curve": [0.442, -5.25, 0.458, -6.68, 0.442, 2.85, 0.458, 2.85]}, {"time": 0.4667, "x": -6.68, "y": 2.85, "curve": [0.472, -8.48, 0.517, -9.77, 0.472, 2.11, 0.517, 1.58]}, {"time": 0.5333, "x": -9.77, "y": 1.58, "curve": [0.593, -9.59, 0.608, 1.84, 0.593, 1.6, 0.608, 2.85]}, {"time": 0.6333, "x": 1.84, "y": 2.85, "curve": [0.675, 1.84, 0.758, -2.15, 0.675, 2.85, 0.758, 0]}, {"time": 0.8, "x": -2.15}]}, "ArmLeft": {"rotate": [{"value": -3.15, "curve": [0.016, 43.97, 0.075, 62.85]}, {"time": 0.1, "value": 62.85, "curve": [0.254, 62.24, 0.275, -54.96]}, {"time": 0.3333, "value": -54.96}, {"time": 0.4333, "value": -3.15, "curve": "stepped"}, {"time": 0.4667, "value": -3.15, "curve": [0.493, 13.32, 0.592, 19.92]}, {"time": 0.6333, "value": 19.92}], "translate": [{"curve": [0.016, 7.06, 0.075, 9.88, 0.016, -8.33, 0.075, -11.67]}, {"time": 0.1, "x": 9.88, "y": -11.67, "curve": [0.254, 9.8, 0.275, -6.72, 0.254, -11.66, 0.275, -9.99]}, {"time": 0.3333, "x": -6.72, "y": -9.99}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.4667, "curve": [0.493, 0.74, 0.592, 1.03, 0.493, -4.61, 0.592, -6.46]}, {"time": 0.6333, "x": 1.03, "y": -6.46}, {"time": 0.7667}]}, "ArmRight": {"rotate": [{"value": -0.32, "curve": [0.016, -46.71, 0.075, -65.3]}, {"time": 0.1, "value": -65.3, "curve": [0.254, -64.71, 0.275, 48.39]}, {"time": 0.3333, "value": 48.39}, {"time": 0.4333, "value": -0.32, "curve": "stepped"}, {"time": 0.4667, "value": -0.32, "curve": [0.493, -9.86, 0.592, -13.68]}, {"time": 0.6333, "value": -13.68}], "translate": [{"curve": [0.016, -7.88, 0.075, -11.03, 0.016, -5.72, 0.075, -8.01]}, {"time": 0.1, "x": -11.03, "y": -8.01, "curve": [0.254, -10.92, 0.275, 10.76, 0.254, -8.01, 0.275, -9.16]}, {"time": 0.3333, "x": 10.76, "y": -9.16}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.4667, "curve": [0.493, 0.74, 0.592, 1.03, 0.493, -4.61, 0.592, -6.46]}, {"time": 0.6333, "x": 1.03, "y": -6.46}, {"time": 0.7667}]}, "FootLeft": {"rotate": [{"time": 0.1, "curve": [0.142, 0, 0.225, 123.03]}, {"time": 0.2667, "value": 123.03, "curve": [0.292, 123.03, 0.342, 30.25]}, {"time": 0.3667, "value": 30.25}, {"time": 0.4333, "value": -83.23}, {"time": 0.4667}], "translate": [{"x": 7.14, "curve": [0.016, 7.14, 0.043, 44.53, 0.016, 0, 0.043, -4.24]}, {"time": 0.0667, "x": 69.47, "y": -7.07, "curve": [0.079, 80.91, 0.091, 89.49, 0.079, 2, 0.091, 8.79]}, {"time": 0.1, "x": 89.49, "y": 8.79, "curve": [0.142, 89.49, 0.225, 65.62, 0.142, 8.79, 0.225, 42.26]}, {"time": 0.2667, "x": 65.62, "y": 42.26, "curve": [0.292, 65.62, 0.342, 29.98, 0.292, 42.26, 0.342, 32.8]}, {"time": 0.3667, "x": 29.98, "y": 32.8}, {"time": 0.4333, "x": -9.68, "y": 22.93, "curve": [0.442, -9.68, 0.458, 7.14, 0.442, 22.93, 0.458, 0]}, {"time": 0.4667, "x": 7.14}]}, "FootRight": {"rotate": [{"time": 0.1, "curve": [0.142, 0, 0.225, -81.71]}, {"time": 0.2667, "value": -81.71, "curve": [0.291, -81.71, 0.33, -48.86]}, {"time": 0.3667, "value": -22.58, "curve": [0.392, 37.11, 0.416, 86.84]}, {"time": 0.4333, "value": 86.84}, {"time": 0.4667}], "translate": [{}, {"time": 0.0667, "x": 10.17, "y": -8.55}, {"time": 0.1, "x": 26.38, "y": 8.85, "curve": [0.142, 26.38, 0.225, -28.26, 0.142, 8.85, 0.225, 50.44]}, {"time": 0.2667, "x": -28.26, "y": 50.44, "curve": [0.292, -28.26, 0.342, -8.03, 0.292, 50.44, 0.342, 33.67]}, {"time": 0.3667, "x": -8.03, "y": 33.67}, {"time": 0.4333, "x": 15.7, "y": 30.85}, {"time": 0.4667}]}, "LegLeft": {"rotate": [{"time": 0.4333, "value": -19.8}, {"time": 0.4667, "value": -16.48}], "translate": [{"y": -7.94}, {"time": 0.1, "x": -9, "y": -36.75, "curve": [0.15, -9, 0.3, -10.99, 0.15, -36.75, 0.3, -28.96]}, {"time": 0.3, "x": -13.9, "y": -17.6}, {"time": 0.3667, "x": 0.01, "y": -20.43, "curve": "stepped"}, {"time": 0.4333, "x": 0.01, "y": -20.43}, {"time": 0.4667, "y": -7.94}]}, "LegRight": {"rotate": [{"time": 0.4333, "value": 49.78}, {"time": 0.4667, "value": 7.95}], "translate": [{}, {"time": 0.1, "x": -10.8, "y": 3.01, "curve": [0.15, -10.8, 0.3, -10.32, 0.15, 3.01, 0.3, 1.79]}, {"time": 0.3, "x": -9.63, "curve": "stepped"}, {"time": 0.4333, "x": -9.63}, {"time": 0.4667}]}, "MouthBtm": {"translate": [{"y": 9.98, "curve": "stepped"}, {"time": 0.1333, "y": 9.98, "curve": [0.183, 0, 0.333, 0, 0.183, 9.98, 0.333, -0.82]}, {"time": 0.3333, "y": -8.98, "curve": "stepped"}, {"time": 0.4333, "y": -8.98, "curve": [0.442, 0, 0.465, 0, 0.442, -8.98, 0.465, 0.92]}, {"time": 0.4667, "y": 9.98, "curve": [0.542, 0, 0.763, 0, 0.542, 9.98, 0.763, 4.68]}, {"time": 0.7667, "y": -6.44}]}, "Main": {"scale": [{"curve": [0.008, 1, 0.025, 1.321, 0.008, 1, 0.025, 0.677]}, {"time": 0.0333, "x": 1.321, "y": 0.677, "curve": [0.058, 1.321, 0.108, 0.667, 0.058, 0.677, 0.108, 1.188]}, {"time": 0.1333, "x": 0.667, "y": 1.188, "curve": [0.158, 0.667, 0.208, 1, 0.158, 1.188, 0.208, 1]}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.4667, "curve": [0.592, 1, 0.842, 0.968, 0.592, 1, 0.842, 1.048]}, {"time": 0.9667, "x": 0.968, "y": 1.048}]}, "TwigLeft": {"rotate": [{"value": -4.61, "curve": [0.051, 32.52, 0.099, 63.47]}, {"time": 0.1333, "value": 63.47, "curve": [0.183, 63.47, 0.333, 7.81]}, {"time": 0.3333, "value": -22.39, "curve": "stepped"}, {"time": 0.4333, "value": -22.39}, {"time": 0.4667, "curve": [0.492, 0, 0.542, 63.47]}, {"time": 0.5667, "value": 63.47, "curve": [0.6, 63.47, 0.667, -22.39]}, {"time": 0.7, "value": -22.39, "curve": [0.742, -22.39, 0.825, 38.32]}, {"time": 0.8667, "value": 38.32, "curve": [0.892, 38.32, 0.942, -4.61]}, {"time": 0.9667, "value": -4.61}], "translate": [{}, {"time": 0.1333, "x": -13.74, "y": 8.36}, {"time": 0.3333}, {"time": 0.4333, "x": 10.23, "y": 2.79}, {"time": 0.4667, "curve": [0.492, 0, 0.542, -24.31, 0.492, 0, 0.542, 7.69]}, {"time": 0.5667, "x": -24.31, "y": 7.69, "curve": [0.6, -24.31, 0.667, 4.1, 0.6, 7.69, 0.667, 0]}, {"time": 0.7, "x": 4.1, "curve": [0.742, 4.1, 0.825, 0, 0.742, 0, 0.825, 0]}, {"time": 0.8667}]}, "TwigRight": {"rotate": [{"value": 15.64, "curve": [0.051, -28.57, 0.099, -65.41]}, {"time": 0.1333, "value": -65.41, "curve": [0.183, -65.41, 0.333, -4.62]}, {"time": 0.3333, "value": 28.36, "curve": "stepped"}, {"time": 0.4333, "value": 28.36}, {"time": 0.4667, "curve": [0.492, 0, 0.542, -65.41]}, {"time": 0.5667, "value": -65.41, "curve": [0.6, -65.41, 0.667, 28.36]}, {"time": 0.7, "value": 28.36, "curve": [0.742, 28.36, 0.825, -29.64]}, {"time": 0.8667, "value": -29.64, "curve": [0.892, -29.64, 0.942, 15.64]}, {"time": 0.9667, "value": 15.64}], "translate": [{}, {"time": 0.1333, "x": -13.73, "y": -9.56}, {"time": 0.3333}, {"time": 0.4333, "x": 10.23, "y": 2.79}, {"time": 0.4667, "curve": [0.492, 0, 0.542, -24.3, 0.492, 0, 0.542, -10.23]}, {"time": 0.5667, "x": -24.3, "y": -10.23, "curve": [0.6, -24.3, 0.667, 4.1, 0.6, -10.23, 0.667, 0]}, {"time": 0.7, "x": 4.1, "curve": [0.742, 4.1, 0.825, 0, 0.742, 0, 0.825, 0]}, {"time": 0.8667}]}, "LegRightBtm": {"rotate": [{"time": 0.4333, "value": 26.12}, {"time": 0.4667, "value": -11.21}]}, "LegLeftBtm": {"rotate": [{"time": 0.4333, "value": 41.97}, {"time": 0.4667, "value": -22.53}]}, "CheekRight": {"scale": [{"time": 0.4667, "curve": [0.467, 1.095, 0.467, 1.256, 0.467, 1.095, 0.467, 1.256]}, {"time": 0.6, "x": 1.256, "y": 1.256, "curve": [0.751, 1.256, 0.75, 0.904, 0.751, 1.256, 0.75, 0.904]}, {"time": 0.8, "x": 0.904, "y": 0.904, "curve": [0.842, 0.904, 0.925, 1, 0.842, 0.904, 0.925, 1]}, {"time": 0.9667}]}, "CheekLeft": {"scale": [{"time": 0.4667, "curve": [0.467, 1.095, 0.467, 1.256, 0.467, 1.095, 0.467, 1.256]}, {"time": 0.6, "x": 1.256, "y": 1.256, "curve": [0.751, 1.256, 0.75, 0.904, 0.751, 1.256, 0.75, 0.904]}, {"time": 0.8, "x": 0.904, "y": 0.904, "curve": [0.842, 0.904, 0.925, 1, 0.842, 0.904, 0.925, 1]}, {"time": 0.9667}]}}}, "jump-end": {"bones": {"Body": {"translate": [{"curve": [0.006, -0.54, 0.05, -0.93, 0.006, -7.22, 0.05, -12.39]}, {"time": 0.0667, "x": -0.93, "y": -12.39, "curve": [0.126, -0.88, 0.142, 2.22, 0.126, -11.98, 0.142, 13.8]}, {"time": 0.1667, "x": 2.22, "y": 13.8, "curve": [0.208, 2.22, 0.292, 0.36, 0.208, 13.8, 0.292, -7.43]}, {"time": 0.3333, "x": 0.36, "y": -7.43, "curve": [0.375, 0.36, 0.458, 1.32, 0.375, -7.43, 0.458, 3.22]}, {"time": 0.5, "x": 1.32, "y": 3.22}], "scale": [{"curve": [0.006, 0.773, 0.05, 0.611, 0.006, 1.275, 0.05, 1.473]}, {"time": 0.0667, "x": 0.611, "y": 1.473, "curve": [0.126, 0.622, 0.142, 1.28, 0.126, 1.464, 0.142, 0.911]}, {"time": 0.1667, "x": 1.28, "y": 0.911, "curve": [0.208, 1.28, 0.292, 0.823, 0.208, 0.911, 0.292, 1.112]}, {"time": 0.3333, "x": 0.823, "y": 1.112}, {"time": 0.5}]}, "LegLeftBtm": {"rotate": [{"value": -22.53}]}, "FootLeft": {"translate": [{"x": 7.14}]}, "LegLeft": {"rotate": [{"value": -16.48}], "translate": [{"y": -7.94}]}, "LegRightBtm": {"rotate": [{"value": -11.21}]}, "LegRight": {"rotate": [{"value": 7.95}]}, "MouthBtm": {"translate": [{"y": 9.98, "curve": [0.075, 0, 0.297, 0, 0.075, 9.98, 0.297, 4.68]}, {"time": 0.3, "y": -6.44}]}, "Main": {"scale": [{"curve": [0.125, 1, 0.375, 0.968, 0.125, 1, 0.375, 1.048]}, {"time": 0.5, "x": 0.968, "y": 1.048}]}, "TwigRight": {"rotate": [{"curve": [0.025, 0, 0.075, -65.41]}, {"time": 0.1, "value": -65.41, "curve": [0.133, -65.41, 0.2, 28.36]}, {"time": 0.2333, "value": 28.36, "curve": [0.275, 28.36, 0.358, -29.64]}, {"time": 0.4, "value": -29.64, "curve": [0.425, -29.64, 0.475, 15.64]}, {"time": 0.5, "value": 15.64}], "translate": [{"curve": [0.025, 0, 0.075, -24.3, 0.025, 0, 0.075, -10.23]}, {"time": 0.1, "x": -24.3, "y": -10.23, "curve": [0.133, -24.3, 0.2, 4.1, 0.133, -10.23, 0.2, 0]}, {"time": 0.2333, "x": 4.1, "curve": [0.275, 4.1, 0.358, 0, 0.275, 0, 0.358, 0]}, {"time": 0.4}]}, "TwigLeft": {"rotate": [{"curve": [0.025, 0, 0.075, 63.47]}, {"time": 0.1, "value": 63.47, "curve": [0.133, 63.47, 0.2, -22.39]}, {"time": 0.2333, "value": -22.39, "curve": [0.275, -22.39, 0.358, 38.32]}, {"time": 0.4, "value": 38.32, "curve": [0.425, 38.32, 0.475, -4.61]}, {"time": 0.5, "value": -4.61}], "translate": [{"curve": [0.025, 0, 0.075, -24.31, 0.025, 0, 0.075, 7.69]}, {"time": 0.1, "x": -24.31, "y": 7.69, "curve": [0.133, -24.31, 0.2, 4.1, 0.133, 7.69, 0.2, 0]}, {"time": 0.2333, "x": 4.1, "curve": [0.275, 4.1, 0.358, 0, 0.275, 0, 0.358, 0]}, {"time": 0.4}]}, "Face": {"translate": [{"x": -6.68, "y": 2.85, "curve": [0.006, -8.48, 0.05, -9.77, 0.006, 2.11, 0.05, 1.58]}, {"time": 0.0667, "x": -9.77, "y": 1.58, "curve": [0.126, -9.59, 0.142, 1.84, 0.126, 1.6, 0.142, 2.85]}, {"time": 0.1667, "x": 1.84, "y": 2.85, "curve": [0.208, 1.84, 0.292, -2.15, 0.208, 2.85, 0.292, 0]}, {"time": 0.3333, "x": -2.15}]}, "ArmRight": {"rotate": [{"value": -0.32, "curve": [0.026, -9.86, 0.125, -13.68]}, {"time": 0.1667, "value": -13.68, "curve": [0.2, -13.68, 0.267, 0]}, {"time": 0.3}], "translate": [{"curve": [0.026, 0.74, 0.125, 1.03, 0.026, -4.61, 0.125, -6.46]}, {"time": 0.1667, "x": 1.03, "y": -6.46}, {"time": 0.3, "y": -4.44}]}, "ArmLeft": {"rotate": [{"value": -3.15, "curve": [0.026, 13.32, 0.125, 19.92]}, {"time": 0.1667, "value": 19.92, "curve": [0.2, 19.92, 0.267, 0]}, {"time": 0.3}], "translate": [{"curve": [0.026, 0.74, 0.125, 1.03, 0.026, -4.61, 0.125, -6.46]}, {"time": 0.1667, "x": 1.03, "y": -6.46}, {"time": 0.3, "y": -4.44}]}, "CheekRight": {"scale": [{"curve": [0, 1.095, 0, 1.256, 0, 1.095, 0, 1.256]}, {"time": 0.1333, "x": 1.256, "y": 1.256, "curve": [0.284, 1.256, 0.283, 0.904, 0.284, 1.256, 0.283, 0.904]}, {"time": 0.3333, "x": 0.904, "y": 0.904, "curve": [0.375, 0.904, 0.458, 1, 0.375, 0.904, 0.458, 1]}, {"time": 0.5}]}, "CheekLeft": {"scale": [{"curve": [0, 1.095, 0, 1.256, 0, 1.095, 0, 1.256]}, {"time": 0.1333, "x": 1.256, "y": 1.256, "curve": [0.284, 1.256, 0.283, 0.904, 0.284, 1.256, 0.283, 0.904]}, {"time": 0.3333, "x": 0.904, "y": 0.904, "curve": [0.375, 0.904, 0.458, 1, 0.375, 0.904, 0.458, 1]}, {"time": 0.5}]}}}, "lay-egg": {"slots": {"Burper/CheekBall1": {"attachment": [{"time": 0.6333}, {"time": 1.0667, "name": "CheekBall1"}]}, "Burper/CheekBall2": {"attachment": [{"time": 0.6333}, {"time": 1.0667, "name": "CheekBall1"}]}, "Burper/Mouth_Shut": {"attachment": [{"time": 0.5667, "name": "Burper/Mouth_Open"}, {"time": 1.1667, "name": "Burper/Mouth_Shut"}]}, "EyeLeft": {"attachment": [{"time": 0.0667, "name": "images/Eye_Closed"}, {"time": 0.6, "name": "images/Eye"}]}, "EyeLeftBack": {"attachment": [{"time": 0.0667, "name": "images/Eye_Closed"}, {"time": 0.6, "name": "images/Eye"}]}, "EyeRight": {"attachment": [{"time": 0.0667, "name": "images/Eye_Closed"}, {"time": 0.6, "name": "images/Eye"}]}, "EyeRightBack": {"attachment": [{"time": 0.0667}, {"time": 0.6, "name": "images/Eye"}]}}, "bones": {"CheekRight": {"scale": [{"x": 1.095, "y": 1.095, "curve": [0.133, 1.095, 0.225, 1.524, 0.133, 1.095, 0.225, 1.524]}, {"time": 0.5333, "x": 1.525, "y": 1.525, "curve": [0.558, 1.525, 0.608, 0.548, 0.558, 1.525, 0.608, 0.548]}, {"time": 0.6333, "x": 0.548, "y": 0.548, "curve": "stepped"}, {"time": 1.0667, "x": 0.548, "y": 0.548, "curve": [1.1, 0.548, 1.123, 1.094, 1.1, 0.548, 1.123, 1.094]}, {"time": 1.2, "x": 1.095, "y": 1.095}]}, "CheekLeft": {"scale": [{"x": 1.095, "y": 1.095, "curve": [0.133, 1.095, 0.225, 1.524, 0.133, 1.095, 0.225, 1.524]}, {"time": 0.5333, "x": 1.525, "y": 1.525, "curve": [0.558, 1.525, 0.608, 0.548, 0.558, 1.525, 0.608, 0.548]}, {"time": 0.6333, "x": 0.548, "y": 0.548, "curve": "stepped"}, {"time": 1.0667, "x": 0.548, "y": 0.548, "curve": [1.1, 0.548, 1.123, 1.094, 1.1, 0.548, 1.123, 1.094]}, {"time": 1.2, "x": 1.095, "y": 1.095}]}, "BurperMouth": {"scale": [{"time": 0.5667, "y": 0.604, "curve": [0.583, 1, 0.617, 1, 0.583, 0.604, 0.617, 1.16]}, {"time": 0.6333, "y": 1.16, "curve": [0.651, 1, 0.675, 1, 0.651, 1.16, 0.675, 1.007]}, {"time": 0.7, "y": 0.824, "curve": [0.724, 1, 0.75, 1, 0.724, 0.973, 0.75, 1.16]}, {"time": 0.7667, "y": 1.16, "curve": [0.818, 1, 0.89, 1, 0.818, 1.16, 0.89, 0.945]}, {"time": 0.9667, "y": 0.688, "curve": [1.008, 1, 1.092, 1, 1.008, 0.688, 1.092, 0.319]}, {"time": 1.1333, "y": 0.319, "curve": "stepped"}, {"time": 1.1667}]}, "Body": {"translate": [{"x": 1.32, "y": 3.22, "curve": [0.133, 1.32, 0.225, 0, 0.133, 3.22, 0.225, -4.28]}, {"time": 0.5333, "y": -4.3, "curve": [0.558, 0, 0.608, 2.22, 0.558, -4.3, 0.608, 13.8]}, {"time": 0.6333, "x": 2.22, "y": 13.8, "curve": [0.65, 2.22, 0.683, 3.05, 0.65, 13.8, 0.683, 9.22]}, {"time": 0.7, "x": 3.05, "y": 9.22, "curve": [0.717, 3.05, 0.75, 2.22, 0.717, 9.22, 0.75, 13.8]}, {"time": 0.7667, "x": 2.22, "y": 13.8, "curve": [0.792, 2.22, 0.842, 0.36, 0.792, 13.8, 0.842, -0.56]}, {"time": 0.8667, "x": 0.36, "y": -0.56, "curve": [0.908, 0.36, 0.992, 1.32, 0.908, -0.56, 0.992, 3.22]}, {"time": 1.0333, "x": 1.32, "y": 3.22}], "scale": [{"time": 0.5333}, {"time": 0.6333, "x": 1.28, "y": 0.911, "curve": [0.65, 1.28, 0.683, 1.137, 0.65, 0.911, 0.683, 0.911]}, {"time": 0.7, "x": 1.137, "y": 0.911, "curve": [0.717, 1.137, 0.75, 1.28, 0.717, 0.911, 0.75, 0.911]}, {"time": 0.7667, "x": 1.28, "y": 0.911, "curve": [0.792, 1.28, 0.842, 0.823, 0.792, 0.911, 0.842, 1.112]}, {"time": 0.8667, "x": 0.823, "y": 1.112, "curve": [0.908, 0.823, 0.992, 1.12, 0.908, 1.112, 0.992, 0.875]}, {"time": 1.0333, "x": 1.12, "y": 0.875, "curve": [1.075, 1.12, 1.158, 1, 1.075, 0.875, 1.158, 1]}, {"time": 1.2}]}, "Face": {"translate": [{"x": -2.15, "curve": [0.042, -2.15, 0.07, 2.14, 0.042, 0, 0.07, 0]}, {"time": 0.1667, "x": 2.15, "curve": [0.258, 2.15, 0.262, -5.33, 0.258, 0, 0.262, 0]}, {"time": 0.5333, "x": -5.33, "curve": [0.563, -4.66, 0.609, -2.15, 0.563, 0, 0.609, 0]}, {"time": 0.6333, "x": -2.15}]}, "Main": {"scale": [{"x": 0.968, "y": 1.048, "curve": [0.133, 0.968, 0.225, 1.148, 0.133, 1.048, 0.225, 0.695]}, {"time": 0.5333, "x": 1.148, "y": 0.694, "curve": [0.558, 1.148, 0.608, 0.968, 0.558, 0.694, 0.608, 1.048]}, {"time": 0.6333, "x": 0.968, "y": 1.048}]}, "FootLeft": {"translate": [{"x": 7.14, "curve": [0.133, 7.14, 0.225, 13.87, 0.133, 0, 0.225, 0]}, {"time": 0.5333, "x": 13.89, "curve": [0.558, 13.89, 0.608, 7.14, 0.558, 0, 0.608, 0]}, {"time": 0.6333, "x": 7.14}]}, "FootRight": {"translate": [{"curve": [0.133, 0, 0.225, -5.24, 0.133, 0, 0.225, 0]}, {"time": 0.5333, "x": -5.25, "curve": [0.558, -5.25, 0.608, 0, 0.558, 0, 0.608, 0]}, {"time": 0.6333}]}, "TwigRight": {"rotate": [{"value": 15.64, "curve": [0.033, 15.64, 0.056, 38.87]}, {"time": 0.1333, "value": 38.93, "curve": [0.258, 38.93, 0.508, 15.64]}, {"time": 0.6333, "value": 15.64}]}, "TwigLeft": {"rotate": [{"value": -4.61, "curve": [0.033, -4.61, 0.056, -19.05]}, {"time": 0.1333, "value": -19.09, "curve": [0.258, -19.09, 0.508, -4.61]}, {"time": 0.6333, "value": -4.61}]}, "LegLeft": {"translate": [{"y": -7.94}]}, "Horn": {"translate": [{"x": -2.35, "curve": [0.042, -2.35, 0.07, -0.01, 0.042, 0, 0.07, 0]}, {"time": 0.1667, "curve": [0.258, 0, 0.262, -20.75, 0.258, 0, 0.262, 0.69]}, {"time": 0.5333, "x": -20.75, "y": 0.69}, {"time": 0.6333, "x": -2.35}]}, "ArmRight": {"rotate": [{"curve": [0.133, 0, 0.4, -40.53]}, {"time": 0.5333, "value": -40.53}, {"time": 0.6667, "value": 25.75}, {"time": 1.2}], "translate": [{"y": -4.44, "curve": [0.042, 0, 0.07, -1.54, 0.042, -4.44, 0.07, -6.45]}, {"time": 0.1667, "x": -1.55, "y": -6.45}, {"time": 0.3333, "x": -2.36, "y": 1.02}, {"time": 0.6333, "y": -4.44}]}, "ArmLeft": {"rotate": [{"curve": [0.133, 0, 0.4, 31.82]}, {"time": 0.5333, "value": 31.82}, {"time": 0.6667, "value": -11.2}, {"time": 1.2}], "translate": [{"y": -4.44, "curve": [0.042, 0, 0.07, 0.92, 0.042, -4.44, 0.07, -6.45]}, {"time": 0.1667, "x": 0.92, "y": -6.45}, {"time": 0.3333, "y": 1.02}, {"time": 0.6333, "y": -4.44}]}}, "events": [{"time": 0.7667, "name": "lay"}]}}}