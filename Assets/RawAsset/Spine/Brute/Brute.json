{"skeleton": {"hash": "iyHXv2IwdAA", "spine": "4.1.24", "x": -334.2, "y": -9.61, "width": 584.12, "height": 581.1, "images": "./images/", "audio": "C:/Users/<USER>/Desktop/咩咩启示录（Cult of the Lamb）/Brute"}, "bones": [{"name": "root"}, {"name": "Holder", "parent": "root", "x": -0.09, "y": 0.7}, {"name": "HIP", "parent": "Holder", "x": -4.2, "y": 85.68}, {"name": "BACK_BTM", "parent": "HIP", "length": 24.51, "rotation": 92.29, "y": 6.22}, {"name": "BACK_TOP", "parent": "BACK_BTM", "length": 128.57, "rotation": 10.47, "x": 30.34, "y": -1.21}, {"name": "HEAD", "parent": "BACK_TOP", "length": 98.57, "rotation": -12.77, "x": 62.62, "y": 70.23}, {"name": "FACE", "parent": "HEAD", "rotation": -90, "x": 48.34, "y": 38.02}, {"name": "SHOULDER_RIGHT", "parent": "BACK_TOP", "length": 113.52, "rotation": -171.51, "x": 121.66, "y": -128.01}, {"name": "ARM_RIGHT", "parent": "SHOULDER_RIGHT", "length": 70.24, "rotation": -29.27, "x": 115.34, "y": -0.71}, {"name": "HAND_RIGHT", "parent": "ARM_RIGHT", "length": 57.83, "rotation": -16.01, "x": 76.19, "y": -0.15}, {"name": "SHOULDER_LEFT", "parent": "BACK_TOP", "length": 93.39, "rotation": -170.08, "x": 140, "y": 82.06}, {"name": "ARM_LEFT", "parent": "SHOULDER_LEFT", "length": 95.31, "rotation": -49.42, "x": 95.27, "y": -1}, {"name": "WEAPON_CONTROLLER", "parent": "ARM_LEFT", "length": 183.2, "rotation": -105.98, "x": 95.4, "y": 0.08}, {"name": "LEG_RIGHT", "parent": "HIP", "length": 41.58, "rotation": -74.98, "x": 39.18, "y": 2.31, "scaleX": 1.0287, "scaleY": 1.0287}, {"name": "LEG_RIGHT_BTM", "parent": "LEG_RIGHT", "length": 39.44, "rotation": -0.64, "x": 42.53, "y": -0.25}, {"name": "FOOT_RIGHT", "parent": "LEG_RIGHT_BTM", "length": 32.33, "rotation": -104.38, "x": 41.34, "y": -0.49}, {"name": "LEG_LEFT", "parent": "HIP", "length": 32.34, "rotation": -88.26, "x": -40.16, "y": -0.63, "scaleX": 1.0287, "scaleY": 1.0287}, {"name": "LEG_LEFT_BTM", "parent": "LEG_LEFT", "length": 38.25, "rotation": 1.2, "x": 35.31, "y": 0.89}, {"name": "FOOT_LEFT", "parent": "LEG_LEFT_BTM", "length": 32.39, "rotation": -89.47, "x": 39.23, "y": -0.05}, {"name": "FOOT_RIGHT_CONSTRAINT", "parent": "Holder", "x": 24.61, "y": 4.17, "scaleX": 1.0287, "scaleY": 1.0287, "color": "ff3f00ff"}, {"name": "ANKLE_RIGHT_CONSTRAINT", "parent": "FOOT_RIGHT_CONSTRAINT", "x": 30.16, "y": 0.86, "scaleX": 1.0287, "scaleY": 1.0287, "color": "ff3f00ff"}, {"name": "FOOT_LEFT_CONSTRAINT", "parent": "Holder", "x": -71.89, "y": 4.95, "scaleX": 1.0287, "scaleY": 1.0287, "color": "ff3f00ff"}, {"name": "ANKLE_LEFT_CONSTRAINT", "parent": "FOOT_LEFT_CONSTRAINT", "x": 31.88, "y": 3.3, "scaleX": 1.0287, "scaleY": 1.0287, "color": "ff3f00ff"}, {"name": "HAND_RIGHT_CONSTRAINT", "parent": "BACK_TOP", "rotation": -102.77, "x": -58.53, "y": -115.91, "color": "ff3f00ff"}, {"name": "WEAPON_CONSTRAINT", "parent": "BACK_BTM", "rotation": -91.06, "x": 222.82, "y": 112.34, "color": "ff3f00ff"}, {"name": "HAND_LEFT_HANDLE", "parent": "BACK_BTM", "rotation": -91.06, "x": 195.73, "y": 189.96, "color": "ff3f00ff"}, {"name": "MASK", "parent": "HEAD", "x": 89.46, "y": 41.36}, {"name": "HORN_RIGHT", "parent": "HEAD", "length": 76.53, "rotation": -39.64, "x": 129.69, "y": -59.39}, {"name": "HORN_LEFT", "parent": "HEAD", "length": 71.79, "rotation": 39.29, "x": 122.79, "y": 91.79}, {"name": "EYE_LEFT", "parent": "MASK", "x": -18.97, "y": 26.36}, {"name": "EYE_RIGHT", "parent": "MASK", "x": -17.66, "y": -57.2}, {"name": "WEAPON", "parent": "WEAPON_CONTROLLER", "x": 40.85, "y": -0.86}, {"name": "WEAPON_FIXED_HOLDER", "parent": "root", "x": -254.54, "y": 42.78}, {"name": "WEAPON_FIXED", "parent": "WEAPON_FIXED_HOLDER", "rotation": 90, "x": -1.35}, {"name": "BACK_TOP2", "parent": "BACK_TOP", "x": 74.04, "y": -135.07}, {"name": "BloodExplode", "parent": "root", "x": 5.86, "y": 151.99, "scaleX": 1.356, "scaleY": 1.356}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "parent": "BACK_BTM", "rotation": -86.33, "x": 146.37, "y": -22.03}, {"name": "BloodExplode2", "parent": "root", "x": -32.13, "y": 259.97, "scaleX": 1.1643, "scaleY": 1.1643}, {"name": "Glow", "parent": "WEAPON", "x": 124.23, "y": 1.85}, {"name": "Sparks", "parent": "root", "x": 904, "y": 212.46}, {"name": "Sparks2", "parent": "root", "x": 912, "y": 298.44, "scaleX": 1.474, "scaleY": 1.474}, {"name": "Smear", "parent": "root", "x": 624.51, "y": 213.84}, {"name": "Impact", "parent": "root", "x": 516.77, "scaleX": 1.4204, "scaleY": 1.4204}, {"name": "Dust", "parent": "root", "x": 1025.25, "y": 89.15}, {"name": "Dust0", "parent": "Dust", "shearY": -16.75}, {"name": "Dust1", "parent": "Dust", "rotation": -46.81, "x": 86.56, "y": 63.31, "scaleX": 0.677, "scaleY": 1.4264, "shearY": 19.37}, {"name": "Dust2", "parent": "Dust", "x": -28.42, "y": 3.88, "shearY": 11.83}, {"name": "Dust3", "parent": "Dust", "rotation": 50.2, "x": -64.6, "y": 46.51, "shearY": 11.83}, {"name": "Dust4", "parent": "Dust", "rotation": 20.39, "x": -120.16, "y": 77.52}, {"name": "Dust5", "parent": "Dust", "rotation": -3.52, "x": -25.84, "y": 55.56}, {"name": "Earth", "parent": "root", "x": 516.77}, {"name": "Earth_0", "parent": "Earth"}, {"name": "Earth_1", "parent": "Earth"}, {"name": "Earth_2", "parent": "Earth"}, {"name": "Earth_3", "parent": "Earth"}, {"name": "Earth_4", "parent": "Earth"}, {"name": "SpawningPentagram", "parent": "root", "x": 984.14, "y": 699.32}, {"name": "Ring0", "parent": "SpawningPentagram"}, {"name": "Ring1", "parent": "SpawningPentagram"}, {"name": "Pegs", "parent": "SpawningPentagram"}, {"name": "Peg0", "parent": "Pegs", "rotation": 60}, {"name": "Peg1", "parent": "Pegs", "rotation": 120}, {"name": "Peg2", "parent": "Pegs", "rotation": 180}, {"name": "Peg3", "parent": "Pegs"}, {"name": "Peg4", "parent": "Pegs", "rotation": -60}, {"name": "Peg5", "parent": "Pegs", "rotation": -120}, {"name": "Tri", "parent": "SpawningPentagram"}, {"name": "Skiulls", "parent": "Pegs"}, {"name": "Skull0", "parent": "Skiulls", "x": -144.23, "y": 259.37}, {"name": "Skull1", "parent": "Skiulls", "x": -294.52, "y": 8.48}, {"name": "Skull2", "parent": "Skiulls", "x": -163.62, "y": -246.04}, {"name": "Skull3", "parent": "Skiulls", "x": 152.71, "y": -250.88}, {"name": "Skull4", "parent": "Skiulls", "x": 293.3, "y": 1.21}, {"name": "Skull5", "parent": "Skiulls", "x": 151.5, "y": 253.31}], "slots": [{"name": "ARM_LEFT", "bone": "SHOULDER_LEFT", "attachment": "ARM_LEFT"}, {"name": "ThrowRock", "bone": "WEAPON"}, {"name": "Brute/GoatCult/Axe_Glow", "bone": "Glow"}, {"name": "WEAPON", "bone": "WEAPON", "attachment": "Axe"}, {"name": "HAND_WEAPON", "bone": "WEAPON_CONTROLLER", "attachment": "HAND_WEAPON"}, {"name": "LEG_LEFT", "bone": "LEG_LEFT", "attachment": "LEG_LEFT"}, {"name": "LEG_RIGHT", "bone": "LEG_RIGHT", "attachment": "LEG_RIGHT"}, {"name": "BACK_BTM", "bone": "BACK_BTM", "attachment": "BODY"}, {"name": "PANTS", "bone": "BACK_BTM", "attachment": "PANTS"}, {"name": "ROBES", "bone": "BACK_BTM", "attachment": "ROBES"}, {"name": "HOOD_BACK", "bone": "HEAD"}, {"name": "SHOULDER_RIGHT", "bone": "SHOULDER_RIGHT", "attachment": "ARM_RIGHT"}, {"name": "Brute/GoatCult/Cape_Executioner", "bone": "BACK_TOP", "attachment": "Cape_Executioner"}, {"name": "HAND_RIGHT", "bone": "HAND_RIGHT", "attachment": "HAND_RIGHT"}, {"name": "HEAD", "bone": "HEAD", "attachment": "HEAD_5"}, {"name": "HORN_RIGHT", "bone": "HORN_RIGHT"}, {"name": "HORN_LEFT", "bone": "HORN_LEFT"}, {"name": "MASK", "bone": "MASK"}, {"name": "Eyes", "bone": "MASK", "attachment": "Eyes"}, {"name": "HOOD_FRONT", "bone": "HEAD"}, {"name": "HEAD_EXTRA", "bone": "HEAD"}, {"name": "EYE_LEFT_SHOCKED", "bone": "EYE_LEFT"}, {"name": "EYE_RIGHT_SHOCKED", "bone": "EYE_RIGHT"}, {"name": "Cape", "bone": "BACK_TOP"}, {"name": "Brute/MonsterHeart", "bone": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "Bru<PERSON>/<PERSON>H<PERSON><PERSON>_glow", "bone": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "BloodExplode", "bone": "BloodExplode", "color": "6700ffff"}, {"name": "BloodExplode2", "bone": "BloodExplode2", "color": "6700ffff"}, {"name": "Brute/GoatCult/SummonSparks", "bone": "Sparks"}, {"name": "Brute/GoatCult/SummonSparks2", "bone": "Sparks2", "color": "ffffffd7"}, {"name": "Smear2", "bone": "Smear"}, {"name": "Smear", "bone": "Smear"}, {"name": "Impact", "bone": "Impact"}, {"name": "Dust0", "bone": "Dust0", "color": "282828ff"}, {"name": "Dust2", "bone": "Dust2", "color": "282828ff"}, {"name": "Dust3", "bone": "Dust3", "color": "323030ff"}, {"name": "Dust4", "bone": "Dust4", "color": "282828ff"}, {"name": "Dust5", "bone": "Dust5", "color": "323030ff"}, {"name": "Dust1", "bone": "Dust1", "color": "323030ff"}, {"name": "Earth_0", "bone": "Earth_0"}, {"name": "Earth_1", "bone": "Earth_1"}, {"name": "Earth_2", "bone": "Earth_2"}, {"name": "Earth_3", "bone": "Earth_3"}, {"name": "Earth_4", "bone": "Earth_4"}, {"name": "Ring0", "bone": "Ring0", "color": "4d2c82ff"}, {"name": "Ring1", "bone": "Ring1", "color": "4d2c82ff"}, {"name": "Brute/pentagram/Dash", "bone": "Peg0", "color": "4d2c82ff"}, {"name": "Brute/pentagram/Dash2", "bone": "Peg1", "color": "4d2c82ff"}, {"name": "Brute/pentagram/Dash3", "bone": "Peg2", "color": "4d2c82ff"}, {"name": "Brute/pentagram/Dash4", "bone": "Peg3", "color": "4d2c82ff"}, {"name": "Brute/pentagram/Dash5", "bone": "Peg4", "color": "4d2c82ff"}, {"name": "Brute/pentagram/Dash6", "bone": "Peg5", "color": "4d2c82ff"}, {"name": "Tri", "bone": "Tri", "color": "4d2c82ff"}, {"name": "Skull0", "bone": "Skull0", "color": "4d2c82ff"}, {"name": "Skull1", "bone": "Skull1", "color": "4d2c82ff"}, {"name": "Skull2", "bone": "Skull2", "color": "4d2c82ff"}, {"name": "Skull3", "bone": "Skull3", "color": "4d2c82ff"}, {"name": "Skull4", "bone": "Skull4", "color": "4d2c82ff"}, {"name": "Skull5", "bone": "Skull5", "color": "4d2c82ff"}], "ik": [{"name": "ANKLE_LEFT_CONSTRAINT", "order": 1, "bones": ["LEG_LEFT", "LEG_LEFT_BTM"], "target": "ANKLE_LEFT_CONSTRAINT"}, {"name": "ANKLE_RIGHT_CONSTRAINT", "bones": ["LEG_RIGHT", "LEG_RIGHT_BTM"], "target": "ANKLE_RIGHT_CONSTRAINT"}, {"name": "FOOT_LEFT_CONSTRAINT", "order": 3, "bones": ["FOOT_LEFT"], "target": "FOOT_LEFT_CONSTRAINT"}, {"name": "FOOT_RIGHT_CONSTRAINT", "order": 2, "bones": ["FOOT_RIGHT"], "target": "FOOT_RIGHT_CONSTRAINT"}, {"name": "HAND_LEFT_CONSTRAINT", "order": 5, "bones": ["SHOULDER_LEFT", "ARM_LEFT"], "target": "HAND_LEFT_HANDLE", "bendPositive": false}, {"name": "HAND_RIGHT", "order": 4, "bones": ["SHOULDER_RIGHT", "ARM_RIGHT"], "target": "HAND_RIGHT_CONSTRAINT", "bendPositive": false}, {"name": "WEAPON_CONSTRAINT", "order": 6, "bones": ["WEAPON_CONTROLLER"], "target": "WEAPON_CONSTRAINT"}], "transform": [{"name": "FACE_CONSTRAINT", "order": 7, "bones": ["MASK"], "target": "FACE", "mixRotate": 0, "mixX": 0.388, "mixScaleX": 0, "mixShearY": 0}, {"name": "HORN_LEFT_CONSTRAINT", "order": 9, "bones": ["HORN_LEFT"], "target": "FACE", "mixRotate": 0, "mixX": 0.15, "mixScaleX": 0, "mixShearY": 0}, {"name": "HORN_RIGHT_CONSTRAINT", "order": 8, "bones": ["HORN_RIGHT"], "target": "FACE", "mixRotate": 0, "mixX": 0.15, "mixScaleX": 0, "mixShearY": 0}], "skins": [{"name": "default", "attachments": {"BloodExplode": {"Brute/blood_explosion0001": {"x": 0.5, "y": 0.5, "width": 323, "height": 313}, "Brute/blood_explosion0002": {"x": 0.5, "y": 0.5, "width": 323, "height": 313}, "Brute/blood_explosion0003": {"x": 0.5, "y": 0.5, "width": 323, "height": 313}, "Brute/blood_explosion0004": {"x": 0.5, "y": 0.5, "width": 323, "height": 313}, "Brute/blood_explosion0005": {"x": 0.5, "y": 0.5, "width": 323, "height": 313}, "Brute/blood_explosion0006": {"x": 0.5, "y": 0.5, "width": 323, "height": 313}, "Brute/blood_explosion0007": {"x": 0.5, "y": 0.5, "width": 323, "height": 313}, "Brute/blood_explosion0008": {"x": 0.5, "y": 0.5, "width": 323, "height": 313}, "Brute/blood_explosion0009": {"x": 0.5, "y": 0.5, "width": 323, "height": 313}, "Brute/blood_explosion0010": {"x": 0.5, "y": 0.5, "width": 323, "height": 313}, "Brute/blood_explosion0011": {"x": 0.5, "y": 0.5, "width": 323, "height": 313}, "Brute/blood_explosion0012": {"x": 0.5, "y": 0.5, "width": 323, "height": 313}, "Brute/blood_explosion0013": {"x": 0.5, "y": 0.5, "width": 323, "height": 313}, "Brute/blood_explosion0014": {"x": 0.5, "y": 0.5, "width": 323, "height": 313}, "Brute/blood_explosion0015": {"x": 0.5, "y": 0.5, "width": 323, "height": 313}, "Brute/blood_explosion0016": {"x": 0.5, "y": 0.5, "width": 323, "height": 313}, "Brute/blood_explosion0017": {"x": 0.5, "y": 0.5, "width": 323, "height": 313}}, "BloodExplode2": {"Brute/blood_explosion0001": {"x": 0.5, "y": 0.5, "width": 323, "height": 313}, "Brute/blood_explosion0002": {"x": 0.5, "y": 0.5, "width": 323, "height": 313}, "Brute/blood_explosion0003": {"x": 0.5, "y": 0.5, "width": 323, "height": 313}, "Brute/blood_explosion0004": {"x": 0.5, "y": 0.5, "width": 323, "height": 313}, "Brute/blood_explosion0005": {"x": 0.5, "y": 0.5, "width": 323, "height": 313}, "Brute/blood_explosion0006": {"x": 0.5, "y": 0.5, "width": 323, "height": 313}, "Brute/blood_explosion0007": {"x": 0.5, "y": 0.5, "width": 323, "height": 313}, "Brute/blood_explosion0008": {"x": 0.5, "y": 0.5, "width": 323, "height": 313}, "Brute/blood_explosion0009": {"x": 0.5, "y": 0.5, "width": 323, "height": 313}, "Brute/blood_explosion0010": {"x": 0.5, "y": 0.5, "width": 323, "height": 313}, "Brute/blood_explosion0011": {"x": 0.5, "y": 0.5, "width": 323, "height": 313}, "Brute/blood_explosion0012": {"x": 0.5, "y": 0.5, "width": 323, "height": 313}, "Brute/blood_explosion0013": {"x": 0.5, "y": 0.5, "width": 323, "height": 313}, "Brute/blood_explosion0014": {"x": 0.5, "y": 0.5, "width": 323, "height": 313}, "Brute/blood_explosion0015": {"x": 0.5, "y": 0.5, "width": 323, "height": 313}, "Brute/blood_explosion0016": {"x": 0.5, "y": 0.5, "width": 323, "height": 313}, "Brute/blood_explosion0017": {"x": 0.5, "y": 0.5, "width": 323, "height": 313}}, "Brute/GoatCult/Axe_Glow": {"Brute/GoatCult/Axe_Glow": {"x": 13.96, "y": 24.59, "scaleX": 1.1144, "scaleY": 1.1144, "rotation": -92.97, "width": 285, "height": 537}}, "Brute/GoatCult/SummonSparks": {"Brute/GoatCult/SummonSparks": {"x": -18, "width": 282, "height": 238}}, "Brute/GoatCult/SummonSparks2": {"Brute/GoatCult/SummonSparks": {"x": -18, "width": 282, "height": 238}}, "Brute/MonsterHeart": {"Brute/MonsterHeart": {"width": 180, "height": 155}, "Brute/MonsterHeart_glow": {"width": 219, "height": 194}}, "Brute/pentagram/Dash": {"Brute/pentagram/Dash": {"x": 0.5, "y": 311.98, "rotation": -0.07, "width": 33, "height": 167}}, "Brute/pentagram/Dash2": {"Brute/pentagram/Dash": {"x": 0.5, "y": 311.98, "rotation": -0.07, "width": 33, "height": 167}}, "Brute/pentagram/Dash3": {"Brute/pentagram/Dash": {"x": 0.5, "y": 311.98, "rotation": -0.07, "width": 33, "height": 167}}, "Brute/pentagram/Dash4": {"Brute/pentagram/Dash": {"x": 0.5, "y": 311.98, "rotation": -0.07, "width": 33, "height": 167}}, "Brute/pentagram/Dash5": {"Brute/pentagram/Dash": {"x": 0.5, "y": 311.98, "rotation": -0.07, "width": 33, "height": 167}}, "Brute/pentagram/Dash6": {"Brute/pentagram/Dash": {"x": 0.5, "y": 311.98, "rotation": -0.07, "width": 33, "height": 167}}, "Dust0": {"Brute/Dust/Dust_0000": {"type": "mesh", "uvs": [0.5542, 0, 0.7771, 0.33652, 1, 0.67304, 1, 0.78554, 0.92128, 0.94433, 0.58456, 0.97216, 0.24783, 1, 0.20436, 1, 0.04008, 0.78099, 0.14019, 0.39836, 0.2403, 0.01573, 0.38924, 0, 0.43738, 0.30368, 0.6308, 0.68911, 0.37406, 0.58878, 0.34699, 0.83838, 0.64484, 0.51911], "triangles": [12, 11, 0, 10, 11, 12, 12, 0, 1, 9, 10, 12, 16, 12, 1, 14, 9, 12, 14, 12, 16, 16, 1, 2, 13, 14, 16, 13, 16, 2, 8, 9, 14, 13, 2, 3, 15, 8, 14, 15, 14, 13, 4, 13, 3, 5, 15, 13, 4, 5, 13, 7, 8, 15, 6, 7, 15, 5, 6, 15], "vertices": [-10.28, 255.18, 22.38, 167.53, 55.04, 79.88, 53.53, 51.23, 38.27, 11.49, -18.26, 7.36, -74.79, 3.23, -82.04, 3.61, -106.5, 60.82, -84.67, 157.38, -62.84, 253.93, -37.79, 256.63, -33.84, 178.88, -6.75, 79.03, -48.22, 106.83, -56.08, 43.51, -2.13, 122.2], "hull": 12, "edges": [0, 22, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22], "width": 167, "height": 255}}, "Dust1": {"Brute/Dust/Dust_0002": {"type": "mesh", "uvs": [0.49278, 0.62293, 0.4378, 0.96762, 0.16667, 0.79378, 0.07634, 0.42552, 0.22178, 0.18769, 0.39556, 0.18817, 0.44417, 0.40555, 0.30487, 0.60032, 0.34941, 0.74756], "triangles": [4, 6, 3, 6, 4, 5, 7, 3, 6, 7, 6, 0, 8, 7, 0, 2, 3, 7, 2, 7, 8, 1, 8, 0, 2, 8, 1], "vertices": [-10.99, 21.57, -96.74, 42.93, -46.53, 81.58, 48.56, 83.41, 105.23, 50.9, 101.05, 22.18, 45.03, 21.87, -0.9, 51.84, -39.12, 49.72], "hull": 7, "edges": [0, 12, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12], "width": 167, "height": 255}}, "Dust2": {"Brute/Dust/Dust_0003": {"type": "mesh", "uvs": [0.53828, 0.14463, 0.92065, 0.41171, 0.94444, 0.91052, 0.8002, 1, 0.71878, 1, 0.51431, 0.92681, 0.36882, 0.83756, 0.16192, 1, 0.04329, 1, 0, 0.67779, 0, 0.57561, 0.26589, 0.17153, 0.39241, 0.51086, 0.64455, 0.76505], "triangles": [12, 11, 0, 12, 0, 1, 10, 11, 12, 9, 10, 12, 13, 12, 1, 6, 9, 12, 6, 12, 13, 13, 1, 2, 5, 6, 13, 4, 5, 13, 7, 8, 9, 6, 7, 9, 2, 4, 13, 3, 4, 2], "vertices": [-23.63, 216, 42.76, 150.37, 51.56, 23.41, 28.36, -0.3, 14.77, -0.82, -20.06, 16.53, -45.2, 38.36, -78.16, -4.35, -97.96, -5.1, -108.3, 76.73, -109.29, 102.76, -68.83, 207.42, -44.43, 121.75, 0.11, 58.58], "hull": 12, "edges": [0, 22, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22], "width": 167, "height": 255}}, "Dust3": {"Brute/Dust/Dust_0004": {"type": "mesh", "uvs": [0.61859, 0.32591, 0.53875, 0.68573, 0.46902, 1, 0.39731, 1, 0.27702, 0.98596, 0.1337, 0.96922, 0.03682, 0.79097, 0.11439, 0.39489, 0.39151, 0.2383, 0.3351, 0.71047, 0.27427, 0.83869], "triangles": [0, 7, 8, 1, 7, 0, 9, 7, 1, 6, 7, 9, 10, 6, 9, 5, 6, 10, 3, 4, 10, 5, 10, 4, 1, 10, 9, 10, 2, 3, 1, 2, 10], "vertices": [-8.48, 170.31, -18.32, 78.12, -26.91, -2.4, -38.88, -2.86, -59.09, -0.04, -83.17, 3.31, -101.06, 48.12, -91.95, 149.54, -47.22, 191.2, -52.06, 70.52, -60.97, 37.47], "hull": 9, "edges": [0, 16, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16], "width": 167, "height": 255}}, "Dust4": {"Brute/Dust/Dust_0005": {"type": "mesh", "uvs": [0.43291, 0.53984, 0.41941, 0.71348, 0.40273, 0.92814, 0.30229, 0.98087, 0.26775, 0.74708, 0.23729, 0.54093], "triangles": [1, 5, 0, 4, 5, 1, 2, 4, 1, 3, 4, 2], "vertices": [18.51, 99.66, 16.26, 55.38, 13.47, 0.64, -3.3, -12.8, -9.07, 46.81, -14.16, 99.38], "hull": 6, "edges": [0, 10, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10], "width": 167, "height": 255}}, "Dust5": {"Brute/Dust/Dust_0006": {"type": "mesh", "uvs": [0.41848, 0.31846, 0.42761, 0.58655, 0.4342, 0.78007, 0.38743, 1, 0.37782, 1, 0.28912, 0.90796, 0.28144, 0.78908, 0.27138, 0.63338, 0.25103, 0.31818, 0.38911, 0.28493], "triangles": [8, 1, 7, 0, 8, 9, 1, 8, 0, 7, 1, 2, 6, 7, 2, 5, 6, 2, 3, 4, 5, 2, 3, 5], "vertices": [16.1, 156.11, 17.62, 87.75, 18.72, 38.4, 10.92, -17.68, 9.31, -17.68, -5.5, 5.79, -6.79, 36.11, -8.47, 75.81, -11.86, 156.18, 11.2, 164.66], "hull": 10, "edges": [0, 18, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18], "width": 167, "height": 255}}, "Earth_0": {"Brute/EarthBits/EarthBit_0000": {"x": 0.5, "y": 0.5, "width": 69, "height": 65}}, "Earth_1": {"Brute/EarthBits/EarthBit_0001": {"x": 0.5, "y": 0.5, "width": 35, "height": 39}}, "Earth_2": {"Brute/EarthBits/EarthBit_0002": {"x": 0.5, "y": 0.5, "width": 66, "height": 63}}, "Earth_3": {"Brute/EarthBits/EarthBit_0003": {"x": 0.5, "y": 0.5, "width": 40, "height": 46}}, "Earth_4": {"Brute/EarthBits/EarthBit_0004": {"x": 0.5, "y": 0.5, "width": 31, "height": 43}}, "Impact": {"Brute/Impact/Impact_0000": {"x": 0.5, "y": 0.5, "width": 331, "height": 229}, "Brute/Impact/Impact_0001": {"x": 0.5, "y": 0.5, "width": 331, "height": 229}, "Brute/Impact/Impact_0002": {"x": 0.5, "y": 0.5, "width": 331, "height": 229}}, "Ring0": {"Brute/pentagram/Ring": {"x": 0.5, "rotation": -0.07, "width": 611, "height": 600}}, "Ring1": {"Brute/pentagram/Ring Small": {"x": 0.5, "y": 0.5, "rotation": -0.07, "width": 429, "height": 425}}, "Skull0": {"Brute/pentagram/Skull": {"rotation": -0.07, "width": 144, "height": 148}}, "Skull1": {"Brute/pentagram/Skull": {"width": 144, "height": 148}}, "Skull2": {"Brute/pentagram/Skull": {"width": 144, "height": 148}}, "Skull3": {"Brute/pentagram/Skull": {"width": 144, "height": 148}}, "Skull4": {"Brute/pentagram/Skull": {"width": 144, "height": 148}}, "Skull5": {"Brute/pentagram/Skull": {"width": 144, "height": 148}}, "Smear": {"Brute/SmearLarge/SmearLarge": {"type": "mesh", "color": "000000ff", "uvs": [0.75583, 0, 0.79533, 0, 0.93821, 0.01198, 0.97708, 0.01375, 0.79084, 0.08565, 0.76106, 0.08522, 0.71361, 0.14765, 0.56519, 0.21879, 0.4815, 0.29032, 0.45984, 0.31497, 0.38848, 0.40654, 0.37435, 0.43591, 0.32925, 0.54801, 0.32058, 0.6416, 0.34437, 0.66261, 0.37081, 0.78772, 0.39028, 0.79958, 0.46138, 0.90704, 0.47463, 0.98975, 0.21053, 0.92887, 0.28648, 0.99385, 0.29111, 1, 0.28352, 1, 0.26115, 0.98187, 0.1796, 0.9491, 0.10851, 0.84955, 0.05614, 0.8501, 0, 0.5579, 0, 0.49987, 0.0852, 0.31771, 0.22934, 0.20312, 0.3775, 0.09929, 0.6386, 0.01555], "triangles": [5, 0, 1, 32, 0, 5, 4, 5, 1, 4, 1, 2, 4, 2, 3, 6, 32, 5, 7, 31, 32, 7, 32, 6, 8, 31, 7, 30, 31, 8, 9, 30, 8, 10, 30, 9, 29, 30, 10, 11, 29, 10, 28, 29, 11, 12, 28, 11, 27, 28, 12, 13, 27, 12, 25, 27, 13, 25, 13, 14, 25, 14, 15, 26, 27, 25, 19, 25, 15, 24, 25, 19, 20, 23, 19, 16, 19, 15, 17, 19, 16, 24, 19, 23, 18, 19, 17, 22, 23, 20, 22, 20, 21], "vertices": [102.59, 259.5, 118.43, 259.5, 175.72, 253.28, 191.31, 252.37, 116.63, 215.05, 104.68, 215.27, 85.66, 182.87, 26.14, 145.95, -7.42, 108.83, -16.1, 96.03, -44.72, 48.5, -50.39, 33.26, -68.47, -24.92, -71.95, -73.49, -62.41, -84.4, -51.81, -149.32, -44, -155.48, -15.49, -211.25, -10.17, -254.18, -116.08, -222.58, -85.62, -256.31, -83.76, -259.5, -86.81, -259.5, -95.78, -250.09, -128.48, -233.08, -156.99, -181.42, -177.99, -181.7, -200.5, -30.05, -200.5, 0.07, -166.33, 94.61, -108.53, 154.08, -49.12, 207.97, 55.58, 251.43], "hull": 33, "edges": [0, 64, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64], "width": 401, "height": 519}}, "Smear2": {"Brute/SmearLarge/SmearLarge2": {"type": "mesh", "path": "<PERSON><PERSON><PERSON>/SmearLarge/SmearLarge", "uvs": [0.75583, 0, 0.79533, 0, 0.93821, 0.01198, 0.97708, 0.01375, 0.79084, 0.08565, 0.76106, 0.08522, 0.71361, 0.14765, 0.56519, 0.21879, 0.4815, 0.29032, 0.45984, 0.31497, 0.38848, 0.40654, 0.37435, 0.43591, 0.32925, 0.54801, 0.32058, 0.6416, 0.34437, 0.66261, 0.37081, 0.78772, 0.39028, 0.79958, 0.46138, 0.90704, 0.47463, 0.98975, 0.21053, 0.92887, 0.28648, 0.99385, 0.29111, 1, 0.28352, 1, 0.26115, 0.98187, 0.1796, 0.9491, 0.10851, 0.84955, 0.05614, 0.8501, 0, 0.5579, 0, 0.49987, 0.0852, 0.31771, 0.22934, 0.20312, 0.3775, 0.09929, 0.6386, 0.01555], "triangles": [5, 0, 1, 32, 0, 5, 4, 5, 1, 4, 1, 2, 4, 2, 3, 6, 32, 5, 7, 31, 32, 7, 32, 6, 8, 31, 7, 30, 31, 8, 9, 30, 8, 10, 30, 9, 29, 30, 10, 11, 29, 10, 28, 29, 11, 12, 28, 11, 27, 28, 12, 13, 27, 12, 25, 27, 13, 25, 13, 14, 25, 14, 15, 26, 27, 25, 19, 25, 15, 24, 25, 19, 20, 23, 19, 16, 19, 15, 17, 19, 16, 24, 19, 23, 18, 19, 17, 22, 23, 20, 22, 20, 21], "vertices": [102.59, 259.5, 118.43, 259.5, 175.72, 253.28, 191.31, 252.37, 116.63, 215.05, 104.68, 215.27, 85.66, 182.87, 26.14, 145.95, -7.42, 108.83, -16.1, 96.03, -44.72, 48.5, -50.39, 33.26, -68.47, -24.92, -71.95, -73.49, -62.41, -84.4, -51.81, -149.32, -44, -155.48, -15.49, -211.25, -10.17, -254.18, -116.08, -222.58, -85.62, -256.31, -83.76, -259.5, -86.81, -259.5, -95.78, -250.09, -128.48, -233.08, -156.99, -181.42, -177.99, -181.7, -200.5, -30.05, -200.5, 0.07, -166.33, 94.61, -108.53, 154.08, -49.12, 207.97, 55.58, 251.43], "hull": 33, "edges": [0, 64, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64], "width": 401, "height": 519}}, "ThrowRock": {"Brute/Rock": {"x": 181.55, "y": -10.36, "rotation": 90.46, "width": 344, "height": 288}}, "Tri": {"Brute/pentagram/Triangle": {"y": 28.38, "width": 276, "height": 243}}}}, {"name": "Executioner", "attachments": {"ARM_LEFT": {"ARM_LEFT": {"name": "Brute/Executioner/ArmLeft", "type": "mesh", "uvs": [0, 0.11354, 0, 0.23951, 0.02936, 0.39616, 0.10239, 0.49791, 0.2021, 0.54033, 0.20481, 0.54578, 0.13767, 0.59488, 0.1678, 0.75635, 0.19035, 0.8772, 0.30133, 0.97671, 0.4744, 0.99942, 0.73024, 1, 0.85816, 0.93344, 0.91083, 0.81879, 0.95974, 0.70197, 0.94477, 0.62773, 0.99971, 0.60464, 1, 0.57062, 0.96379, 0.52081, 1, 0.44305, 0.99337, 0.34828, 0.91308, 0.18668, 0.7546, 0.04331, 0.58978, 0, 0.34045, 0, 0.13126, 0.03238], "triangles": [0, 25, 24, 22, 0, 24, 22, 24, 23, 1, 0, 22, 1, 22, 21, 2, 1, 21, 2, 21, 20, 3, 2, 20, 19, 3, 20, 19, 4, 3, 18, 4, 19, 17, 5, 4, 16, 5, 17, 15, 5, 16, 6, 5, 15, 6, 15, 14, 7, 6, 14, 13, 7, 14, 8, 7, 13, 12, 8, 13, 10, 9, 8, 12, 10, 8, 11, 10, 12, 17, 4, 18], "vertices": [2, 11, -80.99, -79.49, 0.00886, 10, -12.47, -36.66, 0.99114, 2, 11, -50.82, -77.67, 0.07959, 10, 15.42, -48.32, 0.92041, 2, 11, -13.53, -71.35, 0.016, 10, 51.67, -59.08, 0.984, 2, 11, 10.23, -59.82, 0.29654, 10, 78.09, -59.2, 0.70346, 2, 11, 19.57, -45.47, 0.63905, 10, 92.79, -50.43, 0.36095, 2, 11, 20.85, -45.02, 0.74709, 10, 94.14, -50.59, 0.25291, 2, 11, 33.17, -53.55, 0.952, 10, 101.44, -63.69, 0.048, 1, 11, 71.6, -47.06, 1, 1, 11, 100.36, -42.21, 1, 1, 11, 123.28, -25.48, 1, 1, 11, 127.28, -1.31, 1, 1, 11, 125.28, 33.94, 1, 1, 11, 108.27, 50.6, 1, 1, 11, 80.37, 56.19, 1, 2, 11, 51.97, 61.24, 0.95618, 10, 168.91, 31.07, 0.04382, 2, 11, 34.31, 58.1, 0.77012, 10, 151.67, 36.04, 0.22988, 2, 11, 28.33, 65.33, 0.66916, 10, 149.48, 45.17, 0.33084, 2, 11, 20.17, 64.88, 0.60169, 10, 141.97, 48.35, 0.39831, 2, 11, 8.54, 59.17, 0.32481, 10, 129.01, 48.35, 0.67519, 2, 11, -10.39, 63.03, 0.06673, 10, 113.72, 60.16, 0.93327, 2, 11, -33.04, 60.74, 0.03392, 10, 92.38, 68.09, 0.96608, 2, 11, -71.08, 47.34, 0.00987, 10, 52.32, 72.83, 0.99013, 1, 10, 12.14, 65.92, 1, 1, 10, -6.22, 48.94, 1, 1, 10, -19.49, 17.2, 1, 2, 11, -101.53, -62.59, 3e-05, 10, -23.46, -12.43, 0.99997], "hull": 26, "edges": [0, 50, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50], "width": 138, "height": 240}}, "BACK_BTM": {"BODY": {"name": "Brute/GoatCult/Body", "type": "mesh", "uvs": [0.22083, 0.84863, 0.22393, 0.8694, 0.23212, 0.90628, 0.24032, 0.94268, 0.2859, 1, 0.79025, 1, 0.82635, 0.93957, 0.82234, 0.90727, 0.81833, 0.86084, 0.81113, 0.82229, 0.92179, 0.7302, 1, 0.65593, 1, 0.46581, 0.95425, 0.2638, 0.96015, 0.1628, 0.87839, 0.05363, 0.74209, 0, 0.58248, 0, 0.35831, 0, 0.25429, 0.03196, 0.15207, 0.05363, 0.01398, 0.18903, 0.00501, 0.37678, 0, 0.49774, 0, 0.5862, 0.02654, 0.70896, 0.08751, 0.82631, 0.33555, 0.84077, 0.29753, 0.72743, 0.33837, 0.86775, 0.34141, 0.90646, 0.26975, 0.50221, 0.74059, 0.78631, 0.74351, 0.64352, 0.55488, 0.84224, 0.72304, 0.81575, 0.73438, 0.48557, 0.69984, 0.30589, 0.49256, 0.18804, 0.27185, 0.18611], "triangles": [27, 0, 28, 31, 38, 37, 0, 26, 28, 26, 25, 28, 25, 24, 31, 23, 22, 31, 20, 19, 39, 39, 19, 18, 38, 18, 17, 14, 37, 15, 13, 37, 14, 36, 37, 13, 38, 17, 37, 9, 32, 10, 33, 12, 11, 33, 36, 12, 35, 32, 9, 34, 32, 35, 33, 32, 34, 28, 33, 34, 10, 33, 11, 32, 33, 10, 35, 9, 8, 33, 31, 36, 31, 37, 36, 36, 13, 12, 37, 16, 15, 37, 17, 16, 39, 18, 38, 21, 20, 39, 39, 22, 21, 24, 23, 31, 25, 31, 28, 33, 28, 31, 31, 22, 39, 31, 39, 38, 34, 27, 28, 4, 30, 5, 5, 30, 7, 4, 3, 30, 3, 2, 30, 30, 8, 7, 2, 29, 30, 30, 29, 8, 2, 1, 29, 1, 27, 29, 1, 0, 27, 29, 34, 8, 29, 27, 34, 5, 7, 6, 34, 35, 8], "vertices": [2, 3, 29.04, 92.04, 0.5384, 4, 15.67, 91.94, 0.4616, 2, 3, 22.77, 91.35, 0.664, 4, 9.39, 92.4, 0.336, 1, 3, 11.62, 89.32, 1, 1, 3, 0.61, 87.29, 1, 1, 3, -17.12, 74.22, 1, 1, 3, -23.21, -77.97, 1, 1, 3, -5.53, -89.59, 1, 1, 3, 4.2, -88.77, 1, 1, 3, 18.17, -88.11, 1, 1, 4, -16.01, -83.67, 1, 1, 4, 3.55, -122.37, 1, 2, 4, 20.06, -150.33, 0.888, 7, 104.03, 0.35, 0.112, 2, 4, 75.68, -162.93, 0.888, 7, 52.32, 24.42, 0.112, 2, 4, 137.84, -162.85, 0.792, 7, -8.45, 37.47, 0.208, 2, 4, 167, -171.28, 0.968, 7, -35.17, 51.88, 0.032, 2, 4, 204.4, -154.44, 0.904, 7, -75.28, 43.31, 0.096, 2, 4, 229.18, -117.85, 0.984, 7, -107.24, 12.79, 0.016, 1, 4, 239.83, -70.84, 1, 1, 4, 254.79, -4.81, 1, 2, 4, 252.38, 27.94, 0.936, 10, -70.92, 102.1, 0.064, 3, 4, 252.86, 59.49, 0.91985, 10, -76.82, 71.11, 0.08, 5, 163.92, 33.56, 0.00015, 2, 4, 222.46, 109.14, 0.872, 10, -55.4, 16.98, 0.128, 2, 4, 168.13, 124.22, 0.76, 10, -4.46, -7.22, 0.24, 2, 4, 133.07, 133.72, 0.792, 10, 28.44, -22.6, 0.208, 2, 4, 107.19, 139.58, 0.92, 10, 52.93, -32.82, 0.08, 1, 4, 69.5, 139.9, 1, 2, 3, 37.34, 132, 0.288, 4, 31.1, 129.72, 0.712, 2, 3, 30.01, 57.33, 0.64305, 4, 10.31, 57.63, 0.35695, 1, 4, 46.01, 61.31, 1, 2, 3, 21.89, 56.8, 0.84746, 4, 2.23, 58.58, 0.15254, 1, 3, 10.25, 56.35, 1, 2, 4, 113.76, 54.57, 0.952, 10, 61.06, 52.06, 0.048, 1, 4, -0.78, -65.28, 1, 1, 4, 40.8, -75.61, 1, 2, 3, 26.92, -8.84, 0.552, 4, -4.75, -6.88, 0.448, 2, 3, 32.83, -59.9, 0.16, 4, -8.22, -58.16, 0.84, 2, 4, 87.63, -83.39, 0.96882, 7, 23.84, -50.8, 0.03118, 3, 4, 142.5, -85.13, 0.79752, 7, -29.43, -37.51, 0.19348, 5, 88.24, -131.86, 0.00899, 2, 4, 190.82, -31.89, 0.984, 7, -87.9, -79.34, 0.016, 2, 4, 206.11, 32.99, 0.92, 10, -26.2, 89.18, 0.08], "hull": 27, "edges": [0, 52, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52], "width": 302, "height": 300}}, "Brute/GoatCult/Cape_Executioner": {"Cape_Executioner": {"name": "Brute/Executioner/Cape_Executioner", "type": "mesh", "uvs": [0.71897, 0.08967, 0.92297, 0.27735, 1, 0.51977, 1, 0.62665, 0.90825, 0.59537, 0.81151, 0.6084, 0.67902, 0.71006, 0.57387, 0.85343, 0.3867, 0.9577, 0.37828, 1, 0.31519, 1, 0.26472, 1, 0.24159, 0.93163, 0.1806, 0.88992, 0.05232, 0.74134, 1e-05, 0.63447, 1e-05, 0.44157, 0.10699, 0.23825, 0.25631, 0.0636, 0.37408, 0, 0.57386, 0, 0.30257, 0.75959, 0.34884, 0.46764, 0.43296, 0.15744, 0.38599, 0.30395, 0.63906, 0.25389, 0.84726, 0.39204, 0.56335, 0.5302], "triangles": [5, 27, 26, 3, 26, 2, 5, 26, 4, 3, 4, 26, 6, 27, 5, 7, 27, 6, 26, 1, 2, 13, 14, 21, 21, 14, 22, 14, 15, 22, 15, 16, 22, 16, 17, 22, 17, 18, 24, 27, 25, 26, 1, 26, 0, 26, 25, 0, 23, 20, 25, 25, 20, 0, 9, 10, 8, 21, 8, 10, 10, 11, 12, 10, 12, 21, 8, 21, 7, 12, 13, 21, 21, 27, 7, 21, 22, 27, 22, 24, 27, 27, 24, 25, 25, 24, 23, 22, 17, 24, 24, 18, 23, 23, 18, 19, 23, 19, 20], "vertices": [2, 4, 227.82, -157.12, 0.936, 7, -101.93, 41.55, 0.064, 3, 4, 149.21, -224.6, 0.48416, 7, -15.84, 99.19, 0.22784, 34, 75.18, -89.54, 0.288, 3, 4, 64.49, -237.62, 0.31162, 7, 69.83, 102.02, 0.1792, 34, -9.55, -102.55, 0.50918, 3, 4, 30.2, -229.85, 0.27178, 7, 102.95, 90.22, 0.208, 34, -43.84, -94.78, 0.52022, 3, 4, 48.5, -195.63, 0.30309, 7, 80.7, 58.42, 0.11635, 34, -25.54, -60.56, 0.58056, 3, 4, 53.03, -156.21, 0.24403, 7, 71.5, 19.82, 0.08397, 34, -21, -21.14, 0.672, 3, 4, 32.35, -96.13, 0.22022, 7, 84.87, -42.3, 0.07578, 34, -41.68, 38.94, 0.704, 3, 4, -4.17, -43.88, 0.66272, 7, 114.91, -98.52, 0.09728, 34, -78.21, 91.19, 0.24, 1, 4, -20.76, 38.14, 1, 1, 4, -33.58, 44.56, 1, 1, 4, -27.9, 69.65, 1, 1, 4, -23.35, 89.72, 1, 1, 4, 0.67, 93.95, 1, 2, 4, 19.55, 115.18, 0.776, 10, 78.5, 97.16, 0.224, 2, 4, 78.79, 155.4, 0.712, 10, 91.69, 26.79, 0.288, 2, 4, 117.79, 168.43, 0.712, 10, 88.04, -14.18, 0.288, 2, 4, 179.69, 154.41, 0.712, 10, 50.44, -65.3, 0.288, 2, 4, 235.29, 97.08, 0.904, 10, -24.33, -93.35, 0.096, 1, 4, 277.87, 25, 1, 1, 4, 287.67, -26.47, 1, 2, 4, 269.66, -105.92, 0.984, 7, -149.58, -4.29, 0.016, 1, 4, 50.38, 57.19, 1, 1, 4, 139.89, 17.57, 1, 1, 4, 231.84, -38.44, 1, 1, 4, 189.06, -9.11, 1, 3, 4, 182.32, -113.4, 0.66272, 7, -61.97, -7.28, 0.20928, 34, 108.29, 21.67, 0.128, 3, 4, 119.23, -186.16, 0.41888, 7, 9.34, 57.45, 0.19712, 34, 45.2, -51.09, 0.384, 3, 4, 100.49, -63.2, 0.5511, 7, 13.3, -66.87, 0.0809, 34, 26.45, 71.87, 0.368], "hull": 21, "edges": [0, 40, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40], "width": 425, "height": 329}}, "Eyes": {"Eyes_Closed": {"name": "Brute/GoatCult/Eyes_Closed_Executioner", "x": -26.24, "y": -8.76, "rotation": -90, "width": 149, "height": 37}, "Eyes": {"name": "Brute/GoatCult/Eyes_Executioner", "x": -18.14, "y": -11.46, "rotation": -90, "width": 168, "height": 75}, "Eyes_Squint": {"name": "Brute/GoatCult/Eyes_Squint_Executioner", "x": -20.84, "y": -10.11, "rotation": -90, "width": 132, "height": 51}}, "EYE_LEFT_SHOCKED": {"EYE_LEFT_SHOCKED": {"name": "Face/EYE_SHOCKED", "x": 0.5, "width": 65, "height": 68}}, "EYE_RIGHT_SHOCKED": {"EYE_RIGHT_SHOCKED": {"name": "Face/EYE_SHOCKED", "x": 0.5, "width": 65, "height": 68}}, "HAND_RIGHT": {"HAND_RIGHT": {"name": "Brute/Executioner/HandRight", "x": 30.53, "y": -3.57, "rotation": 116.13, "width": 111, "height": 94}}, "HAND_WEAPON": {"HAND_WEAPON": {"name": "Brute/Executioner/HandWeapon", "x": 19.27, "y": 3.71, "scaleX": 0.9359, "rotation": -90.61, "width": 88, "height": 123}}, "HEAD": {"HEAD_5": {"name": "B<PERSON><PERSON>/Executioner/Head", "x": 82.37, "y": 10.24, "rotation": -90, "width": 242, "height": 243}, "HEAD_1": {"name": "Brute/GoatCult/Head", "x": 82.37, "y": 10.24, "rotation": -90, "width": 277, "height": 223}, "HEAD_2": {"name": "Brute/GoatCult/Head", "x": 82.37, "y": 10.24, "rotation": -90, "width": 277, "height": 223}, "HEAD_3": {"name": "Brute/GoatCult/Head", "x": 82.37, "y": 10.24, "rotation": -90, "width": 277, "height": 223}, "HEAD_4": {"name": "Brute/GoatCult/Head", "x": 82.37, "y": 10.24, "rotation": -90, "width": 277, "height": 223}}, "LEG_LEFT": {"LEG_LEFT": {"name": "Brute/GoatCult/LegLeft", "type": "mesh", "uvs": [0, 0.28007, 0.05031, 0.48866, 0.06645, 0.55556, 0.07281, 0.58191, 0.07841, 0.60516, 0.09363, 0.66827, 0.12607, 0.80273, 0.12214, 0.81238, 0, 0.93475, 0, 1, 0.8772, 0.99488, 0.89778, 0.94577, 0.947, 0.69439, 0.9611, 0.62236, 0.96209, 0.59972, 0.96682, 0.57427, 0.97669, 0.50725, 1, 0.39571, 1, 0.25034, 0.8669, 0.08532, 0.58213, 0, 0.31795, 0, 0.08464, 0.08139, 0, 0.18551], "triangles": [9, 7, 10, 9, 8, 7, 10, 7, 11, 7, 6, 11, 13, 4, 14, 12, 5, 13, 6, 5, 12, 11, 6, 12, 5, 4, 13, 23, 22, 21, 23, 21, 20, 23, 20, 19, 0, 23, 19, 0, 19, 18, 0, 18, 17, 1, 0, 17, 16, 1, 17, 2, 1, 16, 15, 2, 16, 3, 2, 15, 14, 3, 15, 4, 3, 14], "vertices": [1, 16, -6.4, -34.34, 1, 2, 16, 19.56, -31.56, 0.96918, 17, -16.56, -32.04, 0.03082, 2, 16, 27.89, -30.66, 0.76177, 17, -8.21, -31.35, 0.23823, 2, 16, 31.17, -30.31, 0.536, 17, -4.93, -31.09, 0.464, 2, 16, 34.06, -30, 0.168, 17, -2.03, -30.85, 0.832, 2, 16, 41.91, -29.16, 0.048, 17, 5.85, -30.2, 0.952, 2, 17, 22.62, -28.83, 0.696, 18, 28.28, -17.45, 0.304, 2, 17, 23.8, -29.17, 0.544, 18, 28.66, -16.28, 0.456, 3, 16, 74.74, -36.8, 0.00044, 17, 38.47, -38.67, 0.00151, 18, 38.58, -1.89, 0.99804, 1, 18, 39.27, 6.17, 1, 1, 18, -22.84, 10.81, 1, 1, 17, 43.37, 24.9, 1, 2, 16, 46.99, 31.31, 0.032, 17, 12.44, 30.11, 0.968, 2, 16, 38.09, 32.58, 0.24, 17, 3.58, 31.61, 0.76, 2, 16, 35.29, 32.73, 0.79634, 17, 0.78, 31.83, 0.20366, 2, 16, 32.14, 33.17, 0.904, 17, -2.35, 32.34, 0.096, 2, 16, 23.86, 34.12, 0.98646, 17, -10.61, 33.51, 0.01354, 1, 16, 10.08, 36.19, 1, 2, 16, -7.94, 36.74, 0.96141, 17, -42.33, 36.93, 0.03859, 2, 16, -28.67, 27.91, 0.99928, 17, -63.28, 28.63, 0.00072, 1, 16, -39.86, 8.02, 1, 1, 16, -40.43, -10.73, 1, 1, 16, -30.84, -27.59, 1, 1, 16, -18.12, -33.99, 1], "hull": 24, "edges": [0, 46, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46], "width": 71, "height": 124}}, "LEG_RIGHT": {"LEG_RIGHT": {"name": "Brute/GoatCult/LegRight", "type": "mesh", "uvs": [0, 0.23093, 0.05174, 0.35375, 0.15194, 0.51159, 0.1851, 0.56322, 0.20301, 0.59226, 0.22216, 0.62036, 0.253, 0.67223, 0.34127, 0.81347, 0.33338, 0.8221, 0.25394, 0.93704, 0.26622, 1, 1, 1, 0.99999, 0.948, 0.95159, 0.68642, 0.93518, 0.61583, 0.9222, 0.57544, 0.9179, 0.53166, 0.90186, 0.46277, 0.85524, 0.272, 0.82845, 0.15678, 0.68851, 0, 0.33988, 0, 0, 0.12324], "triangles": [10, 9, 8, 11, 8, 12, 10, 8, 11, 5, 4, 15, 5, 15, 14, 6, 5, 14, 6, 14, 13, 7, 6, 13, 7, 13, 12, 8, 7, 12, 0, 22, 21, 19, 0, 21, 19, 21, 20, 1, 0, 19, 1, 19, 18, 2, 1, 18, 2, 18, 17, 3, 2, 17, 3, 17, 16, 4, 3, 16, 4, 16, 15], "vertices": [1, 13, -14.86, -36.99, 1, 1, 13, 0.66, -36.5, 1, 2, 13, 21.37, -33.03, 0.88655, 14, -20.8, -32.99, 0.11345, 2, 13, 28.15, -31.86, 0.84109, 14, -14.03, -31.74, 0.15891, 2, 13, 32, -31.28, 0.55708, 14, -10.19, -31.12, 0.44292, 2, 13, 35.62, -30.53, 0.30086, 14, -6.58, -30.33, 0.69914, 2, 13, 42.46, -29.64, 0.208, 14, 0.25, -29.36, 0.792, 2, 14, 18.67, -26.12, 0.68, 15, 30.46, -15.59, 0.32, 3, 13, 61.74, -27.52, 0.0001, 14, 19.52, -27.05, 0.54409, 15, 31.15, -14.54, 0.45581, 2, 14, 31.27, -37.2, 0.00285, 15, 38.06, -0.63, 0.99715, 1, 15, 36.99, 6.98, 1, 1, 15, -26.85, 6.98, 1, 2, 14, 48.8, 25.34, 0.744, 15, -26.99, 0.81, 0.256, 3, 13, 59.82, 28.68, 0.07347, 14, 16.97, 29.13, 0.92316, 15, -22.63, -30.96, 0.00337, 2, 13, 51.21, 29.52, 0.15363, 14, 8.34, 29.87, 0.84637, 2, 13, 46.21, 29.69, 0.50174, 14, 3.34, 29.99, 0.49826, 2, 13, 40.98, 30.7, 0.88382, 14, -1.89, 30.96, 0.11618, 2, 13, 32.57, 31.52, 0.89615, 14, -10.31, 31.68, 0.10385, 1, 13, 9.22, 33.58, 1, 1, 13, -4.85, 34.94, 1, 1, 13, -26.33, 28.1, 1, 1, 13, -34.19, -1.19, 1, 1, 13, -27.45, -33.62, 1], "hull": 23, "edges": [0, 44, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44], "width": 87, "height": 121}}, "PANTS": {"PANTS": {"name": "Brute/Pants", "x": -5.45, "y": -4.68, "rotation": -92.29, "width": 184, "height": 58}}, "ROBES": {"ROBES": {"name": "<PERSON><PERSON><PERSON>/Executioner/<PERSON><PERSON>", "type": "mesh", "uvs": [0.84569, 0.13425, 0.90622, 0.32216, 1, 0.47758, 1, 0.63069, 1, 0.79076, 1, 1, 0, 1, 0, 0.92298, 0.06132, 0.82323, 0.06637, 0.68173, 0.04115, 0.58894, 0, 0.48686, 0, 0.32448, 0, 0.19457, 0.23282, 0, 0.80281, 0], "triangles": [8, 4, 5, 6, 8, 5, 6, 7, 8, 3, 9, 2, 9, 3, 4, 9, 10, 2, 11, 1, 2, 10, 11, 2, 8, 9, 4, 11, 12, 1, 12, 0, 1, 12, 13, 0, 0, 14, 15, 0, 13, 14], "vertices": [1, 4, 176, -203.91, 1, 2, 4, 97.97, -209.01, 0.552, 3, 164.68, -188.93, 0.448, 2, 4, 29.88, -228.88, 0.352, 3, 101.34, -220.84, 0.648, 2, 4, -29.7, -215.38, 0.176, 3, 40.3, -218.4, 0.824, 2, 3, -23.52, -215.85, 0.496, 0, 212.32, 73.88, 0.504, 1, 0, 212.32, -9.61, 1, 1, 0, -154.68, -9.61, 1, 1, 0, -154.68, 21.12, 1, 1, 3, -22.7, 128.89, 1, 2, 4, 26.15, 123.3, 0.208, 3, 33.64, 124.78, 0.792, 2, 4, 64.31, 124.14, 0.368, 3, 71.01, 132.55, 0.632, 2, 4, 107.36, 129.87, 0.488, 3, 112.31, 146.01, 0.512, 2, 4, 170.55, 115.55, 0.91006, 3, 177.05, 143.42, 0.08994, 1, 4, 221.11, 104.1, 1, 1, 4, 277.94, 3.61, 1, 1, 4, 231.72, -200.4, 1], "hull": 16, "edges": [0, 30, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30], "width": 367, "height": 399}, "ROBES_dead": {"name": "Brute/GoatCult/Robes_Dead", "type": "mesh", "uvs": [0.84569, 0.13425, 0.90622, 0.32216, 1, 0.47758, 1, 0.63069, 1, 0.79076, 1, 1, 0, 1, 0, 0.92298, 0.06132, 0.82323, 0.06637, 0.68173, 0.04115, 0.58894, 0, 0.48686, 0, 0.32448, 0, 0.19457, 0.23282, 0, 0.80281, 0], "triangles": [8, 4, 5, 6, 8, 5, 6, 7, 8, 3, 9, 2, 9, 3, 4, 9, 10, 2, 11, 1, 2, 10, 11, 2, 8, 9, 4, 11, 12, 1, 12, 0, 1, 12, 13, 0, 0, 14, 15, 0, 13, 14], "vertices": [1, 4, 176, -203.91, 1, 2, 4, 97.97, -209.01, 0.552, 3, 164.68, -188.93, 0.448, 2, 4, 29.88, -228.88, 0.352, 3, 101.34, -220.84, 0.648, 2, 4, -29.7, -215.38, 0.176, 3, 40.3, -218.4, 0.824, 2, 3, -23.52, -215.85, 0.496, 0, 212.32, 73.88, 0.504, 1, 0, 212.32, -9.61, 1, 1, 0, -154.68, -9.61, 1, 1, 0, -154.68, 21.12, 1, 1, 3, -22.7, 128.89, 1, 2, 4, 26.15, 123.3, 0.208, 3, 33.64, 124.78, 0.792, 2, 4, 64.31, 124.14, 0.368, 3, 71.01, 132.55, 0.632, 2, 4, 107.36, 129.87, 0.488, 3, 112.31, 146.01, 0.512, 2, 4, 170.55, 115.55, 0.91006, 3, 177.05, 143.42, 0.08994, 1, 4, 221.11, 104.1, 1, 1, 4, 277.94, 3.61, 1, 1, 4, 231.72, -200.4, 1], "hull": 16, "edges": [0, 30, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30], "width": 367, "height": 399}}, "SHOULDER_RIGHT": {"ARM_RIGHT": {"name": "Brute/Executioner/ArmRight", "type": "mesh", "uvs": [0.02711, 0.09266, 0, 0.15072, 0, 0.25594, 0.04596, 0.41741, 0.11194, 0.50086, 0.21529, 0.5408, 0.15653, 0.57893, 0.10746, 0.66024, 0.12964, 0.82705, 0.20661, 0.94106, 0.44649, 1, 0.71464, 1, 0.85692, 0.9407, 0.93748, 0.81899, 0.98532, 0.70827, 0.96929, 0.63299, 1, 0.59148, 0.99999, 0.54968, 1, 0.50892, 1, 0.45795, 0.99999, 0.34667, 0.8756, 0.13702, 0.73165, 0.03535, 0.49272, 0, 0.37233, 0], "triangles": [18, 5, 19, 19, 5, 4, 19, 4, 20, 4, 3, 20, 3, 21, 20, 3, 2, 21, 2, 22, 21, 2, 1, 22, 1, 23, 22, 1, 24, 23, 1, 0, 24, 11, 10, 12, 10, 9, 12, 9, 8, 12, 12, 8, 13, 8, 7, 13, 13, 7, 14, 7, 6, 14, 6, 15, 14, 6, 5, 15, 15, 5, 16, 5, 17, 16, 17, 5, 18], "vertices": [2, 8, -95.25, -103.43, 2e-05, 7, -18.32, -44.37, 0.99998, 2, 8, -80.98, -105.27, 0.00129, 7, -6.77, -52.95, 0.99871, 2, 8, -56.08, -101.76, 0.01674, 7, 16.67, -62.07, 0.98326, 3, 8, -18.77, -89.98, 0.0599, 7, 54.98, -70.02, 0.93971, 9, -105.34, -76.58, 0.00038, 2, 8, -0.31, -78, 0.11891, 7, 76.94, -68.6, 0.88109, 2, 8, 7.11, -62.26, 0.476, 7, 91.11, -58.5, 0.524, 2, 8, 17.29, -69.18, 0.86357, 7, 96.6, -69.51, 0.13643, 2, 8, 37.43, -73.29, 0.89856, 7, 112.21, -82.95, 0.10144, 3, 8, 76.21, -64.53, 0.84824, 7, 150.44, -94.25, 0.00421, 9, -5.89, -62.14, 0.14755, 2, 8, 101.81, -50.25, 0.7599, 9, 20.88, -51.99, 0.2401, 2, 8, 111.26, -14.84, 0.51224, 9, 32.98, -19.11, 0.48776, 2, 8, 106, 22.52, 0.55914, 9, 32.34, 18.56, 0.44086, 2, 8, 89.17, 40.43, 0.67052, 9, 17.8, 38.47, 0.32948, 2, 8, 58.76, 47.67, 0.952, 9, -13.84, 49.07, 0.048, 2, 8, 31.58, 50.67, 0.98037, 7, 167.59, 28.11, 0.01963, 2, 8, 14.13, 45.91, 0.85903, 7, 150, 32.51, 0.14097, 2, 8, 3.72, 48.83, 0.6977, 7, 142.44, 40.03, 0.3023, 2, 8, -6.09, 47.48, 0.62915, 7, 133.2, 43.59, 0.37085, 2, 8, -15.69, 46.16, 0.3996, 7, 124.04, 47.2, 0.6004, 2, 8, -27.89, 44.4, 0.13915, 7, 112.69, 51.62, 0.86085, 2, 8, -54.24, 40.67, 0.032, 7, 87.9, 61.26, 0.968, 1, 7, 34.85, 63.1, 1, 1, 7, 4.86, 53.02, 1, 1, 7, -15.2, 24.74, 1, 1, 7, -21.34, 8.95, 1], "hull": 25, "edges": [0, 48, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48], "width": 145, "height": 242}}, "WEAPON": {"Axe": {"name": "<PERSON><PERSON><PERSON>/Executioner/Axe", "x": 140.17, "y": 24.39, "scaleX": 1.1144, "scaleY": 1.1144, "rotation": -92.85, "width": 223, "height": 478}, "Axe_Stuck": {"name": "Brute/Executioner/Axe_Stuck", "x": -12.89, "y": 8.12, "scaleX": 1.1144, "scaleY": 1.1144, "rotation": -92.44, "width": 59, "height": 310}}}}], "events": {"execute": {}, "footsteps": {}, "shake": {}, "throw": {}}, "animations": {"attack-charge": {"bones": {"HIP": {"rotate": [{"time": 0.3333, "curve": [0.455, 0, 0.558, -14.51]}, {"time": 0.5667, "value": -18.73, "curve": [0.817, -18.73, 1.192, -17.91]}, {"time": 1.5667, "value": -17.09}], "translate": [{"y": -1.68, "curve": [0.083, 0, 0.25, 0, 0.083, -1.68, 0.25, -24.41]}, {"time": 0.3333, "y": -24.41, "curve": [0.455, 0, 0.558, 22.99, 0.455, -24.41, 0.558, 5.02]}, {"time": 0.5667, "x": 29.68, "y": 13.58, "curve": [0.817, 29.68, 1.192, 31.63, 0.817, 13.58, 1.192, 12.8]}, {"time": 1.5667, "x": 33.59, "y": 12.02}]}, "HAND_RIGHT_CONSTRAINT": {"translate": [{"x": 2.18, "y": -4.38, "curve": [0.042, 2.18, 0.125, 42.27, 0.042, -4.38, 0.125, 74.37]}, {"time": 0.1667, "x": 42.27, "y": 74.37, "curve": "stepped"}, {"time": 0.3333, "x": 42.27, "y": 74.37, "curve": [0.455, 42.27, 0.558, 58.37, 0.455, 74.37, 0.558, -6.71]}, {"time": 0.5667, "x": 63.05, "y": -30.29, "curve": [0.758, 77.15, 1.317, 83.8, 0.758, -55.5, 1.317, -67.39]}, {"time": 1.5667, "x": 83.8, "y": -67.39}]}, "HAND_RIGHT": {"rotate": [{"value": -21.28}]}, "HEAD": {"rotate": [{"curve": [0.081, 0, 0.224, 19.61]}, {"time": 0.3333, "value": 28.66, "curve": [0.371, 32.06, 0.405, 34.27]}, {"time": 0.4333, "value": 34.27, "curve": [0.508, 34.27, 0.658, -8.81]}, {"time": 0.7333, "value": -8.81}], "translate": [{"time": 0.4333, "curve": [0.53, 4.5, 0.633, 9.76, 0.53, -1.45, 0.633, -3.15]}, {"time": 0.7333, "x": 14.82, "y": -4.78}]}, "WEAPON_CONSTRAINT": {"translate": [{"x": -1.74, "y": -2.65, "curve": [0.042, -1.74, 0.125, -23.95, 0.042, -2.65, 0.125, 20.02]}, {"time": 0.1667, "x": -23.95, "y": 20.02, "curve": "stepped"}, {"time": 0.3333, "x": -23.95, "y": 20.02, "curve": [0.455, -23.95, 0.558, 98.37, 0.455, 20.02, 0.558, -88.99]}, {"time": 0.5667, "x": 133.95, "y": -120.7, "curve": [0.583, 133.95, 0.617, 129.7, 0.583, -120.7, 0.617, -121.45]}, {"time": 0.6333, "x": 129.7, "y": -121.45, "curve": [0.65, 129.7, 0.683, 133.95, 0.65, -121.45, 0.683, -120.7]}, {"time": 0.7, "x": 133.95, "y": -120.7, "curve": [0.723, 132.41, 0.746, 130.86, 0.723, -120.97, 0.746, -121.24]}, {"time": 0.7667, "x": 129.7, "y": -121.45, "curve": [0.792, 132.25, 0.815, 133.95, 0.792, -121, 0.815, -120.7]}, {"time": 0.8333, "x": 133.95, "y": -120.7, "curve": [0.856, 132.91, 0.879, 131.87, 0.856, -120.82, 0.879, -120.94]}, {"time": 0.9, "x": 131.09, "y": -121.03, "curve": [0.925, 132.81, 0.948, 133.95, 0.925, -120.83, 0.948, -120.7]}, {"time": 0.9667, "x": 133.95, "y": -120.7, "curve": [0.99, 131.9, 1.012, 129.86, 0.99, -121.13, 1.012, -121.56]}, {"time": 1.0333, "x": 128.32, "y": -121.88, "curve": [1.059, 131.7, 1.082, 133.95, 1.059, -121.17, 1.082, -120.7]}, {"time": 1.1, "x": 133.95, "y": -120.7, "curve": [1.123, 132.41, 1.146, 130.86, 1.123, -120.96, 1.146, -121.22]}, {"time": 1.1667, "x": 129.71, "y": -121.41, "curve": [1.192, 132.25, 1.215, 133.95, 1.192, -120.99, 1.215, -120.7]}, {"time": 1.2333, "x": 133.95, "y": -120.7, "curve": [1.245, 131.26, 1.256, 128.58, 1.245, -120.84, 1.256, -120.98]}, {"time": 1.2667, "x": 126.56, "y": -121.09, "curve": [1.292, 130.99, 1.315, 133.95, 1.292, -120.86, 1.315, -120.7]}, {"time": 1.3333, "x": 133.95, "y": -120.7, "curve": [1.356, 129.97, 1.379, 125.99, 1.356, -120.29, 1.379, -119.89]}, {"time": 1.4, "x": 123, "y": -119.58, "curve": [1.425, 129.57, 1.448, 133.95, 1.425, -120.25, 1.448, -120.7]}, {"time": 1.4667, "x": 133.95, "y": -120.7, "curve": [1.49, 131.26, 1.512, 128.58, 1.49, -120.84, 1.512, -120.98]}, {"time": 1.5333, "x": 126.56, "y": -121.09, "curve": [1.546, 130.99, 1.557, 133.95, 1.546, -120.86, 1.557, -120.7]}, {"time": 1.5667, "x": 133.95, "y": -120.7}]}, "HAND_LEFT_HANDLE": {"translate": [{"x": 5.8, "y": 10.41, "curve": [0.083, 5.8, 0.25, -63.45, 0.083, 10.41, 0.25, 10.34]}, {"time": 0.3333, "x": -63.45, "y": 10.34, "curve": [0.455, -63.45, 0.558, 116.79, 0.455, 10.34, 0.558, -73.06]}, {"time": 0.5667, "x": 169.22, "y": -97.32, "curve": [0.567, 169.4, 1.062, 169.56, 0.567, -105.58, 1.062, -113.39]}, {"time": 1.5667, "x": 169.56, "y": -113.39}]}, "BACK_BTM": {"rotate": [{"curve": [0.048, 6.36, 0.125, 7.54]}, {"time": 0.1667, "value": 7.54, "curve": "stepped"}, {"time": 0.3333, "value": 7.54, "curve": [0.682, 0.2, 1.258, -3.61]}, {"time": 1.5667, "value": -3.61}]}, "BACK_TOP": {"rotate": [{"curve": [0.048, 13.8, 0.125, 16.36]}, {"time": 0.1667, "value": 16.36, "curve": [0.254, 16.36, 0.292, 10.33]}, {"time": 0.3333, "value": 10.33, "curve": [0.455, 10.33, 0.558, -8.17]}, {"time": 0.5667, "value": -13.55, "curve": [0.771, -15.93, 1.317, -16.6]}, {"time": 1.5667, "value": -16.6}]}, "FOOT_LEFT_CONSTRAINT": {"translate": [{"time": 0.5, "curve": [0.508, 0, 0.521, -1.19, 0.508, 0, 0.521, 14.12]}, {"time": 0.5333, "x": -2.38, "y": 28.25, "curve": [0.583, -3.56, 0.633, -4.75, 0.583, 39.22, 0.633, 50.19]}, {"time": 0.6667, "x": -4.75, "y": 50.19, "curve": [0.728, -9.1, 1.342, -10.72, 0.728, 65.41, 1.342, 71.07]}, {"time": 1.5667, "x": -10.72, "y": 71.07}]}, "LEG_LEFT": {"rotate": [{"time": 0.3333, "value": -41.97, "curve": [0.642, -41.97, 1.258, -57.05]}, {"time": 1.5667, "value": -57.05}], "translate": [{"curve": [0.098, 0, 0.21, -0.55, 0.098, 0, 0.21, 0.76]}, {"time": 0.3333, "x": -1.48, "y": 2.05, "curve": [0.694, -3.86, 1.167, -9.82, 0.694, 5.34, 1.167, 13.6]}, {"time": 1.5667, "x": -13.42, "y": 18.58}]}, "root": {"scale": [{"time": 0.0667, "curve": [0.083, 1, 0.117, 1.095, 0.083, 1, 0.117, 0.919]}, {"time": 0.1333, "x": 1.095, "y": 0.919, "curve": [0.158, 1.095, 0.208, 1.011, 0.158, 0.919, 0.208, 0.984]}, {"time": 0.2333, "x": 1.011, "y": 0.984, "curve": [0.258, 1.011, 0.3, 1.035, 0.258, 0.984, 0.3, 0.956]}, {"time": 0.3333, "x": 1.046, "y": 0.943, "curve": [0.346, 1.051, 0.357, 1.054, 0.346, 0.937, 0.357, 0.933]}, {"time": 0.3667, "x": 1.054, "y": 0.933, "curve": "stepped"}, {"time": 0.5, "x": 1.054, "y": 0.933, "curve": [0.517, 1.054, 0.55, 0.997, 0.517, 0.933, 0.55, 1.007]}, {"time": 0.5667, "x": 0.997, "y": 1.007, "curve": [0.6, 0.997, 0.667, 1.14, 0.6, 1.007, 0.667, 0.871]}, {"time": 0.7, "x": 1.14, "y": 0.871, "curve": [0.75, 1.14, 0.85, 1.054, 0.75, 0.871, 0.85, 0.933]}, {"time": 0.9, "x": 1.054, "y": 0.933}]}, "ANKLE_LEFT_CONSTRAINT": {"translate": [{"time": 0.5, "curve": [0.508, 0, 0.521, 0.52, 0.508, 0, 0.521, 5.48]}, {"time": 0.5333, "x": 1.05, "y": 10.97, "curve": [0.583, 2.88, 0.633, 4.71, 0.583, 5.48, 0.633, 0]}, {"time": 0.6667, "x": 4.71}]}, "SHOULDER_RIGHT": {"rotate": [{"time": 0.3333, "value": -18.43}]}, "ARM_RIGHT": {"rotate": [{"time": 0.3333, "value": -30.43}]}, "SHOULDER_LEFT": {"rotate": [{"time": 0.3333, "value": -53.46}]}, "ARM_LEFT": {"rotate": [{"time": 0.3333, "value": -84.78}]}, "WEAPON_CONTROLLER": {"rotate": [{"time": 0.3333, "value": 37.27}]}, "LEG_RIGHT": {"rotate": [{"time": 0.3333, "value": -35.9}]}, "LEG_RIGHT_BTM": {"rotate": [{"time": 0.3333, "value": 85.6}]}, "FOOT_RIGHT": {"rotate": [{"time": 0.3333, "value": -50.43}]}, "LEG_LEFT_BTM": {"rotate": [{"time": 0.3333, "value": 87.63}]}, "FOOT_LEFT": {"rotate": [{"time": 0.3333, "value": -44.55}]}}}, "attack-impact": {"slots": {"Dust0": {"rgba": [{"time": 0.3667, "color": "282828ff", "curve": [0.462, 0.16, 0.575, 0.16, 0.462, 0.16, 0.575, 0.16, 0.462, 0.16, 0.575, 0.16, 0.462, 0.45, 0.575, 0.08]}, {"time": 0.6667, "color": "28282800"}], "attachment": [{"time": 0.0667, "name": "Brute/Dust/Dust_0000"}, {"time": 0.6667}]}, "Dust1": {"rgba": [{"time": 0.3667, "color": "323030ff", "curve": [0.462, 0.2, 0.575, 0.2, 0.462, 0.19, 0.575, 0.19, 0.462, 0.19, 0.575, 0.19, 0.462, 0.45, 0.575, 0.08]}, {"time": 0.6667, "color": "32303000"}], "attachment": [{"time": 0.0667, "name": "Brute/Dust/Dust_0002"}, {"time": 0.6667}]}, "Dust2": {"rgba": [{"time": 0.3667, "color": "282828ff", "curve": [0.462, 0.16, 0.575, 0.16, 0.462, 0.16, 0.575, 0.16, 0.462, 0.16, 0.575, 0.16, 0.462, 0.45, 0.575, 0.08]}, {"time": 0.6667, "color": "28282800"}], "attachment": [{"time": 0.0667, "name": "Brute/Dust/Dust_0003"}, {"time": 0.6667}]}, "Dust3": {"rgba": [{"time": 0.3667, "color": "323030ff", "curve": [0.462, 0.2, 0.575, 0.2, 0.462, 0.19, 0.575, 0.19, 0.462, 0.19, 0.575, 0.19, 0.462, 0.45, 0.575, 0.08]}, {"time": 0.6667, "color": "32303000"}], "attachment": [{"time": 0.0667, "name": "Brute/Dust/Dust_0004"}, {"time": 0.6667}]}, "Dust4": {"rgba": [{"time": 0.3667, "color": "282828ff", "curve": [0.462, 0.16, 0.575, 0.16, 0.462, 0.16, 0.575, 0.16, 0.462, 0.16, 0.575, 0.16, 0.462, 0.45, 0.575, 0.08]}, {"time": 0.6667, "color": "28282800"}], "attachment": [{"time": 0.0667, "name": "Brute/Dust/Dust_0005"}, {"time": 0.6667}]}, "Dust5": {"rgba": [{"time": 0.3667, "color": "323030ff", "curve": [0.462, 0.2, 0.575, 0.2, 0.462, 0.19, 0.575, 0.19, 0.462, 0.19, 0.575, 0.19, 0.462, 0.45, 0.575, 0.08]}, {"time": 0.6667, "color": "32303000"}], "attachment": [{"time": 0.0667, "name": "Brute/Dust/Dust_0006"}, {"time": 0.6667}]}, "Earth_0": {"attachment": [{"time": 0.0667, "name": "Brute/EarthBits/EarthBit_0000"}, {"time": 0.6667}]}, "Earth_1": {"attachment": [{"time": 0.0667, "name": "Brute/EarthBits/EarthBit_0001"}, {"time": 0.6667}]}, "Earth_2": {"attachment": [{"time": 0.0667, "name": "Brute/EarthBits/EarthBit_0002"}, {"time": 0.7}]}, "Earth_3": {"attachment": [{"time": 0.0667, "name": "Brute/EarthBits/EarthBit_0003"}, {"time": 0.7}]}, "Earth_4": {"attachment": [{"time": 0.0667, "name": "Brute/EarthBits/EarthBit_0004"}, {"time": 0.6667}]}, "Impact": {"attachment": [{"time": 0.0667, "name": "Brute/Impact/Impact_0000"}, {"time": 0.1, "name": "Brute/Impact/Impact_0001"}, {"time": 0.1333, "name": "Brute/Impact/Impact_0002"}, {"time": 0.1667}]}, "Smear": {"attachment": [{"time": 0.0667, "name": "<PERSON><PERSON><PERSON>/SmearLarge/SmearLarge"}, {"time": 0.2}]}, "Smear2": {"attachment": [{"time": 0.0667, "name": "B<PERSON>te/SmearLarge/SmearLarge2"}, {"time": 0.2}]}}, "bones": {"Dust0": {"rotate": [{"time": 0.0667, "value": 2.29, "curve": [0.067, -10.94, 0.396, -14.44]}, {"time": 0.6667, "value": -14.73}], "translate": [{"time": 0.0667, "x": 54.92, "curve": [0.067, 57.06, 0.396, 57.63, 0.067, 20.53, 0.396, 25.98]}, {"time": 0.6667, "x": 57.68, "y": 26.42}], "scale": [{"time": 0.0667, "x": 2.156, "y": 0.477, "curve": [0.067, 1.502, 0.396, 1.329, 0.067, 0.884, 0.396, 0.991]}, {"time": 0.6667, "x": 1.315}], "shear": [{"time": 0.0667, "y": 11.75, "curve": [0.067, -9.71, 0.396, -12.28, 0.067, -1.47, 0.396, -4.98]}, {"time": 0.6667, "x": -12.49, "y": -5.26}]}, "Dust1": {"rotate": [{"time": 0.0667, "value": 23.22, "curve": [0.075, -6.53, 0.359, -14.23]}, {"time": 0.6667, "value": -17.37}], "translate": [{"time": 0.0667, "x": -20.34, "y": -26.36, "curve": [0.075, 69, 0.359, 92.13, 0.075, 55.92, 0.359, 77.22]}, {"time": 0.6667, "x": 101.55, "y": 85.9}], "scale": [{"time": 0.0667, "x": 1.344, "y": 0.446, "curve": [0.075, 1.155, 0.359, 1.106, 0.075, 0.681, 0.359, 0.742]}, {"time": 0.6667, "x": 1.086, "y": 0.767}], "shear": [{"time": 0.0667, "y": -46.22, "curve": [0.075, 0, 0.359, 0, 0.075, -8.82, 0.359, 0.86]}, {"time": 0.6667, "y": 4.81}]}, "Dust2": {"rotate": [{"time": 0.0667, "value": 3.43, "curve": [0.096, 15.57, 0.257, 16.85]}, {"time": 0.6, "value": 17.42}], "translate": [{"time": 0.0667, "x": -7.27, "y": 28.55, "curve": [0.096, -13.39, 0.257, -14.04, 0.096, 50.72, 0.257, 53.07]}, {"time": 0.6, "x": -14.32, "y": 54.1}], "scale": [{"time": 0.0667, "x": 1.38, "y": 0.593, "curve": [0.096, 1.485, 0.257, 1.496, 0.096, 1.174, 0.257, 1.236]}, {"time": 0.6, "x": 1.501, "y": 1.263}]}, "Dust3": {"rotate": [{"time": 0.0667, "value": -27.2, "curve": [0.075, -22.94, 0.463, -21.86]}, {"time": 0.6667, "value": -21.53}], "translate": [{"time": 0.0667, "x": 33.11, "y": 2.5, "curve": [0.075, -29.1, 0.463, -44.81, 0.075, -0.82, 0.463, -1.66]}, {"time": 0.6667, "x": -49.68, "y": -1.92}], "scale": [{"time": 0.0667, "x": 1.669, "y": 0.512, "curve": [0.075, 1.531, 0.463, 1.497, 0.075, 0.856, 0.463, 0.943]}, {"time": 0.6667, "x": 1.486, "y": 0.97}], "shear": [{"time": 0.0667, "y": 7.94, "curve": [0.075, 0, 0.463, 0, 0.075, 18.85, 0.463, 21.61]}, {"time": 0.6667, "y": 22.46}]}, "Dust4": {"rotate": [{"time": 0.0667, "value": -2.69}], "translate": [{"time": 0.0667, "x": 1.05, "y": -2.83, "curve": [0.08, -0.25, 0.111, -1.25, 0.08, 1.25, 0.111, 4.4]}, {"time": 0.1333, "x": -1.4, "y": 4.85, "curve": [0.153, -29.43, 0.199, -51.04, 0.153, 92.69, 0.199, 160.42]}, {"time": 0.2333, "x": -54.14, "y": 170.14, "curve": [0.286, -68.98, 0.41, -80.42, 0.286, 216.63, 0.41, 252.47]}, {"time": 0.5, "x": -82.06, "y": 257.62}], "scale": [{"time": 0.0667, "y": 0.311, "curve": [0.08, 1, 0.111, 1, 0.08, 1.171, 0.111, 1.835]}, {"time": 0.1333, "y": 1.93, "curve": [0.153, 0.843, 0.199, 0.721, 0.153, 1.538, 0.199, 1.236]}, {"time": 0.2333, "x": 0.704, "y": 1.192, "curve": [0.286, 0.704, 0.41, 0.704, 0.286, 0.875, 0.41, 0.631]}, {"time": 0.5, "x": 0.704, "y": 0.596}]}, "Dust5": {"translate": [{"time": 0.1333, "x": 0.73, "y": 11.86, "curve": [0.153, 6.78, 0.199, 11.45, 0.153, 110.44, 0.199, 186.45]}, {"time": 0.2333, "x": 12.12, "y": 197.36, "curve": [0.305, 14.65, 0.476, 16.59, 0.305, 238.42, 0.476, 270.08]}, {"time": 0.6, "x": 16.87, "y": 274.62}], "scale": [{"time": 0.0667, "y": 0.352, "curve": [0.08, 1, 0.111, 1, 0.08, 0.723, 0.111, 1.009]}, {"time": 0.1333, "y": 1.05, "curve": [0.153, 1, 0.199, 1, 0.153, 0.783, 0.199, 0.577]}, {"time": 0.2333, "y": 0.548, "curve": [0.305, 1, 0.476, 1, 0.305, 0.437, 0.476, 0.352]}, {"time": 0.6, "y": 0.34}]}, "Earth_0": {"rotate": [{"time": 0.0667, "value": -26.13, "curve": [0.178, 21.73, 0.441, 58.63]}, {"time": 0.6333, "value": 63.92}], "translate": [{"time": 0.0667, "x": -86.71, "y": 37.81, "curve": [0.178, -239.6, 0.441, -357.48, 0.178, 182.01, 0.441, 293.2]}, {"time": 0.6333, "x": -374.4, "y": 309.16}], "scale": [{"time": 0.5333, "x": 1.041, "y": 1.041, "curve": [0.553, 0.546, 0.599, 0.164, 0.553, 0.546, 0.599, 0.164]}, {"time": 0.6333, "x": 0.109, "y": 0.109}]}, "Earth_1": {"rotate": [{"time": 0.6333, "value": -50.77}], "translate": [{"time": 0.0667, "x": -33.35, "y": 100.81, "curve": [0.086, 18.22, 0.133, 57.98, 0.086, 250.54, 0.133, 365.97]}, {"time": 0.1667, "x": 63.68, "y": 382.55, "curve": [0.258, 132.36, 0.475, 185.31, 0.258, 447.13, 0.475, 496.92]}, {"time": 0.6333, "x": 192.91, "y": 504.07}], "scale": [{"time": 0.5333, "x": 0.977, "y": 0.977, "curve": [0.553, 0.572, 0.599, 0.26, 0.553, 0.572, 0.599, 0.26]}, {"time": 0.6333, "x": 0.215, "y": 0.215}]}, "Earth_2": {"rotate": [{"time": 0.0667, "value": 110.77, "curve": [0.184, 17, 0.463, -55.29]}, {"time": 0.6667, "value": -65.67}], "translate": [{"time": 0.0667, "x": 46.69, "y": 100.81, "curve": [0.184, 275.31, 0.463, 451.57, 0.184, 196.56, 0.463, 270.39]}, {"time": 0.6667, "x": 476.88, "y": 280.98}], "scale": [{"time": 0.5667, "x": 1.004, "y": 1.004, "curve": [0.586, 0.539, 0.633, 0.18, 0.586, 0.539, 0.633, 0.18]}, {"time": 0.6667, "x": 0.129, "y": 0.129}]}, "Earth_3": {"rotate": [{"time": 0.0667, "value": 50.06, "curve": [0.184, 13.37, 0.463, -14.91]}, {"time": 0.6667, "value": -18.97}], "translate": [{"time": 0.0667, "x": 91.71, "y": 40.33, "curve": [0.184, 255.22, 0.463, 381.29, 0.184, 103.29, 0.463, 151.83]}, {"time": 0.6667, "x": 399.39, "y": 158.8}], "scale": [{"time": 0.6, "x": 1.017, "y": 1.017, "curve": [0.613, 0.501, 0.644, 0.103, 0.613, 0.501, 0.644, 0.103]}, {"time": 0.6667, "x": 0.046, "y": 0.046}]}, "Earth_4": {"rotate": [{"time": 0.0667, "value": 25.05, "curve": [0.178, 45.15, 0.441, 60.64]}, {"time": 0.6333, "value": 62.86}], "translate": [{"time": 0.0667, "x": -10, "y": 40.33, "curve": [0.178, -115.25, 0.441, -196.39, 0.178, 318.61, 0.441, 533.17]}, {"time": 0.6333, "x": -208.04, "y": 563.97}], "scale": [{"time": 0.5333, "x": 0.994, "y": 0.994, "curve": [0.553, 0.601, 0.599, 0.298, 0.553, 0.601, 0.599, 0.298]}, {"time": 0.6333, "x": 0.254, "y": 0.254}]}, "Impact": {"translate": [{"time": 0.0667, "x": -901.85, "y": -18.57, "curve": [0.073, -912.88, 0.089, -921.38, 0.073, -11.73, 0.089, -6.45]}, {"time": 0.1, "x": -922.6, "y": -5.69, "curve": [0.107, -947.41, 0.122, -966.54, 0.107, -2.7, 0.122, -0.39]}, {"time": 0.1333, "x": -969.29, "y": -0.06}], "scale": [{"time": 0.1, "x": 1.175, "y": 1.175, "curve": [0.107, 1.346, 0.122, 1.477, 0.107, 1.346, 0.122, 1.477]}, {"time": 0.1333, "x": 1.496, "y": 1.496}]}, "Smear": {"rotate": [{"time": 0.0667, "value": -4.6, "curve": "stepped"}, {"time": 0.1, "value": -7.34, "curve": "stepped"}, {"time": 0.1333, "value": -7.34}, {"time": 0.1667, "value": -11.07}], "translate": [{"time": 0.0667, "x": -759.85, "y": 164.45, "curve": "stepped"}, {"time": 0.1, "x": -757.28, "y": 152.88}, {"time": 0.1333, "x": -795.12, "y": 127.66}, {"time": 0.1667, "x": -808.29, "y": 112.72}], "scale": [{"time": 0.0667, "x": 1.949, "y": 1.947, "curve": "stepped"}, {"time": 0.1, "x": 2.047, "y": 1.732}, {"time": 0.1333, "x": 2.047, "y": 1.5}, {"time": 0.1667, "x": 2.047, "y": 1.516}], "shear": [{"time": 0.0667, "y": -3.96, "curve": "stepped"}, {"time": 0.1, "x": 5.48, "y": -3.96}, {"time": 0.1333, "x": 5.48, "y": -8.04}, {"time": 0.1667, "x": 5.48, "y": -19.58}]}, "HIP": {"rotate": [{"value": -17.09, "curve": [0.025, -8.55, 0.05, 0]}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.4, "curve": [0.675, 0, 1.225, -1.43]}, {"time": 1.5, "value": -1.43}], "translate": [{"x": 33.59, "y": 12.02, "curve": [0.025, 5.88, 0.05, -21.83, 0.025, -3.74, 0.05, -19.5]}, {"time": 0.0667, "x": -21.83, "y": -19.5, "curve": [0.122, -21.83, 0.317, -21.83, 0.122, -24.07, 0.317, -27.6]}, {"time": 0.4, "x": -21.83, "y": -27.6, "curve": [0.583, -21.83, 0.95, 0.12, 0.583, -27.6, 0.95, -9.39]}, {"time": 1.1333, "x": 0.12, "y": -9.39, "curve": [1.158, 0.12, 1.208, 2.34, 1.158, -9.39, 1.208, -29.48]}, {"time": 1.2333, "x": 2.34, "y": -29.48, "curve": [1.3, 2.34, 1.433, 0, 1.3, -29.48, 1.433, -1.68]}, {"time": 1.5, "y": -1.68}]}, "HAND_RIGHT_CONSTRAINT": {"rotate": [{"time": 0.4, "curve": [0.675, 0, 1.225, -1.43]}, {"time": 1.5, "value": -1.43}], "translate": [{"x": 83.8, "y": -67.39, "curve": [0.025, 75.32, 0.05, 66.85, 0.025, -94.68, 0.05, -121.98]}, {"time": 0.0667, "x": 66.85, "y": -121.98, "curve": [0.105, 114.34, 0.192, 129.86, 0.105, -166.48, 0.192, -181.02]}, {"time": 0.2333, "x": 129.86, "y": -181.02, "curve": "stepped"}, {"time": 0.4, "x": 129.86, "y": -181.02, "curve": [0.675, 129.86, 1.225, 2.18, 0.675, -181.02, 1.225, -4.38]}, {"time": 1.5, "x": 2.18, "y": -4.38}]}, "HEAD": {"rotate": [{"value": -8.81, "curve": [0.017, -8.81, 0.05, -7.29]}, {"time": 0.0667, "value": -7.29, "curve": [0.124, 6.18, 0.317, 20.04]}, {"time": 0.4, "value": 20.04, "curve": [0.675, 20.04, 1.225, 5.87]}, {"time": 1.5, "value": 5.87}], "translate": [{"x": 14.82, "y": -4.78, "curve": [0.025, 7.09, 0.05, 0, 0.025, -2.29, 0.05, 0]}, {"time": 0.0667, "curve": [0.083, 0, 0.117, -24.71, 0.083, 0, 0.117, 14.7]}, {"time": 0.1333, "x": -24.71, "y": 14.7, "curve": "stepped"}, {"time": 0.4, "x": -24.71, "y": 14.7, "curve": [0.675, -24.71, 1.225, 0, 0.675, 14.7, 1.225, 0]}, {"time": 1.5}]}, "WEAPON_CONSTRAINT": {"rotate": [{"time": 0.4, "curve": [0.675, 0, 1.225, -1.43]}, {"time": 1.5, "value": -1.43}], "translate": [{"x": 133.95, "y": -120.7, "curve": [0.017, 133.95, 0.05, -244.1, 0.017, -120.7, 0.05, 114.66]}, {"time": 0.0667, "x": -244.1, "y": 114.66, "curve": "stepped"}, {"time": 0.4, "x": -244.1, "y": 114.66, "curve": [0.508, -244.1, 0.725, -29.17, 0.508, 114.66, 0.725, 138.45]}, {"time": 0.8333, "x": -29.17, "y": 138.45, "curve": [1.078, -7.17, 1.332, -1.74, 1.078, 25.29, 1.332, -2.65]}, {"time": 1.5, "x": -1.74, "y": -2.65}]}, "HAND_LEFT_HANDLE": {"rotate": [{"time": 0.4, "curve": [0.675, 0, 1.225, -1.43]}, {"time": 1.5, "value": -1.43}], "translate": [{"x": 169.56, "y": -113.39, "curve": [0.025, -25.31, 0.05, -220.18, 0.025, -112.31, 0.05, -111.23]}, {"time": 0.0667, "x": -220.18, "y": -111.23, "curve": "stepped"}, {"time": 0.4, "x": -220.18, "y": -111.23, "curve": [0.508, -220.18, 0.725, -129.13, 0.508, -111.23, 0.725, 57.3]}, {"time": 0.8333, "x": -129.13, "y": 57.3, "curve": [1.044, -70.33, 1.272, -26.54, 1.044, 36.86, 1.272, 21.65]}, {"time": 1.5, "x": 5.8, "y": 10.41}]}, "BACK_BTM": {"rotate": [{"value": -3.61, "curve": [0.038, -2.14, 0.062, -0.73]}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.4, "curve": [0.675, 0, 1.225, -1.43]}, {"time": 1.5, "value": -1.43}]}, "BACK_TOP": {"rotate": [{"value": -16.6, "curve": [0.025, 1.99, 0.05, 20.58]}, {"time": 0.0667, "value": 20.58, "curve": "stepped"}, {"time": 0.4, "value": 20.58, "curve": [0.558, 20.58, 0.875, -1.43]}, {"time": 1.0333, "value": -1.43}]}, "FOOT_LEFT_CONSTRAINT": {"rotate": [{"time": 1.0667, "curve": [1.175, 0, 1.392, -1.43]}, {"time": 1.5, "value": -1.43}], "translate": [{"x": -10.72, "y": 71.07, "curve": [0.025, -18.92, 0.05, -27.42, 0.025, 34.88, 0.05, -2.61]}, {"time": 0.0667, "x": -27.42, "y": -2.61, "curve": "stepped"}, {"time": 0.9333, "x": -27.42, "y": -2.61, "curve": [0.969, -27.42, 1.016, -23.14, 0.969, -2.61, 1.016, 5.65]}, {"time": 1.0667, "x": -17.22, "y": 17.09, "curve": [1.124, -10.66, 1.193, 0, 1.124, 10.58, 1.193, 0]}, {"time": 1.2333}]}, "LEG_LEFT": {"rotate": [{"value": -57.05, "curve": [0.1, -57.05, 0.3, -42.29]}, {"time": 0.4, "value": -42.29}], "translate": [{"x": -13.42, "y": 18.58, "curve": [0.025, -5.47, 0.048, 0, 0.025, 7.57, 0.048, 0]}, {"time": 0.0667}]}, "HAND_RIGHT": {"rotate": [{"value": -21.28, "curve": "stepped"}, {"time": 0.4, "value": -21.28, "curve": [0.675, -21.28, 1.225, -22.71]}, {"time": 1.5, "value": -22.71}]}, "root": {"rotate": [{"time": 0.4, "curve": [0.675, 0, 1.225, -1.43]}, {"time": 1.5, "value": -1.43}], "translate": [{"time": 0.4, "curve": [0.534, 0, 0.733, 0, 0.534, 0, 0.733, -1.02]}, {"time": 0.9333, "y": -2.07, "curve": [1.145, 0, 1.359, 0, 1.145, -1.05, 1.359, 0]}, {"time": 1.5}], "scale": [{"x": 1.054, "y": 0.933, "curve": [0.017, 1.054, 0.05, 1.175, 0.017, 0.933, 0.05, 0.777]}, {"time": 0.0667, "x": 1.175, "y": 0.777, "curve": [0.1, 1.175, 0.167, 0.873, 0.1, 0.777, 0.167, 1.163]}, {"time": 0.2, "x": 0.873, "y": 1.163, "curve": [0.25, 0.873, 0.35, 1, 0.25, 1.163, 0.35, 1]}, {"time": 0.4}]}, "Holder": {"rotate": [{"time": 0.4, "curve": [0.675, 0, 1.225, -1.43]}, {"time": 1.5, "value": -1.43}], "translate": [{"time": 0.1, "curve": [0.117, 0, 0.15, 17.44, 0.117, 0, 0.15, 0]}, {"time": 0.1667, "x": 17.44, "curve": [0.183, 17.44, 0.217, 0, 0.183, 0, 0.217, 0]}, {"time": 0.2333, "curve": [0.242, 0, 0.258, 17.44, 0.242, 0, 0.258, 0]}, {"time": 0.2667, "x": 17.44, "curve": [0.275, 17.44, 0.292, 0, 0.275, 0, 0.292, 0]}, {"time": 0.3}]}, "FACE": {"rotate": [{"time": 0.4, "curve": [0.675, 0, 1.225, -1.43]}, {"time": 1.5, "value": -1.43}]}, "SHOULDER_RIGHT": {"rotate": [{"time": 0.4, "value": 81.78}]}, "ARM_RIGHT": {"rotate": [{"time": 0.4, "value": -11.81}]}, "SHOULDER_LEFT": {"rotate": [{"value": -169.2, "curve": [0.017, -169.2, 0.05, -346.25]}, {"time": 0.0667, "value": -346.25, "curve": [0.092, -346.25, 0.142, -384.57]}, {"time": 0.1667, "value": -384.57, "curve": "stepped"}, {"time": 0.7, "value": -384.57, "curve": [0.775, -384.57, 0.925, -493.34]}, {"time": 1, "value": -493.34, "curve": [1.042, -493.34, 1.125, -443.05]}, {"time": 1.1667, "value": -443.05}]}, "ARM_LEFT": {"rotate": [{"value": 27.63, "curve": [0.017, 27.63, 0.05, -15.02]}, {"time": 0.0667, "value": -15.02, "curve": [0.092, -15.02, 0.142, -24.19]}, {"time": 0.1667, "value": -24.19, "curve": "stepped"}, {"time": 0.7, "value": -24.19, "curve": [0.775, -24.19, 0.925, -21.93]}, {"time": 1, "value": -21.93, "curve": [1.042, -21.93, 1.125, -63.49]}, {"time": 1.1667, "value": -63.49, "curve": [1.208, -63.49, 1.292, -58.71]}, {"time": 1.3333, "value": -58.71}]}, "WEAPON_CONTROLLER": {"rotate": [{"time": 0.0667, "value": 23.42, "curve": [0.092, 23.42, 0.142, 69.05]}, {"time": 0.1667, "value": 69.05, "curve": [0.175, 69.05, 0.192, 77.82]}, {"time": 0.2, "value": 77.82, "curve": [0.217, 77.82, 0.25, 65.93]}, {"time": 0.2667, "value": 65.93, "curve": [0.292, 65.93, 0.342, 71.21]}, {"time": 0.3667, "value": 71.21, "curve": [0.45, 71.21, 0.617, 75.57]}, {"time": 0.7, "value": 75.57, "curve": [0.8, 75.57, 1, 22.28]}, {"time": 1.1, "value": 22.28, "curve": [1.117, 22.28, 1.15, 18.92]}, {"time": 1.1667, "value": 18.92, "curve": [1.208, 18.92, 1.292, 22.28]}, {"time": 1.3333, "value": 22.28}]}, "LEG_RIGHT": {"rotate": [{"time": 0.4, "value": -26.15}]}, "LEG_RIGHT_BTM": {"rotate": [{"time": 0.4, "value": 57.52}]}, "FOOT_RIGHT": {"rotate": [{"time": 0.4, "value": -32.86}]}, "LEG_LEFT_BTM": {"rotate": [{"time": 0.4, "value": 38.18}]}, "FOOT_LEFT": {"rotate": [{"time": 0.4, "value": 4.81}]}, "FOOT_RIGHT_CONSTRAINT": {"rotate": [{"time": 0.4, "curve": [0.675, 0, 1.225, -1.43]}, {"time": 1.5, "value": -1.43}]}, "ANKLE_RIGHT_CONSTRAINT": {"rotate": [{"time": 0.4, "curve": [0.675, 0, 1.225, -1.43]}, {"time": 1.5, "value": -1.43}]}, "ANKLE_LEFT_CONSTRAINT": {"rotate": [{"time": 0.4, "curve": [0.548, 0, 0.778, -0.42]}, {"time": 1, "value": -0.8, "curve": [1.189, -1.13, 1.373, -1.43]}, {"time": 1.5, "value": -1.43}], "translate": [{"x": 4.71, "curve": [0.1, 4.71, 0.3, 0, 0.1, 0, 0.3, 0]}, {"time": 0.4}]}, "MASK": {"rotate": [{"time": 0.4, "curve": [0.675, 0, 1.225, -1.43]}, {"time": 1.5, "value": -1.43}]}, "HORN_RIGHT": {"rotate": [{"time": 0.4, "curve": [0.675, 0, 1.225, -1.43]}, {"time": 1.5, "value": -1.43}]}, "HORN_LEFT": {"rotate": [{"time": 0.4, "curve": [0.675, 0, 1.225, -1.43]}, {"time": 1.5, "value": -1.43}]}, "EYE_LEFT": {"rotate": [{"time": 0.4, "curve": [0.675, 0, 1.225, -1.43]}, {"time": 1.5, "value": -1.43}]}, "EYE_RIGHT": {"rotate": [{"time": 0.4, "curve": [0.675, 0, 1.225, -1.43]}, {"time": 1.5, "value": -1.43}]}, "WEAPON": {"scale": [{"time": 0.0667, "y": 0.605, "curve": [0.092, 1, 0.142, 1, 0.092, 0.605, 0.142, 1.249]}, {"time": 0.1667, "y": 1.249, "curve": [0.192, 1, 0.242, 1, 0.192, 1.249, 0.242, 1]}, {"time": 0.2667}]}, "Dust": {"translate": [{"time": 0.0667, "x": -1365.84, "y": -100.78, "curve": "stepped"}, {"time": 0.1, "x": -1365.84, "y": -100.78}, {"time": 0.1333, "x": -1404.28, "y": -100.78}, {"time": 0.1667, "x": -1449.45, "y": -100.78}, {"time": 0.2, "x": -1478.53, "y": -100.78}, {"time": 0.2333, "x": -1467.9, "y": -100.78}, {"time": 0.3667, "x": -1423.29, "y": -100.78}, {"time": 0.4333, "x": -1419.92, "y": -100.78}, {"time": 0.8333, "x": -1408.41, "y": -100.78}], "scale": [{"time": 0.0667, "x": 1.311, "y": 1.311}]}, "Earth": {"translate": [{"time": 0.0667, "x": -940.19}]}}, "ik": {"HAND_LEFT_CONSTRAINT": [{"time": 0.0667, "mix": 0, "bendPositive": false}], "WEAPON_CONSTRAINT": [{"time": 0.0667, "mix": 0}]}, "attachments": {"default": {"Dust0": {"Brute/Dust/Dust_0000": {"deform": [{"time": 0.0667, "offset": 10, "vertices": [-2.77924, 1.40099], "curve": [0.067, 0.38, 0.127, 0.71]}, {"time": 0.2333, "vertices": [21.14168, 44.97668, 22.59386, 41.8233, 5.17831, 27.93256, 10.74312, 8.46714, -13.48578, -10.83247, 9.02514, 27.70879, -1.46181, 15.58468, -14.70113, 35.79169, -0.06683, 49.81784, 4.28214, 26.35291, 9.82052, 7.12302, 16.1412, 30.82455, 12.12171, 23.72879, 9.34422, -8.90532, -6.54366, 37.87544, 4.67269, 20.06754, 21.861, 28.38634], "curve": [0.301, 0.75, 0.499, 0.98]}, {"time": 0.6667, "offset": 2, "vertices": [16.57312, 35.9512, 10.39021, 80.91143, 27.26723, 67.45119, 31.18956, 65.92776, 33.09038, 82.2692, -6.35344, 81.16692, -30.04517, 99.83315, -16.29826, 98.58762, 1.09705, 23.06935, 2.91962, -9.6787, 0, 0, 15.66345, 42.92908, 36.19974, 51.7182, 7.54071, 43.14592, -7.4162, 74.78352, 14.29805, 34.34819]}]}}, "Dust1": {"Brute/Dust/Dust_0002": {"deform": [{"time": 0.0667, "offset": 14, "vertices": [4.96161, -20.26488], "curve": [0.075, 0.73, 0.359, 0.92]}, {"time": 0.6667, "vertices": [0.92885, 14.71867, 27.84665, 23.28078, 43.2652, 27.37051, 35.87217, -6.46289, 5.43054, -11.64361, -4.08037, -16.05656, 10.64265, 13.11245, 4.96161, -20.26488, 8.0889, 8.75429]}]}}, "Dust2": {"Brute/Dust/Dust_0003": {"deform": [{"time": 0.0667, "vertices": [-0.01248, -66.52872, -16.59615, -56.30849, -7.31314, -2.85818, 0, 0, 2.27437, 15.3261, 2.27437, 15.3261, 0, 0, 0, 0, 0, 0, 0.14595, -50.89735, 3.26016, -37.64927, -0.01248, -66.5287, -5.59096, -37.06142, -11.39275, -19.88055], "curve": [0.123, 0.37, 0.205, 0.71]}, {"time": 0.3, "vertices": [-3.50149, -13.64775, -2.22484, 5.7802, 1.57967, 28.12178, 1.69059, 17.18996, -4.33159, 23.03421, -0.57697, 26.58369, -5.31025, 10.39643, -1.60089, 3.56733, -6.52814, -0.03957, -11.07267, -17.64996, -3.90579, 5.4173, -3.50149, -13.64775, -6.1789, 11.23767, -0.36285, 14.98593], "curve": [0.391, 0.55, 0.507, 0.92]}, {"time": 0.6, "vertices": [-26.19375, -13.90097, -11.3663, 19.97942, 2.88469, 67.429, 9.10858, 33.44606, -4.05867, 34.55207, -8.66219, 34.06543, -13.94417, 13.00096, -2.81926, 6.28227, -11.49643, -0.06969, -22.74341, -47.81552, -10.12213, -7.19276, -18.74553, -23.70113, 10.00172, 18.41362, -0.63899, 26.39109]}]}}, "Dust3": {"Brute/Dust/Dust_0004": {"deform": [{"time": 0.0667, "offset": 18, "vertices": [-2.24252, -2.76761], "curve": [0.075, 0.75, 0.463, 0.94]}, {"time": 0.6667, "vertices": [-17.80805, 28.3045, 10.26488, 50.12612, 8.90495, 33.1897, 5.4884, -1.76276, 2.84511, 2.12671, 5.84075, -14.90561, 0, 0, -13.52111, -36.91901, -24.96795, -15.86963, 8.03205, 38.68213, 8.39204, -1.8266]}]}}, "Smear": {"Brute/SmearLarge/SmearLarge": {"deform": [{"time": 0.0667, "offset": 50, "vertices": [-0.27608, 1.84958], "curve": "stepped"}, {"time": 0.1, "vertices": [-22.14711, -19.01312, -18.43342, -16.38521, -24.45667, -11.28967, -29.72713, -10.93063, -17.82999, -1.22096, -23.00043, -4.34314, -30.32787, 1.12169, -26.30789, 5.48555, -29.62771, 7.51543, -36.67925, 2.45663, -36.40134, 6.4665, -41.13233, -2.75899, -38.7691, 0.09269, -36.33473, -1.92068, -36.14464, -8.53252, -31.54145, -0.85487, -23.45941, -12.42677, -17.75472, -5.42162, -19.26242, 6.3391, -3.25187, 3.92213, -4.97489, 6.62051, -4.36428, 6.13799, -4.44322, 6.24428, -5.41681, 4.70938, -2.82205, 2.44672, -4.65561, 1.89696, -5.1899, 9.02635, -6.21385, -18.90035, -1.47098, -3.53498, -3.56537, -11.13763, -4.99875, -11.83337, -1.50764, -14.38657, -20.2819, -21.15221], "curve": [0.113, 0.53, 0.144, 0.94]}, {"time": 0.1667, "vertices": [-66.44132, -57.03938, -55.30025, -49.15562, -73.37, -33.869, -89.18137, -32.7919, -53.49, -3.66289, -69.0013, -13.0294, -90.98363, 3.36507, -78.92368, 16.45665, -88.88315, 22.5463, -110.03777, 7.3699, -109.20404, 19.39951, -123.397, -8.27696, -116.30733, 0.27808, -109.0042, -5.76204, -108.43394, -25.59756, -94.62437, -2.56462, -70.37823, -37.28032, -53.26418, -16.26483, -57.78727, 19.01727, -9.75562, 11.76642, -14.92467, 19.86154, -13.09285, 18.41394, -13.32967, 18.73285, -16.25043, 14.12813, -8.46617, 7.34016, -13.41469, 1.99173, -15.5697, 27.07904, -4.83182, -50.24709, -4.41292, -10.60495, -10.69612, -33.4129, -14.99624, -35.50012, -4.52293, -43.15968, -60.84571, -63.45663]}]}}, "Smear2": {"Brute/SmearLarge/SmearLarge2": {"deform": [{"time": 0.0667, "vertices": [108.15683, -313.1635, 85.63467, -331.59442, -0.49896, -392.7517, -23.34851, -410.07587, 54.87012, -290.05835, 72.02264, -276.3615, 74.78668, -225.47368, 131.73622, -123.4559, 151.63438, -51.4669, 154.39317, -30.01057, 159.4514, 45.45517, 156.0872, 65.57237, 138.19066, 138.23651, 106.72259, 185.38185, 84.98229, 183.95505, 21.2383, 229.22888, 5.52095, 225.60553, -76.82526, 241.91194, -116.55821, 273.81476, 57.71316, 369.018, -10.8773, 363.50122, -15.90878, 364.1695, -11.57712, 367.71466, 8.22958, 369.80157, 67.47775, 392.76324, 146.74416, 380.09875, 176.3882, 404.7895, 322.0774, 296.43127, 344.65436, 269.7082, 366.94263, 146.0677, 329.33472, 26.04422, 285.24847, -90.9068, 168.95116, -251.3034], "curve": "stepped"}, {"time": 0.1, "offset": 50, "vertices": [-0.27608, 1.84958]}, {"time": 0.1667, "vertices": [-66.44132, -57.03938, -55.30025, -49.15562, -73.37, -33.869, -89.18137, -32.7919, -53.49, -3.66289, -69.0013, -13.0294, -90.98363, 3.36507, -78.92368, 16.45665, -88.88315, 22.5463, -110.03777, 7.3699, -109.20404, 19.39951, -123.397, -8.27696, -116.30733, 0.27808, -109.0042, -5.76204, -108.43394, -25.59756, -94.62437, -2.56462, -70.37823, -37.28032, -53.26418, -16.26483, -57.78727, 19.01727, -9.75562, 11.76642, -14.92467, 19.86154, -13.09285, 18.41394, -13.32967, 18.73285, -16.25043, 14.12813, -8.46617, 7.34016, -13.41469, 1.99173, -15.5697, 27.07904, -4.83182, -50.24709, -4.41292, -10.60495, -10.69612, -33.4129, -14.99624, -35.50012, -4.52293, -43.15968, -60.84571, -63.45663]}]}}}}}, "attack-impact-multi": {"bones": {"HIP": {"rotate": [{"value": -15.45, "curve": [0.017, -15.45, 0.05, 0]}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.5667, "curve": [0.618, -8.43, 0.666, -15.45]}, {"time": 0.7, "value": -15.45, "curve": [0.717, -15.45, 0.75, 0]}, {"time": 0.7667, "curve": "stepped"}, {"time": 1.2, "curve": [1.283, 0, 1.45, -1.43]}, {"time": 1.5333, "value": -1.43}], "translate": [{"x": 40.47, "y": -1.68, "curve": [0.017, 40.47, 0.05, 0, 0.017, -1.68, 0.05, -10.82]}, {"time": 0.0667, "y": -10.82, "curve": "stepped"}, {"time": 0.5667, "y": -10.82, "curve": [0.618, 22.08, 0.666, 40.47, 0.618, -5.84, 0.666, -1.68]}, {"time": 0.7, "x": 40.47, "y": -1.68, "curve": [0.717, 40.47, 0.75, 0, 0.717, -1.68, 0.75, -10.82]}, {"time": 0.7667, "y": -10.82, "curve": "stepped"}, {"time": 1.2, "y": -10.82, "curve": [1.283, 0, 1.45, 0, 1.283, -10.82, 1.45, -1.68]}, {"time": 1.5333, "y": -1.68}]}, "HAND_RIGHT_CONSTRAINT": {"rotate": [{"time": 1.2, "curve": [1.283, 0, 1.45, -1.43]}, {"time": 1.5333, "value": -1.43}], "translate": [{"x": 104.55, "y": -104.48, "curve": [0.017, 104.55, 0.05, 66.85, 0.017, -104.48, 0.05, -121.98]}, {"time": 0.0667, "x": 66.85, "y": -121.98, "curve": [0.105, 114.34, 0.192, 129.86, 0.105, -166.48, 0.192, -181.02]}, {"time": 0.2333, "x": 129.86, "y": -181.02, "curve": "stepped"}, {"time": 0.2667, "x": 129.86, "y": -181.02, "curve": [0.32, 111.85, 0.393, 66.85, 0.32, -164.15, 0.393, -121.98]}, {"time": 0.4333, "x": 66.85, "y": -121.98, "curve": [0.456, 114.34, 0.508, 129.86, 0.456, -166.48, 0.508, -181.02]}, {"time": 0.5333, "x": 129.86, "y": -181.02, "curve": "stepped"}, {"time": 0.5667, "x": 129.86, "y": -181.02, "curve": [0.609, 122.63, 0.668, 104.55, 0.609, -159.15, 0.668, -104.48]}, {"time": 0.7, "x": 104.55, "y": -104.48, "curve": [0.717, 104.55, 0.75, 66.85, 0.717, -104.48, 0.75, -121.98]}, {"time": 0.7667, "x": 66.85, "y": -121.98, "curve": [0.805, 114.34, 0.892, 129.86, 0.805, -166.48, 0.892, -181.02]}, {"time": 0.9333, "x": 129.86, "y": -181.02, "curve": "stepped"}, {"time": 1.2, "x": 129.86, "y": -181.02, "curve": [1.283, 129.86, 1.45, 2.18, 1.283, -181.02, 1.45, -4.38]}, {"time": 1.5333, "x": 2.18, "y": -4.38}]}, "HEAD": {"rotate": [{"value": -7.29, "curve": "stepped"}, {"time": 0.0667, "value": -7.29, "curve": [0.083, -7.29, 0.117, 20.04]}, {"time": 0.1333, "value": 20.04, "curve": [0.166, 20.04, 0.22, 8.3]}, {"time": 0.2667, "value": 0.47, "curve": [0.305, -3.97, 0.34, -7.29]}, {"time": 0.3667, "value": -7.29, "curve": "stepped"}, {"time": 0.4333, "value": -7.29, "curve": [0.45, -7.29, 0.483, 20.04]}, {"time": 0.5, "value": 20.04, "curve": [0.516, 20.04, 0.543, 8.3]}, {"time": 0.5667, "value": 0.47, "curve": [0.618, -3.97, 0.665, -7.29]}, {"time": 0.7, "value": -7.29, "curve": "stepped"}, {"time": 0.7667, "value": -7.29, "curve": [0.783, -7.29, 0.817, 20.04]}, {"time": 0.8333, "value": 20.04, "curve": [0.866, 20.04, 0.92, 8.3]}, {"time": 0.9667, "value": 0.47, "curve": [1.03, -3.97, 1.089, -7.29]}, {"time": 1.1333, "value": -7.29, "curve": "stepped"}, {"time": 1.2, "value": -7.29, "curve": [1.283, -7.29, 1.45, 5.87]}, {"time": 1.5333, "value": 5.87}], "translate": [{"time": 0.0667, "curve": [0.083, 0, 0.117, -24.71, 0.083, 0, 0.117, 14.7]}, {"time": 0.1333, "x": -24.71, "y": 14.7, "curve": "stepped"}, {"time": 0.2667, "x": -24.71, "y": 14.7, "curve": [0.329, -12.36, 0.392, 0, 0.329, 7.35, 0.392, 0]}, {"time": 0.4333, "curve": [0.45, 0, 0.483, -24.71, 0.45, 0, 0.483, 14.7]}, {"time": 0.5, "x": -24.71, "y": 14.7, "curve": "stepped"}, {"time": 0.5667, "x": -24.71, "y": 14.7, "curve": [0.617, -12.36, 0.667, 0, 0.617, 7.35, 0.667, 0]}, {"time": 0.7, "curve": "stepped"}, {"time": 0.7667, "curve": [0.783, 0, 0.817, -24.71, 0.783, 0, 0.817, 14.7]}, {"time": 0.8333, "x": -24.71, "y": 14.7, "curve": "stepped"}, {"time": 1.2, "x": -24.71, "y": 14.7, "curve": [1.283, -24.71, 1.45, 0, 1.283, 14.7, 1.45, 0]}, {"time": 1.5333}]}, "WEAPON_CONSTRAINT": {"rotate": [{"time": 1.2, "curve": [1.283, 0, 1.45, -1.43]}, {"time": 1.5333, "value": -1.43}], "translate": [{"x": 344.22, "y": -222.29, "curve": [0.017, 344.22, 0.05, -110.96, 0.017, -222.29, 0.05, 141.53]}, {"time": 0.0667, "x": -110.96, "y": 141.53, "curve": "stepped"}, {"time": 0.5667, "x": -110.96, "y": 141.53, "curve": [0.617, 239.64, 0.665, 344.22, 0.617, -138.7, 0.665, -222.29]}, {"time": 0.7, "x": 344.22, "y": -222.29, "curve": [0.717, 344.22, 0.75, -110.96, 0.717, -222.29, 0.75, 141.53]}, {"time": 0.7667, "x": -110.96, "y": 141.53, "curve": "stepped"}, {"time": 1.2, "x": -110.96, "y": 141.53, "curve": [1.283, -110.96, 1.45, -1.74, 1.283, 141.53, 1.45, -2.65]}, {"time": 1.5333, "x": -1.74, "y": -2.65}]}, "HAND_LEFT_HANDLE": {"rotate": [{"time": 1.2, "curve": [1.283, 0, 1.45, -1.43]}, {"time": 1.5333, "value": -1.43}], "translate": [{"x": -196.45, "y": 22.49, "curve": [0.017, -196.45, 0.05, 101.74, 0.017, 22.49, 0.05, 73.63]}, {"time": 0.0667, "x": 101.74, "y": 73.63, "curve": "stepped"}, {"time": 0.5667, "x": 101.74, "y": 73.63, "curve": [0.61, -18.78, 0.655, -116.9, 0.61, 52.96, 0.655, 36.13]}, {"time": 0.7, "x": -196.45, "y": 22.49, "curve": [0.717, -196.45, 0.75, 101.74, 0.717, 22.49, 0.75, 73.63]}, {"time": 0.7667, "x": 101.74, "y": 73.63, "curve": "stepped"}, {"time": 1.2, "x": 101.74, "y": 73.63, "curve": [1.283, 101.74, 1.45, 5.8, 1.283, 73.63, 1.45, 10.41]}, {"time": 1.5333, "x": 5.8, "y": 10.41}]}, "BACK_BTM": {"rotate": [{"value": -24.21, "curve": [0.017, -24.21, 0.05, 0]}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.5667, "curve": [0.617, -18.65, 0.665, -24.21]}, {"time": 0.7, "value": -24.21, "curve": [0.717, -24.21, 0.75, 0]}, {"time": 0.7667, "curve": "stepped"}, {"time": 1.2, "curve": [1.283, 0, 1.45, -1.43]}, {"time": 1.5333, "value": -1.43}]}, "BACK_TOP": {"rotate": [{"value": -10.24, "curve": [0.017, -10.24, 0.05, 20.58]}, {"time": 0.0667, "value": 20.58, "curve": "stepped"}, {"time": 0.5667, "value": 20.58, "curve": [0.617, -3.16, 0.665, -10.24]}, {"time": 0.7, "value": -10.24, "curve": [0.717, -10.24, 0.75, 20.58]}, {"time": 0.7667, "value": 20.58, "curve": "stepped"}, {"time": 1.2, "value": 20.58, "curve": [1.283, 20.58, 1.45, -1.43]}, {"time": 1.5333, "value": -1.43}]}, "FOOT_LEFT_CONSTRAINT": {"rotate": [{"time": 1.2, "curve": [1.283, 0, 1.45, -1.43]}, {"time": 1.5333, "value": -1.43}], "translate": [{"x": -27.42, "y": 94, "curve": [0.017, -27.42, 0.05, -27.42, 0.017, 94, 0.05, -2.61]}, {"time": 0.0667, "x": -27.42, "y": -2.61, "curve": "stepped"}, {"time": 0.5667, "x": -27.42, "y": -2.61, "curve": [0.599, -27.42, 0.641, -27.42, 0.599, 50.09, 0.641, 94]}, {"time": 0.7, "x": -27.42, "y": 94, "curve": [0.717, -27.42, 0.75, -27.42, 0.717, 94, 0.75, -2.61]}, {"time": 0.7667, "x": -27.42, "y": -2.61, "curve": "stepped"}, {"time": 1.2, "x": -27.42, "y": -2.61, "curve": [1.283, -27.42, 1.45, 0, 1.283, -2.61, 1.45, 0]}, {"time": 1.5333}]}, "LEG_LEFT": {"rotate": [{"time": 0.2667, "value": -42.29}], "translate": [{"x": -16.97, "y": 23.5, "curve": [0.017, -16.97, 0.05, 0, 0.017, 23.5, 0.05, 0]}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.5667, "curve": [0.618, -9.26, 0.666, -16.97, 0.618, 12.82, 0.666, 23.5]}, {"time": 0.7, "x": -16.97, "y": 23.5, "curve": [0.717, -16.97, 0.75, 0, 0.717, 23.5, 0.75, 0]}, {"time": 0.7667}]}, "HAND_RIGHT": {"rotate": [{"time": 0.0667, "value": -21.28, "curve": "stepped"}, {"time": 1.2, "value": -21.28, "curve": [1.283, -21.28, 1.45, -22.71]}, {"time": 1.5333, "value": -22.71}]}, "root": {"rotate": [{"time": 1.2, "curve": [1.283, 0, 1.45, -1.43]}, {"time": 1.5333, "value": -1.43}], "scale": [{"curve": [0.017, 1, 0.05, 1.175, 0.017, 1, 0.05, 0.777]}, {"time": 0.0667, "x": 1.175, "y": 0.777, "curve": [0.1, 1.175, 0.167, 0.873, 0.1, 0.777, 0.167, 1.163]}, {"time": 0.2, "x": 0.873, "y": 1.163, "curve": [0.258, 0.873, 0.375, 1.175, 0.258, 1.163, 0.375, 0.777]}, {"time": 0.4333, "x": 1.175, "y": 0.777, "curve": [0.458, 1.175, 0.508, 0.873, 0.458, 0.777, 0.508, 1.163]}, {"time": 0.5333, "x": 0.873, "y": 1.163, "curve": [0.575, 0.873, 0.658, 1, 0.575, 1.163, 0.658, 1]}, {"time": 0.7, "curve": [0.717, 1, 0.75, 1.175, 0.717, 1, 0.75, 0.777]}, {"time": 0.7667, "x": 1.175, "y": 0.777, "curve": [0.8, 1.175, 0.867, 0.873, 0.8, 0.777, 0.867, 1.163]}, {"time": 0.9, "x": 0.873, "y": 1.163, "curve": [0.975, 0.873, 1.125, 1, 0.975, 1.163, 1.125, 1]}, {"time": 1.2}]}, "Holder": {"rotate": [{"time": 1.2, "curve": [1.283, 0, 1.45, -1.43]}, {"time": 1.5333, "value": -1.43}]}, "FACE": {"rotate": [{"time": 1.2, "curve": [1.283, 0, 1.45, -1.43]}, {"time": 1.5333, "value": -1.43}]}, "SHOULDER_RIGHT": {"rotate": [{"time": 0.2667, "value": 81.78}]}, "ARM_RIGHT": {"rotate": [{"time": 0.2667, "value": -11.81}]}, "SHOULDER_LEFT": {"rotate": [{"time": 0.2667, "value": 6.49, "curve": [0.308, 6.49, 0.392, -24.57]}, {"time": 0.4333, "value": -24.57, "curve": [0.467, -24.57, 0.533, 6.49]}, {"time": 0.5667, "value": 6.49, "curve": "stepped"}, {"time": 0.9667, "value": 6.49, "curve": [1.025, 6.49, 1.142, -24.57]}, {"time": 1.2, "value": -24.57}]}, "ARM_LEFT": {"rotate": [{"time": 0.2667, "value": -29.73, "curve": [0.308, -29.73, 0.392, -24.19]}, {"time": 0.4333, "value": -24.19, "curve": [0.467, -24.19, 0.533, -29.73]}, {"time": 0.5667, "value": -29.73, "curve": "stepped"}, {"time": 0.9667, "value": -29.73, "curve": [1.025, -29.73, 1.142, -24.19]}, {"time": 1.2, "value": -24.19}]}, "WEAPON_CONTROLLER": {"rotate": [{"time": 0.2667, "value": 44.18, "curve": [0.308, 44.18, 0.392, 69.05]}, {"time": 0.4333, "value": 69.05, "curve": [0.467, 69.05, 0.533, 44.18]}, {"time": 0.5667, "value": 44.18, "curve": "stepped"}, {"time": 0.9667, "value": 44.18, "curve": [1.025, 44.18, 1.142, 69.05]}, {"time": 1.2, "value": 69.05}]}, "LEG_RIGHT": {"rotate": [{"time": 0.2667, "value": -26.15}]}, "LEG_RIGHT_BTM": {"rotate": [{"time": 0.2667, "value": 57.52}]}, "FOOT_RIGHT": {"rotate": [{"time": 0.2667, "value": -32.86}]}, "LEG_LEFT_BTM": {"rotate": [{"time": 0.2667, "value": 38.18}]}, "FOOT_LEFT": {"rotate": [{"time": 0.2667, "value": 4.81}]}, "FOOT_RIGHT_CONSTRAINT": {"rotate": [{"time": 1.2, "curve": [1.283, 0, 1.45, -1.43]}, {"time": 1.5333, "value": -1.43}]}, "ANKLE_RIGHT_CONSTRAINT": {"rotate": [{"time": 1.2, "curve": [1.283, 0, 1.45, -1.43]}, {"time": 1.5333, "value": -1.43}]}, "ANKLE_LEFT_CONSTRAINT": {"rotate": [{"time": 1.2, "curve": [1.283, 0, 1.45, -1.43]}, {"time": 1.5333, "value": -1.43}]}, "MASK": {"rotate": [{"time": 1.2, "curve": [1.283, 0, 1.45, -1.43]}, {"time": 1.5333, "value": -1.43}]}, "HORN_RIGHT": {"rotate": [{"time": 1.2, "curve": [1.283, 0, 1.45, -1.43]}, {"time": 1.5333, "value": -1.43}]}, "HORN_LEFT": {"rotate": [{"time": 1.2, "curve": [1.283, 0, 1.45, -1.43]}, {"time": 1.5333, "value": -1.43}]}, "EYE_LEFT": {"rotate": [{"time": 1.2, "curve": [1.283, 0, 1.45, -1.43]}, {"time": 1.5333, "value": -1.43}]}, "EYE_RIGHT": {"rotate": [{"time": 1.2, "curve": [1.283, 0, 1.45, -1.43]}, {"time": 1.5333, "value": -1.43}]}, "WEAPON": {"scale": [{"time": 0.0667, "y": 0.605, "curve": [0.092, 1, 0.142, 1, 0.092, 0.605, 0.142, 1.249]}, {"time": 0.1667, "y": 1.249, "curve": [0.192, 1, 0.242, 1, 0.192, 1.249, 0.242, 1]}, {"time": 0.2667, "curve": [0.308, 1, 0.392, 1, 0.308, 1, 0.392, 0.605]}, {"time": 0.4333, "y": 0.605, "curve": [0.45, 1, 0.483, 1, 0.45, 0.605, 0.483, 1.249]}, {"time": 0.5, "y": 1.249, "curve": [0.517, 1, 0.55, 1, 0.517, 1.249, 0.55, 1]}, {"time": 0.5667, "curve": [0.617, 1, 0.717, 1, 0.617, 1, 0.717, 0.605]}, {"time": 0.7667, "y": 0.605, "curve": [0.792, 1, 0.842, 1, 0.792, 0.605, 0.842, 1.249]}, {"time": 0.8667, "y": 1.249, "curve": [0.892, 1, 0.942, 1, 0.892, 1.249, 0.942, 1]}, {"time": 0.9667}]}}}, "dead": {"slots": {"ARM_LEFT": {"attachment": [{}]}, "BACK_BTM": {"attachment": [{}]}, "Brute/GoatCult/Cape_Executioner": {"attachment": [{}]}, "Brute/MonsterHeart": {"attachment": [{"name": "Bru<PERSON>/<PERSON>H<PERSON><PERSON>_glow"}]}, "Eyes": {"attachment": [{}]}, "HAND_RIGHT": {"attachment": [{}]}, "HAND_WEAPON": {"attachment": [{}]}, "HEAD": {"attachment": [{}]}, "LEG_LEFT": {"attachment": [{}]}, "LEG_RIGHT": {"attachment": [{}]}, "PANTS": {"attachment": [{}]}, "ROBES": {"attachment": [{"name": "ROBES_dead"}]}, "SHOULDER_RIGHT": {"attachment": [{}]}, "WEAPON": {"attachment": [{}]}}, "bones": {"MonsterHeart": {"scale": [{"time": 0.6, "curve": [0.608, 1, 0.625, 1.425, 0.608, 1, 0.625, 1.916]}, {"time": 0.6333, "x": 1.425, "y": 1.916, "curve": [0.667, 1.425, 0.733, 1.168, 0.667, 1.916, 0.733, 0.899]}, {"time": 0.7667, "x": 1.168, "y": 0.899, "curve": [0.792, 1.168, 0.842, 1, 0.792, 0.899, 0.842, 1]}, {"time": 0.8667, "curve": [0.875, 1, 0.892, 1.389, 0.875, 1, 0.892, 1.171]}, {"time": 0.9, "x": 1.389, "y": 1.171, "curve": [0.917, 1.389, 0.95, 1, 0.917, 1.171, 0.95, 1]}, {"time": 0.9667}]}}}, "dead-noheart": {"slots": {"ARM_LEFT": {"attachment": [{}]}, "BACK_BTM": {"attachment": [{}]}, "Brute/GoatCult/Cape_Executioner": {"attachment": [{}]}, "Eyes": {"attachment": [{}]}, "HAND_RIGHT": {"attachment": [{}]}, "HAND_WEAPON": {"attachment": [{}]}, "HEAD": {"attachment": [{}]}, "LEG_LEFT": {"attachment": [{}]}, "LEG_RIGHT": {"attachment": [{}]}, "PANTS": {"attachment": [{}]}, "ROBES": {"attachment": [{"name": "ROBES_dead"}]}, "SHOULDER_RIGHT": {"attachment": [{}]}, "WEAPON": {"attachment": [{}]}}, "bones": {"MonsterHeart": {"scale": [{"time": 0.6, "curve": [0.608, 1, 0.625, 1.425, 0.608, 1, 0.625, 1.916]}, {"time": 0.6333, "x": 1.425, "y": 1.916, "curve": [0.667, 1.425, 0.733, 1.168, 0.667, 1.916, 0.733, 0.899]}, {"time": 0.7667, "x": 1.168, "y": 0.899, "curve": [0.792, 1.168, 0.842, 1, 0.792, 0.899, 0.842, 1]}, {"time": 0.8667, "curve": [0.875, 1, 0.892, 1.389, 0.875, 1, 0.892, 1.171]}, {"time": 0.9, "x": 1.389, "y": 1.171, "curve": [0.917, 1.389, 0.95, 1, 0.917, 1.171, 0.95, 1]}, {"time": 0.9667}]}}}, "die": {"slots": {"ARM_LEFT": {"attachment": [{"time": 2.1}]}, "BACK_BTM": {"attachment": [{"time": 2.1}]}, "BloodExplode": {"attachment": [{"time": 2.1, "name": "Brute/blood_explosion0001"}, {"time": 2.1667, "name": "Brute/blood_explosion0002"}, {"time": 2.2333, "name": "Brute/blood_explosion0003"}, {"time": 2.3, "name": "Brute/blood_explosion0004"}, {"time": 2.3667, "name": "Brute/blood_explosion0005"}, {"time": 2.4333, "name": "Brute/blood_explosion0006"}, {"time": 2.5, "name": "Brute/blood_explosion0007"}, {"time": 2.5667, "name": "Brute/blood_explosion0008"}, {"time": 2.6333, "name": "Brute/blood_explosion0009"}, {"time": 2.7, "name": "Brute/blood_explosion0010"}, {"time": 2.7667, "name": "Brute/blood_explosion0011"}, {"time": 2.8333, "name": "Brute/blood_explosion0012"}, {"time": 2.9, "name": "Brute/blood_explosion0013"}, {"time": 2.9667, "name": "Brute/blood_explosion0014"}, {"time": 3.0333, "name": "Brute/blood_explosion0015"}, {"time": 3.1, "name": "Brute/blood_explosion0016"}, {"time": 3.1667, "name": "Brute/blood_explosion0017"}, {"time": 3.2333}]}, "BloodExplode2": {"attachment": [{"time": 2.1667, "name": "Brute/blood_explosion0001"}, {"time": 2.2333, "name": "Brute/blood_explosion0002"}, {"time": 2.3, "name": "Brute/blood_explosion0003"}, {"time": 2.3667, "name": "Brute/blood_explosion0004"}, {"time": 2.4333, "name": "Brute/blood_explosion0005"}, {"time": 2.5, "name": "Brute/blood_explosion0006"}, {"time": 2.5667, "name": "Brute/blood_explosion0007"}, {"time": 2.6333, "name": "Brute/blood_explosion0008"}, {"time": 2.7, "name": "Brute/blood_explosion0009"}, {"time": 2.7667, "name": "Brute/blood_explosion0010"}, {"time": 2.8333, "name": "Brute/blood_explosion0011"}, {"time": 2.9, "name": "Brute/blood_explosion0012"}, {"time": 2.9667, "name": "Brute/blood_explosion0013"}, {"time": 3.0333, "name": "Brute/blood_explosion0014"}, {"time": 3.1, "name": "Brute/blood_explosion0015"}, {"time": 3.1667, "name": "Brute/blood_explosion0016"}, {"time": 3.2333, "name": "Brute/blood_explosion0017"}, {"time": 3.3}]}, "Brute/GoatCult/Cape_Executioner": {"attachment": [{"time": 2.1}]}, "Brute/MonsterHeart": {"rgba": [{"time": 2.1, "color": "ff3a3aff", "curve": [2.25, 1, 2.55, 1, 2.25, 0.23, 2.55, 1, 2.25, 0.23, 2.55, 1, 2.25, 1, 2.55, 1]}, {"time": 2.7, "color": "ffffffff"}], "attachment": [{"time": 2.1, "name": "Bru<PERSON>/<PERSON>H<PERSON><PERSON>_glow"}]}, "Eyes": {"attachment": [{"name": "Eyes_Squint"}, {"time": 2.1}]}, "EYE_LEFT_SHOCKED": {"attachment": [{"name": "EYE_LEFT_SHOCKED"}, {"time": 0.5667}, {"time": 1.3667, "name": "EYE_LEFT_SHOCKED"}, {"time": 1.5667}, {"time": 1.7667, "name": "EYE_LEFT_SHOCKED"}, {"time": 2.1}]}, "EYE_RIGHT_SHOCKED": {"attachment": [{"name": "EYE_RIGHT_SHOCKED"}, {"time": 0.5667}, {"time": 1.3667, "name": "EYE_RIGHT_SHOCKED"}, {"time": 1.5667}, {"time": 1.7667, "name": "EYE_RIGHT_SHOCKED"}, {"time": 2.1}]}, "HAND_RIGHT": {"attachment": [{"time": 2.1}]}, "HAND_WEAPON": {"attachment": [{"time": 2.1}]}, "HEAD": {"attachment": [{"time": 2.1}]}, "LEG_LEFT": {"attachment": [{"time": 2.1}]}, "LEG_RIGHT": {"attachment": [{"time": 2.1}]}, "PANTS": {"attachment": [{"time": 2.1}]}, "ROBES": {"rgba": [{"time": 2.1, "color": "ff3b3bff", "curve": [2.25, 1, 2.55, 1, 2.25, 0.23, 2.55, 1, 2.25, 0.23, 2.55, 1, 2.25, 1, 2.55, 1]}, {"time": 2.7, "color": "ffffffff"}], "attachment": [{"time": 2.1, "name": "ROBES_dead"}]}, "SHOULDER_RIGHT": {"attachment": [{"time": 2.1}]}, "WEAPON": {"attachment": [{"time": 2.1}]}}, "bones": {"SHOULDER_LEFT": {"rotate": [{"time": 2.1, "value": -35.15}]}, "BACK_BTM": {"rotate": [{"value": -13.14, "curve": [0.039, -18.74, 0.2, -21.37]}, {"time": 0.2667, "value": -21.37, "curve": [0.5, -21.37, 0.967, 18.91]}, {"time": 1.2, "value": 18.91, "curve": "stepped"}, {"time": 2, "value": 18.91, "curve": [2.052, 18.91, 2.081, 15.92]}, {"time": 2.1, "value": 12.76, "curve": [2.118, -13.27, 2.275, -25.81]}, {"time": 2.3333, "value": -25.81, "curve": [2.442, -25.81, 2.658, 20.44]}, {"time": 2.7667, "value": 20.44, "curve": [2.892, 20.44, 3.142, 2.83]}, {"time": 3.2667, "value": 2.83}]}, "BloodExplode2": {"translate": [{"time": 2.1667, "x": 101.98, "y": 10}]}, "BACK_TOP": {"rotate": [{"value": 12.81, "curve": [0.039, -10.98, 0.2, -22.14]}, {"time": 0.2667, "value": -22.14, "curve": [0.5, -22.14, 1.179, -10.36]}, {"time": 1.2, "value": -5.23, "curve": [1.237, 5.3, 1.859, 13.64]}, {"time": 2, "value": 13.91, "curve": [2.052, 13.91, 2.081, 13.37]}, {"time": 2.1, "value": 12.81, "curve": [2.134, -7.62, 2.275, -17.2]}, {"time": 2.3333, "value": -17.2, "curve": [2.442, -17.2, 2.658, 26.18]}, {"time": 2.7667, "value": 26.18, "curve": [2.892, 26.18, 3.142, 12.81]}, {"time": 3.2667, "value": 12.81}]}, "MonsterHeart": {"scale": [{"time": 2.1, "curve": [2.128, 1.265, 2.2, 1.268, 2.128, 1.827, 2.2, 1.836]}, {"time": 2.2333, "x": 1.268, "y": 1.836, "curve": [2.292, 1.268, 2.408, 0.918, 2.292, 1.836, 2.408, 0.918]}, {"time": 2.4667, "x": 0.918, "y": 0.918, "curve": [2.542, 0.918, 2.692, 1, 2.542, 0.918, 2.692, 1]}, {"time": 2.7667, "curve": "stepped"}, {"time": 2.8333, "curve": [2.842, 1, 2.858, 1.425, 2.842, 1, 2.858, 1.916]}, {"time": 2.8667, "x": 1.425, "y": 1.916, "curve": [2.9, 1.425, 2.967, 1.168, 2.9, 1.916, 2.967, 0.899]}, {"time": 3, "x": 1.168, "y": 0.899, "curve": [3.025, 1.168, 3.075, 1, 3.025, 0.899, 3.075, 1]}, {"time": 3.1, "curve": [3.108, 1, 3.125, 1.389, 3.108, 1, 3.125, 1.171]}, {"time": 3.1333, "x": 1.389, "y": 1.171, "curve": [3.15, 1.389, 3.183, 1, 3.15, 1.171, 3.183, 1]}, {"time": 3.2}]}, "HAND_RIGHT": {"rotate": [{"value": -21.28}]}, "WEAPON_CONTROLLER": {"rotate": [{"time": 2.1, "value": 21.49}]}, "HEAD": {"rotate": [{"curve": [0.12, 0, 0.269, -11.58]}, {"time": 0.4333, "value": -28.96, "curve": [0.625, -28.96, 1.183, 3.04]}, {"time": 1.2, "value": 16.99, "curve": [1.237, 20.5, 1.859, 23.28]}, {"time": 2, "value": 23.37, "curve": [2.026, 23.37, 2.062, 12.63]}, {"time": 2.1}]}, "LEG_LEFT": {"rotate": [{"time": 2.1, "value": -23.28}]}, "LEG_RIGHT": {"rotate": [{"time": 2.1, "value": -7.76}]}, "SHOULDER_RIGHT": {"rotate": [{"time": 2.1, "value": 2.67}], "translate": [{"time": 1.2, "curve": [1.267, 0, 1.4, -1.09, 1.267, 0, 1.4, 42.5]}, {"time": 1.4667, "x": -1.09, "y": 42.5}]}, "HIP": {"rotate": [{"value": -6.24, "curve": [0.042, -6.24, 0.125, -16.22]}, {"time": 0.1667, "value": -16.22, "curve": [0.425, -16.22, 0.942, 1.86]}, {"time": 1.2, "value": 1.86, "curve": "stepped"}, {"time": 2, "value": 1.86, "curve": [2.052, 1.86, 2.081, -2.08]}, {"time": 2.1, "value": -6.24}], "translate": [{"y": -1.68, "curve": [0.042, 0, 0.125, 35.25, 0.042, -1.68, 0.125, 7.46]}, {"time": 0.1667, "x": 35.25, "y": 7.46, "curve": [0.425, 35.25, 0.942, -16.97, 0.425, 7.46, 0.942, -13.43]}, {"time": 1.2, "x": -16.97, "y": -13.43, "curve": "stepped"}, {"time": 2, "x": -16.97, "y": -13.43, "curve": [2.052, -16.97, 2.081, -8.7, 2.052, -13.43, 2.081, -7.71]}, {"time": 2.1, "y": -1.68}], "scale": [{"time": 1.2, "curve": [1.217, 1, 1.25, 1.041, 1.217, 1, 1.25, 0.959]}, {"time": 1.2667, "x": 1.041, "y": 0.959, "curve": [1.283, 1.041, 1.317, 1, 1.283, 0.959, 1.317, 1]}, {"time": 1.3333, "curve": [1.35, 1, 1.383, 1.041, 1.35, 1, 1.383, 0.959]}, {"time": 1.4, "x": 1.041, "y": 0.959, "curve": [1.417, 1.041, 1.45, 1, 1.417, 0.959, 1.45, 1]}, {"time": 1.4667, "curve": [1.483, 1, 1.517, 1.041, 1.483, 1, 1.517, 0.959]}, {"time": 1.5333, "x": 1.041, "y": 0.959, "curve": [1.55, 1.041, 1.583, 1, 1.55, 0.959, 1.583, 1]}, {"time": 1.6, "curve": [1.617, 1, 1.65, 1.041, 1.617, 1, 1.65, 0.959]}, {"time": 1.6667, "x": 1.041, "y": 0.959, "curve": [1.683, 1.041, 1.717, 1, 1.683, 0.959, 1.717, 1]}, {"time": 1.7333, "curve": [1.75, 1, 1.783, 1.041, 1.75, 1, 1.783, 0.959]}, {"time": 1.8, "x": 1.041, "y": 0.959, "curve": [1.817, 1.041, 1.85, 1, 1.817, 0.959, 1.85, 1]}, {"time": 1.8667, "curve": [1.883, 1, 1.917, 1.041, 1.883, 1, 1.917, 0.959]}, {"time": 1.9333, "x": 1.041, "y": 0.959, "curve": [1.95, 1.041, 1.983, 1, 1.95, 0.959, 1.983, 1]}, {"time": 2, "curve": "stepped"}, {"time": 2.1, "curve": [2.142, 1, 2.225, 0.857, 2.142, 1, 2.225, 1.424]}, {"time": 2.2667, "x": 0.857, "y": 1.424, "curve": [2.358, 0.857, 2.542, 1.23, 2.358, 1.424, 2.542, 0.803]}, {"time": 2.6333, "x": 1.23, "y": 0.803, "curve": [2.717, 1.23, 2.883, 1, 2.717, 0.803, 2.883, 1]}, {"time": 2.9667}]}, "HAND_RIGHT_CONSTRAINT": {"translate": [{"x": 10.86, "y": -31.7, "curve": [0.127, 82.92, 0.247, 142.97, 0.127, -109.52, 0.247, -174.37]}, {"time": 0.3333, "x": 142.97, "y": -174.37, "curve": [0.55, 142.97, 0.983, -3.79, 0.55, -174.37, 0.983, -7.04]}, {"time": 1.2, "x": -3.79, "y": -7.04, "curve": [1.304, -3.79, 1.391, 12.54, 1.304, -7.04, 1.391, 28.7]}, {"time": 1.4667, "x": 40.06, "y": 88.9, "curve": "stepped"}, {"time": 2, "x": 40.06, "y": 88.9, "curve": [2.046, 33.83, 2.077, 22.54, 2.046, 63.17, 2.077, 16.54]}, {"time": 2.1, "x": 10.86, "y": -31.7}]}, "WEAPON_CONSTRAINT": {"translate": [{"x": -1.74, "y": -2.65, "curve": [0.115, 49.59, 0.227, 92.11, 0.115, -61.71, 0.227, -110.64]}, {"time": 0.3333, "x": 124.25, "y": -147.62, "curve": [0.55, 124.25, 0.983, 12.78, 0.55, -147.62, 0.983, 22.78]}, {"time": 1.2, "x": 12.78, "y": 22.78, "curve": "stepped"}, {"time": 2, "x": 12.78, "y": 22.78, "curve": [2.041, 12.78, 2.074, 20.98, 2.041, 22.78, 2.074, 40.69]}, {"time": 2.1, "x": 34.15, "y": 69.47}]}, "HAND_LEFT_HANDLE": {"translate": [{"x": 5.8, "y": 10.41, "curve": [0.127, 86.29, 0.247, 153.36, 0.127, -62.09, 0.247, -122.49]}, {"time": 0.3333, "x": 153.36, "y": -122.49, "curve": [0.55, 153.36, 0.983, -107.96, 0.55, -122.49, 0.983, 11.48]}, {"time": 1.2, "x": -107.96, "y": 11.48, "curve": "stepped"}, {"time": 2, "x": -107.96, "y": 11.48, "curve": [2.041, -107.96, 2.074, -102.85, 2.041, 11.48, 2.074, 11.43]}, {"time": 2.1, "x": -94.65, "y": 11.35}]}, "FOOT_LEFT_CONSTRAINT": {"translate": [{"x": -20.89, "y": 13.06, "curve": [0.03, -25.78, 0.25, -31.33, 0.03, 34.43, 0.25, 58.75]}, {"time": 0.3333, "x": -31.33, "y": 58.75, "curve": [0.55, -31.33, 0.983, 0, 0.55, 58.75, 0.983, 0]}, {"time": 1.2, "curve": "stepped"}, {"time": 2, "curve": [2.052, 0, 2.081, -10.18, 2.052, 0, 2.081, 6.36]}, {"time": 2.1, "x": -20.89, "y": 13.06}]}, "ANKLE_LEFT_CONSTRAINT": {"rotate": [{"value": 2.33}], "translate": [{"curve": [0.015, -1.83, 0.125, -3.92, 0.015, -6.72, 0.125, -14.36]}, {"time": 0.1667, "x": -3.92, "y": -14.36, "curve": [0.425, -3.92, 0.942, 0, 0.425, -14.36, 0.942, 0]}, {"time": 1.2}]}, "ARM_RIGHT": {"rotate": [{"time": 2.1, "value": -4.79}]}, "ARM_LEFT": {"rotate": [{"time": 2.1, "value": -76.96}]}, "LEG_RIGHT_BTM": {"rotate": [{"time": 2.1, "value": 38.31}]}, "FOOT_RIGHT": {"rotate": [{"time": 2.1, "value": -32.29}]}, "LEG_LEFT_BTM": {"rotate": [{"time": 2.1, "value": 63.36}]}, "FOOT_LEFT": {"rotate": [{"time": 2.1, "value": -39.29}]}}}, "execute": {"slots": {"Eyes": {"attachment": [{"time": 2.3667, "name": "Eyes_Squint"}, {"time": 3.2667, "name": "Eyes"}]}}, "bones": {"MASK": {"rotate": [{"time": 2.8, "curve": [3.075, 0, 3.625, -1.43]}, {"time": 3.9, "value": -1.43}]}, "HIP": {"rotate": [{"time": 0.4333, "curve": [0.642, 0, 0.819, -14.51]}, {"time": 0.8333, "value": -18.73, "curve": [1.233, -18.73, 1.833, -17.91]}, {"time": 2.4333, "value": -17.09, "curve": [2.489, -17.09, 2.565, -10.8]}, {"time": 2.6, "curve": "stepped"}, {"time": 2.9333, "curve": [3.208, 0, 3.758, -1.43]}, {"time": 4.0333, "value": -1.43}], "translate": [{"y": -1.68, "curve": [0.108, 0, 0.325, 0, 0.108, -1.68, 0.325, -24.41]}, {"time": 0.4333, "y": -24.41, "curve": [0.642, 0, 0.819, 22.99, 0.642, -24.41, 0.819, 5.02]}, {"time": 0.8333, "x": 29.68, "y": 13.58, "curve": [1.233, 29.68, 1.833, 31.63, 1.233, 13.58, 1.833, 12.8]}, {"time": 2.4333, "x": 33.59, "y": 12.02, "curve": [2.489, 33.59, 2.565, 13.19, 2.489, 12.02, 2.565, 0.42]}, {"time": 2.6, "x": -21.83, "y": -19.5, "curve": [2.655, -21.83, 2.85, -21.83, 2.655, -24.07, 2.85, -27.6]}, {"time": 2.9333, "x": -21.83, "y": -27.6, "curve": [3.117, -21.83, 3.483, 0.12, 3.117, -27.6, 3.483, -9.39]}, {"time": 3.6667, "x": 0.12, "y": -9.39, "curve": [3.692, 0.12, 3.742, 2.34, 3.692, -9.39, 3.742, -29.48]}, {"time": 3.7667, "x": 2.34, "y": -29.48, "curve": [3.833, 2.34, 3.967, 0, 3.833, -29.48, 3.967, -1.68]}, {"time": 4.0333, "y": -1.68}]}, "HAND_RIGHT_CONSTRAINT": {"rotate": [{"time": 3.1, "curve": [3.333, 0, 3.8, -1.43]}, {"time": 4.0333, "value": -1.43}], "translate": [{"x": 2.18, "y": -4.38, "curve": [0.108, 2.18, 0.325, 81.16, 0.108, -4.38, 0.325, 104.38]}, {"time": 0.4333, "x": 81.16, "y": 104.38, "curve": [0.642, 81.16, 0.819, 303.45, 0.642, 104.38, 0.819, 124.43]}, {"time": 0.8333, "x": 368.11, "y": 130.26, "curve": [1.002, 392.87, 2.033, 398.23, 1.002, 133.22, 2.033, 133.86]}, {"time": 2.4333, "x": 398.23, "y": 133.86, "curve": [2.454, 398.23, 2.477, 363.89, 2.454, 133.86, 2.477, 167.83]}, {"time": 2.5, "x": 299.11, "y": 231.9, "curve": [2.511, 270.48, 2.523, 235.13, 2.511, 245.77, 2.523, 262.9]}, {"time": 2.5333, "x": 193.49, "y": 283.08, "curve": [2.56, 153.18, 2.584, 100.7, 2.56, 255.81, 2.584, 220.3]}, {"time": 2.6, "x": 37.69, "y": 177.66, "curve": [2.621, 40.54, 2.643, 43.28, 2.621, 183.34, 2.643, 188.8]}, {"time": 2.6667, "x": 45.9, "y": 194.03, "curve": "stepped"}, {"time": 3.1, "x": 45.9, "y": 194.03, "curve": [3.333, 45.9, 3.8, 2.18, 3.333, 194.03, 3.8, -4.38]}, {"time": 4.0333, "x": 2.18, "y": -4.38}]}, "HAND_RIGHT": {"rotate": [{"value": -21.28, "curve": "stepped"}, {"time": 2.5667, "value": -21.28, "curve": [2.578, -21.27, 2.589, -21.26]}, {"time": 2.6, "value": -21.25, "curve": [2.634, -23.36, 2.668, -26.07]}, {"time": 2.7, "value": -29.35, "curve": "stepped"}, {"time": 2.9333, "value": -29.35, "curve": [3.375, -27.75, 3.774, -25.5]}, {"time": 4.0333, "value": -22.71}]}, "HEAD": {"rotate": [{"curve": [0.105, 0, 0.291, 19.61]}, {"time": 0.4333, "value": 28.66, "curve": [0.484, 32.06, 0.529, 34.27]}, {"time": 0.5667, "value": 34.27, "curve": [0.7, 34.27, 0.967, 9.16]}, {"time": 1.1, "value": 9.16, "curve": [1.196, 3.34, 2.1, 1.22]}, {"time": 2.4333, "value": 1.22, "curve": [2.489, 1.22, 2.565, -1.92]}, {"time": 2.6, "value": -7.29, "curve": [2.618, 16.05, 2.85, 21.37]}, {"time": 2.9333, "value": 21.37, "curve": [3.075, 21.37, 3.358, -12.6]}, {"time": 3.5, "value": -12.6, "curve": [3.862, -8.75, 3.876, 5.87]}, {"time": 4.0333, "value": 5.87}], "translate": [{"time": 0.5667, "curve": [0.739, 4.5, 0.921, 9.76, 0.739, -1.45, 0.921, -3.15]}, {"time": 1.1, "x": 14.82, "y": -4.78, "curve": "stepped"}, {"time": 2.4333, "x": 14.82, "y": -4.78, "curve": [2.489, 14.82, 2.565, 9.37, 2.489, -4.78, 2.565, -3.02]}, {"time": 2.6, "curve": [2.617, 0, 2.65, -24.71, 2.617, 0, 2.65, 14.7]}, {"time": 2.6667, "x": -24.71, "y": 14.7, "curve": [2.733, -24.71, 2.867, -22.57, 2.733, 14.7, 2.867, 40.54]}, {"time": 2.9333, "x": -22.57, "y": 40.54, "curve": [3.208, -22.57, 3.758, 0, 3.208, 40.54, 3.758, 0]}, {"time": 4.0333}]}, "WEAPON_CONSTRAINT": {"rotate": [{"time": 2.9333, "curve": [3.208, 0, 3.758, -1.43]}, {"time": 4.0333, "value": -1.43}], "translate": [{"x": -1.74, "y": -2.65, "curve": [0.058, -1.74, 0.175, -23.95, 0.058, -2.65, 0.175, 20.02]}, {"time": 0.2333, "x": -23.95, "y": 20.02, "curve": "stepped"}, {"time": 0.4333, "x": -23.95, "y": 20.02, "curve": [0.642, -23.95, 0.819, 127.96, 0.642, 20.02, 0.819, -191.55]}, {"time": 0.8333, "x": 172.15, "y": -253.1, "curve": [0.844, 168.66, 0.908, 167.9, 0.844, -253.71, 0.908, -253.84]}, {"time": 0.9333, "x": 167.9, "y": -253.84, "curve": [0.958, 167.9, 1.008, 172.15, 0.958, -253.84, 1.008, -253.1]}, {"time": 1.0333, "x": 172.15, "y": -253.1, "curve": [1.079, 170.6, 1.125, 169.06, 1.079, -253.37, 1.125, -253.64]}, {"time": 1.1667, "x": 167.9, "y": -253.84, "curve": [1.205, 170.45, 1.239, 172.15, 1.205, -253.39, 1.239, -253.1]}, {"time": 1.2667, "x": 172.15, "y": -253.1, "curve": [1.301, 171.11, 1.335, 170.07, 1.301, -253.21, 1.335, -253.33]}, {"time": 1.3667, "x": 169.29, "y": -253.42, "curve": [1.405, 171, 1.439, 172.15, 1.405, -253.23, 1.439, -253.1]}, {"time": 1.4667, "x": 172.15, "y": -253.1, "curve": [1.501, 170.1, 1.535, 168.06, 1.501, -253.52, 1.535, -253.95]}, {"time": 1.5667, "x": 166.52, "y": -254.27, "curve": [1.617, 169.9, 1.663, 172.15, 1.617, -253.57, 1.663, -253.1]}, {"time": 1.7, "x": 172.15, "y": -253.1, "curve": [1.734, 170.6, 1.769, 169.06, 1.734, -253.35, 1.769, -253.61]}, {"time": 1.8, "x": 167.9, "y": -253.81, "curve": [1.838, 170.45, 1.872, 172.15, 1.838, -253.38, 1.872, -253.1]}, {"time": 1.9, "x": 172.15, "y": -253.1, "curve": [1.923, 169.46, 1.946, 166.77, 1.923, -253.24, 1.946, -253.38]}, {"time": 1.9667, "x": 164.76, "y": -253.48, "curve": [2.005, 169.19, 2.039, 172.15, 2.005, -253.25, 2.039, -253.1]}, {"time": 2.0667, "x": 172.15, "y": -253.1, "curve": [2.101, 168.17, 2.135, 164.19, 2.101, -252.69, 2.135, -252.28]}, {"time": 2.1667, "x": 161.2, "y": -251.97, "curve": [2.205, 167.77, 2.239, 172.15, 2.205, -252.65, 2.239, -253.1]}, {"time": 2.2667, "x": 172.15, "y": -253.1, "curve": [2.301, 169.46, 2.335, 166.77, 2.301, -253.24, 2.335, -253.38]}, {"time": 2.3667, "x": 164.76, "y": -253.48, "curve": [2.392, 169.19, 2.415, 172.15, 2.392, -253.25, 2.415, -253.1]}, {"time": 2.4333, "x": 172.15, "y": -253.1, "curve": [2.464, 172.15, 2.5, 182.39, 2.464, -253.1, 2.5, -21.23]}, {"time": 2.5333, "x": 201.43, "y": 409.66, "curve": [2.545, 105.76, 2.557, -12.01, 2.545, 390.86, 2.557, 367.71]}, {"time": 2.5667, "x": -150.05, "y": 340.58, "curve": [2.58, -177.49, 2.591, -208.96, 2.58, 274.66, 2.591, 199.07]}, {"time": 2.6, "x": -244.1, "y": 114.66, "curve": "stepped"}, {"time": 2.9333, "x": -244.1, "y": 114.66, "curve": [3.042, -244.1, 3.258, -29.17, 3.042, 114.66, 3.258, 138.45]}, {"time": 3.3667, "x": -29.17, "y": 138.45, "curve": [3.612, -7.17, 3.866, -1.74, 3.612, 25.29, 3.866, -2.65]}, {"time": 4.0333, "x": -1.74, "y": -2.65}]}, "HAND_LEFT_HANDLE": {"rotate": [{"time": 2.9333, "curve": [3.208, 0, 3.758, -1.43]}, {"time": 4.0333, "value": -1.43}], "translate": [{"x": 5.8, "y": 10.41, "curve": [0.108, 5.8, 0.325, -63.45, 0.108, 10.41, 0.325, 10.34]}, {"time": 0.4333, "x": -63.45, "y": 10.34, "curve": [0.642, -63.45, 0.819, 119.05, 0.642, 10.34, 0.819, -103.13]}, {"time": 0.8333, "x": 172.14, "y": -136.13, "curve": [1.002, 169.98, 2.033, 169.51, 1.002, -163.66, 2.033, -169.62]}, {"time": 2.4333, "x": 169.51, "y": -169.62, "curve": [2.464, 169.51, 2.5, 104.41, 2.464, -169.62, 2.5, -77.87]}, {"time": 2.5333, "x": -16.57, "y": 92.63}, {"time": 2.6, "x": -220.18, "y": -111.23, "curve": "stepped"}, {"time": 2.9333, "x": -220.18, "y": -111.23, "curve": [3.042, -220.18, 3.258, -129.13, 3.042, -111.23, 3.258, 57.3]}, {"time": 3.3667, "x": -129.13, "y": 57.3, "curve": [3.577, -70.33, 3.805, -26.54, 3.577, 36.86, 3.805, 21.65]}, {"time": 4.0333, "x": 5.8, "y": 10.41}]}, "BACK_BTM": {"rotate": [{"curve": [0.067, 6.36, 0.175, 7.54]}, {"time": 0.2333, "value": 7.54, "curve": "stepped"}, {"time": 0.4333, "value": 7.54, "curve": [0.998, 0.2, 1.933, -3.61]}, {"time": 2.4333, "value": -3.61, "curve": [2.489, -3.61, 2.565, -2.28]}, {"time": 2.6, "curve": "stepped"}, {"time": 2.9333, "curve": [3.208, 0, 3.758, -1.43]}, {"time": 4.0333, "value": -1.43}]}, "BACK_TOP": {"rotate": [{"curve": [0.067, 13.8, 0.175, 16.36]}, {"time": 0.2333, "value": 16.36, "curve": [0.338, 16.36, 0.383, 10.33]}, {"time": 0.4333, "value": 10.33, "curve": [0.642, 10.33, 0.819, -8.17]}, {"time": 0.8333, "value": -13.55, "curve": [1.161, -15.93, 2.033, -16.6]}, {"time": 2.4333, "value": -16.6, "curve": [2.489, -16.6, 2.565, -2.91]}, {"time": 2.6, "value": 20.58, "curve": "stepped"}, {"time": 2.9333, "value": 20.58, "curve": [3.092, 20.58, 3.408, -1.43]}, {"time": 3.5667, "value": -1.43}]}, "FOOT_LEFT_CONSTRAINT": {"rotate": [{"time": 3.6, "curve": [3.708, 0, 3.925, -1.43]}, {"time": 4.0333, "value": -1.43}], "translate": [{"time": 0.6667, "curve": [0.692, 0, 0.729, -1.19, 0.692, 0, 0.729, 14.12]}, {"time": 0.7667, "x": -2.38, "y": 28.25, "curve": [0.792, -3.56, 0.817, -4.75, 0.792, 39.22, 0.817, 50.19]}, {"time": 0.8333, "x": -4.75, "y": 50.19, "curve": [0.931, -9.1, 1.908, -10.72, 0.931, 65.41, 1.908, 71.07]}, {"time": 2.2667, "x": -10.72, "y": 71.07, "curve": [2.391, -18.92, 2.517, -27.42, 2.391, 34.88, 2.517, -2.61]}, {"time": 2.6, "x": -27.42, "y": -2.61, "curve": "stepped"}, {"time": 3.4667, "x": -27.42, "y": -2.61, "curve": [3.502, -27.42, 3.549, -23.14, 3.502, -2.61, 3.549, 5.65]}, {"time": 3.6, "x": -17.22, "y": 17.09, "curve": [3.658, -10.66, 3.726, 0, 3.658, 10.58, 3.726, 0]}, {"time": 3.7667}]}, "LEG_LEFT": {"rotate": [{"time": 0.4333, "value": -41.97, "curve": [0.892, -41.97, 1.808, -57.05]}, {"time": 2.2667, "value": -57.05, "curve": [2.433, -57.05, 2.767, -42.29]}, {"time": 2.9333, "value": -42.29}], "translate": [{"curve": [0.127, 0, 0.273, -0.55, 0.127, 0, 0.273, 0.76]}, {"time": 0.4333, "x": -1.48, "y": 2.05, "curve": [0.969, -3.86, 1.673, -9.82, 0.969, 5.34, 1.673, 13.6]}, {"time": 2.2667, "x": -13.42, "y": 18.58, "curve": [2.394, -5.47, 2.509, 0, 2.394, 7.57, 2.509, 0]}, {"time": 2.6}]}, "root": {"rotate": [{"time": 2.9333, "curve": [3.208, 0, 3.758, -1.43]}, {"time": 4.0333, "value": -1.43}], "scale": [{"time": 0.0667, "curve": [0.092, 1, 0.142, 1.095, 0.092, 1, 0.142, 0.919]}, {"time": 0.1667, "x": 1.095, "y": 0.919, "curve": [0.208, 1.095, 0.292, 1.011, 0.208, 0.919, 0.292, 0.984]}, {"time": 0.3333, "x": 1.011, "y": 0.984, "curve": [0.358, 1.011, 0.4, 1.035, 0.358, 0.984, 0.4, 0.956]}, {"time": 0.4333, "x": 1.046, "y": 0.943, "curve": [0.459, 1.051, 0.482, 1.054, 0.459, 0.937, 0.482, 0.933]}, {"time": 0.5, "x": 1.054, "y": 0.933, "curve": "stepped"}, {"time": 0.6667, "x": 1.054, "y": 0.933, "curve": [0.708, 1.054, 0.792, 0.997, 0.708, 0.933, 0.792, 1.007]}, {"time": 0.8333, "x": 0.997, "y": 1.007, "curve": [0.85, 0.997, 0.883, 1.14, 0.85, 1.007, 0.883, 0.871]}, {"time": 0.9, "x": 1.14, "y": 0.871, "curve": [0.975, 1.14, 1.125, 1.054, 0.975, 0.871, 1.125, 0.933]}, {"time": 1.2, "x": 1.054, "y": 0.933, "curve": "stepped"}, {"time": 2.2667, "x": 1.054, "y": 0.933, "curve": [2.35, 1.054, 2.517, 1.175, 2.35, 0.933, 2.517, 0.777]}, {"time": 2.6, "x": 1.175, "y": 0.777, "curve": [2.633, 1.175, 2.7, 0.873, 2.633, 0.777, 2.7, 1.163]}, {"time": 2.7333, "x": 0.873, "y": 1.163, "curve": [2.783, 0.873, 2.883, 1, 2.783, 1.163, 2.883, 1]}, {"time": 2.9333}]}, "ANKLE_LEFT_CONSTRAINT": {"rotate": [{"time": 2.9333, "curve": [3.082, 0, 3.311, -0.42]}, {"time": 3.5333, "value": -0.8, "curve": [3.723, -1.13, 3.907, -1.43]}, {"time": 4.0333, "value": -1.43}], "translate": [{"time": 0.6667, "curve": [0.692, 0, 0.729, 0.52, 0.692, 0, 0.729, 5.48]}, {"time": 0.7667, "x": 1.05, "y": 10.97, "curve": [0.792, 2.88, 0.817, 4.71, 0.792, 5.48, 0.817, 0]}, {"time": 0.8333, "x": 4.71, "curve": "stepped"}, {"time": 2.2667, "x": 4.71, "curve": [2.433, 4.71, 2.767, 0, 2.433, 0, 2.767, 0]}, {"time": 2.9333}]}, "Holder": {"rotate": [{"time": 2.9333, "curve": [3.208, 0, 3.758, -1.43]}, {"time": 4.0333, "value": -1.43}], "translate": [{"time": 2.6333, "curve": [2.65, 0, 2.683, 17.44, 2.65, 0, 2.683, 0]}, {"time": 2.7, "x": 17.44, "curve": [2.717, 17.44, 2.75, 0, 2.717, 0, 2.75, 0]}, {"time": 2.7667, "curve": [2.775, 0, 2.792, 17.44, 2.775, 0, 2.792, 0]}, {"time": 2.8, "x": 17.44, "curve": [2.808, 17.44, 2.825, 0, 2.808, 0, 2.825, 0]}, {"time": 2.8333}]}, "FACE": {"rotate": [{"time": 2.9333, "curve": [3.208, 0, 3.758, -1.43]}, {"time": 4.0333, "value": -1.43}]}, "SHOULDER_RIGHT": {"rotate": [{"time": 0.4333, "value": -18.43, "curve": [1.1, -18.43, 2.433, 81.78]}, {"time": 3.1, "value": 81.78}], "translate": [{}, {"time": 0.3333, "x": -28.9, "y": -6.26}, {"time": 0.4333, "x": -25.68, "y": -21.65, "curve": [0.533, -25.68, 0.733, 49.26, 0.533, -21.65, 0.733, 18.72]}, {"time": 0.8333, "x": 49.26, "y": 18.72, "curve": "stepped"}, {"time": 2.4333, "x": 49.26, "y": 18.72, "curve": [2.465, 49.26, 2.499, 42.74, 2.465, 18.72, 2.499, 38.52]}, {"time": 2.5333, "x": 30.06, "y": 77.01, "curve": [2.555, 12.38, 2.578, -11.02, 2.555, 60.35, 2.578, 38.29]}, {"time": 2.6, "x": -39.92, "y": 11.06, "curve": "stepped"}, {"time": 3.1, "x": -39.92, "y": 11.06, "curve": [3.465, -32.86, 3.828, -19.08, 3.465, 9.1, 3.828, 5.29]}, {"time": 4.0333}], "scale": [{"time": 2.4333, "curve": [2.467, 1.053, 2.501, 1.102, 2.467, 1, 2.501, 1]}, {"time": 2.5333, "x": 1.146, "curve": [2.558, 1.167, 2.581, 1.18, 2.558, 1, 2.581, 1]}, {"time": 2.6, "x": 1.18, "curve": "stepped"}, {"time": 3.1, "x": 1.18, "curve": [3.417, 1.115, 3.729, 1.055, 3.417, 1, 3.729, 1]}, {"time": 4.0333}]}, "ARM_RIGHT": {"rotate": [{"time": 0.4333, "value": -30.43, "curve": [1.058, -30.43, 2.308, -11.81]}, {"time": 2.9333, "value": -11.81}]}, "SHOULDER_LEFT": {"rotate": [{"time": 0.4333, "value": -53.46, "curve": [0.975, -53.46, 2.058, 13.75]}, {"time": 2.6, "value": 13.75, "curve": [2.625, 13.75, 2.675, -24.57]}, {"time": 2.7, "value": -24.57, "curve": "stepped"}, {"time": 3.2333, "value": -24.57, "curve": [3.308, -24.57, 3.458, -133.34]}, {"time": 3.5333, "value": -133.34, "curve": [3.575, -133.34, 3.658, -83.05]}, {"time": 3.7, "value": -83.05}], "translate": [{"time": 2.6, "curve": [2.635, -15.38, 2.668, -23.43, 2.635, -41.21, 2.668, -62.77]}, {"time": 2.7, "x": -23.43, "y": -62.77}]}, "ARM_LEFT": {"rotate": [{"time": 0.4333, "value": -84.78, "curve": [0.975, -84.78, 2.058, -15.02]}, {"time": 2.6, "value": -15.02, "curve": [2.625, -15.02, 2.675, -24.19]}, {"time": 2.7, "value": -24.19, "curve": "stepped"}, {"time": 3.2333, "value": -24.19, "curve": [3.308, -24.19, 3.458, -21.93]}, {"time": 3.5333, "value": -21.93, "curve": [3.575, -21.93, 3.658, -63.49]}, {"time": 3.7, "value": -63.49, "curve": [3.742, -63.49, 3.825, -58.71]}, {"time": 3.8667, "value": -58.71}]}, "WEAPON_CONTROLLER": {"rotate": [{"time": 0.4333, "value": 37.27, "curve": [0.975, 37.27, 2.058, 23.42]}, {"time": 2.6, "value": 23.42, "curve": [2.625, 23.42, 2.675, 69.05]}, {"time": 2.7, "value": 69.05, "curve": [2.708, 69.05, 2.725, 77.82]}, {"time": 2.7333, "value": 77.82, "curve": [2.75, 77.82, 2.783, 65.93]}, {"time": 2.8, "value": 65.93, "curve": [2.825, 65.93, 2.875, 71.21]}, {"time": 2.9, "value": 71.21, "curve": [2.983, 71.21, 3.15, 75.57]}, {"time": 3.2333, "value": 75.57, "curve": [3.333, 75.57, 3.533, 22.28]}, {"time": 3.6333, "value": 22.28, "curve": [3.65, 22.28, 3.683, 18.92]}, {"time": 3.7, "value": 18.92, "curve": [3.742, 18.92, 3.825, 22.28]}, {"time": 3.8667, "value": 22.28}]}, "LEG_RIGHT": {"rotate": [{"time": 0.4333, "value": -35.9, "curve": [1.058, -35.9, 2.308, -26.15]}, {"time": 2.9333, "value": -26.15}]}, "LEG_RIGHT_BTM": {"rotate": [{"time": 0.4333, "value": 85.6, "curve": [1.058, 85.6, 2.308, 57.52]}, {"time": 2.9333, "value": 57.52}]}, "FOOT_RIGHT": {"rotate": [{"time": 0.4333, "value": -50.43, "curve": [1.058, -50.43, 2.308, -32.86]}, {"time": 2.9333, "value": -32.86}]}, "LEG_LEFT_BTM": {"rotate": [{"time": 0.4333, "value": 87.63, "curve": [1.058, 87.63, 2.308, 38.18]}, {"time": 2.9333, "value": 38.18}]}, "FOOT_LEFT": {"rotate": [{"time": 0.4333, "value": -44.55, "curve": [1.058, -44.55, 2.308, 4.81]}, {"time": 2.9333, "value": 4.81}]}, "FOOT_RIGHT_CONSTRAINT": {"rotate": [{"time": 2.9333, "curve": [3.208, 0, 3.758, -1.43]}, {"time": 4.0333, "value": -1.43}]}, "ANKLE_RIGHT_CONSTRAINT": {"rotate": [{"time": 2.9333, "curve": [3.208, 0, 3.758, -1.43]}, {"time": 4.0333, "value": -1.43}]}, "HORN_RIGHT": {"rotate": [{"time": 2.9333, "curve": [3.208, 0, 3.758, -1.43]}, {"time": 4.0333, "value": -1.43}]}, "HORN_LEFT": {"rotate": [{"time": 2.9333, "curve": [3.208, 0, 3.758, -1.43]}, {"time": 4.0333, "value": -1.43}]}, "EYE_LEFT": {"rotate": [{"time": 2.9333, "curve": [3.208, 0, 3.758, -1.43]}, {"time": 4.0333, "value": -1.43}]}, "EYE_RIGHT": {"rotate": [{"time": 2.9333, "curve": [3.208, 0, 3.758, -1.43]}, {"time": 4.0333, "value": -1.43}]}, "BACK_TOP2": {"translate": [{"curve": [0.108, 0, 0.325, 53.16, 0.108, 0, 0.325, 23.72]}, {"time": 0.4333, "x": 53.16, "y": 23.72, "curve": [0.533, 53.16, 0.733, 158.96, 0.533, 23.72, 0.733, 33.29]}, {"time": 0.8333, "x": 158.96, "y": 33.29, "curve": "stepped"}, {"time": 2.4333, "x": 158.96, "y": 33.29, "curve": [2.512, 158.96, 2.618, 100.45, 2.512, 33.29, 2.618, 21.04]}, {"time": 2.6667}]}, "WEAPON": {"scale": [{"time": 2.6, "y": 0.605, "curve": [2.625, 1, 2.675, 1, 2.625, 0.605, 2.675, 1.249]}, {"time": 2.7, "y": 1.249, "curve": [2.725, 1, 2.775, 1, 2.725, 1.249, 2.775, 1]}, {"time": 2.8}]}}, "ik": {"HAND_LEFT_CONSTRAINT": [{"time": 2.6, "mix": 0, "bendPositive": false}], "WEAPON_CONSTRAINT": [{"time": 2.6, "mix": 0}]}, "attachments": {"Executioner": {"Brute/GoatCult/Cape_Executioner": {"Cape_Executioner": {"deform": [{}, {"time": 0.4333, "offset": 22, "vertices": [23.87019, 7.45044, -21.37524, -12.97678, 23.87045, 7.45036, 32.89787, 13.75012, -28.62122, -21.26415, 32.89821, 13.74997, 23.07681, 7.99757, -20.47346, -13.31687, 23.07701, 7.99756, 25.49614, 19.99353, -19.93439, -25.54244, 25.49644, 19.99339, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 15.92649, 9.5128, -13.16866, -13.06651, 15.92668, 9.51279, 15.92649, 9.5128, -13.16866, -13.06651, 15.92668, 9.51279, 15.92649, 9.5128, -13.16866, -13.06651, 15.92668, 9.51279]}, {"time": 0.8333, "vertices": [6.37119, -5.08624, 1.32068, -8.04465, 21.29773, -12.6469, 7.33347, -23.65891, 21.29736, -12.64704, 5.92537, -17.09395, -7.05678, -16.6586, 5.92505, -17.09409, 5.43817, -22.18449, -10.82956, -20.11078, 5.43799, -22.18463]}]}}}}, "drawOrder": [{"time": 0.3333, "offsets": [{"slot": "SHOULDER_RIGHT", "offset": 11}, {"slot": "HAND_RIGHT", "offset": 10}]}, {"time": 2.6, "offsets": [{"slot": "SHOULDER_RIGHT", "offset": 5}, {"slot": "HEAD_EXTRA", "offset": -8}, {"slot": "EYE_LEFT_SHOCKED", "offset": -8}, {"slot": "EYE_RIGHT_SHOCKED", "offset": -8}, {"slot": "Cape", "offset": -8}]}, {"time": 3.3333}], "events": [{"time": 2.5667, "name": "execute"}]}, "hurt-back": {"slots": {"EYE_LEFT_SHOCKED": {"attachment": [{"name": "EYE_LEFT_SHOCKED"}, {"time": 0.3333}]}, "EYE_RIGHT_SHOCKED": {"attachment": [{"name": "EYE_RIGHT_SHOCKED"}, {"time": 0.3333}]}}, "bones": {"HIP": {"rotate": [{"curve": [0.017, 0, 0.05, 1.86]}, {"time": 0.0667, "value": 1.86, "curve": [0.117, 1.86, 0.217, 0]}, {"time": 0.2667}], "translate": [{"y": -1.68, "curve": [0.017, 0, 0.05, -16.97, 0.017, -1.68, 0.05, -13.43]}, {"time": 0.0667, "x": -16.97, "y": -13.43, "curve": [0.117, -16.97, 0.217, 0, 0.117, -13.43, 0.217, -1.68]}, {"time": 0.2667, "y": -1.68}]}, "HAND_RIGHT_CONSTRAINT": {"translate": [{"x": 10.86, "y": -31.7, "curve": [0.051, 40.59, 0.099, 65.36, 0.051, 53.1, 0.099, 123.77]}, {"time": 0.1333, "x": 65.36, "y": 123.77, "curve": [0.192, 65.36, 0.308, 0, 0.192, 123.77, 0.308, 0]}, {"time": 0.3667}]}, "HAND_RIGHT": {"rotate": [{"value": -21.28}]}, "HEAD": {"rotate": [{"curve": [0.046, 0, 0.103, 7.42]}, {"time": 0.1667, "value": 18.55, "curve": [0.209, 13.28, 0.261, 3.91]}, {"time": 0.3}]}, "WEAPON_CONSTRAINT": {"translate": [{"x": -1.74, "y": -2.65, "curve": [0.046, 32.24, 0.091, 60.38, 0.046, 3.04, 0.091, 7.75]}, {"time": 0.1333, "x": 81.66, "y": 11.31, "curve": [0.217, 27.78, 0.295, -1.74, 0.217, 2.29, 0.295, -2.65]}, {"time": 0.3667, "x": -1.74, "y": -2.65}]}, "HAND_LEFT_HANDLE": {"translate": [{"x": 5.8, "y": 10.41, "curve": [0.051, -60.17, 0.099, -115.14, 0.051, 3.48, 0.099, -2.29]}, {"time": 0.1333, "x": -115.14, "y": -2.29}]}, "BACK_BTM": {"rotate": [{"value": 3.21, "curve": [0.015, 13.9, 0.075, 18.91]}, {"time": 0.1, "value": 18.91, "curve": [0.167, 18.91, 0.3, 0]}, {"time": 0.3667}]}, "BACK_TOP": {"rotate": [{"value": -3.91, "curve": [0.015, 13.17, 0.075, 21.18]}, {"time": 0.1, "value": 21.18, "curve": [0.167, 21.18, 0.3, 0]}, {"time": 0.3667}]}}}, "hurt-eyes": {"slots": {"EYE_LEFT_SHOCKED": {"attachment": [{"name": "EYE_LEFT_SHOCKED"}, {"time": 0.2667}]}, "EYE_RIGHT_SHOCKED": {"attachment": [{"name": "EYE_RIGHT_SHOCKED"}, {"time": 0.2667}]}}, "bones": {"EYE_LEFT": {"scale": [{"x": 1.532, "y": 1.532, "curve": [0.008, 1.532, 0.025, 0.799, 0.008, 1.532, 0.025, 0.799]}, {"time": 0.0333, "x": 0.799, "y": 0.799, "curve": [0.058, 0.799, 0.108, 1.157, 0.058, 0.799, 0.108, 1.157]}, {"time": 0.1333, "x": 1.157, "y": 1.157, "curve": [0.167, 1.157, 0.233, 1, 0.167, 1.157, 0.233, 1]}, {"time": 0.2667}]}, "EYE_RIGHT": {"scale": [{"x": 1.532, "y": 1.532, "curve": [0.008, 1.532, 0.025, 0.799, 0.008, 1.532, 0.025, 0.799]}, {"time": 0.0333, "x": 0.799, "y": 0.799, "curve": [0.058, 0.799, 0.108, 1.157, 0.058, 0.799, 0.108, 1.157]}, {"time": 0.1333, "x": 1.157, "y": 1.157, "curve": [0.167, 1.157, 0.233, 1, 0.167, 1.157, 0.233, 1]}, {"time": 0.2667}]}}}, "hurt-front": {"slots": {"EYE_LEFT_SHOCKED": {"attachment": [{"name": "EYE_LEFT_SHOCKED"}, {"time": 0.3333}]}, "EYE_RIGHT_SHOCKED": {"attachment": [{"name": "EYE_RIGHT_SHOCKED"}, {"time": 0.3333}]}}, "bones": {"HIP": {"rotate": [{"value": -6.24, "curve": [0.017, -6.24, 0.05, -16.22]}, {"time": 0.0667, "value": -16.22, "curve": [0.117, -16.22, 0.217, 0]}, {"time": 0.2667}], "translate": [{"y": -1.68, "curve": [0.017, 0, 0.05, 35.25, 0.017, -1.68, 0.05, 7.46]}, {"time": 0.0667, "x": 35.25, "y": 7.46, "curve": [0.117, 35.25, 0.217, 0, 0.117, 7.46, 0.217, -1.68]}, {"time": 0.2667, "y": -1.68}]}, "HAND_RIGHT_CONSTRAINT": {"translate": [{"x": 10.86, "y": -31.7, "curve": [0.051, 82.92, 0.099, 142.97, 0.051, -109.52, 0.099, -174.37]}, {"time": 0.1333, "x": 142.97, "y": -174.37, "curve": [0.192, 142.97, 0.308, 0, 0.192, -174.37, 0.308, 0]}, {"time": 0.3667}]}, "HAND_RIGHT": {"rotate": [{"value": -21.28}]}, "HEAD": {"rotate": [{"curve": [0.046, 0, 0.103, -11.58]}, {"time": 0.1667, "value": -28.96, "curve": [0.209, -20.73, 0.261, -6.1]}, {"time": 0.3}]}, "WEAPON_CONSTRAINT": {"translate": [{"x": -1.74, "y": -2.65, "curve": [0.046, 20, 0.091, 38, 0.046, -60.41, 0.091, -108.26]}, {"time": 0.1333, "x": 51.61, "y": -144.42, "curve": [0.217, 19.95, 0.295, 2.6, 0.217, -51.86, 0.295, -1.14]}, {"time": 0.3667, "x": 2.6, "y": -1.14}]}, "HAND_LEFT_HANDLE": {"translate": [{"x": 5.8, "y": 10.41, "curve": [0.051, 45.73, 0.099, 79, 0.051, -49.64, 0.099, -99.68]}, {"time": 0.1333, "x": 79, "y": -99.68, "curve": [0.192, 79, 0.308, 4.94, 0.192, -99.68, 0.308, 11.22]}, {"time": 0.3667, "x": 4.94, "y": 11.22}]}, "BACK_BTM": {"rotate": [{"value": -13.14, "curve": [0.015, -18.74, 0.075, -21.37]}, {"time": 0.1, "value": -21.37, "curve": [0.167, -21.37, 0.3, 0]}, {"time": 0.3667}]}, "BACK_TOP": {"rotate": [{"value": 12.81, "curve": [0.015, -10.98, 0.075, -22.14]}, {"time": 0.1, "value": -22.14, "curve": [0.167, -22.14, 0.3, 0]}, {"time": 0.3667}]}, "FOOT_LEFT_CONSTRAINT": {"translate": [{"x": -20.89, "y": 13.06, "curve": [0.012, -25.78, 0.1, -31.33, 0.012, 34.43, 0.1, 58.75]}, {"time": 0.1333, "x": -31.33, "y": 58.75, "curve": [0.192, -31.33, 0.308, 0, 0.192, 58.75, 0.308, 0]}, {"time": 0.3667}]}, "ANKLE_LEFT_CONSTRAINT": {"rotate": [{"value": 2.33}], "translate": [{"curve": [0.006, -1.83, 0.05, -3.92, 0.006, -6.72, 0.05, -14.36]}, {"time": 0.0667, "x": -3.92, "y": -14.36, "curve": [0.142, -3.92, 0.292, 0, 0.142, -14.36, 0.292, 0]}, {"time": 0.3667}]}}}, "idle": {"bones": {"HIP": {"rotate": [{"value": -1.43}], "translate": [{"y": -1.68, "curve": [0.092, 0, 0.275, -3.18, 0.092, -1.68, 0.275, -6.17]}, {"time": 0.3667, "x": -3.18, "y": -6.17, "curve": [0.442, -3.18, 0.592, -7.7, 0.442, -6.17, 0.592, -2.07]}, {"time": 0.6667, "x": -7.7, "y": -2.07, "curve": [0.758, -7.7, 0.942, -5.75, 0.758, -2.07, 0.942, -6.3]}, {"time": 1.0333, "x": -5.75, "y": -6.3, "curve": [1.108, -5.75, 1.258, 0, 1.108, -6.3, 1.258, -1.68]}, {"time": 1.3333, "y": -1.68}]}, "HAND_RIGHT_CONSTRAINT": {"rotate": [{"value": -1.43}]}, "HAND_RIGHT": {"rotate": [{"value": -27.34, "curve": [0.05, -28.12, 0.095, -28.6]}, {"time": 0.1333, "value": -28.6, "curve": [0.383, -23.74, 0.633, -18.88]}, {"time": 0.8, "value": -18.88, "curve": [0.929, -18.88, 1.162, -24.92]}, {"time": 1.3333, "value": -27.34}]}, "HEAD": {"rotate": [{"value": 5.87, "curve": [0.167, 5.87, 0.5, 11.23]}, {"time": 0.6667, "value": 11.23, "curve": [0.833, 11.23, 1.167, 5.87]}, {"time": 1.3333, "value": 5.87}], "translate": [{"curve": [0.075, 0, 0.225, -6.4, 0.075, 0, 0.225, 0.63]}, {"time": 0.3, "x": -6.4, "y": 0.63, "curve": [0.392, -6.4, 0.575, 0, 0.392, 0.63, 0.575, 0]}, {"time": 0.6667, "curve": [0.742, 0, 0.892, -6.4, 0.742, 0, 0.892, 0.63]}, {"time": 0.9667, "x": -6.4, "y": 0.63, "curve": [1.058, -6.4, 1.242, 0, 1.058, 0.63, 1.242, 0]}, {"time": 1.3333}]}, "WEAPON_CONSTRAINT": {"rotate": [{"value": -1.43}], "translate": [{"x": 2.6, "y": -1.14, "curve": [0.024, 2.71, 0.046, 2.78, 0.024, -1.17, 0.046, -1.18]}, {"time": 0.0667, "x": 2.78, "y": -1.18, "curve": [0.233, 2.78, 0.567, -1.35, 0.233, -1.18, 0.567, -0.08]}, {"time": 0.7333, "x": -1.35, "y": -0.08, "curve": [0.88, -1.35, 1.16, 1.94, 0.88, -0.08, 1.16, -0.96]}, {"time": 1.3333, "x": 2.6, "y": -1.14}]}, "HAND_LEFT_HANDLE": {"rotate": [{"value": -1.43}], "translate": [{"x": 4.94, "y": 11.22, "curve": [0.024, 5.49, 0.046, 5.8, 0.024, 10.7, 0.046, 10.41]}, {"time": 0.0667, "x": 5.8, "y": 10.41, "curve": [0.233, 5.8, 0.567, -14.28, 0.233, 10.41, 0.567, 29.48]}, {"time": 0.7333, "x": -14.28, "y": 29.48, "curve": [0.88, -14.28, 1.16, 1.74, 0.88, 29.48, 1.16, 14.26]}, {"time": 1.3333, "x": 4.94, "y": 11.22}]}, "root": {"rotate": [{"value": -1.43}]}, "Holder": {"rotate": [{"value": -1.43}]}, "BACK_BTM": {"rotate": [{"value": -1.43}]}, "BACK_TOP": {"rotate": [{"value": -1.43, "curve": [0.167, -1.43, 0.5, 1.15]}, {"time": 0.6667, "value": 1.15, "curve": [0.833, 1.15, 1.167, -1.43]}, {"time": 1.3333, "value": -1.43}], "translate": [{"curve": [0.167, 0, 0.5, 0.9, 0.167, 0, 0.5, 6.49]}, {"time": 0.6667, "x": 0.9, "y": 6.49, "curve": [0.833, 0.9, 1.167, 0, 0.833, 6.49, 1.167, 0]}, {"time": 1.3333}]}, "FACE": {"rotate": [{"value": -1.43}]}, "FOOT_RIGHT_CONSTRAINT": {"rotate": [{"value": -1.43}]}, "ANKLE_RIGHT_CONSTRAINT": {"rotate": [{"value": -1.43}]}, "FOOT_LEFT_CONSTRAINT": {"rotate": [{"value": -1.43}]}, "ANKLE_LEFT_CONSTRAINT": {"rotate": [{"value": -1.43}]}, "MASK": {"rotate": [{"value": -1.43}]}, "HORN_RIGHT": {"rotate": [{"value": -1.43}]}, "HORN_LEFT": {"rotate": [{"value": -1.43}]}, "EYE_LEFT": {"rotate": [{"value": -1.43}]}, "EYE_RIGHT": {"rotate": [{"value": -1.43}]}, "ARM_RIGHT": {"rotate": [{"value": -0.87, "curve": [0.153, 1.88, 0.292, 3.83]}, {"time": 0.4, "value": 3.83, "curve": [0.567, 3.83, 0.817, -1.03]}, {"time": 1.0667, "value": -5.89, "curve": [1.157, -4.12, 1.248, -2.35]}, {"time": 1.3333, "value": -0.87}]}, "SHOULDER_RIGHT": {"rotate": [{"value": 3.04, "curve": [0.048, 3.55, 0.093, 3.83]}, {"time": 0.1333, "value": 3.83, "curve": [0.3, 3.83, 0.55, -1.03]}, {"time": 0.8, "value": -5.89, "curve": [0.991, -2.11, 1.183, 1.68]}, {"time": 1.3333, "value": 3.04}]}, "SHOULDER_LEFT": {"rotate": [{"value": -77.5, "curve": [0.125, -80.28, 0.25, -83.05]}, {"time": 0.3333, "value": -83.05, "curve": [0.5, -83.05, 0.833, -71.95]}, {"time": 1, "value": -71.95, "curve": [1.083, -71.95, 1.208, -74.73]}, {"time": 1.3333, "value": -77.5}]}, "WEAPON_CONTROLLER": {"rotate": [{"value": 21.51, "curve": [0.063, 21.97, 0.121, 22.28]}, {"time": 0.1667, "value": 22.28, "curve": [0.333, 22.28, 0.667, 18.14]}, {"time": 0.8333, "value": 18.14, "curve": [0.955, 18.14, 1.167, 20.39]}, {"time": 1.3333, "value": 21.51}]}, "ARM_LEFT": {"rotate": [{"value": -58.71}]}}, "ik": {"HAND_LEFT_CONSTRAINT": [{"mix": 0, "bendPositive": false}], "HAND_RIGHT": [{"mix": 0, "bendPositive": false}], "WEAPON_CONSTRAINT": [{"mix": 0}]}}, "idle2": {"bones": {"HIP": {"rotate": [{"value": -1.43}], "translate": [{"y": -1.68, "curve": [0.092, 0, 0.275, -3.18, 0.092, -1.68, 0.275, -6.17]}, {"time": 0.3667, "x": -3.18, "y": -6.17, "curve": [0.442, -3.18, 0.592, -7.7, 0.442, -6.17, 0.592, -2.07]}, {"time": 0.6667, "x": -7.7, "y": -2.07, "curve": [0.758, -7.7, 0.942, -5.75, 0.758, -2.07, 0.942, -6.3]}, {"time": 1.0333, "x": -5.75, "y": -6.3, "curve": [1.108, -5.75, 1.258, 0, 1.108, -6.3, 1.258, -1.68]}, {"time": 1.3333, "y": -1.68}]}, "HAND_RIGHT_CONSTRAINT": {"rotate": [{"value": -1.43}]}, "HAND_RIGHT": {"rotate": [{"value": -27.34, "curve": [0.05, -28.12, 0.095, -28.6]}, {"time": 0.1333, "value": -28.6, "curve": [0.383, -23.74, 0.633, -18.88]}, {"time": 0.8, "value": -18.88, "curve": [0.929, -18.88, 1.162, -24.92]}, {"time": 1.3333, "value": -27.34}]}, "HEAD": {"rotate": [{"value": 5.87, "curve": [0.167, 5.87, 0.5, 11.23]}, {"time": 0.6667, "value": 11.23, "curve": [0.833, 11.23, 1.167, 5.87]}, {"time": 1.3333, "value": 5.87}], "translate": [{"curve": [0.075, 0, 0.225, -6.4, 0.075, 0, 0.225, 0.63]}, {"time": 0.3, "x": -6.4, "y": 0.63, "curve": [0.392, -6.4, 0.575, 0, 0.392, 0.63, 0.575, 0]}, {"time": 0.6667, "curve": [0.742, 0, 0.892, -6.4, 0.742, 0, 0.892, 0.63]}, {"time": 0.9667, "x": -6.4, "y": 0.63, "curve": [1.058, -6.4, 1.242, 0, 1.058, 0.63, 1.242, 0]}, {"time": 1.3333}]}, "WEAPON_CONSTRAINT": {"rotate": [{"value": -1.43}], "translate": [{"x": 2.6, "y": -1.14, "curve": [0.024, 2.71, 0.046, 2.78, 0.024, -1.17, 0.046, -1.18]}, {"time": 0.0667, "x": 2.78, "y": -1.18, "curve": [0.233, 2.78, 0.567, -1.35, 0.233, -1.18, 0.567, -0.08]}, {"time": 0.7333, "x": -1.35, "y": -0.08, "curve": [0.88, -1.35, 1.16, 1.94, 0.88, -0.08, 1.16, -0.96]}, {"time": 1.3333, "x": 2.6, "y": -1.14}]}, "HAND_LEFT_HANDLE": {"rotate": [{"value": -1.43}], "translate": [{"x": 4.94, "y": 11.22, "curve": [0.024, 5.49, 0.046, 5.8, 0.024, 10.7, 0.046, 10.41]}, {"time": 0.0667, "x": 5.8, "y": 10.41, "curve": [0.233, 5.8, 0.567, -14.28, 0.233, 10.41, 0.567, 29.48]}, {"time": 0.7333, "x": -14.28, "y": 29.48, "curve": [0.88, -14.28, 1.16, 1.74, 0.88, 29.48, 1.16, 14.26]}, {"time": 1.3333, "x": 4.94, "y": 11.22}]}, "root": {"rotate": [{"value": -1.43}]}, "Holder": {"rotate": [{"value": -1.43}]}, "BACK_BTM": {"rotate": [{"value": -1.43}]}, "BACK_TOP": {"rotate": [{"value": -1.43, "curve": [0.167, -1.43, 0.5, 1.15]}, {"time": 0.6667, "value": 1.15, "curve": [0.833, 1.15, 1.167, -1.43]}, {"time": 1.3333, "value": -1.43}], "translate": [{"curve": [0.167, 0, 0.5, 0.9, 0.167, 0, 0.5, 6.49]}, {"time": 0.6667, "x": 0.9, "y": 6.49, "curve": [0.833, 0.9, 1.167, 0, 0.833, 6.49, 1.167, 0]}, {"time": 1.3333}]}, "FACE": {"rotate": [{"value": -1.43}]}, "FOOT_RIGHT_CONSTRAINT": {"rotate": [{"value": -1.43}]}, "ANKLE_RIGHT_CONSTRAINT": {"rotate": [{"value": -1.43}]}, "FOOT_LEFT_CONSTRAINT": {"rotate": [{"value": -1.43}]}, "ANKLE_LEFT_CONSTRAINT": {"rotate": [{"value": -1.43}]}, "MASK": {"rotate": [{"value": -1.43}]}, "HORN_RIGHT": {"rotate": [{"value": -1.43}]}, "HORN_LEFT": {"rotate": [{"value": -1.43}]}, "EYE_LEFT": {"rotate": [{"value": -1.43}]}, "EYE_RIGHT": {"rotate": [{"value": -1.43}]}, "ARM_RIGHT": {"rotate": [{"value": -0.87, "curve": [0.153, 1.88, 0.292, 3.83]}, {"time": 0.4, "value": 3.83, "curve": [0.567, 3.83, 0.817, -1.03]}, {"time": 1.0667, "value": -5.89, "curve": [1.157, -4.12, 1.248, -2.35]}, {"time": 1.3333, "value": -0.87}]}, "SHOULDER_RIGHT": {"rotate": [{"value": 3.04, "curve": [0.048, 3.55, 0.093, 3.83]}, {"time": 0.1333, "value": 3.83, "curve": [0.3, 3.83, 0.55, -1.03]}, {"time": 0.8, "value": -5.89, "curve": [0.991, -2.11, 1.183, 1.68]}, {"time": 1.3333, "value": 3.04}]}, "SHOULDER_LEFT": {"rotate": [{"value": -77.5, "curve": [0.125, -80.28, 0.25, -83.05]}, {"time": 0.3333, "value": -83.05, "curve": [0.5, -83.05, 0.833, -71.95]}, {"time": 1, "value": -71.95, "curve": [1.083, -71.95, 1.208, -74.73]}, {"time": 1.3333, "value": -77.5}]}, "WEAPON_CONTROLLER": {"rotate": [{"value": 21.51, "curve": [0.063, 21.97, 0.121, 22.28]}, {"time": 0.1667, "value": 22.28, "curve": [0.333, 22.28, 0.667, 18.14]}, {"time": 0.8333, "value": 18.14, "curve": [0.955, 18.14, 1.167, 20.39]}, {"time": 1.3333, "value": 21.51}]}, "ARM_LEFT": {"rotate": [{"value": -58.71}]}}, "ik": {"HAND_LEFT_CONSTRAINT": [{"mix": 0, "bendPositive": false}], "HAND_RIGHT": [{"mix": 0, "bendPositive": false}], "WEAPON_CONSTRAINT": [{"mix": 0}]}}, "jeer": {"bones": {"HIP": {"rotate": [{"value": -6.24, "curve": [0.017, -6.24, 0.024, -16.22]}, {"time": 0.0667, "value": -16.22, "curve": [0.133, -16.22, 0.267, 4.6]}, {"time": 0.3333, "value": 4.6, "curve": [0.508, 4.6, 0.858, -1.43]}, {"time": 1.0333, "value": -1.43}], "translate": [{"y": -1.68, "curve": [0.017, 0, 0.024, 7.29, 0.017, -1.68, 0.024, 6.05]}, {"time": 0.0667, "x": 7.29, "y": 6.05, "curve": [0.108, 7.29, 0.219, 3.2, 0.108, 6.05, 0.219, -3.29]}, {"time": 0.2333, "x": 0.4, "y": -9.67, "curve": [0.342, 0.4, 0.558, -7.7, 0.342, -9.67, 0.558, -2.07]}, {"time": 0.6667, "x": -7.7, "y": -2.07, "curve": [0.733, -7.7, 0.867, -5.75, 0.733, -2.07, 0.867, -6.3]}, {"time": 0.9333, "x": -5.75, "y": -6.3, "curve": [0.958, -5.75, 1.008, 0, 0.958, -6.3, 1.008, -1.68]}, {"time": 1.0333, "y": -1.68}]}, "HAND_RIGHT_CONSTRAINT": {"rotate": [{"value": -1.43}], "translate": [{"x": 10.86, "y": -31.7, "curve": [0.05, 10.86, 0.073, 142.97, 0.05, -31.7, 0.073, -174.37]}, {"time": 0.2, "x": 142.97, "y": -174.37, "curve": [0.217, 142.97, 0.25, 0, 0.217, -174.37, 0.25, 0]}, {"time": 0.2667}]}, "HAND_RIGHT": {"rotate": [{"value": -21.28, "curve": [0.042, -21.28, 0.026, -21.86]}, {"time": 0.1667, "value": -21.86, "curve": [0.333, -21.86, 0.667, -18.88]}, {"time": 0.8333, "value": -18.88, "curve": [0.882, -18.88, 0.969, -24.92]}, {"time": 1.0333, "value": -27.34}]}, "HEAD": {"rotate": [{"curve": [0.05, 0, 0.073, -9.51]}, {"time": 0.2, "value": -9.51, "curve": [0.264, -6.81, 0.341, -2]}, {"time": 0.4, "curve": [0.548, 7.02, 0.684, 11.23]}, {"time": 0.8, "value": 11.23, "curve": [0.858, 11.23, 0.975, 5.87]}, {"time": 1.0333, "value": 5.87}], "translate": [{"time": 0.5333, "curve": [0.558, 0, 0.608, -6.4, 0.558, 0, 0.608, 0.63]}, {"time": 0.6333, "x": -6.4, "y": 0.63, "curve": [0.675, -6.4, 0.758, 0, 0.675, 0.63, 0.758, 0]}, {"time": 0.8, "curve": [0.825, 0, 0.875, -6.4, 0.825, 0, 0.875, 0.63]}, {"time": 0.9, "x": -6.4, "y": 0.63, "curve": [0.933, -6.4, 1, 0, 0.933, 0.63, 1, 0]}, {"time": 1.0333}]}, "WEAPON_CONSTRAINT": {"rotate": [{"value": -1.43, "curve": [0.017, -1.43, 0.024, 0]}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.7, "curve": [0.783, 0, 0.95, -1.43]}, {"time": 1.0333, "value": -1.43}], "translate": [{"x": -1.74, "y": -2.65, "curve": [0.017, -1.74, 0.024, -23.95, 0.017, -2.65, 0.024, 20.02]}, {"time": 0.0667, "x": -23.95, "y": 20.02, "curve": [0.119, -23.95, 0.163, 98.37, 0.119, 20.02, 0.163, -88.99]}, {"time": 0.1667, "x": 133.95, "y": -120.7, "curve": [0.183, 133.95, 0.217, 129.7, 0.183, -120.7, 0.217, -121.45]}, {"time": 0.2333, "x": 129.7, "y": -121.45, "curve": [0.25, 129.7, 0.283, 133.95, 0.25, -121.45, 0.283, -120.7]}, {"time": 0.3, "x": 133.95, "y": -120.7, "curve": [0.334, 132.41, 0.369, 130.86, 0.334, -120.96, 0.369, -121.22]}, {"time": 0.4, "x": 129.71, "y": -121.41, "curve": [0.475, 129.71, 0.432, 3.56, 0.475, -121.41, 0.432, -26.54]}, {"time": 0.7, "x": 3.56, "y": -26.54, "curve": [0.814, 3.21, 0.929, 2.86, 0.814, -17.3, 0.929, -8.07]}, {"time": 1.0333, "x": 2.6, "y": -1.14}]}, "HAND_LEFT_HANDLE": {"rotate": [{"value": -1.43, "curve": [0.017, -1.43, 0.024, 0]}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.6, "curve": [0.708, 0, 0.925, -1.43]}, {"time": 1.0333, "value": -1.43}], "translate": [{"time": 0.0667, "x": -63.45, "y": 10.34, "curve": [0.119, -63.45, 0.163, 65.88, 0.119, 10.34, 0.163, -37.47]}, {"time": 0.1667, "x": 103.51, "y": -51.38, "curve": "stepped"}, {"time": 0.3667, "x": 103.51, "y": -51.38, "curve": [0.45, 103.51, 0.617, -32.25, 0.45, -51.38, 0.617, -2.7]}, {"time": 0.7, "x": -32.25, "y": -2.7, "curve": [0.891, -32.25, 0.95, 4.94, 0.891, -2.7, 0.95, 11.22]}, {"time": 1.0333, "x": 4.94, "y": 11.22}]}, "root": {"rotate": [{"value": -1.43}]}, "Holder": {"rotate": [{"value": -1.43}], "scale": [{"curve": [0.017, 1, 0.05, 1.077, 0.017, 1, 0.05, 0.84]}, {"time": 0.0667, "x": 1.077, "y": 0.84, "curve": [0.1, 1.077, 0.167, 0.9, 0.1, 0.84, 0.167, 1.23]}, {"time": 0.2, "x": 0.9, "y": 1.23, "curve": [0.242, 0.9, 0.325, 1.08, 0.242, 1.23, 0.325, 0.942]}, {"time": 0.3667, "x": 1.08, "y": 0.942, "curve": [0.442, 1.08, 0.592, 1, 0.442, 0.942, 0.592, 1]}, {"time": 0.6667}]}, "BACK_BTM": {"rotate": [{"value": -13.14, "curve": [0.133, -13.14, 0.195, -1.43]}, {"time": 0.5333, "value": -1.43}]}, "BACK_TOP": {"rotate": [{"value": 12.81, "curve": [0.133, 12.81, 0.195, -1.43]}, {"time": 0.5333, "value": -1.43, "curve": [0.6, -1.43, 0.733, 1.15]}, {"time": 0.8, "value": 1.15, "curve": [0.858, 1.15, 0.975, -1.43]}, {"time": 1.0333, "value": -1.43}], "translate": [{"time": 0.5333, "curve": [0.6, 0, 0.733, 0.9, 0.6, 0, 0.733, 6.49]}, {"time": 0.8, "x": 0.9, "y": 6.49, "curve": [0.858, 0.9, 0.975, 0, 0.858, 6.49, 0.975, 0]}, {"time": 1.0333}]}, "FACE": {"rotate": [{"value": -1.43}]}, "FOOT_RIGHT_CONSTRAINT": {"rotate": [{"value": -1.43}]}, "ANKLE_RIGHT_CONSTRAINT": {"rotate": [{"value": -1.43}]}, "FOOT_LEFT_CONSTRAINT": {"rotate": [{"value": -1.43}], "translate": [{"x": -20.89, "y": 13.06, "curve": [0.067, -20.89, 0.098, -13.76, 0.067, 13.06, 0.098, 27.6]}, {"time": 0.2667, "x": -13.76, "y": 27.6, "curve": [0.292, -13.76, 0.342, 0, 0.292, 27.6, 0.342, 0]}, {"time": 0.3667}]}, "ANKLE_LEFT_CONSTRAINT": {"rotate": [{"value": 2.33, "curve": [0.092, 2.33, 0.134, -1.43]}, {"time": 0.3667, "value": -1.43}], "translate": [{"curve": [0.033, 0, 0.049, -1.3, 0.033, 0, 0.049, 2.78]}, {"time": 0.1333, "x": -1.3, "y": 2.78, "curve": [0.192, -1.3, 0.308, 0, 0.192, 2.78, 0.308, 0]}, {"time": 0.3667}]}, "MASK": {"rotate": [{"value": -1.43}], "translate": [{"curve": [0.06, 0, 0.113, 1.58, 0.06, 0, 0.113, -10.86]}, {"time": 0.1667, "x": 3.96, "y": -27.15, "curve": [0.192, 3.96, 0.242, 0, 0.192, -27.15, 0.242, 0]}, {"time": 0.2667, "curve": [0.292, 0, 0.342, 3.96, 0.292, 0, 0.342, -27.15]}, {"time": 0.3667, "x": 3.96, "y": -27.15, "curve": [0.392, 3.96, 0.442, 0, 0.392, -27.15, 0.442, 0]}, {"time": 0.4667, "curve": [0.5, 0, 0.567, 3.96, 0.5, 0, 0.567, -27.15]}, {"time": 0.6, "x": 3.96, "y": -27.15, "curve": [0.634, 2.64, 0.671, 0, 0.634, -18.1, 0.671, 0]}, {"time": 0.7667}]}, "HORN_RIGHT": {"rotate": [{"value": -1.43}]}, "HORN_LEFT": {"rotate": [{"value": -1.43}]}, "EYE_LEFT": {"rotate": [{"value": -1.43}]}, "EYE_RIGHT": {"rotate": [{"value": -1.43}]}, "ARM_RIGHT": {"rotate": [{"value": -0.87, "curve": [0.042, -0.87, 0.026, -41.59]}, {"time": 0.1667, "value": -41.59, "curve": [0.3, -41.59, 0.567, 3.83]}, {"time": 0.7, "value": 3.83, "curve": [0.758, 3.83, 0.846, -1.03]}, {"time": 0.9333, "value": -5.89, "curve": [0.967, -4.12, 1.001, -2.35]}, {"time": 1.0333, "value": -0.87}]}, "SHOULDER_RIGHT": {"rotate": [{"value": 3.04, "curve": [0.042, 3.04, 0.026, 65.54]}, {"time": 0.1667, "value": 65.54, "curve": [0.333, 65.54, 0.667, -5.89]}, {"time": 0.8333, "value": -5.89, "curve": [0.905, -2.11, 0.977, 1.68]}, {"time": 1.0333, "value": 3.04}]}, "SHOULDER_LEFT": {"rotate": [{"value": -77.5}]}, "WEAPON_CONTROLLER": {"rotate": [{"value": 21.51, "curve": "stepped"}, {"time": 0.5333, "value": 21.51, "curve": [0.559, 21.97, 0.582, 22.28]}, {"time": 0.6, "value": 22.28, "curve": [0.658, 22.28, 0.775, 18.14]}, {"time": 0.8333, "value": 18.14, "curve": [0.882, 18.14, 0.967, 20.39]}, {"time": 1.0333, "value": 21.51}]}, "ARM_LEFT": {"rotate": [{"value": -58.71}]}}, "ik": {"HAND_LEFT_CONSTRAINT": [{"time": 1.0333, "mix": 0, "bendPositive": false}], "HAND_RIGHT": [{"mix": 0, "bendPositive": false}], "WEAPON_CONSTRAINT": [{"time": 1.0333, "mix": 0}]}}, "notice-player": {"bones": {"HIP": {"rotate": [{"value": -6.24, "curve": [0.017, -6.24, 0.024, -16.22]}, {"time": 0.0667, "value": -16.22, "curve": [0.133, -16.22, 0.267, 4.6]}, {"time": 0.3333, "value": 4.6, "curve": [0.508, 4.6, 0.858, -1.43]}, {"time": 1.0333, "value": -1.43}], "translate": [{"y": -1.68, "curve": [0.017, 0, 0.024, 7.29, 0.017, -1.68, 0.024, 6.05]}, {"time": 0.0667, "x": 7.29, "y": 6.05, "curve": [0.108, 7.29, 0.219, 3.2, 0.108, 6.05, 0.219, -3.29]}, {"time": 0.2333, "x": 0.4, "y": -9.67, "curve": [0.342, 0.4, 0.558, -7.7, 0.342, -9.67, 0.558, -2.07]}, {"time": 0.6667, "x": -7.7, "y": -2.07, "curve": [0.733, -7.7, 0.867, -5.75, 0.733, -2.07, 0.867, -6.3]}, {"time": 0.9333, "x": -5.75, "y": -6.3, "curve": [0.958, -5.75, 1.008, 0, 0.958, -6.3, 1.008, -1.68]}, {"time": 1.0333, "y": -1.68}]}, "HAND_RIGHT_CONSTRAINT": {"rotate": [{"value": -1.43}], "translate": [{"x": 10.86, "y": -31.7, "curve": [0.05, 10.86, 0.073, 142.97, 0.05, -31.7, 0.073, -174.37]}, {"time": 0.2, "x": 142.97, "y": -174.37, "curve": [0.217, 142.97, 0.25, 0, 0.217, -174.37, 0.25, 0]}, {"time": 0.2667}]}, "HAND_RIGHT": {"rotate": [{"value": -21.28, "curve": [0.042, -21.28, 0.026, -21.86]}, {"time": 0.1667, "value": -21.86, "curve": [0.333, -21.86, 0.667, -18.88]}, {"time": 0.8333, "value": -18.88, "curve": [0.882, -18.88, 0.969, -24.92]}, {"time": 1.0333, "value": -27.34}]}, "HEAD": {"rotate": [{"curve": [0.05, 0, 0.073, -9.51]}, {"time": 0.2, "value": -9.51, "curve": [0.264, -6.81, 0.341, -2]}, {"time": 0.4, "curve": [0.548, 7.02, 0.684, 11.23]}, {"time": 0.8, "value": 11.23, "curve": [0.858, 11.23, 0.975, 5.87]}, {"time": 1.0333, "value": 5.87}], "translate": [{"time": 0.5333, "curve": [0.558, 0, 0.608, -6.4, 0.558, 0, 0.608, 0.63]}, {"time": 0.6333, "x": -6.4, "y": 0.63, "curve": [0.675, -6.4, 0.758, 0, 0.675, 0.63, 0.758, 0]}, {"time": 0.8, "curve": [0.825, 0, 0.875, -6.4, 0.825, 0, 0.875, 0.63]}, {"time": 0.9, "x": -6.4, "y": 0.63, "curve": [0.933, -6.4, 1, 0, 0.933, 0.63, 1, 0]}, {"time": 1.0333}]}, "WEAPON_CONSTRAINT": {"rotate": [{"value": -1.43, "curve": [0.017, -1.43, 0.024, 0]}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.7, "curve": [0.783, 0, 0.95, -1.43]}, {"time": 1.0333, "value": -1.43}], "translate": [{"x": -1.74, "y": -2.65, "curve": [0.017, -1.74, 0.024, -23.95, 0.017, -2.65, 0.024, 20.02]}, {"time": 0.0667, "x": -23.95, "y": 20.02, "curve": [0.119, -23.95, 0.163, 98.37, 0.119, 20.02, 0.163, -88.99]}, {"time": 0.1667, "x": 133.95, "y": -120.7, "curve": [0.183, 133.95, 0.217, 129.7, 0.183, -120.7, 0.217, -121.45]}, {"time": 0.2333, "x": 129.7, "y": -121.45, "curve": [0.25, 129.7, 0.283, 133.95, 0.25, -121.45, 0.283, -120.7]}, {"time": 0.3, "x": 133.95, "y": -120.7, "curve": [0.334, 132.41, 0.369, 130.86, 0.334, -120.96, 0.369, -121.22]}, {"time": 0.4, "x": 129.71, "y": -121.41, "curve": [0.475, 129.71, 0.432, 3.56, 0.475, -121.41, 0.432, -26.54]}, {"time": 0.7, "x": 3.56, "y": -26.54, "curve": [0.814, 3.21, 0.929, 2.86, 0.814, -17.3, 0.929, -8.07]}, {"time": 1.0333, "x": 2.6, "y": -1.14}]}, "HAND_LEFT_HANDLE": {"rotate": [{"value": -1.43, "curve": [0.017, -1.43, 0.024, 0]}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.6, "curve": [0.708, 0, 0.925, -1.43]}, {"time": 1.0333, "value": -1.43}], "translate": [{"time": 0.0667, "x": -63.45, "y": 10.34, "curve": [0.119, -63.45, 0.163, 65.88, 0.119, 10.34, 0.163, -37.47]}, {"time": 0.1667, "x": 103.51, "y": -51.38, "curve": "stepped"}, {"time": 0.3667, "x": 103.51, "y": -51.38, "curve": [0.45, 103.51, 0.617, -32.25, 0.45, -51.38, 0.617, -2.7]}, {"time": 0.7, "x": -32.25, "y": -2.7, "curve": [0.891, -32.25, 0.95, 4.94, 0.891, -2.7, 0.95, 11.22]}, {"time": 1.0333, "x": 4.94, "y": 11.22}]}, "root": {"rotate": [{"value": -1.43}]}, "Holder": {"rotate": [{"value": -1.43}], "scale": [{"curve": [0.017, 1, 0.05, 1.077, 0.017, 1, 0.05, 0.84]}, {"time": 0.0667, "x": 1.077, "y": 0.84, "curve": [0.1, 1.077, 0.167, 0.9, 0.1, 0.84, 0.167, 1.23]}, {"time": 0.2, "x": 0.9, "y": 1.23, "curve": [0.242, 0.9, 0.325, 1.08, 0.242, 1.23, 0.325, 0.942]}, {"time": 0.3667, "x": 1.08, "y": 0.942, "curve": [0.442, 1.08, 0.592, 1, 0.442, 0.942, 0.592, 1]}, {"time": 0.6667}]}, "BACK_BTM": {"rotate": [{"value": -13.14, "curve": [0.133, -13.14, 0.195, -1.43]}, {"time": 0.5333, "value": -1.43}]}, "BACK_TOP": {"rotate": [{"value": 12.81, "curve": [0.133, 12.81, 0.195, -1.43]}, {"time": 0.5333, "value": -1.43, "curve": [0.6, -1.43, 0.733, 1.15]}, {"time": 0.8, "value": 1.15, "curve": [0.858, 1.15, 0.975, -1.43]}, {"time": 1.0333, "value": -1.43}], "translate": [{"time": 0.5333, "curve": [0.6, 0, 0.733, 0.9, 0.6, 0, 0.733, 6.49]}, {"time": 0.8, "x": 0.9, "y": 6.49, "curve": [0.858, 0.9, 0.975, 0, 0.858, 6.49, 0.975, 0]}, {"time": 1.0333}]}, "FACE": {"rotate": [{"value": -1.43}]}, "FOOT_RIGHT_CONSTRAINT": {"rotate": [{"value": -1.43}]}, "ANKLE_RIGHT_CONSTRAINT": {"rotate": [{"value": -1.43}]}, "FOOT_LEFT_CONSTRAINT": {"rotate": [{"value": -1.43}], "translate": [{"x": -20.89, "y": 13.06, "curve": [0.067, -20.89, 0.098, -13.76, 0.067, 13.06, 0.098, 27.6]}, {"time": 0.2667, "x": -13.76, "y": 27.6, "curve": [0.292, -13.76, 0.342, 0, 0.292, 27.6, 0.342, 0]}, {"time": 0.3667}]}, "ANKLE_LEFT_CONSTRAINT": {"rotate": [{"value": 2.33, "curve": [0.092, 2.33, 0.134, -1.43]}, {"time": 0.3667, "value": -1.43}], "translate": [{"curve": [0.033, 0, 0.049, -1.3, 0.033, 0, 0.049, 2.78]}, {"time": 0.1333, "x": -1.3, "y": 2.78, "curve": [0.192, -1.3, 0.308, 0, 0.192, 2.78, 0.308, 0]}, {"time": 0.3667}]}, "MASK": {"rotate": [{"value": -1.43}], "translate": [{"curve": [0.06, 0, 0.113, 1.58, 0.06, 0, 0.113, -10.86]}, {"time": 0.1667, "x": 3.96, "y": -27.15, "curve": [0.192, 3.96, 0.242, 0, 0.192, -27.15, 0.242, 0]}, {"time": 0.2667, "curve": [0.292, 0, 0.342, 3.96, 0.292, 0, 0.342, -27.15]}, {"time": 0.3667, "x": 3.96, "y": -27.15, "curve": [0.392, 3.96, 0.442, 0, 0.392, -27.15, 0.442, 0]}, {"time": 0.4667, "curve": [0.5, 0, 0.567, 3.96, 0.5, 0, 0.567, -27.15]}, {"time": 0.6, "x": 3.96, "y": -27.15, "curve": [0.634, 2.64, 0.671, 0, 0.634, -18.1, 0.671, 0]}, {"time": 0.7667}]}, "HORN_RIGHT": {"rotate": [{"value": -1.43}]}, "HORN_LEFT": {"rotate": [{"value": -1.43}]}, "EYE_LEFT": {"rotate": [{"value": -1.43}]}, "EYE_RIGHT": {"rotate": [{"value": -1.43}]}, "ARM_RIGHT": {"rotate": [{"value": -0.87, "curve": [0.042, -0.87, 0.026, -41.59]}, {"time": 0.1667, "value": -41.59, "curve": [0.3, -41.59, 0.567, 3.83]}, {"time": 0.7, "value": 3.83, "curve": [0.758, 3.83, 0.846, -1.03]}, {"time": 0.9333, "value": -5.89, "curve": [0.967, -4.12, 1.001, -2.35]}, {"time": 1.0333, "value": -0.87}]}, "SHOULDER_RIGHT": {"rotate": [{"value": 3.04, "curve": [0.042, 3.04, 0.026, 65.54]}, {"time": 0.1667, "value": 65.54, "curve": [0.333, 65.54, 0.667, -5.89]}, {"time": 0.8333, "value": -5.89, "curve": [0.905, -2.11, 0.977, 1.68]}, {"time": 1.0333, "value": 3.04}]}, "SHOULDER_LEFT": {"rotate": [{"value": -77.5}]}, "WEAPON_CONTROLLER": {"rotate": [{"value": 21.51, "curve": "stepped"}, {"time": 0.5333, "value": 21.51, "curve": [0.559, 21.97, 0.582, 22.28]}, {"time": 0.6, "value": 22.28, "curve": [0.658, 22.28, 0.775, 18.14]}, {"time": 0.8333, "value": 18.14, "curve": [0.882, 18.14, 0.967, 20.39]}, {"time": 1.0333, "value": 21.51}]}, "ARM_LEFT": {"rotate": [{"value": -58.71}]}}, "ik": {"HAND_LEFT_CONSTRAINT": [{"time": 1.0333, "mix": 0, "bendPositive": false}], "HAND_RIGHT": [{"mix": 0, "bendPositive": false}], "WEAPON_CONSTRAINT": [{"time": 1.0333, "mix": 0}]}}, "quickattack-charge": {"bones": {"HIP": {"translate": [{"y": -1.68, "curve": [0.167, 0, 0.23, 43.27, 0.167, -1.68, 0.23, -15.35]}, {"time": 0.6667, "x": 43.27, "y": -15.35}]}, "HAND_RIGHT_CONSTRAINT": {"translate": [{"x": 2.18, "y": -4.38, "curve": [0.167, 2.18, 0.23, 118.02, 0.167, -4.38, 0.23, -70.33]}, {"time": 0.6667, "x": 118.02, "y": -70.33}]}, "HAND_RIGHT": {"rotate": [{"value": -21.28}]}, "WEAPON_CONSTRAINT": {"translate": [{"x": -1.74, "y": -2.65, "curve": [0.167, -1.74, 0.23, 76.17, 0.167, -2.65, 0.23, -58.15]}, {"time": 0.6667, "x": 76.17, "y": -58.15}]}, "HAND_LEFT_HANDLE": {"translate": [{"x": 5.8, "y": 10.41, "curve": [0.167, 5.8, 0.23, -77.04, 0.167, 10.41, 0.23, -16.77]}, {"time": 0.6667, "x": -77.04, "y": -16.77}]}, "BACK_BTM": {"rotate": [{"curve": [0.167, 0, 0.23, -5.82]}, {"time": 0.6667, "value": -5.82}]}, "BACK_TOP": {"rotate": [{"curve": [0.167, 0, 0.23, -5.82]}, {"time": 0.6667, "value": -5.82}]}, "FOOT_RIGHT_CONSTRAINT": {"translate": [{"curve": [0.042, 0, 0.125, 20.5, 0.042, 0, 0.125, 9.11]}, {"time": 0.1667, "x": 20.5, "y": 9.11, "curve": [0.208, 20.5, 0.292, 59.22, 0.208, 9.11, 0.292, 0]}, {"time": 0.3333, "x": 59.22}]}, "ANKLE_RIGHT_CONSTRAINT": {"translate": [{"curve": [0.042, 0, 0.125, -9.11, 0.042, 0, 0.125, 20.5]}, {"time": 0.1667, "x": -9.11, "y": 20.5, "curve": [0.208, -9.11, 0.292, 0, 0.208, 20.5, 0.292, 0]}, {"time": 0.3333}]}}}, "quickattack-impact": {"bones": {"HIP": {"rotate": [{"curve": [0.25, 0, 0.75, -1.43]}, {"time": 1, "value": -1.43}], "translate": [{"x": 43.27, "y": -15.35, "curve": [0.017, 43.27, 0.05, -27.33, 0.017, -15.35, 0.05, -35.85]}, {"time": 0.0667, "x": -27.33, "y": -35.85, "curve": [0.3, -27.33, 0.767, 0, 0.3, -35.85, 0.767, -1.68]}, {"time": 1, "y": -1.68}]}, "HAND_RIGHT_CONSTRAINT": {"rotate": [{"time": 1, "value": -1.43}], "translate": [{"x": 118.02, "y": -70.33, "curve": [0.017, 118.02, 0.05, 114.9, 0.017, -70.33, 0.05, 347.57]}, {"time": 0.0667, "x": 114.9, "y": 347.57, "curve": [0.3, 114.9, 0.767, 2.18, 0.3, 347.57, 0.767, -4.38]}, {"time": 1, "x": 2.18, "y": -4.38}], "scale": [{"curve": [0.017, 1, 0.05, 1.712, 0.017, 1, 0.05, 1.712]}, {"time": 0.0667, "x": 1.712, "y": 1.712, "curve": [0.133, 1.712, 0.267, 1, 0.133, 1.712, 0.267, 1]}, {"time": 0.3333}]}, "HAND_RIGHT": {"rotate": [{"value": -21.28, "curve": [0.017, -21.28, 0.05, 2.82]}, {"time": 0.0667, "value": 2.82, "curve": [0.3, 2.82, 0.767, -22.71]}, {"time": 1, "value": -22.71}]}, "HEAD": {"rotate": [{"curve": [0.021, 0, 0.043, -4.99]}, {"time": 0.0667, "value": -14.3, "curve": [0.091, 11.17, 0.192, 26.56]}, {"time": 0.2333, "value": 26.56, "curve": [0.333, 26.56, 0.533, 2.78]}, {"time": 0.6333, "value": 2.78, "curve": [0.725, 2.78, 0.908, 5.87]}, {"time": 1, "value": 5.87}]}, "WEAPON_CONSTRAINT": {"rotate": [{"time": 1, "value": -1.43}], "translate": [{"x": 76.17, "y": -58.15, "curve": [0.06, 63.54, 0.2, 53.6, 0.06, -31.57, 0.2, -10.67]}, {"time": 0.2667, "x": 53.6, "y": -10.67, "curve": [0.45, 53.6, 0.817, -1.74, 0.45, -10.67, 0.817, -2.65]}, {"time": 1, "x": -1.74, "y": -2.65}]}, "HAND_LEFT_HANDLE": {"rotate": [{"time": 1, "value": -1.43}], "translate": [{"x": -77.04, "y": -16.77, "curve": [0.06, -79.3, 0.2, -81.08, 0.06, -17.21, 0.2, -17.55]}, {"time": 0.2667, "x": -81.08, "y": -17.55, "curve": [0.45, -81.08, 0.817, 5.8, 0.45, -17.55, 0.817, 10.41]}, {"time": 1, "x": 5.8, "y": 10.41}]}, "BACK_BTM": {"rotate": [{"value": -5.82, "curve": [0.025, -5.82, 0.075, 13.3]}, {"time": 0.1, "value": 13.3, "curve": [0.158, 13.3, 0.275, -5.82]}, {"time": 0.3333, "value": -5.82, "curve": [0.5, -5.82, 0.833, -1.43]}, {"time": 1, "value": -1.43}]}, "BACK_TOP": {"rotate": [{"value": -5.82, "curve": [0.025, -5.82, 0.075, 13.3]}, {"time": 0.1, "value": 13.3, "curve": [0.158, 13.3, 0.275, -5.82]}, {"time": 0.3333, "value": -5.82, "curve": [0.5, -5.82, 0.833, -1.43]}, {"time": 1, "value": -1.43}]}, "FOOT_RIGHT_CONSTRAINT": {"rotate": [{"time": 1, "value": -1.43}], "translate": [{"x": 59.22, "curve": [0.04, 59.22, 0.085, 47.84, 0.04, 0, 0.085, 0]}, {"time": 0.1333, "x": 28.11, "curve": "stepped"}, {"time": 0.5, "x": 28.11, "curve": [0.65, 22.19, 0.878, 0, 0.65, 0, 0.878, 0]}, {"time": 1}]}, "ANKLE_RIGHT_CONSTRAINT": {"rotate": [{"time": 1, "value": -1.43}]}, "SHOULDER_RIGHT": {"rotate": [{"time": 0.0667, "value": -65.04}], "translate": [{"curve": [0.017, 0, 0.05, -28.25, 0.017, 0, 0.05, 68.9]}, {"time": 0.0667, "x": -28.25, "y": 68.9, "curve": [0.3, -28.25, 0.767, 0, 0.3, 68.9, 0.767, 0]}, {"time": 1}]}, "ARM_RIGHT": {"scale": [{"curve": [0.017, 1, 0.05, 1.712, 0.017, 1, 0.05, 1.712]}, {"time": 0.0667, "x": 1.712, "y": 1.712, "curve": [0.133, 1.712, 0.267, 1, 0.133, 1.712, 0.267, 1]}, {"time": 0.3333}]}, "EYE_LEFT": {"rotate": [{"time": 1, "value": -1.43}]}, "Holder": {"rotate": [{"time": 1, "value": -1.43}]}, "root": {"rotate": [{"time": 1, "value": -1.43}]}, "HORN_LEFT": {"rotate": [{"time": 1, "value": -1.43}]}, "HORN_RIGHT": {"rotate": [{"time": 1, "value": -1.43}]}, "MASK": {"rotate": [{"time": 1, "value": -1.43}]}, "FACE": {"rotate": [{"time": 1, "value": -1.43}]}, "FOOT_LEFT_CONSTRAINT": {"rotate": [{"time": 1, "value": -1.43}]}, "ANKLE_LEFT_CONSTRAINT": {"rotate": [{"time": 1, "value": -1.43}]}, "EYE_RIGHT": {"rotate": [{"time": 1, "value": -1.43}]}}}, "run": {"bones": {"HIP": {"rotate": [{"value": 5.82}], "translate": [{"x": -14.14, "y": -13.83, "curve": [0.044, -17.15, 0.175, -19.28, 0.044, -5.21, 0.175, 0.89]}, {"time": 0.2333, "x": -19.28, "y": 0.89, "curve": [0.275, -19.28, 0.379, -16.4, 0.275, 0.89, 0.379, -7.36]}, {"time": 0.4, "x": -14.14, "y": -13.83, "curve": [0.42, -17.04, 0.575, -19.28, 0.42, -5.54, 0.575, 0.89]}, {"time": 0.6333, "x": -19.28, "y": 0.89, "curve": [0.683, -19.28, 0.803, -17.27, 0.683, 0.89, 0.803, -4.88]}, {"time": 0.8333, "x": -14.14, "y": -13.83}]}, "HAND_RIGHT_CONSTRAINT": {"translate": [{"x": 7.19, "y": -43.35, "curve": [0.1, 7.19, 0.3, 28.12, 0.1, -43.35, 0.3, 70.88]}, {"time": 0.4, "x": 28.12, "y": 70.88, "curve": [0.508, 28.12, 0.725, 7.19, 0.508, 70.88, 0.725, -43.35]}, {"time": 0.8333, "x": 7.19, "y": -43.35}]}, "HAND_RIGHT": {"rotate": [{"value": -12.44, "curve": [0.038, -8.52, 0.073, -5.72]}, {"time": 0.1, "value": -5.72, "curve": [0.208, -5.72, 0.371, -19.59]}, {"time": 0.5333, "value": -33.45, "curve": [0.606, -33.45, 0.73, -20.32]}, {"time": 0.8333, "value": -12.44}]}, "HEAD": {"rotate": [{"value": -1.04, "curve": [0.073, 0.56, 0.151, 2.83]}, {"time": 0.2333, "value": 5.5, "curve": [0.283, 5.5, 0.383, -1.56]}, {"time": 0.4333, "value": -1.56, "curve": [0.483, -1.56, 0.583, 5.5]}, {"time": 0.6333, "value": 5.5, "curve": [0.683, 5.5, 0.783, -1.04]}, {"time": 0.8333, "value": -1.04}], "translate": [{"x": 5.5, "y": 1.86, "curve": [0.058, 5.5, 0.175, 16.48, 0.058, 1.86, 0.175, 0.02]}, {"time": 0.2333, "x": 16.48, "y": 0.02, "curve": [0.283, 16.48, 0.383, 5.5, 0.283, 0.02, 0.383, 1.86]}, {"time": 0.4333, "x": 5.5, "y": 1.86, "curve": [0.483, 5.5, 0.583, 16.48, 0.483, 1.86, 0.583, 0.02]}, {"time": 0.6333, "x": 16.48, "y": 0.02, "curve": [0.683, 16.48, 0.783, 5.5, 0.683, 0.02, 0.783, 1.86]}, {"time": 0.8333, "x": 5.5, "y": 1.86}]}, "FACE": {"translate": [{"x": -1.55, "y": -0.15, "curve": [0.053, -4.52, 0.126, -11.95, 0.053, -0.42, 0.126, -1.12]}, {"time": 0.1667, "x": -11.95, "y": -1.12, "curve": [0.217, -11.95, 0.317, 0, 0.217, -1.12, 0.317, 0]}, {"time": 0.3667, "curve": [0.417, 0, 0.517, -11.95, 0.417, 0, 0.517, -1.12]}, {"time": 0.5667, "x": -11.95, "y": -1.12, "curve": [0.625, -11.95, 0.742, 0, 0.625, -1.12, 0.742, 0]}, {"time": 0.8, "curve": [0.809, 0, 0.821, -0.6, 0.809, 0, 0.821, -0.06]}, {"time": 0.8333, "x": -1.55, "y": -0.15}]}, "BACK_BTM": {"rotate": [{"value": -7.15, "curve": [0.025, -7.67, 0.049, -8.11]}, {"time": 0.0667, "value": -8.11, "curve": [0.125, -8.11, 0.242, -5.49]}, {"time": 0.3, "value": -5.49, "curve": [0.35, -5.49, 0.45, -8.11]}, {"time": 0.5, "value": -8.11, "curve": [0.55, -8.11, 0.65, -5.49]}, {"time": 0.7, "value": -5.49, "curve": [0.733, -5.49, 0.785, -6.41]}, {"time": 0.8333, "value": -7.15}]}, "BACK_TOP": {"rotate": [{"value": 2.68, "curve": [0.025, 4.7, 0.049, 6.39]}, {"time": 0.0667, "value": 6.39, "curve": [0.125, 6.39, 0.242, -3.7]}, {"time": 0.3, "value": -3.7, "curve": [0.35, -3.7, 0.45, 6.39]}, {"time": 0.5, "value": 6.39, "curve": [0.55, 6.39, 0.65, -3.7]}, {"time": 0.7, "value": -3.7, "curve": [0.733, -3.7, 0.785, -0.16]}, {"time": 0.8333, "value": 2.68}], "translate": [{"x": 1.18, "y": 0.09, "curve": [0.025, 0.54, 0.049, 0, 0.025, 0.04, 0.049, 0]}, {"time": 0.0667, "curve": [0.125, 0, 0.242, 3.21, 0.125, 0, 0.242, 0.23]}, {"time": 0.3, "x": 3.21, "y": 0.23, "curve": [0.358, 3.21, 0.475, 0, 0.358, 0.23, 0.475, 0]}, {"time": 0.5333, "curve": [0.575, 0, 0.658, 3.21, 0.575, 0, 0.658, 0.23]}, {"time": 0.7, "x": 3.21, "y": 0.23, "curve": [0.733, 3.21, 0.785, 2.09, 0.733, 0.23, 0.785, 0.15]}, {"time": 0.8333, "x": 1.18, "y": 0.09}]}, "FOOT_RIGHT_CONSTRAINT": {"translate": [{"curve": [0.033, 0, 0.084, 19.75, 0.033, 0, 0.084, 1.5]}, {"time": 0.1333, "x": 36.68, "y": 2.78, "curve": [0.171, 0.05, 0.208, -32, 0.171, 3.35, 0.208, 3.86]}, {"time": 0.2333, "x": -32, "y": 3.86, "curve": [0.275, -32, 0.358, -84.55, 0.275, 3.86, 0.358, 26.28]}, {"time": 0.4, "x": -84.55, "y": 26.28, "curve": [0.418, -84.55, 0.441, -83.45, 0.418, 26.28, 0.441, 18.66]}, {"time": 0.4667, "x": -81.88, "y": 7.76, "curve": [0.477, -78.44, 0.489, -74.5, 0.477, 5.36, 0.489, 2.59]}, {"time": 0.5, "x": -70.38, "y": -0.3, "curve": [0.533, -70.38, 0.609, -62.22, 0.533, -0.3, 0.609, -0.73]}, {"time": 0.6333, "x": -52.77, "y": -1.22, "curve": [0.679, -18.71, 0.783, 0, 0.679, -0.43, 0.783, 0]}, {"time": 0.8333}]}, "ANKLE_RIGHT_CONSTRAINT": {"translate": [{"x": 10.89, "y": 0.86, "curve": [0.033, 10.89, 0.084, -8.62, 0.033, 0.86, 0.084, 18.06]}, {"time": 0.1333, "x": -25.34, "y": 32.8, "curve": [0.171, -12.59, 0.208, -1.43, 0.171, 31.87, 0.208, 31.07]}, {"time": 0.2333, "x": -1.43, "y": 31.07, "curve": [0.275, -1.43, 0.358, 8.04, 0.275, 31.07, 0.358, -9.86]}, {"time": 0.4, "x": 8.04, "y": -9.86, "curve": [0.426, 8.04, 0.462, 8.97, 0.426, -9.86, 0.462, -4.4]}, {"time": 0.5, "x": 10.14, "y": 2.43, "curve": [0.533, 10.14, 0.609, 16.86, 0.533, 2.43, 0.609, 2.12]}, {"time": 0.6333, "x": 24.65, "y": 1.75, "curve": [0.679, 15.77, 0.783, 10.89, 0.679, 1.18, 0.783, 0.86]}, {"time": 0.8333, "x": 10.89, "y": 0.86}]}, "FOOT_LEFT_CONSTRAINT": {"translate": [{"x": -59.56, "y": 28.28, "curve": [0.017, -59.56, 0.05, -50.18, 0.017, 28.28, 0.05, -0.49]}, {"time": 0.0667, "x": -50.18, "y": -0.49, "curve": [0.108, -50.18, 0.213, -19.46, 0.108, -0.49, 0.213, -0.22]}, {"time": 0.2333, "x": 6.71, "curve": [0.26, 17.98, 0.322, 28.64, 0.26, -0.93, 0.322, -1.82]}, {"time": 0.3667, "x": 32.46, "y": -2.14, "curve": [0.379, 33.63, 0.391, 34.28, 0.379, -2.23, 0.391, -2.29]}, {"time": 0.4, "x": 34.28, "y": -2.29, "curve": [0.433, 34.28, 0.483, 51.31, 0.433, -2.29, 0.483, 3]}, {"time": 0.5333, "x": 68.35, "y": 8.28, "curve": [0.571, 37.82, 0.608, 7.28, 0.571, 7.14, 0.608, 6]}, {"time": 0.6333, "x": 7.28, "y": 6, "curve": [0.683, 7.28, 0.783, -67.28, 0.683, 6, 0.783, 28.28]}, {"time": 0.8333, "x": -67.28, "y": 28.28}]}, "ANKLE_LEFT_CONSTRAINT": {"translate": [{"x": 5.14, "y": -27, "curve": [0.017, 5.14, 0.05, 3.42, 0.017, -27, 0.05, 0.07]}, {"time": 0.0667, "x": 3.42, "y": 0.07, "curve": [0.108, 3.42, 0.213, 1.57, 0.108, 0.07, 0.213, 0.03]}, {"time": 0.2333, "curve": [0.26, -0.49, 0.322, -0.96, 0.26, -2.44, 0.322, -4.75]}, {"time": 0.3667, "x": -1.13, "y": -5.58, "curve": [0.379, -1.18, 0.391, -1.21, 0.379, -2.61, 0.391, -0.97]}, {"time": 0.4, "x": -1.21, "y": -0.97, "curve": [0.433, -1.21, 0.483, -13.19, 0.433, -0.97, 0.483, 16.16]}, {"time": 0.5333, "x": -25.17, "y": 33.29, "curve": [0.571, -19.8, 0.608, -14.43, 0.571, 31.07, 0.608, 28.85]}, {"time": 0.6333, "x": -14.43, "y": 28.85, "curve": [0.683, -14.43, 0.783, 5.14, 0.683, 28.85, 0.783, -27]}, {"time": 0.8333, "x": 5.14, "y": -27}]}, "LEG_LEFT": {"translate": [{"x": 8.95, "y": -0.91}]}, "WEAPON_CONTROLLER": {"rotate": [{"value": 22.28, "curve": [0.05, 22.28, 0.15, 24.07]}, {"time": 0.2, "value": 24.07, "curve": [0.258, 24.07, 0.375, 22.28]}, {"time": 0.4333, "value": 22.28, "curve": [0.483, 22.28, 0.583, 24.07]}, {"time": 0.6333, "value": 24.07, "curve": [0.683, 24.07, 0.783, 22.28]}, {"time": 0.8333, "value": 22.28}]}, "WEAPON_CONSTRAINT": {"translate": [{"curve": [0.058, 0, 0.175, 6.12, 0.058, 0, 0.175, -17.9]}, {"time": 0.2333, "x": 6.12, "y": -17.9, "curve": [0.283, 6.12, 0.383, -1.35, 0.283, -17.9, 0.383, -0.08]}, {"time": 0.4333, "x": -1.35, "y": -0.08}]}, "HAND_LEFT_HANDLE": {"translate": [{"curve": [0.058, 0, 0.175, 3.98, 0.058, 0, 0.175, 11.27]}, {"time": 0.2333, "x": 3.98, "y": 11.27, "curve": [0.283, 3.98, 0.383, -14.28, 0.283, 11.27, 0.383, 29.48]}, {"time": 0.4333, "x": -14.28, "y": 29.48}]}, "SHOULDER_LEFT": {"rotate": [{"value": -83.05, "curve": [0.047, -85.81, 0.101, -89.94]}, {"time": 0.1333, "value": -89.94, "curve": [0.192, -89.94, 0.308, -80.33]}, {"time": 0.3667, "value": -80.33, "curve": [0.417, -80.33, 0.517, -89.94]}, {"time": 0.5667, "value": -89.94, "curve": [0.625, -89.94, 0.742, -80.33]}, {"time": 0.8, "value": -80.33, "curve": [0.809, -80.33, 0.821, -81.5]}, {"time": 0.8333, "value": -83.05}]}, "ARM_LEFT": {"rotate": [{"value": -58.71, "curve": [0.05, -58.71, 0.15, -55.77]}, {"time": 0.2, "value": -55.77, "curve": [0.258, -55.77, 0.375, -58.71]}, {"time": 0.4333, "value": -58.71, "curve": [0.483, -58.71, 0.583, -55.77]}, {"time": 0.6333, "value": -55.77, "curve": [0.683, -55.77, 0.783, -58.71]}, {"time": 0.8333, "value": -58.71}]}}, "ik": {"HAND_LEFT_CONSTRAINT": [{"mix": 0, "bendPositive": false}], "WEAPON_CONSTRAINT": [{"mix": 0}]}, "events": [{"time": 0.1, "name": "footsteps"}, {"time": 0.5, "name": "footsteps"}]}, "sleeping": {"slots": {"Eyes": {"attachment": [{"name": "Eyes_Closed"}]}}, "bones": {"MASK": {"rotate": [{"value": -1.43}]}, "HIP": {"rotate": [{"value": -1.43}], "translate": [{"x": 22.58, "y": -46.16}]}, "HAND_RIGHT_CONSTRAINT": {"rotate": [{"value": -1.43}]}, "HAND_RIGHT": {"rotate": [{"value": -24.91}]}, "HEAD": {"rotate": [{"value": 17.91, "curve": [0.203, 19.02, 0.396, 19.94]}, {"time": 0.5333, "value": 19.94, "curve": [0.867, 19.94, 1.533, 14.42]}, {"time": 1.8667, "value": 14.42, "curve": [2.063, 14.42, 2.376, 16.36]}, {"time": 2.6667, "value": 17.91}], "translate": [{"x": -22.25, "y": -0.41, "curve": [0.333, -22.25, 0.833, -21.23, 0.333, -0.41, 0.833, 0.93]}, {"time": 1.3333, "x": -20.21, "y": 2.28, "curve": [1.833, -21.23, 2.333, -22.25, 1.833, 0.93, 2.333, -0.41]}, {"time": 2.6667, "x": -22.25, "y": -0.41}]}, "WEAPON_CONSTRAINT": {"rotate": [{"value": -1.43}], "translate": [{"x": 2.6, "y": -1.14, "curve": [0.048, 2.71, 0.093, 2.78, 0.048, -1.17, 0.093, -1.18]}, {"time": 0.1333, "x": 2.78, "y": -1.18, "curve": [0.467, 2.78, 1.133, -1.35, 0.467, -1.18, 1.133, -0.08]}, {"time": 1.4667, "x": -1.35, "y": -0.08, "curve": [1.761, -1.35, 2.32, 1.94, 1.761, -0.08, 2.32, -0.96]}, {"time": 2.6667, "x": 2.6, "y": -1.14}]}, "HAND_LEFT_HANDLE": {"rotate": [{"value": -1.43}], "translate": [{"x": 4.94, "y": 11.22}]}, "root": {"rotate": [{"value": -1.43}]}, "Holder": {"rotate": [{"value": -1.43}]}, "BACK_BTM": {"rotate": [{"value": -19.7, "curve": [0.333, -19.7, 1, -17.43]}, {"time": 1.3333, "value": -17.43, "curve": [1.667, -17.43, 2.333, -19.7]}, {"time": 2.6667, "value": -19.7}]}, "BACK_TOP": {"rotate": [{"value": 24.22, "curve": [0.333, 24.22, 1, 16.79]}, {"time": 1.3333, "value": 16.79, "curve": [1.667, 16.79, 2.333, 24.22]}, {"time": 2.6667, "value": 24.22}]}, "FACE": {"rotate": [{"value": -1.43}]}, "FOOT_RIGHT_CONSTRAINT": {"rotate": [{"value": -1.43}], "translate": [{"x": -48.26, "y": 19.2}]}, "ANKLE_RIGHT_CONSTRAINT": {"rotate": [{"value": -1.43}], "translate": [{"x": -24.49, "y": -24.21}]}, "FOOT_LEFT_CONSTRAINT": {"rotate": [{"value": -1.43}], "translate": [{"x": -29.59, "y": 24.19}]}, "ANKLE_LEFT_CONSTRAINT": {"rotate": [{"value": -1.43}], "translate": [{"x": -24.88, "y": -18.98}]}, "HORN_RIGHT": {"rotate": [{"value": -1.43}]}, "HORN_LEFT": {"rotate": [{"value": -1.43}]}, "EYE_LEFT": {"rotate": [{"value": -1.43}]}, "EYE_RIGHT": {"rotate": [{"value": -1.43}]}, "ARM_RIGHT": {"rotate": [{"value": -21.13, "curve": [0.509, -23.3, 0.975, -24.85]}, {"time": 1.3333, "value": -24.85, "curve": [1.667, -24.85, 2.333, -21.13]}, {"time": 2.6667, "value": -21.13}]}, "SHOULDER_RIGHT": {"rotate": [{"value": -4.67, "curve": [0.461, -3.83, 0.906, -3.18]}, {"time": 1.3333, "value": -2.74, "curve": [1.798, -4, 2.243, -4.67]}, {"time": 2.6667, "value": -4.67}]}, "SHOULDER_LEFT": {"rotate": [{"value": -4.29, "curve": [0.333, -4.29, 1, -9.71]}, {"time": 1.3333, "value": -9.71, "curve": [1.667, -9.71, 2.333, -4.29]}, {"time": 2.6667, "value": -4.29}]}, "WEAPON_CONTROLLER": {"rotate": [{"value": 22.31, "curve": [0.333, 22.35, 0.758, 22.43]}, {"time": 1, "value": 22.43, "curve": [1.333, 22.43, 2, 22.28]}, {"time": 2.3333, "value": 22.28, "curve": [2.425, 22.28, 2.54, 22.29]}, {"time": 2.6667, "value": 22.31}]}, "ARM_LEFT": {"rotate": [{"value": -57.93, "curve": [0.347, -55.6, 0.882, -46.28]}, {"time": 1.1667, "value": -46.28, "curve": [1.5, -46.28, 2.167, -58.71]}, {"time": 2.5, "value": -58.71, "curve": [2.55, -58.71, 2.606, -58.43]}, {"time": 2.6667, "value": -57.93}]}}, "ik": {"HAND_LEFT_CONSTRAINT": [{"mix": 0, "bendPositive": false}], "HAND_RIGHT": [{"mix": 0, "bendPositive": false}], "WEAPON_CONSTRAINT": [{"mix": 0}]}}, "summon": {"slots": {"Brute/GoatCult/Axe_Glow": {"rgba": [{"time": 0.7333, "color": "ffffffff", "curve": [0.807, 1, 1.008, 1, 0.807, 1, 1.008, 1, 0.807, 1, 1.008, 1, 0.807, 0.41, 1.008, 0]}, {"time": 1.1, "color": "ffffff00"}], "attachment": [{"time": 0.7333, "name": "Brute/GoatCult/Axe_Glow"}]}, "Brute/GoatCult/SummonSparks": {"rgba": [{"time": 0.7333, "color": "ffffffff", "curve": [0.858, 1, 1.108, 1, 0.858, 1, 1.108, 1, 0.858, 1, 1.108, 1, 0.858, 1, 1.108, 0]}, {"time": 1.2333, "color": "ffffff00"}], "attachment": [{"time": 0.7333, "name": "Brute/GoatCult/SummonSparks"}]}, "Brute/GoatCult/SummonSparks2": {"rgba": [{"time": 0.7333, "color": "ffffffd7", "curve": [0.858, 1, 1.108, 1, 0.858, 1, 1.108, 1, 0.858, 1, 1.108, 1, 0.858, 0.84, 1.108, 0]}, {"time": 1.2333, "color": "ffffff00"}], "attachment": [{"time": 0.7333, "name": "Brute/GoatCult/SummonSparks"}]}, "Brute/pentagram/Dash": {"attachment": [{"time": 0.5, "name": "Brute/pentagram/Dash"}, {"time": 2.0333}]}, "Brute/pentagram/Dash2": {"attachment": [{"time": 0.5, "name": "Brute/pentagram/Dash"}, {"time": 2.0333}]}, "Brute/pentagram/Dash3": {"attachment": [{"time": 0.5, "name": "Brute/pentagram/Dash"}, {"time": 2.0333}]}, "Brute/pentagram/Dash4": {"attachment": [{"time": 0.5, "name": "Brute/pentagram/Dash"}, {"time": 2.0333}]}, "Brute/pentagram/Dash5": {"attachment": [{"time": 0.5, "name": "Brute/pentagram/Dash"}, {"time": 2.0333}]}, "Brute/pentagram/Dash6": {"attachment": [{"time": 0.5, "name": "Brute/pentagram/Dash"}, {"time": 2.0333}]}, "Eyes": {"attachment": [{"time": 0.2, "name": "Eyes_Squint"}, {"time": 0.7667, "name": "Eyes"}]}, "Ring0": {"attachment": [{"time": 0.5, "name": "Brute/pentagram/Ring"}, {"time": 2.0333}]}, "Ring1": {"attachment": [{"time": 0.5, "name": "Brute/pentagram/Ring Small"}, {"time": 2.0333}]}, "Skull0": {"attachment": [{"time": 0.5, "name": "Brute/pentagram/Skull"}, {"time": 2.0333}]}, "Skull1": {"attachment": [{"time": 0.5, "name": "Brute/pentagram/Skull"}, {"time": 2.0333}]}, "Skull2": {"attachment": [{"time": 0.5, "name": "Brute/pentagram/Skull"}, {"time": 2.0333}]}, "Skull3": {"attachment": [{"time": 0.5, "name": "Brute/pentagram/Skull"}, {"time": 2.0333}]}, "Skull4": {"attachment": [{"time": 0.5, "name": "Brute/pentagram/Skull"}, {"time": 2.0333}]}, "Skull5": {"attachment": [{"time": 0.5, "name": "Brute/pentagram/Skull"}, {"time": 2.0333}]}, "Tri": {"attachment": [{"time": 0.5, "name": "Brute/pentagram/Triangle"}, {"time": 2.0333}]}, "WEAPON": {"rgba": [{"time": 0.7333, "color": "ff0000ff", "curve": [0.792, 1, 0.908, 1, 0.792, 0, 0.908, 1, 0.792, 0, 0.908, 1, 0.792, 1, 0.908, 1]}, {"time": 0.9667, "color": "ffffffff"}]}}, "bones": {"Glow": {"scale": [{"time": 0.7333, "curve": [0.807, 1.235, 1.008, 1.398, 0.807, 1.414, 1.008, 1.702]}, {"time": 1.1, "x": 1.398, "y": 1.702}]}, "Sparks": {"translate": [{"time": 0.7333, "x": -835.88, "y": -102.57, "curve": [0.797, -835.88, 1.108, -835.88, 0.797, -13.81, 1.108, 74.95]}, {"time": 1.2333, "x": -835.88, "y": 74.95}], "scale": [{"time": 0.7333, "x": 1.117, "y": 1.117}]}, "Sparks2": {"translate": [{"time": 0.7333, "x": -809.27, "y": -160.87, "curve": [0.797, -809.27, 1.108, -809.27, 0.797, -30.04, 1.108, 100.8]}, {"time": 1.2333, "x": -809.27, "y": 100.8}], "scale": [{"time": 0.7333, "x": 1.117, "y": 1.117}]}, "Peg0": {"translate": [{"time": 0.5, "x": -20.28, "y": 11.71}, {"time": 0.6667, "x": 21.54, "y": -12.44}, {"time": 0.8333, "x": -20.28, "y": 11.71}, {"time": 1, "x": 21.54, "y": -12.44}, {"time": 1.1667, "x": -20.28, "y": 11.71}, {"time": 1.3333, "x": 21.54, "y": -12.44}, {"time": 1.5, "x": -20.28, "y": 11.71}, {"time": 1.6667, "x": 21.54, "y": -12.44}, {"time": 1.8333, "x": -20.28, "y": 11.71}]}, "Peg1": {"translate": [{"time": 0.5, "x": -19.92, "y": -11.5}, {"time": 0.6667, "x": 20.78, "y": 12}, {"time": 0.8333, "x": -19.92, "y": -11.5}, {"time": 1, "x": 20.78, "y": 12}, {"time": 1.1667, "x": -19.92, "y": -11.5}, {"time": 1.3333, "x": 20.78, "y": 12}, {"time": 1.5, "x": -19.92, "y": -11.5}, {"time": 1.6667, "x": 20.78, "y": 12}, {"time": 1.8333}]}, "Peg2": {"translate": [{"time": 0.5, "y": -23}, {"time": 0.6667, "y": 23}, {"time": 0.8333, "y": -23}, {"time": 1, "y": 23}, {"time": 1.1667, "y": -23}, {"time": 1.3333, "y": 23}, {"time": 1.5, "y": -23}, {"time": 1.6667, "y": 23}, {"time": 1.8333, "y": -23}]}, "Peg3": {"translate": [{"time": 0.5, "y": 23}, {"time": 0.6667, "y": -23}, {"time": 0.8333, "y": 23}, {"time": 1, "y": -23}, {"time": 1.1667, "y": 23}, {"time": 1.3333, "y": -23}, {"time": 1.5, "y": 23}, {"time": 1.6667, "y": -23}, {"time": 1.8333, "y": 23}]}, "Peg4": {"translate": [{"time": 0.5, "x": 19.92, "y": 11.5}, {"time": 0.6667, "x": -19.92, "y": -11.5}, {"time": 0.8333, "x": 19.92, "y": 11.5}, {"time": 1, "x": -19.92, "y": -11.5}, {"time": 1.1667, "x": 19.92, "y": 11.5}, {"time": 1.3333, "x": -19.92, "y": -11.5}, {"time": 1.5, "x": 19.92, "y": 11.5}, {"time": 1.6667, "x": -19.92, "y": -11.5}, {"time": 1.8333, "x": 19.92, "y": 11.5}]}, "Peg5": {"translate": [{"time": 0.5, "x": 19.92, "y": -11.5}, {"time": 0.6667, "x": -19.92, "y": 11.5}, {"time": 0.8333, "x": 19.92, "y": -11.5}, {"time": 1, "x": -19.92, "y": 11.5}, {"time": 1.1667, "x": 19.92, "y": -11.5}, {"time": 1.3333, "x": -19.92, "y": 11.5}, {"time": 1.5, "x": 19.92, "y": -11.5}, {"time": 1.6667, "x": -19.92, "y": 11.5}, {"time": 1.8333, "x": 19.92, "y": -11.5}]}, "Ring1": {"rotate": [{"time": 0.5}, {"time": 1.8333, "value": 62.21}]}, "Skull0": {"rotate": [{"time": 0.5}, {"time": 1.8333, "value": -60}], "scale": [{"time": 0.5, "x": 0.867, "y": 0.867}, {"time": 0.6667, "x": 1.088, "y": 1.088}, {"time": 0.8333, "x": 0.867, "y": 0.867}, {"time": 1, "x": 1.088, "y": 1.088}, {"time": 1.1667, "x": 0.867, "y": 0.867}, {"time": 1.3333, "x": 1.088, "y": 1.088}, {"time": 1.5, "x": 0.867, "y": 0.867}, {"time": 1.6667, "x": 1.088, "y": 1.088}, {"time": 1.8333, "x": 0.867, "y": 0.867}]}, "Skull1": {"rotate": [{"time": 0.5}, {"time": 1.8333, "value": -60}], "scale": [{"time": 0.5, "x": 0.867, "y": 0.867}, {"time": 0.6667, "x": 1.088, "y": 1.088}, {"time": 0.8333, "x": 0.867, "y": 0.867}, {"time": 1, "x": 1.088, "y": 1.088}, {"time": 1.1667, "x": 0.867, "y": 0.867}, {"time": 1.3333, "x": 1.088, "y": 1.088}, {"time": 1.5, "x": 0.867, "y": 0.867}, {"time": 1.6667, "x": 1.088, "y": 1.088}, {"time": 1.8333, "x": 0.867, "y": 0.867}]}, "Skull2": {"rotate": [{"time": 0.5}, {"time": 1.8333, "value": -60}], "scale": [{"time": 0.5, "x": 0.867, "y": 0.867}, {"time": 0.6667, "x": 1.088, "y": 1.088}, {"time": 0.8333, "x": 0.867, "y": 0.867}, {"time": 1, "x": 1.088, "y": 1.088}, {"time": 1.1667, "x": 0.867, "y": 0.867}, {"time": 1.3333, "x": 1.088, "y": 1.088}, {"time": 1.5, "x": 0.867, "y": 0.867}, {"time": 1.6667, "x": 1.088, "y": 1.088}, {"time": 1.8333, "x": 0.867, "y": 0.867}]}, "Skull3": {"rotate": [{"time": 0.5}, {"time": 1.8333, "value": -60}], "scale": [{"time": 0.5, "x": 0.867, "y": 0.867}, {"time": 0.6667, "x": 1.088, "y": 1.088}, {"time": 0.8333, "x": 0.867, "y": 0.867}, {"time": 1, "x": 1.088, "y": 1.088}, {"time": 1.1667, "x": 0.867, "y": 0.867}, {"time": 1.3333, "x": 1.088, "y": 1.088}, {"time": 1.5, "x": 0.867, "y": 0.867}, {"time": 1.6667, "x": 1.088, "y": 1.088}, {"time": 1.8333, "x": 0.867, "y": 0.867}]}, "Skull4": {"rotate": [{"time": 0.5}, {"time": 1.8333, "value": -60}], "scale": [{"time": 0.5, "x": 0.867, "y": 0.867}, {"time": 0.6667, "x": 1.088, "y": 1.088}, {"time": 0.8333, "x": 0.867, "y": 0.867}, {"time": 1, "x": 1.088, "y": 1.088}, {"time": 1.1667, "x": 0.867, "y": 0.867}, {"time": 1.3333, "x": 1.088, "y": 1.088}, {"time": 1.5, "x": 0.867, "y": 0.867}, {"time": 1.6667, "x": 1.088, "y": 1.088}, {"time": 1.8333, "x": 0.867, "y": 0.867}]}, "Skull5": {"rotate": [{"time": 0.5}, {"time": 1.8333, "value": -60}]}, "Tri": {"rotate": [{"time": 0.5, "value": -11.99}, {"time": 1.1667, "value": 9.93}, {"time": 1.8333, "value": -11.99}], "scale": [{"time": 0.5}, {"time": 0.8333, "x": 1.203, "y": 1.203}, {"time": 1.1667}, {"time": 1.5, "x": 1.203, "y": 1.203}, {"time": 1.8333}]}, "HIP": {"rotate": [{"time": 0.4333, "curve": [0.686, 0, 0.658, -18.73]}, {"time": 0.7333, "value": -18.73, "curve": "stepped"}, {"time": 1.2333, "value": -18.73, "curve": [1.475, -18.73, 1.958, -1.43]}, {"time": 2.2, "value": -1.43}], "translate": [{"y": -1.68, "curve": [0.108, 0, 0.325, 0, 0.108, -1.68, 0.325, -24.41]}, {"time": 0.4333, "y": -24.41, "curve": [0.686, 0, 0.658, 29.68, 0.686, -24.41, 0.658, 13.58]}, {"time": 0.7333, "x": 29.68, "y": 13.58, "curve": "stepped"}, {"time": 1.2333, "x": 29.68, "y": 13.58, "curve": [1.383, 29.68, 1.683, 0.12, 1.383, 13.58, 1.683, -9.39]}, {"time": 1.8333, "x": 0.12, "y": -9.39, "curve": [1.858, 0.12, 1.908, 2.34, 1.858, -9.39, 1.908, -29.48]}, {"time": 1.9333, "x": 2.34, "y": -29.48, "curve": [2, 2.34, 2.133, 0, 2, -29.48, 2.133, -1.68]}, {"time": 2.2, "y": -1.68}]}, "HAND_RIGHT_CONSTRAINT": {"translate": [{"x": 2.18, "y": -4.38, "curve": [0.108, 2.18, 0.325, 65.95, 0.108, -4.38, 0.325, 124.5]}, {"time": 0.4333, "x": 65.95, "y": 124.5, "curve": [0.686, 65.95, 0.658, 105.06, 0.686, 124.5, 0.658, -24.71]}, {"time": 0.7333, "x": 105.06, "y": -24.71, "curve": "stepped"}, {"time": 1.2333, "x": 105.06, "y": -24.71, "curve": [1.383, 105.06, 1.683, 2.18, 1.383, -24.71, 1.683, -4.38]}, {"time": 1.8333, "x": 2.18, "y": -4.38}]}, "HAND_RIGHT": {"rotate": [{"value": -21.28, "curve": "stepped"}, {"time": 0.4333, "value": -21.28, "curve": [0.686, -21.28, 0.658, 12.41]}, {"time": 0.7333, "value": 12.41, "curve": "stepped"}, {"time": 1.2333, "value": 12.41, "curve": [1.383, 12.41, 1.683, -21.28]}, {"time": 1.8333, "value": -21.28}]}, "HEAD": {"rotate": [{"curve": [0.105, 0, 0.291, 19.61]}, {"time": 0.4333, "value": 28.66, "curve": [0.574, 28.66, 0.558, 34.27]}, {"time": 0.6, "value": 34.27, "curve": [0.758, 34.27, 1.233, 16.25]}, {"time": 1.2333, "value": 9.16, "curve": [1.383, 9.16, 1.683, 9.08]}, {"time": 1.8333, "value": 9.08, "curve": [1.922, 9.08, 2.08, 2.87]}, {"time": 2.2}], "translate": [{"time": 0.5667, "curve": [0.609, 7.89, 1.056, 14.82, 0.609, -2.54, 1.056, -4.78]}, {"time": 1.2333, "x": 14.82, "y": -4.78, "curve": [1.461, 7.09, 1.681, 0, 1.461, -2.29, 1.681, 0]}, {"time": 1.8333}]}, "WEAPON_CONSTRAINT": {"translate": [{"x": -1.74, "y": -2.65, "curve": [0.058, -1.74, 0.175, -23.95, 0.058, -2.65, 0.175, 20.02]}, {"time": 0.2333, "x": -23.95, "y": 20.02, "curve": "stepped"}, {"time": 0.4333, "x": -23.95, "y": 20.02, "curve": [0.686, -23.95, 0.658, 278.29, 0.686, 20.02, 0.658, 98.39]}, {"time": 0.7333, "x": 278.29, "y": 98.39, "curve": [0.75, 278.29, 0.783, 282.86, 0.75, 98.39, 0.783, 116.13]}, {"time": 0.8, "x": 282.86, "y": 116.13, "curve": [0.817, 282.86, 0.85, 278.29, 0.817, 116.13, 0.85, 98.39]}, {"time": 0.8667, "x": 278.29, "y": 98.39, "curve": [0.883, 278.29, 0.917, 282.86, 0.883, 98.39, 0.917, 116.13]}, {"time": 0.9333, "x": 282.86, "y": 116.13, "curve": [0.95, 282.86, 0.983, 278.29, 0.95, 116.13, 0.983, 98.39]}, {"time": 1, "x": 278.29, "y": 98.39, "curve": [1.017, 278.29, 1.05, 282.86, 1.017, 98.39, 1.05, 116.13]}, {"time": 1.0667, "x": 282.86, "y": 116.13, "curve": [1.083, 282.86, 1.117, 278.29, 1.083, 116.13, 1.117, 98.39]}, {"time": 1.1333, "x": 278.29, "y": 98.39, "curve": [1.15, 278.29, 1.183, 282.86, 1.15, 98.39, 1.183, 116.13]}, {"time": 1.2, "x": 282.86, "y": 116.13, "curve": [1.217, 282.86, 1.25, 278.29, 1.217, 116.13, 1.25, 98.39]}, {"time": 1.2667, "x": 278.29, "y": 98.39, "curve": [1.283, 278.29, 1.317, 282.86, 1.283, 98.39, 1.317, 116.13]}, {"time": 1.3333, "x": 282.86, "y": 116.13, "curve": [1.35, 282.86, 1.383, 278.29, 1.35, 116.13, 1.383, 98.39]}, {"time": 1.4, "x": 278.29, "y": 98.39, "curve": "stepped"}, {"time": 1.5, "x": 278.29, "y": 98.39, "curve": [1.583, 278.29, 1.75, -1.74, 1.583, 98.39, 1.75, -2.65]}, {"time": 1.8333, "x": -1.74, "y": -2.65}]}, "HAND_LEFT_HANDLE": {"translate": [{"x": 5.8, "y": 10.41, "curve": [0.108, 5.8, 0.325, -63.45, 0.108, 10.41, 0.325, 10.34]}, {"time": 0.4333, "x": -63.45, "y": 10.34, "curve": [0.686, -63.45, 0.658, 56.84, 0.686, 10.34, 0.658, 54.96]}, {"time": 0.7333, "x": 56.84, "y": 54.96, "curve": "stepped"}, {"time": 1.5, "x": 56.84, "y": 54.96, "curve": [1.583, 56.84, 1.75, 5.8, 1.583, 54.96, 1.75, 10.41]}, {"time": 1.8333, "x": 5.8, "y": 10.41}]}, "BACK_BTM": {"rotate": [{"curve": [0.067, 6.36, 0.175, 7.54]}, {"time": 0.2333, "value": 7.54, "curve": "stepped"}, {"time": 1.2333, "value": 7.54, "curve": [1.383, 7.54, 1.683, 0]}, {"time": 1.8333}]}, "BACK_TOP": {"rotate": [{"curve": [0.067, 13.8, 0.175, 16.36]}, {"time": 0.2333, "value": 16.36, "curve": [0.338, 16.36, 0.383, 10.33]}, {"time": 0.4333, "value": 10.33, "curve": [0.686, 10.33, 0.658, -13.55]}, {"time": 0.7333, "value": -13.55, "curve": "stepped"}, {"time": 1.2333, "value": -13.55, "curve": [1.383, -13.55, 1.683, 0]}, {"time": 1.8333}]}, "FOOT_LEFT_CONSTRAINT": {"translate": [{"time": 0.6, "curve": [0.617, 0, 0.642, -1.19, 0.617, 0, 0.642, 14.12]}, {"time": 0.6667, "x": -2.38, "y": 28.25, "curve": [0.692, -3.56, 0.717, -4.75, 0.692, 39.22, 0.717, 50.19]}, {"time": 0.7333, "x": -4.75, "y": 50.19}]}, "LEG_LEFT": {"rotate": [{"time": 0.4333, "value": -41.97, "curve": [1.108, -41.97, 1.033, -47.11]}, {"time": 1.2333, "value": -47.11}], "translate": [{"curve": [0.127, 0, 0.273, -0.55, 0.127, 0, 0.273, 0.76]}, {"time": 0.4333, "x": -1.48, "y": 2.05, "curve": "stepped"}, {"time": 1.2333, "x": -1.48, "y": 2.05, "curve": [1.383, -1.48, 1.683, 0, 1.383, 2.05, 1.683, 0]}, {"time": 1.8333}]}, "root": {"scale": [{"time": 0.0667, "curve": [0.092, 1, 0.142, 1.095, 0.092, 1, 0.142, 0.919]}, {"time": 0.1667, "x": 1.095, "y": 0.919, "curve": [0.208, 1.095, 0.292, 1.011, 0.208, 0.919, 0.292, 0.984]}, {"time": 0.3333, "x": 1.011, "y": 0.984, "curve": [0.358, 1.011, 0.4, 1.035, 0.358, 0.984, 0.4, 0.956]}, {"time": 0.4333, "x": 1.046, "y": 0.943, "curve": [0.49, 1.046, 0.483, 1.054, 0.49, 0.943, 0.483, 0.933]}, {"time": 0.5, "x": 1.054, "y": 0.933, "curve": "stepped"}, {"time": 0.6, "x": 1.054, "y": 0.933, "curve": [0.633, 1.054, 0.7, 0.857, 0.633, 0.933, 0.7, 1.218]}, {"time": 0.7333, "x": 0.857, "y": 1.218, "curve": [0.783, 0.857, 0.883, 1.188, 0.783, 1.218, 0.883, 0.811]}, {"time": 0.9333, "x": 1.188, "y": 0.811, "curve": [1.008, 1.188, 1.158, 1, 1.008, 0.811, 1.158, 1]}, {"time": 1.2333}]}, "ANKLE_LEFT_CONSTRAINT": {"translate": [{"time": 0.6, "curve": [0.617, 0, 0.642, 0.52, 0.617, 0, 0.642, 5.48]}, {"time": 0.6667, "x": 1.05, "y": 10.97, "curve": [0.692, 2.88, 0.717, 4.71, 0.692, 5.48, 0.717, 0]}, {"time": 0.7333, "x": 4.71}]}, "SHOULDER_RIGHT": {"rotate": [{"time": 0.4333, "value": -18.43, "curve": [1.108, -18.43, 1.033, 37.64]}, {"time": 1.2333, "value": 37.64}]}, "ARM_RIGHT": {"rotate": [{"time": 0.4333, "value": -30.43, "curve": [1.108, -30.43, 1.033, -111.2]}, {"time": 1.2333, "value": -111.2}]}, "SHOULDER_LEFT": {"rotate": [{"time": 0.4333, "value": -53.46, "curve": [1.108, -53.46, 1.033, -112.4]}, {"time": 1.2333, "value": -112.4, "curve": [1.292, -112.4, 1.408, -24.57]}, {"time": 1.4667, "value": -24.57, "curve": [1.525, -24.57, 1.642, -133.34]}, {"time": 1.7, "value": -133.34, "curve": [1.742, -133.34, 1.825, -83.05]}, {"time": 1.8667, "value": -83.05}]}, "ARM_LEFT": {"rotate": [{"time": 0.4333, "value": -84.78, "curve": [1.108, -84.78, 1.033, 32.24]}, {"time": 1.2333, "value": 32.24, "curve": [1.292, 32.24, 1.408, -24.19]}, {"time": 1.4667, "value": -24.19, "curve": [1.525, -24.19, 1.642, -21.93]}, {"time": 1.7, "value": -21.93, "curve": [1.742, -21.93, 1.825, -63.49]}, {"time": 1.8667, "value": -63.49, "curve": [1.908, -63.49, 1.992, -58.71]}, {"time": 2.0333, "value": -58.71}]}, "WEAPON_CONTROLLER": {"rotate": [{"time": 0.4333, "value": 37.27, "curve": [1.108, 37.27, 1.033, 40.85]}, {"time": 1.2333, "value": 40.85, "curve": [1.292, 40.85, 1.408, 75.57]}, {"time": 1.4667, "value": 75.57, "curve": [1.55, 75.57, 1.717, 22.28]}, {"time": 1.8, "value": 22.28, "curve": [1.817, 22.28, 1.85, 18.92]}, {"time": 1.8667, "value": 18.92, "curve": [1.908, 18.92, 1.992, 22.28]}, {"time": 2.0333, "value": 22.28}], "scale": [{"time": 0.7, "curve": [0.725, 1, 0.775, 1.361, 0.725, 1, 0.775, 1.159]}, {"time": 0.8, "x": 1.361, "y": 1.159, "curve": [0.85, 1.361, 0.95, 0.89, 0.85, 1.159, 0.95, 0.89]}, {"time": 1, "x": 0.89, "y": 0.89, "curve": [1.058, 0.89, 1.175, 1, 1.058, 0.89, 1.175, 1]}, {"time": 1.2333}]}, "LEG_RIGHT": {"rotate": [{"time": 0.4333, "value": -35.9, "curve": [1.108, -35.9, 1.033, -4.7]}, {"time": 1.2333, "value": -4.7}]}, "LEG_RIGHT_BTM": {"rotate": [{"time": 0.4333, "value": 85.6, "curve": [1.108, 85.6, 1.033, 7.59]}, {"time": 1.2333, "value": 7.59}]}, "FOOT_RIGHT": {"rotate": [{"time": 0.4333, "value": -50.43, "curve": [1.108, -50.43, 1.033, 13.86]}, {"time": 1.2333, "value": 13.86}]}, "LEG_LEFT_BTM": {"rotate": [{"time": 0.4333, "value": 87.63, "curve": [1.108, 87.63, 1.033, 72.85]}, {"time": 1.2333, "value": 72.85}]}, "FOOT_LEFT": {"rotate": [{"time": 0.4333, "value": -44.55, "curve": [1.108, -44.55, 1.033, -6.84]}, {"time": 1.2333, "value": -6.84}]}, "BACK_TOP2": {"translate": [{"curve": [0.108, 0, 0.325, 53.16, 0.108, 0, 0.325, 23.72]}, {"time": 0.4333, "x": 53.16, "y": 23.72, "curve": "stepped"}, {"time": 1.2333, "x": 53.16, "y": 23.72, "curve": [1.383, 53.16, 1.683, 0, 1.383, 23.72, 1.683, 0]}, {"time": 1.8333}]}, "Pegs": {"rotate": [{"time": 0.5}, {"time": 1.8333, "value": 60}]}, "SpawningPentagram": {"translate": [{"time": 0.5, "x": -1070.39, "y": -317.51}, {"time": 0.7333, "x": -1081.7, "y": -49.86}, {"time": 1, "x": -1087.18, "y": -97.12}, {"time": 1.3, "x": -1090.92, "y": -77.25}, {"time": 1.5, "x": -1097.45, "y": -86.22}], "scale": [{"time": 0.5, "x": 0.184, "y": 0.184}, {"time": 0.6333, "curve": "stepped"}, {"time": 1.3667, "curve": [1.4, 1, 1.495, 0.904, 1.4, 1, 1.495, 0.904]}, {"time": 1.5, "x": 0.806, "y": 0.806, "curve": [1.573, 0.806, 1.597, 1.248, 1.573, 0.806, 1.597, 1.248]}, {"time": 1.6667, "x": 1.248, "y": 1.248, "curve": [1.775, 1.155, 2, 0.47, 1.775, 1.155, 2, 0.47]}, {"time": 2, "x": 0.055, "y": 0.055}]}}}, "throw": {"slots": {"Eyes": {"attachment": [{"time": 0.7667, "name": "Eyes_Squint"}, {"time": 1.7667, "name": "Eyes"}]}, "ThrowRock": {"attachment": [{"time": 1.8, "name": "Brute/Rock"}, {"time": 2.5}]}, "WEAPON": {"attachment": [{"time": 0.7333, "name": "<PERSON><PERSON>_Stuck"}, {"time": 2.5, "name": "Axe"}]}}, "bones": {"HIP": {"rotate": [{"value": -1.43, "curve": [0.042, -1.43, 0.125, 0]}, {"time": 0.1667, "curve": [0.292, 0, 0.542, 2.52]}, {"time": 0.6667, "value": 2.52, "curve": "stepped"}, {"time": 3, "value": 2.52, "curve": [3.05, 2.52, 3.15, 0]}, {"time": 3.2}], "translate": [{"y": -1.68, "curve": [0.042, 0, 0.125, 0, 0.042, -1.68, 0.125, -8.21]}, {"time": 0.1667, "y": -8.21, "curve": [0.292, 0, 0.542, 16.65, 0.292, -8.21, 0.542, -0.42]}, {"time": 0.6667, "x": 16.65, "y": -0.42, "curve": [0.692, 16.65, 0.742, -10.6, 0.692, -0.42, 0.742, -11.5]}, {"time": 0.7667, "x": -10.6, "y": -11.5, "curve": [0.792, -10.6, 0.842, -10.08, 0.792, -11.5, 0.842, -23.32]}, {"time": 0.8667, "x": -10.08, "y": -23.32, "curve": [0.917, -10.08, 1.017, 11.52, 0.917, -23.32, 1.017, -8.13]}, {"time": 1.0667, "x": 11.52, "y": -8.13, "curve": [1.304, 0.35, 1.597, -20.14, 1.304, -8.59, 1.597, -9.45]}, {"time": 1.7667, "x": -20.14, "y": -9.45, "curve": [1.798, -25.65, 1.872, -27.6, 1.798, -29.52, 1.872, -36.59]}, {"time": 1.9, "x": -27.6, "y": -36.59, "curve": [1.917, -27.6, 1.964, -3.06, 1.917, -36.59, 1.964, -18.64]}, {"time": 1.9667, "x": 22.18, "y": -0.18, "curve": "stepped"}, {"time": 2.4667, "x": 22.18, "y": -0.18, "curve": [2.469, -34.96, 2.492, -65.55, 2.469, -14.99, 2.492, -22.91]}, {"time": 2.5, "x": -65.55, "y": -22.91, "curve": [2.5, -37.17, 2.525, -35.92, 2.5, -21.64, 2.525, -21.58]}, {"time": 2.5333, "x": -35.92, "y": -21.58, "curve": "stepped"}, {"time": 3, "x": -35.92, "y": -21.58, "curve": [3.033, -35.92, 3.1, -2.72, 3.033, -21.58, 3.1, -15.95]}, {"time": 3.1333, "x": -2.72, "y": -15.95, "curve": [3.158, -1.36, 3.183, 0, 3.158, -8.82, 3.183, -1.68]}, {"time": 3.2, "y": -1.68}]}, "HAND_RIGHT_CONSTRAINT": {"translate": [{"x": 2.18, "y": -4.38, "curve": [0.042, 2.18, 0.125, 30.85, 0.042, -4.38, 0.125, 49.87]}, {"time": 0.1667, "x": 30.85, "y": 49.87, "curve": [0.28, 30.85, 0.314, 213.97, 0.28, 49.87, 0.314, 230.42]}, {"time": 0.6667, "x": 213.97, "y": 230.42, "curve": [0.692, 213.97, 0.742, 103.95, 0.692, 230.42, 0.742, 215.96]}, {"time": 0.7667, "x": 103.95, "y": 215.96, "curve": [1.017, 103.95, 1.517, 114.45, 1.017, 215.96, 1.517, 200.86]}, {"time": 1.7667, "x": 114.45, "y": 200.86, "curve": [1.814, 234.3, 1.925, 276.54, 1.814, 163.91, 1.925, 150.89]}, {"time": 1.9667, "x": 276.54, "y": 150.89, "curve": [2.085, 295.29, 2.342, 297.5, 2.085, 121.87, 2.342, 118.44]}, {"time": 2.4667, "x": 297.5, "y": 118.44, "curve": [2.475, 297.5, 2.492, 86.96, 2.475, 118.44, 2.492, 226.17]}, {"time": 2.5, "x": 86.96, "y": 226.17, "curve": [2.51, 86.96, 2.522, 86.62, 2.51, 226.17, 2.522, 107.98]}, {"time": 2.5333, "x": 85.97, "y": -113.64, "curve": "stepped"}, {"time": 3, "x": 85.97, "y": -113.64, "curve": [3.06, 69.22, 3.151, 2.18, 3.06, -91.79, 3.151, -4.38]}, {"time": 3.2, "x": 2.18, "y": -4.38}]}, "HAND_RIGHT": {"rotate": [{"value": -21.28, "curve": "stepped"}, {"time": 0.6667, "value": -21.28, "curve": [0.7, -17.11, 0.733, -12.74]}, {"time": 0.7667, "value": -8.2, "curve": [1.112, -13.13, 1.524, -21.28]}, {"time": 1.7667, "value": -21.28, "curve": [1.814, 6.66, 1.925, 16.5]}, {"time": 1.9667, "value": 16.5, "curve": "stepped"}, {"time": 3, "value": 16.5, "curve": [3.05, 16.5, 3.15, -21.28]}, {"time": 3.2, "value": -21.28}]}, "HEAD": {"rotate": [{"curve": [0.167, 0, 0.5, -14.09]}, {"time": 0.6667, "value": -14.09, "curve": [0.678, -12.06, 0.689, -9.79]}, {"time": 0.7, "value": -7.29, "curve": [0.716, 0.82, 0.775, 7.05]}, {"time": 0.8333, "value": 10.91, "curve": [0.872, 6.66, 0.91, 4.38]}, {"time": 0.9333, "value": 4.38, "curve": [1.031, 4.38, 1.188, 12.45]}, {"time": 1.3333, "value": 18.91, "curve": [1.395, 22.95, 1.502, 26.37]}, {"time": 1.6, "value": 28.41, "curve": [1.677, 37.27, 1.749, 42.35]}, {"time": 1.8, "value": 42.35, "curve": [1.966, 21.86, 2.352, 14.64]}, {"time": 2.5, "value": 14.64, "curve": [2.52, 34.44, 2.702, 37.26]}, {"time": 2.7667, "value": 37.26, "curve": "stepped"}, {"time": 3, "value": 37.26, "curve": [3.05, 37.26, 3.15, 0]}, {"time": 3.2}], "translate": [{"time": 0.7, "curve": [0.755, -21.9, 1.083, -36.16, 0.755, -10.23, 1.083, -16.89]}, {"time": 1.3333, "x": -41.93, "y": -19.58, "curve": [1.395, -52.95, 1.502, -62.28, 1.395, -21.09, 1.502, -22.36]}, {"time": 1.6, "x": -67.83, "y": -23.12, "curve": [1.677, -63.56, 1.749, -61.11, 1.677, -39.61, 1.749, -49.06]}, {"time": 1.8, "x": -61.11, "y": -49.06, "curve": [1.832, -3.42, 1.905, 16.91, 1.832, -10.39, 1.905, 3.24]}, {"time": 1.9333, "x": 16.91, "y": 3.24, "curve": [2.046, 31.78, 2.333, 37.02, 2.046, 0.81, 2.333, -0.05]}, {"time": 2.4667, "x": 37.02, "y": -0.05, "curve": [2.478, 20.5, 2.489, 3.78, 2.478, -3.84, 2.489, -7.69]}, {"time": 2.5, "x": -10.67, "y": -11.01, "curve": [2.555, -8.78, 2.867, -8.19, 2.555, -9.54, 2.867, -9.08]}, {"time": 3, "x": -8.19, "y": -9.08, "curve": [3.05, -8.19, 3.15, 0, 3.05, -9.08, 3.15, 0]}, {"time": 3.2}]}, "WEAPON_CONSTRAINT": {"translate": [{"x": -1.74, "y": -2.65, "curve": [0.048, -30.61, 0.125, -35.97, 0.048, 11.29, 0.125, 13.88]}, {"time": 0.1667, "x": -35.97, "y": 13.88, "curve": "stepped"}, {"time": 3, "x": -35.97, "y": 13.88, "curve": [3.05, -35.97, 3.15, -1.74, 3.05, 13.88, 3.15, -2.65]}, {"time": 3.2, "x": -1.74, "y": -2.65}]}, "HAND_LEFT_HANDLE": {"translate": [{"x": 5.8, "y": 10.41, "curve": [0.047, 10.85, 0.107, 14.09, 0.047, 26.41, 0.107, 36.67]}, {"time": 0.1667, "x": 16.14, "y": 43.16, "curve": "stepped"}, {"time": 3, "x": 16.14, "y": 43.16, "curve": [3.05, 16.14, 3.15, 5.8, 3.05, 43.16, 3.15, 10.41]}, {"time": 3.2, "x": 5.8, "y": 10.41}]}, "BACK_BTM": {"rotate": [{"curve": [0.048, 6.36, 0.125, 7.54]}, {"time": 0.1667, "value": 7.54, "curve": [0.311, -19.24, 0.542, -24.21]}, {"time": 0.6667, "value": -24.21, "curve": [0.692, -24.21, 0.742, 4.97]}, {"time": 0.7667, "value": 4.97, "curve": "stepped"}, {"time": 1.7667, "value": 4.97, "curve": [1.806, -13.41, 1.898, -19.89]}, {"time": 1.9333, "value": -19.89, "curve": [1.976, -28.96, 2.333, -33.82]}, {"time": 2.4667, "value": -33.82, "curve": [2.469, -8.55, 2.492, 4.97]}, {"time": 2.5, "value": 4.97, "curve": "stepped"}, {"time": 3, "value": 4.97, "curve": [3.05, 4.97, 3.15, 0]}, {"time": 3.2}]}, "BACK_TOP": {"rotate": [{"curve": [0.048, 6.36, 0.125, 7.54]}, {"time": 0.1667, "value": 7.54, "curve": [0.311, -7.45, 0.542, -10.24]}, {"time": 0.6667, "value": -10.24, "curve": [0.697, -10.24, 0.731, -10]}, {"time": 0.7667, "value": -9.55, "curve": [1.053, -6.63, 1.521, 9.42]}, {"time": 1.7667, "value": 9.42, "curve": [1.933, -14.14, 2.319, -22.45]}, {"time": 2.4667, "value": -22.45, "curve": "stepped"}, {"time": 3, "value": -22.45, "curve": [3.05, -22.45, 3.15, 0]}, {"time": 3.2}]}, "LEG_LEFT": {"rotate": [{"value": -5.94, "curve": [0.442, -5.94, 1.325, -14.08]}, {"time": 1.7667, "value": -14.08, "curve": [1.933, -18.3, 2.319, -19.79]}, {"time": 2.4667, "value": -19.79, "curve": [2.6, -19.79, 2.867, -37.67]}, {"time": 3, "value": -37.67, "curve": [3.05, -37.67, 3.15, -5.94]}, {"time": 3.2, "value": -5.94}], "translate": [{"time": 1.7667, "curve": [1.933, -6, 2.319, -8.11, 1.933, 11.66, 2.319, 15.77]}, {"time": 2.4667, "x": -8.11, "y": 15.77, "curve": [2.475, -8.11, 2.492, 0, 2.475, 15.77, 2.492, 0]}, {"time": 2.5}]}, "SHOULDER_LEFT": {"rotate": [{"value": -83.05, "curve": [0.094, -37.64, 0.202, 30.47]}, {"time": 0.2667, "value": 30.47, "curve": [0.367, 30.47, 0.567, -134.61]}, {"time": 0.6667, "value": -134.61, "curve": [0.692, -134.61, 0.742, -54.99]}, {"time": 0.7667, "value": -54.99, "curve": [1.017, -54.99, 1.521, -73.82]}, {"time": 1.7667, "value": -73.82, "curve": [1.785, -71.88, 1.808, -70.2]}, {"time": 1.8333, "value": -68.74, "curve": [1.877, -133.53, 1.939, -157.79]}, {"time": 1.9667, "value": -157.79, "curve": "stepped"}, {"time": 2.4667, "value": -157.79, "curve": [2.469, -105.54, 2.492, -77.58]}, {"time": 2.5, "value": -77.58, "curve": [2.51, -77.58, 2.521, -19.4]}, {"time": 2.5333, "value": 82.41, "curve": "stepped"}, {"time": 3, "value": 82.41, "curve": [3.06, 71.07, 3.151, 25.69]}, {"time": 3.2, "value": 25.69}], "translate": [{"time": 0.2667, "curve": [0.367, 0, 0.567, -86.85, 0.367, 0, 0.567, -88.4]}, {"time": 0.6667, "x": -86.85, "y": -88.4, "curve": [0.692, -86.85, 0.742, -27.2, 0.692, -88.4, 0.742, -119.44]}, {"time": 0.7667, "x": -27.2, "y": -119.44, "curve": [0.906, -27.2, 1.125, -34.86, 0.906, -119.44, 1.125, -130.4]}, {"time": 1.3333, "x": -41.51, "y": -139.89, "curve": [1.499, -49.09, 1.658, -55.79, 1.499, -149.34, 1.658, -157.67]}, {"time": 1.7667, "x": -55.79, "y": -157.67, "curve": [1.814, -67.33, 1.925, -71.4, 1.814, -94.73, 1.925, -72.55]}, {"time": 1.9667, "x": -71.4, "y": -72.55, "curve": [2.085, -43.95, 2.342, -40.7, 2.085, -115.88, 2.342, -120.99]}, {"time": 2.4667, "x": -40.7, "y": -120.99, "curve": [2.485, -40.7, 2.508, -70.9, 2.485, -120.99, 2.508, -79.72]}, {"time": 2.5333, "x": -117.87, "y": -15.51, "curve": "stepped"}, {"time": 3, "x": -117.87, "y": -15.51, "curve": [3.065, -81.6, 3.151, 0, 3.065, -10.74, 3.151, 0]}, {"time": 3.2}], "scale": [{"time": 2.4667, "curve": [2.477, 1, 2.488, 0.991, 2.477, 1, 2.488, 0.991]}, {"time": 2.5, "x": 0.974, "y": 0.974, "curve": [2.51, 1.049, 2.522, 1.157, 2.51, 0.953, 2.522, 0.923]}, {"time": 2.5333, "x": 1.288, "y": 0.886, "curve": "stepped"}, {"time": 3, "x": 1.288, "y": 0.886, "curve": [3.065, 1.199, 3.151, 1, 3.065, 0.921, 3.151, 1]}, {"time": 3.2}]}, "ARM_LEFT": {"rotate": [{"value": -58.71, "curve": [0.067, -58.71, 0.2, -34.07]}, {"time": 0.2667, "value": -34.07, "curve": [0.367, -34.07, 0.567, 25.86]}, {"time": 0.6667, "value": 25.86, "curve": [0.692, 25.86, 0.742, 9.6]}, {"time": 0.7667, "value": 9.6, "curve": "stepped"}, {"time": 2.4667, "value": 9.6, "curve": [2.469, 30.87, 2.492, 42.25]}, {"time": 2.5, "value": 42.25, "curve": [2.649, 41.43, 2.878, 38.12]}, {"time": 3, "value": 38.12, "curve": [3.05, 38.12, 3.15, -19.99]}, {"time": 3.2, "value": -19.99}]}, "WEAPON_CONTROLLER": {"rotate": [{"value": 22.28, "curve": [0.067, 22.28, 0.257, 22.46]}, {"time": 0.2667, "value": 22.55, "curve": [0.325, 22.55, 0.442, 198.81]}, {"time": 0.5, "value": 198.81, "curve": [0.564, 203.5, 0.623, 207.01]}, {"time": 0.6667, "value": 207.01, "curve": [0.692, 207.01, 0.742, 172.23]}, {"time": 0.7667, "value": 172.23, "curve": [1.017, 172.23, 1.517, 182.95]}, {"time": 1.7667, "value": 182.95, "curve": [1.775, 177.91, 1.787, 173.95]}, {"time": 1.8, "value": 170.92, "curve": [1.824, 94.86, 1.853, 65.13]}, {"time": 1.8667, "value": 65.13, "curve": [1.892, 50.28, 1.945, 49.43]}, {"time": 1.9667, "value": 49.43}, {"time": 2.4667, "value": 33.44, "curve": [2.469, 78.59, 2.492, 102.75]}, {"time": 2.5, "value": 102.75, "curve": [2.51, 102.75, 2.521, 109.87]}, {"time": 2.5333, "value": 122.32, "curve": [2.55, 122.32, 2.583, 148.24]}, {"time": 2.6, "value": 148.24, "curve": [2.625, 148.24, 2.675, 118.61]}, {"time": 2.7, "value": 118.61, "curve": [2.725, 118.61, 2.775, 125.64]}, {"time": 2.8, "value": 125.64, "curve": [2.842, 125.64, 2.925, 125.71]}, {"time": 2.9667, "value": 125.71, "curve": [3.025, 125.71, 3.142, 6.17]}, {"time": 3.2, "value": 6.17}], "translate": [{"curve": [0.199, 0, 0.474, 10.66, 0.199, 0, 0.474, -11.79]}, {"time": 0.7667, "x": 23.81, "y": -26.34, "curve": [0.792, 23.81, 0.842, -17.6, 0.792, -26.34, 0.842, -15.64]}, {"time": 0.8667, "x": -17.6, "y": -15.64, "curve": [0.892, -17.6, 0.942, -1.79, 0.892, -15.64, 0.942, -6.62]}, {"time": 0.9667, "x": -1.79, "y": -6.62, "curve": "stepped"}, {"time": 1.7667, "x": -1.79, "y": -6.62, "curve": [2.032, -0.85, 2.289, 0, 2.032, -3.15, 2.289, 0]}, {"time": 2.4667, "curve": [2.485, 0, 2.508, 12.85, 2.485, 0, 2.508, -0.26]}, {"time": 2.5333, "x": 32.83, "y": -0.67, "curve": "stepped"}, {"time": 3, "x": 32.83, "y": -0.67, "curve": [3.065, 22.73, 3.151, 0, 3.065, -0.46, 3.151, 0]}, {"time": 3.2}], "scale": [{"time": 0.5, "curve": "stepped"}, {"time": 0.5333, "y": -1, "curve": "stepped"}, {"time": 0.6333, "y": -1, "curve": [0.642, 1, 0.658, 1, 0.642, -1, 0.658, -1.228]}, {"time": 0.6667, "y": -1.228, "curve": [0.692, 1, 0.742, 1, 0.692, -1.228, 0.742, -1]}, {"time": 0.7667, "y": -1, "curve": "stepped"}, {"time": 0.9667, "y": -1.021, "curve": [0.983, 1, 1.017, 1, 0.983, -1.021, 1.017, -1.118]}, {"time": 1.0333, "y": -1.118, "curve": [1.05, 1, 1.083, 1, 1.05, -1.118, 1.083, -1.021]}, {"time": 1.1, "y": -1.021, "curve": [1.117, 1, 1.15, 1, 1.117, -1.021, 1.15, -1.118]}, {"time": 1.1667, "y": -1.118, "curve": [1.183, 1, 1.217, 1, 1.183, -1.118, 1.217, -1.021]}, {"time": 1.2333, "y": -1.021, "curve": [1.25, 1, 1.283, 1, 1.25, -1.021, 1.283, -1.118]}, {"time": 1.3, "y": -1.118, "curve": [1.317, 1, 1.35, 1, 1.317, -1.118, 1.35, -1.021]}, {"time": 1.3667, "y": -1.021, "curve": [1.383, 1, 1.417, 1, 1.383, -1.021, 1.417, -1.118]}, {"time": 1.4333, "y": -1.118, "curve": [1.45, 1, 1.483, 1, 1.45, -1.118, 1.483, -1.021]}, {"time": 1.5, "y": -1.021, "curve": [1.517, 1, 1.55, 1, 1.517, -1.021, 1.55, -1.118]}, {"time": 1.5667, "y": -1.118, "curve": [1.583, 1, 1.617, 1, 1.583, -1.118, 1.617, -1.021]}, {"time": 1.6333, "y": -1.021, "curve": [1.65, 1, 1.683, 1, 1.65, -1.021, 1.683, -1.118]}, {"time": 1.7, "y": -1.118, "curve": [1.717, 1, 1.75, 1, 1.717, -1.118, 1.75, -1]}, {"time": 1.7667, "y": -1, "curve": "stepped"}, {"time": 2.5, "y": -1, "curve": "stepped"}, {"time": 2.5333, "y": -1.346, "curve": [2.55, 1, 2.583, 1, 2.55, -1.346, 2.583, -0.929]}, {"time": 2.6, "y": -0.929, "curve": [2.625, 1, 2.675, 1, 2.625, -0.929, 2.675, -1.118]}, {"time": 2.7, "y": -1.118, "curve": [2.767, 1, 2.9, 1, 2.767, -1.118, 2.9, -1]}, {"time": 2.9667, "y": -1}]}, "SHOULDER_RIGHT": {"rotate": [{"value": 1.96, "curve": [0.75, 1.96, 2.25, -83.95]}, {"time": 3, "value": -83.95, "curve": [3.05, -83.95, 3.15, 1.96]}, {"time": 3.2, "value": 1.96}], "translate": [{"time": 0.1667, "curve": [0.292, 0, 0.542, -35.93, 0.292, 0, 0.542, 63.18]}, {"time": 0.6667, "x": -35.93, "y": 63.18, "curve": [0.692, -35.93, 0.742, -42.76, 0.692, 63.18, 0.742, 44.62]}, {"time": 0.7667, "x": -42.76, "y": 44.62, "curve": "stepped"}, {"time": 1.7667, "x": -42.76, "y": 44.62, "curve": [1.817, -42.76, 1.917, -28.22, 1.817, 44.62, 1.917, 30.94]}, {"time": 1.9667, "x": -28.22, "y": 30.94, "curve": "stepped"}, {"time": 2.4667, "x": -28.22, "y": 30.94, "curve": [2.475, -28.22, 2.492, -61.83, 2.475, 30.94, 2.492, 60.16]}, {"time": 2.5, "x": -61.83, "y": 60.16, "curve": [2.51, -61.83, 2.521, -41.9, 2.51, 60.16, 2.521, 86.33]}, {"time": 2.5333, "x": -7.02, "y": 132.14, "curve": "stepped"}, {"time": 3, "x": -7.02, "y": 132.14, "curve": [3.06, -5.61, 3.151, 0, 3.06, 105.71, 3.151, 0]}, {"time": 3.2}]}, "root": {"scale": [{"time": 0.7667, "curve": [0.779, 1.062, 0.817, 1.121, 0.779, 0.932, 0.817, 0.868]}, {"time": 0.8333, "x": 1.121, "y": 0.868, "curve": [0.875, 1.121, 0.958, 1.013, 0.875, 0.868, 0.958, 0.942]}, {"time": 1, "x": 1.013, "y": 0.942, "curve": [1.232, 1.013, 1.365, 1.122, 1.232, 0.942, 1.365, 0.842]}, {"time": 1.6333, "x": 1.22, "y": 0.751, "curve": [1.673, 1.364, 1.717, 1.505, 1.673, 0.671, 1.717, 0.593]}, {"time": 1.7667, "x": 1.635, "y": 0.521, "curve": [1.806, 1.175, 1.898, 1.013, 1.806, 0.833, 1.898, 0.942]}, {"time": 1.9333, "x": 1.013, "y": 0.942, "curve": [2.116, 1.013, 2.203, 0.972, 2.116, 0.942, 2.203, 1.016]}, {"time": 2.4667, "x": 0.945, "y": 1.066, "curve": [2.475, 0.945, 2.492, 1, 2.475, 1.066, 2.492, 1]}, {"time": 2.5, "curve": [2.506, 1.062, 2.525, 1.121, 2.506, 0.932, 2.525, 0.868]}, {"time": 2.5333, "x": 1.121, "y": 0.868, "curve": [2.567, 1.121, 2.633, 0.92, 2.567, 0.868, 2.633, 1.075]}, {"time": 2.6667, "x": 0.92, "y": 1.075, "curve": [2.725, 0.92, 2.842, 1.054, 2.725, 1.075, 2.842, 0.946]}, {"time": 2.9, "x": 1.054, "y": 0.946, "curve": [2.975, 1.054, 3.125, 1, 2.975, 0.946, 3.125, 1]}, {"time": 3.2}]}, "ARM_RIGHT": {"rotate": [{"value": -4.6, "curve": [0.442, -4.6, 1.325, 1.6]}, {"time": 1.7667, "value": 1.6, "curve": [1.933, -5.9, 2.319, -8.54]}, {"time": 2.4667, "value": -8.54, "curve": [2.6, -8.54, 2.867, 28.91]}, {"time": 3, "value": 28.91, "curve": [3.05, 28.91, 3.15, -4.6]}, {"time": 3.2, "value": -4.6}]}, "LEG_RIGHT": {"rotate": [{"value": -6.41, "curve": [0.442, -6.41, 1.325, 4.69]}, {"time": 1.7667, "value": 4.69, "curve": [1.933, -9.41, 2.319, -14.38]}, {"time": 2.4667, "value": -14.38, "curve": [2.6, -14.38, 2.867, 4.69]}, {"time": 3, "value": 4.69, "curve": [3.05, 4.69, 3.15, -6.41]}, {"time": 3.2, "value": -6.41}]}, "LEG_RIGHT_BTM": {"rotate": [{"value": 12.75, "curve": [0.442, 12.75, 1.325, 0.3]}, {"time": 1.7667, "value": 0.3, "curve": "stepped"}, {"time": 3, "value": 0.3, "curve": [3.05, 0.3, 3.15, 12.75]}, {"time": 3.2, "value": 12.75}]}, "FOOT_RIGHT": {"rotate": [{"value": -8.39, "curve": [0.442, -8.39, 1.325, -8.67]}, {"time": 1.7667, "value": -8.67, "curve": [1.933, 4.79, 2.319, 9.53]}, {"time": 2.4667, "value": 9.53, "curve": [2.6, 9.53, 2.867, -8.67]}, {"time": 3, "value": -8.67, "curve": [3.05, -8.67, 3.15, -8.39]}, {"time": 3.2, "value": -8.39}]}, "LEG_LEFT_BTM": {"rotate": [{"value": 13.28, "curve": [0.442, 13.28, 1.325, 41.46]}, {"time": 1.7667, "value": 41.46, "curve": [1.933, 18.6, 2.319, 10.55]}, {"time": 2.4667, "value": 10.55, "curve": [2.6, 10.55, 2.867, 35.56]}, {"time": 3, "value": 35.56, "curve": [3.05, 35.56, 3.15, 13.28]}, {"time": 3.2, "value": 13.28}]}, "FOOT_LEFT": {"rotate": [{"value": -6.65, "curve": [0.442, -6.65, 1.325, -29.05]}, {"time": 1.7667, "value": -29.05, "curve": [1.933, -2.07, 2.319, 7.44]}, {"time": 2.4667, "value": 7.44, "curve": [2.6, 7.44, 2.867, 0.28]}, {"time": 3, "value": 0.28, "curve": [3.05, 0.28, 3.15, -6.65]}, {"time": 3.2, "value": -6.65}]}, "FOOT_LEFT_CONSTRAINT": {"translate": [{"time": 0.5667, "curve": [0.599, -4.62, 0.633, -9.61, 0.599, 6.3, 0.633, 13.11]}, {"time": 0.6667, "x": -14.85, "y": 20.24, "curve": [0.7, -10.08, 0.733, -5.09, 0.7, 13.74, 0.733, 6.94]}, {"time": 0.7667, "curve": "stepped"}, {"time": 1.9333, "curve": [1.958, -17.86, 2.008, -22.65, 1.958, 27.84, 2.008, 35.29]}, {"time": 2.0333, "x": -22.65, "y": 35.29}, {"time": 2.4333, "x": -42.88, "y": 53.88, "curve": [2.439, -36.44, 2.483, -32.99, 2.439, 18.07, 2.483, -1.1]}, {"time": 2.5, "x": -32.99, "y": -1.1, "curve": "stepped"}, {"time": 3, "x": -32.99, "y": -1.1, "curve": [3.033, -32.99, 3.083, -22.04, 3.033, -1.1, 3.083, 7.27]}, {"time": 3.1333, "x": -11.1, "y": 15.65, "curve": [3.158, -5.55, 3.183, 0, 3.158, 7.82, 3.183, 0]}, {"time": 3.2}]}, "ANKLE_LEFT_CONSTRAINT": {"translate": [{"time": 3, "curve": [3.033, 0, 3.083, -3.28, 3.033, 0, 3.083, 5.9]}, {"time": 3.1333, "x": -6.56, "y": 11.81, "curve": [3.158, -3.28, 3.183, 0, 3.158, 5.9, 3.183, 0]}, {"time": 3.2}]}, "WEAPON_FIXED": {"rotate": [{"time": 0.7667, "value": -179.77, "curve": [0.786, -181.88, 0.808, -183.91]}, {"time": 0.8333, "value": -185.84, "curve": [0.911, -175.83, 1.061, -170.21]}, {"time": 1.1333, "value": -170.21, "curve": [1.292, -170.21, 1.758, -180.99]}, {"time": 1.7667, "value": -189.45}], "translate": [{"time": 0.7667, "x": 53.42, "y": 110.26, "curve": "stepped"}, {"time": 1, "x": 53.42, "y": 110.26, "curve": [1.262, 53.42, 1.714, 53.16, 1.262, 110.26, 1.714, 109.21]}, {"time": 1.7667, "x": 52.01, "y": 104.57}]}, "WEAPON_FIXED_HOLDER": {"translate": [{"time": 1, "curve": [1.017, 0, 1.05, 5.31, 1.017, 0, 1.05, 0]}, {"time": 1.0667, "x": 5.31, "curve": [1.083, 5.31, 1.117, 0, 1.083, 0, 1.117, 0]}, {"time": 1.1333, "curve": [1.15, 0, 1.183, 9.29, 1.15, 0, 1.183, 0]}, {"time": 1.2, "x": 9.29, "curve": [1.217, 9.29, 1.25, 0, 1.217, 0, 1.25, 0]}, {"time": 1.2667, "curve": [1.283, 0, 1.317, 9.29, 1.283, 0, 1.317, 0]}, {"time": 1.3333, "x": 9.29, "curve": [1.35, 9.29, 1.383, 0, 1.35, 0, 1.383, 0]}, {"time": 1.4, "curve": [1.408, 0, 1.425, 9.29, 1.408, 0, 1.425, 0]}, {"time": 1.4333, "x": 9.29, "curve": [1.45, 9.29, 1.483, 0, 1.45, 0, 1.483, 0]}, {"time": 1.5, "curve": [1.508, 0, 1.525, 9.29, 1.508, 0, 1.525, 0]}, {"time": 1.5333, "x": 9.29, "curve": [1.55, 9.29, 1.583, 0, 1.55, 0, 1.583, 0]}, {"time": 1.6, "curve": [1.608, 0, 1.625, 9.29, 1.608, 0, 1.625, 0]}, {"time": 1.6333, "x": 9.29, "curve": [1.642, 9.29, 1.658, 0, 1.642, 0, 1.658, 0]}, {"time": 1.6667, "curve": [1.675, 0, 1.692, 9.29, 1.675, 0, 1.692, 0]}, {"time": 1.7, "x": 9.29, "curve": [1.708, 9.29, 1.725, 0, 1.708, 0, 1.725, 0]}, {"time": 1.7333, "curve": [1.742, 0, 1.758, 9.29, 1.742, 0, 1.758, 0]}, {"time": 1.7667, "x": 9.29, "curve": [1.775, 9.29, 1.792, 0, 1.775, 0, 1.792, 0]}, {"time": 1.8}]}}, "ik": {"HAND_LEFT_CONSTRAINT": [{"mix": 0, "bendPositive": false}], "WEAPON_CONSTRAINT": [{"mix": 0}]}, "drawOrder": [{"time": 0.2333, "offsets": [{"slot": "SHOULDER_RIGHT", "offset": 13}, {"slot": "HAND_RIGHT", "offset": 12}]}, {"time": 0.7667, "offsets": [{"slot": "HOOD_BACK", "offset": 7}, {"slot": "SHOULDER_RIGHT", "offset": 4}, {"slot": "HOOD_FRONT", "offset": -8}, {"slot": "HEAD_EXTRA", "offset": -8}, {"slot": "EYE_LEFT_SHOCKED", "offset": -8}, {"slot": "EYE_RIGHT_SHOCKED", "offset": -8}]}, {"time": 1.8667}, {"time": 2.5, "offsets": [{"slot": "ARM_LEFT", "offset": 14}, {"slot": "WEAPON", "offset": 12}, {"slot": "HAND_WEAPON", "offset": 12}, {"slot": "HOOD_BACK", "offset": 7}, {"slot": "SHOULDER_RIGHT", "offset": 1}, {"slot": "HOOD_FRONT", "offset": -11}, {"slot": "HEAD_EXTRA", "offset": -11}, {"slot": "EYE_LEFT_SHOCKED", "offset": -11}, {"slot": "EYE_RIGHT_SHOCKED", "offset": -11}]}, {"time": 3.0667, "offsets": [{"slot": "HOOD_BACK", "offset": 7}, {"slot": "HOOD_FRONT", "offset": -6}, {"slot": "HEAD_EXTRA", "offset": -6}, {"slot": "EYE_LEFT_SHOCKED", "offset": -6}, {"slot": "EYE_RIGHT_SHOCKED", "offset": -6}]}], "events": [{"time": 1.8, "name": "shake"}, {"time": 2.5, "name": "throw"}]}, "wake-up": {"bones": {"HIP": {"rotate": [{"value": -1.43}], "translate": [{"x": 22.58, "y": -46.16, "curve": [0.033, 22.58, 0.1, -2.23, 0.033, -46.16, 0.1, 42.8]}, {"time": 0.1333, "x": -2.23, "y": 42.8, "curve": [0.183, -2.23, 0.283, 0.68, 0.183, 42.8, 0.283, -15.16]}, {"time": 0.3333, "x": 0.68, "y": -15.16, "curve": [0.4, 0.68, 0.533, -3.18, 0.4, -15.16, 0.533, -6.17]}, {"time": 0.6, "x": -3.18, "y": -6.17, "curve": [0.642, -3.18, 0.725, -7.64, 0.642, -6.17, 0.725, -3.42]}, {"time": 0.7667, "x": -7.64, "y": -3.42, "curve": [0.825, -7.64, 0.942, -5.75, 0.825, -3.42, 0.942, -6.3]}, {"time": 1, "x": -5.75, "y": -6.3, "curve": [1.042, -5.75, 1.125, 0, 1.042, -6.3, 1.125, -1.68]}, {"time": 1.1667, "y": -1.68}]}, "HAND_RIGHT_CONSTRAINT": {"rotate": [{"value": -1.43}]}, "HAND_RIGHT": {"rotate": [{"value": -24.91, "curve": [0.325, -21.2, 0.62, -18.88]}, {"time": 0.8667, "value": -18.88, "curve": [0.939, -18.88, 1.071, -24.92]}, {"time": 1.1667, "value": -27.34}]}, "HEAD": {"rotate": [{"value": 17.91, "curve": [0.051, 11.34, 0.099, 5.87]}, {"time": 0.1333, "value": 5.87, "curve": "stepped"}, {"time": 0.3333, "value": 5.87, "curve": [0.442, 5.87, 0.658, 11.23]}, {"time": 0.7667, "value": 11.23, "curve": [0.867, 11.23, 1.067, 5.87]}, {"time": 1.1667, "value": 5.87}], "translate": [{"x": -22.25, "y": -0.41, "curve": [0.033, -22.25, 0.083, -11.13, 0.033, -0.41, 0.083, -0.2]}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3333, "curve": [0.392, 0, 0.508, -6.4, 0.392, 0, 0.508, 0.63]}, {"time": 0.5667, "x": -6.4, "y": 0.63, "curve": [0.617, -6.4, 0.717, 0, 0.617, 0.63, 0.717, 0]}, {"time": 0.7667, "curve": [0.817, 0, 0.917, -6.4, 0.817, 0, 0.917, 0.63]}, {"time": 0.9667, "x": -6.4, "y": 0.63, "curve": [1.017, -6.4, 1.117, 0, 1.017, 0.63, 1.117, 0]}, {"time": 1.1667}], "scale": [{"time": 0.2333, "curve": "stepped"}, {"time": 0.2667, "y": -1, "curve": "stepped"}, {"time": 0.4667, "y": -1, "curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 0.7333, "curve": "stepped"}, {"time": 0.7667, "y": -1, "curve": "stepped"}, {"time": 0.9667, "y": -1, "curve": "stepped"}, {"time": 1}]}, "WEAPON_CONSTRAINT": {"rotate": [{"value": -1.43}], "translate": [{"x": 2.6, "y": -1.14, "curve": "stepped"}, {"time": 0.3333, "x": 2.6, "y": -1.14, "curve": [0.357, 2.71, 0.38, 2.78, 0.357, -1.17, 0.38, -1.18]}, {"time": 0.4, "x": 2.78, "y": -1.18, "curve": [0.508, 2.78, 0.725, -1.35, 0.508, -1.18, 0.725, -0.08]}, {"time": 0.8333, "x": -1.35, "y": -0.08, "curve": [0.915, -1.35, 1.07, 1.94, 0.915, -0.08, 1.07, -0.96]}, {"time": 1.1667, "x": 2.6, "y": -1.14}]}, "HAND_LEFT_HANDLE": {"rotate": [{"value": -1.43}], "translate": [{"x": 4.94, "y": 11.22, "curve": "stepped"}, {"time": 0.3333, "x": 4.94, "y": 11.22, "curve": [0.357, 5.49, 0.38, 5.8, 0.357, 10.7, 0.38, 10.41]}, {"time": 0.4, "x": 5.8, "y": 10.41, "curve": [0.508, 5.8, 0.725, -14.28, 0.508, 10.41, 0.725, 29.48]}, {"time": 0.8333, "x": -14.28, "y": 29.48, "curve": [0.915, -14.28, 1.07, 1.74, 0.915, 29.48, 1.07, 14.26]}, {"time": 1.1667, "x": 4.94, "y": 11.22}]}, "root": {"rotate": [{"value": -1.43}]}, "Holder": {"rotate": [{"value": -1.43}]}, "BACK_BTM": {"rotate": [{"value": -19.7, "curve": [0.033, -19.7, 0.1, -1.43]}, {"time": 0.1333, "value": -1.43}]}, "BACK_TOP": {"rotate": [{"value": 24.22, "curve": [0.033, 24.22, 0.1, -1.43]}, {"time": 0.1333, "value": -1.43, "curve": "stepped"}, {"time": 0.3333, "value": -1.43, "curve": [0.442, -1.43, 0.658, 1.15]}, {"time": 0.7667, "value": 1.15, "curve": [0.867, 1.15, 1.067, -1.43]}, {"time": 1.1667, "value": -1.43}], "translate": [{"time": 0.3333, "curve": [0.442, 0, 0.658, 0.9, 0.442, 0, 0.658, 6.49]}, {"time": 0.7667, "x": 0.9, "y": 6.49, "curve": [0.867, 0.9, 1.067, 0, 0.867, 6.49, 1.067, 0]}, {"time": 1.1667}]}, "FACE": {"rotate": [{"value": -1.43}]}, "FOOT_RIGHT_CONSTRAINT": {"rotate": [{"value": -1.43}], "translate": [{"x": -48.26, "y": 19.2, "curve": [0.033, -48.26, 0.1, 0, 0.033, 19.2, 0.1, 0]}, {"time": 0.1333}]}, "ANKLE_RIGHT_CONSTRAINT": {"rotate": [{"value": -1.43}], "translate": [{"x": -24.49, "y": -24.21, "curve": [0.033, -24.49, 0.1, 0, 0.033, -24.21, 0.1, 0]}, {"time": 0.1333}]}, "FOOT_LEFT_CONSTRAINT": {"rotate": [{"value": -1.43}], "translate": [{"x": -29.59, "y": 24.19, "curve": [0.005, -32.02, 0.1, -34.58, 0.005, 46.44, 0.1, 69.89]}, {"time": 0.1333, "x": -34.58, "y": 69.89, "curve": [0.183, -34.58, 0.327, -10.22, 0.183, 69.89, 0.327, 20.66]}, {"time": 0.3333}]}, "ANKLE_LEFT_CONSTRAINT": {"rotate": [{"value": -1.43}], "translate": [{"x": -24.88, "y": -18.98, "curve": [0.033, -24.88, 0.1, 0, 0.033, -18.98, 0.1, 0]}, {"time": 0.1333}]}, "MASK": {"rotate": [{"value": -1.43}]}, "HORN_RIGHT": {"rotate": [{"value": -1.43}]}, "HORN_LEFT": {"rotate": [{"value": -1.43}]}, "EYE_LEFT": {"rotate": [{"value": -1.43}]}, "EYE_RIGHT": {"rotate": [{"value": -1.43}]}, "ARM_RIGHT": {"rotate": [{"value": -21.13, "curve": [0.064, -31.3, 0.122, -38.56]}, {"time": 0.1667, "value": -38.56, "curve": "stepped"}, {"time": 0.8667, "value": -38.56, "curve": [0.9, -38.56, 0.967, -5.89]}, {"time": 1, "value": -5.89, "curve": [1.057, -4.12, 1.113, -2.35]}, {"time": 1.1667, "value": -0.87}]}, "SHOULDER_RIGHT": {"rotate": [{"value": -4.67, "curve": [0.058, 12.61, 0.113, 26.06]}, {"time": 0.1667, "value": 35.18, "curve": [0.342, 35.18, 0.692, 43.39]}, {"time": 0.8667, "value": 43.39, "curve": [0.942, 43.39, 1.092, 3.04]}, {"time": 1.1667, "value": 3.04}]}, "SHOULDER_LEFT": {"rotate": [{"value": -4.29, "curve": [0.033, -4.29, 0.1, -77.5]}, {"time": 0.1333, "value": -77.5, "curve": "stepped"}, {"time": 0.3333, "value": -77.5, "curve": [0.433, -80.28, 0.533, -83.05]}, {"time": 0.6, "value": -83.05, "curve": [0.692, -83.05, 0.875, -71.95]}, {"time": 0.9667, "value": -71.95, "curve": [1.017, -71.95, 1.092, -74.73]}, {"time": 1.1667, "value": -77.5}]}, "WEAPON_CONTROLLER": {"rotate": [{"value": 22.31, "curve": [0.044, 22.04, 0.101, 21.51]}, {"time": 0.1333, "value": 21.51, "curve": [0.272, 21.97, 0.399, 22.28]}, {"time": 0.5, "value": 22.28, "curve": [0.592, 22.28, 0.775, 18.14]}, {"time": 0.8667, "value": 18.14, "curve": [0.939, 18.14, 1.067, 20.39]}, {"time": 1.1667, "value": 21.51}]}, "ARM_LEFT": {"rotate": [{"value": -57.93, "curve": [0.04, -50.79, 0.101, -22.22]}, {"time": 0.1333, "value": -22.22, "curve": [0.233, -22.22, 0.383, -40.47]}, {"time": 0.5333, "value": -58.71}]}}, "ik": {"HAND_LEFT_CONSTRAINT": [{"mix": 0, "bendPositive": false}], "HAND_RIGHT": [{"mix": 0, "bendPositive": false}], "WEAPON_CONSTRAINT": [{"mix": 0}]}}}}