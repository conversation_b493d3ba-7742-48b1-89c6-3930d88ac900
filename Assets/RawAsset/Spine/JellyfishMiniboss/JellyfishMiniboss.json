{"skeleton": {"hash": "FmKgFWKWOIw", "spine": "4.1.24", "x": -248.73, "y": 46.38, "width": 500, "height": 555.16, "images": "./images/", "audio": "C:/Users/<USER>/Desktop/咩咩启示录（Cult of the Lamb）/JellyfishMiniboss"}, "bones": [{"name": "root"}, {"name": "MAIN", "parent": "root", "x": 2.15, "y": 242.92}, {"name": "Head", "parent": "MAIN", "length": 133.97, "rotation": 91.02, "x": 1.2, "y": 7.18}, {"name": "Spikes", "parent": "Head", "x": 71.99, "y": 0.81, "color": "ffc600ff"}, {"name": "Spike1", "parent": "Spikes", "length": 95.87, "rotation": -34.32, "x": 91.3, "y": -74.98}, {"name": "Spike2", "parent": "Spikes", "length": 90.97, "rotation": -62.77, "x": 32.26, "y": -99.04}, {"name": "Spike3", "parent": "Spikes", "length": 89.66, "rotation": -81.81, "x": -25.68, "y": -127.91}, {"name": "Spike4", "parent": "Spikes", "length": 70.6, "rotation": -142.21, "x": -38.81, "y": -126.48}, {"name": "Spike5", "parent": "Spikes", "length": 70.2, "rotation": -157.95, "x": -51.13, "y": -79.61}, {"name": "Spike10", "parent": "Spikes", "length": 101.51, "rotation": 33.42, "x": 91.41, "y": 64.98}, {"name": "Spike6", "parent": "Spikes", "length": 85.06, "rotation": 61.33, "x": 46.78, "y": 111.23}, {"name": "Spike7", "parent": "Spikes", "length": 88.21, "rotation": 76.45, "x": -20.93, "y": 137.56}, {"name": "Spike8", "parent": "Spikes", "length": 83.03, "rotation": 130.47, "x": -40.3, "y": 124.74}, {"name": "Spike9", "parent": "Spikes", "length": 88.87, "rotation": 150, "x": -48.4, "y": 73.45}, {"name": "Tentacles", "parent": "MAIN", "rotation": -92.07, "x": -1.36, "y": -20.08}, {"name": "Tentacle3", "parent": "Tentacles", "length": 68.27, "rotation": 5.08, "x": -24.99, "y": -27.49, "color": "9100ffff"}, {"name": "Tentacle2_Mid", "parent": "Tentacle3", "length": 64.68, "rotation": 0.17, "x": 67.07, "y": 0.06, "color": "9100ffff"}, {"name": "Tentacle2_Btm", "parent": "Tentacle2_Mid", "length": 45.59, "rotation": 1.33, "x": 68.29, "y": -0.52, "color": "9100ffff"}, {"name": "Tentacle5", "parent": "Tentacles", "length": 59.99, "rotation": 6.64, "x": -23.33, "y": -87.39, "color": "ff00e5ff"}, {"name": "Tentacle3_Mid", "parent": "Tentacle5", "length": 53.94, "rotation": -0.76, "x": 58.7, "y": -1.1, "color": "ff00e5ff"}, {"name": "Tentacle3_Btm", "parent": "Tentacle3_Mid", "length": 47.9, "rotation": -0.95, "x": 53.94, "color": "ff00e5ff"}, {"name": "Tentacle6", "parent": "Tentacles", "length": 59.99, "rotation": -178.95, "x": -28.08, "y": 81.2, "scaleX": -1, "color": "ff00e5ff"}, {"name": "Tentacle3_Mid2", "parent": "Tentacle6", "length": 53.94, "rotation": -0.76, "x": 58.7, "y": -1.1, "color": "ff00e5ff"}, {"name": "Tentacle3_Btm2", "parent": "Tentacle3_Mid2", "length": 47.9, "rotation": -0.95, "x": 53.94, "color": "ff00e5ff"}, {"name": "Tentacle4", "parent": "Tentacles", "length": 68.27, "rotation": -175.75, "x": -22.68, "y": 15.03, "scaleX": -1, "color": "9100ffff"}, {"name": "Tentacle2_Mid2", "parent": "Tentacle4", "length": 64.68, "rotation": 0.17, "x": 67.07, "y": 0.06, "color": "9100ffff"}, {"name": "Tentacle2_Btm2", "parent": "Tentacle2_Mid2", "length": 45.59, "rotation": 1.33, "x": 68.29, "y": -0.52, "color": "9100ffff"}, {"name": "FACE", "parent": "Head", "x": 71.43, "y": 0.8, "color": "00ff0eff"}, {"name": "DangleHandle", "parent": "MAIN", "x": -0.12, "y": -105.7}, {"name": "Mouth", "parent": "FACE", "x": -50.17, "y": 28.94, "color": "00ff0eff"}, {"name": "Mouth2", "parent": "FACE", "x": -50.36, "y": 1.11, "color": "00ff0e49"}, {"name": "Mouth3", "parent": "Mouth", "length": 16.22, "rotation": -131.84, "x": 29.39, "y": -7.07, "color": "00ff0eff"}, {"name": "Mouth4", "parent": "Mouth", "length": 21.09, "rotation": -128.5, "x": 4.42, "y": 1.19, "color": "00ff0eff"}, {"name": "Mouth5", "parent": "Mouth", "length": 28.17, "rotation": -124.71, "x": -28.8, "y": 16.3, "color": "00ff0eff"}, {"name": "Mouth6", "parent": "Mouth2", "length": 19.09, "rotation": 141.1, "x": 30.66, "y": 5.6, "color": "00ff0e49"}, {"name": "Mouth7", "parent": "Mouth2", "length": 22.94, "rotation": 130.03, "x": 6.49, "y": -4.01, "color": "00ff0e49"}, {"name": "Mouth8", "parent": "Mouth2", "length": 29.8, "rotation": 127.13, "x": -24.97, "y": -15.73, "color": "00ff0e49"}, {"name": "ScuttleEye1", "parent": "FACE", "rotation": -91.02, "x": 3.79, "y": 97.64}, {"name": "ScuttleEyeball", "parent": "ScuttleEye1", "x": -1.02, "y": -1.02}, {"name": "ScuttleEye2", "parent": "FACE", "rotation": -91.02, "x": 0.31, "y": -97.11}, {"name": "ScuttleEyeball2", "parent": "ScuttleEye2", "x": -1.02, "y": -1.02}, {"name": "Tentacle1", "parent": "Tentacles", "length": 59.99, "rotation": 6.64, "x": -28, "y": -47.32, "color": "ff00e5ff"}, {"name": "Tentacle3_Mid3", "parent": "Tentacle1", "length": 53.94, "rotation": -0.76, "x": 58.7, "y": -1.1, "color": "ff00e5ff"}, {"name": "Tentacle3_Btm3", "parent": "Tentacle3_Mid3", "length": 47.9, "rotation": -0.95, "x": 53.94, "color": "ff00e5ff"}, {"name": "Tentacle2", "parent": "Tentacles", "length": 59.99, "rotation": -178.95, "x": -27.3, "y": 38.42, "scaleX": -1, "color": "ff00e5ff"}, {"name": "Tentacle3_Mid4", "parent": "Tentacle2", "length": 53.94, "rotation": -0.76, "x": 58.7, "y": -1.1, "color": "ff00e5ff"}, {"name": "Tentacle3_Btm4", "parent": "Tentacle3_Mid4", "length": 47.9, "rotation": -0.95, "x": 53.94, "color": "ff00e5ff"}, {"name": "ScuttleEye3", "parent": "FACE", "rotation": -91.02, "x": 81.01, "y": 0.73}, {"name": "ScuttleEyeball3", "parent": "ScuttleEye3", "x": -1.02, "y": -1.02}, {"name": "spawnerConstraint", "parent": "root", "x": 0.72, "y": 33.65, "scaleX": 0.1, "scaleY": 0.1, "skin": true, "color": "abe323ff"}, {"name": "TeleportEffect", "parent": "root", "x": -0.39, "y": -0.54, "scaleX": 1.7187, "scaleY": 1.7187}, {"name": "SpikerSpike1", "parent": "Head", "length": 211.9, "rotation": 106.81, "x": 66.14, "y": 129.91, "skin": true}, {"name": "SpikerSpike2", "parent": "Head", "length": 211.9, "rotation": 48.88, "x": 129.27, "y": 97.58, "skin": true}, {"name": "SpikerSpike3", "parent": "Head", "length": 211.9, "rotation": 82.35, "x": 101.63, "y": 78.57, "skin": true}, {"name": "SpikerSpike4", "parent": "Head", "length": 211.9, "rotation": 133.63, "x": 59.92, "y": 72.81, "skin": true}, {"name": "SpikerSpike5", "parent": "Head", "length": 211.9, "rotation": -58.08, "x": 134.82, "y": -101.45, "skin": true}, {"name": "SpikerSpike6", "parent": "Head", "length": 211.9, "rotation": -80.1, "x": 102.67, "y": -81.38, "skin": true}, {"name": "SpikerSpike7", "parent": "Head", "length": 211.9, "rotation": -113.73, "x": 74.49, "y": -130.28, "skin": true}, {"name": "SpikerSpike8", "parent": "Head", "length": 211.9, "rotation": -134.8, "x": 58.39, "y": -85.79, "skin": true}], "slots": [{"name": "MASK", "bone": "root"}, {"name": "Drips2", "bone": "MAIN", "attachment": "Drips2"}, {"name": "Tentacle5", "bone": "Tentacle5", "attachment": "Tentacle5"}, {"name": "Tentacle1", "bone": "Tentacle1", "attachment": "Tentacle5"}, {"name": "Tentacle6", "bone": "Tentacle6", "attachment": "Tentacle6"}, {"name": "Tentacle2", "bone": "Tentacle2", "attachment": "Tentacle6"}, {"name": "Tentacle3", "bone": "Tentacle3", "attachment": "Tentacle3"}, {"name": "Tentacle4", "bone": "Tentacle4", "attachment": "Tentacle4"}, {"name": "images/Drips", "bone": "MAIN", "attachment": "<PERSON><PERSON>s"}, {"name": "Skirt", "bone": "Head", "attachment": "Skirt"}, {"name": "images/SpikerBossSpike3", "bone": "SpikerSpike3"}, {"name": "images/SpikerBossSpike6", "bone": "SpikerSpike6"}, {"name": "Spikes", "bone": "Head", "attachment": "Spikes"}, {"name": "images/SpikerBossSpike4", "bone": "SpikerSpike4"}, {"name": "images/SpikerBossSpike8", "bone": "SpikerSpike8"}, {"name": "Head", "bone": "Head", "attachment": "Head"}, {"name": "MouthBack", "bone": "FACE", "attachment": "Mouth Back"}, {"name": "Mouth", "bone": "FACE", "attachment": "Mouth Open"}, {"name": "Spikes_Front", "bone": "Head", "attachment": "SpikesFront"}, {"name": "Eyes", "bone": "FACE", "attachment": "Eyes"}, {"name": "images/SpikerBossSpike1", "bone": "SpikerSpike1"}, {"name": "images/SpikerBossSpike7", "bone": "SpikerSpike7"}, {"name": "images/SpikerBossSpike2", "bone": "SpikerSpike2"}, {"name": "images/SpikerBossSpike5", "bone": "SpikerSpike5"}, {"name": "ScuttleEye1", "bone": "ScuttleEye1", "attachment": "<PERSON><PERSON><PERSON>_Eye_Closed"}, {"name": "ScuttleEye2", "bone": "ScuttleEye2", "attachment": "<PERSON><PERSON><PERSON>_Eye_Closed"}, {"name": "images/Eye", "bone": "ScuttleEyeball"}, {"name": "images/Eye2", "bone": "ScuttleEyeball2"}, {"name": "ScuttleEye3", "bone": "ScuttleEye3", "attachment": "<PERSON><PERSON><PERSON>_<PERSON>_Back"}, {"name": "images/Eye3", "bone": "ScuttleEyeball3", "attachment": "Eye"}, {"name": "TeleportEffect", "bone": "TeleportEffect"}], "transform": [{"name": "MouthConstraint", "order": 2, "bones": ["Mouth2"], "target": "Mouth", "mixRotate": 0, "mixX": -1, "mixScaleX": 0, "mixShearY": 0}, {"name": "spawnerConstraint", "skin": true, "bones": ["MAIN"], "target": "spawnerConstraint", "mixRotate": 0, "mixX": 0.33, "mixScaleX": 0.394, "mixShearY": 0}, {"name": "SPIKESOFFSET", "order": 1, "bones": ["Spikes"], "target": "FACE", "mixRotate": 0, "mixX": -0.4, "mixScaleX": 0, "mixShearY": 0}], "skins": [{"name": "default", "attachments": {"MASK": {"MASK": {"type": "clipping", "end": "Mouth", "vertexCount": 4, "vertices": [-596.86, -8.8, 597.78, -11.44, 596.01, 1049.28, -602.15, 1048.4], "color": "ce3a3aff"}}, "TeleportEffect": {"images/Teleport_1": {"y": 0.5, "width": 160, "height": 55}, "images/Teleport_2": {"y": 0.5, "width": 160, "height": 55}, "images/Teleport_3": {"y": 0.5, "width": 160, "height": 55}, "images/Teleport_4": {"y": 0.5, "width": 160, "height": 55}}}}, {"name": "Chaser", "attachments": {"Drips2": {"Drips2": {"name": "images/Drips", "type": "mesh", "uvs": [1, 0.1099, 0.99149, 0.47829, 0.98808, 0.61203, 1, 0.79051, 1, 0.88399, 0.90036, 0.8709, 0.9074, 0.76218, 0.9151, 0.60355, 0.91216, 0.4596, 0.88562, 0.36799, 0.83844, 0.34929, 0.79421, 0.43716, 0.79274, 0.54747, 0.79716, 0.62599, 0.71018, 0.63347, 0.70769, 0.54718, 0.69986, 0.45025, 0.67332, 0.39977, 0.64826, 0.43342, 0.62909, 0.57364, 0.64679, 0.70638, 0.64089, 0.77929, 0.59076, 0.80173, 0.54211, 0.73629, 0.55652, 0.56371, 0.54948, 0.42968, 0.50525, 0.36051, 0.45172, 0.42105, 0.44242, 0.60742, 0.45251, 0.77455, 0.48121, 0.91706, 0.48756, 1, 0.34456, 1, 0.35716, 0.91519, 0.37168, 0.76276, 0.37201, 0.61011, 0.35058, 0.42229, 0.31065, 0.35111, 0.29181, 0.42748, 0.30475, 0.60537, 0.31212, 0.72877, 0.20519, 0.73437, 0.20666, 0.59574, 0.2214, 0.42906, 0.17649, 0.26511, 0.12194, 0.25046, 0.09402, 0.40566, 0.09348, 0.5572, 0.10977, 0.71712, 0.1239, 0.85594, 0, 0.86086, 0, 0.72508, 0.01173, 0.55255, 0.01127, 0.4213, 0.0112, 0.40055, 0, 0.2081, 0.0343, 0.08471, 0.18173, 0, 0.87144, 0, 1, 0], "triangles": [31, 33, 30, 32, 33, 31, 50, 48, 49, 5, 3, 4, 29, 30, 33, 21, 22, 20, 58, 59, 0, 45, 56, 57, 55, 56, 45, 44, 45, 57, 57, 26, 37, 10, 58, 0, 37, 44, 57, 26, 57, 58, 58, 17, 26, 9, 10, 0, 10, 17, 58, 54, 55, 45, 46, 54, 45, 27, 37, 26, 53, 54, 46, 36, 37, 27, 37, 43, 44, 38, 43, 37, 25, 26, 17, 18, 25, 17, 11, 17, 10, 16, 17, 11, 1, 8, 9, 0, 1, 9, 12, 15, 16, 11, 12, 16, 47, 52, 53, 46, 47, 53, 24, 25, 18, 19, 24, 18, 7, 8, 1, 27, 35, 36, 27, 28, 35, 2, 7, 1, 14, 15, 12, 14, 12, 13, 51, 52, 47, 51, 47, 48, 42, 43, 38, 39, 42, 38, 41, 42, 39, 41, 39, 40, 20, 23, 24, 20, 24, 19, 6, 7, 2, 20, 22, 23, 6, 2, 3, 50, 51, 48, 5, 6, 3, 34, 35, 28, 29, 34, 28, 33, 34, 29], "vertices": [1, 1, 81.68, -18.42, 1, 2, 1, 80.51, -56.3, 0.76, 28, 80.63, 49.39, 0.24, 2, 1, 80.04, -70.06, 0.76, 28, 80.16, 35.64, 0.24, 2, 1, 81.68, -88.41, 0.6, 28, 81.81, 17.29, 0.4, 2, 1, 81.68, -98.03, 0.424, 28, 81.81, 7.67, 0.576, 2, 1, 67.94, -96.68, 0.424, 28, 68.07, 9.02, 0.576, 2, 1, 68.91, -85.5, 0.6, 28, 69.04, 20.2, 0.4, 2, 1, 69.97, -69.19, 0.76, 28, 70.1, 36.51, 0.24, 2, 1, 69.57, -54.38, 0.76, 28, 69.69, 51.32, 0.24, 2, 1, 65.91, -44.96, 0.888, 28, 66.03, 60.74, 0.112, 2, 1, 59.4, -43.04, 0.888, 28, 59.53, 62.66, 0.112, 2, 1, 53.3, -52.07, 0.888, 28, 53.43, 53.62, 0.112, 2, 1, 53.1, -63.42, 0.76, 28, 53.22, 42.28, 0.24, 2, 1, 53.71, -71.49, 0.76, 28, 53.83, 34.2, 0.24, 2, 1, 41.71, -72.26, 0.76, 28, 41.84, 33.44, 0.24, 2, 1, 41.37, -63.39, 0.76, 28, 41.5, 42.31, 0.24, 2, 1, 40.29, -53.42, 0.888, 28, 40.42, 52.28, 0.112, 2, 1, 36.63, -48.23, 0.888, 28, 36.76, 57.47, 0.112, 2, 1, 33.18, -51.69, 0.888, 28, 33.3, 54.01, 0.112, 2, 1, 30.53, -66.11, 0.76, 28, 30.66, 39.59, 0.24, 2, 1, 32.97, -79.76, 0.6, 28, 33.1, 25.94, 0.4, 2, 1, 32.16, -87.26, 0.472, 28, 32.28, 18.44, 0.528, 2, 1, 25.25, -89.57, 0.472, 28, 25.37, 16.13, 0.528, 2, 1, 18.54, -82.84, 0.6, 28, 18.66, 22.86, 0.4, 2, 1, 20.53, -65.09, 0.76, 28, 20.65, 40.61, 0.24, 2, 1, 19.55, -51.31, 0.888, 28, 19.68, 54.39, 0.112, 2, 1, 13.46, -44.19, 0.888, 28, 13.58, 61.51, 0.112, 2, 1, 6.07, -50.42, 0.888, 28, 6.2, 55.28, 0.112, 2, 1, 4.79, -69.58, 0.76, 28, 4.91, 36.11, 0.24, 2, 1, 6.18, -86.77, 0.6, 28, 6.31, 18.93, 0.4, 2, 1, 10.14, -101.43, 0.424, 28, 10.26, 4.27, 0.576, 2, 1, 11.02, -109.96, 0.328, 28, 11.14, -4.26, 0.672, 2, 1, -8.7, -109.96, 0.328, 28, -8.58, -4.26, 0.672, 2, 1, -6.97, -101.23, 0.424, 28, -6.84, 4.46, 0.576, 2, 1, -4.96, -85.56, 0.6, 28, -4.84, 20.14, 0.4, 2, 1, -4.92, -69.86, 0.76, 28, -4.79, 35.84, 0.24, 2, 1, -7.87, -50.54, 0.888, 28, -7.75, 55.15, 0.112, 2, 1, -13.38, -43.23, 0.888, 28, -13.26, 62.47, 0.112, 2, 1, -15.98, -51.08, 0.888, 28, -15.85, 54.62, 0.112, 2, 1, -14.19, -69.37, 0.76, 28, -14.07, 36.32, 0.24, 2, 1, -13.18, -82.06, 0.6, 28, -13.05, 23.63, 0.4, 2, 1, -27.92, -82.64, 0.6, 28, -27.8, 23.06, 0.4, 2, 1, -27.72, -68.38, 0.76, 28, -27.6, 37.32, 0.24, 2, 1, -25.69, -51.24, 0.888, 28, -25.56, 54.46, 0.112, 1, 1, -31.88, -34.38, 1, 1, 1, -39.4, -32.87, 1, 2, 1, -43.25, -48.83, 0.888, 28, -43.13, 56.86, 0.112, 2, 1, -43.33, -64.42, 0.76, 28, -43.2, 41.28, 0.24, 2, 1, -41.08, -80.87, 0.6, 28, -40.96, 24.83, 0.4, 2, 1, -39.13, -95.14, 0.424, 28, -39.01, 10.56, 0.576, 2, 1, -56.22, -95.65, 0.424, 28, -56.09, 10.05, 0.576, 2, 1, -56.22, -81.68, 0.6, 28, -56.09, 24.01, 0.4, 2, 1, -54.6, -63.94, 0.76, 28, -54.48, 41.76, 0.24, 2, 1, -54.66, -50.44, 0.888, 28, -54.54, 55.25, 0.112, 2, 1, -54.67, -48.31, 0.888, 28, -54.55, 57.39, 0.112, 1, 1, -56.22, -28.52, 1, 1, 1, -51.49, -15.83, 1, 1, 1, -31.16, -7.12, 1, 1, 1, 63.95, -7.12, 1, 1, 1, 81.68, -7.12, 1], "hull": 60, "edges": [100, 98, 94, 92, 92, 90, 90, 88, 88, 86, 86, 84, 84, 82, 82, 80, 80, 78, 78, 76, 76, 74, 74, 72, 72, 70, 70, 68, 68, 66, 66, 64, 62, 64, 62, 60, 60, 58, 58, 56, 56, 54, 54, 52, 52, 50, 50, 48, 48, 46, 46, 44, 36, 38, 38, 40, 40, 42, 36, 34, 34, 32, 32, 30, 30, 28, 28, 26, 26, 24, 24, 22, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 100, 102, 98, 96, 96, 94, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 2, 0, 0, 118, 116, 0, 44, 42], "width": 175, "height": 138}}, "Eyes": {"Eyes": {"name": "images/ChaserBossEyes", "x": 13.4, "y": -2.6, "rotation": -91.02, "width": 286, "height": 174}, "EyesCharge": {"name": "images/ChaserBossEyes", "x": 13.4, "y": -2.6, "rotation": -91.02, "width": 286, "height": 174}, "EyesCharge2": {"name": "images/ChaserBossEyes", "x": 13.4, "y": -2.6, "rotation": -91.02, "width": 286, "height": 174}, "EyesClosed": {"name": "images/ChaserBossEyesShut", "x": 12.45, "y": 0.42, "rotation": -91.02, "width": 260, "height": 172}}, "Head": {"Head": {"name": "images/ChaserBossHead", "type": "mesh", "uvs": [0.60186, 0.02378, 0.70221, 0.12951, 0.77367, 0.23889, 0.81423, 0.325, 0.81166, 0.37951, 0.85551, 0.38274, 0.89366, 0.4377, 0.98741, 0.65799, 0.99996, 0.83938, 0.96607, 0.95834, 0.84611, 1, 0.66358, 1, 0.51936, 0.99998, 0.36722, 0.99996, 0.20082, 1, 0.07621, 1, 1e-05, 0.86469, 0.00476, 0.68566, 0.08205, 0.47244, 0.14622, 0.37991, 0.18842, 0.37859, 0.18379, 0.31883, 0.22506, 0.2359, 0.29453, 0.13345, 0.38716, 0.02837, 0.46127, 0, 0.53753, 0, 0.34662, 0.1912, 0.31968, 0.37523, 0.29907, 0.56332, 0.30224, 0.68061, 0.13742, 0.8505, 0.13425, 0.6887, 0.16437, 0.44602, 0.71112, 0.68061, 0.7127, 0.54916, 0.70003, 0.37119, 0.66041, 0.15479, 0.51144, 0.90105, 0.50034, 0.47433, 0.49876, 0.27614, 0.49717, 0.13053, 0.50643, 0.81215, 0.3179, 0.85303, 0.70507, 0.85712, 0.50291, 0.61], "triangles": [14, 43, 13, 8, 44, 34, 31, 30, 43, 44, 11, 38, 13, 43, 38, 43, 42, 38, 39, 45, 29, 45, 39, 35, 29, 45, 30, 42, 30, 45, 42, 45, 34, 34, 45, 35, 39, 29, 28, 39, 36, 35, 32, 29, 30, 28, 40, 39, 39, 40, 36, 30, 42, 43, 44, 42, 34, 13, 38, 12, 11, 12, 38, 38, 42, 44, 11, 44, 10, 29, 33, 28, 15, 31, 14, 14, 31, 43, 15, 16, 31, 31, 17, 32, 17, 31, 16, 31, 32, 30, 17, 18, 32, 32, 33, 29, 32, 18, 33, 18, 19, 33, 33, 19, 20, 9, 10, 8, 8, 10, 44, 34, 7, 8, 35, 6, 7, 6, 4, 5, 6, 35, 4, 28, 27, 40, 27, 41, 40, 27, 24, 41, 24, 25, 41, 35, 36, 4, 4, 36, 3, 21, 22, 28, 28, 22, 27, 40, 37, 36, 36, 2, 3, 2, 37, 1, 2, 36, 37, 37, 41, 0, 0, 41, 26, 41, 37, 40, 22, 23, 27, 27, 23, 24, 37, 0, 1, 41, 25, 26, 28, 33, 20, 20, 21, 28, 7, 34, 35], "vertices": [1, 2, 254.91, -37.04, 1, 1, 2, 225.96, -70.85, 1, 1, 2, 196.22, -94.76, 1, 1, 2, 172.9, -108.22, 1, 1, 2, 158.31, -107.08, 1, 1, 2, 157.17, -122.06, 1, 1, 2, 142.21, -134.84, 1, 1, 2, 82.61, -165.85, 1, 1, 2, 33.93, -169.27, 1, 1, 2, 2.26, -157.11, 1, 2, 2, -8.17, -115.89, 0.792, 27, -90.21, -115.99, 0.208, 2, 2, -7.06, -53.48, 0.184, 27, -89.1, -53.57, 0.816, 2, 2, -6.17, -4.16, 0.072, 27, -88.21, -4.26, 0.928, 2, 2, -5.24, 47.86, 0.216, 27, -87.28, 47.77, 0.784, 2, 2, -4.23, 104.76, 0.632, 27, -86.27, 104.67, 0.368, 1, 2, -3.47, 147.37, 1, 1, 2, 33.25, 172.78, 1, 1, 2, 81.2, 170.3, 1, 1, 2, 137.86, 142.85, 1, 1, 2, 162.26, 120.46, 1, 1, 2, 162.36, 106.03, 1, 1, 2, 178.4, 107.33, 1, 1, 2, 200.37, 92.82, 1, 1, 2, 227.4, 68.57, 1, 1, 2, 254.99, 36.39, 1, 1, 2, 262.14, 10.92, 1, 1, 2, 261.67, -15.16, 1, 2, 2, 211.6, 51.04, 0.808, 27, 129.56, 50.94, 0.192, 2, 2, 162.45, 61.13, 0.472, 27, 80.41, 61.04, 0.528, 2, 2, 112.18, 69.07, 0.232, 27, 30.14, 68.98, 0.768, 2, 2, 80.73, 68.55, 0.2, 27, -1.31, 68.46, 0.8, 2, 2, 36.22, 125.72, 0.728, 27, -45.82, 125.63, 0.272, 2, 2, 79.59, 126.03, 0.808, 27, -2.45, 125.94, 0.192, 2, 2, 144.44, 114.58, 0.808, 27, 62.39, 114.48, 0.192, 2, 2, 78.23, -71.26, 0.248, 27, -3.81, -71.36, 0.752, 2, 2, 113.45, -72.43, 0.264, 27, 31.41, -72.53, 0.736, 2, 2, 161.22, -68.95, 0.472, 27, 79.17, -69.04, 0.528, 2, 2, 219.44, -56.44, 0.808, 27, 137.4, -56.53, 0.192, 1, 27, -61.66, -2.02, 1, 2, 2, 134.8, -0.17, 0.04, 27, 52.76, -0.27, 0.96, 2, 2, 187.91, -0.58, 0.392, 27, 105.87, -0.67, 0.608, 2, 2, 226.94, -0.74, 0.808, 27, 144.9, -0.83, 0.192, 1, 27, -37.8, -0.73, 1, 2, 2, 34.44, 64.02, 0.376, 27, -47.61, 63.93, 0.624, 2, 2, 30.98, -68.35, 0.136, 27, -51.07, -68.44, 0.864, 1, 27, 16.39, -0.5, 1], "hull": 27, "edges": [38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 16, 18, 18, 20, 32, 34, 32, 30, 48, 54, 54, 56, 56, 58, 58, 60, 26, 28, 28, 30, 28, 62, 62, 64, 64, 66, 34, 36, 14, 16, 20, 22, 68, 70, 70, 72, 72, 74, 74, 0, 36, 38, 12, 14, 22, 24, 24, 26, 24, 76, 78, 80, 80, 82, 76, 84, 26, 86, 86, 60, 22, 88, 88, 68, 78, 90, 90, 84], "width": 342, "height": 268}}, "images/Drips": {"Drips": {"name": "images/Drips", "type": "mesh", "uvs": [1, 0.1099, 0.99149, 0.47829, 0.98808, 0.61203, 1, 0.79051, 1, 0.88399, 0.90036, 0.8709, 0.9074, 0.76218, 0.9151, 0.60355, 0.91216, 0.4596, 0.88562, 0.36799, 0.83844, 0.34929, 0.79421, 0.43716, 0.79274, 0.54747, 0.79716, 0.62599, 0.71018, 0.63347, 0.70769, 0.54718, 0.69986, 0.45025, 0.67332, 0.39977, 0.64826, 0.43342, 0.62909, 0.57364, 0.64679, 0.70638, 0.64089, 0.77929, 0.59076, 0.80173, 0.54211, 0.73629, 0.55652, 0.56371, 0.54948, 0.42968, 0.50525, 0.36051, 0.45172, 0.42105, 0.44242, 0.60742, 0.45251, 0.77455, 0.48121, 0.91706, 0.48756, 1, 0.34456, 1, 0.35716, 0.91519, 0.37168, 0.76276, 0.37201, 0.61011, 0.35058, 0.42229, 0.31065, 0.35111, 0.29181, 0.42748, 0.30475, 0.60537, 0.31212, 0.72877, 0.20519, 0.73437, 0.20666, 0.59574, 0.2214, 0.42906, 0.17649, 0.26511, 0.12194, 0.25046, 0.09402, 0.40566, 0.09348, 0.5572, 0.10977, 0.71712, 0.1239, 0.85594, 0, 0.86086, 0, 0.72508, 0.01173, 0.55255, 0.01127, 0.4213, 0.0112, 0.40055, 0, 0.2081, 0.0343, 0.08471, 0.18173, 0, 0.87144, 0, 1, 0], "triangles": [29, 30, 33, 5, 3, 4, 50, 48, 49, 32, 33, 31, 31, 33, 30, 33, 34, 29, 29, 34, 28, 34, 35, 28, 5, 6, 3, 50, 51, 48, 6, 2, 3, 20, 22, 23, 6, 7, 2, 20, 24, 19, 20, 23, 24, 41, 39, 40, 41, 42, 39, 39, 42, 38, 42, 43, 38, 51, 47, 48, 51, 52, 47, 14, 12, 13, 14, 15, 12, 2, 7, 1, 27, 28, 35, 27, 35, 36, 7, 8, 1, 19, 24, 18, 24, 25, 18, 46, 47, 53, 47, 52, 53, 11, 12, 16, 12, 15, 16, 0, 1, 9, 1, 8, 9, 16, 17, 11, 11, 17, 10, 18, 25, 17, 25, 26, 17, 38, 43, 37, 37, 43, 44, 36, 37, 27, 53, 54, 46, 27, 37, 26, 46, 54, 45, 54, 55, 45, 10, 17, 58, 9, 10, 0, 58, 17, 26, 26, 57, 58, 37, 44, 57, 10, 58, 0, 57, 26, 37, 44, 45, 57, 55, 56, 45, 45, 56, 57, 58, 59, 0, 21, 22, 20], "vertices": [1, 1, 79.67, 10.88, 1, 2, 1, 78.18, -27, 0.76, 28, 78.3, 78.69, 0.24, 2, 1, 77.58, -40.76, 0.76, 28, 77.71, 64.94, 0.24, 2, 1, 79.67, -59.11, 0.6, 28, 79.79, 46.59, 0.4, 2, 1, 79.67, -68.72, 0.424, 28, 79.79, 36.97, 0.576, 2, 1, 62.23, -67.38, 0.424, 28, 62.36, 38.32, 0.576, 2, 1, 63.46, -56.2, 0.6, 28, 63.59, 49.5, 0.4, 2, 1, 64.81, -39.88, 0.76, 28, 64.94, 65.81, 0.24, 2, 1, 64.3, -25.08, 0.76, 28, 64.42, 80.62, 0.24, 2, 1, 59.65, -15.66, 0.888, 28, 59.78, 90.04, 0.112, 2, 1, 51.4, -13.74, 0.888, 28, 51.52, 91.96, 0.112, 2, 1, 43.66, -22.77, 0.888, 28, 43.78, 82.92, 0.112, 2, 1, 43.4, -34.12, 0.76, 28, 43.52, 71.58, 0.24, 2, 1, 44.17, -42.19, 0.76, 28, 44.3, 63.51, 0.24, 2, 1, 28.95, -42.96, 0.76, 28, 29.07, 62.74, 0.24, 2, 1, 28.51, -34.09, 0.76, 28, 28.64, 71.61, 0.24, 2, 1, 27.14, -24.12, 0.888, 28, 27.27, 81.58, 0.112, 2, 1, 22.5, -18.93, 0.888, 28, 22.62, 86.77, 0.112, 2, 1, 18.11, -22.39, 0.888, 28, 18.24, 83.31, 0.112, 2, 1, 14.76, -36.81, 0.76, 28, 14.88, 68.89, 0.24, 2, 1, 17.86, -50.46, 0.6, 28, 17.98, 55.24, 0.4, 2, 1, 16.82, -57.96, 0.472, 28, 16.95, 47.74, 0.528, 2, 1, 8.05, -60.26, 0.472, 28, 8.18, 45.43, 0.528, 2, 1, -0.46, -53.53, 0.6, 28, -0.34, 52.16, 0.4, 2, 1, 2.06, -35.79, 0.76, 28, 2.18, 69.91, 0.24, 2, 1, 0.83, -22, 0.888, 28, 0.95, 83.69, 0.112, 2, 1, -6.91, -14.89, 0.888, 28, -6.79, 90.81, 0.112, 2, 1, -16.28, -21.12, 0.888, 28, -16.16, 84.58, 0.112, 2, 1, -17.91, -40.28, 0.76, 28, -17.78, 65.42, 0.24, 2, 1, -16.14, -57.47, 0.6, 28, -16.02, 48.23, 0.4, 2, 1, -11.12, -72.12, 0.424, 28, -11, 33.57, 0.576, 2, 1, -10.01, -80.65, 0.328, 28, -9.88, 25.04, 0.672, 2, 1, -35.03, -80.65, 0.328, 28, -34.91, 25.04, 0.672, 2, 1, -32.83, -71.93, 0.424, 28, -32.7, 33.77, 0.576, 2, 1, -30.29, -56.26, 0.6, 28, -30.16, 49.44, 0.4, 2, 1, -30.23, -40.56, 0.76, 28, -30.1, 65.14, 0.24, 2, 1, -33.98, -21.24, 0.888, 28, -33.86, 84.45, 0.112, 2, 1, -40.97, -13.92, 0.888, 28, -40.84, 91.77, 0.112, 2, 1, -44.26, -21.78, 0.888, 28, -44.14, 83.92, 0.112, 2, 1, -42, -40.07, 0.76, 28, -41.87, 65.63, 0.24, 2, 1, -40.71, -52.76, 0.6, 28, -40.58, 52.94, 0.4, 2, 1, -59.42, -53.34, 0.6, 28, -59.3, 52.36, 0.4, 2, 1, -59.17, -39.08, 0.76, 28, -59.04, 66.62, 0.24, 2, 1, -56.59, -21.94, 0.888, 28, -56.46, 83.76, 0.112, 1, 1, -64.45, -5.08, 1, 1, 1, -73.99, -3.57, 1, 2, 1, -78.88, -19.53, 0.888, 28, -78.75, 86.16, 0.112, 2, 1, -78.97, -35.12, 0.76, 28, -78.85, 70.58, 0.24, 2, 1, -76.12, -51.56, 0.6, 28, -76, 54.13, 0.4, 2, 1, -73.65, -65.84, 0.424, 28, -73.52, 39.86, 0.576, 2, 1, -95.33, -66.34, 0.424, 28, -95.21, 39.35, 0.576, 2, 1, -95.33, -52.38, 0.6, 28, -95.21, 53.32, 0.4, 2, 1, -93.28, -34.64, 0.76, 28, -93.15, 71.06, 0.24, 2, 1, -93.36, -21.14, 0.888, 28, -93.23, 84.56, 0.112, 2, 1, -93.37, -19.01, 0.888, 28, -93.25, 86.69, 0.112, 1, 1, -95.33, 0.78, 1, 1, 1, -89.33, 13.47, 1, 1, 1, -62.28, 52.18, 1, 1, 1, 54.05, 52.18, 1, 1, 1, 79.67, 22.18, 1], "hull": 60, "edges": [100, 98, 94, 92, 92, 90, 90, 88, 88, 86, 86, 84, 84, 82, 82, 80, 80, 78, 78, 76, 76, 74, 74, 72, 72, 70, 70, 68, 68, 66, 66, 64, 62, 64, 62, 60, 60, 58, 58, 56, 56, 54, 54, 52, 52, 50, 50, 48, 48, 46, 46, 44, 36, 38, 38, 40, 40, 42, 36, 34, 34, 32, 32, 30, 30, 28, 28, 26, 26, 24, 24, 22, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 100, 102, 98, 96, 96, 94, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 2, 0, 0, 118, 116, 0, 44, 42], "width": 175, "height": 138}}, "Mouth": {"Mouth Open": {"name": "images/ChaserBossMouth", "type": "mesh", "uvs": [0.53839, 0.16587, 0.59644, 0.18622, 0.56451, 0.33199, 0.63115, 0.40233, 0.73893, 0.4073, 0.74158, 0.54253, 0.8057, 0.71621, 0.99999, 0.84884, 1, 0.90684, 0.91717, 0.88152, 0.80673, 1, 0.52144, 1, 0.51453, 0.94061, 0.73311, 0.72113, 0.73476, 0.70415, 0.61744, 0.74693, 0.50294, 0.7398, 0.50082, 0.69507, 0.59694, 0.63154, 0.70296, 0.4792, 0.61771, 0.55894, 0.51864, 0.57086, 0.51215, 0.51574, 0.56019, 0.42215, 0.51991, 0.36049, 0.47994, 0.36697, 0.42788, 0.42211, 0.48513, 0.52259, 0.48725, 0.56667, 0.37625, 0.5611, 0.29156, 0.47812, 0.38124, 0.61036, 0.48584, 0.69853, 0.48725, 0.74132, 0.37064, 0.74715, 0.25781, 0.70503, 0.25455, 0.72113, 0.46852, 0.94272, 0.47312, 1, 0.15102, 1, 0.03388, 0.88761, 0, 0.91317, 1e-05, 0.85238, 0.17202, 0.74418, 0.24547, 0.59426, 0.24185, 0.4073, 0.36282, 0.39582, 0.42452, 0.32086, 0.40552, 0.18264, 0.45521, 0.15478, 0.45918, 1e-05, 0.53328, 0, 0.6973, 0.44808, 0.35762, 0.42916], "triangles": [39, 37, 38, 39, 36, 37, 34, 32, 33, 34, 31, 32, 34, 35, 31, 29, 27, 28, 29, 26, 27, 21, 22, 20, 22, 23, 20, 15, 16, 17, 14, 15, 18, 15, 17, 18, 12, 10, 11, 13, 10, 12, 23, 24, 2, 23, 2, 3, 19, 23, 52, 13, 9, 10, 9, 7, 8, 13, 6, 9, 9, 6, 7, 13, 14, 6, 14, 5, 6, 5, 14, 19, 19, 14, 18, 19, 4, 5, 19, 52, 4, 19, 20, 23, 23, 3, 52, 52, 3, 4, 25, 26, 47, 29, 53, 26, 53, 46, 26, 26, 46, 47, 53, 45, 46, 30, 45, 53, 39, 40, 36, 41, 42, 40, 40, 43, 36, 40, 42, 43, 36, 43, 35, 35, 43, 44, 31, 35, 30, 30, 35, 44, 44, 45, 30, 30, 53, 29, 25, 47, 24, 47, 0, 2, 47, 49, 0, 49, 50, 51, 0, 49, 51, 2, 0, 1, 47, 48, 49, 24, 47, 2], "vertices": [1, 27, 22.16, -9.95, 1, 1, 27, 17.87, -15.99, 1, 3, 27, -3.73, -10.63, 0.48, 29, 44.01, -37.77, 0.00067, 30, 44.38, 17.88, 0.51933, 2, 27, -16.52, -16.02, 0.808, 30, 34.02, 9.21, 0.192, 1, 30, 33.05, -5.12, 1, 2, 29, 13.07, -60.77, 0.00183, 30, 13.44, -5.12, 0.99817, 1, 30, -11.89, -13.19, 1, 1, 30, -31.58, -38.69, 1, 1, 30, -39.99, -38.54, 1, 1, 30, -36.12, -27.59, 1, 1, 36, 19.44, 20.49, 1, 1, 36, 49.28, -2.95, 1, 1, 36, 44.68, -10.29, 1, 1, 30, -12.44, -3.53, 1, 1, 30, -9.98, -3.79, 1, 1, 35, 26.6, 6.9, 1, 1, 35, 37.41, -3.88, 1, 1, 35, 33.36, -8.96, 1, 1, 35, 17.67, -7.51, 1, 2, 29, 22.34, -55.8, 0.00774, 30, 22.71, -0.15, 0.99226, 1, 34, 18.67, 7.62, 1, 1, 34, 28.12, -1.72, 1, 1, 34, 22.34, -7.31, 1, 2, 29, 30.95, -36.97, 0.00356, 30, 31.32, 18.69, 0.99644, 2, 27, -8.02, -4.27, 0.76, 30, 40.36, 23.89, 0.24, 2, 27, -8.89, -0.35, 0.744, 29, 39.14, -26.44, 0.256, 1, 29, 31.27, -19.37, 1, 1, 31, 23.2, 3.56, 1, 1, 31, 27.59, -1.09, 1, 1, 31, 15.89, -10.13, 1, 1, 29, 23.47, -1.1, 1, 1, 32, 11.06, 8.38, 1, 1, 32, 29.88, 6.7, 1, 2, 29, -15.15, -26.44, 0.12, 32, 33.81, 1.89, 0.88, 1, 32, 22.01, -8.22, 1, 1, 29, -9.34, 3.98, 1, 1, 29, -11.67, 4.45, 1, 1, 33, 41.48, 9.87, 1, 1, 33, 46.6, 3.3, 1, 1, 33, 10.96, -20.46, 1, 1, 29, -35.28, 34.23, 1, 1, 29, -38.91, 38.8, 1, 1, 29, -30.09, 38.64, 1, 1, 29, -14.82, 15.49, 1, 1, 29, 6.75, 5.33, 1, 1, 29, 33.86, 5.33, 1, 2, 27, -14.94, 16.66, 0.064, 29, 35.24, -10.79, 0.936, 2, 27, -2.68, 7.28, 0.304, 29, 45.96, -19.19, 0.696, 1, 27, 17.2, 9.58, 1, 1, 27, 23.96, 1.09, 1, 1, 27, 46.39, 0.16, 1, 1, 27, 46.22, -9.7, 1, 1, 30, 27.24, 0.53, 1, 1, 29, 30.42, -10.01, 1], "hull": 52, "edges": [84, 86, 90, 92, 96, 98, 98, 100, 100, 102, 102, 0, 0, 2, 6, 8, 12, 14, 82, 84, 26, 18, 14, 16, 18, 16, 20, 18, 72, 74, 74, 76, 82, 80, 80, 72, 76, 78, 80, 78, 26, 24, 20, 22, 24, 22, 86, 88, 88, 90, 8, 10, 10, 12, 92, 94, 94, 96, 2, 4, 4, 6, 60, 58, 72, 70, 70, 60, 70, 68, 68, 66, 66, 64, 64, 62, 62, 60, 58, 56, 56, 54, 54, 52, 52, 50, 34, 32, 32, 30, 30, 28, 28, 26, 34, 36, 36, 38, 38, 28, 46, 104, 104, 38, 60, 106, 106, 52, 46, 48, 48, 50, 46, 44, 44, 42, 42, 40, 40, 38], "width": 133, "height": 145}, "Mouth Closed": {"name": "images/ChaserBossMouthShut", "x": -40.53, "y": 0.18, "rotation": -91.02, "width": 105, "height": 131}}, "MouthBack": {"Mouth Back": {"name": "images/ChaserBossMouthBack", "type": "mesh", "uvs": [0.63525, 0.28525, 0.84154, 0.31237, 0.83063, 0.49565, 0.89299, 0.68697, 1, 0.82274, 1, 0.92354, 0.77719, 1, 0.59905, 1, 0.51443, 0.95234, 0.41645, 1, 0.16259, 1, 0, 0.90091, 0, 0.84331, 0.12251, 0.70754, 0.15591, 0.53268, 0.1849, 0.32653, 0.36381, 0.27025, 0.42536, 0, 0.49809, 0, 0.58569, 0, 0.51095, 0.2818, 0.50636, 0.57526], "triangles": [21, 16, 20, 14, 15, 16, 21, 14, 16, 10, 11, 12, 21, 13, 14, 8, 13, 21, 13, 9, 10, 10, 12, 13, 8, 9, 13, 20, 18, 19, 17, 18, 20, 16, 17, 20, 20, 19, 0, 2, 0, 1, 21, 20, 0, 21, 0, 2, 6, 7, 3, 3, 8, 21, 3, 21, 2, 7, 8, 3, 6, 3, 4, 5, 6, 4], "vertices": [2, 30, 34.03, 13.75, 0.904, 27, -14.91, -13.65, 0.096, 1, 30, 30.97, -6.43, 1, 1, 30, 11.72, -5.03, 1, 1, 30, -8.47, -10.72, 1, 1, 30, -22.91, -20.84, 1, 1, 30, -33.49, -20.65, 1, 1, 30, -41.13, 1.1, 1, 1, 30, -40.82, 18.38, 1, 2, 30, -35.67, 26.49, 0.512, 29, -36.04, -29.16, 0.488, 1, 29, -40.88, -19.57, 1, 1, 29, -40.44, 5.05, 1, 1, 29, -29.75, 20.63, 1, 1, 29, -23.71, 20.52, 1, 1, 29, -9.66, 8.39, 1, 1, 29, 8.63, 4.82, 1, 1, 29, 30.22, 1.64, 1, 1, 29, 35.83, -15.83, 1, 1, 27, 16.56, 3.45, 1, 1, 27, 16.63, -3.37, 1, 1, 27, 16.71, -11.58, 1, 3, 30, 35.15, 25.34, 0.26648, 27, -15.4, -1.38, 0.37073, 29, 34.78, -30.32, 0.36279, 2, 30, 3.93, 26.57, 0.50661, 29, 3.56, -29.09, 0.49339], "hull": 20, "edges": [24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 38, 0, 0, 2, 2, 4, 4, 6, 6, 8, 22, 24, 22, 20, 18, 20, 18, 16, 16, 14, 12, 14, 8, 10, 12, 10, 0, 40, 40, 32, 34, 36, 36, 38, 40, 36, 40, 42, 42, 16], "width": 97, "height": 105}}, "Spikes": {"Spikes": {"name": "images/ChaserBossSpikes", "type": "mesh", "uvs": [0.80635, 0.21036, 0.79061, 0.30683, 0.85358, 0.3659, 0.87982, 0.25458, 0.91262, 0.30356, 0.90606, 0.48611, 0.90137, 0.48981, 0.90699, 0.55569, 0.88176, 0.55329, 0.87864, 0.50767, 0.87082, 0.51382, 0.87843, 0.56288, 0.91572, 0.60345, 1, 0.52583, 1, 0.58856, 0.9646, 0.68214, 0.96494, 0.71844, 0.94518, 0.71878, 0.94594, 0.69149, 0.92867, 0.70014, 0.92663, 0.75856, 0.93089, 0.80017, 0.89958, 0.79914, 0.89897, 0.75719, 0.8988, 0.7151, 0.87717, 0.72115, 0.8783, 0.74378, 0.88012, 0.76819, 0.85641, 0.76957, 0.85458, 0.74653, 0.85312, 0.72787, 0.8189, 0.73743, 0.86402, 0.79934, 0.95708, 0.86633, 0.92507, 0.88538, 0.91673, 0.88481, 0.91557, 0.91424, 0.89551, 0.91562, 0.89005, 0.88299, 0.86118, 0.88103, 0.8625, 0.91582, 0.86371, 0.94797, 0.8403, 0.94969, 0.83635, 0.91633, 0.835, 0.87722, 0.81539, 0.87297, 0.81203, 0.89019, 0.81385, 0.91117, 0.79014, 0.91186, 0.78771, 0.89329, 0.78755, 0.86692, 0.73148, 0.78233, 0.75592, 0.87271, 0.84428, 1, 0.80207, 0.99874, 0.67601, 0.92479, 0.63583, 0.87042, 0.6121, 0.68769, 0.3737, 0.65058, 0.37964, 0.78945, 0.34794, 0.90883, 0.20102, 0.99999, 0.15722, 0.99925, 0.24245, 0.88129, 0.19212, 0.87154, 0.17109, 0.88176, 0.15814, 0.90196, 0.16366, 0.9463, 0.13771, 0.94817, 0.13275, 0.89759, 0.11067, 0.87886, 0.10349, 0.9182, 0.10239, 0.95629, 0.07699, 0.96066, 0.07423, 0.91695, 0.06827, 0.87682, 0.03331, 0.86838, 0.13539, 0.79783, 0.11905, 0.72189, 0.11508, 0.75139, 0.093, 0.74827, 0.09092, 0.71678, 0.07269, 0.71347, 0.07589, 0.73516, 0.07644, 0.77262, 0.07589, 0.8001, 0.04995, 0.8001, 0.04498, 0.73578, 0.03987, 0.68303, 0.03243, 0.67614, 1e-05, 0.58635, 1e-05, 0.52348, 0.04761, 0.58172, 0.08687, 0.54717, 0.12028, 0.51777, 0.09058, 0.47148, 0.09058, 0.28109, 0.1216, 0.24824, 0.16148, 0.40373, 0.19369, 0.36616, 0.22548, 0.32909, 0.1989, 0.27981, 0.19496, 0.22342, 0.28811, 0, 0.32091, 0, 0.29205, 0.19819, 0.43374, 0.41636, 0.56757, 0.41369, 0.70533, 0.19255, 0.67909, 0, 0.71976, 0, 0.68434, 0.49235, 0.79061, 0.4401, 0.83522, 0.40448, 0.82866, 0.54696, 0.70139, 0.59297, 0.30779, 0.48166, 0.29238, 0.48618, 0.28858, 0.60437, 0.09447, 0.60859, 0.19215, 0.62427, 0.18357, 0.73362, 0.2252, 0.84918, 0.26876, 0.77825, 0.31035, 0.60333, 0.20139, 0.42887, 0.26686, 0.40579, 0.23051, 0.4186, 0.1498, 0.61747, 0.18061, 0.54881, 0.1631, 0.58783, 0.15086, 0.72768, 0.16655, 0.75631, 0.25271, 0.84011, 0.75054, 0.37677, 0.87154, 0.61621, 0.70422, 0.62472, 0.61586, 0.50434, 0.65816, 0.51391, 0.85253, 0.58551, 0.84862, 0.88018], "triangles": [58, 124, 116, 117, 126, 116, 126, 127, 100, 106, 58, 116, 116, 126, 106, 106, 126, 105, 126, 100, 105, 100, 101, 105, 101, 102, 105, 102, 103, 105, 105, 103, 104, 127, 99, 100, 93, 94, 130, 120, 129, 118, 118, 124, 58, 124, 118, 117, 118, 129, 117, 117, 116, 124, 130, 94, 129, 129, 125, 117, 129, 94, 125, 94, 98, 125, 94, 95, 98, 125, 127, 117, 127, 126, 117, 95, 96, 98, 127, 125, 99, 125, 98, 99, 97, 98, 96, 78, 131, 77, 85, 86, 84, 84, 86, 87, 118, 121, 120, 87, 83, 84, 79, 80, 78, 80, 81, 78, 87, 82, 83, 87, 88, 82, 121, 131, 120, 120, 131, 128, 131, 78, 128, 128, 78, 119, 78, 81, 119, 81, 82, 119, 82, 88, 119, 88, 89, 119, 89, 92, 119, 89, 90, 92, 128, 130, 120, 130, 129, 120, 128, 119, 130, 92, 93, 119, 119, 93, 130, 90, 91, 92, 61, 63, 60, 61, 62, 63, 63, 133, 60, 60, 133, 59, 59, 133, 123, 123, 58, 59, 123, 118, 58, 63, 122, 133, 57, 58, 137, 137, 58, 106, 137, 106, 107, 73, 74, 72, 72, 74, 71, 68, 66, 67, 68, 69, 66, 71, 74, 70, 74, 75, 70, 66, 69, 65, 69, 70, 65, 64, 65, 77, 64, 122, 63, 77, 65, 70, 70, 75, 77, 75, 76, 77, 64, 77, 122, 123, 133, 122, 122, 77, 132, 123, 122, 132, 123, 132, 121, 131, 132, 77, 123, 121, 118, 132, 131, 121, 53, 54, 52, 52, 54, 55, 55, 56, 52, 52, 56, 51, 56, 57, 51, 57, 136, 51, 42, 40, 41, 42, 43, 40, 40, 140, 39, 40, 43, 140, 43, 44, 140, 36, 37, 35, 35, 37, 38, 47, 48, 46, 48, 49, 46, 49, 50, 46, 46, 50, 45, 34, 35, 33, 33, 35, 32, 32, 35, 38, 32, 38, 39, 32, 39, 140, 140, 44, 32, 44, 45, 32, 45, 50, 32, 50, 31, 32, 50, 51, 31, 51, 136, 31, 22, 20, 21, 22, 23, 20, 27, 28, 26, 28, 29, 26, 19, 20, 24, 24, 20, 23, 29, 30, 26, 30, 25, 26, 30, 31, 135, 30, 135, 25, 135, 31, 136, 25, 135, 24, 17, 18, 16, 18, 15, 16, 24, 135, 19, 135, 12, 19, 19, 12, 18, 18, 12, 15, 15, 12, 14, 136, 139, 135, 135, 11, 12, 135, 139, 11, 14, 12, 13, 139, 114, 11, 111, 134, 112, 57, 115, 136, 115, 137, 138, 115, 57, 137, 136, 114, 139, 136, 115, 114, 138, 111, 115, 115, 111, 114, 7, 9, 6, 11, 114, 10, 7, 8, 9, 111, 112, 114, 114, 112, 10, 138, 137, 111, 9, 10, 113, 10, 112, 113, 9, 113, 6, 6, 113, 5, 113, 2, 5, 5, 2, 4, 112, 134, 113, 2, 113, 1, 2, 3, 4, 137, 107, 111, 111, 107, 134, 107, 108, 134, 113, 134, 1, 134, 108, 1, 1, 108, 0, 108, 110, 0, 108, 109, 110], "vertices": [1, 4, 122.11, -15.36, 1, 1, 4, 82.15, -32.19, 1, 1, 5, 108.55, 36.45, 1, 1, 5, 143.4, 73.58, 1, 1, 5, 147.6, 46.75, 1, 2, 5, 106.52, -22.77, 0.96532, 6, 87.8, 74.67, 0.03468, 1, 5, 103.68, -23.1, 1, 1, 5, 92.37, -50.08, 1, 2, 5, 81.76, -43.17, 0.95787, 6, 71.05, 47.31, 0.04213, 1, 5, 89.93, -24.68, 1, 1, 5, 85.2, -25.22, 1, 2, 5, 78.29, -46.12, 0.4346, 6, 68.72, 43.39, 0.5654, 1, 6, 84.26, 22.71, 1, 1, 6, 131.35, 49.83, 1, 1, 6, 126.91, 22.46, 1, 1, 6, 102.82, -15.54, 1, 1, 6, 100.42, -31.4, 1, 1, 6, 90.64, -29.97, 1, 1, 6, 92.94, -18.12, 1, 1, 6, 83.81, -20.51, 1, 1, 6, 78.67, -45.84, 1, 1, 6, 77.83, -64.34, 1, 1, 6, 62.45, -61.38, 1, 1, 6, 65.12, -43.03, 1, 1, 6, 68.01, -24.65, 1, 1, 6, 56.9, -25.56, 1, 1, 6, 55.86, -35.52, 1, 1, 6, 55.03, -46.32, 1, 1, 6, 43.23, -45.02, 1, 1, 6, 43.96, -34.82, 1, 1, 6, 44.56, -26.57, 1, 2, 6, 27, -28, 0.37654, 7, 28.18, 18.82, 0.62346, 1, 7, 63.64, 19.25, 1, 1, 7, 115.88, 36.95, 1, 1, 7, 112.4, 19.2, 1, 1, 7, 109.59, 16.11, 1, 1, 7, 119.37, 7.5, 1, 1, 7, 113.55, -0.7, 1, 1, 7, 100.61, 6.22, 1, 1, 7, 90.89, -4.49, 1, 2, 7, 103.28, -13.61, 0.99999, 8, 109.27, 62.98, 1e-05, 2, 7, 114.73, -22.05, 0.99998, 8, 122.58, 57.97, 2e-05, 2, 7, 107.99, -31.64, 0.99998, 8, 118.7, 46.91, 2e-05, 2, 7, 95.26, -23.94, 0.99998, 8, 104.36, 50.87, 2e-05, 2, 7, 81.37, -13.63, 0.99991, 8, 88.19, 57.02, 9e-05, 2, 7, 73.76, -20.09, 0.99811, 8, 82.62, 48.74, 0.00189, 2, 7, 78.64, -26.18, 0.99896, 8, 88.96, 44.21, 0.00104, 1, 7, 86.43, -31.28, 1, 1, 7, 79.24, -40.71, 1, 2, 7, 72.08, -36.51, 0.99911, 8, 85.46, 32.48, 0.00089, 2, 7, 62.95, -29.27, 0.98939, 8, 74.71, 36.98, 0.01061, 2, 7, 16.24, -27.68, 0.50964, 8, 29.32, 25.84, 0.49036, 1, 8, 70.86, 21.43, 1, 1, 8, 139.94, 40.02, 1, 1, 8, 131.15, 20.83, 1, 1, 8, 76.38, -24.35, 1, 1, 8, 46.4, -33.42, 1, 11, 4, -107.55, -49.99, 0.00085, 5, -65.13, -31.69, 0.18573, 6, -71.55, 10.25, 0.06693, 7, -53.76, -47.97, 0.0074, 8, -32.56, -12.69, 0.4607, 12, -146.68, 105.98, 1e-05, 2, 39.57, -54.03, 0.27803, 13, -84.19, 100.45, 0.00013, 11, -188.93, -40.62, 2e-05, 10, -181.18, -16.44, 0.00016, 9, -164.18, -36.17, 5e-05, 9, 5, -162.37, 39.18, 0.00062, 6, -186.59, 45.53, 9e-05, 8, -94.36, -115.93, 0.00093, 12, -68.26, 14.72, 0.05285, 2, 58.1, 64.87, 0.2975, 13, -40.78, -11.78, 0.29897, 11, -69.01, -30.77, 0.08203, 10, -67.98, 24.34, 0.22687, 9, -83.24, 52.87, 0.04014, 1, 13, 11.47, 20.56, 1, 1, 13, 65.31, 32.26, 1, 1, 13, 136.15, -12.48, 1, 1, 13, 146.48, -31.8, 1, 1, 13, 80.22, -19.78, 1, 2, 12, 64.44, 27.72, 0.84365, 13, 88.64, -43.88, 0.15635, 2, 12, 75.31, 24.13, 0.93155, 13, 97.69, -50.89, 0.06845, 2, 12, 86.08, 26.53, 0.98417, 13, 108.64, -52.23, 0.01583, 1, 12, 97, 43.04, 1, 2, 12, 107.26, 35.06, 0.99867, 13, 131.45, -51.27, 0.00133, 2, 12, 94.31, 16.67, 0.99074, 13, 113.1, -64.27, 0.00926, 2, 12, 97.09, 3.15, 0.99925, 13, 111.2, -77.94, 0.00075, 1, 12, 111.3, 13.8, 1, 1, 12, 122.87, 26.04, 1, 1, 12, 133.66, 19.08, 1, 1, 12, 121.89, 3.69, 1, 1, 12, 112.37, -11.57, 1, 2, 12, 122.99, -25.94, 0.99994, 11, 76.25, 100.12, 6e-05, 1, 12, 64.1, -15.48, 1, 1, 11, 48.45, 27.62, 1, 1, 11, 47.56, 40.78, 1, 1, 11, 58.63, 41.82, 1, 1, 11, 62.67, 28.46, 1, 1, 11, 71.88, 29.01, 1, 1, 11, 68.24, 38.02, 1, 1, 11, 64.38, 54.13, 1, 1, 11, 62.01, 66.04, 1, 1, 11, 74.68, 68.86, 1, 1, 11, 83.27, 41.64, 1, 1, 11, 90.82, 19.44, 1, 1, 11, 95.11, 17.27, 1, 1, 11, 119.55, -17.95, 1, 1, 11, 125.57, -45.08, 1, 1, 11, 96.76, -25.12, 1, 2, 11, 80.91, -44.28, 0.67462, 10, 80.27, 50.39, 0.32538, 1, 10, 71.51, 31.13, 1, 1, 10, 94.15, 19.9, 1, 1, 10, 133.2, -54.65, 1, 2, 10, 126.2, -74.7, 0.984, 9, 134.71, 56.25, 0.016, 1, 10, 76.65, -23.08, 1, 2, 10, 70.08, -45.26, 0.45668, 9, 71.34, 56, 0.54332, 1, 9, 75.86, 33.62, 1, 1, 9, 101.34, 32.27, 1, 2, 10, 98.8, -101.45, 0.00541, 9, 123.01, 19.8, 0.99459, 1, 9, 178.11, -74.46, 1, 1, 9, 168.84, -87.99, 1, 1, 9, 104.76, -26.55, 1, 5, 4, -56.26, 90.38, 0.00093, 2, 161.07, 33, 0.40099, 13, -145.89, -35.67, 0.00076, 10, -46.54, -81.3, 0.00462, 9, -14.84, -30.44, 0.59269, 3, 4, -18.55, 35.1, 0.54343, 5, -27.42, 85.53, 0.01939, 2, 161.06, -33.92, 0.43718, 1, 4, 100.96, 31.18, 1, 1, 4, 164.9, 88.87, 1, 1, 4, 176.06, 71.87, 1, 3, 4, -15.56, -32.78, 0.35429, 5, 7.56, 27.27, 0.6128, 2, 125.25, -91.68, 0.03291, 1, 5, 65.29, 22.47, 1, 1, 5, 92.39, 25.78, 1, 1, 5, 59.69, -28.15, 1, 6, 5, -5.98, -15.94, 0.7113, 6, -20.78, 44.43, 0.22539, 8, -53.58, 44.79, 0.04842, 2, 80.63, -99.41, 0.01486, 13, -142.44, 119.22, 1e-05, 10, -201.3, -74.24, 1e-05, 1, 9, -3.03, 37.82, 1, 1, 10, 1.76, -21.17, 1, 9, 5, -190.19, 77.32, 0.00013, 6, -225.33, 72.5, 2e-05, 8, -129.83, -147.08, 0.0002, 12, -49.92, -28.78, 0.04483, 2, 79.28, 107.06, 0.0558, 13, -38.03, -58.91, 0.07799, 11, -23.03, -41.48, 0.29668, 10, -20.8, 26, 0.52176, 9, -42.33, 76.42, 0.00258, 1, 11, 71.31, -18.6, 1, 1, 11, 22.13, -22.43, 1, 2, 12, 27.26, -20.77, 0.53811, 11, 15.83, 25.68, 0.46189, 1, 12, 45.51, 31.27, 1, 2, 12, 8.42, 22.22, 0.62227, 13, 34, -30.34, 0.37773, 9, 5, -180.38, 72.57, 0.00022, 6, -214.51, 71.21, 3e-05, 8, -125.98, -136.89, 0.00033, 12, -58.38, -21.91, 0.04702, 2, 79.54, 96.16, 0.10531, 13, -43.71, -49.6, 0.11619, 11, -33.56, -44.29, 0.20818, 10, -30.23, 20.53, 0.49807, 9, -48.11, 67.18, 0.02465, 1, 10, 53.81, -22.49, 1, 1, 9, 36.2, 35.73, 1, 2, 10, 43.02, -33.27, 0.52742, 9, 41.81, 53.93, 0.47258, 1, 11, 43.45, -20.77, 1, 1, 10, 38.42, 29.29, 1, 2, 11, 39.8, -35, 0.62598, 10, 38.17, 48.63, 0.37402, 1, 11, 32.36, 26.66, 1, 1, 12, 40.27, -18.9, 1, 1, 13, 61.81, -24.11, 1, 1, 4, 45.31, -32.41, 1, 1, 6, 61.55, 20.68, 1, 8, 5, -11.38, -28.97, 0.47392, 6, -21.63, 30.36, 0.39523, 7, -46.58, 5.36, 0.00415, 8, -40.11, 40.6, 0.09949, 2, 66.58, -100.57, 0.02714, 13, -130.85, 127.25, 2e-05, 10, -209.07, -62.47, 2e-05, 9, -167.27, -89.9, 1e-05, 6, 4, -38.78, -7.08, 0.36994, 5, -25.11, 38.81, 0.30001, 6, -56.72, 89.95, 4e-05, 8, -106.38, 20.8, 0.01026, 2, 120.57, -57.35, 0.31973, 10, -145.24, -89.11, 1e-05, 4, 4, -30.71, -27.08, 0.29796, 5, -8.48, 25.07, 0.57988, 8, -94.2, 38.6, 0.00577, 2, 115.96, -78.42, 0.11637, 2, 5, 62.15, -48.81, 0.36841, 6, 54.34, 35.59, 0.63159, 1, 7, 86.66, -9.14, 1], "hull": 111, "edges": [214, 216, 216, 218, 218, 220, 220, 0, 0, 2, 222, 224, 224, 226, 226, 4, 4, 6, 6, 8, 8, 10, 228, 230, 210, 212, 210, 208, 206, 208, 206, 204, 204, 202, 234, 232, 196, 194, 194, 192, 192, 190, 190, 188, 182, 184, 184, 238, 240, 236, 180, 182, 180, 178, 154, 152, 244, 246, 126, 124, 122, 124, 122, 120, 120, 118, 118, 116, 116, 248, 202, 200, 196, 250, 250, 234, 232, 252, 252, 200, 196, 198, 198, 200, 250, 254, 254, 252, 238, 256, 256, 240, 188, 258, 258, 236, 188, 186, 186, 184, 256, 260, 260, 258, 156, 154, 242, 262, 262, 156, 242, 264, 264, 154, 262, 264, 130, 128, 128, 244, 126, 128, 246, 266, 266, 126, 244, 266, 2, 268, 268, 222, 268, 224, 2, 4, 272, 270, 24, 26, 26, 28, 30, 28, 62, 64, 64, 66, 102, 100, 68, 66, 102, 104, 104, 106, 106, 108, 112, 114, 108, 110, 110, 112, 114, 272, 214, 274, 274, 276, 276, 230, 230, 272, 20, 228, 20, 22, 22, 24, 228, 278, 278, 270, 18, 20, 18, 16, 16, 14, 10, 12, 12, 18, 14, 12, 36, 30, 36, 34, 34, 32, 32, 30, 48, 46, 46, 44, 44, 42, 42, 40, 48, 38, 38, 36, 40, 38, 62, 60, 60, 58, 58, 56, 56, 54, 54, 52, 48, 50, 50, 60, 52, 50, 100, 98, 98, 96, 96, 94, 94, 92, 86, 84, 84, 82, 78, 280, 88, 280, 86, 88, 100, 90, 90, 88, 92, 90, 76, 78, 76, 74, 74, 72, 68, 70, 70, 76, 72, 70, 152, 150, 150, 148, 148, 146, 146, 144, 144, 142, 130, 140, 140, 150, 142, 140, 140, 138, 138, 136, 136, 134, 134, 132, 132, 130, 178, 176, 176, 164, 176, 174, 174, 172, 172, 170, 170, 168, 168, 166, 166, 164, 164, 162, 162, 156, 162, 160, 160, 158, 158, 156, 82, 80, 80, 78, 116, 114, 212, 214], "width": 500, "height": 442}}, "Tentacle3": {"Tentacle3": {"name": "images/ChaserTongue_2", "type": "mesh", "uvs": [1, 0.15899, 1, 0.30532, 0.95794, 0.43346, 0.86524, 0.65153, 0.81499, 0.75451, 0.73124, 0.86452, 0.62404, 1, 0.45319, 1, 0.41299, 0.94527, 0.39112, 0.86215, 0.27722, 0.73927, 0.17002, 0.64799, 0.06282, 0.53213, 0, 0.41882, 0, 0.2703, 0.07967, 0.12648, 0.33922, 0, 1, 0, 0.65419, 0.95347, 0.90209, 0.54948], "triangles": [6, 18, 5, 6, 7, 18, 7, 8, 18, 5, 18, 9, 18, 8, 9, 5, 9, 4, 9, 10, 4, 15, 16, 0, 16, 17, 0, 13, 14, 1, 1, 14, 0, 0, 14, 15, 2, 13, 1, 4, 10, 3, 10, 11, 3, 3, 19, 2, 3, 11, 19, 11, 12, 19, 19, 12, 2, 12, 13, 2], "vertices": [3, 16, -57.13, 30.73, 0.06116, 15, 9.85, 30.63, 0.49351, 2, -12.26, 26.94, 0.44533, 3, 16, -23.67, 28.87, 0.23182, 15, 43.32, 28.87, 0.65618, 2, -45.76, 27.54, 0.112, 3, 16, 5.44, 23.89, 0.544, 15, 72.44, 23.97, 0.384, 17, -62.27, 25.87, 0.072, 3, 16, 54.89, 13.71, 0.61804, 15, 121.92, 13.94, 0.17, 17, -13.07, 14.54, 0.21196, 2, 16, 78.21, 8.39, 0.40006, 17, 10.13, 8.68, 0.59994, 2, 16, 103, 0.3, 0.12204, 17, 34.71, 0.02, 0.87796, 2, 16, 133.5, -9.98, 0.012, 17, 64.96, -10.97, 0.988, 1, 17, 63.89, -24.6, 1, 2, 16, 120.05, -26.14, 0.02674, 17, 51.14, -26.82, 0.97326, 2, 16, 100.94, -26.83, 0.18, 17, 32.03, -27.06, 0.82, 2, 16, 72.34, -34.37, 0.40667, 17, 3.26, -33.93, 0.59333, 3, 16, 50.99, -41.77, 0.62432, 15, 118.19, -41.56, 0.08895, 17, -18.25, -40.84, 0.28674, 3, 16, 24.03, -48.87, 0.62039, 15, 91.24, -48.73, 0.27295, 17, -45.38, -47.3, 0.10667, 2, 16, -2.16, -52.44, 0.44172, 15, 65.07, -52.39, 0.55828, 3, 16, -36.12, -50.56, 0.19733, 15, 31.1, -50.6, 0.70133, 2, -36.32, 107.38, 0.10133, 3, 16, -68.65, -42.37, 0.048, 15, -1.45, -42.5, 0.51733, 2, -3.5, 100.42, 0.43467, 2, 15, -29.28, -20.25, 0.232, 2, 25.09, 79.14, 0.768, 3, 16, -93.48, 32.75, 0.01316, 15, -26.51, 32.54, 0.20818, 2, 24.14, 26.29, 0.77867, 2, 16, 122.99, -6.98, 0.016, 17, 54.53, -7.73, 0.984, 3, 16, 31.72, 17.95, 0.67733, 15, 98.74, 18.11, 0.22667, 17, -36.13, 19.32, 0.096], "hull": 18, "edges": [26, 28, 28, 30, 32, 34, 30, 32, 26, 24, 24, 22, 22, 20, 20, 18, 18, 16, 16, 14, 12, 14, 12, 36, 36, 10, 10, 8, 8, 6, 6, 38, 38, 4, 4, 2, 2, 0, 0, 34, 4, 6, 10, 12], "width": 80, "height": 229}}, "Tentacle4": {"Tentacle4": {"name": "images/ChaserTongue_2", "type": "mesh", "uvs": [1, 0.15899, 1, 0.30532, 0.95794, 0.43346, 0.86524, 0.65153, 0.81499, 0.75451, 0.73124, 0.86452, 0.62404, 1, 0.45319, 1, 0.41299, 0.94527, 0.39112, 0.86215, 0.27722, 0.73927, 0.17002, 0.64799, 0.06282, 0.53213, 0, 0.41882, 0, 0.2703, 0.07967, 0.12648, 0.33922, 0, 1, 0, 0.65419, 0.95347, 0.90209, 0.54948], "triangles": [6, 18, 5, 6, 7, 18, 7, 8, 18, 5, 18, 9, 18, 8, 9, 5, 9, 4, 9, 10, 4, 15, 16, 0, 16, 17, 0, 13, 14, 1, 1, 14, 0, 0, 14, 15, 2, 13, 1, 4, 10, 3, 10, 11, 3, 3, 19, 2, 3, 11, 19, 11, 12, 19, 19, 12, 2, 12, 13, 2], "vertices": [3, 25, -58.65, 31.22, 0.06116, 24, 8.34, 31.11, 0.49351, 2, -9.91, -3.3, 0.44533, 3, 25, -25.19, 29.36, 0.23182, 24, 41.8, 29.35, 0.65618, 2, -43.41, -2.7, 0.112, 3, 25, 3.92, 24.37, 0.544, 24, 70.92, 24.45, 0.384, 26, -63.77, 26.39, 0.072, 3, 25, 53.37, 14.2, 0.61804, 24, 120.4, 14.42, 0.17, 26, -14.57, 15.06, 0.21196, 2, 25, 76.7, 8.87, 0.40006, 26, 8.62, 9.2, 0.59994, 2, 25, 101.48, 0.79, 0.12204, 26, 33.21, 0.54, 0.87796, 2, 25, 131.98, -9.5, 0.012, 26, 63.46, -10.45, 0.988, 1, 26, 62.38, -24.08, 1, 2, 25, 118.53, -25.66, 0.02674, 26, 49.64, -26.3, 0.97326, 2, 25, 99.43, -26.35, 0.18, 26, 30.52, -26.54, 0.82, 2, 25, 70.83, -33.89, 0.40667, 26, 1.75, -33.41, 0.59333, 3, 25, 49.48, -41.29, 0.62432, 24, 116.67, -41.08, 0.08895, 26, -19.76, -40.32, 0.28674, 3, 25, 22.51, -48.38, 0.62039, 24, 89.72, -48.25, 0.27295, 26, -46.88, -46.78, 0.10667, 2, 25, -3.68, -51.96, 0.44172, 24, 63.55, -51.91, 0.55828, 3, 25, -45.06, -62.81, 0.19733, 24, 22.19, -62.87, 0.70133, 2, -26.32, 64.54, 0.10133, 3, 25, -77.79, -105.16, 0.048, 24, -10.41, -105.33, 0.51733, 2, 7.57, 7.04, 0.43467, 2, 24, -28.87, -105.03, 0.232, 2, 27.23, -36.38, 0.768, 3, 25, -96.01, 24.67, 0.01316, 24, -29.01, 24.45, 0.20818, 2, 27.66, -12.5, 0.77867, 2, 25, 121.47, -6.5, 0.016, 26, 53.03, -7.21, 0.984, 3, 25, 30.2, 18.44, 0.67733, 24, 97.22, 18.59, 0.22667, 26, -37.64, 19.84, 0.096], "hull": 18, "edges": [26, 28, 28, 30, 32, 34, 30, 32, 26, 24, 24, 22, 22, 20, 20, 18, 18, 16, 16, 14, 12, 14, 12, 36, 36, 10, 10, 8, 8, 6, 6, 38, 38, 4, 4, 2, 2, 0, 0, 34, 4, 6, 10, 12], "width": 80, "height": 229}}, "Tentacle5": {"Tentacle5": {"name": "images/ChaserTongue_3", "type": "mesh", "uvs": [1, 0.10807, 1, 0.23422, 1, 0.3513, 1, 0.46837, 0.95426, 0.58444, 0.89453, 0.67888, 0.81091, 0.78284, 0.67952, 0.90395, 0.5093, 1, 0.33311, 1, 0.34505, 0.88679, 0.29727, 0.78082, 0.21979, 0.67979, 0.12014, 0.58732, 0.01075, 0.4739, 0, 0.35413, 1e-05, 0.23927, 0.07927, 0.12018, 0.28831, 0, 0.75119, 0], "triangles": [7, 8, 10, 8, 9, 10, 6, 7, 11, 7, 10, 11, 5, 6, 12, 6, 11, 12, 3, 14, 2, 4, 12, 13, 4, 13, 3, 3, 13, 14, 5, 12, 4, 18, 19, 0, 1, 17, 18, 0, 1, 18, 2, 14, 15, 1, 2, 16, 1, 16, 17, 2, 15, 16], "vertices": [2, 18, -1.57, 46.89, 0.51733, 2, 5.28, 42.35, 0.48267, 3, 18, 25.59, 44.72, 0.71611, 2, -21.96, 42.83, 0.14933, 19, -33.72, 45.38, 0.13455, 3, 18, 50.8, 42.71, 0.63089, 19, -8.49, 43.69, 0.36821, 20, -63.14, 42.65, 0.0009, 3, 18, 76, 40.69, 0.33489, 19, 16.75, 42.01, 0.59203, 20, -37.89, 41.39, 0.07307, 3, 18, 100.73, 35.36, 0.13611, 19, 41.54, 37.01, 0.60083, 20, -13.01, 36.8, 0.26305, 3, 18, 120.72, 29.39, 0.03755, 19, 61.6, 31.3, 0.4269, 20, 7.14, 31.43, 0.53555, 3, 18, 142.61, 21.51, 0.00022, 19, 83.6, 23.72, 0.20307, 20, 29.26, 24.21, 0.79671, 3, 18, 167.92, 9.86, 0.00022, 19, 109.07, 12.41, 0.05972, 20, 54.91, 13.32, 0.94006, 1, 20, 75.01, -0.12, 1, 1, 20, 74.37, -12.97, 1, 2, 19, 103.74, -11.71, 0.07467, 20, 49.99, -10.88, 0.92533, 2, 19, 80.67, -13.66, 0.20025, 20, 26.96, -13.22, 0.79975, 3, 18, 116.98, -19.73, 0.03123, 19, 58.52, -17.86, 0.44206, 20, 4.88, -17.78, 0.5267, 3, 18, 96.49, -25.39, 0.14857, 19, 38.11, -23.79, 0.5834, 20, -15.43, -24.05, 0.26804, 3, 18, 71.44, -31.39, 0.33662, 19, 13.13, -30.12, 0.60309, 20, -40.3, -30.8, 0.06028, 2, 18, 45.59, -30.11, 0.63873, 19, -12.73, -29.19, 0.36127, 3, 18, 20.85, -28.13, 0.68381, 2, -21.75, 115.84, 0.17067, 19, -37.49, -27.53, 0.14552, 3, 18, -4.33, -20.31, 0.49576, 2, 3.87, 109.6, 0.504, 19, -62.77, -20.05, 0.00024, 3, 18, -28.98, -3.03, 0.16242, 2, 29.55, 93.88, 0.83733, 19, -87.65, -3.1, 0.00024, 2, 18, -26.29, 30.65, 0.184, 2, 28.95, 60.09, 0.816], "hull": 20, "edges": [30, 28, 28, 26, 26, 24, 24, 22, 22, 20, 20, 18, 16, 18, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 30, 32, 32, 34, 34, 36, 36, 38, 38, 0, 0, 2, 2, 4], "width": 73, "height": 216}}, "Tentacle6": {"Tentacle6": {"name": "images/ChaserTongue_3", "type": "mesh", "uvs": [1, 0.10807, 1, 0.23422, 1, 0.3513, 1, 0.46837, 0.95426, 0.58444, 0.89453, 0.67888, 0.81091, 0.78284, 0.67952, 0.90395, 0.5093, 1, 0.33311, 1, 0.34505, 0.88679, 0.29727, 0.78082, 0.21979, 0.67979, 0.12014, 0.58732, 0.01075, 0.4739, 0, 0.35413, 1e-05, 0.23927, 0.07927, 0.12018, 0.28831, 0, 0.75119, 0], "triangles": [7, 8, 10, 8, 9, 10, 6, 7, 11, 7, 10, 11, 5, 6, 12, 6, 11, 12, 3, 14, 2, 4, 12, 13, 4, 13, 3, 3, 13, 14, 5, 12, 4, 18, 19, 0, 1, 17, 18, 0, 1, 18, 2, 14, 15, 1, 2, 16, 1, 16, 17, 2, 15, 16], "vertices": [2, 21, -6.89, 6.31, 0.51733, 2, 9.15, 1.6, 0.48267, 3, 21, 23.41, 32.95, 0.71611, 2, -20.21, 30.99, 0.14933, 22, -35.74, 33.58, 0.13455, 3, 21, 50.8, 42.71, 0.63089, 22, -8.49, 43.69, 0.36821, 23, -63.14, 42.65, 0.0009, 3, 21, 76, 40.69, 0.33489, 22, 16.75, 42.01, 0.59203, 23, -37.89, 41.39, 0.07307, 3, 21, 100.73, 35.36, 0.13611, 22, 41.54, 37.01, 0.60083, 23, -13.01, 36.8, 0.26305, 3, 21, 120.72, 29.39, 0.03755, 22, 61.6, 31.3, 0.4269, 23, 7.14, 31.43, 0.53555, 3, 21, 142.61, 21.51, 0.00022, 22, 83.6, 23.72, 0.20307, 23, 29.26, 24.21, 0.79671, 3, 21, 167.92, 9.86, 0.00022, 22, 109.07, 12.41, 0.05972, 23, 54.91, 13.32, 0.94006, 1, 23, 75.01, -0.12, 1, 1, 23, 74.37, -12.97, 1, 2, 22, 103.74, -11.71, 0.07467, 23, 49.99, -10.88, 0.92533, 2, 22, 80.67, -13.66, 0.20025, 23, 26.96, -13.22, 0.79975, 3, 21, 116.98, -19.73, 0.03123, 22, 58.52, -17.86, 0.44206, 23, 4.88, -17.78, 0.5267, 3, 21, 96.49, -25.39, 0.14857, 22, 38.11, -23.79, 0.5834, 23, -15.43, -24.05, 0.26804, 3, 21, 71.44, -31.39, 0.33662, 22, 13.13, -30.12, 0.60309, 23, -40.3, -30.8, 0.06028, 2, 21, 45.59, -30.11, 0.63873, 22, -12.73, -29.19, 0.36127, 3, 21, 7.07, -65.28, 0.68381, 2, -9.3, 78.22, 0.17067, 22, -50.78, -64.87, 0.14552, 3, 21, -7.55, -127.86, 0.49576, 2, 3.26, 2.01, 0.504, 22, -64.57, -127.63, 0.00024, 3, 21, -25.21, -159.91, 0.16242, 2, 20.19, -62.77, 0.83733, 22, -81.8, -159.91, 0.00024, 2, 21, -21.44, -65.88, 0.184, 2, 20.66, -36.21, 0.816], "hull": 20, "edges": [30, 28, 28, 26, 26, 24, 24, 22, 22, 20, 20, 18, 16, 18, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 30, 32, 32, 34, 34, 36, 36, 38, 38, 0, 0, 2, 2, 4], "width": 73, "height": 216}}}}, {"name": "<PERSON><PERSON><PERSON>", "attachments": {"Drips2": {"Drips2": {"name": "images/Drips", "type": "<PERSON><PERSON><PERSON>", "skin": "Chaser", "parent": "Drips2", "width": 175, "height": 138}}, "Head": {"Head": {"name": "images/ScuttleBossHead", "type": "mesh", "uvs": [0.60408, 0.00681, 0.75319, 0.04748, 0.86232, 0.15404, 0.92948, 0.22601, 0.94686, 0.32294, 0.95081, 0.36577, 0.98675, 0.4377, 1, 0.65799, 0.99996, 0.83938, 0.96607, 0.95834, 0.84611, 1, 0.66358, 1, 0.51936, 0.99998, 0.36722, 0.99996, 0.20082, 1, 0.07621, 1, 0, 0.89581, 0, 0.68, 0.01334, 0.43285, 0.02654, 0.329, 0.06652, 0.26546, 0.1173, 0.23115, 0.13862, 0.18781, 0.19701, 0.07971, 0.32289, 0, 0.46127, 0, 0.53753, 0, 0.34662, 0.1912, 0.31968, 0.37523, 0.29907, 0.56332, 0.30224, 0.68061, 0.13742, 0.8505, 0.13425, 0.6887, 0.16437, 0.44602, 0.71112, 0.68061, 0.7127, 0.54916, 0.70003, 0.37119, 0.66041, 0.15479, 0.51144, 0.90105, 0.50034, 0.47433, 0.49876, 0.27614, 0.49717, 0.13053, 0.50643, 0.81215, 0.3179, 0.85303, 0.70507, 0.85712, 0.50291, 0.61], "triangles": [11, 44, 10, 44, 11, 38, 14, 43, 13, 13, 38, 12, 11, 12, 38, 13, 43, 38, 43, 42, 38, 38, 42, 44, 44, 34, 8, 44, 42, 34, 31, 30, 43, 43, 30, 42, 30, 45, 42, 42, 45, 34, 32, 29, 30, 34, 45, 35, 30, 29, 45, 29, 39, 45, 45, 39, 35, 29, 28, 39, 39, 36, 35, 28, 40, 39, 39, 40, 36, 9, 10, 8, 15, 31, 14, 14, 31, 43, 15, 16, 31, 8, 10, 44, 31, 17, 32, 31, 16, 17, 31, 32, 30, 8, 34, 7, 32, 33, 29, 33, 32, 18, 34, 35, 7, 32, 17, 18, 35, 6, 7, 29, 33, 28, 35, 5, 6, 5, 36, 4, 4, 36, 3, 3, 36, 2, 35, 36, 5, 18, 19, 33, 19, 20, 33, 20, 21, 33, 33, 21, 28, 21, 22, 28, 28, 22, 27, 28, 27, 40, 27, 22, 23, 40, 37, 36, 2, 37, 1, 2, 36, 37, 37, 41, 0, 0, 41, 26, 27, 41, 40, 41, 37, 40, 23, 24, 27, 27, 25, 41, 27, 24, 25, 37, 0, 1, 41, 25, 26], "vertices": [1, 2, 259.44, -37.88, 1, 1, 2, 247.63, -88.68, 1, 1, 2, 218.41, -125.48, 1, 1, 2, 198.72, -148.11, 1, 1, 2, 172.64, -153.58, 1, 1, 2, 161.13, -154.73, 1, 1, 2, 141.64, -166.67, 1, 1, 2, 82.53, -170.15, 1, 1, 2, 33.93, -169.27, 1, 1, 2, 2.26, -157.11, 1, 2, 2, -8.17, -115.89, 0.792, 27, -90.21, -115.99, 0.208, 2, 2, -7.06, -53.48, 0.184, 27, -89.1, -53.57, 0.816, 2, 2, -6.17, -4.16, 0.072, 27, -88.21, -4.26, 0.928, 2, 2, -5.24, 47.86, 0.216, 27, -87.28, 47.77, 0.784, 2, 2, -4.23, 104.76, 0.632, 27, -86.27, 104.67, 0.368, 1, 2, -3.47, 147.37, 1, 1, 2, 24.91, 172.93, 1, 1, 2, 82.74, 171.9, 1, 1, 2, 148.89, 166.15, 1, 1, 2, 176.63, 161.15, 1, 1, 2, 193.41, 147.17, 1, 1, 2, 202.3, 129.64, 1, 1, 2, 213.78, 122.14, 1, 1, 2, 242.39, 101.66, 1, 1, 2, 262.98, 58.24, 1, 1, 2, 262.14, 10.92, 1, 1, 2, 261.67, -15.16, 1, 2, 2, 211.6, 51.04, 0.808, 27, 129.56, 50.94, 0.192, 2, 2, 162.45, 61.13, 0.472, 27, 80.41, 61.04, 0.528, 2, 2, 112.18, 69.07, 0.232, 27, 30.14, 68.98, 0.768, 2, 2, 80.73, 68.55, 0.2, 27, -1.31, 68.46, 0.8, 2, 2, 36.22, 125.72, 0.728, 27, -45.82, 125.63, 0.272, 2, 2, 79.59, 126.03, 0.808, 27, -2.45, 125.94, 0.192, 2, 2, 144.44, 114.58, 0.808, 27, 62.39, 114.48, 0.192, 2, 2, 78.23, -71.26, 0.248, 27, -3.81, -71.36, 0.752, 2, 2, 113.45, -72.43, 0.264, 27, 31.41, -72.53, 0.736, 2, 2, 161.22, -68.95, 0.472, 27, 79.17, -69.04, 0.528, 2, 2, 219.44, -56.44, 0.808, 27, 137.4, -56.53, 0.192, 1, 27, -61.66, -2.02, 1, 2, 2, 134.8, -0.17, 0.04, 27, 52.76, -0.27, 0.96, 2, 2, 187.91, -0.58, 0.392, 27, 105.87, -0.67, 0.608, 2, 2, 226.94, -0.74, 0.808, 27, 144.9, -0.83, 0.192, 1, 27, -37.8, -0.73, 1, 2, 2, 34.44, 64.02, 0.376, 27, -47.61, 63.93, 0.624, 2, 2, 30.98, -68.35, 0.136, 27, -51.07, -68.44, 0.864, 1, 27, 16.39, -0.5, 1], "hull": 27, "edges": [38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 16, 18, 18, 20, 32, 34, 32, 30, 48, 54, 54, 56, 56, 58, 58, 60, 26, 28, 28, 30, 28, 62, 62, 64, 64, 66, 34, 36, 14, 16, 20, 22, 68, 70, 70, 72, 72, 74, 74, 0, 36, 38, 12, 14, 22, 24, 24, 26, 24, 76, 78, 80, 80, 82, 76, 84, 26, 86, 86, 60, 22, 88, 88, 68, 78, 90, 90, 84], "width": 361, "height": 291}}, "images/Drips": {"Drips": {"name": "images/Drips", "type": "mesh", "uvs": [1, 0.1099, 0.99149, 0.47829, 0.98808, 0.61203, 1, 0.79051, 1, 0.88399, 0.90036, 0.8709, 0.9074, 0.76218, 0.9151, 0.60355, 0.91216, 0.4596, 0.88562, 0.36799, 0.83844, 0.34929, 0.79421, 0.43716, 0.79274, 0.54747, 0.79716, 0.62599, 0.71018, 0.63347, 0.70769, 0.54718, 0.69986, 0.45025, 0.67332, 0.39977, 0.64826, 0.43342, 0.62909, 0.57364, 0.64679, 0.70638, 0.64089, 0.77929, 0.59076, 0.80173, 0.54211, 0.73629, 0.55652, 0.56371, 0.54948, 0.42968, 0.50525, 0.36051, 0.45172, 0.42105, 0.44242, 0.60742, 0.45251, 0.77455, 0.48121, 0.91706, 0.48756, 1, 0.34456, 1, 0.35716, 0.91519, 0.37168, 0.76276, 0.37201, 0.61011, 0.35058, 0.42229, 0.31065, 0.35111, 0.29181, 0.42748, 0.30475, 0.60537, 0.31212, 0.72877, 0.20519, 0.73437, 0.20666, 0.59574, 0.2214, 0.42906, 0.17649, 0.26511, 0.12194, 0.25046, 0.09402, 0.40566, 0.09348, 0.5572, 0.10977, 0.71712, 0.1239, 0.85594, 0, 0.86086, 0, 0.72508, 0.01173, 0.55255, 0.01127, 0.4213, 0.0112, 0.40055, 0, 0.2081, 0.0343, 0.08471, 0.18173, 0, 0.87144, 0, 1, 0], "triangles": [29, 30, 33, 5, 3, 4, 50, 48, 49, 32, 33, 31, 31, 33, 30, 33, 34, 29, 29, 34, 28, 34, 35, 28, 5, 6, 3, 50, 51, 48, 6, 2, 3, 20, 22, 23, 6, 7, 2, 20, 24, 19, 20, 23, 24, 41, 39, 40, 41, 42, 39, 39, 42, 38, 42, 43, 38, 51, 47, 48, 51, 52, 47, 14, 12, 13, 14, 15, 12, 2, 7, 1, 27, 28, 35, 27, 35, 36, 7, 8, 1, 19, 24, 18, 24, 25, 18, 46, 47, 53, 47, 52, 53, 11, 12, 16, 12, 15, 16, 0, 1, 9, 1, 8, 9, 16, 17, 11, 11, 17, 10, 18, 25, 17, 25, 26, 17, 38, 43, 37, 37, 43, 44, 36, 37, 27, 53, 54, 46, 27, 37, 26, 46, 54, 45, 54, 55, 45, 10, 17, 58, 9, 10, 0, 58, 17, 26, 26, 57, 58, 37, 44, 57, 10, 58, 0, 57, 26, 37, 44, 45, 57, 55, 56, 45, 45, 56, 57, 58, 59, 0, 21, 22, 20], "vertices": [1, 1, 105.49, 30.28, 1, 2, 1, 103.56, -26.96, 0.76, 28, 103.68, 78.74, 0.24, 2, 1, 102.79, -47.74, 0.76, 28, 102.91, 57.96, 0.24, 2, 1, 105.49, -75.47, 0.6, 28, 105.62, 30.23, 0.4, 2, 1, 105.49, -90, 0.424, 28, 105.62, 15.7, 0.576, 2, 1, 82.86, -87.96, 0.424, 28, 82.99, 17.73, 0.576, 2, 1, 84.46, -71.07, 0.6, 28, 84.58, 34.63, 0.4, 2, 1, 86.21, -46.42, 0.76, 28, 86.33, 59.28, 0.24, 2, 1, 85.54, -24.05, 0.76, 28, 85.66, 81.64, 0.24, 2, 1, 79.51, -9.82, 0.888, 28, 79.64, 95.88, 0.112, 2, 1, 68.8, -6.91, 0.888, 28, 68.92, 98.78, 0.112, 2, 1, 58.75, -20.57, 0.888, 28, 58.87, 85.13, 0.112, 2, 1, 58.41, -37.71, 0.76, 28, 58.54, 67.99, 0.24, 2, 1, 59.42, -49.91, 0.76, 28, 59.54, 55.79, 0.24, 2, 1, 39.66, -51.07, 0.76, 28, 39.79, 54.63, 0.24, 2, 1, 39.1, -37.66, 0.76, 28, 39.22, 68.04, 0.24, 2, 1, 37.32, -22.6, 0.888, 28, 37.44, 83.1, 0.112, 2, 1, 31.29, -14.76, 0.888, 28, 31.41, 90.94, 0.112, 2, 1, 25.6, -19.99, 0.888, 28, 25.72, 85.71, 0.112, 2, 1, 21.24, -41.77, 0.76, 28, 21.37, 63.92, 0.24, 2, 1, 25.26, -62.4, 0.6, 28, 25.39, 43.3, 0.4, 2, 1, 23.92, -73.73, 0.472, 28, 24.05, 31.97, 0.528, 2, 1, 12.54, -77.21, 0.472, 28, 12.66, 28.48, 0.528, 2, 1, 1.48, -67.05, 0.6, 28, 1.61, 38.65, 0.4, 2, 1, 4.76, -40.23, 0.76, 28, 4.88, 65.47, 0.24, 2, 1, 3.16, -19.41, 0.888, 28, 3.28, 86.29, 0.112, 2, 1, -6.89, -8.66, 0.888, 28, -6.76, 97.04, 0.112, 2, 1, -19.05, -18.06, 0.888, 28, -18.92, 87.63, 0.112, 2, 1, -21.16, -47.02, 0.76, 28, -21.04, 58.67, 0.24, 2, 1, -18.87, -72.99, 0.6, 28, -18.74, 32.7, 0.4, 2, 1, -12.35, -95.14, 0.424, 28, -12.23, 10.56, 0.576, 2, 1, -10.91, -108.02, 0.328, 28, -10.78, -2.33, 0.672, 2, 1, -43.39, -108.02, 0.328, 28, -43.27, -2.33, 0.672, 2, 1, -40.53, -94.85, 0.424, 28, -40.4, 10.85, 0.576, 2, 1, -37.23, -71.16, 0.6, 28, -37.1, 34.54, 0.4, 2, 1, -37.15, -47.44, 0.76, 28, -37.03, 58.26, 0.24, 2, 1, -42.02, -18.26, 0.888, 28, -41.9, 87.44, 0.112, 2, 1, -51.09, -7.2, 0.888, 28, -50.97, 98.5, 0.112, 2, 1, -55.37, -19.06, 0.888, 28, -55.25, 86.63, 0.112, 2, 1, -52.43, -46.71, 0.76, 28, -52.31, 58.99, 0.24, 2, 1, -50.76, -65.88, 0.6, 28, -50.63, 39.82, 0.4, 2, 1, -75.05, -66.75, 0.6, 28, -74.92, 38.95, 0.4, 2, 1, -74.71, -45.21, 0.76, 28, -74.59, 60.49, 0.24, 2, 1, -71.36, -19.31, 0.888, 28, -71.24, 86.39, 0.112, 1, 1, -81.57, 6.17, 1, 1, 1, -93.96, 8.44, 1, 2, 1, -100.3, -15.67, 0.888, 28, -100.18, 90.02, 0.112, 2, 1, -100.42, -39.22, 0.76, 28, -100.3, 66.48, 0.24, 2, 1, -96.72, -64.07, 0.6, 28, -96.6, 41.63, 0.4, 2, 1, -93.51, -85.64, 0.424, 28, -93.39, 20.06, 0.576, 2, 1, -121.66, -86.4, 0.424, 28, -121.53, 19.29, 0.576, 2, 1, -121.66, -65.3, 0.6, 28, -121.53, 40.39, 0.4, 2, 1, -118.99, -38.5, 0.76, 28, -118.87, 67.2, 0.24, 2, 1, -119.1, -18.1, 0.888, 28, -118.97, 87.59, 0.112, 2, 1, -119.11, -14.88, 0.888, 28, -118.99, 90.82, 0.112, 1, 1, -121.66, 15.03, 1, 1, 1, -113.87, 34.2, 1, 1, 1, -80.38, 47.36, 1, 1, 1, 76.29, 47.36, 1, 1, 1, 105.49, 47.36, 1], "hull": 60, "edges": [100, 98, 94, 92, 92, 90, 90, 88, 88, 86, 86, 84, 84, 82, 82, 80, 80, 78, 78, 76, 76, 74, 74, 72, 72, 70, 70, 68, 68, 66, 66, 64, 62, 64, 62, 60, 60, 58, 58, 56, 56, 54, 54, 52, 52, 50, 50, 48, 48, 46, 46, 44, 36, 38, 38, 40, 40, 42, 36, 34, 34, 32, 32, 30, 30, 28, 28, 26, 26, 24, 24, 22, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 100, 102, 98, 96, 96, 94, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 2, 0, 0, 118, 116, 0, 44, 42], "width": 175, "height": 138}}, "images/Eye": {"Eye": {"name": "images/Eye", "x": 0.5, "width": 67, "height": 52}}, "images/Eye2": {"Eye": {"name": "images/Eye", "x": 0.5, "width": 67, "height": 52}}, "images/Eye3": {"Eye": {"name": "images/Scuttle_Eye_Big", "x": 0.5, "width": 86, "height": 63}}, "Mouth": {"Mouth Open": {"name": "images/ScuttleBossMouth", "type": "mesh", "uvs": [0.66999, 0.12061, 0.81384, 0.17159, 0.79763, 0.2654, 0.92729, 0.33677, 0.89285, 0.44893, 1, 0.50806, 1, 0.64877, 1, 0.71402, 0.93945, 0.71606, 0.93945, 0.87512, 0.86448, 0.869, 0.84625, 0.74461, 0.76521, 0.73442, 0.75913, 0.89755, 0.69025, 0.90163, 0.66594, 0.76908, 0.61732, 0.68955, 0.55856, 0.73645, 0.55046, 1, 0.44511, 1, 0.42485, 0.79559, 0.34989, 0.75277, 0.34178, 0.83026, 0.26682, 0.84249, 0.24049, 0.7181, 0.2182, 0.93426, 0.11082, 0.9363, 0.09057, 0.69771, 0.06018, 0.78947, 0, 0.79151, 0, 0.56718, 0.06828, 0.48563, 0, 0.35512, 0.15337, 0.33473, 0.15134, 0.20626, 0.26682, 0.17771, 0.22833, 0, 0.29519, 0, 0.38636, 0.07575, 0.42282, 0, 0.51804, 0.07371, 0.59706, 0, 0.68215, 0, 0.13311, 0.42242, 0.39041, 0.48155, 0.41472, 0.42853, 0.4836, 0.36736, 0.43498, 0.38163, 0.55046, 0.37348, 0.56262, 0.40814, 0.77534, 0.41222, 0.61124, 0.45504, 0.61326, 0.51418, 0.75103, 0.57536, 0.29721, 0.55496, 0.33368, 0.51214, 0.26194, 0.57933], "triangles": [55, 43, 44, 45, 44, 33, 44, 43, 33, 43, 32, 33, 25, 26, 24, 27, 24, 26, 22, 23, 21, 21, 23, 24, 28, 29, 27, 27, 29, 30, 24, 56, 21, 56, 54, 21, 54, 55, 21, 21, 55, 44, 24, 27, 56, 30, 31, 27, 27, 31, 56, 31, 43, 56, 54, 56, 55, 55, 56, 43, 31, 32, 43, 33, 34, 35, 35, 45, 33, 2, 0, 1, 35, 37, 38, 35, 36, 37, 47, 35, 38, 47, 45, 35, 52, 17, 44, 52, 44, 45, 45, 49, 52, 49, 47, 46, 52, 49, 51, 47, 49, 45, 48, 49, 46, 51, 49, 50, 2, 50, 48, 50, 49, 48, 38, 46, 47, 2, 48, 0, 0, 48, 40, 48, 46, 40, 40, 46, 39, 40, 41, 0, 0, 41, 42, 46, 38, 39, 14, 15, 13, 13, 15, 12, 15, 16, 12, 17, 52, 16, 16, 53, 12, 16, 52, 53, 17, 18, 20, 18, 19, 20, 20, 21, 17, 17, 21, 44, 11, 12, 53, 53, 52, 50, 50, 2, 3, 9, 11, 8, 9, 10, 11, 8, 11, 53, 7, 8, 6, 8, 53, 6, 53, 4, 6, 4, 5, 6, 53, 50, 4, 4, 50, 3, 52, 51, 50], "vertices": [2, 30, 65.02, 3.47, 0.27133, 27, 14.48, -23.25, 0.72867, 2, 30, 58.03, -14.73, 0.50756, 27, 7.49, -41.45, 0.49244, 2, 30, 45.81, -12.45, 0.66489, 27, -4.73, -39.17, 0.33511, 2, 30, 36.19, -28.8, 0.674, 27, -14.35, -55.51, 0.326, 2, 30, 21.61, -24.15, 0.72, 27, -28.93, -50.87, 0.28, 2, 30, 13.64, -37.66, 0.72, 27, -36.9, -64.38, 0.28, 2, 30, -4.74, -37.33, 0.68933, 27, -55.29, -64.05, 0.31067, 2, 30, -13.27, -37.18, 0.72, 27, -63.81, -63.9, 0.28, 2, 30, -13.4, -29.46, 0.72, 27, -63.94, -56.18, 0.28, 2, 30, -34.19, -29.09, 0.72, 27, -84.73, -55.81, 0.28, 2, 30, -33.22, -19.56, 0.67911, 27, -83.76, -46.27, 0.32089, 2, 30, -16.92, -17.52, 0.59733, 27, -67.46, -44.24, 0.40267, 2, 30, -15.4, -7.23, 0.47467, 27, -65.94, -33.94, 0.52533, 2, 30, -36.71, -6.07, 0.39289, 27, -87.25, -32.79, 0.60711, 2, 30, -37.08, 2.71, 0.352, 27, -87.62, -24.01, 0.648, 2, 30, -19.71, 5.5, 0.33244, 27, -70.25, -21.22, 0.66756, 3, 30, -9.2, 11.51, 0.28568, 27, -59.75, -15.21, 0.68587, 29, -9.57, -44.15, 0.02844, 3, 30, -15.2, 19.1, 0.19981, 27, -65.74, -7.62, 0.72197, 29, -15.57, -36.56, 0.07822, 3, 30, -49.62, 20.75, 0.11393, 27, -100.16, -5.97, 0.72962, 29, -49.99, -34.91, 0.15644, 3, 30, -49.38, 34.16, 0.04336, 27, -99.92, 7.45, 0.71664, 29, -49.75, -21.49, 0.24, 3, 30, -22.62, 36.27, 0.0119, 27, -73.16, 9.55, 0.64498, 29, -22.99, -19.39, 0.34311, 2, 27, -67.4, 19, 0.52, 29, -17.23, -9.94, 0.48, 2, 27, -77.51, 20.21, 0.38844, 29, -27.33, -8.73, 0.61156, 2, 27, -78.93, 29.79, 0.26933, 29, -28.76, 0.85, 0.73067, 2, 27, -62.62, 32.85, 0.21244, 29, -12.45, 3.91, 0.78756, 2, 27, -90.81, 36.19, 0.184, 29, -40.64, 7.25, 0.816, 2, 27, -90.84, 49.87, 0.184, 29, -40.66, 20.93, 0.816, 2, 27, -59.61, 51.9, 0.184, 29, -9.44, 22.96, 0.816, 2, 27, -71.54, 55.98, 0.184, 29, -21.36, 27.04, 0.816, 2, 27, -71.66, 63.65, 0.184, 29, -21.49, 34.71, 0.816, 2, 27, -42.35, 63.13, 0.184, 29, 7.82, 34.19, 0.816, 2, 27, -31.85, 54.24, 0.184, 29, 18.32, 25.3, 0.816, 2, 27, -14.64, 62.63, 0.2, 29, 35.53, 33.69, 0.8, 2, 27, -12.32, 43.05, 0.24444, 29, 37.85, 14.11, 0.75556, 2, 27, 4.47, 43.01, 0.36578, 29, 54.64, 14.07, 0.63422, 2, 27, 7.94, 28.23, 0.592, 29, 58.11, -0.7, 0.408, 2, 27, 31.25, 32.72, 0.73022, 29, 81.42, 3.78, 0.26978, 2, 27, 31.09, 24.21, 0.82755, 29, 81.27, -4.73, 0.17245, 2, 27, 20.99, 12.77, 0.86666, 29, 71.16, -16.17, 0.13334, 2, 27, 30.8, 7.95, 0.91333, 29, 80.98, -20.99, 0.08667, 2, 27, 20.96, -4, 0.95555, 29, 71.13, -32.94, 0.04445, 3, 30, 80.95, 12.48, 0.02489, 27, 30.41, -14.24, 0.95733, 29, 80.58, -43.18, 0.01778, 2, 30, 80.76, 1.64, 0.10756, 27, 30.21, -25.08, 0.89244, 2, 27, -23.74, 45.84, 0.24266, 29, 26.44, 16.9, 0.75734, 2, 27, -32.05, 13.2, 0.41822, 29, 18.12, -15.74, 0.58178, 2, 27, -25.18, 9.98, 0.592, 29, 25, -18.96, 0.408, 2, 27, -17.34, 1.07, 0.89333, 29, 32.83, -27.87, 0.10667, 2, 27, -19.09, 7.29, 0.804, 29, 31.08, -21.65, 0.196, 2, 30, 32.25, 19.28, 0.27644, 27, -18.29, -7.43, 0.72356, 2, 30, 27.69, 17.82, 0.37244, 27, -22.85, -8.9, 0.62756, 2, 30, 26.68, -9.27, 0.526, 27, -23.86, -35.99, 0.474, 2, 30, 21.45, 11.73, 0.49511, 27, -29.09, -14.98, 0.50489, 2, 30, 13.72, 11.61, 0.51556, 27, -36.82, -15.1, 0.48444, 2, 30, 5.41, -5.79, 0.59733, 27, -45.13, -32.51, 0.40267, 2, 27, -41.43, 25.24, 0.184, 29, 8.74, -3.69, 0.816, 2, 27, -35.92, 20.5, 0.20533, 29, 14.25, -8.44, 0.79467, 2, 27, -44.53, 29.79, 0.184, 29, 5.64, 0.85, 0.816], "hull": 43, "edges": [60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 58, 60, 58, 56, 56, 54, 54, 52, 52, 50, 50, 48, 48, 46, 46, 44, 44, 42, 42, 40, 40, 38, 36, 38, 36, 34, 34, 32, 32, 30, 30, 28, 28, 26, 26, 24, 24, 22, 22, 20, 20, 18, 18, 16, 16, 14, 64, 86, 86, 88, 70, 90, 90, 88, 78, 92, 92, 94, 94, 90, 0, 96, 96, 98, 98, 100, 100, 6, 100, 102, 102, 104, 104, 106, 10, 12, 12, 14, 106, 12, 86, 110, 54, 112, 112, 108, 110, 112], "width": 154, "height": 153}}, "MouthBack": {"Mouth Back": {"name": "images/ScuttleBossMouthBack", "type": "mesh", "uvs": [0.66999, 0.12061, 0.69658, 0.17593, 0.77349, 0.27407, 0.81003, 0.3758, 0.83767, 0.43592, 0.93102, 0.43, 0.90688, 0.64877, 1, 0.71402, 1, 0.77244, 0.97738, 0.92282, 0.89552, 0.87768, 0.86694, 0.84002, 0.83074, 0.8862, 0.77638, 0.94092, 0.7006, 0.91464, 0.67284, 0.86015, 0.61387, 0.85001, 0.55856, 0.90558, 0.56081, 0.96097, 0.49684, 0.92194, 0.45934, 0.83028, 0.39817, 0.83083, 0.34178, 0.95168, 0.26682, 0.84249, 0.21979, 0.88289, 0.16302, 0.91691, 0.08323, 0.97966, 0.04573, 0.90153, 0.00345, 0.88054, 0, 0.79151, 0.03104, 0.62356, 0, 0.49864, 0.17244, 0.52425, 0.18441, 0.37376, 0.13755, 0.27131, 0.29786, 0.22541, 0.36283, 0.19081, 0.39175, 0.14311, 0.38636, 0.07575, 0.42282, 0, 0.51804, 0.07371, 0.59016, 0.09107, 0.68215, 0], "triangles": [22, 23, 21, 21, 32, 20, 32, 33, 35, 33, 34, 35, 23, 32, 21, 26, 27, 25, 24, 25, 29, 29, 25, 27, 30, 23, 24, 29, 27, 28, 24, 29, 30, 30, 32, 23, 30, 31, 32, 19, 17, 18, 13, 14, 12, 19, 20, 17, 14, 15, 12, 17, 20, 16, 12, 15, 11, 6, 15, 16, 36, 20, 32, 32, 35, 36, 3, 20, 36, 16, 20, 3, 0, 1, 41, 41, 1, 37, 41, 37, 40, 1, 36, 37, 37, 38, 40, 0, 41, 42, 38, 39, 40, 9, 10, 8, 10, 11, 8, 11, 6, 8, 3, 1, 2, 6, 7, 8, 6, 4, 5, 4, 6, 16, 4, 16, 3, 1, 3, 36, 11, 15, 6], "vertices": [2, 30, 58.45, 3.59, 0.224, 27, 7.91, -23.13, 0.776, 2, 30, 52.07, -4.99, 0.72, 27, 1.53, -31.71, 0.28, 2, 30, 45.81, -12.45, 0.72, 27, -4.73, -39.17, 0.28, 2, 30, 33.7, -21.31, 0.72, 27, -16.84, -48.02, 0.28, 2, 30, 20.36, -20.62, 0.72, 27, -30.18, -47.34, 0.28, 2, 30, 9.79, -32.33, 0.72, 27, -40.75, -59.05, 0.28, 2, 30, 3.75, -27.84, 0.72, 27, -46.79, -54.56, 0.28, 2, 30, -2.99, -25.53, 0.72, 27, -53.53, -52.25, 0.28, 2, 30, -8.07, -25.17, 0.72, 27, -58.61, -51.89, 0.28, 2, 30, -15.76, -27.67, 0.72, 27, -66.3, -54.38, 0.28, 2, 30, -15.29, -21.63, 0.72, 27, -65.83, -48.35, 0.28, 2, 30, 0.23, -13.89, 0.72, 27, -50.31, -40.61, 0.28, 2, 30, -1.82, -7.03, 0.352, 27, -52.36, -33.75, 0.648, 2, 30, -12.17, -5.63, 0.352, 27, -62.71, -32.35, 0.648, 2, 30, -27.1, -2.72, 0.352, 27, -77.65, -29.44, 0.648, 2, 30, -6.62, 2.2, 0.352, 27, -57.16, -24.52, 0.648, 2, 30, 0.06, 14.85, 0.352, 27, -50.49, -11.87, 0.648, 2, 30, -15.2, 19.1, 0.176, 27, -65.74, -7.62, 0.824, 3, 30, -11.94, 20.95, 0.10713, 27, -62.48, -5.77, 0.63687, 29, -12.31, -34.71, 0.256, 2, 27, -66.76, -0.15, 0.808, 29, -16.59, -29.09, 0.192, 2, 27, -61.8, 8.03, 0.744, 29, -11.63, -20.91, 0.256, 2, 27, -59.51, 18.86, 0.44, 29, -9.34, -10.08, 0.56, 2, 27, -65.64, 22.19, 0.44, 29, -15.47, -6.75, 0.56, 2, 27, -61.04, 25.96, 0.184, 29, -10.87, -2.98, 0.816, 2, 27, -54.74, 32.27, 0.184, 29, -4.57, 3.33, 0.816, 2, 27, -61.84, 39.62, 0.184, 29, -11.67, 10.68, 0.816, 2, 27, -62.89, 44.99, 0.184, 29, -12.72, 16.05, 0.816, 2, 27, -57.91, 49.24, 0.184, 29, -7.74, 20.3, 0.816, 2, 27, -60.11, 57.97, 0.184, 29, -9.94, 29.03, 0.816, 2, 27, -47.64, 59.72, 0.184, 29, 2.53, 30.78, 0.816, 2, 27, -35.19, 47.23, 0.184, 29, 14.99, 18.29, 0.816, 2, 27, -19.66, 50.08, 0.184, 29, 30.51, 21.14, 0.816, 2, 27, -19.42, 40.38, 0.184, 29, 30.75, 11.44, 0.816, 2, 27, -15.03, 38.72, 0.184, 29, 35.14, 9.78, 0.816, 2, 27, -1.4, 33.48, 0.184, 29, 48.78, 4.54, 0.816, 2, 27, 7.94, 28.23, 0.728, 29, 58.11, -0.7, 0.272, 2, 27, 6.11, 23.53, 0.84, 29, 56.28, -5.41, 0.16, 2, 27, 16.53, 18.33, 0.84, 29, 66.71, -10.6, 0.16, 2, 27, 15.69, 10.24, 0.84, 29, 65.86, -18.7, 0.16, 2, 27, 19.81, 5.52, 0.84, 29, 69.98, -23.42, 0.16, 1, 27, 16.54, -5.68, 1, 1, 27, 18.54, -16.66, 1, 1, 27, 17.61, -19.6, 1], "hull": 43, "edges": [60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 58, 60, 58, 56, 56, 54, 54, 52, 52, 50, 50, 48, 48, 46, 46, 44, 44, 42, 42, 40, 40, 38, 36, 38, 36, 34, 34, 32, 32, 30, 30, 28, 28, 26, 26, 24, 24, 22, 22, 20, 20, 18, 18, 16, 16, 14, 10, 12, 12, 14], "width": 127, "height": 101}, "Mouth Back Shut": {"name": "images/ScuttleBossMouth_Closed", "x": -29.52, "y": 5.51, "rotation": -91.02, "width": 52, "height": 119}}, "ScuttleEye1": {"Scuttle_Eye_Back": {"name": "images/Scuttle_Eye_Back", "width": 122, "height": 90}, "Scuttle_Eye_Closed": {"name": "images/Scuttle_Eye_Closed", "x": 0.5, "y": 0.5, "width": 77, "height": 63}}, "ScuttleEye2": {"Scuttle_Eye_Back": {"name": "images/Scuttle_Eye_Back", "width": 122, "height": 90}, "Scuttle_Eye_Closed": {"name": "images/Scuttle_Eye_Closed", "x": 0.5, "y": 0.5, "width": 77, "height": 63}}, "ScuttleEye3": {"Scuttle_Eye_Back": {"name": "images/Scuttle_Eye_Back_Big", "width": 146, "height": 109}, "Scuttle_Eye_Closed": {"name": "images/Scuttle_Eye_Closed", "x": 0.5, "y": 0.5, "width": 77, "height": 63}}, "Skirt": {"Skirt": {"name": "images/ScuttleBossSkirt", "type": "mesh", "uvs": [0.99906, 0.40831, 1, 0.70402, 0.94119, 0.81182, 0.91696, 0.71722, 0.91122, 0.93282, 0.84745, 0.95482, 0.83087, 0.84482, 0.81748, 0.95922, 0.74159, 0.94821, 0.71983, 0.79842, 0.70389, 0.93702, 0.62992, 0.93262, 0.61015, 0.84022, 0.5923, 1, 0.54447, 1, 0.51672, 0.86482, 0.49696, 1, 0.4249, 1, 0.40189, 0.81411, 0.38915, 0.9505, 0.31008, 0.9483, 0.28585, 0.78109, 0.24631, 0.9659, 0.18916, 0.92152, 0.17768, 0.74992, 0.14452, 0.90392, 0.09095, 0.87752, 0.07055, 0.64872, 0.03357, 0.77192, 0, 0.62915, 0, 0.3091, 0.03095, 0, 0.11148, 0, 0.20568, 0, 0.29611, 0, 0.40916, 0.013, 0.50486, 0, 0.59906, 0, 0.71493, 0, 0.82326, 0, 0.89015, 0, 0.97008, 0, 0.18599, 0.45066, 0.08225, 0.41526, 0.28906, 0.48105, 0.40659, 0.4723, 0.51278, 0.50804, 0.60512, 0.50786, 0.71875, 0.49496, 0.82542, 0.48496, 0.90774, 0.47212], "triangles": [5, 6, 4, 2, 3, 1, 4, 6, 3, 3, 50, 1, 3, 6, 49, 25, 26, 24, 42, 27, 43, 11, 12, 9, 16, 17, 15, 17, 18, 15, 42, 32, 33, 13, 14, 12, 18, 46, 15, 21, 22, 24, 20, 21, 18, 22, 23, 24, 43, 32, 42, 14, 15, 12, 19, 20, 18, 43, 31, 32, 24, 27, 42, 42, 33, 44, 30, 31, 43, 46, 45, 36, 47, 48, 9, 46, 37, 47, 47, 38, 48, 47, 37, 38, 48, 38, 39, 44, 34, 45, 45, 34, 35, 50, 40, 41, 7, 8, 6, 45, 35, 36, 46, 36, 37, 48, 39, 49, 49, 39, 40, 50, 41, 0, 3, 49, 50, 50, 0, 1, 15, 47, 12, 21, 44, 18, 44, 24, 42, 46, 18, 45, 8, 9, 6, 10, 11, 9, 15, 46, 47, 12, 47, 9, 18, 44, 45, 9, 48, 49, 21, 24, 44, 44, 33, 34, 9, 49, 6, 49, 40, 50, 26, 27, 24, 27, 29, 43, 28, 29, 27, 29, 30, 43], "vertices": [3, 2, 2.49, -138.18, 0.5107, 21, -8.11, -58.38, 0.24998, 14, -35.55, 139.36, 0.23932, 3, 2, -22.98, -137.73, 0.32903, 21, 17.36, -58.84, 0.44697, 14, -11.9, 140.49, 0.224, 3, 2, -31.32, -121.34, 0.32903, 21, 26.27, -42.76, 0.44697, 14, -2.9, 124.66, 0.224, 3, 2, -23.63, -114.79, 0.32903, 21, 18.82, -35.94, 0.44697, 14, -9.88, 117.64, 0.224, 3, 2, -40.85, -112.9, 0.32903, 21, 36.1, -34.66, 0.44697, 14, 6.76, 116.84, 0.224, 3, 2, -42.29, -95.27, 0.37869, 21, 38.17, -17.09, 0.39731, 14, 9.19, 99.3, 0.224, 3, 2, -33.41, -90.85, 0.37869, 21, 29.45, -12.36, 0.39731, 14, 0.98, 94.34, 0.224, 3, 2, -42.5, -86.99, 0.37869, 21, 38.67, -8.83, 0.39731, 14, 9.88, 91.05, 0.224, 4, 2, -41.24, -66.07, 0.4512, 21, 38.16, 12.13, 0.26272, 14, 9.77, 70, 0.20608, 28, 58.88, 39.54, 0.08, 4, 2, -29.15, -60.28, 0.4512, 21, 26.28, 18.34, 0.26272, 14, -1.28, 63.5, 0.20608, 28, 54.83, 48.09, 0.08, 5, 2, -40.16, -55.68, 0.37179, 21, 37.45, 22.55, 0.21648, 14, 8.23, 58.73, 0.20608, 28, 47.55, 42, 0.08, 27, -122.2, -55.77, 0.12565, 5, 2, -39.45, -35.27, 0.49415, 21, 37.46, 42.97, 0.09412, 14, 8.42, 38.21, 0.20608, 28, 26.09, 46.72, 0.08, 27, -121.49, -35.37, 0.12565, 5, 2, -31.96, -29.95, 0.49415, 21, 30.17, 48.55, 0.09412, 14, 1.8, 32.48, 0.20608, 28, 22.49, 52.55, 0.08, 27, -114, -30.04, 0.12565, 5, 2, -44.65, -24.8, 0.52239, 21, 43.03, 53.25, 0.06589, 14, 13.66, 28.01, 0.20608, 28, 14.43, 45.47, 0.08, 27, -126.69, -24.89, 0.12565, 4, 2, -44.41, -11.6, 0.58827, 14, 13.88, 14.76, 0.20608, 28, 0.63, 49.09, 0.08, 27, -126.46, -11.69, 0.12565, 4, 2, -33.46, -4.13, 0.58827, 14, 4.3, 6.71, 0.20608, 28, -3.85, 57.8, 0.08, 27, -115.51, -4.23, 0.12565, 4, 2, -44.18, 1.51, 0.58827, 14, 14.31, 1.65, 0.20608, 28, -11.95, 51.77, 0.08, 27, -126.22, 1.42, 0.12565, 4, 2, -43.82, 21.4, 0.58827, 14, 14.97, -18.22, 0.20608, 28, -31.03, 55.84, 0.08, 27, -125.87, 21.31, 0.12565, 4, 2, -28.84, 27.48, 0.58827, 14, 1.66, -25.1, 0.20608, 28, -33.18, 66.95, 0.08, 27, -110.88, 27.39, 0.12565, 4, 2, -39.69, 31.19, 0.58827, 14, 11.69, -28.23, 0.20608, 28, -39.44, 60.47, 0.08, 27, -121.73, 31.1, 0.12565, 5, 2, -39.12, 53.01, 0.50356, 14, 12.8, -50.47, 0.20608, 28, -62.93, 64.89, 0.08, 27, -121.17, 52.92, 0.12565, 18, 42.09, 33.5, 0.08471, 5, 2, -25.63, 59.46, 0.50356, 14, 0.65, -57.49, 0.20608, 28, -65.08, 74.91, 0.08, 27, -107.67, 59.36, 0.12565, 18, 28.22, 27.9, 0.08471, 4, 2, -40.22, 70.63, 0.61112, 14, 15.6, -67.33, 0.20608, 28, -79.57, 68.01, 0.08, 18, 42.09, 15.85, 0.1028, 4, 2, -36.39, 86.34, 0.54367, 14, 12.86, -83.66, 0.22042, 28, -95.12, 74.84, 0.016, 18, 37.29, 0.41, 0.21991, 4, 2, -22.6, 89.26, 0.54367, 14, 0.05, -86.96, 0.22042, 28, -92.95, 83.86, 0.016, 18, 23.35, -1.66, 0.21991, 4, 2, -34.76, 98.63, 0.54367, 14, 11.64, -95.98, 0.22042, 28, -105.68, 79.01, 0.016, 18, 34.9, -11.76, 0.21991, 3, 2, -32.38, 113.38, 0.3911, 14, 9.52, -111.41, 0.224, 18, 31.62, -26.33, 0.3849, 3, 2, -13.98, 118.68, 0.47802, 14, -7.62, -116.71, 0.224, 18, 12.92, -30.49, 0.29798, 3, 2, -23.65, 129.06, 0.36627, 14, 1.18, -127.23, 0.224, 18, 21.94, -41.45, 0.40973, 3, 2, -12.07, 138.12, 0.3911, 14, -9.7, -136.3, 0.224, 18, 9.81, -49.77, 0.3849, 3, 2, 13.53, 137.67, 0.50285, 14, -33.04, -136.22, 0.224, 18, -15.71, -47.73, 0.27315, 3, 2, 38.1, 128.68, 0.66426, 14, -54.45, -128.49, 0.224, 18, -39.68, -37.24, 0.11174, 3, 2, 64.08, 109.42, 0.66426, 14, -81.07, -110.66, 0.224, 18, -64.41, -16.41, 0.11174, 3, 2, 63.61, 83.43, 0.66426, 14, -81.59, -84.68, 0.224, 18, -62.33, 9.51, 0.11174, 4, 2, 63.17, 58.47, 0.39855, 14, -84.3, -61.73, 0.224, 27, -18.88, 58.38, 0.3104, 18, -60.34, 34.39, 0.06705, 3, 2, 62.55, 24.16, 0.4656, 14, -85.21, -27.72, 0.224, 27, -19.49, 24.06, 0.3104, 3, 2, 62.14, 0.87, 0.4656, 14, -85.97, -4.45, 0.224, 27, -19.9, 0.77, 0.3104, 3, 2, 61.67, -25.13, 0.4656, 14, -86.83, 21.54, 0.224, 27, -20.37, -25.22, 0.3104, 3, 2, 61.1, -57.1, 0.4656, 14, -87.88, 53.5, 0.224, 27, -20.94, -57.2, 0.3104, 3, 2, 60.57, -87, 0.56405, 21, -64.33, -5.16, 0.1129, 14, -89.54, 84.88, 0.32305, 3, 2, 60.24, -105.46, 0.53353, 21, -64.66, -23.62, 0.1609, 14, -90.33, 103.32, 0.30557, 3, 2, 33.48, -130.48, 0.55388, 21, -38.81, -49.58, 0.1289, 14, -65.09, 129.74, 0.31723, 3, 2, 7.24, 84.88, 0.61459, 14, -27.76, -83.44, 0.224, 18, -6.16, 4.57, 0.16141, 3, 2, 10.32, 112.94, 0.49043, 14, -30.55, -111.44, 0.224, 18, -10.97, -23.25, 0.28557, 4, 2, 5.36, 57.5, 0.33479, 14, -29.81, -58.33, 0.224, 27, -76.68, 57.4, 0.3849, 18, -2.59, 31.78, 0.05632, 3, 2, 0.8, 24.62, 0.3911, 14, -27.29, -25.29, 0.224, 27, -81.25, 24.52, 0.3849, 3, 2, 0.56, -3.13, 0.3911, 14, -28.44, 2.44, 0.224, 27, -81.48, -3.23, 0.3849, 4, 2, -1.45, -29.11, 0.32853, 21, -0.29, 50.48, 0.06258, 14, -28.02, 28.48, 0.224, 27, -83.49, -29.2, 0.3849, 4, 2, -2.79, -60.19, 0.38366, 21, -0.06, 19.37, 0.08195, 14, -27.86, 60.02, 0.224, 27, -84.83, -60.28, 0.3104, 3, 2, -1.75, -90.44, 0.59507, 21, -2.17, -10.82, 0.15232, 14, -28.64, 91.96, 0.25261, 3, 2, 0.19, -112.15, 0.54339, 21, -4.89, -32.45, 0.20198, 14, -31.89, 113.52, 0.25463], "hull": 42, "edges": [58, 56, 56, 54, 54, 52, 52, 50, 50, 48, 48, 46, 46, 44, 44, 42, 42, 40, 40, 38, 38, 36, 36, 34, 32, 34, 32, 30, 30, 28, 26, 28, 26, 24, 24, 22, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 78, 80, 76, 78, 74, 76, 72, 74, 64, 66, 66, 68, 68, 70, 70, 72, 48, 84, 84, 66, 54, 86, 86, 64, 58, 60, 62, 64, 60, 62, 42, 88, 88, 68, 36, 90, 90, 70, 30, 92, 92, 72, 74, 94, 94, 24, 76, 96, 96, 18, 78, 98, 98, 12, 6, 100, 100, 80, 2, 0, 80, 82, 82, 0], "width": 276, "height": 80}}, "Spikes": {"Spikes": {"name": "images/ScuttleBossHorns", "type": "mesh", "uvs": [0.75419, 0.24262, 0.69308, 0.33704, 0.63463, 0.57382, 0.63939, 0.67815, 0.68837, 0.66695, 0.76911, 0.48104, 0.77043, 0.36906, 0.83397, 0.3601, 0.80485, 0.05773, 1, 0.10476, 1, 0.45193, 0.97296, 0.48776, 0.83, 0.55048, 0.73734, 0.82149, 0.6513, 0.82821, 0.66322, 0.87749, 0.73999, 0.86181, 0.81809, 0.84837, 0.81288, 0.65001, 0.89879, 0.58557, 1, 0.53311, 1, 0.86431, 0.97585, 0.88529, 0.88994, 0.92276, 0.85362, 0.96922, 0.74292, 1, 0.66675, 1, 0.3298, 1, 0.27223, 1, 0.21024, 0.96105, 0.13761, 1, 0.02425, 0.83517, 0.01805, 0.5804, 0.04816, 0.54293, 0.19164, 0.66582, 0.21112, 0.83517, 0.305, 0.85915, 0.3546, 0.93857, 0.36877, 0.81119, 0.27489, 0.8052, 0.19518, 0.57441, 0.0703, 0.49348, 0, 0.49648, 0, 0.28966, 0.1447, 0.04389, 0.23061, 0.04689, 0.20758, 0.38408, 0.23238, 0.46351, 0.2678, 0.49348, 0.31829, 0.61787, 0.367, 0.63885, 0.40967, 0.61728, 0.35387, 0.37001, 0.29542, 0.28309, 0.33882, 0.12273, 0.36096, 0.1557, 0.35653, 0.24412, 0.39107, 0.08976, 0.39284, 0, 0.50318, 0, 0.53149, 0, 0.60275, 0, 0.66828, 0, 0.74799, 0, 0.47521, 0.5948, 0.66031, 0.23063, 0.55403, 0.58731, 0.41823, 0.28869, 0.44655, 0.11073, 0.57902, 0.12613, 0.61442, 0.30752, 0.60026, 0.3811, 0.55172, 0.50601, 0.43043, 0.35423, 0.43826, 0.16277, 0.50937, 0.23305, 0.60484, 0.3573], "triangles": [69, 60, 61, 61, 65, 69, 62, 65, 61, 70, 69, 65, 65, 62, 63, 0, 65, 63, 1, 65, 0, 70, 65, 1, 1, 76, 70, 1, 71, 76, 7, 8, 9, 7, 9, 10, 11, 7, 10, 7, 5, 6, 12, 7, 11, 12, 5, 7, 5, 12, 4, 12, 13, 4, 68, 58, 59, 57, 58, 68, 74, 57, 68, 74, 68, 75, 56, 54, 55, 74, 56, 57, 53, 54, 56, 67, 56, 74, 67, 74, 75, 52, 53, 56, 52, 56, 67, 52, 67, 73, 73, 67, 75, 17, 18, 19, 19, 20, 21, 19, 23, 17, 21, 22, 19, 22, 23, 19, 24, 17, 23, 25, 16, 17, 25, 17, 24, 34, 31, 32, 34, 32, 33, 31, 34, 35, 35, 30, 31, 29, 35, 36, 29, 30, 35, 28, 29, 36, 46, 44, 45, 43, 44, 46, 41, 43, 46, 42, 43, 41, 40, 41, 46, 40, 46, 47, 49, 40, 48, 40, 47, 48, 75, 69, 70, 76, 75, 70, 72, 71, 2, 64, 73, 72, 66, 72, 2, 64, 72, 66, 51, 73, 64, 66, 2, 3, 38, 50, 51, 38, 49, 50, 14, 3, 4, 37, 27, 36, 25, 26, 15, 14, 15, 26, 66, 3, 14, 64, 66, 14, 38, 51, 64, 14, 38, 64, 37, 38, 14, 26, 27, 37, 26, 37, 14, 38, 39, 49, 72, 73, 71, 27, 28, 36, 25, 15, 16, 51, 52, 73, 14, 4, 13, 2, 71, 1, 49, 39, 40, 73, 75, 76, 59, 69, 68, 75, 68, 69, 69, 59, 60, 71, 73, 76], "vertices": [2, 4, 177.46, 26.28, 0.392, 3, 252.69, -153.32, 0.608, 2, 4, 127.67, 38.98, 0.392, 3, 218.72, -114.76, 0.608, 1, 3, 121.87, -76.21, 1, 1, 3, 83.54, -78.48, 1, 2, 3, 97.71, -109.67, 0.608, 5, 39.4, 53.34, 0.392, 2, 3, 165.03, -161.02, 0.608, 5, 115.86, 89.71, 0.392, 3, 4, 144.21, -7.62, 0.02341, 3, 206.11, -162.58, 0.608, 5, 136.04, 125.53, 0.36859, 2, 3, 208.69, -202.09, 0.608, 5, 172.35, 109.75, 0.392, 2, 3, 319.97, -185.99, 0.608, 5, 208.94, 216.06, 0.392, 2, 3, 300.55, -306.85, 0.608, 5, 307.53, 143.49, 0.392, 2, 3, 173.16, -304.57, 0.608, 5, 247.22, 31.26, 0.392, 2, 3, 160.31, -287.54, 0.608, 5, 226.2, 27.62, 0.392, 2, 3, 138.88, -198.37, 0.608, 5, 137.11, 49.37, 0.392, 2, 3, 40.46, -139.06, 0.608, 5, 39.34, -11.01, 0.392, 1, 3, 28.34, -84.89, 1, 1, 3, 10.13, -91.97, 1, 2, 3, 25.64, -140.44, 0.608, 6, 19.71, 49.01, 0.392, 2, 3, 29.7, -189.02, 0.608, 6, 68.37, 46.11, 0.392, 2, 3, 102.55, -187.09, 0.608, 6, 76.84, 118.49, 0.392, 2, 3, 125.24, -240.86, 0.608, 6, 133.29, 133.29, 0.392, 2, 3, 143.37, -304.04, 0.608, 6, 198.41, 142.23, 0.392, 2, 3, 21.84, -301.87, 0.608, 6, 178.95, 22.25, 0.392, 2, 3, 14.4, -286.73, 0.608, 6, 162.91, 17.05, 0.392, 2, 3, 1.61, -233.15, 0.608, 6, 108.05, 12.02, 0.392, 2, 3, -15.03, -210.3, 0.608, 6, 83.06, -1.2, 0.392, 2, 3, -25.1, -141.36, 0.608, 6, 13.39, -1.35, 0.392, 1, 3, -34.87, -93.36, 1, 1, 3, -31.13, 115.86, 1, 2, 3, -19.88, 150.89, 0.608, 11, 13.21, 2.1, 0.392, 2, 3, -4.9, 189.13, 0.608, 11, 53.9, -3.5, 0.392, 2, 3, -18.39, 234.48, 0.608, 11, 94.82, 20.24, 0.392, 2, 3, 43.35, 303.79, 0.608, 11, 176.67, -23.54, 0.392, 2, 3, 136.91, 305.97, 0.608, 11, 200.71, -113.98, 0.392, 2, 3, 150.32, 287.03, 0.608, 11, 185.44, -131.46, 0.392, 2, 3, 103.63, 198.74, 0.608, 11, 88.68, -106.76, 0.392, 2, 3, 41.28, 187.76, 0.608, 11, 63.38, -48.72, 0.392, 2, 3, 31.44, 129.62, 0.608, 11, 4.56, -52.77, 0.392, 1, 3, -8.87, 100.06, 1, 1, 3, 37.72, 90.42, 1, 2, 3, 51.57, 147.97, 0.608, 9, 12.45, 91.21, 0.392, 2, 3, 137.14, 195.95, 0.608, 10, 117.68, -38.64, 0.392, 2, 3, 168.22, 272.95, 0.608, 10, 200.16, -28.97, 0.392, 2, 3, 167.9, 316.62, 0.608, 10, 238.32, -7.74, 0.392, 2, 3, 243.79, 315.27, 0.608, 10, 273.54, -74.97, 0.392, 2, 3, 332.37, 223.81, 0.608, 10, 235.8, -196.57, 0.392, 2, 3, 330.32, 170.49, 0.608, 10, 188.03, -220.35, 0.392, 2, 3, 206.84, 187, 0.608, 10, 143.27, -104.09, 0.392, 2, 3, 177.42, 172.12, 0.608, 10, 116.11, -85.41, 0.392, 2, 3, 166.03, 150.32, 0.608, 10, 91.51, -85.88, 0.392, 2, 3, 119.83, 119.79, 0.608, 10, 42.56, -59.98, 0.392, 1, 3, 100.98, 90.39, 1, 1, 3, 108.42, 63.76, 1, 2, 3, 210.38, 96.07, 0.608, 9, 116.43, -39.56, 0.392, 2, 3, 242.93, 131.8, 0.608, 9, 163.27, -27.67, 0.392, 2, 3, 301.28, 103.8, 0.608, 9, 196.57, -83.17, 0.392, 2, 3, 288.94, 90.27, 0.608, 9, 178.81, -87.67, 0.392, 2, 3, 256.55, 93.6, 0.608, 9, 153.6, -67.05, 0.392, 2, 3, 312.8, 71.14, 0.608, 9, 188.19, -116.78, 0.392, 2, 3, 345.72, 69.45, 0.608, 9, 214.74, -136.32, 0.392, 2, 3, 344.5, 0.95, 0.608, 9, 175.99, -192.83, 0.392, 2, 4, 175.97, 190.75, 0.392, 3, 344.18, -16.64, 0.608, 2, 4, 200.26, 153.77, 0.392, 3, 343.39, -60.88, 0.608, 2, 4, 222.6, 119.75, 0.392, 3, 342.67, -101.57, 0.608, 2, 4, 249.78, 78.37, 0.392, 3, 341.78, -151.06, 0.608, 1, 3, 115.94, 22.92, 1, 2, 4, 149.14, 77.42, 0.392, 3, 258.13, -95.11, 0.608, 1, 3, 117.81, -26.08, 1, 2, 3, 239.51, 55.58, 0.608, 9, 118.44, -89.4, 0.392, 2, 3, 304.49, 36.83, 0.608, 9, 162.36, -140.84, 0.392, 2, 4, 153.48, 140.67, 0.392, 3, 297.37, -45.32, 0.608, 2, 4, 109.91, 85.76, 0.392, 3, 230.42, -66.11, 0.608, 2, 4, 82.51, 78.28, 0.392, 3, 203.58, -56.84, 0.608, 1, 3, 147.67, -25.17, 1, 2, 3, 215.32, 48.44, 0.608, 9, 94.33, -82.05, 0.392, 2, 3, 285.49, 42.32, 0.608, 9, 149.52, -125.8, 0.392, 3, 4, 96.94, 155.29, 0.02908, 3, 258.91, -1.37, 0.608, 9, 103.28, -147.63, 0.36292, 2, 4, 91.37, 80.7, 0.392, 3, 212.26, -59.83, 0.608], "hull": 64, "edges": [6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 28, 26, 26, 24, 24, 22, 18, 20, 22, 20, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 30, 52, 6, 28, 50, 52, 50, 48, 48, 46, 46, 44, 40, 42, 44, 42, 100, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 66, 68, 68, 70, 70, 72, 72, 74, 52, 54, 74, 54, 54, 56, 58, 56, 58, 60, 60, 62, 62, 64, 64, 66, 106, 104, 104, 102, 102, 128, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 122, 130, 122, 124, 130, 124, 4, 2, 2, 0, 124, 126, 0, 126, 128, 132, 132, 4, 28, 30, 4, 6, 76, 74, 100, 102, 116, 118, 136, 118, 118, 120, 120, 122, 120, 138, 138, 140, 142, 144, 144, 132, 128, 146, 146, 134, 134, 148, 148, 136, 138, 150, 150, 146, 148, 150, 150, 140, 140, 152, 152, 142, 146, 152], "width": 621, "height": 367}}, "Tentacle1": {"Tentacle5": {"name": "images/ScuttleBossTentacle4", "type": "mesh", "uvs": [1, 0.10807, 1, 0.23422, 1, 0.3513, 1, 0.46837, 1, 0.58662, 1, 0.68306, 1, 0.7912, 1, 0.90395, 1, 1, 0, 1, 0, 0.88679, 0, 0.75991, 0, 0.67143, 0, 0.58314, 0.01075, 0.4739, 0, 0.35413, 1e-05, 0.23927, 0, 0.10763, 0, 0, 1, 0], "triangles": [12, 13, 4, 12, 4, 5, 11, 12, 5, 11, 5, 6, 10, 11, 6, 10, 6, 7, 9, 10, 7, 9, 7, 8, 14, 15, 3, 14, 3, 4, 13, 14, 4, 17, 19, 0, 17, 18, 19, 15, 16, 2, 17, 0, 1, 16, 17, 1, 2, 16, 1, 3, 15, 2], "vertices": [2, 41, 6.62, 38.68, 0.50667, 2, -0.9, 9.77, 0.49333, 3, 41, 35.3, 36.51, 0.66667, 2, -29.66, 10.16, 0.16, 42, -23.9, 37.29, 0.17333, 3, 41, 61.92, 34.49, 0.55826, 42, 2.74, 35.63, 0.35331, 43, -51.78, 34.77, 0.08843, 3, 41, 88.53, 32.47, 0.22794, 42, 29.38, 33.96, 0.53975, 43, -25.12, 33.55, 0.23232, 3, 41, 114.69, 27.42, 0.06794, 42, 55.6, 29.26, 0.44625, 43, 1.18, 29.28, 0.48582, 3, 41, 137.34, 28.77, 0.00301, 42, 78.24, 30.91, 0.33561, 43, 23.78, 31.31, 0.66138, 2, 42, 102.84, 29.37, 0.14917, 43, 48.41, 30.18, 0.85084, 2, 42, 128.5, 27.77, 0.06933, 43, 74.09, 29, 0.93067, 1, 43, 95.97, 28, 1, 1, 43, 92.91, -38.76, 1, 2, 42, 120.43, -38.69, 0.04907, 43, 67.12, -37.58, 0.95093, 2, 42, 91.56, -36.88, 0.13222, 43, 38.23, -36.25, 0.86778, 2, 42, 71.42, -35.63, 0.31377, 43, 18.07, -35.33, 0.68623, 3, 41, 109.57, -36.15, 0.09075, 42, 51.33, -34.37, 0.42889, 43, -2.04, -34.41, 0.48035, 3, 41, 84.79, -33.55, 0.23475, 42, 26.52, -32.1, 0.53507, 43, -26.88, -32.55, 0.23018, 3, 41, 57.51, -32.2, 0.56809, 42, -0.78, -31.12, 0.35353, 43, -54.2, -32.02, 0.07839, 3, 41, 31.4, -30.22, 0.62374, 2, -29.9, 77, 0.18667, 42, -26.92, -29.48, 0.18959, 3, 41, 1.47, -27.95, 0.47974, 2, 0.11, 76.59, 0.52, 42, -56.87, -27.61, 0.00026, 3, 41, -23, -26.1, 0.14641, 2, 24.65, 76.26, 0.85333, 42, -81.37, -26.08, 0.00026, 2, 41, -17.95, 40.54, 0.17333, 2, 23.73, 9.43, 0.82667], "hull": 20, "edges": [30, 28, 28, 26, 26, 24, 24, 22, 22, 20, 20, 18, 16, 18, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 30, 32, 32, 34, 34, 36, 36, 38, 38, 0, 0, 2, 2, 4], "width": 57, "height": 228}}, "Tentacle2": {"Tentacle6": {"name": "images/ScuttleBossTentacle4", "type": "mesh", "uvs": [1, 0.10807, 1, 0.23422, 1, 0.3513, 1, 0.46837, 1, 0.5866, 1, 0.67888, 1, 0.78715, 1, 0.89964, 1, 1, 0, 0.99784, 0, 0.88248, 0, 0.78082, 0, 0.67979, 0, 0.58732, 0.01075, 0.4739, 0, 0.35413, 1e-05, 0.23927, 0, 0.11155, 0, 0.00647, 1, 0], "triangles": [13, 4, 5, 12, 13, 5, 9, 7, 8, 9, 10, 7, 10, 6, 7, 10, 11, 6, 11, 5, 6, 11, 12, 5, 14, 15, 3, 13, 14, 4, 14, 3, 4, 3, 15, 2, 15, 16, 2, 17, 18, 0, 18, 19, 0, 2, 16, 1, 16, 17, 1, 17, 0, 1], "vertices": [2, 44, -2.41, 38.95, 0.504, 2, 1.78, 2.05, 0.496, 3, 44, 26.31, 37.42, 0.61152, 2, -26.98, 1.54, 0.22848, 45, -32.9, 38.08, 0.16, 2, 44, 52.97, 36, 0.45609, 45, -6.23, 37.01, 0.54391, 3, 44, 79.62, 34.57, 0.45597, 45, 20.44, 35.94, 0.54372, 46, -34.09, 35.38, 0.00032, 3, 44, 106.54, 33.14, 0.14478, 45, 47.38, 34.87, 0.39486, 46, -7.14, 34.75, 0.46036, 3, 44, 127.55, 32.02, 0.00059, 45, 68.4, 34.02, 0.25934, 46, 13.9, 34.26, 0.74007, 2, 45, 93.07, 33.04, 0.192, 46, 38.57, 33.68, 0.808, 1, 46, 64.21, 33.08, 1, 1, 46, 87.09, 32.54, 1, 1, 46, 84.91, -39.37, 1, 1, 46, 58.62, -38.76, 1, 2, 45, 88.75, -38.8, 0.16031, 46, 35.45, -38.22, 0.83969, 2, 45, 65.73, -37.88, 0.336, 46, 12.42, -37.68, 0.664, 3, 44, 102.87, -38.72, 0.06814, 45, 44.66, -37.03, 0.44066, 46, -8.66, -37.18, 0.49119, 3, 44, 77.09, -36.57, 0.448, 45, 18.86, -35.23, 0.54317, 46, -34.49, -35.8, 0.00883, 2, 44, 49.78, -35.89, 0.36315, 45, -8.46, -34.91, 0.63685, 3, 44, 23.63, -34.49, 0.57273, 2, -26.86, -70.42, 0.15178, 45, -34.63, -33.86, 0.27549, 3, 44, -5.45, -32.94, 0.56785, 2, 2.26, -69.9, 0.432, 45, -63.72, -32.69, 0.00015, 1, 2, 26.21, -69.48, 1, 1, 2, 26.42, 2.49, 1], "hull": 20, "edges": [30, 28, 28, 26, 26, 24, 24, 22, 22, 20, 20, 18, 16, 18, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 30, 32, 32, 34, 34, 36, 36, 38, 38, 0, 0, 2, 2, 4], "width": 57, "height": 228}}, "Tentacle3": {"Tentacle3": {"name": "images/ScuttleBossTentacle3", "type": "mesh", "uvs": [1, 0.15899, 1, 0.30532, 0.93247, 0.45063, 0.92403, 0.55377, 0.90715, 0.64294, 0.90715, 0.75022, 0.94092, 0.84091, 0.94092, 0.95132, 0.93247, 1, 0.32075, 0.99141, 0.3123, 0.93239, 0.3123, 0.84069, 0.33763, 0.73712, 0.27854, 0.62437, 0, 0.54286, 0, 0.41882, 0, 0.2703, 0, 0.12218, 0, 0, 1, 0], "triangles": [12, 4, 5, 8, 9, 7, 9, 10, 7, 7, 10, 6, 10, 11, 6, 11, 5, 6, 11, 12, 5, 17, 19, 0, 17, 18, 19, 2, 15, 1, 15, 16, 1, 16, 0, 1, 16, 17, 0, 12, 13, 4, 4, 13, 3, 13, 14, 3, 3, 14, 2, 14, 15, 2], "vertices": [3, 16, -56.49, 35.8, 0.09766, 15, 10.48, 35.7, 0.42763, 2, -10.26, 0.57, 0.47472, 3, 16, -22.59, 33.9, 0.28005, 15, 44.38, 33.89, 0.57857, 2, -44.2, 1.19, 0.14138, 4, 16, 10.84, 28.03, 0.59369, 15, 77.83, 28.12, 0.38393, 2, -77.83, 5.8, 0.01171, 17, -56.77, 29.88, 0.01067, 3, 16, 34.71, 26.19, 0.70722, 15, 101.7, 26.35, 0.15094, 17, -32.96, 27.49, 0.14183, 2, 16, 55.31, 24.04, 0.66062, 17, -12.41, 24.86, 0.33938, 2, 16, 80.16, 22.64, 0.39342, 17, 12.4, 22.88, 0.60658, 2, 16, 101.27, 23.45, 0.19125, 17, 33.53, 23.2, 0.80875, 2, 16, 126.85, 22.02, 0.05546, 17, 59.06, 21.17, 0.94454, 1, 17, 70.28, 19.78, 1, 1, 17, 65.44, -16.04, 1, 2, 16, 120.39, -14.77, 0.064, 17, 51.75, -15.45, 0.936, 2, 16, 99.14, -13.57, 0.192, 17, 30.54, -13.77, 0.808, 3, 16, 75.24, -10.74, 0.39449, 15, 142.34, -10.45, 0.0008, 17, 6.71, -10.37, 0.60471, 3, 16, 48.93, -12.75, 0.60332, 15, 116.04, -12.55, 0.0453, 17, -19.65, -11.77, 0.35138, 3, 16, 29.12, -28.1, 0.63297, 15, 96.28, -27.95, 0.22098, 17, -39.8, -26.66, 0.14605, 3, 16, 0.39, -26.49, 0.52649, 15, 67.54, -26.42, 0.45751, 17, -68.49, -24.38, 0.016, 3, 16, -34.01, -24.56, 0.25365, 15, 33.13, -24.59, 0.66635, 2, -34.99, 60.03, 0.08, 3, 16, -68.32, -22.63, 0.096, 15, -1.18, -22.77, 0.49067, 2, -0.63, 59.4, 0.41333, 2, 15, -29.49, -21.26, 0.25333, 2, 27.71, 58.88, 0.74667, 3, 16, -93.31, 37.87, 0.00902, 15, -26.35, 37.66, 0.19464, 2, 26.62, -0.11, 0.79634], "hull": 20, "edges": [30, 32, 32, 34, 36, 38, 34, 36, 30, 28, 28, 26, 26, 24, 24, 22, 22, 20, 20, 18, 16, 18, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 0, 0, 38], "width": 59, "height": 232}}, "Tentacle4": {"Tentacle4": {"name": "images/ScuttleBossTentacle2", "type": "mesh", "uvs": [1, 0.15899, 1, 0.30532, 0.991, 0.41817, 1, 0.54948, 0.991, 0.65153, 0.991, 0.75451, 1, 0.87598, 1, 1, 0.2324, 0.99295, 0.2324, 0.86275, 0.22133, 0.73576, 0.22133, 0.67473, 0.21027, 0.53184, 0.18813, 0.41177, 0.12173, 0.25856, 0, 0.1494, 0, 0, 1, 0], "triangles": [9, 10, 5, 9, 5, 6, 8, 9, 6, 8, 6, 7, 15, 16, 17, 15, 17, 0, 12, 13, 2, 12, 2, 3, 4, 12, 3, 11, 12, 4, 11, 4, 5, 10, 11, 5, 2, 13, 1, 14, 15, 0, 13, 14, 1, 14, 0, 1], "vertices": [3, 24, 18.43, 53.33, 0.43014, 25, -48.49, 53.41, 0.10139, 2, -24.65, 28.88, 0.46846, 3, 24, 49.44, 54.06, 0.56576, 25, -17.47, 54.05, 0.29911, 2, -55.67, 28.98, 0.13513, 2, 24, 73.38, 53.67, 0.4868, 25, 6.46, 53.59, 0.5132, 3, 24, 101.2, 54.98, 0.28634, 25, 34.28, 54.82, 0.63204, 26, -32.71, 56.12, 0.08162, 3, 24, 122.84, 54.46, 0.15072, 25, 55.93, 54.24, 0.59608, 26, -11.09, 55.03, 0.2532, 3, 24, 144.67, 54.81, 0.03768, 25, 77.76, 54.52, 0.45126, 26, 10.74, 54.81, 0.51106, 2, 25, 103.5, 55.72, 0.23723, 26, 36.5, 55.41, 0.76277, 2, 25, 129.79, 56.07, 0.07548, 26, 62.79, 55.14, 0.92452, 2, 25, 129.25, -17.34, 0.064, 26, 60.55, -18.23, 0.936, 2, 25, 101.65, -17.7, 0.22196, 26, 32.95, -17.95, 0.77804, 2, 25, 74.75, -19.11, 0.52236, 26, 6.02, -18.74, 0.47764, 3, 24, 128.94, -19.04, 0.08542, 25, 61.81, -19.28, 0.70627, 26, -6.92, -18.6, 0.20831, 3, 24, 98.67, -20.58, 0.25355, 25, 31.53, -20.74, 0.71351, 26, -37.22, -19.35, 0.03294, 2, 24, 73.25, -23.11, 0.46422, 25, 6.11, -23.19, 0.53578, 3, 24, 40.87, -30.09, 0.54146, 25, -26.29, -30.08, 0.28787, 2, -45.41, -54.98, 0.17067, 3, 24, 17.93, -42.32, 0.37333, 25, -49.27, -42.24, 0.12267, 2, -22.22, -66.75, 0.504, 2, 24, -13.74, -43.06, 0.16267, 2, 9.46, -66.85, 0.83733, 3, 24, -15.27, 52.57, 0.19199, 25, -82.19, 52.75, 0.00621, 2, 9.06, 28.79, 0.8018], "hull": 18, "edges": [26, 28, 28, 30, 32, 34, 30, 32, 26, 24, 24, 22, 22, 20, 20, 18, 18, 16, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 0, 0, 34, 14, 16], "width": 90, "height": 212}}, "Tentacle5": {"Tentacle5": {"name": "images/ScuttleBossTentacle5", "type": "mesh", "uvs": [1, 0.10807, 1, 0.23422, 1, 0.3513, 1, 0.46837, 0.95426, 0.58444, 1, 0.68306, 1, 0.7912, 1, 0.90395, 1, 1, 0, 1, 0, 0.88679, 0, 0.75991, 0, 0.67143, 0, 0.58314, 0.01075, 0.4739, 0, 0.35413, 1e-05, 0.23927, 0, 0.10763, 0, 0, 1, 0], "triangles": [9, 7, 8, 9, 10, 7, 10, 6, 7, 10, 11, 6, 11, 5, 6, 11, 12, 5, 12, 4, 5, 12, 13, 4, 13, 14, 4, 4, 14, 3, 14, 15, 3, 3, 15, 2, 15, 16, 2, 2, 16, 1, 16, 17, 1, 17, 0, 1, 17, 18, 19, 17, 19, 0], "vertices": [2, 2, 26.16, 66.1, 0.464, 18, -23.88, 24.48, 0.536, 2, 2, -4.49, 66.65, 0.24, 18, 6.68, 22.03, 0.76, 2, 18, 35.04, 19.77, 0.76, 19, -23.94, 20.55, 0.24, 2, 18, 63.39, 17.5, 0.51895, 19, 4.44, 18.65, 0.48105, 3, 18, 91.35, 13.31, 0.17106, 19, 32.46, 14.84, 0.62094, 20, -21.73, 14.48, 0.208, 2, 19, 56.5, 15.18, 0.4875, 20, 2.31, 15.22, 0.5125, 2, 19, 82.72, 13.44, 0.432, 20, 28.55, 13.91, 0.568, 2, 19, 110.05, 11.61, 0.208, 20, 55.91, 12.54, 0.792, 1, 20, 79.23, 11.38, 1, 1, 20, 77.1, -31.07, 1, 2, 19, 103.07, -30.51, 0.224, 20, 49.63, -29.69, 0.776, 2, 19, 72.3, -28.46, 0.37101, 20, 18.83, -28.15, 0.62899, 2, 19, 50.85, -27.03, 0.50636, 20, -2.64, -27.08, 0.49364, 3, 18, 87.8, -27.09, 0.13115, 19, 29.44, -25.6, 0.67685, 20, -24.07, -26.01, 0.192, 2, 18, 61.38, -24.52, 0.48, 19, 2.99, -23.38, 0.52, 2, 18, 32.33, -22.65, 0.79196, 19, -26.08, -21.9, 0.20804, 3, 2, -4.96, 109.16, 0.288, 18, 4.51, -20.43, 0.71171, 19, -53.93, -20.05, 0.00029, 2, 2, 27.02, 108.59, 0.432, 18, -27.38, -17.88, 0.568, 1, 2, 53.17, 108.13, 1, 1, 2, 52.42, 65.63, 1], "hull": 20, "edges": [30, 28, 28, 26, 26, 24, 24, 22, 22, 20, 20, 18, 16, 18, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 30, 32, 32, 34, 34, 36, 36, 38, 38, 0, 0, 2, 2, 4], "width": 39, "height": 243}}, "Tentacle6": {"Tentacle6": {"name": "images/ScuttleBossTentacle1", "type": "mesh", "uvs": [1, 0.10807, 1, 0.23422, 1, 0.3513, 1, 0.46837, 1, 0.5866, 1, 0.67888, 1, 0.78715, 1, 0.89964, 1, 1, 0, 0.99784, 0, 0.88248, 0, 0.78082, 0, 0.67979, 0, 0.58732, 0.01075, 0.4739, 0, 0.35413, 1e-05, 0.23927, 0, 0.11155, 0, 0.00647, 1, 0], "triangles": [9, 7, 8, 9, 10, 7, 10, 6, 7, 10, 11, 6, 11, 5, 6, 11, 12, 5, 18, 19, 0, 17, 18, 0, 14, 15, 3, 3, 15, 2, 15, 16, 2, 2, 16, 1, 16, 17, 1, 17, 0, 1, 13, 4, 5, 13, 14, 4, 14, 3, 4, 12, 13, 5], "vertices": [3, 22, -69.35, 26.19, 0.04966, 21, -10.29, 26.01, 0.377, 2, 7.67, -53.92, 0.57333, 3, 22, -40.23, 25.03, 0.16166, 21, 18.81, 24.46, 0.59834, 2, -21.47, -54.43, 0.24, 3, 22, -13.21, 23.94, 0.32794, 21, 45.82, 23.02, 0.59739, 2, -48.51, -54.91, 0.07467, 3, 22, 13.82, 22.86, 0.46257, 21, 72.82, 21.58, 0.47048, 23, -40.5, 22.19, 0.06696, 3, 22, 41.1, 21.77, 0.50235, 21, 100.09, 20.13, 0.28114, 23, -13.2, 21.55, 0.21651, 3, 22, 62.41, 20.92, 0.43207, 21, 121.38, 18.99, 0.11409, 23, 8.12, 21.05, 0.45384, 3, 22, 87.4, 19.91, 0.30111, 21, 146.36, 17.66, 0.032, 23, 33.12, 20.47, 0.66689, 2, 22, 113.36, 18.87, 0.14933, 23, 59.1, 19.86, 0.85067, 2, 22, 136.52, 17.95, 0.05333, 23, 82.28, 19.32, 0.94667, 2, 22, 134.15, -28.82, 0.05333, 23, 80.68, -27.49, 0.94667, 2, 22, 107.52, -27.76, 0.144, 23, 54.04, -26.86, 0.856, 3, 22, 84.06, -26.82, 0.30284, 21, 142.4, -29.03, 0.02667, 23, 30.56, -26.31, 0.67049, 3, 22, 60.74, -25.88, 0.43273, 21, 119.1, -27.78, 0.09067, 23, 7.23, -25.77, 0.4766, 3, 22, 39.4, -25.03, 0.5154, 21, 97.76, -26.64, 0.25067, 23, -14.13, -25.27, 0.23393, 3, 22, 13.24, -23.48, 0.47416, 21, 71.63, -24.75, 0.43972, 23, -40.31, -24.15, 0.08611, 3, 22, -14.43, -22.87, 0.34871, 21, 43.98, -23.78, 0.60863, 2, -48.33, -101.74, 0.04267, 3, 22, -40.94, -21.81, 0.17537, 21, 17.48, -22.36, 0.62729, 2, -21.8, -101.27, 0.19733, 3, 22, -70.42, -20.63, 0.05776, 21, -11.98, -20.79, 0.41157, 2, 7.69, -100.75, 0.53067, 2, 21, -36.22, -19.5, 0.17867, 2, 31.96, -100.32, 0.82133, 2, 21, -35.22, 27.34, 0.168, 2, 32.63, -53.48, 0.832], "hull": 20, "edges": [30, 28, 28, 26, 26, 24, 24, 22, 22, 20, 20, 18, 16, 18, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 30, 32, 32, 34, 34, 36, 36, 38, 38, 0, 0, 2, 2, 4], "width": 39, "height": 231}}}}, {"name": "Spawner", "bones": ["spawnerConstraint"], "transform": ["spawnerConstraint"], "attachments": {"Drips2": {"Drips2": {"name": "images/Drips", "type": "mesh", "uvs": [1, 0.1099, 0.99149, 0.47829, 0.98808, 0.61203, 1, 0.79051, 1, 0.88399, 0.90036, 0.8709, 0.9074, 0.76218, 0.9151, 0.60355, 0.91216, 0.4596, 0.88562, 0.36799, 0.83844, 0.34929, 0.79421, 0.43716, 0.79274, 0.54747, 0.79716, 0.62599, 0.71018, 0.63347, 0.70769, 0.54718, 0.69986, 0.45025, 0.67332, 0.39977, 0.64826, 0.43342, 0.62909, 0.57364, 0.64679, 0.70638, 0.64089, 0.77929, 0.59076, 0.80173, 0.54211, 0.73629, 0.55652, 0.56371, 0.54948, 0.42968, 0.50525, 0.36051, 0.45172, 0.42105, 0.44242, 0.60742, 0.45251, 0.77455, 0.48121, 0.91706, 0.48756, 1, 0.34456, 1, 0.35716, 0.91519, 0.37168, 0.76276, 0.37201, 0.61011, 0.35058, 0.42229, 0.31065, 0.35111, 0.29181, 0.42748, 0.30475, 0.60537, 0.31212, 0.72877, 0.20519, 0.73437, 0.20666, 0.59574, 0.2214, 0.42906, 0.17649, 0.26511, 0.12194, 0.25046, 0.09402, 0.40566, 0.09348, 0.5572, 0.10977, 0.71712, 0.1239, 0.85594, 0, 0.86086, 0, 0.72508, 0.01173, 0.55255, 0.01127, 0.4213, 0.0112, 0.40055, 0, 0.2081, 0.0343, 0.08471, 0.18173, 0, 0.87144, 0, 1, 0], "triangles": [29, 30, 33, 5, 3, 4, 50, 48, 49, 32, 33, 31, 31, 33, 30, 33, 34, 29, 29, 34, 28, 34, 35, 28, 5, 6, 3, 50, 51, 48, 6, 2, 3, 20, 22, 23, 6, 7, 2, 20, 24, 19, 20, 23, 24, 41, 39, 40, 41, 42, 39, 39, 42, 38, 42, 43, 38, 51, 47, 48, 51, 52, 47, 14, 12, 13, 14, 15, 12, 2, 7, 1, 27, 28, 35, 27, 35, 36, 7, 8, 1, 19, 24, 18, 24, 25, 18, 46, 47, 53, 47, 52, 53, 11, 12, 16, 12, 15, 16, 0, 1, 9, 1, 8, 9, 16, 17, 11, 11, 17, 10, 18, 25, 17, 25, 26, 17, 38, 43, 37, 37, 43, 44, 36, 37, 27, 53, 54, 46, 27, 37, 26, 46, 54, 45, 54, 55, 45, 10, 17, 58, 9, 10, 0, 58, 17, 26, 26, 57, 58, 37, 44, 57, 10, 58, 0, 57, 26, 37, 44, 45, 57, 55, 56, 45, 45, 56, 57, 58, 59, 0, 21, 22, 20], "vertices": [1, 1, 81.68, -18.42, 1, 2, 1, 80.51, -56.3, 0.76, 28, 80.63, 49.39, 0.24, 2, 1, 80.04, -70.06, 0.76, 28, 80.16, 35.64, 0.24, 2, 1, 81.68, -88.41, 0.6, 28, 81.81, 17.29, 0.4, 2, 1, 81.68, -98.03, 0.424, 28, 81.81, 7.67, 0.576, 2, 1, 67.94, -96.68, 0.424, 28, 68.07, 9.02, 0.576, 2, 1, 68.91, -85.5, 0.6, 28, 69.04, 20.2, 0.4, 2, 1, 69.97, -69.19, 0.76, 28, 70.1, 36.51, 0.24, 2, 1, 69.57, -54.38, 0.76, 28, 69.69, 51.32, 0.24, 2, 1, 65.91, -44.96, 0.888, 28, 66.03, 60.74, 0.112, 2, 1, 59.4, -43.04, 0.888, 28, 59.53, 62.66, 0.112, 2, 1, 53.3, -52.07, 0.888, 28, 53.43, 53.62, 0.112, 2, 1, 53.1, -63.42, 0.76, 28, 53.22, 42.28, 0.24, 2, 1, 53.71, -71.49, 0.76, 28, 53.83, 34.2, 0.24, 2, 1, 41.71, -72.26, 0.76, 28, 41.84, 33.44, 0.24, 2, 1, 41.37, -63.39, 0.76, 28, 41.5, 42.31, 0.24, 2, 1, 40.29, -53.42, 0.888, 28, 40.42, 52.28, 0.112, 2, 1, 36.63, -48.23, 0.888, 28, 36.76, 57.47, 0.112, 2, 1, 33.18, -51.69, 0.888, 28, 33.3, 54.01, 0.112, 2, 1, 30.53, -66.11, 0.76, 28, 30.66, 39.59, 0.24, 2, 1, 32.97, -79.76, 0.6, 28, 33.1, 25.94, 0.4, 2, 1, 32.16, -87.26, 0.472, 28, 32.28, 18.44, 0.528, 2, 1, 25.25, -89.57, 0.472, 28, 25.37, 16.13, 0.528, 2, 1, 18.54, -82.84, 0.6, 28, 18.66, 22.86, 0.4, 2, 1, 20.53, -65.09, 0.76, 28, 20.65, 40.61, 0.24, 2, 1, 19.55, -51.31, 0.888, 28, 19.68, 54.39, 0.112, 2, 1, 13.46, -44.19, 0.888, 28, 13.58, 61.51, 0.112, 2, 1, 6.07, -50.42, 0.888, 28, 6.2, 55.28, 0.112, 2, 1, 4.79, -69.58, 0.76, 28, 4.91, 36.11, 0.24, 2, 1, 6.18, -86.77, 0.6, 28, 6.31, 18.93, 0.4, 2, 1, 10.14, -101.43, 0.424, 28, 10.26, 4.27, 0.576, 2, 1, 11.02, -109.96, 0.328, 28, 11.14, -4.26, 0.672, 2, 1, -8.7, -109.96, 0.328, 28, -8.58, -4.26, 0.672, 2, 1, -6.97, -101.23, 0.424, 28, -6.84, 4.46, 0.576, 2, 1, -4.96, -85.56, 0.6, 28, -4.84, 20.14, 0.4, 2, 1, -4.92, -69.86, 0.76, 28, -4.79, 35.84, 0.24, 2, 1, -7.87, -50.54, 0.888, 28, -7.75, 55.15, 0.112, 2, 1, -13.38, -43.23, 0.888, 28, -13.26, 62.47, 0.112, 2, 1, -15.98, -51.08, 0.888, 28, -15.85, 54.62, 0.112, 2, 1, -14.19, -69.37, 0.76, 28, -14.07, 36.32, 0.24, 2, 1, -13.18, -82.06, 0.6, 28, -13.05, 23.63, 0.4, 2, 1, -27.92, -82.64, 0.6, 28, -27.8, 23.06, 0.4, 2, 1, -27.72, -68.38, 0.76, 28, -27.6, 37.32, 0.24, 2, 1, -25.69, -51.24, 0.888, 28, -25.56, 54.46, 0.112, 1, 1, -31.88, -34.38, 1, 1, 1, -39.4, -32.87, 1, 2, 1, -43.25, -48.83, 0.888, 28, -43.13, 56.86, 0.112, 2, 1, -43.33, -64.42, 0.76, 28, -43.2, 41.28, 0.24, 2, 1, -41.08, -80.87, 0.6, 28, -40.96, 24.83, 0.4, 2, 1, -39.13, -95.14, 0.424, 28, -39.01, 10.56, 0.576, 2, 1, -56.22, -95.65, 0.424, 28, -56.09, 10.05, 0.576, 2, 1, -56.22, -81.68, 0.6, 28, -56.09, 24.01, 0.4, 2, 1, -54.6, -63.94, 0.76, 28, -54.48, 41.76, 0.24, 2, 1, -54.66, -50.44, 0.888, 28, -54.54, 55.25, 0.112, 2, 1, -54.67, -48.31, 0.888, 28, -54.55, 57.39, 0.112, 1, 1, -56.22, -28.52, 1, 1, 1, -51.49, -15.83, 1, 1, 1, -31.16, -7.12, 1, 1, 1, 63.95, -7.12, 1, 1, 1, 81.68, -7.12, 1], "hull": 60, "edges": [100, 98, 94, 92, 92, 90, 90, 88, 88, 86, 86, 84, 84, 82, 82, 80, 80, 78, 78, 76, 76, 74, 74, 72, 72, 70, 70, 68, 68, 66, 66, 64, 62, 64, 62, 60, 60, 58, 58, 56, 56, 54, 54, 52, 52, 50, 50, 48, 48, 46, 46, 44, 36, 38, 38, 40, 40, 42, 36, 34, 34, 32, 32, 30, 30, 28, 28, 26, 26, 24, 24, 22, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 100, 102, 98, 96, 96, 94, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 2, 0, 0, 118, 116, 0, 44, 42], "width": 175, "height": 138}}, "Eyes": {"Eyes": {"name": "images/SpawnerEyes", "x": -3.39, "y": -1.32, "scaleX": 1.5461, "scaleY": 1.5461, "rotation": -91.02, "width": 181, "height": 92}, "EyesCharge": {"name": "images/SpawnerEyes", "x": -3.39, "y": -1.32, "scaleX": 1.5461, "scaleY": 1.5461, "rotation": -91.02, "width": 181, "height": 92}, "EyesCharge2": {"name": "images/SpawnerEyes", "x": -3.39, "y": -1.32, "scaleX": 1.5461, "scaleY": 1.5461, "rotation": -91.02, "width": 181, "height": 92}, "EyesClosed": {"name": "images/SpawnerEyesShut", "x": -4.12, "y": 0.73, "scaleX": 1.5461, "scaleY": 1.5461, "rotation": -91.02, "width": 180, "height": 91}}, "Head": {"Head": {"name": "images/SpawnerHead", "type": "mesh", "uvs": [0.61339, 0.01397, 0.71758, 0.10499, 0.80825, 0.20702, 0.86994, 0.29068, 0.94553, 0.41809, 1, 0.59181, 1, 0.80016, 0.96607, 0.95834, 0.84611, 1, 0.66358, 1, 0.51936, 0.99998, 0.36722, 0.99996, 0.20082, 1, 0.07621, 1, 1e-05, 0.86469, 0, 0.58025, 0.05132, 0.43322, 0.12509, 0.32353, 0.20969, 0.20403, 0.27916, 0.10403, 0.38332, 0.02102, 0.46127, 0, 0.5433, 1e-05, 0.34662, 0.1912, 0.31968, 0.37523, 0.29907, 0.56332, 0.30224, 0.68061, 0.13742, 0.8505, 0.13425, 0.6887, 0.16437, 0.44602, 0.71112, 0.68061, 0.7127, 0.54916, 0.70003, 0.37119, 0.66041, 0.15479, 0.51144, 0.90105, 0.50034, 0.47433, 0.49876, 0.27614, 0.49717, 0.13053, 0.50643, 0.81215, 0.3179, 0.85303, 0.70507, 0.85712, 0.50291, 0.61], "triangles": [8, 9, 40, 40, 9, 34, 12, 39, 11, 11, 34, 10, 9, 10, 34, 11, 39, 34, 39, 38, 34, 34, 38, 40, 40, 30, 6, 40, 38, 30, 27, 26, 39, 38, 26, 41, 35, 41, 25, 26, 38, 39, 38, 41, 30, 28, 25, 26, 30, 41, 31, 35, 25, 24, 24, 36, 35, 25, 41, 26, 41, 35, 31, 35, 32, 31, 35, 36, 32, 6, 7, 8, 13, 27, 12, 12, 27, 39, 13, 14, 27, 6, 8, 40, 14, 28, 27, 14, 15, 28, 27, 28, 26, 6, 30, 5, 28, 15, 29, 28, 29, 25, 29, 15, 16, 30, 31, 5, 24, 23, 36, 23, 37, 36, 31, 4, 5, 23, 20, 37, 25, 29, 24, 4, 32, 3, 4, 31, 32, 16, 17, 29, 29, 17, 24, 17, 18, 24, 24, 18, 23, 36, 33, 32, 32, 2, 3, 2, 33, 1, 2, 32, 33, 33, 37, 0, 0, 37, 22, 37, 33, 36, 18, 19, 23, 20, 21, 37, 23, 19, 20, 33, 0, 1, 37, 21, 22], "vertices": [1, 2, 244.16, -43.04, 1, 1, 2, 215.03, -80.24, 1, 1, 2, 182.54, -112.48, 1, 1, 2, 155.99, -134.34, 1, 1, 2, 115.67, -160.99, 1, 1, 2, 61, -179.73, 1, 1, 2, -4.14, -178.57, 1, 1, 2, -53.37, -165.4, 1, 2, 2, -65.62, -121.76, 0.792, 27, -147.67, -121.85, 0.208, 2, 2, -64.44, -55.7, 0.184, 27, -146.49, -55.79, 0.816, 2, 2, -63.5, -3.51, 0.072, 27, -145.55, -3.6, 0.928, 2, 2, -62.51, 51.55, 0.216, 27, -144.56, 51.45, 0.784, 2, 2, -61.45, 111.77, 0.632, 27, -143.5, 111.68, 0.368, 1, 2, -60.65, 156.86, 1, 1, 2, -17.85, 183.68, 1, 1, 2, 71.08, 182.1, 1, 1, 2, 116.71, 162.71, 1, 1, 2, 150.53, 135.4, 1, 1, 2, 187.35, 104.11, 1, 1, 2, 218.16, 78.41, 1, 1, 2, 243.44, 40.26, 1, 1, 2, 249.51, 11.93, 1, 1, 2, 248.98, -17.76, 1, 2, 2, 190.47, 54.49, 0.808, 27, 108.43, 54.4, 0.192, 2, 2, 133.11, 65.27, 0.472, 27, 51.06, 65.17, 0.528, 2, 2, 74.44, 73.77, 0.232, 27, -7.61, 73.68, 0.768, 2, 2, 37.75, 73.28, 0.2, 27, -44.3, 73.19, 0.8, 2, 2, -14.3, 133.88, 0.728, 27, -96.35, 133.78, 0.272, 2, 2, 36.3, 134.12, 0.808, 27, -45.74, 134.03, 0.192, 2, 2, 111.98, 121.87, 0.808, 27, 29.93, 121.77, 0.192, 2, 2, 35.11, -74.69, 0.248, 27, -46.94, -74.78, 0.752, 2, 2, 76.2, -76, 0.264, 27, -5.85, -76.09, 0.736, 2, 2, 131.92, -72.4, 0.472, 27, 49.87, -72.5, 0.528, 2, 2, 199.83, -59.27, 0.808, 27, 117.78, -59.37, 0.192, 1, 27, -114.57, -1.29, 1, 2, 2, 100.96, 0.44, 0.04, 27, 18.91, 0.34, 0.96, 2, 2, 162.94, -0.1, 0.392, 27, 80.89, -0.19, 0.608, 2, 2, 208.47, -0.33, 0.808, 27, 126.42, -0.43, 0.192, 1, 27, -86.74, 0.03, 1, 2, 2, -16.26, 68.58, 0.376, 27, -98.3, 68.48, 0.624, 2, 2, -20.03, -71.51, 0.136, 27, -102.08, -71.61, 0.864, 1, 27, -23.52, 0.17, 1], "hull": 23, "edges": [36, 38, 38, 40, 40, 42, 42, 44, 44, 0, 0, 2, 2, 4, 4, 6, 12, 14, 14, 16, 28, 30, 28, 26, 40, 46, 46, 48, 48, 50, 50, 52, 22, 24, 24, 26, 24, 54, 54, 56, 56, 58, 30, 32, 10, 12, 16, 18, 60, 62, 62, 64, 64, 66, 66, 0, 32, 34, 8, 10, 18, 20, 20, 22, 20, 68, 70, 72, 72, 74, 68, 76, 22, 78, 78, 52, 18, 80, 80, 60, 70, 82, 82, 76, 34, 36, 6, 8], "width": 234, "height": 204}}, "images/Drips": {"Drips": {"name": "images/Drips", "type": "mesh", "uvs": [1, 0.1099, 0.99149, 0.47829, 0.98808, 0.61203, 1, 0.79051, 1, 0.88399, 0.90036, 0.8709, 0.9074, 0.76218, 0.9151, 0.60355, 0.91216, 0.4596, 0.88562, 0.36799, 0.83844, 0.34929, 0.79421, 0.43716, 0.79274, 0.54747, 0.79716, 0.62599, 0.71018, 0.63347, 0.70769, 0.54718, 0.69986, 0.45025, 0.67332, 0.39977, 0.64826, 0.43342, 0.62909, 0.57364, 0.64679, 0.70638, 0.64089, 0.77929, 0.59076, 0.80173, 0.54211, 0.73629, 0.55652, 0.56371, 0.54948, 0.42968, 0.50525, 0.36051, 0.45172, 0.42105, 0.44242, 0.60742, 0.45251, 0.77455, 0.48121, 0.91706, 0.48756, 1, 0.34456, 1, 0.35716, 0.91519, 0.37168, 0.76276, 0.37201, 0.61011, 0.35058, 0.42229, 0.31065, 0.35111, 0.29181, 0.42748, 0.30475, 0.60537, 0.31212, 0.72877, 0.20519, 0.73437, 0.20666, 0.59574, 0.2214, 0.42906, 0.17649, 0.26511, 0.12194, 0.25046, 0.09402, 0.40566, 0.09348, 0.5572, 0.10977, 0.71712, 0.1239, 0.85594, 0, 0.86086, 0, 0.72508, 0.01173, 0.55255, 0.01127, 0.4213, 0.0112, 0.40055, 0, 0.2081, 0.0343, 0.08471, 0.18173, 0, 0.87144, 0, 1, 0], "triangles": [29, 30, 33, 5, 3, 4, 50, 48, 49, 32, 33, 31, 31, 33, 30, 33, 34, 29, 29, 34, 28, 34, 35, 28, 5, 6, 3, 50, 51, 48, 6, 2, 3, 20, 22, 23, 6, 7, 2, 20, 24, 19, 20, 23, 24, 41, 39, 40, 41, 42, 39, 39, 42, 38, 42, 43, 38, 51, 47, 48, 51, 52, 47, 14, 12, 13, 14, 15, 12, 2, 7, 1, 27, 28, 35, 27, 35, 36, 7, 8, 1, 19, 24, 18, 24, 25, 18, 46, 47, 53, 47, 52, 53, 11, 12, 16, 12, 15, 16, 0, 1, 9, 1, 8, 9, 16, 17, 11, 11, 17, 10, 18, 25, 17, 25, 26, 17, 38, 43, 37, 37, 43, 44, 36, 37, 27, 53, 54, 46, 27, 37, 26, 46, 54, 45, 54, 55, 45, 10, 17, 58, 9, 10, 0, 58, 17, 26, 26, 57, 58, 37, 44, 57, 10, 58, 0, 57, 26, 37, 44, 45, 57, 55, 56, 45, 45, 56, 57, 58, 59, 0, 21, 22, 20], "vertices": [1, 1, 79.67, 10.88, 1, 2, 1, 78.18, -27, 0.76, 28, 78.3, 78.69, 0.24, 2, 1, 77.58, -40.76, 0.76, 28, 77.71, 64.94, 0.24, 2, 1, 79.67, -59.11, 0.6, 28, 79.79, 46.59, 0.4, 2, 1, 79.67, -68.72, 0.424, 28, 79.79, 36.97, 0.576, 2, 1, 62.23, -67.38, 0.424, 28, 62.36, 38.32, 0.576, 2, 1, 63.46, -56.2, 0.6, 28, 63.59, 49.5, 0.4, 2, 1, 64.81, -39.88, 0.76, 28, 64.94, 65.81, 0.24, 2, 1, 64.3, -25.08, 0.76, 28, 64.42, 80.62, 0.24, 2, 1, 59.65, -15.66, 0.888, 28, 59.78, 90.04, 0.112, 2, 1, 51.4, -13.74, 0.888, 28, 51.52, 91.96, 0.112, 2, 1, 43.66, -22.77, 0.888, 28, 43.78, 82.92, 0.112, 2, 1, 43.4, -34.12, 0.76, 28, 43.52, 71.58, 0.24, 2, 1, 44.17, -42.19, 0.76, 28, 44.3, 63.51, 0.24, 2, 1, 28.95, -42.96, 0.76, 28, 29.07, 62.74, 0.24, 2, 1, 28.51, -34.09, 0.76, 28, 28.64, 71.61, 0.24, 2, 1, 27.14, -24.12, 0.888, 28, 27.27, 81.58, 0.112, 2, 1, 22.5, -18.93, 0.888, 28, 22.62, 86.77, 0.112, 2, 1, 18.11, -22.39, 0.888, 28, 18.24, 83.31, 0.112, 2, 1, 14.76, -36.81, 0.76, 28, 14.88, 68.89, 0.24, 2, 1, 17.86, -50.46, 0.6, 28, 17.98, 55.24, 0.4, 2, 1, 16.82, -57.96, 0.472, 28, 16.95, 47.74, 0.528, 2, 1, 8.05, -60.26, 0.472, 28, 8.18, 45.43, 0.528, 2, 1, -0.46, -53.53, 0.6, 28, -0.34, 52.16, 0.4, 2, 1, 2.06, -35.79, 0.76, 28, 2.18, 69.91, 0.24, 2, 1, 0.83, -22, 0.888, 28, 0.95, 83.69, 0.112, 2, 1, -6.91, -14.89, 0.888, 28, -6.79, 90.81, 0.112, 2, 1, -16.28, -21.12, 0.888, 28, -16.16, 84.58, 0.112, 2, 1, -17.91, -40.28, 0.76, 28, -17.78, 65.42, 0.24, 2, 1, -16.14, -57.47, 0.6, 28, -16.02, 48.23, 0.4, 2, 1, -11.12, -72.12, 0.424, 28, -11, 33.57, 0.576, 2, 1, -10.01, -80.65, 0.328, 28, -9.88, 25.04, 0.672, 2, 1, -35.03, -80.65, 0.328, 28, -34.91, 25.04, 0.672, 2, 1, -32.83, -71.93, 0.424, 28, -32.7, 33.77, 0.576, 2, 1, -30.29, -56.26, 0.6, 28, -30.16, 49.44, 0.4, 2, 1, -30.23, -40.56, 0.76, 28, -30.1, 65.14, 0.24, 2, 1, -33.98, -21.24, 0.888, 28, -33.86, 84.45, 0.112, 2, 1, -40.97, -13.92, 0.888, 28, -40.84, 91.77, 0.112, 2, 1, -44.26, -21.78, 0.888, 28, -44.14, 83.92, 0.112, 2, 1, -42, -40.07, 0.76, 28, -41.87, 65.63, 0.24, 2, 1, -40.71, -52.76, 0.6, 28, -40.58, 52.94, 0.4, 2, 1, -59.42, -53.34, 0.6, 28, -59.3, 52.36, 0.4, 2, 1, -59.17, -39.08, 0.76, 28, -59.04, 66.62, 0.24, 2, 1, -56.59, -21.94, 0.888, 28, -56.46, 83.76, 0.112, 1, 1, -64.45, -5.08, 1, 1, 1, -73.99, -3.57, 1, 2, 1, -78.88, -19.53, 0.888, 28, -78.75, 86.16, 0.112, 2, 1, -78.97, -35.12, 0.76, 28, -78.85, 70.58, 0.24, 2, 1, -76.12, -51.56, 0.6, 28, -76, 54.13, 0.4, 2, 1, -73.65, -65.84, 0.424, 28, -73.52, 39.86, 0.576, 2, 1, -95.33, -66.34, 0.424, 28, -95.21, 39.35, 0.576, 2, 1, -95.33, -52.38, 0.6, 28, -95.21, 53.32, 0.4, 2, 1, -93.28, -34.64, 0.76, 28, -93.15, 71.06, 0.24, 2, 1, -93.36, -21.14, 0.888, 28, -93.23, 84.56, 0.112, 2, 1, -93.37, -19.01, 0.888, 28, -93.25, 86.69, 0.112, 1, 1, -95.33, 0.78, 1, 1, 1, -89.33, 13.47, 1, 1, 1, -63.53, 22.18, 1, 1, 1, 57.17, 22.18, 1, 1, 1, 79.67, 22.18, 1], "hull": 60, "edges": [100, 98, 94, 92, 92, 90, 90, 88, 88, 86, 86, 84, 84, 82, 82, 80, 80, 78, 78, 76, 76, 74, 74, 72, 72, 70, 70, 68, 68, 66, 66, 64, 62, 64, 62, 60, 60, 58, 58, 56, 56, 54, 54, 52, 52, 50, 50, 48, 48, 46, 46, 44, 36, 38, 38, 40, 40, 42, 36, 34, 34, 32, 32, 30, 30, 28, 28, 26, 26, 24, 24, 22, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 100, 102, 98, 96, 96, 94, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 2, 0, 0, 118, 116, 0, 44, 42], "width": 175, "height": 138}}, "Mouth": {"Mouth Open": {"name": "images/ChaserBossMouth", "type": "mesh", "uvs": [0.53839, 0.16587, 0.59644, 0.18622, 0.56451, 0.33199, 0.63115, 0.40233, 0.73893, 0.4073, 0.74158, 0.54253, 0.8057, 0.71621, 0.99999, 0.84884, 1, 0.90684, 0.91717, 0.88152, 0.80673, 1, 0.52144, 1, 0.51453, 0.94061, 0.73311, 0.72113, 0.73476, 0.70415, 0.61744, 0.74693, 0.50294, 0.7398, 0.50082, 0.69507, 0.59694, 0.63154, 0.70296, 0.4792, 0.61771, 0.55894, 0.51864, 0.57086, 0.51215, 0.51574, 0.56019, 0.42215, 0.51991, 0.36049, 0.47994, 0.36697, 0.42788, 0.42211, 0.48513, 0.52259, 0.48725, 0.56667, 0.37625, 0.5611, 0.29156, 0.47812, 0.38124, 0.61036, 0.48584, 0.69853, 0.48725, 0.74132, 0.37064, 0.74715, 0.25781, 0.70503, 0.25455, 0.72113, 0.46852, 0.94272, 0.47312, 1, 0.15102, 1, 0.03388, 0.88761, 0, 0.91317, 1e-05, 0.85238, 0.17202, 0.74418, 0.24547, 0.59426, 0.24185, 0.4073, 0.36282, 0.39582, 0.42452, 0.32086, 0.40552, 0.18264, 0.45521, 0.15478, 0.45918, 1e-05, 0.53328, 0, 0.6973, 0.44808, 0.35762, 0.42916], "triangles": [39, 37, 38, 39, 36, 37, 34, 32, 33, 34, 31, 32, 34, 35, 31, 29, 27, 28, 29, 26, 27, 21, 22, 20, 22, 23, 20, 15, 16, 17, 14, 15, 18, 15, 17, 18, 12, 10, 11, 13, 10, 12, 23, 24, 2, 23, 2, 3, 19, 23, 52, 13, 9, 10, 9, 7, 8, 13, 6, 9, 9, 6, 7, 13, 14, 6, 14, 5, 6, 5, 14, 19, 19, 14, 18, 19, 4, 5, 19, 52, 4, 19, 20, 23, 23, 3, 52, 52, 3, 4, 25, 26, 47, 29, 53, 26, 53, 46, 26, 26, 46, 47, 53, 45, 46, 30, 45, 53, 39, 40, 36, 41, 42, 40, 40, 43, 36, 40, 42, 43, 36, 43, 35, 35, 43, 44, 31, 35, 30, 30, 35, 44, 44, 45, 30, 30, 53, 29, 25, 47, 24, 47, 0, 2, 47, 49, 0, 49, 50, 51, 0, 49, 51, 2, 0, 1, 47, 48, 49, 24, 47, 2], "vertices": [1, 27, 22.16, -9.95, 1, 1, 27, 17.87, -15.99, 1, 3, 27, -3.73, -10.63, 0.48, 29, 44.01, -37.77, 0.00067, 30, 44.38, 17.88, 0.51933, 2, 27, -16.52, -16.02, 0.808, 30, 34.02, 9.21, 0.192, 1, 30, 33.05, -5.12, 1, 2, 29, 13.07, -60.77, 0.00183, 30, 13.44, -5.12, 0.99817, 1, 30, -11.89, -13.19, 1, 1, 30, -31.58, -38.69, 1, 1, 30, -39.99, -38.54, 1, 1, 30, -36.12, -27.59, 1, 1, 36, 19.44, 20.49, 1, 1, 36, 49.28, -2.95, 1, 1, 36, 44.68, -10.29, 1, 1, 30, -12.44, -3.53, 1, 1, 30, -9.98, -3.79, 1, 1, 35, 26.6, 6.9, 1, 1, 35, 37.41, -3.88, 1, 1, 35, 33.36, -8.96, 1, 1, 35, 17.67, -7.51, 1, 2, 29, 22.34, -55.8, 0.00774, 30, 22.71, -0.15, 0.99226, 1, 34, 18.67, 7.62, 1, 1, 34, 28.12, -1.72, 1, 1, 34, 22.34, -7.31, 1, 2, 29, 30.95, -36.97, 0.00356, 30, 31.32, 18.69, 0.99644, 2, 27, -8.02, -4.27, 0.76, 30, 40.36, 23.89, 0.24, 2, 27, -8.89, -0.35, 0.744, 29, 39.14, -26.44, 0.256, 1, 29, 31.27, -19.37, 1, 1, 31, 23.2, 3.56, 1, 1, 31, 27.59, -1.09, 1, 1, 31, 15.89, -10.13, 1, 1, 29, 23.47, -1.1, 1, 1, 32, 11.06, 8.38, 1, 1, 32, 29.88, 6.7, 1, 2, 29, -15.15, -26.44, 0.12, 32, 33.81, 1.89, 0.88, 1, 32, 22.01, -8.22, 1, 1, 29, -9.34, 3.98, 1, 1, 29, -11.67, 4.45, 1, 1, 33, 41.48, 9.87, 1, 1, 33, 46.6, 3.3, 1, 1, 33, 10.96, -20.46, 1, 1, 29, -35.28, 34.23, 1, 1, 29, -38.91, 38.8, 1, 1, 29, -30.09, 38.64, 1, 1, 29, -14.82, 15.49, 1, 1, 29, 6.75, 5.33, 1, 1, 29, 33.86, 5.33, 1, 2, 27, -14.94, 16.66, 0.064, 29, 35.24, -10.79, 0.936, 2, 27, -2.68, 7.28, 0.304, 29, 45.96, -19.19, 0.696, 1, 27, 17.2, 9.58, 1, 1, 27, 23.96, 1.09, 1, 1, 27, 46.39, 0.16, 1, 1, 27, 46.22, -9.7, 1, 1, 30, 27.24, 0.53, 1, 1, 29, 30.42, -10.01, 1], "hull": 52, "edges": [84, 86, 90, 92, 96, 98, 98, 100, 100, 102, 102, 0, 0, 2, 6, 8, 12, 14, 82, 84, 26, 18, 14, 16, 18, 16, 20, 18, 72, 74, 74, 76, 82, 80, 80, 72, 76, 78, 80, 78, 26, 24, 20, 22, 24, 22, 86, 88, 88, 90, 8, 10, 10, 12, 92, 94, 94, 96, 2, 4, 4, 6, 60, 58, 72, 70, 70, 60, 70, 68, 68, 66, 66, 64, 64, 62, 62, 60, 58, 56, 56, 54, 54, 52, 52, 50, 34, 32, 32, 30, 30, 28, 28, 26, 34, 36, 36, 38, 38, 28, 46, 104, 104, 38, 60, 106, 106, 52, 46, 48, 48, 50, 46, 44, 44, 42, 42, 40, 40, 38], "width": 133, "height": 145}, "Mouth Closed": {"name": "images/ChaserBossMouthShut", "x": -40.53, "y": 0.18, "rotation": -91.02, "width": 105, "height": 131}}, "MouthBack": {"Mouth Back": {"name": "images/ChaserBossMouthBack", "type": "mesh", "uvs": [0.61241, 0.26525, 0.84845, 0.32903, 0.83063, 0.49565, 0.89299, 0.68697, 1, 0.82274, 1, 0.92354, 0.77719, 1, 0.59905, 1, 0.51443, 0.95234, 0.41645, 1, 0.16259, 1, 0, 0.90091, 0, 0.84331, 0.12251, 0.70754, 0.15591, 0.53268, 0.18932, 0.34137, 0.40086, 0.26937, 0.42536, 0, 0.58569, 0], "triangles": [10, 11, 12, 16, 2, 14, 14, 15, 16, 13, 14, 8, 13, 10, 12, 13, 9, 10, 8, 9, 13, 16, 17, 0, 17, 18, 0, 2, 16, 0, 2, 0, 1, 6, 7, 3, 8, 2, 3, 7, 8, 3, 2, 8, 14, 6, 3, 4, 5, 6, 4], "vertices": [2, 30, 36.29, 15.7, 0.584, 27, -12.65, -11.69, 0.416, 1, 30, 29.19, -7.07, 1, 1, 30, 11.72, -5.03, 1, 1, 30, -8.47, -10.72, 1, 1, 30, -22.91, -20.84, 1, 1, 30, -33.49, -20.65, 1, 1, 30, -41.13, 1.1, 1, 1, 30, -40.82, 18.38, 1, 2, 30, -35.67, 26.49, 0.512, 29, -36.04, -29.16, 0.488, 1, 29, -40.88, -19.57, 1, 1, 29, -40.44, 5.05, 1, 1, 29, -29.75, 20.63, 1, 1, 29, -23.71, 20.52, 1, 1, 29, -9.66, 8.39, 1, 1, 29, 8.63, 4.82, 1, 1, 29, 28.66, 1.22, 1, 1, 29, 35.85, -19.43, 1, 1, 27, 16.56, 3.45, 1, 1, 27, 16.71, -11.58, 1], "hull": 19, "edges": [24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 0, 0, 2, 2, 4, 4, 6, 6, 8, 22, 24, 22, 20, 18, 20, 18, 16, 16, 14, 12, 14, 8, 10, 12, 10], "width": 97, "height": 105}}, "Spikes": {"Spikes": {"name": "images/SpawnerSpikes", "type": "mesh", "uvs": [0.72805, 0.05651, 0.80878, 0.23196, 0.78844, 0.34368, 0.82116, 0.431, 0.85831, 0.47722, 0.8698, 0.318, 0.89279, 0.30515, 0.93347, 0.40531, 0.95028, 0.60692, 0.91225, 0.67212, 0.92555, 0.7339, 0.95081, 0.78796, 1, 0.7088, 1, 0.81885, 0.96278, 0.94242, 0.8604, 1, 0.25808, 1, 0.17298, 1, 0.03071, 0.95208, 0, 0.79376, 0, 0.71268, 0.05863, 0.79569, 0.07857, 0.74936, 0.0799, 0.66441, 0.05198, 0.59876, 0.06926, 0.39797, 0.12112, 0.29371, 0.13708, 0.43079, 0.17697, 0.41341, 0.21024, 0.31942, 0.19159, 0.23579, 0.26339, 0.07361, 0.32987, 0, 0.36843, 0, 0.31791, 0.24737, 0.43757, 0.48678, 0.55826, 0.48027, 0.67587, 0.25041, 0.63519, 0, 0.65818, 0, 0.26738, 0.57559, 0.15968, 0.53119, 0.19957, 0.48099, 0.23291, 0.42104, 0.27935, 0.68178, 0.09453, 0.81693, 0.11049, 0.76866, 0.11713, 0.72426, 0.1783, 0.80921, 0.27935, 0.74743, 0.21154, 0.83817, 0.27004, 0.89802, 0.72744, 0.93084, 0.80323, 0.82851, 0.92023, 0.81113, 0.83443, 0.79696, 0.87776, 0.72891, 0.88838, 0.76871, 0.71328, 0.69552, 0.79375, 0.57995, 0.72743, 0.58509, 0.75421, 0.47912, 0.77253, 0.53373, 0.65293, 0.61515, 0.75728, 0.77567, 0.34014, 0.61139], "triangles": [34, 31, 32, 30, 31, 34, 34, 32, 33, 29, 30, 34, 43, 29, 34, 28, 29, 43, 42, 28, 43, 43, 34, 35, 40, 43, 35, 42, 43, 40, 65, 40, 35, 63, 65, 35, 44, 40, 65, 50, 48, 49, 46, 47, 48, 46, 22, 47, 27, 25, 26, 42, 41, 27, 42, 27, 28, 24, 25, 27, 24, 27, 41, 23, 24, 41, 41, 42, 40, 44, 41, 40, 47, 23, 41, 47, 41, 44, 49, 44, 65, 48, 47, 44, 22, 23, 47, 49, 48, 44, 65, 51, 49, 21, 19, 20, 45, 22, 46, 21, 22, 45, 45, 46, 48, 50, 45, 48, 51, 50, 49, 18, 19, 21, 18, 21, 45, 17, 45, 50, 17, 50, 51, 18, 45, 17, 16, 17, 51, 16, 51, 52, 52, 51, 63, 54, 57, 10, 54, 10, 11, 11, 12, 13, 54, 53, 55, 54, 55, 57, 52, 64, 53, 13, 54, 11, 14, 54, 13, 15, 53, 54, 15, 54, 14, 52, 53, 15, 16, 52, 15, 53, 64, 55, 57, 56, 10, 55, 56, 57, 5, 6, 7, 4, 5, 7, 62, 3, 4, 59, 62, 4, 4, 7, 8, 9, 59, 4, 8, 9, 4, 58, 60, 59, 56, 59, 9, 64, 58, 59, 56, 9, 10, 56, 64, 59, 55, 64, 56, 52, 58, 64, 37, 39, 0, 37, 0, 1, 38, 39, 37, 2, 37, 1, 61, 37, 2, 61, 2, 3, 36, 37, 61, 62, 61, 3, 61, 63, 36, 60, 61, 62, 60, 63, 61, 35, 36, 63, 60, 62, 59, 58, 63, 60, 65, 63, 51, 63, 58, 52], "vertices": [1, 4, 164.07, 62.63, 1, 1, 4, 136.07, -3.49, 1, 1, 4, 98.77, -15.95, 1, 2, 4, 82.81, -45.78, 0.59197, 5, 100.23, 62.73, 0.40803, 1, 5, 108.96, 40.18, 1, 1, 5, 139.6, 85.22, 1, 1, 5, 151.68, 83.69, 1, 1, 5, 153.25, 44.16, 1, 1, 5, 128.1, -20.2, 1, 1, 5, 101.05, -30.85, 1, 2, 5, 96.88, -52.48, 0.46752, 6, 88.38, 43.45, 0.53248, 1, 6, 97.75, 23.29, 1, 1, 6, 126.05, 45.99, 1, 2, 5, 115.6, -95.35, 0.00047, 6, 120.06, 9.03, 0.99953, 1, 6, 95.18, -29.53, 1, 1, 6, 42.11, -40.78, 1, 5, 5, -236.48, 23.86, 6e-05, 6, -251.66, 6.86, 0.0002, 11, -22.89, 29.24, 0.91053, 10, -39.11, 94.3, 0.01305, 2, 10.56, 123.76, 0.07616, 1, 11, 18.15, 38.36, 1, 1, 11, 90.31, 37.69, 1, 1, 11, 116.81, -11.61, 1, 1, 11, 122.79, -38.54, 1, 1, 11, 88.39, -17.25, 1, 2, 11, 82.19, -34.78, 0.5221, 10, 79.03, 59.9, 0.4779, 1, 10, 91.86, 33.99, 1, 2, 11, 106.13, -81.95, 0.02144, 10, 114.45, 20.61, 0.97856, 1, 10, 138.58, -43.88, 1, 1, 10, 132.35, -87.19, 1, 1, 10, 103.72, -49.53, 1, 2, 10, 89.01, -63.91, 0.48219, 9, 96.79, 48.38, 0.51781, 2, 10, 89.28, -99.88, 0.03982, 9, 113.87, 16.73, 0.96018, 1, 9, 142.55, 8.24, 1, 1, 9, 168, -52.23, 1, 1, 9, 170.08, -93.49, 1, 1, 9, 159.31, -109.2, 1, 1, 9, 104.01, -41.01, 1, 1, 9, 3.38, -43.7, 1, 1, 4, -2.51, 53.6, 1, 1, 4, 94.77, 47.96, 1, 1, 4, 154.96, 111.53, 1, 1, 4, 161.19, 102.03, 1, 1, 9, 26.02, 42.74, 1, 1, 10, 77.98, -24.45, 1, 2, 10, 68.44, -48.73, 0.57661, 9, 71.51, 52.17, 0.42339, 1, 9, 79.02, 27.05, 1, 1, 10, 1.82, -6.5, 1, 1, 11, 69.5, -14.04, 1, 2, 11, 65.37, -31.78, 0.5025, 10, 62.02, 58.41, 0.4975, 1, 10, 66.12, 43.5, 1, 1, 10, 25.93, 55.08, 1, 1, 10, -8.54, 13.29, 1, 1, 11, 11.5, -19.53, 1, 5, 5, -214.85, 51.63, 6e-05, 6, -240.26, 40.17, 0.00017, 11, -21.13, -5.92, 0.78731, 10, -28.24, 60.82, 0.12272, 2, 45.15, 117.23, 0.08974, 1, 6, -18.97, -7.03, 1, 1, 6, 23.56, 21.35, 1, 1, 6, 81.58, 17.93, 1, 1, 5, 47.07, -50.07, 1, 1, 5, 76.89, -39.81, 1, 2, 5, 75.1, -54.22, 0.48623, 6, 68.35, 34.7, 0.51377, 1, 5, 10.67, 8.67, 1, 1, 5, 64.31, 24.49, 1, 1, 4, 13.56, -35.85, 1, 1, 4, 50.97, -27.11, 1, 2, 4, 40.4, -44.88, 0.50067, 5, 62.52, 43.31, 0.49933, 1, 4, -15.19, -10.7, 1, 1, 5, 16.92, -25.65, 1, 3, 10, -13.67, -41.65, 0.28246, 2, 142.05, 80.86, 0.0306, 9, -4.36, 19.98, 0.68694], "hull": 40, "edges": [68, 70, 68, 66, 64, 66, 64, 62, 62, 60, 48, 50, 50, 52, 52, 54, 54, 56, 60, 58, 56, 58, 82, 84, 80, 86, 86, 58, 84, 86, 82, 88, 48, 46, 46, 44, 44, 42, 42, 40, 38, 40, 38, 36, 36, 34, 90, 92, 92, 94, 94, 96, 96, 98, 98, 88, 90, 100, 100, 102, 32, 34, 102, 32, 104, 106, 106, 108, 30, 32, 30, 28, 28, 26, 26, 24, 22, 24, 18, 20, 20, 22, 12, 14, 14, 16, 18, 16, 110, 112, 112, 114, 114, 108, 116, 118, 120, 122, 122, 4, 122, 124, 124, 118, 4, 6, 6, 8, 118, 8, 8, 10, 10, 12, 4, 2, 78, 0, 0, 2, 76, 78, 76, 74, 74, 72, 70, 72, 120, 126, 126, 116, 110, 128, 128, 104, 102, 98, 80, 130, 88, 130], "width": 318, "height": 219}}, "Tentacle3": {"Tentacle3": {"name": "images/SpawnerTentacle_2", "type": "mesh", "uvs": [1, 0.0925, 1, 0.20912, 1, 0.31861, 0.99999, 0.43761, 1, 0.55423, 1, 0.66609, 1, 0.8327, 1, 1, 0.07444, 0.99286, 0.07037, 0.83331, 0.04214, 0.66856, 0.03731, 0.55494, 0.03523, 0.44753, 0.03416, 0.34065, 0.02587, 0.2078, 0, 0.09964, 0, 0, 1, 0], "triangles": [9, 10, 5, 6, 9, 5, 8, 9, 6, 8, 6, 7, 12, 3, 4, 10, 11, 5, 11, 12, 4, 11, 4, 5, 16, 17, 0, 15, 16, 0, 14, 15, 0, 14, 0, 1, 14, 1, 2, 12, 13, 3, 3, 13, 2, 13, 14, 2], "vertices": [2, 41, -21.71, -14.97, 0.38933, 2, 24.05, 65.07, 0.61067, 3, 41, 5.38, -17.14, 0.68231, 2, -3.12, 65.56, 0.27733, 42, -53.1, -16.75, 0.04036, 3, 41, 30.82, -19.17, 0.70631, 2, -28.64, 66.01, 0.112, 42, -27.64, -18.44, 0.18169, 3, 41, 58.46, -21.38, 0.57031, 42, 0.03, -20.29, 0.42859, 43, -53.57, -21.18, 0.0011, 3, 41, 85.56, -23.55, 0.27733, 42, 27.15, -22.1, 0.56538, 43, -26.42, -22.54, 0.15729, 3, 41, 111.54, -25.63, 0.08533, 42, 53.16, -23.83, 0.49871, 43, -0.38, -23.84, 0.41595, 2, 42, 91.9, -26.41, 0.25181, 43, 38.4, -25.78, 0.74819, 2, 42, 130.81, -29.01, 0.07467, 43, 77.34, -27.73, 0.92533, 2, 42, 132.06, 14.73, 0.06933, 43, 77.86, 16.03, 0.93067, 3, 41, 153.89, 15.04, 0.00068, 42, 94.97, 17.4, 0.21846, 43, 40.73, 18.08, 0.78086, 3, 41, 115.73, 19.43, 0.08754, 42, 56.75, 21.28, 0.46446, 43, 2.45, 21.33, 0.448, 3, 41, 89.35, 21.77, 0.26211, 42, 30.34, 23.27, 0.5539, 43, -23.98, 22.88, 0.184, 3, 41, 64.4, 23.86, 0.5201, 42, 5.37, 25.03, 0.47944, 43, -48.98, 24.22, 0.00046, 3, 41, 39.58, 25.9, 0.68656, 2, -34.59, 20.48, 0.08, 42, -19.48, 26.74, 0.23344, 3, 41, 8.74, 28.76, 0.67467, 2, -3.64, 19.54, 0.25067, 42, -50.35, 29.19, 0.07467, 2, 41, -16.29, 31.99, 0.416, 2, 21.54, 17.87, 0.584, 2, 41, -39.44, 33.84, 0.16267, 2, 44.76, 17.45, 0.83733, 2, 41, -43.2, -13.25, 0.168, 2, 45.61, 64.69, 0.832], "hull": 18, "edges": [32, 34, 14, 16, 30, 32, 30, 28, 6, 8, 8, 10, 20, 22, 22, 24, 16, 18, 18, 20, 10, 12, 12, 14, 6, 4, 24, 26, 26, 28, 4, 2, 2, 0, 0, 34], "width": 31, "height": 150}}, "Tentacle4": {"Tentacle4": {"name": "images/SpawnerTentacle_3", "type": "mesh", "uvs": [1, 0.0925, 1, 0.20912, 1, 0.31861, 0.99999, 0.43761, 1, 0.55423, 1, 0.66609, 1, 0.8327, 1, 1, 0.07444, 0.99286, 0, 0.83331, 0, 0.66856, 0, 0.54066, 0, 0.44991, 0, 0.33589, 0, 0.21018, 0, 0.09964, 0, 0, 1, 0], "triangles": [10, 5, 6, 8, 6, 7, 8, 9, 6, 9, 10, 6, 15, 16, 0, 16, 17, 0, 15, 0, 1, 12, 13, 3, 3, 13, 2, 13, 14, 2, 14, 1, 2, 14, 15, 1, 5, 10, 4, 10, 11, 4, 11, 3, 4, 11, 12, 3], "vertices": [3, 45, -84.72, -22.87, 0.00025, 44, -26.32, -22.84, 0.39441, 2, 23.47, -60.55, 0.60533, 3, 45, -57.54, -22.99, 0.08293, 44, 0.86, -23.33, 0.64507, 2, -3.71, -60.07, 0.272, 3, 45, -32.03, -23.11, 0.25093, 44, 26.37, -23.78, 0.63707, 2, -29.22, -59.61, 0.112, 3, 45, -4.29, -23.23, 0.43478, 44, 54.1, -24.27, 0.49048, 46, -57.84, -24.2, 0.07474, 3, 45, 22.89, -23.36, 0.50944, 44, 81.28, -24.76, 0.23983, 46, -30.66, -23.87, 0.25074, 3, 45, 48.96, -23.48, 0.45877, 44, 107.35, -25.22, 0.07449, 46, -4.59, -23.55, 0.46674, 2, 45, 87.79, -23.65, 0.27467, 46, 34.23, -23.09, 0.72533, 2, 45, 126.78, -23.83, 0.11733, 46, 73.22, -22.61, 0.88267, 2, 45, 125.31, 19.91, 0.09632, 46, 71.03, 21.09, 0.90368, 2, 45, 88.14, 23.59, 0.26432, 46, 33.81, 24.16, 0.73568, 3, 45, 49.75, 23.76, 0.45106, 44, 108.76, 22, 0.07726, 46, -4.59, 23.69, 0.47168, 3, 45, 19.94, 23.9, 0.53237, 44, 78.96, 22.53, 0.23296, 46, -34.39, 23.33, 0.23467, 3, 45, -1.21, 23.99, 0.4337, 44, 57.81, 22.91, 0.49696, 46, -55.54, 23.07, 0.06933, 3, 45, -27.78, 24.11, 0.24697, 44, 31.24, 23.38, 0.6677, 2, -32.4, -12.31, 0.08533, 3, 45, -57.08, 24.25, 0.06933, 44, 1.95, 23.9, 0.67467, 2, -3.11, -12.83, 0.256, 2, 44, -23.81, 24.36, 0.41067, 2, 22.65, -13.29, 0.58933, 2, 44, -47.03, 24.78, 0.16267, 2, 45.87, -13.71, 0.83733, 2, 44, -47.87, -22.46, 0.17333, 2, 45.03, -60.94, 0.82667], "hull": 18, "edges": [32, 34, 14, 16, 30, 32, 30, 28, 6, 8, 8, 10, 20, 22, 22, 24, 16, 18, 18, 20, 10, 12, 12, 14, 6, 4, 24, 26, 26, 28, 4, 2, 2, 0, 0, 34], "width": 30, "height": 151}}, "Tentacle5": {"Tentacle5": {"name": "images/SpawnerTentacle_1", "type": "mesh", "uvs": [1, 0.0925, 1, 0.20912, 1, 0.31861, 0.99999, 0.43761, 1, 0.55423, 1, 0.66609, 1, 0.8327, 1, 1, 0.21534, 1, 0.17604, 0.83569, 0.13607, 0.66856, 0.10776, 0.55018, 0.08093, 0.43801, 0.05764, 0.34065, 0.02587, 0.2078, 0, 0.09964, 0, 0, 1, 0], "triangles": [9, 10, 5, 6, 9, 5, 8, 9, 6, 8, 6, 7, 11, 12, 3, 11, 3, 4, 10, 11, 4, 5, 10, 4, 15, 16, 0, 16, 17, 0, 14, 15, 0, 14, 0, 1, 14, 1, 2, 13, 14, 2, 3, 13, 2, 12, 13, 3], "vertices": [2, 18, -22.79, 25.72, 0.37867, 2, 25.15, 64.8, 0.62133, 3, 18, 4.3, 23.55, 0.61067, 2, -2.02, 65.28, 0.288, 19, -54.73, 23.93, 0.10133, 3, 18, 29.73, 21.52, 0.624, 2, -27.54, 65.74, 0.112, 19, -29.27, 22.23, 0.264, 3, 18, 57.38, 19.31, 0.47838, 19, -1.59, 20.38, 0.52161, 20, -55.86, 19.46, 2e-05, 3, 18, 84.47, 17.14, 0.24638, 19, 25.53, 18.58, 0.58078, 20, -28.72, 18.1, 0.17284, 3, 18, 110.46, 15.06, 0.07571, 19, 51.54, 16.84, 0.49811, 20, -2.68, 16.8, 0.42618, 2, 19, 90.28, 14.26, 0.24051, 20, 36.1, 14.86, 0.75949, 2, 19, 129.19, 11.67, 0.08, 20, 75.04, 12.91, 0.92, 2, 19, 126.72, -25.32, 0.07467, 20, 73.19, -24.11, 0.92533, 2, 19, 88.39, -24.63, 0.2542, 20, 34.85, -24.05, 0.7458, 3, 18, 107.78, -25.67, 0.096, 19, 49.4, -23.92, 0.49087, 20, -4.14, -23.99, 0.41313, 3, 18, 80.17, -24.8, 0.256, 19, 21.78, -23.42, 0.58954, 20, -31.77, -23.95, 0.15446, 3, 18, 54.01, -23.98, 0.49452, 19, -4.39, -22.94, 0.50481, 20, -57.94, -23.91, 0.00067, 3, 18, 31.31, -23.27, 0.62454, 2, -31.88, 110.34, 0.10667, 19, -27.1, -22.53, 0.2688, 3, 18, 0.32, -22.3, 0.6192, 2, -0.89, 111.29, 0.28533, 19, -58.1, -21.97, 0.09547, 3, 18, -24.9, -21.5, 0.38068, 2, 24.33, 112.06, 0.61867, 19, -83.33, -21.51, 0.00066, 2, 18, -48.05, -19.65, 0.15467, 2, 47.55, 111.65, 0.84533, 2, 18, -44.28, 27.44, 0.15733, 2, 46.71, 64.41, 0.84267], "hull": 18, "edges": [32, 34, 14, 16, 30, 32, 30, 28, 6, 8, 8, 10, 20, 22, 22, 24, 16, 18, 18, 20, 10, 12, 12, 14, 6, 4, 24, 26, 26, 28, 4, 2, 2, 0, 0, 34], "width": 30, "height": 148}}, "Tentacle6": {"Tentacle6": {"name": "images/SpawnerTentacle_2", "type": "mesh", "uvs": [1, 0.0925, 1, 0.20912, 1, 0.31861, 0.99999, 0.43761, 1, 0.55423, 1, 0.66609, 1, 0.8327, 1, 1, 0.07444, 0.99286, 0.07037, 0.83331, 0.04214, 0.66856, 0.03731, 0.55494, 0.03523, 0.44753, 0.03416, 0.34065, 0.02587, 0.2078, 0, 0.09964, 0, 0, 1, 0], "triangles": [11, 4, 5, 11, 12, 4, 10, 11, 5, 12, 3, 4, 9, 10, 5, 15, 16, 0, 16, 17, 0, 13, 14, 2, 3, 13, 2, 12, 13, 3, 14, 1, 2, 14, 0, 1, 14, 15, 0, 8, 6, 7, 8, 9, 6, 6, 9, 5], "vertices": [3, 23, -142.93, -23.5, 0.00595, 21, -30.93, -21.03, 0.41271, 2, 26.62, -101.66, 0.58133, 4, 23, -115.75, -23.17, 0.00595, 21, -3.75, -21.51, 0.66071, 2, -0.56, -101.18, 0.248, 22, -62.18, -21.24, 0.08533, 4, 23, -90.23, -22.86, 0.00595, 21, 21.76, -21.97, 0.66871, 2, -26.07, -100.72, 0.08533, 22, -36.66, -21.36, 0.24, 2, 21, 49.49, -22.46, 0.42667, 22, -8.93, -21.48, 0.57333, 3, 23, -35.32, -22.19, 0.17333, 21, 76.67, -22.94, 0.17867, 22, 18.25, -21.61, 0.648, 2, 23, -9.26, -21.88, 0.37867, 22, 44.32, -21.72, 0.62133, 2, 23, 29.57, -21.41, 0.712, 22, 83.15, -21.9, 0.288, 2, 23, 68.56, -20.94, 0.872, 22, 122.14, -22.07, 0.128, 2, 23, 66.37, 22.76, 0.888, 22, 120.67, 21.66, 0.112, 2, 23, 29.18, 22.51, 0.70133, 22, 83.49, 22.02, 0.29867, 2, 23, -9.23, 23.38, 0.368, 22, 45.1, 23.53, 0.632, 3, 23, -35.71, 23.28, 0.14667, 21, 77.64, 22.53, 0.17867, 22, 18.62, 23.87, 0.67467, 2, 21, 52.61, 23.07, 0.44267, 22, -6.41, 24.08, 0.55733, 3, 21, 27.71, 23.56, 0.688, 2, -30.39, -55.01, 0.088, 22, -31.32, 24.25, 0.224, 3, 21, -3.24, 24.51, 0.68, 2, 0.57, -55.17, 0.25067, 22, -62.28, 24.78, 0.06933, 2, 21, -28.42, 26.18, 0.416, 2, 25.8, -54.4, 0.584, 2, 21, -51.64, 26.59, 0.17067, 2, 49.02, -54.82, 0.82933, 2, 21, -52.48, -20.65, 0.17067, 2, 48.17, -102.05, 0.82933], "hull": 18, "edges": [32, 34, 14, 16, 30, 32, 30, 28, 6, 8, 8, 10, 20, 22, 22, 24, 16, 18, 18, 20, 10, 12, 12, 14, 6, 4, 24, 26, 26, 28, 4, 2, 2, 0, 0, 34], "width": 31, "height": 150}}}}, {"name": "SpawnerExplosive", "bones": ["spawnerConstraint"], "transform": ["spawnerConstraint"], "attachments": {"Drips2": {"Drips2": {"name": "images/Drips", "type": "<PERSON><PERSON><PERSON>", "skin": "Spawner", "parent": "Drips2", "width": 175, "height": 138}}, "Eyes": {"Eyes": {"name": "images/SpawnerEyesExplosive", "x": 38.13, "y": -3.61, "scaleX": 1.5461, "scaleY": 1.5461, "rotation": -91.02, "width": 181, "height": 146}, "EyesCharge": {"name": "images/SpawnerEyesExplosive", "x": 38.13, "y": -3.61, "scaleX": 1.5461, "scaleY": 1.5461, "rotation": -91.02, "width": 181, "height": 146}, "EyesCharge2": {"name": "images/SpawnerEyesExplosive", "x": 38.13, "y": -3.61, "scaleX": 1.5461, "scaleY": 1.5461, "rotation": -91.02, "width": 181, "height": 146}, "EyesClosed": {"name": "images/SpawnerEyesShutExplosive", "x": 11.63, "y": -0.88, "scaleX": 1.5461, "scaleY": 1.5461, "rotation": -91.02, "width": 180, "height": 112}}, "Head": {"Head": {"name": "images/SpawnerHead_Explosive", "type": "<PERSON><PERSON><PERSON>", "skin": "Spawner", "parent": "Head", "width": 234, "height": 204}}, "images/Drips": {"Drips": {"name": "images/Drips", "type": "<PERSON><PERSON><PERSON>", "skin": "Spawner", "parent": "<PERSON><PERSON>s", "width": 175, "height": 138}}, "Mouth": {"Mouth Open": {"name": "images/ChaserBossMouth", "type": "<PERSON><PERSON><PERSON>", "skin": "Spawner", "parent": "Mouth Open", "width": 133, "height": 145}, "Mouth Closed": {"name": "images/ChaserBossMouthShut", "x": -40.53, "y": 0.18, "rotation": -91.02, "width": 105, "height": 131}}, "MouthBack": {"Mouth Back": {"name": "images/ChaserBossMouthBack", "type": "<PERSON><PERSON><PERSON>", "skin": "Spawner", "parent": "Mouth Back", "width": 97, "height": 105}}, "Spikes": {"Spikes": {"name": "images/SpawnerSpikes", "type": "<PERSON><PERSON><PERSON>", "skin": "Spawner", "parent": "Spikes", "width": 318, "height": 219}}, "Tentacle3": {"Tentacle3": {"name": "images/SpawnerTentacle_2", "type": "<PERSON><PERSON><PERSON>", "skin": "Spawner", "parent": "Tentacle3", "width": 31, "height": 150}}, "Tentacle4": {"Tentacle4": {"name": "images/SpawnerTentacle_3", "type": "<PERSON><PERSON><PERSON>", "skin": "Spawner", "parent": "Tentacle4", "width": 30, "height": 151}}, "Tentacle5": {"Tentacle5": {"name": "images/SpawnerTentacle_1", "type": "<PERSON><PERSON><PERSON>", "skin": "Spawner", "parent": "Tentacle5", "width": 30, "height": 148}}, "Tentacle6": {"Tentacle6": {"name": "images/SpawnerTentacle_2", "type": "<PERSON><PERSON><PERSON>", "skin": "Spawner", "parent": "Tentacle6", "width": 31, "height": 150}}}}, {"name": "Spiker", "bones": ["SpikerSpike2", "SpikerSpike6", "SpikerSpike8", "SpikerSpike4", "SpikerSpike3", "SpikerSpike5", "SpikerSpike7", "SpikerSpike1"], "attachments": {"Drips2": {"Drips2": {"name": "images/Drips", "type": "<PERSON><PERSON><PERSON>", "skin": "Chaser", "parent": "Drips2", "width": 175, "height": 138}}, "Eyes": {"Eyes": {"name": "images/<PERSON>rB<PERSON>_Eyes", "x": 7.96, "y": 0.41, "rotation": -91.02, "width": 236, "height": 129}, "EyesCharge": {"name": "images/SpikerBoss_EyesCharge", "x": 0.92, "y": 0.54, "rotation": -91.02, "width": 236, "height": 113}, "EyesCharge2": {"name": "images/SpikerBoss_EyesCharge2", "x": 0.92, "y": 0.54, "rotation": -91.02, "width": 236, "height": 113}, "EyesClosed": {"name": "images/SpikerB<PERSON>_EyesClosed", "x": 7.96, "y": 0.42, "rotation": -91.02, "width": 213, "height": 114}}, "Head": {"Head": {"name": "images/SpikerBoss_Body1", "type": "mesh", "uvs": [0.61448, 0.02328, 0.74439, 0.08196, 0.88532, 0.11273, 0.92521, 0.22575, 0.99975, 0.30829, 1, 0.42544, 0.99998, 0.6543, 0.99002, 0.84688, 0.9746, 0.98612, 0.83455, 0.9953, 0.65806, 0.99543, 0.51891, 0.99538, 0.3717, 0.99551, 0.21127, 0.99547, 0.10462, 0.98713, 0.02076, 0.84888, 0.00039, 0.66621, 0.01325, 0.43789, 0.0298, 0.24103, 0.10718, 0.1884, 0.18409, 0.04851, 0.37698, 0.03398, 0.43884, 0, 0.54342, 0.01278, 0.35195, 0.21472, 0.32594, 0.38474, 0.30606, 0.55849, 0.30912, 0.66685, 0.15003, 0.82379, 0.14697, 0.67433, 0.17603, 0.45013, 0.70378, 0.66685, 0.70531, 0.54541, 0.69307, 0.381, 0.65483, 0.1811, 0.51104, 0.8705, 0.50033, 0.47629, 0.4988, 0.29319, 0.49727, 0.15868, 0.50621, 0.78837, 0.32423, 0.82613, 0.69794, 0.82991, 0.50281, 0.60162], "triangles": [36, 37, 33, 25, 37, 36, 36, 33, 32, 26, 25, 36, 42, 36, 32, 26, 36, 42, 27, 26, 42, 31, 42, 32, 29, 26, 27, 39, 42, 31, 27, 42, 39, 40, 27, 39, 28, 27, 40, 41, 39, 31, 41, 31, 7, 35, 39, 41, 40, 39, 35, 10, 11, 35, 41, 10, 35, 10, 41, 9, 12, 40, 35, 12, 35, 11, 13, 40, 12, 38, 22, 23, 21, 22, 38, 34, 0, 1, 24, 20, 21, 24, 21, 38, 19, 20, 24, 38, 34, 37, 24, 38, 37, 0, 38, 23, 34, 38, 0, 2, 33, 34, 37, 34, 33, 25, 19, 24, 25, 24, 37, 30, 19, 25, 18, 19, 30, 17, 18, 30, 5, 32, 33, 1, 2, 34, 3, 33, 2, 3, 5, 33, 4, 5, 3, 26, 30, 25, 6, 32, 5, 31, 32, 6, 29, 17, 30, 29, 30, 26, 16, 17, 29, 28, 29, 27, 7, 31, 6, 15, 16, 29, 15, 29, 28, 7, 9, 41, 14, 15, 28, 8, 9, 7, 13, 28, 40, 14, 28, 13], "vertices": [1, 2, 278.66, -42.54, 1, 1, 2, 259.56, -90.58, 1, 1, 2, 248.9, -142.33, 1, 1, 2, 213.55, -156.85, 1, 1, 2, 187.19, -183.59, 1, 1, 2, 150.84, -183.41, 1, 1, 2, 79.62, -182.25, 1, 1, 2, 19.7, -177.4, 1, 1, 2, -23.57, -170.85, 1, 2, 2, -25.46, -118.86, 0.792, 27, -107.5, -118.96, 0.208, 2, 2, -24.25, -53.47, 0.184, 27, -106.29, -53.57, 0.816, 2, 2, -23.31, -1.76, 0.072, 27, -105.36, -1.85, 0.928, 2, 2, -25.98, 45.86, 0.216, 27, -108.03, 45.77, 0.784, 2, 2, -21.32, 112.57, 0.632, 27, -103.36, 112.48, 0.368, 1, 2, -18.02, 152.16, 1, 1, 2, 25.58, 182.56, 1, 1, 2, 82.56, 189.21, 1, 1, 2, 153.57, 183.15, 1, 1, 2, 214.7, 176.05, 1, 1, 2, 230.57, 147.04, 1, 1, 2, 273.62, 117.67, 1, 1, 2, 276.96, 45.61, 1, 1, 2, 287.06, 22.63, 1, 1, 2, 282.39, -16.17, 1, 2, 2, 220.84, 55.96, 0.808, 27, 138.8, 55.87, 0.192, 2, 2, 168.1, 66.52, 0.472, 27, 86.05, 66.43, 0.528, 2, 2, 114.14, 74.85, 0.232, 27, 32.1, 74.75, 0.768, 2, 2, 80.39, 74.31, 0.2, 27, -1.65, 74.21, 0.8, 2, 2, 32.54, 134.37, 0.728, 27, -49.5, 134.28, 0.272, 2, 2, 79.1, 134.69, 0.808, 27, -2.95, 134.59, 0.192, 2, 2, 148.71, 122.64, 0.808, 27, 66.67, 122.55, 0.192, 2, 2, 77.77, -72.35, 0.248, 27, -4.28, -72.44, 0.752, 2, 2, 115.57, -73.59, 0.264, 27, 33.52, -73.68, 0.736, 2, 2, 166.82, -69.93, 0.472, 27, 84.78, -70.02, 0.528, 2, 2, 229.3, -56.78, 0.808, 27, 147.26, -56.87, 0.192, 1, 27, -66.39, 0.28, 1, 2, 2, 138.46, 2.17, 0.04, 27, 56.42, 2.08, 0.96, 2, 2, 195.46, 1.77, 0.392, 27, 113.42, 1.67, 0.608, 2, 2, 237.32, 1.65, 0.808, 27, 155.28, 1.55, 0.192, 1, 27, -40.78, 1.62, 1, 2, 2, 30.68, 69.6, 0.376, 27, -51.36, 69.51, 0.624, 2, 2, 27.04, -69.28, 0.136, 27, -55, -69.38, 0.864, 1, 27, 17.39, 1.85, 1], "hull": 24, "edges": [40, 42, 42, 44, 44, 46, 46, 0, 0, 2, 2, 4, 4, 6, 8, 10, 14, 16, 16, 18, 30, 32, 30, 28, 42, 48, 48, 50, 50, 52, 52, 54, 24, 26, 26, 28, 26, 56, 56, 58, 58, 60, 32, 34, 12, 14, 18, 20, 62, 64, 64, 66, 66, 68, 68, 0, 34, 36, 10, 12, 20, 22, 22, 24, 22, 70, 72, 74, 74, 76, 70, 78, 24, 80, 80, 54, 20, 82, 82, 62, 72, 84, 84, 78, 6, 8, 38, 40, 36, 38], "width": 374, "height": 315}}, "images/Drips": {"Drips": {"name": "images/Drips", "type": "mesh", "uvs": [1, 0.1099, 0.99149, 0.47829, 0.98808, 0.61203, 1, 0.79051, 1, 0.88399, 0.90036, 0.8709, 0.9074, 0.76218, 0.9151, 0.60355, 0.91216, 0.4596, 0.88562, 0.36799, 0.83844, 0.34929, 0.79421, 0.43716, 0.79274, 0.54747, 0.79716, 0.62599, 0.71018, 0.63347, 0.70769, 0.54718, 0.69986, 0.45025, 0.67332, 0.39977, 0.64826, 0.43342, 0.62909, 0.57364, 0.64679, 0.70638, 0.64089, 0.77929, 0.59076, 0.80173, 0.54211, 0.73629, 0.55652, 0.56371, 0.54948, 0.42968, 0.50525, 0.36051, 0.45172, 0.42105, 0.44242, 0.60742, 0.45251, 0.77455, 0.48121, 0.91706, 0.48756, 1, 0.34456, 1, 0.35716, 0.91519, 0.37168, 0.76276, 0.37201, 0.61011, 0.35058, 0.42229, 0.31065, 0.35111, 0.29181, 0.42748, 0.30475, 0.60537, 0.31212, 0.72877, 0.20519, 0.73437, 0.20666, 0.59574, 0.2214, 0.42906, 0.17649, 0.26511, 0.12194, 0.25046, 0.09402, 0.40566, 0.09348, 0.5572, 0.10977, 0.71712, 0.1239, 0.85594, 0, 0.86086, 0, 0.72508, 0.01173, 0.55255, 0.01127, 0.4213, 0.0112, 0.40055, 0, 0.2081, 0.0343, 0.08471, 0.18173, 0, 0.87144, 0, 1, 0], "triangles": [29, 30, 33, 5, 3, 4, 50, 48, 49, 32, 33, 31, 31, 33, 30, 33, 34, 29, 29, 34, 28, 34, 35, 28, 5, 6, 3, 50, 51, 48, 6, 2, 3, 20, 22, 23, 6, 7, 2, 20, 24, 19, 20, 23, 24, 41, 39, 40, 41, 42, 39, 39, 42, 38, 42, 43, 38, 51, 47, 48, 51, 52, 47, 14, 12, 13, 14, 15, 12, 2, 7, 1, 27, 28, 35, 27, 35, 36, 7, 8, 1, 19, 24, 18, 24, 25, 18, 46, 47, 53, 47, 52, 53, 11, 12, 16, 12, 15, 16, 0, 1, 9, 1, 8, 9, 16, 17, 11, 11, 17, 10, 18, 25, 17, 25, 26, 17, 38, 43, 37, 37, 43, 44, 36, 37, 27, 53, 54, 46, 27, 37, 26, 46, 54, 45, 54, 55, 45, 10, 17, 58, 9, 10, 0, 58, 17, 26, 26, 57, 58, 37, 44, 57, 10, 58, 0, 57, 26, 37, 44, 45, 57, 55, 56, 45, 45, 56, 57, 58, 59, 0, 21, 22, 20], "vertices": [1, 1, 105.49, 30.28, 1, 2, 1, 103.56, -26.96, 0.76, 28, 103.68, 78.74, 0.24, 2, 1, 102.79, -47.74, 0.76, 28, 102.91, 57.96, 0.24, 2, 1, 105.49, -75.47, 0.6, 28, 105.62, 30.23, 0.4, 2, 1, 105.49, -90, 0.424, 28, 105.62, 15.7, 0.576, 2, 1, 82.86, -87.96, 0.424, 28, 82.99, 17.73, 0.576, 2, 1, 84.46, -71.07, 0.6, 28, 84.58, 34.63, 0.4, 2, 1, 86.21, -46.42, 0.76, 28, 86.33, 59.28, 0.24, 2, 1, 85.54, -24.05, 0.76, 28, 85.66, 81.64, 0.24, 2, 1, 79.51, -9.82, 0.888, 28, 79.64, 95.88, 0.112, 2, 1, 68.8, -6.91, 0.888, 28, 68.92, 98.78, 0.112, 2, 1, 58.75, -20.57, 0.888, 28, 58.87, 85.13, 0.112, 2, 1, 58.41, -37.71, 0.76, 28, 58.54, 67.99, 0.24, 2, 1, 59.42, -49.91, 0.76, 28, 59.54, 55.79, 0.24, 2, 1, 39.66, -51.07, 0.76, 28, 39.79, 54.63, 0.24, 2, 1, 39.1, -37.66, 0.76, 28, 39.22, 68.04, 0.24, 2, 1, 37.32, -22.6, 0.888, 28, 37.44, 83.1, 0.112, 2, 1, 31.29, -14.76, 0.888, 28, 31.41, 90.94, 0.112, 2, 1, 25.6, -19.99, 0.888, 28, 25.72, 85.71, 0.112, 2, 1, 21.24, -41.77, 0.76, 28, 21.37, 63.92, 0.24, 2, 1, 25.26, -62.4, 0.6, 28, 25.39, 43.3, 0.4, 2, 1, 23.92, -73.73, 0.472, 28, 24.05, 31.97, 0.528, 2, 1, 12.54, -77.21, 0.472, 28, 12.66, 28.48, 0.528, 2, 1, 1.48, -67.05, 0.6, 28, 1.61, 38.65, 0.4, 2, 1, 4.76, -40.23, 0.76, 28, 4.88, 65.47, 0.24, 2, 1, 3.16, -19.41, 0.888, 28, 3.28, 86.29, 0.112, 2, 1, -6.89, -8.66, 0.888, 28, -6.76, 97.04, 0.112, 2, 1, -19.05, -18.06, 0.888, 28, -18.92, 87.63, 0.112, 2, 1, -21.16, -47.02, 0.76, 28, -21.04, 58.67, 0.24, 2, 1, -18.87, -72.99, 0.6, 28, -18.74, 32.7, 0.4, 2, 1, -12.35, -95.14, 0.424, 28, -12.23, 10.56, 0.576, 2, 1, -10.91, -108.02, 0.328, 28, -10.78, -2.33, 0.672, 2, 1, -43.39, -108.02, 0.328, 28, -43.27, -2.33, 0.672, 2, 1, -40.53, -94.85, 0.424, 28, -40.4, 10.85, 0.576, 2, 1, -37.23, -71.16, 0.6, 28, -37.1, 34.54, 0.4, 2, 1, -37.15, -47.44, 0.76, 28, -37.03, 58.26, 0.24, 2, 1, -42.02, -18.26, 0.888, 28, -41.9, 87.44, 0.112, 2, 1, -51.09, -7.2, 0.888, 28, -50.97, 98.5, 0.112, 2, 1, -55.37, -19.06, 0.888, 28, -55.25, 86.63, 0.112, 2, 1, -52.43, -46.71, 0.76, 28, -52.31, 58.99, 0.24, 2, 1, -50.76, -65.88, 0.6, 28, -50.63, 39.82, 0.4, 2, 1, -75.05, -66.75, 0.6, 28, -74.92, 38.95, 0.4, 2, 1, -74.71, -45.21, 0.76, 28, -74.59, 60.49, 0.24, 2, 1, -71.36, -19.31, 0.888, 28, -71.24, 86.39, 0.112, 1, 1, -81.57, 6.17, 1, 1, 1, -93.96, 8.44, 1, 2, 1, -100.3, -15.67, 0.888, 28, -100.18, 90.02, 0.112, 2, 1, -100.42, -39.22, 0.76, 28, -100.3, 66.48, 0.24, 2, 1, -96.72, -64.07, 0.6, 28, -96.6, 41.63, 0.4, 2, 1, -93.51, -85.64, 0.424, 28, -93.39, 20.06, 0.576, 2, 1, -121.66, -86.4, 0.424, 28, -121.53, 19.29, 0.576, 2, 1, -121.66, -65.3, 0.6, 28, -121.53, 40.39, 0.4, 2, 1, -118.99, -38.5, 0.76, 28, -118.87, 67.2, 0.24, 2, 1, -119.1, -18.1, 0.888, 28, -118.97, 87.59, 0.112, 2, 1, -119.11, -14.88, 0.888, 28, -118.99, 90.82, 0.112, 1, 1, -121.66, 15.03, 1, 1, 1, -113.87, 34.2, 1, 1, 1, -80.38, 47.36, 1, 1, 1, 76.29, 47.36, 1, 1, 1, 105.49, 47.36, 1], "hull": 60, "edges": [100, 98, 94, 92, 92, 90, 90, 88, 88, 86, 86, 84, 84, 82, 82, 80, 80, 78, 78, 76, 76, 74, 74, 72, 72, 70, 70, 68, 68, 66, 66, 64, 62, 64, 62, 60, 60, 58, 58, 56, 56, 54, 54, 52, 52, 50, 50, 48, 48, 46, 46, 44, 36, 38, 38, 40, 40, 42, 36, 34, 34, 32, 32, 30, 30, 28, 28, 26, 26, 24, 24, 22, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 100, 102, 98, 96, 96, 94, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 2, 0, 0, 118, 116, 0, 44, 42], "width": 175, "height": 138}}, "images/SpikerBossSpike1": {"images/SpikerBossSpike2": {"x": 104, "y": -2.6, "rotation": -90, "width": 74, "height": 246}}, "images/SpikerBossSpike2": {"images/SpikerBossSpike1": {"x": 104, "y": -2.6, "rotation": -90, "width": 74, "height": 246}}, "images/SpikerBossSpike3": {"images/SpikerBossSpike1": {"x": 104, "y": -2.6, "rotation": -90, "width": 74, "height": 246}}, "images/SpikerBossSpike4": {"images/SpikerBossSpike1": {"x": 104, "y": -2.6, "rotation": -90, "width": 74, "height": 246}}, "images/SpikerBossSpike5": {"images/SpikerBossSpike2": {"x": 104, "y": -2.6, "rotation": -90, "width": 74, "height": 246}}, "images/SpikerBossSpike6": {"images/SpikerBossSpike2": {"x": 104, "y": -2.6, "rotation": -90, "width": 74, "height": 246}}, "images/SpikerBossSpike7": {"images/SpikerBossSpike1": {"x": 104, "y": -2.6, "rotation": -90, "width": 74, "height": 246}}, "images/SpikerBossSpike8": {"images/SpikerBossSpike1": {"x": 104, "y": -2.6, "rotation": -90, "width": 74, "height": 246}}, "Mouth": {"Mouth Open": {"name": "images/SpikerB<PERSON>_Mouth", "type": "mesh", "uvs": [1, 0.34183, 1, 0.6657, 0.86335, 1, 0.78554, 1, 0.81449, 0.51829, 0.7403, 0.80329, 0.61545, 1, 0.56659, 0.91139, 0.64259, 0.44294, 0.60459, 0.43311, 0.54326, 0.74252, 0.51049, 0.7607, 0.41821, 0.38725, 0.38926, 0.41674, 0.45315, 0.91997, 0.42183, 1, 0.27526, 0.79674, 0.19564, 0.52484, 0.20968, 1, 0.11325, 1, 0, 0.64235, 0, 0.56743, 0, 0.442, 0.39465, 0, 0.62864, 0, 0.09431, 0.60674, 0.16126, 0.47243, 0.26802, 0.51501, 0.31145, 0.42001, 0.40735, 0.32829, 0.68602, 0.41018, 0.46345, 0.19725, 0.58468, 0.22674, 0.02629, 0.72248], "triangles": [19, 25, 18, 20, 33, 19, 15, 16, 14, 6, 7, 5, 11, 12, 10, 32, 12, 31, 12, 29, 31, 13, 29, 12, 29, 13, 28, 13, 27, 28, 8, 9, 32, 12, 32, 9, 30, 8, 32, 28, 26, 23, 0, 30, 24, 29, 23, 31, 29, 28, 23, 32, 31, 24, 30, 32, 24, 31, 23, 24, 5, 30, 4, 13, 16, 27, 10, 12, 9, 8, 30, 5, 14, 16, 13, 27, 26, 28, 5, 7, 8, 4, 30, 0, 17, 26, 27, 17, 25, 26, 4, 0, 1, 26, 25, 22, 20, 21, 25, 23, 26, 22, 25, 21, 22, 1, 2, 4, 17, 18, 25, 33, 20, 25, 33, 25, 19, 3, 4, 2, 17, 27, 16], "vertices": [1, 2, 15.11, -107.38, 1, 2, 2, -22.45, -106.71, 0.648, 36, -31.07, 60.97, 0.352, 2, 2, -60.71, -77.33, 0.536, 36, 15.45, 73.74, 0.464, 2, 2, -60.42, -60.99, 0.536, 36, 28.3, 63.64, 0.464, 2, 2, -4.66, -68.07, 0.264, 27, -87.57, -65.38, 0.736, 3, 2, -37.43, -51.9, 0.28519, 27, -120.35, -49.21, 0.19769, 35, 34.92, 70.31, 0.51712, 3, 2, -59.78, -25.28, 0.28519, 27, -142.7, -22.59, 0.19769, 35, 69.68, 70.3, 0.51712, 3, 2, -49.32, -15.2, 0.28519, 27, -132.23, -12.51, 0.19769, 35, 70.67, 55.81, 0.51712, 2, 2, 4.73, -32.13, 0.264, 27, -78.19, -29.44, 0.736, 2, 2, 6.01, -24.17, 0.264, 27, -76.91, -21.48, 0.736, 4, 2, -29.65, -10.66, 0.53088, 36, 49.85, 8.72, 0.02556, 27, -112.56, -7.97, 0.368, 35, 61.5, 37.82, 0.07556, 2, 2, -31.63, -3.74, 0.632, 27, -114.55, -1.05, 0.368, 2, 2, 12.03, 14.87, 0.264, 27, -70.89, 17.55, 0.736, 2, 2, 8.71, 21.01, 0.264, 27, -74.2, 23.69, 0.736, 3, 2, -49.89, 8.63, 0.28519, 27, -132.8, 11.32, 0.19769, 32, 68.91, -56.42, 0.51712, 4, 2, -59.05, 15.38, 0.28541, 27, -141.97, 18.06, 0.19784, 32, 69.34, -67.79, 0.51632, 33, 58.21, -36.31, 0.00043, 3, 2, -34.93, 45.73, 0.28519, 27, -117.84, 48.42, 0.19769, 32, 30.57, -67.8, 0.51712, 2, 2, -3.1, 61.88, 0.712, 27, -86.01, 64.57, 0.288, 2, 2, -58.26, 59.92, 0.312, 33, 21.14, -61.03, 0.688, 2, 2, -57.9, 80.17, 0.312, 33, 4.29, -72.26, 0.688, 2, 2, -15.99, 103.2, 0.584, 33, -38.51, -50.93, 0.416, 2, 2, -7.3, 103.05, 0.696, 33, -43.33, -43.7, 0.304, 1, 2, 7.24, 102.79, 1, 1, 27, -25.89, 21.7, 1, 1, 27, -26.76, -27.43, 1, 3, 2, -12.22, 83.33, 0.62918, 27, -95.13, 86.02, 0.06682, 33, -24.32, -36.51, 0.304, 2, 2, 3.11, 68.99, 0.616, 27, -79.8, 71.68, 0.384, 2, 2, -2.23, 46.67, 0.264, 27, -85.14, 49.35, 0.736, 2, 2, 8.63, 37.35, 0.264, 27, -74.29, 40.04, 0.736, 2, 2, 18.91, 17.02, 0.264, 27, -64.01, 19.71, 0.736, 2, 2, 8.36, -41.32, 0.264, 27, -74.55, -38.63, 0.736, 2, 2, 33.89, 4.97, 0.264, 27, -49.02, 7.66, 0.736, 2, 2, 30.02, -20.42, 0.264, 27, -52.9, -17.73, 0.736, 2, 2, -25.39, 97.85, 0.584, 33, -28.76, -55.6, 0.416], "hull": 25, "edges": [42, 50, 50, 52, 52, 34, 34, 54, 54, 56, 56, 26, 26, 24, 58, 24, 24, 22, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 4, 6, 4, 2, 16, 60, 60, 8, 58, 62, 62, 64, 64, 18, 34, 32, 32, 30, 26, 28, 28, 30, 34, 36, 40, 42, 40, 66, 36, 38, 66, 38, 22, 20, 20, 18, 38, 40, 42, 44, 44, 46, 46, 48, 2, 0, 48, 0], "width": 210, "height": 116}, "Mouth Closed": {"name": "images/SpikerB<PERSON>_Mouth", "type": "mesh", "uvs": [1, 0.34183, 1, 0.6657, 0.86335, 1, 0.78554, 1, 0.81449, 0.51829, 0.7403, 0.80329, 0.61545, 1, 0.56659, 0.91139, 0.64259, 0.44294, 0.60459, 0.43311, 0.54326, 0.74252, 0.51049, 0.7607, 0.41821, 0.38725, 0.38926, 0.41674, 0.45315, 0.91997, 0.42183, 1, 0.27526, 0.79674, 0.19564, 0.52484, 0.20968, 1, 0.11325, 1, 0, 0.64235, 0, 0.56743, 0, 0.442, 0.39465, 0, 0.62864, 0, 0.09431, 0.60674, 0.16126, 0.47243, 0.26802, 0.51501, 0.31145, 0.42001, 0.40735, 0.32829, 0.68602, 0.41018, 0.46345, 0.19725, 0.58468, 0.22674, 0.02629, 0.72248], "triangles": [19, 25, 18, 20, 33, 19, 15, 16, 14, 6, 7, 5, 11, 12, 10, 32, 12, 31, 12, 29, 31, 13, 29, 12, 29, 13, 28, 13, 27, 28, 8, 9, 32, 12, 32, 9, 30, 8, 32, 28, 26, 23, 0, 30, 24, 29, 23, 31, 29, 28, 23, 32, 31, 24, 30, 32, 24, 31, 23, 24, 5, 30, 4, 13, 16, 27, 10, 12, 9, 8, 30, 5, 14, 16, 13, 27, 26, 28, 5, 7, 8, 4, 30, 0, 17, 26, 27, 17, 25, 26, 4, 0, 1, 26, 25, 22, 20, 21, 25, 23, 26, 22, 25, 21, 22, 1, 2, 4, 17, 18, 25, 33, 20, 25, 33, 25, 19, 3, 4, 2, 17, 27, 16], "vertices": [1, 2, 15.11, -107.38, 1, 2, 2, -22.45, -106.71, 0.648, 36, -31.07, 60.97, 0.352, 2, 2, -60.71, -77.33, 0.536, 36, 15.45, 73.74, 0.464, 2, 2, -60.42, -60.99, 0.536, 36, 28.3, 63.64, 0.464, 2, 2, -4.66, -68.07, 0.264, 27, -87.57, -65.38, 0.736, 3, 2, -37.43, -51.9, 0.28519, 27, -120.35, -49.21, 0.19769, 35, 34.92, 70.31, 0.51712, 3, 2, -59.78, -25.28, 0.28519, 27, -142.7, -22.59, 0.19769, 35, 69.68, 70.3, 0.51712, 3, 2, -49.32, -15.2, 0.28519, 27, -132.23, -12.51, 0.19769, 35, 70.67, 55.81, 0.51712, 2, 2, 4.73, -32.13, 0.264, 27, -78.19, -29.44, 0.736, 2, 2, 6.01, -24.17, 0.264, 27, -76.91, -21.48, 0.736, 4, 2, -29.65, -10.66, 0.53088, 36, 49.85, 8.72, 0.02556, 27, -112.56, -7.97, 0.368, 35, 61.5, 37.82, 0.07556, 2, 2, -31.63, -3.74, 0.632, 27, -114.55, -1.05, 0.368, 2, 2, 12.03, 14.87, 0.264, 27, -70.89, 17.55, 0.736, 2, 2, 8.71, 21.01, 0.264, 27, -74.2, 23.69, 0.736, 3, 2, -49.89, 8.63, 0.28519, 27, -132.8, 11.32, 0.19769, 32, 68.91, -56.42, 0.51712, 4, 2, -59.05, 15.38, 0.28541, 27, -141.97, 18.06, 0.19784, 32, 69.34, -67.79, 0.51632, 33, 58.21, -36.31, 0.00043, 3, 2, -34.93, 45.73, 0.28519, 27, -117.84, 48.42, 0.19769, 32, 30.57, -67.8, 0.51712, 2, 2, -3.1, 61.88, 0.712, 27, -86.01, 64.57, 0.288, 2, 2, -58.26, 59.92, 0.312, 33, 21.14, -61.03, 0.688, 2, 2, -57.9, 80.17, 0.312, 33, 4.29, -72.26, 0.688, 2, 2, -15.99, 103.2, 0.584, 33, -38.51, -50.93, 0.416, 2, 2, -7.3, 103.05, 0.696, 33, -43.33, -43.7, 0.304, 1, 2, 7.24, 102.79, 1, 1, 27, -25.89, 21.7, 1, 1, 27, -26.76, -27.43, 1, 3, 2, -12.22, 83.33, 0.62918, 27, -95.13, 86.02, 0.06682, 33, -24.32, -36.51, 0.304, 2, 2, 3.11, 68.99, 0.616, 27, -79.8, 71.68, 0.384, 2, 2, -2.23, 46.67, 0.264, 27, -85.14, 49.35, 0.736, 2, 2, 8.63, 37.35, 0.264, 27, -74.29, 40.04, 0.736, 2, 2, 18.91, 17.02, 0.264, 27, -64.01, 19.71, 0.736, 2, 2, 8.36, -41.32, 0.264, 27, -74.55, -38.63, 0.736, 2, 2, 33.89, 4.97, 0.264, 27, -49.02, 7.66, 0.736, 2, 2, 30.02, -20.42, 0.264, 27, -52.9, -17.73, 0.736, 2, 2, -25.39, 97.85, 0.584, 33, -28.76, -55.6, 0.416], "hull": 25, "edges": [42, 50, 50, 52, 52, 34, 34, 54, 54, 56, 56, 26, 26, 24, 58, 24, 24, 22, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 4, 6, 4, 2, 16, 60, 60, 8, 58, 62, 62, 64, 64, 18, 34, 32, 32, 30, 26, 28, 28, 30, 34, 36, 40, 42, 40, 66, 36, 38, 66, 38, 22, 20, 20, 18, 38, 40, 42, 44, 44, 46, 46, 48, 2, 0, 48, 0], "width": 210, "height": 116}}, "Skirt": {"Skirt": {"name": "images/SpikerBossSkirt", "type": "mesh", "uvs": [0.99906, 0.40831, 1, 0.70402, 0.94119, 0.81182, 0.91696, 0.71722, 0.91122, 0.93282, 0.84745, 0.95482, 0.83087, 0.84482, 0.81748, 0.95922, 0.74159, 0.94821, 0.71983, 0.79842, 0.70389, 0.93702, 0.62992, 0.93262, 0.61015, 0.84022, 0.5923, 1, 0.54447, 1, 0.51672, 0.86482, 0.49696, 1, 0.4249, 1, 0.40189, 0.81411, 0.38915, 0.9505, 0.31008, 0.9483, 0.28585, 0.78109, 0.24631, 0.9659, 0.18916, 0.92152, 0.17768, 0.74992, 0.14452, 0.90392, 0.09095, 0.87752, 0.07055, 0.64872, 0.03357, 0.77192, 0, 0.62915, 0, 0.3091, 0.03095, 0, 0.11148, 0, 0.20568, 0, 0.29611, 0, 0.40916, 0.013, 0.50486, 0, 0.59906, 0, 0.71493, 0, 0.82326, 0, 0.89015, 0, 0.97008, 0, 0.18599, 0.45066, 0.08225, 0.41526, 0.28906, 0.48105, 0.40659, 0.4723, 0.51278, 0.50804, 0.60512, 0.50786, 0.71875, 0.49496, 0.82542, 0.48496, 0.90774, 0.47212], "triangles": [5, 6, 4, 2, 3, 1, 4, 6, 3, 3, 50, 1, 3, 6, 49, 25, 26, 24, 42, 27, 43, 11, 12, 9, 16, 17, 15, 17, 18, 15, 42, 32, 33, 13, 14, 12, 18, 46, 15, 21, 22, 24, 20, 21, 18, 22, 23, 24, 43, 32, 42, 14, 15, 12, 19, 20, 18, 43, 31, 32, 24, 27, 42, 42, 33, 44, 30, 31, 43, 46, 45, 36, 47, 48, 9, 46, 37, 47, 47, 38, 48, 47, 37, 38, 48, 38, 39, 44, 34, 45, 45, 34, 35, 50, 40, 41, 7, 8, 6, 45, 35, 36, 46, 36, 37, 48, 39, 49, 49, 39, 40, 50, 41, 0, 3, 49, 50, 50, 0, 1, 15, 47, 12, 21, 44, 18, 44, 24, 42, 46, 18, 45, 8, 9, 6, 10, 11, 9, 15, 46, 47, 12, 47, 9, 18, 44, 45, 9, 48, 49, 21, 24, 44, 44, 33, 34, 9, 49, 6, 49, 40, 50, 26, 27, 24, 27, 29, 43, 28, 29, 27, 29, 30, 43], "vertices": [3, 2, 2.67, -127.79, 0.5107, 21, -7.93, -47.99, 0.24998, 14, -35.17, 128.98, 0.23932, 3, 2, -22.8, -127.33, 0.32903, 21, 17.54, -48.44, 0.44697, 14, -11.52, 130.1, 0.224, 3, 2, -31.15, -112.18, 0.32903, 21, 26.43, -33.59, 0.44697, 14, -2.57, 115.5, 0.224, 3, 2, -23.48, -106.14, 0.32903, 21, 18.98, -27.28, 0.44697, 14, -9.56, 108.99, 0.224, 3, 2, -40.69, -104.36, 0.32903, 21, 36.25, -26.12, 0.44697, 14, 7.07, 108.3, 0.224, 3, 2, -42.16, -88.07, 0.37869, 21, 38.3, -9.89, 0.39731, 14, 9.45, 92.1, 0.224, 3, 2, -33.29, -84, 0.37869, 21, 29.57, -5.51, 0.39731, 14, 1.23, 87.49, 0.224, 3, 2, -42.38, -80.42, 0.37869, 21, 38.78, -2.25, 0.39731, 14, 10.12, 84.48, 0.224, 4, 2, -41.15, -61.14, 0.4512, 21, 38.25, 17.05, 0.26272, 14, 9.95, 65.07, 0.20608, 28, 53.95, 39.54, 0.08, 4, 2, -29.07, -55.8, 0.4512, 21, 26.36, 22.83, 0.26272, 14, -1.12, 59.01, 0.20608, 28, 50.35, 48.09, 0.08, 5, 2, -40.09, -51.53, 0.37179, 21, 37.52, 26.69, 0.21648, 14, 8.38, 54.59, 0.20608, 28, 43.4, 42, 0.08, 27, -122.13, -51.63, 0.12565, 5, 2, -39.4, -32.68, 0.49415, 21, 37.51, 45.56, 0.09412, 14, 8.51, 35.62, 0.20608, 28, 23.5, 46.72, 0.08, 27, -121.44, -32.78, 0.12565, 5, 2, -31.92, -27.77, 0.49415, 21, 30.21, 50.74, 0.09412, 14, 1.88, 30.29, 0.20608, 28, 20.31, 52.55, 0.08, 27, -113.96, -27.86, 0.12565, 5, 2, -44.62, -23, 0.52239, 21, 43.07, 55.05, 0.06589, 14, 13.72, 26.22, 0.20608, 28, 12.64, 45.47, 0.08, 27, -126.66, -23.1, 0.12565, 4, 2, -44.4, -10.81, 0.58827, 14, 13.91, 13.97, 0.20608, 28, -0.16, 49.09, 0.08, 27, -126.44, -10.9, 0.12565, 4, 2, -33.46, -3.91, 0.58827, 14, 4.31, 6.49, 0.20608, 28, -4.08, 57.8, 0.08, 27, -115.5, -4, 0.12565, 4, 2, -44.18, 1.31, 0.58827, 14, 14.31, 1.86, 0.20608, 28, -11.75, 51.77, 0.08, 27, -126.23, 1.22, 0.12565, 4, 2, -43.85, 19.69, 0.58827, 14, 14.91, -16.51, 0.20608, 28, -29.32, 55.84, 0.08, 27, -125.9, 19.6, 0.12565, 4, 2, -28.88, 25.32, 0.58827, 14, 1.58, -22.94, 0.20608, 28, -31.02, 66.95, 0.08, 27, -110.92, 25.22, 0.12565, 4, 2, -39.73, 28.74, 0.58827, 14, 11.6, -25.78, 0.20608, 28, -36.99, 60.47, 0.08, 27, -121.77, 28.65, 0.12565, 5, 2, -39.2, 48.89, 0.50356, 14, 12.65, -46.35, 0.20608, 28, -58.8, 64.89, 0.08, 27, -121.24, 48.79, 0.12565, 18, 42.42, 37.61, 0.08471, 5, 2, -25.71, 54.86, 0.50356, 14, 0.48, -52.89, 0.20608, 28, -60.48, 74.91, 0.08, 27, -107.75, 54.76, 0.12565, 18, 28.59, 32.49, 0.08471, 4, 2, -40.31, 65.16, 0.61112, 14, 15.41, -61.86, 0.20608, 28, -74.1, 68.01, 0.08, 18, 42.52, 21.3, 0.1028, 4, 2, -36.5, 79.72, 0.54367, 14, 12.62, -77.04, 0.22042, 28, -88.5, 74.84, 0.016, 18, 37.82, 7.01, 0.21991, 4, 2, -22.73, 82.42, 0.54367, 14, -0.19, -80.11, 0.22042, 28, -86.1, 83.86, 0.016, 18, 23.9, 5.17, 0.21991, 4, 2, -34.89, 91.08, 0.54367, 14, 11.36, -88.44, 0.22042, 28, -98.12, 79.01, 0.016, 18, 35.51, -4.23, 0.21991, 3, 2, -32.54, 104.71, 0.3911, 14, 9.21, -102.74, 0.224, 18, 32.31, -17.68, 0.3849, 3, 2, -14.14, 109.6, 0.47802, 14, -7.95, -107.63, 0.224, 18, 13.65, -21.43, 0.29798, 3, 2, -23.83, 119.19, 0.36627, 14, 0.83, -117.36, 0.224, 18, 22.72, -31.61, 0.40973, 3, 2, -12.26, 127.56, 0.3911, 14, -10.08, -125.74, 0.224, 18, 10.65, -39.24, 0.3849, 3, 2, 13.34, 127.12, 0.50285, 14, -33.42, -125.68, 0.224, 18, -14.87, -37.21, 0.27315, 3, 2, 37.93, 118.78, 0.66426, 14, -54.81, -118.59, 0.224, 18, -38.89, -27.37, 0.11174, 3, 2, 37.56, 98.25, 0.66426, 14, -55.19, -98.07, 0.224, 18, -37.25, -6.9, 0.11174, 3, 2, 65.49, 74.45, 0.66426, 14, -83.95, -75.81, 0.224, 18, -63.66, 18.59, 0.11174, 4, 2, 65.08, 51.42, 0.39855, 14, -86.59, -54.79, 0.224, 27, -16.96, 51.32, 0.3104, 18, -61.82, 41.55, 0.06705, 3, 2, 64.52, 19.7, 0.4656, 14, -87.41, -23.38, 0.224, 27, -17.53, 19.61, 0.3104, 3, 2, 64.13, -1.82, 0.4656, 14, -88.11, -1.87, 0.224, 27, -17.91, -1.91, 0.3104, 3, 2, 63.7, -25.84, 0.4656, 14, -88.89, 22.14, 0.224, 27, -18.34, -25.94, 0.3104, 3, 2, 63.17, -55.39, 0.4656, 14, -89.85, 51.67, 0.224, 27, -18.87, -55.48, 0.3104, 3, 2, 62.68, -83.06, 0.56405, 21, -66.3, -1.15, 0.1129, 14, -91.44, 80.83, 0.32305, 3, 2, 34.01, -100.34, 0.53353, 21, -38.27, -19.44, 0.1609, 14, -63.86, 99.62, 0.30557, 3, 2, 33.65, -120.72, 0.55388, 21, -38.63, -39.82, 0.1289, 14, -64.74, 119.99, 0.31723, 3, 2, 7.13, 78.34, 0.61459, 14, -28, -76.91, 0.224, 18, -5.64, 11.09, 0.16141, 3, 2, 10.16, 104.27, 0.49043, 14, -30.86, -102.78, 0.224, 18, -10.28, -14.61, 0.28557, 4, 2, 5.28, 53.08, 0.33479, 14, -29.97, -53.92, 0.224, 27, -76.76, 52.99, 0.3849, 18, -2.24, 36.18, 0.05632, 3, 2, 0.76, 22.7, 0.3911, 14, -27.36, -23.37, 0.224, 27, -81.28, 22.61, 0.3849, 3, 2, 0.56, -2.94, 0.3911, 14, -28.43, 2.24, 0.224, 27, -81.48, -3.03, 0.3849, 4, 2, -1.41, -26.94, 0.32853, 21, -0.25, 52.65, 0.06258, 14, -27.94, 26.32, 0.224, 27, -83.45, -27.04, 0.3849, 4, 2, -2.71, -55.67, 0.38366, 21, 0.02, 23.89, 0.08195, 14, -27.69, 55.51, 0.224, 27, -84.75, -55.77, 0.3104, 3, 2, -1.63, -83.66, 0.59507, 21, -2.05, -4.04, 0.15232, 14, -28.39, 85.19, 0.25261, 3, 2, 0.34, -103.73, 0.54339, 21, -4.74, -24.03, 0.20198, 14, -31.59, 105.1, 0.25463], "hull": 42, "edges": [58, 56, 56, 54, 54, 52, 52, 50, 50, 48, 48, 46, 46, 44, 44, 42, 42, 40, 40, 38, 38, 36, 36, 34, 32, 34, 32, 30, 30, 28, 26, 28, 26, 24, 24, 22, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 78, 80, 76, 78, 74, 76, 72, 74, 64, 66, 66, 68, 68, 70, 70, 72, 48, 84, 84, 66, 54, 86, 86, 64, 58, 60, 62, 64, 60, 62, 42, 88, 88, 68, 36, 90, 90, 70, 30, 92, 92, 72, 74, 94, 94, 24, 76, 96, 96, 18, 78, 98, 98, 12, 6, 100, 100, 80, 2, 0, 80, 82, 82, 0], "width": 276, "height": 80}}, "Spikes": {"Spikes": {"name": "images/SpikerB<PERSON>_SpikesBack", "type": "mesh", "uvs": [1, 1, 0.60264, 1, 0.27944, 1, 0, 1, 0, 0, 0.24598, 0, 0.65423, 0, 1, 0], "triangles": [3, 4, 5, 1, 2, 5, 3, 5, 2, 6, 1, 5, 6, 7, 0, 1, 6, 0], "vertices": [258.53, 157.24, 201.44, 35.23, 155.01, -64.01, 114.86, -149.81, 285.15, -229.48, 320.49, -153.96, 379.14, -28.6, 428.81, 77.57], "hull": 8, "edges": [6, 8, 0, 14, 8, 10, 4, 6, 0, 2, 2, 4, 10, 12, 12, 14], "width": 339, "height": 188}}, "Spikes_Front": {"SpikesFront": {"name": "images/SpikerBoss_SpikesFront", "type": "mesh", "uvs": [0.69205, 0.1165, 0.75416, 0.27913, 0.85178, 0.42131, 0.91868, 0.48093, 0.97862, 0.214, 1, 0.2224, 1, 0.56963, 0.95525, 0.71375, 0.88399, 0.94042, 0.93089, 0.95468, 0.93414, 1, 0.72818, 1, 0.469, 1, 0.28593, 0.95523, 0.11711, 0.71784, 0, 0.34221, 0, 0.22377, 0.30232, 0.75168, 0.40021, 0.74223, 0.48632, 0.69074, 0.44797, 0.611, 0.44179, 0.38154, 0.60325, 0, 0.69355, 0, 0.37375, 0.86735, 0.4121, 0.93082, 0.45663, 0.94709, 0.44303, 0.86162, 0.50859, 0.81768, 0.56301, 0.73306, 0.56054, 0.88277, 0.56549, 0.61914, 0.68736, 0.48093, 0.78002, 0.49083, 0.7789, 0.67296, 0.67623, 0.73317, 0.67623, 0.88452, 0.72818, 0.9252, 0.88775, 0.57369, 0.79127, 0.87638, 0.68958, 0.3085, 0.72303, 0.46709, 0.78101, 0.47743, 0.81601, 0.58345], "triangles": [16, 17, 15, 17, 14, 15, 24, 17, 18, 0, 22, 23, 0, 21, 22, 40, 0, 1, 0, 40, 21, 41, 40, 1, 42, 41, 1, 2, 42, 1, 5, 3, 4, 32, 21, 40, 32, 40, 41, 33, 41, 42, 32, 41, 33, 3, 5, 6, 38, 2, 3, 38, 3, 6, 43, 33, 42, 42, 2, 43, 38, 43, 2, 31, 20, 21, 32, 31, 21, 34, 32, 33, 34, 33, 43, 35, 31, 32, 19, 20, 31, 7, 38, 6, 29, 19, 31, 35, 29, 31, 32, 34, 35, 28, 19, 29, 18, 19, 28, 27, 18, 28, 24, 18, 27, 38, 7, 39, 35, 34, 39, 34, 43, 38, 34, 38, 39, 30, 28, 29, 36, 35, 39, 29, 35, 36, 30, 29, 36, 37, 36, 39, 25, 24, 27, 8, 39, 7, 26, 27, 28, 26, 28, 30, 25, 27, 26, 13, 14, 17, 13, 17, 24, 12, 26, 30, 25, 26, 12, 13, 24, 25, 13, 25, 12, 11, 36, 37, 30, 36, 11, 12, 30, 11, 8, 9, 10, 11, 37, 39, 39, 8, 11, 10, 11, 8], "vertices": [2, 27, 344.72, -32.01, 0.32, 2, 427.64, -34.7, 0.68, 2, 27, 301.57, -52.74, 0.32, 2, 384.49, -55.43, 0.68, 3, 27, 263.59, -85.84, 0.2944, 2, 346.5, -88.53, 0.6256, 4, 165.4, 94.55, 0.08, 3, 27, 247.5, -108.7, 0.22016, 2, 330.41, -111.39, 0.46784, 4, 165, 66.59, 0.312, 3, 27, 317.32, -130.69, 0.22016, 2, 400.23, -133.38, 0.46784, 4, 235.07, 87.79, 0.312, 3, 27, 314.98, -138.05, 0.22016, 2, 397.89, -140.74, 0.46784, 4, 237.28, 80.4, 0.312, 3, 27, 223.67, -136.42, 0.22016, 2, 306.58, -139.11, 0.46784, 4, 160.95, 30.27, 0.312, 3, 27, 186.05, -120.26, 0.22016, 2, 268.96, -122.95, 0.46784, 4, 120.77, 22.4, 0.312, 2, 27, 126.88, -94.54, 0.32, 2, 209.8, -97.23, 0.68, 2, 27, 122.84, -110.71, 0.32, 2, 205.76, -113.39, 0.68, 2, 27, 110.91, -111.62, 0.32, 2, 193.82, -114.3, 0.68, 2, 27, 112.18, -40.37, 0.32, 2, 195.09, -43.05, 0.68, 2, 27, 113.78, 49.3, 0.32, 2, 196.69, 46.61, 0.68, 2, 27, 126.68, 112.42, 0.32, 2, 209.6, 109.73, 0.68, 3, 27, 179.73, 157.15, 0.15104, 2, 262.65, 154.46, 0.32096, 9, 136.84, 15, 0.528, 3, 27, 279.23, 195.9, 0.15104, 2, 362.14, 193.21, 0.32096, 9, 241.23, -7.45, 0.528, 3, 27, 310.38, 195.34, 0.15104, 2, 393.29, 192.66, 0.32096, 9, 266.92, -25.06, 0.528, 3, 27, 180.11, 105.79, 0.15104, 2, 263.02, 103.11, 0.32096, 9, 108.87, -28.07, 0.528, 3, 27, 181.99, 71.89, 0.25856, 2, 264.9, 69.2, 0.54944, 9, 91.76, -57.41, 0.192, 2, 27, 194.99, 41.85, 0.32, 2, 277.91, 39.17, 0.68, 2, 27, 216.2, 54.75, 0.32, 2, 299.11, 52.06, 0.68, 2, 27, 276.58, 55.81, 0.32, 2, 359.49, 53.12, 0.68, 2, 27, 375.91, -1.84, 0.32, 2, 458.82, -4.53, 0.68, 2, 27, 375.35, -33.08, 0.32, 2, 458.26, -35.77, 0.68, 3, 27, 149.25, 81.62, 0.15104, 2, 232.16, 78.94, 0.32096, 9, 69.8, -31.25, 0.528, 2, 27, 132.32, 68.66, 0.32, 2, 215.24, 65.97, 0.68, 2, 27, 127.77, 53.33, 0.32, 2, 210.68, 50.64, 0.68, 3, 27, 150.33, 57.63, 0.23808, 2, 233.24, 54.95, 0.50592, 9, 57.49, -51.87, 0.256, 2, 27, 161.48, 34.75, 0.32, 2, 244.39, 32.06, 0.68, 2, 27, 183.39, 15.52, 0.32, 2, 266.31, 12.83, 0.68, 2, 27, 144.04, 17.08, 0.32, 2, 226.95, 14.39, 0.68, 2, 27, 213.33, 14.13, 0.32, 2, 296.25, 11.44, 0.68, 2, 27, 248.92, -28.68, 0.32, 2, 331.84, -31.37, 0.68, 2, 27, 245.75, -60.69, 0.32, 2, 328.66, -63.38, 0.68, 2, 27, 197.86, -59.45, 0.32, 2, 280.78, -62.13, 0.68, 2, 27, 182.66, -23.64, 0.32, 2, 265.58, -26.33, 0.68, 2, 27, 142.87, -22.93, 0.32, 2, 225.78, -25.62, 0.68, 2, 27, 131.85, -40.72, 0.32, 2, 214.76, -43.4, 0.68, 3, 27, 223.29, -97.57, 0.22016, 2, 306.21, -100.26, 0.46784, 4, 138.74, 62.14, 0.312, 2, 27, 144.29, -62.77, 0.32, 2, 227.21, -65.46, 0.68, 2, 27, 294.25, -30.26, 0.32, 2, 377.17, -32.95, 0.68, 2, 27, 252.34, -41.08, 0.32, 2, 335.26, -43.77, 0.68, 2, 27, 249.27, -61.1, 0.32, 2, 332.18, -63.78, 0.68, 3, 27, 221.17, -72.7, 0.2944, 2, 304.08, -75.39, 0.6256, 4, 122.96, 81.48, 0.08], "hull": 24, "edges": [32, 34, 30, 32, 30, 28, 28, 26, 48, 50, 50, 52, 52, 24, 48, 54, 54, 56, 56, 58, 58, 60, 58, 62, 62, 38, 38, 40, 40, 42, 42, 44, 44, 46, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 22, 24, 74, 22, 46, 0, 0, 2, 2, 4, 4, 6, 76, 78, 64, 80, 80, 0, 80, 82, 82, 84, 84, 86, 86, 76, 16, 14, 14, 12, 6, 8, 12, 10, 8, 10, 34, 36, 36, 38, 24, 26, 16, 18, 20, 22, 18, 20], "width": 346, "height": 263}}, "Tentacle1": {"Tentacle5": {"name": "images/SpikerBossTentacle4", "type": "mesh", "uvs": [1, 0.10807, 1, 0.23422, 1, 0.3513, 1, 0.46837, 1, 0.58662, 1, 0.68306, 1, 0.7912, 1, 0.90395, 1, 1, 0, 1, 0, 0.88679, 0, 0.75991, 0, 0.67143, 0, 0.58314, 0.01075, 0.4739, 0, 0.35413, 1e-05, 0.23927, 0, 0.10763, 0, 0, 1, 0], "triangles": [12, 13, 4, 12, 4, 5, 11, 12, 5, 11, 5, 6, 10, 11, 6, 10, 6, 7, 9, 10, 7, 9, 7, 8, 14, 15, 3, 14, 3, 4, 13, 14, 4, 17, 19, 0, 17, 18, 19, 15, 16, 2, 17, 0, 1, 16, 17, 1, 2, 16, 1, 3, 15, 2], "vertices": [2, 41, 6.62, 38.68, 0.50667, 2, -0.9, 9.77, 0.49333, 3, 41, 35.3, 36.51, 0.66667, 2, -29.66, 10.16, 0.16, 42, -23.9, 37.29, 0.17333, 3, 41, 61.92, 34.49, 0.55826, 42, 2.74, 35.63, 0.35331, 43, -51.78, 34.77, 0.08843, 3, 41, 88.53, 32.47, 0.22794, 42, 29.38, 33.96, 0.53975, 43, -25.12, 33.55, 0.23232, 3, 41, 114.69, 27.42, 0.06794, 42, 55.6, 29.26, 0.44625, 43, 1.18, 29.28, 0.48582, 3, 41, 137.34, 28.77, 0.00301, 42, 78.24, 30.91, 0.33561, 43, 23.78, 31.31, 0.66138, 2, 42, 102.84, 29.37, 0.14917, 43, 48.41, 30.18, 0.85084, 2, 42, 128.5, 27.77, 0.06933, 43, 74.09, 29, 0.93067, 1, 43, 95.97, 28, 1, 1, 43, 92.91, -38.76, 1, 2, 42, 120.43, -38.69, 0.04907, 43, 67.12, -37.58, 0.95093, 2, 42, 91.56, -36.88, 0.13222, 43, 38.23, -36.25, 0.86778, 2, 42, 71.42, -35.63, 0.31377, 43, 18.07, -35.33, 0.68623, 3, 41, 109.57, -36.15, 0.09075, 42, 51.33, -34.37, 0.42889, 43, -2.04, -34.41, 0.48035, 3, 41, 84.79, -33.55, 0.23475, 42, 26.52, -32.1, 0.53507, 43, -26.88, -32.55, 0.23018, 3, 41, 57.51, -32.2, 0.56809, 42, -0.78, -31.12, 0.35353, 43, -54.2, -32.02, 0.07839, 3, 41, 31.4, -30.22, 0.62374, 2, -29.9, 77, 0.18667, 42, -26.92, -29.48, 0.18959, 3, 41, 1.47, -27.95, 0.47974, 2, 0.11, 76.59, 0.52, 42, -56.87, -27.61, 0.00026, 3, 41, -23, -26.1, 0.14641, 2, 24.65, 76.26, 0.85333, 42, -81.37, -26.08, 0.00026, 2, 41, -17.95, 40.54, 0.17333, 2, 23.73, 9.43, 0.82667], "hull": 20, "edges": [30, 28, 28, 26, 26, 24, 24, 22, 22, 20, 20, 18, 16, 18, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 30, 32, 32, 34, 34, 36, 36, 38, 38, 0, 0, 2, 2, 4], "width": 57, "height": 228}}, "Tentacle2": {"Tentacle6": {"name": "images/SpikerBossTentacle4", "type": "mesh", "uvs": [1, 0.10807, 1, 0.23422, 1, 0.3513, 1, 0.46837, 1, 0.5866, 1, 0.67888, 1, 0.78715, 1, 0.89964, 1, 1, 0, 0.99784, 0, 0.88248, 0, 0.78082, 0, 0.67979, 0, 0.58732, 0.01075, 0.4739, 0, 0.35413, 1e-05, 0.23927, 0, 0.11155, 0, 0.00647, 1, 0], "triangles": [13, 4, 5, 12, 13, 5, 9, 7, 8, 9, 10, 7, 10, 6, 7, 10, 11, 6, 11, 5, 6, 11, 12, 5, 14, 15, 3, 13, 14, 4, 14, 3, 4, 3, 15, 2, 15, 16, 2, 17, 18, 0, 18, 19, 0, 2, 16, 1, 16, 17, 1, 17, 0, 1], "vertices": [2, 44, -2.41, 38.95, 0.504, 2, 1.78, 2.05, 0.496, 3, 44, 26.31, 37.42, 0.61152, 2, -26.98, 1.54, 0.22848, 45, -32.9, 38.08, 0.16, 2, 44, 52.97, 36, 0.45609, 45, -6.23, 37.01, 0.54391, 3, 44, 79.62, 34.57, 0.45597, 45, 20.44, 35.94, 0.54372, 46, -34.09, 35.38, 0.00032, 3, 44, 106.54, 33.14, 0.14478, 45, 47.38, 34.87, 0.39486, 46, -7.14, 34.75, 0.46036, 3, 44, 127.55, 32.02, 0.00059, 45, 68.4, 34.02, 0.25934, 46, 13.9, 34.26, 0.74007, 2, 45, 93.07, 33.04, 0.192, 46, 38.57, 33.68, 0.808, 1, 46, 64.21, 33.08, 1, 1, 46, 87.09, 32.54, 1, 1, 46, 84.91, -39.37, 1, 1, 46, 58.62, -38.76, 1, 2, 45, 88.75, -38.8, 0.16031, 46, 35.45, -38.22, 0.83969, 2, 45, 65.73, -37.88, 0.336, 46, 12.42, -37.68, 0.664, 3, 44, 102.87, -38.72, 0.06814, 45, 44.66, -37.03, 0.44066, 46, -8.66, -37.18, 0.49119, 3, 44, 77.09, -36.57, 0.448, 45, 18.86, -35.23, 0.54317, 46, -34.49, -35.8, 0.00883, 2, 44, 49.78, -35.89, 0.36315, 45, -8.46, -34.91, 0.63685, 3, 44, 23.63, -34.49, 0.57273, 2, -26.86, -70.42, 0.15178, 45, -34.63, -33.86, 0.27549, 3, 44, -5.45, -32.94, 0.56785, 2, 2.26, -69.9, 0.432, 45, -63.72, -32.69, 0.00015, 1, 2, 26.21, -69.48, 1, 1, 2, 26.42, 2.49, 1], "hull": 20, "edges": [30, 28, 28, 26, 26, 24, 24, 22, 22, 20, 20, 18, 16, 18, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 30, 32, 32, 34, 34, 36, 36, 38, 38, 0, 0, 2, 2, 4], "width": 57, "height": 228}}, "Tentacle3": {"Tentacle3": {"name": "images/SpikerBossTentacle3", "type": "mesh", "uvs": [1, 0.15899, 1, 0.30532, 0.93247, 0.45063, 0.92403, 0.55377, 0.90715, 0.64294, 0.90715, 0.75022, 0.94092, 0.84091, 0.94092, 0.95132, 0.93247, 1, 0.32075, 0.99141, 0.3123, 0.93239, 0.3123, 0.84069, 0.33763, 0.73712, 0.27854, 0.62437, 0, 0.54286, 0, 0.41882, 0, 0.2703, 0, 0.12218, 0, 0, 1, 0], "triangles": [12, 4, 5, 8, 9, 7, 9, 10, 7, 7, 10, 6, 10, 11, 6, 11, 5, 6, 11, 12, 5, 17, 19, 0, 17, 18, 19, 2, 15, 1, 15, 16, 1, 16, 0, 1, 16, 17, 0, 12, 13, 4, 4, 13, 3, 13, 14, 3, 3, 14, 2, 14, 15, 2], "vertices": [3, 16, -56.49, 35.8, 0.09766, 15, 10.48, 35.7, 0.42763, 2, -10.26, 0.57, 0.47472, 3, 16, -22.59, 33.9, 0.28005, 15, 44.38, 33.89, 0.57857, 2, -44.2, 1.19, 0.14138, 4, 16, 10.84, 28.03, 0.59369, 15, 77.83, 28.12, 0.38393, 2, -77.83, 5.8, 0.01171, 17, -56.77, 29.88, 0.01067, 3, 16, 34.71, 26.19, 0.70722, 15, 101.7, 26.35, 0.15094, 17, -32.96, 27.49, 0.14183, 2, 16, 55.31, 24.04, 0.66062, 17, -12.41, 24.86, 0.33938, 2, 16, 80.16, 22.64, 0.39342, 17, 12.4, 22.88, 0.60658, 2, 16, 101.27, 23.45, 0.19125, 17, 33.53, 23.2, 0.80875, 2, 16, 126.85, 22.02, 0.05546, 17, 59.06, 21.17, 0.94454, 1, 17, 70.28, 19.78, 1, 1, 17, 65.44, -16.04, 1, 2, 16, 120.39, -14.77, 0.064, 17, 51.75, -15.45, 0.936, 2, 16, 99.14, -13.57, 0.192, 17, 30.54, -13.77, 0.808, 3, 16, 75.24, -10.74, 0.39449, 15, 142.34, -10.45, 0.0008, 17, 6.71, -10.37, 0.60471, 3, 16, 48.93, -12.75, 0.60332, 15, 116.04, -12.55, 0.0453, 17, -19.65, -11.77, 0.35138, 3, 16, 29.12, -28.1, 0.63297, 15, 96.28, -27.95, 0.22098, 17, -39.8, -26.66, 0.14605, 3, 16, 0.39, -26.49, 0.52649, 15, 67.54, -26.42, 0.45751, 17, -68.49, -24.38, 0.016, 3, 16, -34.01, -24.56, 0.25365, 15, 33.13, -24.59, 0.66635, 2, -34.99, 60.03, 0.08, 3, 16, -68.32, -22.63, 0.096, 15, -1.18, -22.77, 0.49067, 2, -0.63, 59.4, 0.41333, 2, 15, -29.49, -21.26, 0.25333, 2, 27.71, 58.88, 0.74667, 3, 16, -93.31, 37.87, 0.00902, 15, -26.35, 37.66, 0.19464, 2, 26.62, -0.11, 0.79634], "hull": 20, "edges": [30, 32, 32, 34, 36, 38, 34, 36, 30, 28, 28, 26, 26, 24, 24, 22, 22, 20, 20, 18, 16, 18, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 0, 0, 38], "width": 59, "height": 232}}, "Tentacle4": {"Tentacle4": {"name": "images/SpikerBossTentacle2", "type": "mesh", "uvs": [1, 0.15899, 1, 0.30532, 0.991, 0.41817, 1, 0.54948, 0.991, 0.65153, 0.991, 0.75451, 1, 0.87598, 1, 1, 0.2324, 0.99295, 0.2324, 0.86275, 0.22133, 0.73576, 0.22133, 0.67473, 0.21027, 0.53184, 0.18813, 0.41177, 0.12173, 0.25856, 0, 0.1494, 0, 0, 1, 0], "triangles": [9, 10, 5, 9, 5, 6, 8, 9, 6, 8, 6, 7, 15, 16, 17, 15, 17, 0, 12, 13, 2, 12, 2, 3, 4, 12, 3, 11, 12, 4, 11, 4, 5, 10, 11, 5, 2, 13, 1, 14, 15, 0, 13, 14, 1, 14, 0, 1], "vertices": [3, 24, 18.43, 53.33, 0.43014, 25, -48.49, 53.41, 0.10139, 2, -24.65, 28.88, 0.46846, 3, 24, 49.44, 54.06, 0.56576, 25, -17.47, 54.05, 0.29911, 2, -55.67, 28.98, 0.13513, 2, 24, 73.38, 53.67, 0.4868, 25, 6.46, 53.59, 0.5132, 3, 24, 101.2, 54.98, 0.28634, 25, 34.28, 54.82, 0.63204, 26, -32.71, 56.12, 0.08162, 3, 24, 122.84, 54.46, 0.15072, 25, 55.93, 54.24, 0.59608, 26, -11.09, 55.03, 0.2532, 3, 24, 144.67, 54.81, 0.03768, 25, 77.76, 54.52, 0.45126, 26, 10.74, 54.81, 0.51106, 2, 25, 103.5, 55.72, 0.23723, 26, 36.5, 55.41, 0.76277, 2, 25, 129.79, 56.07, 0.07548, 26, 62.79, 55.14, 0.92452, 2, 25, 129.25, -17.34, 0.064, 26, 60.55, -18.23, 0.936, 2, 25, 101.65, -17.7, 0.22196, 26, 32.95, -17.95, 0.77804, 2, 25, 74.75, -19.11, 0.52236, 26, 6.02, -18.74, 0.47764, 3, 24, 128.94, -19.04, 0.08542, 25, 61.81, -19.28, 0.70627, 26, -6.92, -18.6, 0.20831, 3, 24, 98.67, -20.58, 0.25355, 25, 31.53, -20.74, 0.71351, 26, -37.22, -19.35, 0.03294, 2, 24, 73.25, -23.11, 0.46422, 25, 6.11, -23.19, 0.53578, 3, 24, 40.87, -30.09, 0.54146, 25, -26.29, -30.08, 0.28787, 2, -45.41, -54.98, 0.17067, 3, 24, 17.93, -42.32, 0.37333, 25, -49.27, -42.24, 0.12267, 2, -22.22, -66.75, 0.504, 2, 24, -13.74, -43.06, 0.16267, 2, 9.46, -66.85, 0.83733, 3, 24, -15.27, 52.57, 0.19199, 25, -82.19, 52.75, 0.00621, 2, 9.06, 28.79, 0.8018], "hull": 18, "edges": [26, 28, 28, 30, 32, 34, 30, 32, 26, 24, 24, 22, 22, 20, 20, 18, 18, 16, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 0, 0, 34, 14, 16], "width": 90, "height": 212}}, "Tentacle5": {"Tentacle5": {"name": "images/SpikerBossTentacle5", "type": "mesh", "uvs": [1, 0.10807, 1, 0.23422, 1, 0.3513, 1, 0.46837, 0.95426, 0.58444, 1, 0.68306, 1, 0.7912, 1, 0.90395, 1, 1, 0, 1, 0, 0.88679, 0, 0.75991, 0, 0.67143, 0, 0.58314, 0.01075, 0.4739, 0, 0.35413, 1e-05, 0.23927, 0, 0.10763, 0, 0, 1, 0], "triangles": [9, 7, 8, 9, 10, 7, 10, 6, 7, 10, 11, 6, 11, 5, 6, 11, 12, 5, 12, 4, 5, 12, 13, 4, 13, 14, 4, 4, 14, 3, 14, 15, 3, 3, 15, 2, 15, 16, 2, 2, 16, 1, 16, 17, 1, 17, 0, 1, 17, 18, 19, 17, 19, 0], "vertices": [2, 2, 26.16, 66.1, 0.464, 18, -23.88, 24.48, 0.536, 2, 2, -4.49, 66.65, 0.24, 18, 6.68, 22.03, 0.76, 2, 18, 35.04, 19.77, 0.76, 19, -23.94, 20.55, 0.24, 2, 18, 63.39, 17.5, 0.51895, 19, 4.44, 18.65, 0.48105, 3, 18, 91.35, 13.31, 0.17106, 19, 32.46, 14.84, 0.62094, 20, -21.73, 14.48, 0.208, 2, 19, 56.5, 15.18, 0.4875, 20, 2.31, 15.22, 0.5125, 2, 19, 82.72, 13.44, 0.432, 20, 28.55, 13.91, 0.568, 2, 19, 110.05, 11.61, 0.208, 20, 55.91, 12.54, 0.792, 1, 20, 79.23, 11.38, 1, 1, 20, 77.1, -31.07, 1, 2, 19, 103.07, -30.51, 0.224, 20, 49.63, -29.69, 0.776, 2, 19, 72.3, -28.46, 0.37101, 20, 18.83, -28.15, 0.62899, 2, 19, 50.85, -27.03, 0.50636, 20, -2.64, -27.08, 0.49364, 3, 18, 87.8, -27.09, 0.13115, 19, 29.44, -25.6, 0.67685, 20, -24.07, -26.01, 0.192, 2, 18, 61.38, -24.52, 0.48, 19, 2.99, -23.38, 0.52, 2, 18, 32.33, -22.65, 0.79196, 19, -26.08, -21.9, 0.20804, 3, 2, -4.96, 109.16, 0.288, 18, 4.51, -20.43, 0.71171, 19, -53.93, -20.05, 0.00029, 2, 2, 27.02, 108.59, 0.432, 18, -27.38, -17.88, 0.568, 1, 2, 53.17, 108.13, 1, 1, 2, 52.42, 65.63, 1], "hull": 20, "edges": [30, 28, 28, 26, 26, 24, 24, 22, 22, 20, 20, 18, 16, 18, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 30, 32, 32, 34, 34, 36, 36, 38, 38, 0, 0, 2, 2, 4], "width": 39, "height": 243}}, "Tentacle6": {"Tentacle6": {"name": "images/SpikerBossTentacle1", "type": "mesh", "uvs": [1, 0.10807, 1, 0.23422, 1, 0.3513, 1, 0.46837, 1, 0.5866, 1, 0.67888, 1, 0.78715, 1, 0.89964, 1, 1, 0, 0.99784, 0, 0.88248, 0, 0.78082, 0, 0.67979, 0, 0.58732, 0.01075, 0.4739, 0, 0.35413, 1e-05, 0.23927, 0, 0.11155, 0, 0.00647, 1, 0], "triangles": [9, 7, 8, 9, 10, 7, 10, 6, 7, 10, 11, 6, 11, 5, 6, 11, 12, 5, 18, 19, 0, 17, 18, 0, 14, 15, 3, 3, 15, 2, 15, 16, 2, 2, 16, 1, 16, 17, 1, 17, 0, 1, 13, 4, 5, 13, 14, 4, 14, 3, 4, 12, 13, 5], "vertices": [3, 22, -69.35, 26.19, 0.04966, 21, -10.29, 26.01, 0.377, 2, 7.67, -53.92, 0.57333, 3, 22, -40.23, 25.03, 0.16166, 21, 18.81, 24.46, 0.59834, 2, -21.47, -54.43, 0.24, 3, 22, -13.21, 23.94, 0.32794, 21, 45.82, 23.02, 0.59739, 2, -48.51, -54.91, 0.07467, 3, 22, 13.82, 22.86, 0.46257, 21, 72.82, 21.58, 0.47048, 23, -40.5, 22.19, 0.06696, 3, 22, 41.1, 21.77, 0.50235, 21, 100.09, 20.13, 0.28114, 23, -13.2, 21.55, 0.21651, 3, 22, 62.41, 20.92, 0.43207, 21, 121.38, 18.99, 0.11409, 23, 8.12, 21.05, 0.45384, 3, 22, 87.4, 19.91, 0.30111, 21, 146.36, 17.66, 0.032, 23, 33.12, 20.47, 0.66689, 2, 22, 113.36, 18.87, 0.14933, 23, 59.1, 19.86, 0.85067, 2, 22, 136.52, 17.95, 0.05333, 23, 82.28, 19.32, 0.94667, 2, 22, 134.15, -28.82, 0.05333, 23, 80.68, -27.49, 0.94667, 2, 22, 107.52, -27.76, 0.144, 23, 54.04, -26.86, 0.856, 3, 22, 84.06, -26.82, 0.30284, 21, 142.4, -29.03, 0.02667, 23, 30.56, -26.31, 0.67049, 3, 22, 60.74, -25.88, 0.43273, 21, 119.1, -27.78, 0.09067, 23, 7.23, -25.77, 0.4766, 3, 22, 39.4, -25.03, 0.5154, 21, 97.76, -26.64, 0.25067, 23, -14.13, -25.27, 0.23393, 3, 22, 13.24, -23.48, 0.47416, 21, 71.63, -24.75, 0.43972, 23, -40.31, -24.15, 0.08611, 3, 22, -14.43, -22.87, 0.34871, 21, 43.98, -23.78, 0.60863, 2, -48.33, -101.74, 0.04267, 3, 22, -40.94, -21.81, 0.17537, 21, 17.48, -22.36, 0.62729, 2, -21.8, -101.27, 0.19733, 3, 22, -70.42, -20.63, 0.05776, 21, -11.98, -20.79, 0.41157, 2, 7.69, -100.75, 0.53067, 2, 21, -36.22, -19.5, 0.17867, 2, 31.96, -100.32, 0.82133, 2, 21, -35.22, 27.34, 0.168, 2, 32.63, -53.48, 0.832], "hull": 20, "edges": [30, 28, 28, 26, 26, 24, 24, 22, 22, 20, 20, 18, 16, 18, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 30, 32, 32, 34, 34, 36, 36, 38, 38, 0, 0, 2, 2, 4], "width": 39, "height": 231}}}}], "events": {"teleport": {}}, "animations": {"animation": {"slots": {"images/Eye3": {"attachment": [{}]}, "ScuttleEye3": {"attachment": [{"name": "<PERSON><PERSON><PERSON>_Eye_Closed"}]}}, "bones": {"ScuttleEyeball3": {"translate": [{"y": 0.19, "curve": [0.102, 0, 0.198, 0, 0.102, 1.38, 0.198, 2.38]}, {"time": 0.2667, "y": 2.38, "curve": [0.433, 0, 0.767, 0, 0.433, 2.38, 0.767, -3.57]}, {"time": 0.9333, "y": -3.57, "curve": [1.031, 0, 1.188, 0, 1.031, -3.57, 1.188, -1.48]}, {"time": 1.3333, "y": 0.19}]}, "FACE": {"translate": [{"x": 7.05, "y": 4.16, "curve": [0.067, 9.68, 0.144, 11.92, 0.067, 4.16, 0.144, 4.16]}, {"time": 0.2667, "x": 12.06, "y": 4.16, "curve": [0.576, 12.06, 0.6, -3.76, 0.576, 4.16, 0.6, 4.16]}, {"time": 0.9333, "x": -4.17, "y": 4.16, "curve": [1.13, -4.17, 1.218, 2.01, 1.13, 4.16, 1.218, 4.16]}, {"time": 1.3333, "x": 7.05, "y": 4.16}]}, "MAIN": {"translate": [{"y": 17.53, "curve": [0.31, 0, 0.333, 0, 0.31, 17.53, 0.333, -32.5]}, {"time": 0.6667, "y": -33.8, "curve": [0.976, 0, 1, 0, 0.976, -33.8, 1, 16.22]}, {"time": 1.3333, "y": 17.53}], "scale": [{"x": 0.987, "y": 1.006, "curve": [0.125, 0.962, 0.25, 0.937, 0.125, 1.028, 0.25, 1.05]}, {"time": 0.3333, "x": 0.937, "y": 1.05, "curve": [0.5, 0.937, 0.833, 1.038, 0.5, 1.05, 0.833, 0.962]}, {"time": 1, "x": 1.038, "y": 0.962, "curve": [1.083, 1.038, 1.208, 1.013, 1.083, 0.962, 1.208, 0.984]}, {"time": 1.3333, "x": 0.987, "y": 1.006}]}, "Spike5": {"rotate": [{"value": 3.35, "curve": [0.063, 5.39, 0.121, 6.75]}, {"time": 0.1667, "value": 6.75, "curve": [0.333, 6.75, 0.667, -11.66]}, {"time": 0.8333, "value": -11.66, "curve": [0.955, -11.66, 1.167, -1.65]}, {"time": 1.3333, "value": 3.35}]}, "Spike4": {"rotate": [{"value": 4.35, "curve": [0.05, 5.82, 0.095, 6.75]}, {"time": 0.1333, "value": 6.75, "curve": [0.3, 6.75, 0.633, -11.66]}, {"time": 0.8, "value": -11.66, "curve": [0.929, -11.66, 1.162, -0.22]}, {"time": 1.3333, "value": 4.35}]}, "Spike3": {"rotate": [{"value": 5.22, "curve": [0.173, 1.32, 0.429, -11.66]}, {"time": 0.5667, "value": -11.66, "curve": [0.733, -11.66, 1.067, 6.75]}, {"time": 1.2333, "value": 6.75, "curve": [1.263, 6.75, 1.296, 6.18]}, {"time": 1.3333, "value": 5.22}]}, "Spike2": {"rotate": [{"value": -0.02, "curve": [0.145, -5.19, 0.302, -11.66]}, {"time": 0.4, "value": -11.66, "curve": [0.567, -11.66, 0.9, 6.75]}, {"time": 1.0667, "value": 6.75, "curve": [1.135, 6.75, 1.232, 3.67]}, {"time": 1.3333, "value": -0.02}]}, "Spike1": {"rotate": [{"value": -2.5, "curve": [0.045, -4.16, 0.09, -5.81]}, {"time": 0.1333, "value": -7.2, "curve": [0.21, -9.8, 0.28, -11.66]}, {"time": 0.3333, "value": -11.66, "curve": [0.5, -11.66, 0.833, 6.75]}, {"time": 1, "value": 6.75, "curve": [1.083, 6.75, 1.208, 2.12]}, {"time": 1.3333, "value": -2.5}]}, "Spike10": {"rotate": [{"value": 5.4, "curve": [0.076, 7.48, 0.146, 8.96]}, {"time": 0.2, "value": 8.96, "curve": [0.367, 8.96, 0.7, -5.73]}, {"time": 0.8667, "value": -5.73, "curve": [0.98, -5.73, 1.172, 1.22]}, {"time": 1.3333, "value": 5.4}]}, "Spike6": {"rotate": [{"value": -0.33, "curve": [0.145, 3.8, 0.302, 8.96]}, {"time": 0.4, "value": 8.96, "curve": [0.567, 8.96, 0.9, -5.73]}, {"time": 1.0667, "value": -5.73, "curve": [1.135, -5.73, 1.232, -3.28]}, {"time": 1.3333, "value": -0.33}]}, "Spike7": {"rotate": [{"value": -4.52, "curve": [0.173, -1.41, 0.429, 8.96]}, {"time": 0.5667, "value": 8.96, "curve": [0.733, 8.96, 1.067, -5.73]}, {"time": 1.2333, "value": -5.73, "curve": [1.263, -5.73, 1.296, -5.28]}, {"time": 1.3333, "value": -4.52}]}, "Spike8": {"rotate": [{"value": -3.82, "curve": [0.05, -5, 0.095, -5.74]}, {"time": 0.1333, "value": -5.74, "curve": [0.3, -5.74, 0.633, 8.96]}, {"time": 0.8, "value": 8.96, "curve": [0.929, 8.96, 1.162, -0.17]}, {"time": 1.3333, "value": -3.82}]}, "Spike9": {"rotate": [{"value": -3.02, "curve": [0.063, -4.65, 0.121, -5.73]}, {"time": 0.1667, "value": -5.73, "curve": [0.333, -5.73, 0.667, 8.96]}, {"time": 0.8333, "value": 8.96, "curve": [0.955, 8.96, 1.167, 0.97]}, {"time": 1.3333, "value": -3.02}]}, "Tentacle5": {"rotate": [{"value": -49.78, "curve": [0.192, -49.78, 0.575, 9.39]}, {"time": 0.7667, "value": 9.39, "curve": [0.908, 9.39, 1.192, -49.78]}, {"time": 1.3333, "value": -49.78}]}, "Tentacle3_Mid": {"rotate": [{"value": 51.86, "curve": [0.03, 48.48, 0.064, 42.9]}, {"time": 0.1, "value": 36.14, "curve": [0.237, 11.69, 0.403, -29.86]}, {"time": 0.5, "value": -29.86, "curve": [0.57, -29.86, 0.665, -18.56]}, {"time": 0.7667, "value": -3.82, "curve": [0.944, 20.88, 1.145, 56.4]}, {"time": 1.2667, "value": 56.4, "curve": [1.287, 56.4, 1.309, 54.76]}, {"time": 1.3333, "value": 51.86}]}, "Tentacle3_Btm": {"rotate": [{"value": 73.54, "curve": [0.029, 73.54, 0.063, 69.14]}, {"time": 0.1, "value": 61.89, "curve": [0.246, 35.84, 0.453, -37.96]}, {"time": 0.5667, "value": -37.96, "curve": [0.622, -37.96, 0.691, -29.13]}, {"time": 0.7667, "value": -16.08, "curve": [0.957, 14.65, 1.196, 73.54]}, {"time": 1.3333, "value": 73.54}]}, "Tentacle6": {"rotate": [{"value": 36.14, "curve": [0.192, 36.14, 0.575, -10.68]}, {"time": 0.7667, "value": -10.68, "curve": [0.908, -10.68, 1.192, 36.14]}, {"time": 1.3333, "value": 36.14}]}, "Tentacle3_Mid2": {"rotate": [{"value": 52.81, "curve": [0.021, 49.53, 0.044, 45.39]}, {"time": 0.0667, "value": 40.71, "curve": [0.204, 14.47, 0.37, -30.13]}, {"time": 0.4667, "value": -30.13, "curve": [0.544, -30.13, 0.652, -15.25]}, {"time": 0.7667, "value": 2.86, "curve": [0.935, 29.02, 1.119, 62.45]}, {"time": 1.2333, "value": 62.45, "curve": [1.262, 62.45, 1.296, 58.81]}, {"time": 1.3333, "value": 52.81}]}, "Tentacle3_Btm2": {"rotate": [{"value": 60.88, "curve": [0.02, 59.21, 0.043, 55.98]}, {"time": 0.0667, "value": 51.6, "curve": [0.213, 27.35, 0.42, -41.36]}, {"time": 0.5333, "value": -41.36, "curve": [0.596, -41.36, 0.678, -30.61]}, {"time": 0.7667, "value": -15.65, "curve": [0.951, 13.9, 1.171, 62.45]}, {"time": 1.3, "value": 62.45, "curve": [1.311, 62.45, 1.322, 61.9]}, {"time": 1.3333, "value": 60.88}]}, "Tentacle2_Btm2": {"rotate": [{"value": 55.68, "curve": [0.148, 34.28, 0.378, -56.67]}, {"time": 0.5, "value": -56.67, "curve": [0.57, -56.67, 0.665, -41.06]}, {"time": 0.7667, "value": -20.71, "curve": [0.944, 13.41, 1.145, 62.45]}, {"time": 1.2667, "value": 62.45, "curve": [1.287, 62.45, 1.309, 60]}, {"time": 1.3333, "value": 55.68}]}, "Tentacle2_Mid2": {"rotate": [{"value": 26.96, "curve": [0.121, 1.18, 0.251, -30.13]}, {"time": 0.3333, "value": -30.13, "curve": [0.44, -30.13, 0.607, -1.1]}, {"time": 0.7667, "value": 24.14, "curve": [0.893, 44.47, 1.015, 62.45]}, {"time": 1.1, "value": 62.45, "curve": [1.16, 62.45, 1.244, 46.14]}, {"time": 1.3333, "value": 26.96}]}, "Tentacle4": {"rotate": [{"value": 26.28, "curve": [0.192, 26.28, 0.575, -10.68]}, {"time": 0.7667, "value": -10.68, "curve": [0.908, -10.68, 1.192, 26.28]}, {"time": 1.3333, "value": 26.28}]}, "Tentacle2_Btm": {"rotate": [{"value": 47.08, "curve": [0.148, 25.7, 0.378, -65.15]}, {"time": 0.5, "value": -65.15, "curve": [0.57, -65.15, 0.665, -49.56]}, {"time": 0.7667, "value": -29.23, "curve": [0.944, 4.85, 1.145, 53.84]}, {"time": 1.2667, "value": 53.84, "curve": [1.287, 53.84, 1.309, 51.4]}, {"time": 1.3333, "value": 47.08}]}, "Tentacle2_Mid": {"rotate": [{"value": 16.89, "curve": [0.121, -7.13, 0.251, -36.3]}, {"time": 0.3333, "value": -36.3, "curve": [0.44, -36.3, 0.607, -9.25]}, {"time": 0.7667, "value": 14.27, "curve": [0.893, 33.21, 1.015, 49.96]}, {"time": 1.1, "value": 49.96, "curve": [1.16, 49.96, 1.244, 34.77]}, {"time": 1.3333, "value": 16.89}]}, "Tentacle3": {"rotate": [{"value": -36.75, "curve": [0.192, -36.75, 0.575, 6.68]}, {"time": 0.7667, "value": 6.68, "curve": [0.908, 6.68, 1.192, -36.75]}, {"time": 1.3333, "value": -36.75}]}, "DangleHandle": {"rotate": [{"value": 6.7, "curve": [0.043, 3.66, 0.088, 0.04]}, {"time": 0.1333, "value": -3.58, "curve": [0.258, -13.51, 0.383, -23.45]}, {"time": 0.4667, "value": -23.45, "curve": [0.633, -23.45, 0.967, 16.3]}, {"time": 1.1333, "value": 16.3, "curve": [1.187, 16.3, 1.257, 12.3]}, {"time": 1.3333, "value": 6.7}], "translate": [{"x": -3.96, "y": 2.8, "curve": [0.125, -27.88, 0.25, -51.8, 0.125, 1.82, 0.25, 0.85]}, {"time": 0.3333, "x": -51.8, "y": 0.85, "curve": [0.5, -51.8, 0.833, 43.87, 0.5, 0.85, 0.833, 4.74]}, {"time": 1, "x": 43.87, "y": 4.74, "curve": [1.083, 43.87, 1.208, 19.95, 1.083, 4.74, 1.208, 3.77]}, {"time": 1.3333, "x": -3.96, "y": 2.8}], "scale": [{"y": 1.723, "curve": [0.167, 1, 0.5, 1, 0.167, 1.723, 0.5, 0.313]}, {"time": 0.6667, "y": 0.313, "curve": [0.833, 1, 1.167, 1, 0.833, 0.313, 1.167, 1.723]}, {"time": 1.3333, "y": 1.723}]}, "Tentacles": {"translate": [{"time": 1.0667, "x": 0.52}], "scale": [{"x": 0.699}, {"time": 0.6667, "x": 1.199}, {"time": 1.3333, "x": 0.699}]}, "Mouth": {"translate": [{"y": -10.97, "curve": [0.167, 0, 0.5, 0, 0.167, -10.97, 0.5, 4.02]}, {"time": 0.6667, "y": 4.02, "curve": [0.833, 0, 1.167, 0, 0.833, 4.02, 1.167, -10.97]}, {"time": 1.3333, "y": -10.97}]}, "Mouth8": {"rotate": [{"value": -31.71, "curve": [0.038, -38.54, 0.073, -43.41]}, {"time": 0.1, "value": -43.41, "curve": [0.183, -43.41, 0.35, 4.89]}, {"time": 0.4333, "value": 4.89, "curve": [0.517, 4.89, 0.683, -43.41]}, {"time": 0.7667, "value": -43.41, "curve": [0.85, -43.41, 1.017, 4.89]}, {"time": 1.1, "value": 4.89, "curve": [1.157, 4.89, 1.253, -17.99]}, {"time": 1.3333, "value": -31.71}]}, "Mouth5": {"rotate": [{"value": 18.98, "curve": [0.085, 12.95, 0.202, -2.12]}, {"time": 0.2667, "value": -2.12, "curve": [0.35, -2.12, 0.517, 22.13]}, {"time": 0.6, "value": 22.13, "curve": [0.683, 22.13, 0.85, -2.12]}, {"time": 0.9333, "value": -2.12, "curve": [1.017, -2.12, 1.183, 22.13]}, {"time": 1.2667, "value": 22.13, "curve": [1.286, 22.13, 1.308, 20.92]}, {"time": 1.3333, "value": 18.98}]}, "Mouth4": {"rotate": [{"value": 6.8, "curve": [0.051, 1.94, 0.099, -2.12]}, {"time": 0.1333, "value": -2.12, "curve": [0.217, -2.12, 0.383, 22.13]}, {"time": 0.4667, "value": 22.13, "curve": [0.55, 22.13, 0.717, -2.12]}, {"time": 0.8, "value": -2.12, "curve": [0.883, -2.12, 1.05, 22.13]}, {"time": 1.1333, "value": 22.13, "curve": [1.182, 22.13, 1.261, 13.62]}, {"time": 1.3333, "value": 6.8}]}, "Mouth7": {"rotate": [{"value": -41.36, "curve": [0.087, -33.65, 0.226, 4.89]}, {"time": 0.3, "value": 4.89, "curve": [0.383, 4.89, 0.55, -43.41]}, {"time": 0.6333, "value": -43.41, "curve": [0.717, -43.41, 0.883, 4.89]}, {"time": 0.9667, "value": 4.89, "curve": [1.05, 4.89, 1.217, -43.41]}, {"time": 1.3, "value": -43.41, "curve": [1.31, -43.41, 1.321, -42.68]}, {"time": 1.3333, "value": -41.36}]}, "Mouth3": {"rotate": [{"value": -2.12, "curve": [0.083, -2.12, 0.25, 22.13]}, {"time": 0.3333, "value": 22.13, "curve": [0.417, 22.13, 0.583, -2.12]}, {"time": 0.6667, "value": -2.12, "curve": [0.75, -2.12, 0.917, 22.13]}, {"time": 1, "value": 22.13, "curve": [1.083, 22.13, 1.25, -2.12]}, {"time": 1.3333, "value": -2.12}]}, "Mouth6": {"rotate": [{"value": -25.64, "curve": [0.073, -12.07, 0.151, 4.89]}, {"time": 0.2, "value": 4.89, "curve": [0.283, 4.89, 0.45, -43.41]}, {"time": 0.5333, "value": -43.41, "curve": [0.617, -43.41, 0.783, 4.89]}, {"time": 0.8667, "value": 4.89, "curve": [0.95, 4.89, 1.117, -43.41]}, {"time": 1.2, "value": -43.41, "curve": [1.234, -43.41, 1.282, -35.33]}, {"time": 1.3333, "value": -25.64}]}, "Tentacle3_Btm3": {"rotate": [{"value": 61.89, "curve": [0.146, 35.84, 0.353, -37.96]}, {"time": 0.4667, "value": -37.96, "curve": [0.544, -37.96, 0.652, -20.04]}, {"time": 0.7667, "value": 1.77, "curve": [0.935, 33.28, 1.119, 73.54]}, {"time": 1.2333, "value": 73.54, "curve": [1.262, 73.54, 1.296, 69.14]}, {"time": 1.3333, "value": 61.89}]}, "Tentacle3_Mid3": {"rotate": [{"value": 36.14, "curve": [0.137, 11.69, 0.303, -29.86]}, {"time": 0.4, "value": -29.86, "curve": [0.492, -29.86, 0.628, -9.97]}, {"time": 0.7667, "value": 10.79, "curve": [0.916, 33.09, 1.067, 56.4]}, {"time": 1.1667, "value": 56.4, "curve": [1.212, 56.4, 1.27, 48]}, {"time": 1.3333, "value": 36.14}]}, "Tentacle1": {"rotate": [{"value": -49.78, "curve": [0.192, -49.78, 0.575, 9.39]}, {"time": 0.7667, "value": 9.39, "curve": [0.908, 9.39, 1.192, -49.78]}, {"time": 1.3333, "value": -49.78}]}, "Tentacle3_Btm4": {"rotate": [{"value": 51.6, "curve": [0.146, 27.35, 0.353, -41.36]}, {"time": 0.4667, "value": -41.36, "curve": [0.544, -41.36, 0.652, -24.67]}, {"time": 0.7667, "value": -4.36, "curve": [0.935, 24.97, 1.119, 62.45]}, {"time": 1.2333, "value": 62.45, "curve": [1.262, 62.45, 1.296, 58.35]}, {"time": 1.3333, "value": 51.6}]}, "Tentacle3_Mid4": {"rotate": [{"value": 40.71, "curve": [0.137, 14.47, 0.303, -30.13]}, {"time": 0.4, "value": -30.13, "curve": [0.492, -30.13, 0.628, -8.78]}, {"time": 0.7667, "value": 13.5, "curve": [0.916, 37.43, 1.067, 62.45]}, {"time": 1.1667, "value": 62.45, "curve": [1.212, 62.45, 1.27, 53.43]}, {"time": 1.3333, "value": 40.71}]}, "Tentacle2": {"rotate": [{"value": 36.14, "curve": [0.192, 36.14, 0.575, -10.68]}, {"time": 0.7667, "value": -10.68, "curve": [0.908, -10.68, 1.192, 36.14]}, {"time": 1.3333, "value": 36.14}]}}}, "anticipate": {"slots": {"Eyes": {"attachment": [{"name": "EyesClosed"}]}, "images/Eye3": {"attachment": [{}]}, "Mouth": {"attachment": [{"name": "Mouth Closed"}]}, "MouthBack": {"attachment": [{"name": "Mouth Back Shut"}]}, "ScuttleEye3": {"attachment": [{"name": "<PERSON><PERSON><PERSON>_Eye_Closed"}]}}, "bones": {"FACE": {"translate": [{"x": 43.75, "curve": [0.067, -12.02, 0.75, -33.5, 0.067, 3.1, 0.75, 4.29]}, {"time": 1, "x": -33.5, "y": 4.29}], "scale": [{}, {"time": 0.0667, "x": 0.9, "y": 0.9}, {"time": 0.1333}, {"time": 0.2, "x": 0.9, "y": 0.9}, {"time": 0.2667}, {"time": 0.3333, "x": 0.9, "y": 0.9}, {"time": 0.4}, {"time": 0.4667, "x": 0.9, "y": 0.9}, {"time": 0.5333}, {"time": 0.6, "x": 0.9, "y": 0.9}, {"time": 0.6667}, {"time": 0.7333, "x": 0.9, "y": 0.9}, {"time": 0.8}, {"time": 0.8667, "x": 0.9, "y": 0.9}, {"time": 0.9333}, {"time": 1, "x": 0.9, "y": 0.9}, {"time": 1.0667}, {"time": 1.1333, "x": 0.9, "y": 0.9}, {"time": 1.2}, {"time": 1.2667, "x": 0.9, "y": 0.9}, {"time": 1.3333}, {"time": 1.4, "x": 0.9, "y": 0.9}, {"time": 1.4667}, {"time": 1.5333, "x": 0.9, "y": 0.9}, {"time": 1.6}, {"time": 1.6667, "x": 0.9, "y": 0.9}, {"time": 1.7333}, {"time": 1.8, "x": 0.9, "y": 0.9}, {"time": 1.8667}, {"time": 1.9333, "x": 0.9, "y": 0.9}, {"time": 2}, {"time": 2.0667, "x": 0.9, "y": 0.9}, {"time": 2.1333}, {"time": 2.2, "x": 0.9, "y": 0.9}, {"time": 2.2667}, {"time": 2.3333, "x": 0.9, "y": 0.9}, {"time": 2.4}, {"time": 2.4667, "x": 0.9, "y": 0.9}, {"time": 2.5333}, {"time": 2.6, "x": 0.9, "y": 0.9}, {"time": 2.6667}, {"time": 2.7333, "x": 0.9, "y": 0.9}, {"time": 2.8}, {"time": 2.8667, "x": 0.9, "y": 0.9}, {"time": 2.9333}, {"time": 3, "x": 0.9, "y": 0.9}, {"time": 3.0667}, {"time": 3.1333, "x": 0.9, "y": 0.9}, {"time": 3.2}, {"time": 3.2667, "x": 0.9, "y": 0.9}, {"time": 3.3333}, {"time": 3.4, "x": 0.9, "y": 0.9}, {"time": 3.4667}, {"time": 3.5333, "x": 0.9, "y": 0.9}, {"time": 3.6}, {"time": 3.6667, "x": 0.9, "y": 0.9}, {"time": 3.7333}, {"time": 3.8, "x": 0.9, "y": 0.9}]}, "Tentacle2": {"rotate": [{"value": 36.14, "curve": [0.192, 36.14, 0.575, -10.68]}, {"time": 0.7667, "value": -10.68, "curve": [0.908, -10.68, 1.192, 36.14]}, {"time": 1.3333, "value": 36.14, "curve": [1.525, 36.14, 1.908, -10.68]}, {"time": 2.1, "value": -10.68, "curve": [2.242, -10.68, 2.525, 36.14]}, {"time": 2.6667, "value": 36.14, "curve": [2.858, 36.14, 3.242, -10.68]}, {"time": 3.4333, "value": -10.68, "curve": [3.575, -10.68, 3.858, 36.14]}, {"time": 4, "value": 36.14}]}, "Spike2": {"rotate": [{"value": -17.63, "curve": [0.013, -19.61, 0.025, -21.09]}, {"time": 0.0333, "value": -21.09, "curve": [0.058, -21.09, 0.108, -8.89]}, {"time": 0.1333, "value": -8.89, "curve": [0.158, -8.89, 0.208, -21.09]}, {"time": 0.2333, "value": -21.09, "curve": [0.258, -21.09, 0.308, -8.89]}, {"time": 0.3333, "value": -8.89, "curve": [0.358, -8.89, 0.408, -21.09]}, {"time": 0.4333, "value": -21.09, "curve": [0.45, -21.09, 0.483, -8.89]}, {"time": 0.5, "value": -8.89, "curve": [0.525, -8.89, 0.575, -21.09]}, {"time": 0.6, "value": -21.09, "curve": [0.625, -21.09, 0.675, -8.89]}, {"time": 0.7, "value": -8.89, "curve": [0.725, -8.89, 0.775, -21.09]}, {"time": 0.8, "value": -21.09, "curve": [0.825, -21.09, 0.875, -8.89]}, {"time": 0.9, "value": -8.89, "curve": [0.925, -8.89, 0.975, -21.09]}, {"time": 1, "value": -21.09, "curve": [1.025, -21.09, 1.075, -8.89]}, {"time": 1.1, "value": -8.89, "curve": [1.125, -8.89, 1.175, -21.09]}, {"time": 1.2, "value": -21.09, "curve": [1.217, -21.09, 1.25, -8.89]}, {"time": 1.2667, "value": -8.89, "curve": [1.292, -8.89, 1.342, -17.63]}, {"time": 1.3667, "value": -17.63, "curve": [1.405, -12.64, 1.44, -8.89]}, {"time": 1.4667, "value": -8.89, "curve": [1.492, -8.89, 1.542, -21.09]}, {"time": 1.5667, "value": -21.09, "curve": [1.592, -21.09, 1.642, -8.89]}, {"time": 1.6667, "value": -8.89, "curve": [1.692, -8.89, 1.742, -21.09]}, {"time": 1.7667, "value": -21.09, "curve": [1.783, -21.09, 1.817, -8.89]}, {"time": 1.8333, "value": -8.89, "curve": [1.858, -8.89, 1.908, -17.63]}, {"time": 1.9333, "value": -17.63, "curve": [1.972, -12.64, 2.007, -8.89]}, {"time": 2.0333, "value": -8.89, "curve": [2.058, -8.89, 2.108, -21.09]}, {"time": 2.1333, "value": -21.09, "curve": [2.158, -21.09, 2.208, -8.89]}, {"time": 2.2333, "value": -8.89, "curve": [2.258, -8.89, 2.308, -21.09]}, {"time": 2.3333, "value": -21.09, "curve": [2.35, -21.09, 2.383, -8.89]}, {"time": 2.4, "value": -8.89, "curve": [2.424, -8.89, 2.465, -14.14]}, {"time": 2.5, "value": -17.63, "curve": [2.538, -12.64, 2.574, -8.89]}, {"time": 2.6, "value": -8.89, "curve": [2.625, -8.89, 2.675, -21.09]}, {"time": 2.7, "value": -21.09, "curve": [2.725, -21.09, 2.775, -8.89]}, {"time": 2.8, "value": -8.89, "curve": [2.825, -8.89, 2.875, -21.09]}, {"time": 2.9, "value": -21.09, "curve": [2.917, -21.09, 2.95, -8.89]}, {"time": 2.9667, "value": -8.89, "curve": [2.992, -8.89, 3.042, -17.63]}, {"time": 3.0667, "value": -17.63, "curve": [3.105, -12.64, 3.14, -8.89]}, {"time": 3.1667, "value": -8.89, "curve": [3.192, -8.89, 3.242, -21.09]}, {"time": 3.2667, "value": -21.09, "curve": [3.292, -21.09, 3.342, -8.89]}, {"time": 3.3667, "value": -8.89, "curve": [3.392, -8.89, 3.442, -21.09]}, {"time": 3.4667, "value": -21.09, "curve": [3.483, -21.09, 3.517, -8.89]}, {"time": 3.5333, "value": -8.89, "curve": [3.55, -8.89, 3.577, -14.14]}, {"time": 3.6, "value": -17.63}]}, "Tentacle3_Mid2": {"rotate": [{"value": 52.81, "curve": [0.021, 49.53, 0.044, 45.39]}, {"time": 0.0667, "value": 40.71, "curve": [0.204, 14.47, 0.37, -30.13]}, {"time": 0.4667, "value": -30.13, "curve": [0.658, -30.13, 1.042, 62.45]}, {"time": 1.2333, "value": 62.45, "curve": [1.262, 62.45, 1.296, 58.81]}, {"time": 1.3333, "value": 52.81, "curve": [1.354, 49.53, 1.377, 45.39]}, {"time": 1.4, "value": 40.71, "curve": [1.537, 14.47, 1.703, -30.13]}, {"time": 1.8, "value": -30.13, "curve": [1.992, -30.13, 2.375, 62.45]}, {"time": 2.5667, "value": 62.45, "curve": [2.596, 62.45, 2.629, 58.81]}, {"time": 2.6667, "value": 52.81, "curve": [2.688, 49.53, 2.71, 45.39]}, {"time": 2.7333, "value": 40.71, "curve": [2.871, 14.47, 3.036, -30.13]}, {"time": 3.1333, "value": -30.13, "curve": [3.325, -30.13, 3.708, 62.45]}, {"time": 3.9, "value": 62.45, "curve": [3.929, 62.45, 3.963, 58.81]}, {"time": 4, "value": 52.81}]}, "Tentacle3_Btm2": {"rotate": [{"value": 60.88, "curve": [0.02, 59.21, 0.043, 55.98]}, {"time": 0.0667, "value": 51.6, "curve": [0.213, 27.35, 0.42, -41.36]}, {"time": 0.5333, "value": -41.36, "curve": [0.725, -41.36, 1.108, 62.45]}, {"time": 1.3, "value": 62.45, "curve": [1.311, 62.45, 1.322, 61.9]}, {"time": 1.3333, "value": 60.88, "curve": [1.354, 59.21, 1.376, 55.98]}, {"time": 1.4, "value": 51.6, "curve": [1.546, 27.35, 1.753, -41.36]}, {"time": 1.8667, "value": -41.36, "curve": [2.058, -41.36, 2.442, 62.45]}, {"time": 2.6333, "value": 62.45, "curve": [2.644, 62.45, 2.655, 61.9]}, {"time": 2.6667, "value": 60.88, "curve": [2.687, 59.21, 2.71, 55.98]}, {"time": 2.7333, "value": 51.6, "curve": [2.88, 27.35, 3.087, -41.36]}, {"time": 3.2, "value": -41.36, "curve": [3.392, -41.36, 3.775, 62.45]}, {"time": 3.9667, "value": 62.45, "curve": [3.977, 62.45, 3.988, 61.9]}, {"time": 4, "value": 60.88}]}, "Tentacle2_Btm2": {"rotate": [{"value": 55.68, "curve": [0.148, 34.28, 0.378, -56.67]}, {"time": 0.5, "value": -56.67, "curve": [0.692, -56.67, 1.075, 62.45]}, {"time": 1.2667, "value": 62.45, "curve": [1.287, 62.45, 1.309, 60]}, {"time": 1.3333, "value": 55.68, "curve": [1.481, 34.28, 1.711, -56.67]}, {"time": 1.8333, "value": -56.67, "curve": [2.025, -56.67, 2.408, 62.45]}, {"time": 2.6, "value": 62.45, "curve": [2.62, 62.45, 2.642, 60]}, {"time": 2.6667, "value": 55.68, "curve": [2.814, 34.28, 3.044, -56.67]}, {"time": 3.1667, "value": -56.67, "curve": [3.358, -56.67, 3.742, 62.45]}, {"time": 3.9333, "value": 62.45, "curve": [3.953, 62.45, 3.976, 60]}, {"time": 4, "value": 55.68}]}, "Spike7": {"rotate": [{"value": 14.37, "curve": [0.025, 14.37, 0.075, 6.42]}, {"time": 0.1, "value": 6.42, "curve": [0.125, 6.42, 0.175, 14.37]}, {"time": 0.2, "value": 14.37, "curve": [0.225, 14.37, 0.275, 6.42]}, {"time": 0.3, "value": 6.42, "curve": [0.325, 6.42, 0.375, 14.37]}, {"time": 0.4, "value": 14.37, "curve": [0.417, 14.37, 0.45, 6.42]}, {"time": 0.4667, "value": 6.42, "curve": [0.492, 6.42, 0.542, 14.37]}, {"time": 0.5667, "value": 14.37, "curve": [0.592, 14.37, 0.642, 6.42]}, {"time": 0.6667, "value": 6.42, "curve": [0.692, 6.42, 0.742, 14.37]}, {"time": 0.7667, "value": 14.37, "curve": [0.792, 14.37, 0.842, 6.42]}, {"time": 0.8667, "value": 6.42, "curve": [0.892, 6.42, 0.942, 14.37]}, {"time": 0.9667, "value": 14.37, "curve": [0.992, 14.37, 1.042, 6.42]}, {"time": 1.0667, "value": 6.42, "curve": [1.092, 6.42, 1.142, 14.37]}, {"time": 1.1667, "value": 14.37, "curve": [1.183, 14.37, 1.217, 6.42]}, {"time": 1.2333, "value": 6.42, "curve": [1.258, 6.42, 1.308, 14.37]}, {"time": 1.3333, "value": 14.37, "curve": [1.358, 14.37, 1.408, 6.42]}, {"time": 1.4333, "value": 6.42, "curve": [1.458, 6.42, 1.508, 14.37]}, {"time": 1.5333, "value": 14.37, "curve": [1.558, 14.37, 1.608, 6.42]}, {"time": 1.6333, "value": 6.42, "curve": [1.658, 6.42, 1.708, 14.37]}, {"time": 1.7333, "value": 14.37, "curve": [1.75, 14.37, 1.783, 6.42]}, {"time": 1.8, "value": 6.42, "curve": [1.825, 6.42, 1.875, 14.37]}, {"time": 1.9, "value": 14.37, "curve": [1.925, 14.37, 1.975, 6.42]}, {"time": 2, "value": 6.42, "curve": [2.025, 6.42, 2.075, 14.37]}, {"time": 2.1, "value": 14.37, "curve": [2.125, 14.37, 2.175, 6.42]}, {"time": 2.2, "value": 6.42, "curve": [2.225, 6.42, 2.275, 14.37]}, {"time": 2.3, "value": 14.37, "curve": [2.317, 14.37, 2.35, 6.42]}, {"time": 2.3667, "value": 6.42, "curve": [2.392, 6.42, 2.442, 14.37]}, {"time": 2.4667, "value": 14.37, "curve": [2.492, 14.37, 2.542, 6.42]}, {"time": 2.5667, "value": 6.42, "curve": [2.592, 6.42, 2.642, 14.37]}, {"time": 2.6667, "value": 14.37, "curve": [2.692, 14.37, 2.742, 6.42]}, {"time": 2.7667, "value": 6.42, "curve": [2.792, 6.42, 2.842, 14.37]}, {"time": 2.8667, "value": 14.37, "curve": [2.883, 14.37, 2.917, 6.42]}, {"time": 2.9333, "value": 6.42, "curve": [2.958, 6.42, 3.008, 14.37]}, {"time": 3.0333, "value": 14.37, "curve": [3.058, 14.37, 3.108, 6.42]}, {"time": 3.1333, "value": 6.42, "curve": [3.158, 6.42, 3.208, 14.37]}, {"time": 3.2333, "value": 14.37, "curve": [3.258, 14.37, 3.308, 6.42]}, {"time": 3.3333, "value": 6.42, "curve": [3.358, 6.42, 3.408, 14.37]}, {"time": 3.4333, "value": 14.37, "curve": [3.45, 14.37, 3.483, 6.42]}, {"time": 3.5, "value": 6.42, "curve": [3.525, 6.42, 3.575, 14.37]}, {"time": 3.6, "value": 14.37}]}, "Tentacle2_Btm": {"rotate": [{"value": 47.08, "curve": [0.148, 25.7, 0.378, -65.15]}, {"time": 0.5, "value": -65.15, "curve": [0.692, -65.15, 1.075, 53.84]}, {"time": 1.2667, "value": 53.84, "curve": [1.287, 53.84, 1.309, 51.4]}, {"time": 1.3333, "value": 47.08, "curve": [1.481, 25.7, 1.711, -65.15]}, {"time": 1.8333, "value": -65.15, "curve": [2.025, -65.15, 2.408, 53.84]}, {"time": 2.6, "value": 53.84, "curve": [2.62, 53.84, 2.642, 51.4]}, {"time": 2.6667, "value": 47.08, "curve": [2.814, 25.7, 3.044, -65.15]}, {"time": 3.1667, "value": -65.15, "curve": [3.358, -65.15, 3.742, 53.84]}, {"time": 3.9333, "value": 53.84, "curve": [3.953, 53.84, 3.976, 51.4]}, {"time": 4, "value": 47.08}]}, "Spike6": {"rotate": [{"value": 16.3, "curve": [0.013, 17.59, 0.025, 18.56]}, {"time": 0.0333, "value": 18.56, "curve": [0.058, 18.56, 0.108, 10.61]}, {"time": 0.1333, "value": 10.61, "curve": [0.158, 10.61, 0.208, 18.56]}, {"time": 0.2333, "value": 18.56, "curve": [0.258, 18.56, 0.308, 10.61]}, {"time": 0.3333, "value": 10.61, "curve": [0.358, 10.61, 0.408, 18.56]}, {"time": 0.4333, "value": 18.56, "curve": [0.45, 18.56, 0.483, 10.61]}, {"time": 0.5, "value": 10.61, "curve": [0.525, 10.61, 0.575, 18.56]}, {"time": 0.6, "value": 18.56, "curve": [0.625, 18.56, 0.675, 10.61]}, {"time": 0.7, "value": 10.61, "curve": [0.725, 10.61, 0.775, 18.56]}, {"time": 0.8, "value": 18.56, "curve": [0.825, 18.56, 0.875, 10.61]}, {"time": 0.9, "value": 10.61, "curve": [0.925, 10.61, 0.975, 18.56]}, {"time": 1, "value": 18.56, "curve": [1.025, 18.56, 1.075, 10.61]}, {"time": 1.1, "value": 10.61, "curve": [1.125, 10.61, 1.175, 18.56]}, {"time": 1.2, "value": 18.56, "curve": [1.217, 18.56, 1.25, 10.61]}, {"time": 1.2667, "value": 10.61, "curve": [1.292, 10.61, 1.342, 16.3]}, {"time": 1.3667, "value": 16.3, "curve": [1.405, 13.05, 1.44, 10.61]}, {"time": 1.4667, "value": 10.61, "curve": [1.492, 10.61, 1.542, 18.56]}, {"time": 1.5667, "value": 18.56, "curve": [1.592, 18.56, 1.642, 10.61]}, {"time": 1.6667, "value": 10.61, "curve": [1.692, 10.61, 1.742, 18.56]}, {"time": 1.7667, "value": 18.56, "curve": [1.783, 18.56, 1.817, 10.61]}, {"time": 1.8333, "value": 10.61, "curve": [1.858, 10.61, 1.908, 16.3]}, {"time": 1.9333, "value": 16.3, "curve": [1.972, 13.05, 2.007, 10.61]}, {"time": 2.0333, "value": 10.61, "curve": [2.058, 10.61, 2.108, 18.56]}, {"time": 2.1333, "value": 18.56, "curve": [2.158, 18.56, 2.208, 10.61]}, {"time": 2.2333, "value": 10.61, "curve": [2.258, 10.61, 2.308, 18.56]}, {"time": 2.3333, "value": 18.56, "curve": [2.35, 18.56, 2.383, 10.61]}, {"time": 2.4, "value": 10.61, "curve": [2.424, 10.61, 2.465, 14.03]}, {"time": 2.5, "value": 16.3, "curve": [2.538, 13.05, 2.574, 10.61]}, {"time": 2.6, "value": 10.61, "curve": [2.625, 10.61, 2.675, 18.56]}, {"time": 2.7, "value": 18.56, "curve": [2.725, 18.56, 2.775, 10.61]}, {"time": 2.8, "value": 10.61, "curve": [2.825, 10.61, 2.875, 18.56]}, {"time": 2.9, "value": 18.56, "curve": [2.917, 18.56, 2.95, 10.61]}, {"time": 2.9667, "value": 10.61, "curve": [2.992, 10.61, 3.042, 16.3]}, {"time": 3.0667, "value": 16.3, "curve": [3.105, 13.05, 3.14, 10.61]}, {"time": 3.1667, "value": 10.61, "curve": [3.192, 10.61, 3.242, 18.56]}, {"time": 3.2667, "value": 18.56, "curve": [3.292, 18.56, 3.342, 10.61]}, {"time": 3.3667, "value": 10.61, "curve": [3.392, 10.61, 3.442, 18.56]}, {"time": 3.4667, "value": 18.56, "curve": [3.483, 18.56, 3.517, 10.61]}, {"time": 3.5333, "value": 10.61, "curve": [3.55, 10.61, 3.577, 14.03]}, {"time": 3.6, "value": 16.3}]}, "Mouth7": {"rotate": [{"value": -92.16, "curve": [0.1, -92.16, 0.3, 48.3]}, {"time": 0.4, "value": 48.3, "curve": [0.592, 48.3, 0.975, -100.21]}, {"time": 1.1667, "value": -100.21}]}, "MAIN": {"translate": [{"y": 17.53, "curve": [0.31, 0, 0.333, 0, 0.31, 17.53, 0.333, -32.5]}, {"time": 0.6667, "y": -33.8, "curve": [0.976, 0, 1, 0, 0.976, -33.8, 1, 16.22]}, {"time": 1.3333, "y": 17.53, "curve": [1.643, 0, 1.667, 0, 1.643, 17.53, 1.667, -32.5]}, {"time": 2, "y": -33.8, "curve": [2.31, 0, 2.333, 0, 2.31, -33.8, 2.333, 16.22]}, {"time": 2.6667, "y": 17.53, "curve": [2.976, 0, 3, 0, 2.976, 17.53, 3, -32.5]}, {"time": 3.3333, "y": -33.8, "curve": [3.643, 0, 3.667, 0, 3.643, -33.8, 3.667, 16.22]}, {"time": 4, "y": 17.53}], "scale": [{"x": 0.987, "y": 1.006, "curve": [0.125, 0.962, 0.25, 0.937, 0.125, 1.028, 0.25, 1.05]}, {"time": 0.3333, "x": 0.937, "y": 1.05, "curve": [0.5, 0.937, 0.833, 1.038, 0.5, 1.05, 0.833, 0.962]}, {"time": 1, "x": 1.038, "y": 0.962, "curve": [1.083, 1.038, 1.208, 1.013, 1.083, 0.962, 1.208, 0.984]}, {"time": 1.3333, "x": 0.987, "y": 1.006, "curve": [1.458, 0.962, 1.583, 0.937, 1.458, 1.028, 1.583, 1.05]}, {"time": 1.6667, "x": 0.937, "y": 1.05, "curve": [1.833, 0.937, 2.167, 1.038, 1.833, 1.05, 2.167, 0.962]}, {"time": 2.3333, "x": 1.038, "y": 0.962, "curve": [2.417, 1.038, 2.542, 1.013, 2.417, 0.962, 2.542, 0.984]}, {"time": 2.6667, "x": 0.987, "y": 1.006, "curve": [2.792, 0.962, 2.917, 0.937, 2.792, 1.028, 2.917, 1.05]}, {"time": 3, "x": 0.937, "y": 1.05, "curve": [3.167, 0.937, 3.5, 1.038, 3.167, 1.05, 3.5, 0.962]}, {"time": 3.6667, "x": 1.038, "y": 0.962, "curve": [3.75, 1.038, 3.875, 1.013, 3.75, 0.962, 3.875, 0.984]}, {"time": 4, "x": 0.987, "y": 1.006}]}, "Tentacle3_Btm3": {"rotate": [{"value": 61.89, "curve": [0.146, 35.84, 0.353, -37.96]}, {"time": 0.4667, "value": -37.96, "curve": [0.658, -37.96, 1.042, 73.54]}, {"time": 1.2333, "value": 73.54, "curve": [1.262, 73.54, 1.296, 69.14]}, {"time": 1.3333, "value": 61.89, "curve": [1.48, 35.84, 1.687, -37.96]}, {"time": 1.8, "value": -37.96, "curve": [1.992, -37.96, 2.375, 73.54]}, {"time": 2.5667, "value": 73.54, "curve": [2.596, 73.54, 2.629, 69.14]}, {"time": 2.6667, "value": 61.89, "curve": [2.813, 35.84, 3.02, -37.96]}, {"time": 3.1333, "value": -37.96, "curve": [3.325, -37.96, 3.708, 73.54]}, {"time": 3.9, "value": 73.54, "curve": [3.929, 73.54, 3.963, 69.14]}, {"time": 4, "value": 61.89}]}, "Spike5": {"rotate": [{"value": 0.22, "curve": [0.025, 0.22, 0.075, 12.42]}, {"time": 0.1, "value": 12.42, "curve": [0.125, 12.42, 0.175, 0.22]}, {"time": 0.2, "value": 0.22, "curve": [0.225, 0.22, 0.275, 12.42]}, {"time": 0.3, "value": 12.42, "curve": [0.325, 12.42, 0.375, 0.22]}, {"time": 0.4, "value": 0.22, "curve": [0.417, 0.22, 0.45, 12.42]}, {"time": 0.4667, "value": 12.42, "curve": [0.492, 12.42, 0.542, 0.22]}, {"time": 0.5667, "value": 0.22, "curve": [0.592, 0.22, 0.642, 12.42]}, {"time": 0.6667, "value": 12.42, "curve": [0.692, 12.42, 0.742, 0.22]}, {"time": 0.7667, "value": 0.22, "curve": [0.792, 0.22, 0.842, 12.42]}, {"time": 0.8667, "value": 12.42, "curve": [0.892, 12.42, 0.942, 0.22]}, {"time": 0.9667, "value": 0.22, "curve": [0.992, 0.22, 1.042, 12.42]}, {"time": 1.0667, "value": 12.42, "curve": [1.092, 12.42, 1.142, 0.22]}, {"time": 1.1667, "value": 0.22, "curve": [1.183, 0.22, 1.217, 12.42]}, {"time": 1.2333, "value": 12.42, "curve": [1.258, 12.42, 1.308, 0.22]}, {"time": 1.3333, "value": 0.22}]}, "Tentacle3_Mid": {"rotate": [{"value": 51.86, "curve": [0.03, 48.48, 0.064, 42.9]}, {"time": 0.1, "value": 36.14, "curve": [0.237, 11.69, 0.403, -29.86]}, {"time": 0.5, "value": -29.86, "curve": [0.692, -29.86, 1.075, 56.4]}, {"time": 1.2667, "value": 56.4, "curve": [1.287, 56.4, 1.309, 54.76]}, {"time": 1.3333, "value": 51.86, "curve": [1.364, 48.48, 1.398, 42.9]}, {"time": 1.4333, "value": 36.14, "curve": [1.571, 11.69, 1.736, -29.86]}, {"time": 1.8333, "value": -29.86, "curve": [2.025, -29.86, 2.408, 56.4]}, {"time": 2.6, "value": 56.4, "curve": [2.62, 56.4, 2.642, 54.76]}, {"time": 2.6667, "value": 51.86, "curve": [2.697, 48.48, 2.731, 42.9]}, {"time": 2.7667, "value": 36.14, "curve": [2.904, 11.69, 3.07, -29.86]}, {"time": 3.1667, "value": -29.86, "curve": [3.358, -29.86, 3.742, 56.4]}, {"time": 3.9333, "value": 56.4, "curve": [3.953, 56.4, 3.976, 54.76]}, {"time": 4, "value": 51.86}]}, "Tentacle6": {"rotate": [{"value": 36.14, "curve": [0.192, 36.14, 0.575, -10.68]}, {"time": 0.7667, "value": -10.68, "curve": [0.908, -10.68, 1.192, 36.14]}, {"time": 1.3333, "value": 36.14, "curve": [1.525, 36.14, 1.908, -10.68]}, {"time": 2.1, "value": -10.68, "curve": [2.242, -10.68, 2.525, 36.14]}, {"time": 2.6667, "value": 36.14, "curve": [2.858, 36.14, 3.242, -10.68]}, {"time": 3.4333, "value": -10.68, "curve": [3.575, -10.68, 3.858, 36.14]}, {"time": 4, "value": 36.14}]}, "Mouth5": {"rotate": [{"value": 78.81, "curve": [0.1, 78.81, 0.3, -48.27]}, {"time": 0.4, "value": -48.27, "curve": [0.592, -48.27, 0.975, 107.9]}, {"time": 1.1667, "value": 107.9}]}, "Tentacle3_Mid3": {"rotate": [{"value": 36.14, "curve": [0.137, 11.69, 0.303, -29.86]}, {"time": 0.4, "value": -29.86, "curve": [0.592, -29.86, 0.975, 56.4]}, {"time": 1.1667, "value": 56.4, "curve": [1.212, 56.4, 1.27, 48]}, {"time": 1.3333, "value": 36.14, "curve": [1.471, 11.69, 1.636, -29.86]}, {"time": 1.7333, "value": -29.86, "curve": [1.925, -29.86, 2.308, 56.4]}, {"time": 2.5, "value": 56.4, "curve": [2.545, 56.4, 2.603, 48]}, {"time": 2.6667, "value": 36.14, "curve": [2.804, 11.69, 2.97, -29.86]}, {"time": 3.0667, "value": -29.86, "curve": [3.258, -29.86, 3.642, 56.4]}, {"time": 3.8333, "value": 56.4, "curve": [3.878, 56.4, 3.936, 48]}, {"time": 4, "value": 36.14}]}, "DangleHandle": {"rotate": [{"value": 6.7, "curve": [0.043, 3.66, 0.088, 0.04]}, {"time": 0.1333, "value": -3.58, "curve": [0.258, -13.51, 0.383, -23.45]}, {"time": 0.4667, "value": -23.45, "curve": [0.633, -23.45, 0.967, 16.3]}, {"time": 1.1333, "value": 16.3, "curve": [1.187, 16.3, 1.257, 12.3]}, {"time": 1.3333, "value": 6.7}], "translate": [{"x": -3.96, "y": 2.8, "curve": [0.125, -27.88, 0.25, -51.8, 0.125, 1.82, 0.25, 0.85]}, {"time": 0.3333, "x": -51.8, "y": 0.85, "curve": [0.5, -51.8, 0.833, 43.87, 0.5, 0.85, 0.833, 4.74]}, {"time": 1, "x": 43.87, "y": 4.74, "curve": [1.083, 43.87, 1.208, 19.95, 1.083, 4.74, 1.208, 3.77]}, {"time": 1.3333, "x": -3.96, "y": 2.8}], "scale": [{"y": 1.723, "curve": [0.167, 1, 0.5, 1, 0.167, 1.723, 0.5, 0.313]}, {"time": 0.6667, "y": 0.313, "curve": [0.833, 1, 1.167, 1, 0.833, 0.313, 1.167, 1.723]}, {"time": 1.3333, "y": 1.723}]}, "Mouth8": {"rotate": [{"value": -82.52, "curve": [0.1, -82.52, 0.3, 55.9]}, {"time": 0.4, "value": 55.9, "curve": [0.592, 55.9, 0.975, -82.52]}, {"time": 1.1667, "value": -82.52}]}, "Spike8": {"rotate": [{"value": -9.22, "curve": [0.013, -11.2, 0.025, -13.19]}, {"time": 0.0333, "value": -13.19, "curve": [0.058, -13.19, 0.108, -5.25]}, {"time": 0.1333, "value": -5.25, "curve": [0.158, -5.25, 0.208, -13.19]}, {"time": 0.2333, "value": -13.19, "curve": [0.258, -13.19, 0.308, -5.25]}, {"time": 0.3333, "value": -5.25, "curve": [0.358, -5.25, 0.408, -13.19]}, {"time": 0.4333, "value": -13.19, "curve": [0.458, -13.19, 0.508, -5.25]}, {"time": 0.5333, "value": -5.25, "curve": [0.55, -5.25, 0.583, -13.19]}, {"time": 0.6, "value": -13.19, "curve": [0.625, -13.19, 0.675, -5.25]}, {"time": 0.7, "value": -5.25, "curve": [0.725, -5.25, 0.775, -13.19]}, {"time": 0.8, "value": -13.19, "curve": [0.825, -13.19, 0.875, -5.25]}, {"time": 0.9, "value": -5.25, "curve": [0.925, -5.25, 0.975, -13.19]}, {"time": 1, "value": -13.19, "curve": [1.025, -13.19, 1.075, -5.25]}, {"time": 1.1, "value": -5.25, "curve": [1.125, -5.25, 1.175, -13.19]}, {"time": 1.2, "value": -13.19, "curve": [1.225, -13.19, 1.275, -5.25]}, {"time": 1.3, "value": -5.25, "curve": [1.308, -5.25, 1.321, -7.23]}, {"time": 1.3333, "value": -9.22}]}, "Tentacle2_Mid": {"rotate": [{"value": 16.89, "curve": [0.121, -7.13, 0.251, -36.3]}, {"time": 0.3333, "value": -36.3, "curve": [0.525, -36.3, 0.908, 49.96]}, {"time": 1.1, "value": 49.96, "curve": [1.16, 49.96, 1.244, 34.77]}, {"time": 1.3333, "value": 16.89, "curve": [1.455, -7.13, 1.585, -36.3]}, {"time": 1.6667, "value": -36.3, "curve": [1.858, -36.3, 2.242, 49.96]}, {"time": 2.4333, "value": 49.96, "curve": [2.493, 49.96, 2.578, 34.77]}, {"time": 2.6667, "value": 16.89, "curve": [2.788, -7.13, 2.918, -36.3]}, {"time": 3, "value": -36.3, "curve": [3.192, -36.3, 3.575, 49.96]}, {"time": 3.7667, "value": 49.96, "curve": [3.827, 49.96, 3.911, 34.77]}, {"time": 4, "value": 16.89}]}, "Tentacles": {"scale": [{"x": 0.699}, {"time": 0.6667, "x": 1.199}, {"time": 1.3333, "x": 0.699}, {"time": 2, "x": 1.199}, {"time": 2.6667, "x": 0.699}, {"time": 3.3333, "x": 1.199}, {"time": 4, "x": 0.699}]}, "Tentacle2_Mid2": {"rotate": [{"value": 26.96, "curve": [0.121, 1.18, 0.251, -30.13]}, {"time": 0.3333, "value": -30.13, "curve": [0.525, -30.13, 0.908, 62.45]}, {"time": 1.1, "value": 62.45, "curve": [1.16, 62.45, 1.244, 46.14]}, {"time": 1.3333, "value": 26.96, "curve": [1.455, 1.18, 1.585, -30.13]}, {"time": 1.6667, "value": -30.13, "curve": [1.858, -30.13, 2.242, 62.45]}, {"time": 2.4333, "value": 62.45, "curve": [2.493, 62.45, 2.578, 46.14]}, {"time": 2.6667, "value": 26.96, "curve": [2.788, 1.18, 2.918, -30.13]}, {"time": 3, "value": -30.13, "curve": [3.192, -30.13, 3.575, 62.45]}, {"time": 3.7667, "value": 62.45, "curve": [3.827, 62.45, 3.911, 46.14]}, {"time": 4, "value": 26.96}]}, "Spike9": {"rotate": [{"value": -4.45, "curve": [0.025, -4.45, 0.075, -12.39]}, {"time": 0.1, "value": -12.39, "curve": [0.125, -12.39, 0.175, -4.45]}, {"time": 0.2, "value": -4.45, "curve": [0.225, -4.45, 0.275, -12.39]}, {"time": 0.3, "value": -12.39, "curve": [0.325, -12.39, 0.375, -4.45]}, {"time": 0.4, "value": -4.45, "curve": [0.417, -4.45, 0.45, -12.39]}, {"time": 0.4667, "value": -12.39, "curve": [0.492, -12.39, 0.542, -4.45]}, {"time": 0.5667, "value": -4.45, "curve": [0.592, -4.45, 0.642, -12.39]}, {"time": 0.6667, "value": -12.39, "curve": [0.692, -12.39, 0.742, -4.45]}, {"time": 0.7667, "value": -4.45, "curve": [0.792, -4.45, 0.842, -12.39]}, {"time": 0.8667, "value": -12.39, "curve": [0.892, -12.39, 0.942, -4.45]}, {"time": 0.9667, "value": -4.45, "curve": [0.992, -4.45, 1.042, -12.39]}, {"time": 1.0667, "value": -12.39, "curve": [1.092, -12.39, 1.142, -4.45]}, {"time": 1.1667, "value": -4.45, "curve": [1.183, -4.45, 1.217, -12.39]}, {"time": 1.2333, "value": -12.39, "curve": [1.258, -12.39, 1.308, -4.45]}, {"time": 1.3333, "value": -4.45}]}, "Spike1": {"rotate": [{"value": -23.57, "curve": [0.075, -23.57, 0.225, 10.75]}, {"time": 0.3, "value": 10.75, "curve": [0.392, 10.75, 0.575, -11.37]}, {"time": 0.6667, "value": -11.37, "curve": [0.692, -11.37, 0.742, -23.57]}, {"time": 0.7667, "value": -23.57, "curve": [0.792, -23.57, 0.842, -11.37]}, {"time": 0.8667, "value": -11.37, "curve": [0.892, -11.37, 0.942, -23.57]}, {"time": 0.9667, "value": -23.57, "curve": [0.992, -23.57, 1.042, -11.37]}, {"time": 1.0667, "value": -11.37, "curve": [1.092, -11.37, 1.142, -23.57]}, {"time": 1.1667, "value": -23.57, "curve": [1.183, -23.57, 1.217, -11.37]}, {"time": 1.2333, "value": -11.37, "curve": [1.258, -11.37, 1.308, -23.57]}, {"time": 1.3333, "value": -23.57, "curve": [1.358, -23.57, 1.408, -11.37]}, {"time": 1.4333, "value": -11.37, "curve": [1.458, -11.37, 1.508, -23.57]}, {"time": 1.5333, "value": -23.57, "curve": [1.558, -23.57, 1.608, -11.37]}, {"time": 1.6333, "value": -11.37, "curve": [1.658, -11.37, 1.708, -23.57]}, {"time": 1.7333, "value": -23.57, "curve": [1.75, -23.57, 1.783, -11.37]}, {"time": 1.8, "value": -11.37, "curve": [1.825, -11.37, 1.875, -23.57]}, {"time": 1.9, "value": -23.57, "curve": [1.925, -23.57, 1.975, -11.37]}, {"time": 2, "value": -11.37, "curve": [2.025, -11.37, 2.075, -23.57]}, {"time": 2.1, "value": -23.57, "curve": [2.125, -23.57, 2.175, -11.37]}, {"time": 2.2, "value": -11.37, "curve": [2.225, -11.37, 2.275, -23.57]}, {"time": 2.3, "value": -23.57, "curve": [2.317, -23.57, 2.35, -11.37]}, {"time": 2.3667, "value": -11.37, "curve": [2.392, -11.37, 2.442, -23.57]}, {"time": 2.4667, "value": -23.57, "curve": [2.492, -23.57, 2.542, -11.37]}, {"time": 2.5667, "value": -11.37, "curve": [2.592, -11.37, 2.642, -23.57]}, {"time": 2.6667, "value": -23.57, "curve": [2.692, -23.57, 2.742, -11.37]}, {"time": 2.7667, "value": -11.37, "curve": [2.792, -11.37, 2.842, -23.57]}, {"time": 2.8667, "value": -23.57, "curve": [2.883, -23.57, 2.917, -11.37]}, {"time": 2.9333, "value": -11.37, "curve": [2.958, -11.37, 3.008, -23.57]}, {"time": 3.0333, "value": -23.57, "curve": [3.058, -23.57, 3.108, -11.37]}, {"time": 3.1333, "value": -11.37, "curve": [3.158, -11.37, 3.208, -23.57]}, {"time": 3.2333, "value": -23.57, "curve": [3.258, -23.57, 3.308, -11.37]}, {"time": 3.3333, "value": -11.37, "curve": [3.358, -11.37, 3.408, -23.57]}, {"time": 3.4333, "value": -23.57, "curve": [3.45, -23.57, 3.483, -11.37]}, {"time": 3.5, "value": -11.37, "curve": [3.525, -11.37, 3.575, -23.57]}, {"time": 3.6, "value": -23.57}]}, "Tentacle4": {"rotate": [{"value": 26.28, "curve": [0.192, 26.28, 0.575, -10.68]}, {"time": 0.7667, "value": -10.68, "curve": [0.908, -10.68, 1.192, 26.28]}, {"time": 1.3333, "value": 26.28, "curve": [1.525, 26.28, 1.908, -10.68]}, {"time": 2.1, "value": -10.68, "curve": [2.242, -10.68, 2.525, 26.28]}, {"time": 2.6667, "value": 26.28, "curve": [2.858, 26.28, 3.242, -10.68]}, {"time": 3.4333, "value": -10.68, "curve": [3.575, -10.68, 3.858, 26.28]}, {"time": 4, "value": 26.28}]}, "Spike4": {"rotate": [{"value": 7.32, "curve": [0.013, 10.37, 0.025, 13.42]}, {"time": 0.0333, "value": 13.42, "curve": [0.058, 13.42, 0.108, 1.22]}, {"time": 0.1333, "value": 1.22, "curve": [0.158, 1.22, 0.208, 13.42]}, {"time": 0.2333, "value": 13.42, "curve": [0.258, 13.42, 0.308, 1.22]}, {"time": 0.3333, "value": 1.22, "curve": [0.358, 1.22, 0.408, 13.42]}, {"time": 0.4333, "value": 13.42, "curve": [0.458, 13.42, 0.508, 1.22]}, {"time": 0.5333, "value": 1.22, "curve": [0.55, 1.22, 0.583, 13.42]}, {"time": 0.6, "value": 13.42, "curve": [0.625, 13.42, 0.675, 1.22]}, {"time": 0.7, "value": 1.22, "curve": [0.725, 1.22, 0.775, 13.42]}, {"time": 0.8, "value": 13.42, "curve": [0.825, 13.42, 0.875, 1.22]}, {"time": 0.9, "value": 1.22, "curve": [0.925, 1.22, 0.975, 13.42]}, {"time": 1, "value": 13.42, "curve": [1.025, 13.42, 1.075, 1.22]}, {"time": 1.1, "value": 1.22, "curve": [1.125, 1.22, 1.175, 13.42]}, {"time": 1.2, "value": 13.42, "curve": [1.225, 13.42, 1.275, 1.22]}, {"time": 1.3, "value": 1.22, "curve": [1.308, 1.22, 1.321, 4.27]}, {"time": 1.3333, "value": 7.32}]}, "Spike3": {"rotate": [{"value": -15.85, "curve": [0.025, -15.85, 0.075, -3.65]}, {"time": 0.1, "value": -3.65, "curve": [0.125, -3.65, 0.175, -15.85]}, {"time": 0.2, "value": -15.85, "curve": [0.225, -15.85, 0.275, -3.65]}, {"time": 0.3, "value": -3.65, "curve": [0.325, -3.65, 0.375, -15.85]}, {"time": 0.4, "value": -15.85, "curve": [0.417, -15.85, 0.45, -3.65]}, {"time": 0.4667, "value": -3.65, "curve": [0.492, -3.65, 0.542, -15.85]}, {"time": 0.5667, "value": -15.85, "curve": [0.592, -15.85, 0.642, -3.65]}, {"time": 0.6667, "value": -3.65, "curve": [0.692, -3.65, 0.742, -15.85]}, {"time": 0.7667, "value": -15.85, "curve": [0.792, -15.85, 0.842, -3.65]}, {"time": 0.8667, "value": -3.65, "curve": [0.892, -3.65, 0.942, -15.85]}, {"time": 0.9667, "value": -15.85, "curve": [0.992, -15.85, 1.042, -3.65]}, {"time": 1.0667, "value": -3.65, "curve": [1.092, -3.65, 1.142, -15.85]}, {"time": 1.1667, "value": -15.85, "curve": [1.183, -15.85, 1.217, -3.65]}, {"time": 1.2333, "value": -3.65, "curve": [1.258, -3.65, 1.308, -15.85]}, {"time": 1.3333, "value": -15.85, "curve": [1.358, -15.85, 1.408, -3.65]}, {"time": 1.4333, "value": -3.65, "curve": [1.458, -3.65, 1.508, -15.85]}, {"time": 1.5333, "value": -15.85, "curve": [1.558, -15.85, 1.608, -3.65]}, {"time": 1.6333, "value": -3.65, "curve": [1.658, -3.65, 1.708, -15.85]}, {"time": 1.7333, "value": -15.85, "curve": [1.75, -15.85, 1.783, -3.65]}, {"time": 1.8, "value": -3.65, "curve": [1.825, -3.65, 1.875, -15.85]}, {"time": 1.9, "value": -15.85, "curve": [1.925, -15.85, 1.975, -3.65]}, {"time": 2, "value": -3.65, "curve": [2.025, -3.65, 2.075, -15.85]}, {"time": 2.1, "value": -15.85, "curve": [2.125, -15.85, 2.175, -3.65]}, {"time": 2.2, "value": -3.65, "curve": [2.225, -3.65, 2.275, -15.85]}, {"time": 2.3, "value": -15.85, "curve": [2.317, -15.85, 2.35, -3.65]}, {"time": 2.3667, "value": -3.65, "curve": [2.392, -3.65, 2.442, -15.85]}, {"time": 2.4667, "value": -15.85, "curve": [2.492, -15.85, 2.542, -3.65]}, {"time": 2.5667, "value": -3.65, "curve": [2.592, -3.65, 2.642, -15.85]}, {"time": 2.6667, "value": -15.85, "curve": [2.692, -15.85, 2.742, -3.65]}, {"time": 2.7667, "value": -3.65, "curve": [2.792, -3.65, 2.842, -15.85]}, {"time": 2.8667, "value": -15.85, "curve": [2.883, -15.85, 2.917, -3.65]}, {"time": 2.9333, "value": -3.65, "curve": [2.958, -3.65, 3.008, -15.85]}, {"time": 3.0333, "value": -15.85, "curve": [3.058, -15.85, 3.108, -3.65]}, {"time": 3.1333, "value": -3.65, "curve": [3.158, -3.65, 3.208, -15.85]}, {"time": 3.2333, "value": -15.85, "curve": [3.258, -15.85, 3.308, -3.65]}, {"time": 3.3333, "value": -3.65, "curve": [3.358, -3.65, 3.408, -15.85]}, {"time": 3.4333, "value": -15.85, "curve": [3.45, -15.85, 3.483, -3.65]}, {"time": 3.5, "value": -3.65, "curve": [3.525, -3.65, 3.575, -15.85]}, {"time": 3.6, "value": -15.85}]}, "Mouth": {"translate": [{"x": -0.72, "y": -42.85}]}, "Tentacle3_Btm": {"rotate": [{"value": 73.54, "curve": [0.029, 73.54, 0.063, 69.14]}, {"time": 0.1, "value": 61.89, "curve": [0.246, 35.84, 0.453, -37.96]}, {"time": 0.5667, "value": -37.96, "curve": [0.758, -37.96, 1.142, 73.54]}, {"time": 1.3333, "value": 73.54, "curve": [1.362, 73.54, 1.396, 69.14]}, {"time": 1.4333, "value": 61.89, "curve": [1.58, 35.84, 1.787, -37.96]}, {"time": 1.9, "value": -37.96, "curve": [2.092, -37.96, 2.475, 73.54]}, {"time": 2.6667, "value": 73.54, "curve": [2.696, 73.54, 2.729, 69.14]}, {"time": 2.7667, "value": 61.89, "curve": [2.913, 35.84, 3.12, -37.96]}, {"time": 3.2333, "value": -37.96, "curve": [3.425, -37.96, 3.808, 73.54]}, {"time": 4, "value": 73.54}]}, "Mouth4": {"rotate": [{"value": 66.64, "curve": [0.1, 66.64, 0.3, -55.86]}, {"time": 0.4, "value": -55.86, "curve": [0.592, -55.86, 0.975, 86.12]}, {"time": 1.1667, "value": 86.12}]}, "Mouth3": {"rotate": [{"value": 57.71, "curve": [0.1, 57.71, 0.3, -88.97]}, {"time": 0.4, "value": -88.97, "curve": [0.592, -88.97, 0.975, 57.71]}, {"time": 1.1667, "value": 57.71}]}, "Mouth6": {"rotate": [{"value": -76.45, "curve": [0.1, -76.45, 0.3, 62.51]}, {"time": 0.4, "value": 62.51, "curve": [0.592, 62.51, 0.975, -76.45]}, {"time": 1.1667, "value": -76.45}]}, "Head": {"translate": [{"x": -5.2, "y": -0.09}, {"time": 0.0667, "x": 1.78, "y": 0.03}, {"time": 0.1333, "x": -5.2, "y": -0.09}, {"time": 0.2, "x": 1.78, "y": 0.03}, {"time": 0.2667, "x": -5.2, "y": -0.09}, {"time": 0.3333, "x": 1.78, "y": 0.03}, {"time": 0.4, "x": -5.2, "y": -0.09}, {"time": 0.4667, "x": 1.78, "y": 0.03}, {"time": 0.5333, "x": -5.2, "y": -0.09}, {"time": 0.6, "x": 1.78, "y": 0.03}, {"time": 0.6667, "x": -5.2, "y": -0.09}, {"time": 0.7333, "x": 1.78, "y": 0.03}, {"time": 0.8, "x": -5.2, "y": -0.09}, {"time": 0.8667, "x": 1.78, "y": 0.03}, {"time": 0.9333, "x": -5.2, "y": -0.09}, {"time": 1, "x": 1.78, "y": 0.03}, {"time": 1.0667, "x": -5.2, "y": -0.09}, {"time": 1.1333, "x": 1.78, "y": 0.03}, {"time": 1.2, "x": -5.2, "y": -0.09}, {"time": 1.2667, "x": 1.78, "y": 0.03}, {"time": 1.3333, "x": -5.2, "y": -0.09}, {"time": 1.4, "x": 1.78, "y": 0.03}, {"time": 1.4667, "x": -5.2, "y": -0.09}, {"time": 1.5333, "x": 1.78, "y": 0.03}, {"time": 1.6, "x": -5.2, "y": -0.09}, {"time": 1.6667, "x": 1.78, "y": 0.03}, {"time": 1.7333, "x": -5.2, "y": -0.09}, {"time": 1.8, "x": 1.78, "y": 0.03, "curve": "stepped"}, {"time": 1.8667, "x": 1.78, "y": 0.03}, {"time": 1.9333, "x": -5.2, "y": -0.09}, {"time": 2, "x": 1.78, "y": 0.03}, {"time": 2.0667, "x": -5.2, "y": -0.09}, {"time": 2.1333, "x": 1.78, "y": 0.03}, {"time": 2.2, "x": -5.2, "y": -0.09}, {"time": 2.2667, "x": 1.78, "y": 0.03}, {"time": 2.3333, "x": -5.2, "y": -0.09}, {"time": 2.4, "x": 1.78, "y": 0.03}, {"time": 2.4667, "x": -5.2, "y": -0.09}, {"time": 2.5333, "x": 1.78, "y": 0.03}, {"time": 2.6, "x": -5.2, "y": -0.09, "curve": "stepped"}, {"time": 2.6667, "x": -5.2, "y": -0.09}, {"time": 2.7333, "x": 1.78, "y": 0.03}, {"time": 2.8, "x": -5.2, "y": -0.09}, {"time": 2.8667, "x": 1.78, "y": 0.03}, {"time": 2.9333, "x": -5.2, "y": -0.09}, {"time": 3, "x": 1.78, "y": 0.03}, {"time": 3.0667, "x": -5.2, "y": -0.09}, {"time": 3.1333, "x": 1.78, "y": 0.03, "curve": "stepped"}, {"time": 3.2, "x": 1.78, "y": 0.03}, {"time": 3.2667, "x": -5.2, "y": -0.09}, {"time": 3.3333, "x": 1.78, "y": 0.03}, {"time": 3.4, "x": -5.2, "y": -0.09}, {"time": 3.4667, "x": 1.78, "y": 0.03}, {"time": 3.5333, "x": -5.2, "y": -0.09}, {"time": 3.6, "x": 1.78, "y": 0.03}, {"time": 3.6667, "x": -5.2, "y": -0.09}, {"time": 3.7333, "x": 1.78, "y": 0.03}, {"time": 3.8, "x": -5.2, "y": -0.09}, {"time": 3.8667, "x": 1.78, "y": 0.03}, {"time": 3.9333, "x": -5.2, "y": -0.09}, {"time": 4, "x": 1.78, "y": 0.03}], "scale": [{"curve": [0.079, 0.877, 0.55, 0.805, 0.079, 1.153, 0.55, 1.243]}, {"time": 0.7333, "x": 0.805, "y": 1.243}]}, "Tentacle5": {"rotate": [{"value": -49.78, "curve": [0.192, -49.78, 0.575, 9.39]}, {"time": 0.7667, "value": 9.39, "curve": [0.908, 9.39, 1.192, -49.78]}, {"time": 1.3333, "value": -49.78, "curve": [1.525, -49.78, 1.908, 9.39]}, {"time": 2.1, "value": 9.39, "curve": [2.242, 9.39, 2.525, -49.78]}, {"time": 2.6667, "value": -49.78, "curve": [2.858, -49.78, 3.242, 9.39]}, {"time": 3.4333, "value": 9.39, "curve": [3.575, 9.39, 3.858, -49.78]}, {"time": 4, "value": -49.78}]}, "Tentacle1": {"rotate": [{"value": -49.78, "curve": [0.192, -49.78, 0.575, 9.39]}, {"time": 0.7667, "value": 9.39, "curve": [0.908, 9.39, 1.192, -49.78]}, {"time": 1.3333, "value": -49.78, "curve": [1.525, -49.78, 1.908, 9.39]}, {"time": 2.1, "value": 9.39, "curve": [2.242, 9.39, 2.525, -49.78]}, {"time": 2.6667, "value": -49.78, "curve": [2.858, -49.78, 3.242, 9.39]}, {"time": 3.4333, "value": 9.39, "curve": [3.575, 9.39, 3.858, -49.78]}, {"time": 4, "value": -49.78}]}, "Tentacle3_Mid4": {"rotate": [{"value": 40.71, "curve": [0.137, 14.47, 0.303, -30.13]}, {"time": 0.4, "value": -30.13, "curve": [0.592, -30.13, 0.975, 62.45]}, {"time": 1.1667, "value": 62.45, "curve": [1.212, 62.45, 1.27, 53.43]}, {"time": 1.3333, "value": 40.71, "curve": [1.471, 14.47, 1.636, -30.13]}, {"time": 1.7333, "value": -30.13, "curve": [1.925, -30.13, 2.308, 62.45]}, {"time": 2.5, "value": 62.45, "curve": [2.545, 62.45, 2.603, 53.43]}, {"time": 2.6667, "value": 40.71, "curve": [2.804, 14.47, 2.97, -30.13]}, {"time": 3.0667, "value": -30.13, "curve": [3.258, -30.13, 3.642, 62.45]}, {"time": 3.8333, "value": 62.45, "curve": [3.878, 62.45, 3.936, 53.43]}, {"time": 4, "value": 40.71}]}, "Tentacle3": {"rotate": [{"value": -36.75, "curve": [0.192, -36.75, 0.575, 6.68]}, {"time": 0.7667, "value": 6.68, "curve": [0.908, 6.68, 1.192, -36.75]}, {"time": 1.3333, "value": -36.75, "curve": [1.525, -36.75, 1.908, 6.68]}, {"time": 2.1, "value": 6.68, "curve": [2.242, 6.68, 2.525, -36.75]}, {"time": 2.6667, "value": -36.75, "curve": [2.858, -36.75, 3.242, 6.68]}, {"time": 3.4333, "value": 6.68, "curve": [3.575, 6.68, 3.858, -36.75]}, {"time": 4, "value": -36.75}]}, "Tentacle3_Btm4": {"rotate": [{"value": 51.6, "curve": [0.146, 27.35, 0.353, -41.36]}, {"time": 0.4667, "value": -41.36, "curve": [0.658, -41.36, 1.042, 62.45]}, {"time": 1.2333, "value": 62.45, "curve": [1.262, 62.45, 1.296, 58.35]}, {"time": 1.3333, "value": 51.6, "curve": [1.48, 27.35, 1.687, -41.36]}, {"time": 1.8, "value": -41.36, "curve": [1.992, -41.36, 2.375, 62.45]}, {"time": 2.5667, "value": 62.45, "curve": [2.596, 62.45, 2.629, 58.35]}, {"time": 2.6667, "value": 51.6, "curve": [2.813, 27.35, 3.02, -41.36]}, {"time": 3.1333, "value": -41.36, "curve": [3.325, -41.36, 3.708, 62.45]}, {"time": 3.9, "value": 62.45, "curve": [3.929, 62.45, 3.963, 58.35]}, {"time": 4, "value": 51.6}]}, "Spike10": {"rotate": [{"value": 24.29, "curve": [0.075, 24.29, 0.225, -9.92]}, {"time": 0.3, "value": -9.92, "curve": [0.392, -9.92, 0.575, 16.34]}, {"time": 0.6667, "value": 16.34, "curve": [0.692, 16.34, 0.742, 24.29]}, {"time": 0.7667, "value": 24.29, "curve": [0.792, 24.29, 0.842, 16.34]}, {"time": 0.8667, "value": 16.34, "curve": [0.892, 16.34, 0.942, 24.29]}, {"time": 0.9667, "value": 24.29, "curve": [0.992, 24.29, 1.042, 16.34]}, {"time": 1.0667, "value": 16.34, "curve": [1.092, 16.34, 1.142, 24.29]}, {"time": 1.1667, "value": 24.29, "curve": [1.183, 24.29, 1.217, 16.34]}, {"time": 1.2333, "value": 16.34, "curve": [1.258, 16.34, 1.308, 24.29]}, {"time": 1.3333, "value": 24.29, "curve": [1.358, 24.29, 1.408, 16.34]}, {"time": 1.4333, "value": 16.34, "curve": [1.458, 16.34, 1.508, 24.29]}, {"time": 1.5333, "value": 24.29, "curve": [1.558, 24.29, 1.608, 16.34]}, {"time": 1.6333, "value": 16.34, "curve": [1.658, 16.34, 1.708, 24.29]}, {"time": 1.7333, "value": 24.29, "curve": [1.75, 24.29, 1.783, 16.34]}, {"time": 1.8, "value": 16.34, "curve": [1.825, 16.34, 1.875, 24.29]}, {"time": 1.9, "value": 24.29, "curve": [1.925, 24.29, 1.975, 16.34]}, {"time": 2, "value": 16.34, "curve": [2.025, 16.34, 2.075, 24.29]}, {"time": 2.1, "value": 24.29, "curve": [2.125, 24.29, 2.175, 16.34]}, {"time": 2.2, "value": 16.34, "curve": [2.225, 16.34, 2.275, 24.29]}, {"time": 2.3, "value": 24.29, "curve": [2.317, 24.29, 2.35, 16.34]}, {"time": 2.3667, "value": 16.34, "curve": [2.392, 16.34, 2.442, 24.29]}, {"time": 2.4667, "value": 24.29, "curve": [2.492, 24.29, 2.542, 16.34]}, {"time": 2.5667, "value": 16.34, "curve": [2.592, 16.34, 2.642, 24.29]}, {"time": 2.6667, "value": 24.29, "curve": [2.692, 24.29, 2.742, 16.34]}, {"time": 2.7667, "value": 16.34, "curve": [2.792, 16.34, 2.842, 24.29]}, {"time": 2.8667, "value": 24.29, "curve": [2.883, 24.29, 2.917, 16.34]}, {"time": 2.9333, "value": 16.34, "curve": [2.958, 16.34, 3.008, 24.29]}, {"time": 3.0333, "value": 24.29, "curve": [3.058, 24.29, 3.108, 16.34]}, {"time": 3.1333, "value": 16.34, "curve": [3.158, 16.34, 3.208, 24.29]}, {"time": 3.2333, "value": 24.29, "curve": [3.258, 24.29, 3.308, 16.34]}, {"time": 3.3333, "value": 16.34, "curve": [3.358, 16.34, 3.408, 24.29]}, {"time": 3.4333, "value": 24.29, "curve": [3.45, 24.29, 3.483, 16.34]}, {"time": 3.5, "value": 16.34, "curve": [3.525, 16.34, 3.575, 24.29]}, {"time": 3.6, "value": 24.29}]}, "root": {"scale": [{}, {"time": 0.0667, "x": 1.026, "y": 0.983}, {"time": 0.1333}, {"time": 0.2, "x": 1.026, "y": 0.983}, {"time": 0.2667}, {"time": 0.3333, "x": 1.026, "y": 0.983}, {"time": 0.4}, {"time": 0.4667, "x": 1.026, "y": 0.983}, {"time": 0.5333}, {"time": 0.6, "x": 1.026, "y": 0.983}, {"time": 0.6667}, {"time": 0.7333, "x": 1.026, "y": 0.983}, {"time": 0.8}, {"time": 0.8667, "x": 1.026, "y": 0.983}, {"time": 0.9333}, {"time": 1, "x": 1.026, "y": 0.983}, {"time": 1.0667}, {"time": 1.1333, "x": 1.026, "y": 0.983}, {"time": 1.2}, {"time": 1.2667, "x": 1.026, "y": 0.983}, {"time": 1.3333}, {"time": 1.4, "x": 1.026, "y": 0.983}, {"time": 1.4667}, {"time": 1.5333, "x": 1.026, "y": 0.983}, {"time": 1.6}, {"time": 1.6667, "x": 1.026, "y": 0.983}, {"time": 1.7333, "curve": "stepped"}, {"time": 1.8}, {"time": 1.8667, "x": 1.026, "y": 0.983}, {"time": 1.9333}, {"time": 2, "x": 1.026, "y": 0.983}, {"time": 2.0667}, {"time": 2.1333, "x": 1.026, "y": 0.983}, {"time": 2.2}, {"time": 2.2667, "x": 1.026, "y": 0.983}, {"time": 2.3333}, {"time": 2.4, "x": 1.026, "y": 0.983}, {"time": 2.4667}, {"time": 2.5333, "x": 1.026, "y": 0.983}, {"time": 2.6, "curve": "stepped"}, {"time": 2.6667}, {"time": 2.7333, "x": 1.026, "y": 0.983}, {"time": 2.8}, {"time": 2.8667, "x": 1.026, "y": 0.983}, {"time": 2.9333}, {"time": 3, "x": 1.026, "y": 0.983}, {"time": 3.0667, "curve": "stepped"}, {"time": 3.1333}, {"time": 3.2, "x": 1.026, "y": 0.983}, {"time": 3.2667}, {"time": 3.3333, "x": 1.026, "y": 0.983}, {"time": 3.4}, {"time": 3.4667, "x": 1.026, "y": 0.983}, {"time": 3.5333}, {"time": 3.6, "x": 1.026, "y": 0.983}, {"time": 3.6667}, {"time": 3.7333, "x": 1.026, "y": 0.983}, {"time": 3.8}, {"time": 3.8667, "x": 1.026, "y": 0.983}, {"time": 3.9333}, {"time": 4, "x": 1.026, "y": 0.983}]}}}, "anticipate-charge": {"slots": {"Eyes": {"attachment": [{"name": "EyesCharge"}]}, "images/Eye3": {"attachment": [{}]}, "Mouth": {"attachment": [{"name": "Mouth Closed"}]}, "MouthBack": {"attachment": [{"name": "Mouth Back Shut"}]}, "ScuttleEye3": {"attachment": [{"name": "<PERSON><PERSON><PERSON>_Eye_Closed"}]}}, "bones": {"FACE": {"translate": [{"x": 43.75, "curve": [0.042, 43.75, 0.125, 62.2, 0.042, 0, 0.125, -0.4]}, {"time": 0.1667, "x": 62.2, "y": -0.4, "curve": [0.352, -1.94, 1.042, -32.43, 0.352, -0.13, 1.042, 0]}, {"time": 1.3333, "x": -32.43}]}, "MAIN": {"translate": [{"y": 17.53, "curve": [0.31, 0, 0.333, 0, 0.31, 17.53, 0.333, -32.5]}, {"time": 0.6667, "y": -33.8, "curve": [0.976, 0, 1, 0, 0.976, -33.8, 1, 16.22]}, {"time": 1.3333, "y": 17.53, "curve": [1.643, 0, 1.667, 0, 1.643, 17.53, 1.667, -32.5]}, {"time": 2, "y": -33.8, "curve": [2.31, 0, 2.333, 0, 2.31, -33.8, 2.333, 16.22]}, {"time": 2.6667, "y": 17.53}], "scale": [{"x": 0.987, "y": 1.006, "curve": [0.125, 0.962, 0.25, 0.937, 0.125, 1.028, 0.25, 1.05]}, {"time": 0.3333, "x": 0.937, "y": 1.05, "curve": [0.5, 0.937, 0.833, 1.038, 0.5, 1.05, 0.833, 0.962]}, {"time": 1, "x": 1.038, "y": 0.962, "curve": [1.083, 1.038, 1.208, 1.013, 1.083, 0.962, 1.208, 0.984]}, {"time": 1.3333, "x": 0.987, "y": 1.006, "curve": [1.458, 0.962, 1.583, 0.937, 1.458, 1.028, 1.583, 1.05]}, {"time": 1.6667, "x": 0.937, "y": 1.05, "curve": [1.833, 0.937, 2.167, 1.038, 1.833, 1.05, 2.167, 0.962]}, {"time": 2.3333, "x": 1.038, "y": 0.962, "curve": [2.417, 1.038, 2.542, 1.013, 2.417, 0.962, 2.542, 0.984]}, {"time": 2.6667, "x": 0.987, "y": 1.006}]}, "Spike5": {"rotate": [{"value": 0.22, "curve": [0.025, 0.22, 0.075, 12.42]}, {"time": 0.1, "value": 12.42, "curve": [0.125, 12.42, 0.175, 0.22]}, {"time": 0.2, "value": 0.22, "curve": [0.225, 0.22, 0.275, 12.42]}, {"time": 0.3, "value": 12.42, "curve": [0.325, 12.42, 0.375, 0.22]}, {"time": 0.4, "value": 0.22, "curve": [0.417, 0.22, 0.45, 12.42]}, {"time": 0.4667, "value": 12.42, "curve": [0.492, 12.42, 0.542, 0.22]}, {"time": 0.5667, "value": 0.22, "curve": [0.592, 0.22, 0.642, 12.42]}, {"time": 0.6667, "value": 12.42, "curve": [0.692, 12.42, 0.742, 0.22]}, {"time": 0.7667, "value": 0.22, "curve": [0.792, 0.22, 0.842, 12.42]}, {"time": 0.8667, "value": 12.42, "curve": [0.892, 12.42, 0.942, 0.22]}, {"time": 0.9667, "value": 0.22, "curve": [0.992, 0.22, 1.042, 12.42]}, {"time": 1.0667, "value": 12.42, "curve": [1.092, 12.42, 1.142, 0.22]}, {"time": 1.1667, "value": 0.22, "curve": [1.183, 0.22, 1.217, 12.42]}, {"time": 1.2333, "value": 12.42, "curve": [1.258, 12.42, 1.308, 0.22]}, {"time": 1.3333, "value": 0.22}]}, "Spike4": {"rotate": [{"value": 7.32, "curve": [0.013, 10.37, 0.025, 13.42]}, {"time": 0.0333, "value": 13.42, "curve": [0.058, 13.42, 0.108, 1.22]}, {"time": 0.1333, "value": 1.22, "curve": [0.158, 1.22, 0.208, 13.42]}, {"time": 0.2333, "value": 13.42, "curve": [0.258, 13.42, 0.308, 1.22]}, {"time": 0.3333, "value": 1.22, "curve": [0.358, 1.22, 0.408, 13.42]}, {"time": 0.4333, "value": 13.42, "curve": [0.458, 13.42, 0.508, 1.22]}, {"time": 0.5333, "value": 1.22, "curve": [0.55, 1.22, 0.583, 13.42]}, {"time": 0.6, "value": 13.42, "curve": [0.625, 13.42, 0.675, 1.22]}, {"time": 0.7, "value": 1.22, "curve": [0.725, 1.22, 0.775, 13.42]}, {"time": 0.8, "value": 13.42, "curve": [0.825, 13.42, 0.875, 1.22]}, {"time": 0.9, "value": 1.22, "curve": [0.925, 1.22, 0.975, 13.42]}, {"time": 1, "value": 13.42, "curve": [1.025, 13.42, 1.075, 1.22]}, {"time": 1.1, "value": 1.22, "curve": [1.125, 1.22, 1.175, 13.42]}, {"time": 1.2, "value": 13.42, "curve": [1.225, 13.42, 1.275, 1.22]}, {"time": 1.3, "value": 1.22, "curve": [1.308, 1.22, 1.321, 4.27]}, {"time": 1.3333, "value": 7.32}]}, "Spike3": {"rotate": [{"value": -15.85, "curve": [0.025, -15.85, 0.075, -3.65]}, {"time": 0.1, "value": -3.65, "curve": [0.125, -3.65, 0.175, -15.85]}, {"time": 0.2, "value": -15.85, "curve": [0.225, -15.85, 0.275, -3.65]}, {"time": 0.3, "value": -3.65, "curve": [0.325, -3.65, 0.375, -15.85]}, {"time": 0.4, "value": -15.85, "curve": [0.417, -15.85, 0.45, -3.65]}, {"time": 0.4667, "value": -3.65, "curve": [0.492, -3.65, 0.542, -15.85]}, {"time": 0.5667, "value": -15.85, "curve": [0.592, -15.85, 0.642, -3.65]}, {"time": 0.6667, "value": -3.65, "curve": [0.692, -3.65, 0.742, -15.85]}, {"time": 0.7667, "value": -15.85, "curve": [0.792, -15.85, 0.842, -3.65]}, {"time": 0.8667, "value": -3.65, "curve": [0.892, -3.65, 0.942, -15.85]}, {"time": 0.9667, "value": -15.85, "curve": [0.992, -15.85, 1.042, -3.65]}, {"time": 1.0667, "value": -3.65, "curve": [1.092, -3.65, 1.142, -15.85]}, {"time": 1.1667, "value": -15.85, "curve": [1.183, -15.85, 1.217, -3.65]}, {"time": 1.2333, "value": -3.65, "curve": [1.258, -3.65, 1.308, -15.85]}, {"time": 1.3333, "value": -15.85, "curve": [1.358, -15.85, 1.408, -3.65]}, {"time": 1.4333, "value": -3.65, "curve": [1.458, -3.65, 1.508, -15.85]}, {"time": 1.5333, "value": -15.85, "curve": [1.558, -15.85, 1.608, -3.65]}, {"time": 1.6333, "value": -3.65, "curve": [1.658, -3.65, 1.708, -15.85]}, {"time": 1.7333, "value": -15.85, "curve": [1.75, -15.85, 1.783, -3.65]}, {"time": 1.8, "value": -3.65, "curve": [1.825, -3.65, 1.875, -15.85]}, {"time": 1.9, "value": -15.85, "curve": [1.925, -15.85, 1.975, -3.65]}, {"time": 2, "value": -3.65, "curve": [2.025, -3.65, 2.075, -15.85]}, {"time": 2.1, "value": -15.85, "curve": [2.125, -15.85, 2.175, -3.65]}, {"time": 2.2, "value": -3.65, "curve": [2.225, -3.65, 2.275, -15.85]}, {"time": 2.3, "value": -15.85, "curve": [2.317, -15.85, 2.35, -3.65]}, {"time": 2.3667, "value": -3.65, "curve": [2.392, -3.65, 2.442, -15.85]}, {"time": 2.4667, "value": -15.85}]}, "Spike2": {"rotate": [{"value": -17.63, "curve": [0.013, -19.61, 0.025, -21.09]}, {"time": 0.0333, "value": -21.09, "curve": [0.058, -21.09, 0.108, -8.89]}, {"time": 0.1333, "value": -8.89, "curve": [0.158, -8.89, 0.208, -21.09]}, {"time": 0.2333, "value": -21.09, "curve": [0.258, -21.09, 0.308, -8.89]}, {"time": 0.3333, "value": -8.89, "curve": [0.358, -8.89, 0.408, -21.09]}, {"time": 0.4333, "value": -21.09, "curve": [0.45, -21.09, 0.483, -8.89]}, {"time": 0.5, "value": -8.89, "curve": [0.525, -8.89, 0.575, -21.09]}, {"time": 0.6, "value": -21.09, "curve": [0.625, -21.09, 0.675, -8.89]}, {"time": 0.7, "value": -8.89, "curve": [0.725, -8.89, 0.775, -21.09]}, {"time": 0.8, "value": -21.09, "curve": [0.825, -21.09, 0.875, -8.89]}, {"time": 0.9, "value": -8.89, "curve": [0.925, -8.89, 0.975, -21.09]}, {"time": 1, "value": -21.09, "curve": [1.025, -21.09, 1.075, -8.89]}, {"time": 1.1, "value": -8.89, "curve": [1.125, -8.89, 1.175, -21.09]}, {"time": 1.2, "value": -21.09, "curve": [1.217, -21.09, 1.25, -8.89]}, {"time": 1.2667, "value": -8.89, "curve": [1.283, -8.89, 1.317, -17.63]}, {"time": 1.3333, "value": -17.63, "curve": [1.346, -19.61, 1.358, -21.09]}, {"time": 1.3667, "value": -21.09, "curve": [1.392, -21.09, 1.442, -8.89]}, {"time": 1.4667, "value": -8.89, "curve": [1.492, -8.89, 1.542, -21.09]}, {"time": 1.5667, "value": -21.09, "curve": [1.592, -21.09, 1.642, -8.89]}, {"time": 1.6667, "value": -8.89, "curve": [1.692, -8.89, 1.742, -21.09]}, {"time": 1.7667, "value": -21.09, "curve": [1.783, -21.09, 1.817, -8.89]}, {"time": 1.8333, "value": -8.89, "curve": [1.858, -8.89, 1.908, -17.63]}, {"time": 1.9333, "value": -17.63, "curve": [1.972, -12.64, 2.007, -8.89]}, {"time": 2.0333, "value": -8.89, "curve": [2.058, -8.89, 2.108, -21.09]}, {"time": 2.1333, "value": -21.09, "curve": [2.158, -21.09, 2.208, -8.89]}, {"time": 2.2333, "value": -8.89, "curve": [2.258, -8.89, 2.308, -21.09]}, {"time": 2.3333, "value": -21.09, "curve": [2.35, -21.09, 2.383, -8.89]}, {"time": 2.4, "value": -8.89, "curve": [2.416, -8.89, 2.443, -14.14]}, {"time": 2.4667, "value": -17.63}]}, "Spike1": {"rotate": [{"value": -23.57, "curve": [0.075, -23.57, 0.225, 10.75]}, {"time": 0.3, "value": 10.75, "curve": [0.392, 10.75, 0.575, -11.37]}, {"time": 0.6667, "value": -11.37, "curve": [0.692, -11.37, 0.742, -23.57]}, {"time": 0.7667, "value": -23.57, "curve": [0.792, -23.57, 0.842, -11.37]}, {"time": 0.8667, "value": -11.37, "curve": [0.892, -11.37, 0.942, -23.57]}, {"time": 0.9667, "value": -23.57, "curve": [0.992, -23.57, 1.042, -11.37]}, {"time": 1.0667, "value": -11.37, "curve": [1.092, -11.37, 1.142, -23.57]}, {"time": 1.1667, "value": -23.57, "curve": [1.183, -23.57, 1.217, -11.37]}, {"time": 1.2333, "value": -11.37, "curve": [1.258, -11.37, 1.308, -23.57]}, {"time": 1.3333, "value": -23.57, "curve": [1.358, -23.57, 1.408, -11.37]}, {"time": 1.4333, "value": -11.37, "curve": [1.458, -11.37, 1.508, -23.57]}, {"time": 1.5333, "value": -23.57, "curve": [1.558, -23.57, 1.608, -11.37]}, {"time": 1.6333, "value": -11.37, "curve": [1.658, -11.37, 1.708, -23.57]}, {"time": 1.7333, "value": -23.57, "curve": [1.75, -23.57, 1.783, -11.37]}, {"time": 1.8, "value": -11.37, "curve": [1.825, -11.37, 1.875, -23.57]}, {"time": 1.9, "value": -23.57, "curve": [1.925, -23.57, 1.975, -11.37]}, {"time": 2, "value": -11.37, "curve": [2.025, -11.37, 2.075, -23.57]}, {"time": 2.1, "value": -23.57, "curve": [2.125, -23.57, 2.175, -11.37]}, {"time": 2.2, "value": -11.37, "curve": [2.225, -11.37, 2.275, -23.57]}, {"time": 2.3, "value": -23.57, "curve": [2.317, -23.57, 2.35, -11.37]}, {"time": 2.3667, "value": -11.37, "curve": [2.392, -11.37, 2.442, -23.57]}, {"time": 2.4667, "value": -23.57}]}, "Spike10": {"rotate": [{"value": 24.29, "curve": [0.075, 24.29, 0.225, -9.92]}, {"time": 0.3, "value": -9.92, "curve": [0.392, -9.92, 0.575, 16.34]}, {"time": 0.6667, "value": 16.34, "curve": [0.692, 16.34, 0.742, 24.29]}, {"time": 0.7667, "value": 24.29, "curve": [0.792, 24.29, 0.842, 16.34]}, {"time": 0.8667, "value": 16.34, "curve": [0.892, 16.34, 0.942, 24.29]}, {"time": 0.9667, "value": 24.29, "curve": [0.992, 24.29, 1.042, 16.34]}, {"time": 1.0667, "value": 16.34, "curve": [1.092, 16.34, 1.142, 24.29]}, {"time": 1.1667, "value": 24.29, "curve": [1.183, 24.29, 1.217, 16.34]}, {"time": 1.2333, "value": 16.34, "curve": [1.258, 16.34, 1.308, 24.29]}, {"time": 1.3333, "value": 24.29, "curve": [1.358, 24.29, 1.408, 16.34]}, {"time": 1.4333, "value": 16.34, "curve": [1.458, 16.34, 1.508, 24.29]}, {"time": 1.5333, "value": 24.29, "curve": [1.558, 24.29, 1.608, 16.34]}, {"time": 1.6333, "value": 16.34, "curve": [1.658, 16.34, 1.708, 24.29]}, {"time": 1.7333, "value": 24.29, "curve": [1.75, 24.29, 1.783, 16.34]}, {"time": 1.8, "value": 16.34, "curve": [1.825, 16.34, 1.875, 24.29]}, {"time": 1.9, "value": 24.29, "curve": [1.925, 24.29, 1.975, 16.34]}, {"time": 2, "value": 16.34, "curve": [2.025, 16.34, 2.075, 24.29]}, {"time": 2.1, "value": 24.29, "curve": [2.125, 24.29, 2.175, 16.34]}, {"time": 2.2, "value": 16.34, "curve": [2.225, 16.34, 2.275, 24.29]}, {"time": 2.3, "value": 24.29, "curve": [2.317, 24.29, 2.35, 16.34]}, {"time": 2.3667, "value": 16.34, "curve": [2.392, 16.34, 2.442, 24.29]}, {"time": 2.4667, "value": 24.29}]}, "Spike6": {"rotate": [{"value": 16.3, "curve": [0.013, 17.59, 0.025, 18.56]}, {"time": 0.0333, "value": 18.56, "curve": [0.058, 18.56, 0.108, 10.61]}, {"time": 0.1333, "value": 10.61, "curve": [0.158, 10.61, 0.208, 18.56]}, {"time": 0.2333, "value": 18.56, "curve": [0.258, 18.56, 0.308, 10.61]}, {"time": 0.3333, "value": 10.61, "curve": [0.358, 10.61, 0.408, 18.56]}, {"time": 0.4333, "value": 18.56, "curve": [0.45, 18.56, 0.483, 10.61]}, {"time": 0.5, "value": 10.61, "curve": [0.525, 10.61, 0.575, 18.56]}, {"time": 0.6, "value": 18.56, "curve": [0.625, 18.56, 0.675, 10.61]}, {"time": 0.7, "value": 10.61, "curve": [0.725, 10.61, 0.775, 18.56]}, {"time": 0.8, "value": 18.56, "curve": [0.825, 18.56, 0.875, 10.61]}, {"time": 0.9, "value": 10.61, "curve": [0.925, 10.61, 0.975, 18.56]}, {"time": 1, "value": 18.56, "curve": [1.025, 18.56, 1.075, 10.61]}, {"time": 1.1, "value": 10.61, "curve": [1.125, 10.61, 1.175, 18.56]}, {"time": 1.2, "value": 18.56, "curve": [1.217, 18.56, 1.25, 10.61]}, {"time": 1.2667, "value": 10.61, "curve": [1.292, 10.61, 1.342, 16.3]}, {"time": 1.3667, "value": 16.3, "curve": [1.405, 13.05, 1.44, 10.61]}, {"time": 1.4667, "value": 10.61, "curve": [1.492, 10.61, 1.542, 18.56]}, {"time": 1.5667, "value": 18.56, "curve": [1.592, 18.56, 1.642, 10.61]}, {"time": 1.6667, "value": 10.61, "curve": [1.692, 10.61, 1.742, 18.56]}, {"time": 1.7667, "value": 18.56, "curve": [1.783, 18.56, 1.817, 10.61]}, {"time": 1.8333, "value": 10.61, "curve": [1.858, 10.61, 1.908, 16.3]}, {"time": 1.9333, "value": 16.3, "curve": [1.972, 13.05, 2.007, 10.61]}, {"time": 2.0333, "value": 10.61, "curve": [2.058, 10.61, 2.108, 18.56]}, {"time": 2.1333, "value": 18.56, "curve": [2.158, 18.56, 2.208, 10.61]}, {"time": 2.2333, "value": 10.61, "curve": [2.258, 10.61, 2.308, 18.56]}, {"time": 2.3333, "value": 18.56, "curve": [2.35, 18.56, 2.383, 10.61]}, {"time": 2.4, "value": 10.61, "curve": [2.416, 10.61, 2.443, 14.03]}, {"time": 2.4667, "value": 16.3}]}, "Spike7": {"rotate": [{"value": 14.37, "curve": [0.025, 14.37, 0.075, 6.42]}, {"time": 0.1, "value": 6.42, "curve": [0.125, 6.42, 0.175, 14.37]}, {"time": 0.2, "value": 14.37, "curve": [0.225, 14.37, 0.275, 6.42]}, {"time": 0.3, "value": 6.42, "curve": [0.325, 6.42, 0.375, 14.37]}, {"time": 0.4, "value": 14.37, "curve": [0.417, 14.37, 0.45, 6.42]}, {"time": 0.4667, "value": 6.42, "curve": [0.492, 6.42, 0.542, 14.37]}, {"time": 0.5667, "value": 14.37, "curve": [0.592, 14.37, 0.642, 6.42]}, {"time": 0.6667, "value": 6.42, "curve": [0.692, 6.42, 0.742, 14.37]}, {"time": 0.7667, "value": 14.37, "curve": [0.792, 14.37, 0.842, 6.42]}, {"time": 0.8667, "value": 6.42, "curve": [0.892, 6.42, 0.942, 14.37]}, {"time": 0.9667, "value": 14.37, "curve": [0.992, 14.37, 1.042, 6.42]}, {"time": 1.0667, "value": 6.42, "curve": [1.092, 6.42, 1.142, 14.37]}, {"time": 1.1667, "value": 14.37, "curve": [1.183, 14.37, 1.217, 6.42]}, {"time": 1.2333, "value": 6.42, "curve": [1.258, 6.42, 1.308, 14.37]}, {"time": 1.3333, "value": 14.37, "curve": [1.358, 14.37, 1.408, 6.42]}, {"time": 1.4333, "value": 6.42, "curve": [1.458, 6.42, 1.508, 14.37]}, {"time": 1.5333, "value": 14.37, "curve": [1.558, 14.37, 1.608, 6.42]}, {"time": 1.6333, "value": 6.42, "curve": [1.658, 6.42, 1.708, 14.37]}, {"time": 1.7333, "value": 14.37, "curve": [1.75, 14.37, 1.783, 6.42]}, {"time": 1.8, "value": 6.42, "curve": [1.825, 6.42, 1.875, 14.37]}, {"time": 1.9, "value": 14.37, "curve": [1.925, 14.37, 1.975, 6.42]}, {"time": 2, "value": 6.42, "curve": [2.025, 6.42, 2.075, 14.37]}, {"time": 2.1, "value": 14.37, "curve": [2.125, 14.37, 2.175, 6.42]}, {"time": 2.2, "value": 6.42, "curve": [2.225, 6.42, 2.275, 14.37]}, {"time": 2.3, "value": 14.37, "curve": [2.317, 14.37, 2.35, 6.42]}, {"time": 2.3667, "value": 6.42, "curve": [2.392, 6.42, 2.442, 14.37]}, {"time": 2.4667, "value": 14.37}]}, "Spike8": {"rotate": [{"value": -9.22, "curve": [0.013, -11.2, 0.025, -13.19]}, {"time": 0.0333, "value": -13.19, "curve": [0.058, -13.19, 0.108, -5.25]}, {"time": 0.1333, "value": -5.25, "curve": [0.158, -5.25, 0.208, -13.19]}, {"time": 0.2333, "value": -13.19, "curve": [0.258, -13.19, 0.308, -5.25]}, {"time": 0.3333, "value": -5.25, "curve": [0.358, -5.25, 0.408, -13.19]}, {"time": 0.4333, "value": -13.19, "curve": [0.458, -13.19, 0.508, -5.25]}, {"time": 0.5333, "value": -5.25, "curve": [0.55, -5.25, 0.583, -13.19]}, {"time": 0.6, "value": -13.19, "curve": [0.625, -13.19, 0.675, -5.25]}, {"time": 0.7, "value": -5.25, "curve": [0.725, -5.25, 0.775, -13.19]}, {"time": 0.8, "value": -13.19, "curve": [0.825, -13.19, 0.875, -5.25]}, {"time": 0.9, "value": -5.25, "curve": [0.925, -5.25, 0.975, -13.19]}, {"time": 1, "value": -13.19, "curve": [1.025, -13.19, 1.075, -5.25]}, {"time": 1.1, "value": -5.25, "curve": [1.125, -5.25, 1.175, -13.19]}, {"time": 1.2, "value": -13.19, "curve": [1.225, -13.19, 1.275, -5.25]}, {"time": 1.3, "value": -5.25, "curve": [1.308, -5.25, 1.321, -7.23]}, {"time": 1.3333, "value": -9.22}]}, "Spike9": {"rotate": [{"value": -4.45, "curve": [0.025, -4.45, 0.075, -12.39]}, {"time": 0.1, "value": -12.39, "curve": [0.125, -12.39, 0.175, -4.45]}, {"time": 0.2, "value": -4.45, "curve": [0.225, -4.45, 0.275, -12.39]}, {"time": 0.3, "value": -12.39, "curve": [0.325, -12.39, 0.375, -4.45]}, {"time": 0.4, "value": -4.45, "curve": [0.417, -4.45, 0.45, -12.39]}, {"time": 0.4667, "value": -12.39, "curve": [0.492, -12.39, 0.542, -4.45]}, {"time": 0.5667, "value": -4.45, "curve": [0.592, -4.45, 0.642, -12.39]}, {"time": 0.6667, "value": -12.39, "curve": [0.692, -12.39, 0.742, -4.45]}, {"time": 0.7667, "value": -4.45, "curve": [0.792, -4.45, 0.842, -12.39]}, {"time": 0.8667, "value": -12.39, "curve": [0.892, -12.39, 0.942, -4.45]}, {"time": 0.9667, "value": -4.45, "curve": [0.992, -4.45, 1.042, -12.39]}, {"time": 1.0667, "value": -12.39, "curve": [1.092, -12.39, 1.142, -4.45]}, {"time": 1.1667, "value": -4.45, "curve": [1.183, -4.45, 1.217, -12.39]}, {"time": 1.2333, "value": -12.39, "curve": [1.258, -12.39, 1.308, -4.45]}, {"time": 1.3333, "value": -4.45}]}, "DangleHandle": {"rotate": [{"value": 6.7, "curve": [0.043, 3.66, 0.088, 0.04]}, {"time": 0.1333, "value": -3.58, "curve": [0.258, -13.51, 0.383, -23.45]}, {"time": 0.4667, "value": -23.45, "curve": [0.633, -23.45, 0.967, 16.3]}, {"time": 1.1333, "value": 16.3, "curve": [1.187, 16.3, 1.257, 12.3]}, {"time": 1.3333, "value": 6.7}], "translate": [{"x": -3.96, "y": 2.8, "curve": [0.125, -27.88, 0.25, -51.8, 0.125, 1.82, 0.25, 0.85]}, {"time": 0.3333, "x": -51.8, "y": 0.85, "curve": [0.5, -51.8, 0.833, 43.87, 0.5, 0.85, 0.833, 4.74]}, {"time": 1, "x": 43.87, "y": 4.74, "curve": [1.083, 43.87, 1.208, 19.95, 1.083, 4.74, 1.208, 3.77]}, {"time": 1.3333, "x": -3.96, "y": 2.8}], "scale": [{"y": 1.723, "curve": [0.167, 1, 0.5, 1, 0.167, 1.723, 0.5, 0.313]}, {"time": 0.6667, "y": 0.313, "curve": [0.833, 1, 1.167, 1, 0.833, 0.313, 1.167, 1.723]}, {"time": 1.3333, "y": 1.723}]}, "Tentacles": {"scale": [{"x": 0.699}, {"time": 0.6667, "x": 1.199}, {"time": 1.3333, "x": 0.699}]}, "Mouth": {"translate": [{"x": -0.72, "y": -42.85}]}, "Mouth8": {"rotate": [{"value": -82.52, "curve": [0.1, -82.52, 0.3, 55.9]}, {"time": 0.4, "value": 55.9, "curve": [0.592, 55.9, 0.975, -82.52]}, {"time": 1.1667, "value": -82.52}]}, "Mouth5": {"rotate": [{"value": 78.81, "curve": [0.1, 78.81, 0.3, -48.27]}, {"time": 0.4, "value": -48.27, "curve": [0.582, -48.27, 0.937, 75]}, {"time": 1.1333, "value": 85.72, "curve": [1.145, 77.76, 1.156, 73.59]}, {"time": 1.1667, "value": 73.59}]}, "Mouth4": {"rotate": [{"value": 66.64, "curve": [0.1, 66.64, 0.3, -55.86]}, {"time": 0.4, "value": -55.86, "curve": [0.592, -55.86, 0.975, 64.84]}, {"time": 1.1667, "value": 64.84}]}, "Mouth7": {"rotate": [{"value": -92.16, "curve": [0.1, -92.16, 0.3, 48.3]}, {"time": 0.4, "value": 48.3, "curve": [0.592, 48.3, 0.975, -100.21]}, {"time": 1.1667, "value": -100.21}]}, "Mouth3": {"rotate": [{"value": 57.71, "curve": [0.1, 57.71, 0.3, -88.97]}, {"time": 0.4, "value": -88.97, "curve": [0.592, -88.97, 0.975, 57.71]}, {"time": 1.1667, "value": 57.71}]}, "Mouth6": {"rotate": [{"value": -76.45, "curve": [0.1, -76.45, 0.3, 62.51]}, {"time": 0.4, "value": 62.51, "curve": [0.592, 62.51, 0.975, -76.45]}, {"time": 1.1667, "value": -76.45}]}, "Head": {"translate": [{"x": -5.2, "y": -0.09}, {"time": 0.0667, "x": 1.78, "y": 0.03}, {"time": 0.1333, "x": -5.2, "y": -0.09}, {"time": 0.2, "x": 1.78, "y": 0.03}, {"time": 0.2667, "x": -5.2, "y": -0.09}, {"time": 0.3333, "x": 1.78, "y": 0.03}, {"time": 0.4, "x": -5.2, "y": -0.09}, {"time": 0.4667, "x": 1.78, "y": 0.03}, {"time": 0.5333, "x": -5.2, "y": -0.09}, {"time": 0.6, "x": 1.78, "y": 0.03}, {"time": 0.6667, "x": -5.2, "y": -0.09}, {"time": 0.7333, "x": 1.78, "y": 0.03}, {"time": 0.8, "x": -5.2, "y": -0.09}, {"time": 0.8667, "x": 1.78, "y": 0.03}, {"time": 0.9333, "x": -5.2, "y": -0.09}, {"time": 1, "x": 1.78, "y": 0.03}, {"time": 1.0667, "x": -5.2, "y": -0.09}, {"time": 1.1333, "x": 1.78, "y": 0.03}, {"time": 1.2, "x": -5.2, "y": -0.09}, {"time": 1.2667, "x": 1.78, "y": 0.03}, {"time": 1.3333, "x": -5.2, "y": -0.09}, {"time": 1.4, "x": 1.78, "y": 0.03}, {"time": 1.4667, "x": -5.2, "y": -0.09}, {"time": 1.5333, "x": 1.78, "y": 0.03}, {"time": 1.6, "x": -5.2, "y": -0.09}, {"time": 1.6667, "x": 1.78, "y": 0.03}, {"time": 1.7333, "x": -5.2, "y": -0.09}, {"time": 1.8, "x": 1.78, "y": 0.03}, {"time": 1.8667, "x": -5.2, "y": -0.09}, {"time": 1.9333, "x": 1.78, "y": 0.03}, {"time": 2, "x": -5.2, "y": -0.09}, {"time": 2.0667, "x": 1.78, "y": 0.03}, {"time": 2.1333, "x": -5.2, "y": -0.09}, {"time": 2.2, "x": 1.78, "y": 0.03}, {"time": 2.2667, "x": -5.2, "y": -0.09}, {"time": 2.3333, "x": 1.78, "y": 0.03}, {"time": 2.4, "x": -5.2, "y": -0.09}, {"time": 2.4667, "x": 1.78, "y": 0.03}, {"time": 2.5333, "x": -5.2, "y": -0.09}, {"time": 2.6, "x": 1.78, "y": 0.03}, {"time": 2.6667, "x": -5.2, "y": -0.09}], "scale": [{"x": 0.746, "y": 1.272, "curve": [0.042, 0.746, 0.125, 1.153, 0.042, 1.272, 0.125, 0.891]}, {"time": 0.1667, "x": 1.153, "y": 0.891, "curve": [0.308, 1.153, 0.592, 0.805, 0.308, 0.891, 0.592, 1.243]}, {"time": 0.7333, "x": 0.805, "y": 1.243, "curve": [0.883, 0.805, 1.183, 0.954, 0.883, 1.243, 1.183, 1.127]}, {"time": 1.3333, "x": 0.954, "y": 1.127}, {"time": 2.0667, "x": 0.805, "y": 1.243, "curve": [2.217, 0.805, 2.517, 0.954, 2.217, 1.243, 2.517, 1.127]}, {"time": 2.6667, "x": 0.954, "y": 1.127}]}, "Tentacle3_Btm4": {"rotate": [{"value": 51.6, "curve": [0.146, 27.35, 0.353, -41.36]}, {"time": 0.4667, "value": -41.36, "curve": [0.658, -41.36, 1.042, 62.45]}, {"time": 1.2333, "value": 62.45, "curve": [1.262, 62.45, 1.296, 58.35]}, {"time": 1.3333, "value": 51.6, "curve": [1.48, 27.35, 1.687, -41.36]}, {"time": 1.8, "value": -41.36, "curve": [1.992, -41.36, 2.375, 62.45]}, {"time": 2.5667, "value": 62.45, "curve": [2.596, 62.45, 2.629, 58.35]}, {"time": 2.6667, "value": 51.6}]}, "Tentacle1": {"rotate": [{"value": -49.78, "curve": [0.192, -49.78, 0.575, 9.39]}, {"time": 0.7667, "value": 9.39, "curve": [0.908, 9.39, 1.192, -49.78]}, {"time": 1.3333, "value": -49.78, "curve": [1.525, -49.78, 1.908, 9.39]}, {"time": 2.1, "value": 9.39, "curve": [2.242, 9.39, 2.525, -49.78]}, {"time": 2.6667, "value": -49.78}]}, "Tentacle3_Mid4": {"rotate": [{"value": 40.71, "curve": [0.137, 14.47, 0.303, -30.13]}, {"time": 0.4, "value": -30.13, "curve": [0.592, -30.13, 0.975, 62.45]}, {"time": 1.1667, "value": 62.45, "curve": [1.212, 62.45, 1.27, 53.43]}, {"time": 1.3333, "value": 40.71, "curve": [1.471, 14.47, 1.636, -30.13]}, {"time": 1.7333, "value": -30.13, "curve": [1.925, -30.13, 2.308, 62.45]}, {"time": 2.5, "value": 62.45, "curve": [2.545, 62.45, 2.603, 53.43]}, {"time": 2.6667, "value": 40.71}]}, "Tentacle3": {"rotate": [{"value": -36.75, "curve": [0.192, -36.75, 0.575, 6.68]}, {"time": 0.7667, "value": 6.68, "curve": [0.908, 6.68, 1.192, -36.75]}, {"time": 1.3333, "value": -36.75, "curve": [1.525, -36.75, 1.908, 6.68]}, {"time": 2.1, "value": 6.68, "curve": [2.242, 6.68, 2.525, -36.75]}, {"time": 2.6667, "value": -36.75}]}, "Tentacle3_Mid": {"rotate": [{"value": 51.86, "curve": [0.03, 48.48, 0.064, 42.9]}, {"time": 0.1, "value": 36.14, "curve": [0.237, 11.69, 0.403, -29.86]}, {"time": 0.5, "value": -29.86, "curve": [0.692, -29.86, 1.075, 56.4]}, {"time": 1.2667, "value": 56.4, "curve": [1.287, 56.4, 1.309, 54.76]}, {"time": 1.3333, "value": 51.86, "curve": [1.364, 48.48, 1.398, 42.9]}, {"time": 1.4333, "value": 36.14, "curve": [1.571, 11.69, 1.736, -29.86]}, {"time": 1.8333, "value": -29.86, "curve": [2.025, -29.86, 2.408, 56.4]}, {"time": 2.6, "value": 56.4, "curve": [2.62, 56.4, 2.642, 54.76]}, {"time": 2.6667, "value": 51.86}]}, "Tentacle5": {"rotate": [{"value": -49.78, "curve": [0.192, -49.78, 0.575, 9.39]}, {"time": 0.7667, "value": 9.39, "curve": [0.908, 9.39, 1.192, -49.78]}, {"time": 1.3333, "value": -49.78, "curve": [1.525, -49.78, 1.908, 9.39]}, {"time": 2.1, "value": 9.39, "curve": [2.242, 9.39, 2.525, -49.78]}, {"time": 2.6667, "value": -49.78}]}, "Tentacle3_Btm": {"rotate": [{"value": 73.54, "curve": [0.029, 73.54, 0.063, 69.14]}, {"time": 0.1, "value": 61.89, "curve": [0.246, 35.84, 0.453, -37.96]}, {"time": 0.5667, "value": -37.96, "curve": [0.758, -37.96, 1.142, 73.54]}, {"time": 1.3333, "value": 73.54, "curve": [1.362, 73.54, 1.396, 69.14]}, {"time": 1.4333, "value": 61.89, "curve": [1.58, 35.84, 1.787, -37.96]}, {"time": 1.9, "value": -37.96, "curve": [2.092, -37.96, 2.475, 73.54]}, {"time": 2.6667, "value": 73.54}]}, "Tentacle3_Mid2": {"rotate": [{"value": 52.81, "curve": [0.021, 49.53, 0.044, 45.39]}, {"time": 0.0667, "value": 40.71, "curve": [0.204, 14.47, 0.37, -30.13]}, {"time": 0.4667, "value": -30.13, "curve": [0.658, -30.13, 1.042, 62.45]}, {"time": 1.2333, "value": 62.45, "curve": [1.262, 62.45, 1.296, 58.81]}, {"time": 1.3333, "value": 52.81, "curve": [1.354, 49.53, 1.377, 45.39]}, {"time": 1.4, "value": 40.71, "curve": [1.537, 14.47, 1.703, -30.13]}, {"time": 1.8, "value": -30.13, "curve": [1.992, -30.13, 2.375, 62.45]}, {"time": 2.5667, "value": 62.45, "curve": [2.596, 62.45, 2.629, 58.81]}, {"time": 2.6667, "value": 52.81}]}, "Tentacle3_Btm2": {"rotate": [{"value": 60.88, "curve": [0.02, 59.21, 0.043, 55.98]}, {"time": 0.0667, "value": 51.6, "curve": [0.213, 27.35, 0.42, -41.36]}, {"time": 0.5333, "value": -41.36, "curve": [0.725, -41.36, 1.108, 62.45]}, {"time": 1.3, "value": 62.45, "curve": [1.311, 62.45, 1.322, 61.9]}, {"time": 1.3333, "value": 60.88, "curve": [1.354, 59.21, 1.376, 55.98]}, {"time": 1.4, "value": 51.6, "curve": [1.546, 27.35, 1.753, -41.36]}, {"time": 1.8667, "value": -41.36, "curve": [2.058, -41.36, 2.442, 62.45]}, {"time": 2.6333, "value": 62.45, "curve": [2.644, 62.45, 2.655, 61.9]}, {"time": 2.6667, "value": 60.88}]}, "Tentacle2_Btm2": {"rotate": [{"value": 55.68, "curve": [0.148, 34.28, 0.378, -56.67]}, {"time": 0.5, "value": -56.67, "curve": [0.692, -56.67, 1.075, 62.45]}, {"time": 1.2667, "value": 62.45, "curve": [1.287, 62.45, 1.309, 60]}, {"time": 1.3333, "value": 55.68, "curve": [1.481, 34.28, 1.711, -56.67]}, {"time": 1.8333, "value": -56.67, "curve": [2.025, -56.67, 2.408, 62.45]}, {"time": 2.6, "value": 62.45, "curve": [2.62, 62.45, 2.642, 60]}, {"time": 2.6667, "value": 55.68}]}, "Tentacle2_Mid2": {"rotate": [{"value": 26.96, "curve": [0.121, 1.18, 0.251, -30.13]}, {"time": 0.3333, "value": -30.13, "curve": [0.525, -30.13, 0.908, 62.45]}, {"time": 1.1, "value": 62.45, "curve": [1.16, 62.45, 1.244, 46.14]}, {"time": 1.3333, "value": 26.96, "curve": [1.455, 1.18, 1.585, -30.13]}, {"time": 1.6667, "value": -30.13, "curve": [1.858, -30.13, 2.242, 62.45]}, {"time": 2.4333, "value": 62.45, "curve": [2.493, 62.45, 2.578, 46.14]}, {"time": 2.6667, "value": 26.96}]}, "Tentacle4": {"rotate": [{"value": 26.28, "curve": [0.192, 26.28, 0.575, -10.68]}, {"time": 0.7667, "value": -10.68, "curve": [0.908, -10.68, 1.192, 26.28]}, {"time": 1.3333, "value": 26.28, "curve": [1.525, 26.28, 1.908, -10.68]}, {"time": 2.1, "value": -10.68, "curve": [2.242, -10.68, 2.525, 26.28]}, {"time": 2.6667, "value": 26.28}]}, "Tentacle2_Btm": {"rotate": [{"value": 47.08, "curve": [0.148, 25.7, 0.378, -65.15]}, {"time": 0.5, "value": -65.15, "curve": [0.692, -65.15, 1.075, 53.84]}, {"time": 1.2667, "value": 53.84, "curve": [1.287, 53.84, 1.309, 51.4]}, {"time": 1.3333, "value": 47.08, "curve": [1.481, 25.7, 1.711, -65.15]}, {"time": 1.8333, "value": -65.15, "curve": [2.025, -65.15, 2.408, 53.84]}, {"time": 2.6, "value": 53.84, "curve": [2.62, 53.84, 2.642, 51.4]}, {"time": 2.6667, "value": 47.08}]}, "Tentacle3_Mid3": {"rotate": [{"value": 36.14, "curve": [0.137, 11.69, 0.303, -29.86]}, {"time": 0.4, "value": -29.86, "curve": [0.592, -29.86, 0.975, 56.4]}, {"time": 1.1667, "value": 56.4, "curve": [1.212, 56.4, 1.27, 48]}, {"time": 1.3333, "value": 36.14, "curve": [1.471, 11.69, 1.636, -29.86]}, {"time": 1.7333, "value": -29.86, "curve": [1.925, -29.86, 2.308, 56.4]}, {"time": 2.5, "value": 56.4, "curve": [2.545, 56.4, 2.603, 48]}, {"time": 2.6667, "value": 36.14}]}, "Tentacle2_Mid": {"rotate": [{"value": 16.89, "curve": [0.121, -7.13, 0.251, -36.3]}, {"time": 0.3333, "value": -36.3, "curve": [0.525, -36.3, 0.908, 49.96]}, {"time": 1.1, "value": 49.96, "curve": [1.16, 49.96, 1.244, 34.77]}, {"time": 1.3333, "value": 16.89, "curve": [1.455, -7.13, 1.585, -36.3]}, {"time": 1.6667, "value": -36.3, "curve": [1.858, -36.3, 2.242, 49.96]}, {"time": 2.4333, "value": 49.96, "curve": [2.493, 49.96, 2.578, 34.77]}, {"time": 2.6667, "value": 16.89}]}, "Tentacle6": {"rotate": [{"value": 36.14, "curve": [0.192, 36.14, 0.575, -10.68]}, {"time": 0.7667, "value": -10.68, "curve": [0.908, -10.68, 1.192, 36.14]}, {"time": 1.3333, "value": 36.14, "curve": [1.525, 36.14, 1.908, -10.68]}, {"time": 2.1, "value": -10.68, "curve": [2.242, -10.68, 2.525, 36.14]}, {"time": 2.6667, "value": 36.14}]}, "Tentacle3_Btm3": {"rotate": [{"value": 61.89, "curve": [0.146, 35.84, 0.353, -37.96]}, {"time": 0.4667, "value": -37.96, "curve": [0.658, -37.96, 1.042, 73.54]}, {"time": 1.2333, "value": 73.54, "curve": [1.262, 73.54, 1.296, 69.14]}, {"time": 1.3333, "value": 61.89, "curve": [1.48, 35.84, 1.687, -37.96]}, {"time": 1.8, "value": -37.96, "curve": [1.992, -37.96, 2.375, 73.54]}, {"time": 2.5667, "value": 73.54, "curve": [2.596, 73.54, 2.629, 69.14]}, {"time": 2.6667, "value": 61.89}]}, "Tentacle2": {"rotate": [{"value": 36.14, "curve": [0.192, 36.14, 0.575, -10.68]}, {"time": 0.7667, "value": -10.68, "curve": [0.908, -10.68, 1.192, 36.14]}, {"time": 1.3333, "value": 36.14, "curve": [1.525, 36.14, 1.908, -10.68]}, {"time": 2.1, "value": -10.68, "curve": [2.242, -10.68, 2.525, 36.14]}, {"time": 2.6667, "value": 36.14}]}, "root": {"scale": [{}, {"time": 0.0667, "x": 1.026, "y": 0.983}, {"time": 0.1333}, {"time": 0.2, "x": 1.026, "y": 0.983}, {"time": 0.2667}, {"time": 0.3333, "x": 1.026, "y": 0.983}, {"time": 0.4}, {"time": 0.4667, "x": 1.026, "y": 0.983}, {"time": 0.5333}, {"time": 0.6, "x": 1.026, "y": 0.983}, {"time": 0.6667}, {"time": 0.7333, "x": 1.026, "y": 0.983}, {"time": 0.8}, {"time": 0.8667, "x": 1.026, "y": 0.983}, {"time": 0.9333}, {"time": 1, "x": 1.026, "y": 0.983}, {"time": 1.0667}, {"time": 1.1333, "x": 1.026, "y": 0.983}, {"time": 1.2}, {"time": 1.2667, "x": 1.026, "y": 0.983}, {"time": 1.3333}, {"time": 1.4, "x": 1.026, "y": 0.983}, {"time": 1.4667}, {"time": 1.5333, "x": 1.026, "y": 0.983}, {"time": 1.6}, {"time": 1.6667, "x": 1.026, "y": 0.983}, {"time": 1.7333}, {"time": 1.8, "x": 1.026, "y": 0.983}, {"time": 1.8667}, {"time": 1.9333, "x": 1.026, "y": 0.983}, {"time": 2}, {"time": 2.0667, "x": 1.026, "y": 0.983}, {"time": 2.1333}, {"time": 2.2, "x": 1.026, "y": 0.983}, {"time": 2.2667}, {"time": 2.3333, "x": 1.026, "y": 0.983}, {"time": 2.4}, {"time": 2.4667, "x": 1.026, "y": 0.983}, {"time": 2.5333}, {"time": 2.6, "x": 1.026, "y": 0.983}]}}}, "anticipate-spikes": {"slots": {"Eyes": {"attachment": [{"name": "EyesClosed"}]}, "images/Eye3": {"attachment": [{}]}, "Mouth": {"attachment": [{"name": "Mouth Closed"}]}, "MouthBack": {"attachment": [{"name": "Mouth Back Shut"}]}, "ScuttleEye3": {"attachment": [{"name": "<PERSON><PERSON><PERSON>_Eye_Closed"}]}}, "bones": {"FACE": {"translate": [{"x": 43.75, "curve": [0.042, 43.75, 0.125, 62.2, 0.042, 0, 0.125, -0.4]}, {"time": 0.1667, "x": 62.2, "y": -0.4, "curve": [0.352, -27.96, 1.042, -70.81, 0.352, -0.13, 1.042, 0]}, {"time": 1.3333, "x": -70.81}], "scale": [{}, {"time": 0.0667, "x": 0.851, "y": 0.851}, {"time": 0.1333}, {"time": 0.2, "x": 0.851, "y": 0.851}, {"time": 0.2667}, {"time": 0.3333, "x": 0.851, "y": 0.851}, {"time": 0.4}, {"time": 0.4667, "x": 0.851, "y": 0.851}, {"time": 0.5333}, {"time": 0.6, "x": 0.851, "y": 0.851}, {"time": 0.6667}, {"time": 0.7333, "x": 0.851, "y": 0.851}, {"time": 0.8}, {"time": 0.8667, "x": 0.851, "y": 0.851}, {"time": 0.9333}, {"time": 1, "x": 0.851, "y": 0.851}, {"time": 1.0667}, {"time": 1.1333, "x": 0.851, "y": 0.851}, {"time": 1.2}, {"time": 1.2667, "x": 0.851, "y": 0.851}, {"time": 1.3333}, {"time": 1.4, "x": 0.851, "y": 0.851}, {"time": 1.4667}, {"time": 1.5333, "x": 0.851, "y": 0.851}, {"time": 1.6}, {"time": 1.6667, "x": 0.851, "y": 0.851}, {"time": 1.7333}, {"time": 1.8, "x": 0.851, "y": 0.851}, {"time": 1.8667}, {"time": 1.9333, "x": 0.851, "y": 0.851}, {"time": 2}, {"time": 2.0667, "x": 0.851, "y": 0.851}, {"time": 2.1333}, {"time": 2.2, "x": 0.851, "y": 0.851}, {"time": 2.2667}, {"time": 2.3333, "x": 0.851, "y": 0.851}, {"time": 2.4}, {"time": 2.4667, "x": 0.851, "y": 0.851}, {"time": 2.5333}, {"time": 2.6, "x": 0.851, "y": 0.851}, {"time": 2.6667}]}, "MAIN": {"translate": [{"y": 17.53, "curve": [0.31, 0, 0.333, 0, 0.31, 17.53, 0.333, -32.5]}, {"time": 0.6667, "y": -33.8, "curve": [0.976, 0, 1, 0, 0.976, -33.8, 1, 16.22]}, {"time": 1.3333, "y": 17.53, "curve": [1.643, 0, 1.667, 0, 1.643, 17.53, 1.667, -32.5]}, {"time": 2, "y": -33.8, "curve": [2.31, 0, 2.333, 0, 2.31, -33.8, 2.333, 16.22]}, {"time": 2.6667, "y": 17.53}], "scale": [{"x": 0.987, "y": 1.006, "curve": [0.125, 0.962, 0.25, 0.937, 0.125, 1.028, 0.25, 1.05]}, {"time": 0.3333, "x": 0.937, "y": 1.05, "curve": [0.5, 0.937, 0.833, 1.038, 0.5, 1.05, 0.833, 0.962]}, {"time": 1, "x": 1.038, "y": 0.962, "curve": [1.083, 1.038, 1.208, 1.013, 1.083, 0.962, 1.208, 0.984]}, {"time": 1.3333, "x": 0.987, "y": 1.006, "curve": [1.458, 0.962, 1.583, 0.937, 1.458, 1.028, 1.583, 1.05]}, {"time": 1.6667, "x": 0.937, "y": 1.05, "curve": [1.833, 0.937, 2.167, 1.038, 1.833, 1.05, 2.167, 0.962]}, {"time": 2.3333, "x": 1.038, "y": 0.962, "curve": [2.417, 1.038, 2.542, 1.013, 2.417, 0.962, 2.542, 0.984]}, {"time": 2.6667, "x": 0.987, "y": 1.006}]}, "Spike5": {"rotate": [{"value": 0.22, "curve": [0.025, 0.22, 0.075, 12.42]}, {"time": 0.1, "value": 12.42, "curve": [0.125, 12.42, 0.175, 0.22]}, {"time": 0.2, "value": 0.22, "curve": [0.225, 0.22, 0.275, 12.42]}, {"time": 0.3, "value": 12.42, "curve": [0.325, 12.42, 0.375, 0.22]}, {"time": 0.4, "value": 0.22, "curve": [0.417, 0.22, 0.45, 12.42]}, {"time": 0.4667, "value": 12.42, "curve": [0.492, 12.42, 0.542, 0.22]}, {"time": 0.5667, "value": 0.22, "curve": [0.592, 0.22, 0.642, 12.42]}, {"time": 0.6667, "value": 12.42, "curve": [0.692, 12.42, 0.742, 0.22]}, {"time": 0.7667, "value": 0.22, "curve": [0.792, 0.22, 0.842, 12.42]}, {"time": 0.8667, "value": 12.42, "curve": [0.892, 12.42, 0.942, 0.22]}, {"time": 0.9667, "value": 0.22, "curve": [0.992, 0.22, 1.042, 12.42]}, {"time": 1.0667, "value": 12.42, "curve": [1.092, 12.42, 1.142, 0.22]}, {"time": 1.1667, "value": 0.22, "curve": [1.183, 0.22, 1.217, 12.42]}, {"time": 1.2333, "value": 12.42, "curve": [1.258, 12.42, 1.308, 0.22]}, {"time": 1.3333, "value": 0.22}]}, "Spike4": {"rotate": [{"value": 7.32, "curve": [0.013, 10.37, 0.025, 13.42]}, {"time": 0.0333, "value": 13.42, "curve": [0.058, 13.42, 0.108, 1.22]}, {"time": 0.1333, "value": 1.22, "curve": [0.158, 1.22, 0.208, 13.42]}, {"time": 0.2333, "value": 13.42, "curve": [0.258, 13.42, 0.308, 1.22]}, {"time": 0.3333, "value": 1.22, "curve": [0.358, 1.22, 0.408, 13.42]}, {"time": 0.4333, "value": 13.42, "curve": [0.458, 13.42, 0.508, 1.22]}, {"time": 0.5333, "value": 1.22, "curve": [0.55, 1.22, 0.583, 13.42]}, {"time": 0.6, "value": 13.42, "curve": [0.625, 13.42, 0.675, 1.22]}, {"time": 0.7, "value": 1.22, "curve": [0.725, 1.22, 0.775, 13.42]}, {"time": 0.8, "value": 13.42, "curve": [0.825, 13.42, 0.875, 1.22]}, {"time": 0.9, "value": 1.22, "curve": [0.925, 1.22, 0.975, 13.42]}, {"time": 1, "value": 13.42, "curve": [1.025, 13.42, 1.075, 1.22]}, {"time": 1.1, "value": 1.22, "curve": [1.125, 1.22, 1.175, 13.42]}, {"time": 1.2, "value": 13.42, "curve": [1.225, 13.42, 1.275, 1.22]}, {"time": 1.3, "value": 1.22, "curve": [1.308, 1.22, 1.321, 4.27]}, {"time": 1.3333, "value": 7.32}]}, "Spike3": {"rotate": [{"value": -15.85, "curve": [0.025, -15.85, 0.075, -3.65]}, {"time": 0.1, "value": -3.65, "curve": [0.125, -3.65, 0.175, -15.85]}, {"time": 0.2, "value": -15.85, "curve": [0.225, -15.85, 0.275, -3.65]}, {"time": 0.3, "value": -3.65, "curve": [0.325, -3.65, 0.375, -15.85]}, {"time": 0.4, "value": -15.85, "curve": [0.417, -15.85, 0.45, -3.65]}, {"time": 0.4667, "value": -3.65, "curve": [0.492, -3.65, 0.542, -15.85]}, {"time": 0.5667, "value": -15.85, "curve": [0.592, -15.85, 0.642, -3.65]}, {"time": 0.6667, "value": -3.65, "curve": [0.692, -3.65, 0.742, -15.85]}, {"time": 0.7667, "value": -15.85, "curve": [0.792, -15.85, 0.842, -3.65]}, {"time": 0.8667, "value": -3.65, "curve": [0.892, -3.65, 0.942, -15.85]}, {"time": 0.9667, "value": -15.85, "curve": [0.992, -15.85, 1.042, -3.65]}, {"time": 1.0667, "value": -3.65, "curve": [1.092, -3.65, 1.142, -15.85]}, {"time": 1.1667, "value": -15.85, "curve": [1.183, -15.85, 1.217, -3.65]}, {"time": 1.2333, "value": -3.65, "curve": [1.258, -3.65, 1.308, -15.85]}, {"time": 1.3333, "value": -15.85}]}, "Spike2": {"rotate": [{"value": -17.63, "curve": [0.013, -19.61, 0.025, -21.09]}, {"time": 0.0333, "value": -21.09, "curve": [0.058, -21.09, 0.108, -8.89]}, {"time": 0.1333, "value": -8.89, "curve": [0.158, -8.89, 0.208, -21.09]}, {"time": 0.2333, "value": -21.09, "curve": [0.258, -21.09, 0.308, -8.89]}, {"time": 0.3333, "value": -8.89, "curve": [0.358, -8.89, 0.408, -21.09]}, {"time": 0.4333, "value": -21.09, "curve": [0.45, -21.09, 0.483, -8.89]}, {"time": 0.5, "value": -8.89, "curve": [0.525, -8.89, 0.575, -21.09]}, {"time": 0.6, "value": -21.09, "curve": [0.625, -21.09, 0.675, -8.89]}, {"time": 0.7, "value": -8.89, "curve": [0.725, -8.89, 0.775, -21.09]}, {"time": 0.8, "value": -21.09, "curve": [0.825, -21.09, 0.875, -8.89]}, {"time": 0.9, "value": -8.89, "curve": [0.925, -8.89, 0.975, -21.09]}, {"time": 1, "value": -21.09, "curve": [1.025, -21.09, 1.075, -8.89]}, {"time": 1.1, "value": -8.89, "curve": [1.125, -8.89, 1.175, -21.09]}, {"time": 1.2, "value": -21.09, "curve": [1.217, -21.09, 1.25, -8.89]}, {"time": 1.2667, "value": -8.89, "curve": [1.283, -8.89, 1.31, -14.14]}, {"time": 1.3333, "value": -17.63}]}, "Spike1": {"rotate": [{"value": -23.57, "curve": [0.075, -23.57, 0.225, 10.75]}, {"time": 0.3, "value": 10.75, "curve": [0.392, 10.75, 0.575, -11.37]}, {"time": 0.6667, "value": -11.37, "curve": [0.692, -11.37, 0.742, -23.57]}, {"time": 0.7667, "value": -23.57, "curve": [0.792, -23.57, 0.842, -11.37]}, {"time": 0.8667, "value": -11.37, "curve": [0.892, -11.37, 0.942, -23.57]}, {"time": 0.9667, "value": -23.57, "curve": [0.992, -23.57, 1.042, -11.37]}, {"time": 1.0667, "value": -11.37, "curve": [1.092, -11.37, 1.142, -23.57]}, {"time": 1.1667, "value": -23.57, "curve": [1.183, -23.57, 1.217, -11.37]}, {"time": 1.2333, "value": -11.37, "curve": [1.258, -11.37, 1.308, -23.57]}, {"time": 1.3333, "value": -23.57}]}, "Spike10": {"rotate": [{"value": 24.29, "curve": [0.075, 24.29, 0.225, -9.92]}, {"time": 0.3, "value": -9.92, "curve": [0.392, -9.92, 0.575, 16.34]}, {"time": 0.6667, "value": 16.34, "curve": [0.692, 16.34, 0.742, 24.29]}, {"time": 0.7667, "value": 24.29, "curve": [0.792, 24.29, 0.842, 16.34]}, {"time": 0.8667, "value": 16.34, "curve": [0.892, 16.34, 0.942, 24.29]}, {"time": 0.9667, "value": 24.29, "curve": [0.992, 24.29, 1.042, 16.34]}, {"time": 1.0667, "value": 16.34, "curve": [1.092, 16.34, 1.142, 24.29]}, {"time": 1.1667, "value": 24.29, "curve": [1.183, 24.29, 1.217, 16.34]}, {"time": 1.2333, "value": 16.34, "curve": [1.258, 16.34, 1.308, 24.29]}, {"time": 1.3333, "value": 24.29}]}, "Spike6": {"rotate": [{"value": 16.3, "curve": [0.013, 17.59, 0.025, 18.56]}, {"time": 0.0333, "value": 18.56, "curve": [0.058, 18.56, 0.108, 10.61]}, {"time": 0.1333, "value": 10.61, "curve": [0.158, 10.61, 0.208, 18.56]}, {"time": 0.2333, "value": 18.56, "curve": [0.258, 18.56, 0.308, 10.61]}, {"time": 0.3333, "value": 10.61, "curve": [0.358, 10.61, 0.408, 18.56]}, {"time": 0.4333, "value": 18.56, "curve": [0.45, 18.56, 0.483, 10.61]}, {"time": 0.5, "value": 10.61, "curve": [0.525, 10.61, 0.575, 18.56]}, {"time": 0.6, "value": 18.56, "curve": [0.625, 18.56, 0.675, 10.61]}, {"time": 0.7, "value": 10.61, "curve": [0.725, 10.61, 0.775, 18.56]}, {"time": 0.8, "value": 18.56, "curve": [0.825, 18.56, 0.875, 10.61]}, {"time": 0.9, "value": 10.61, "curve": [0.925, 10.61, 0.975, 18.56]}, {"time": 1, "value": 18.56, "curve": [1.025, 18.56, 1.075, 10.61]}, {"time": 1.1, "value": 10.61, "curve": [1.125, 10.61, 1.175, 18.56]}, {"time": 1.2, "value": 18.56, "curve": [1.217, 18.56, 1.25, 10.61]}, {"time": 1.2667, "value": 10.61, "curve": [1.283, 10.61, 1.31, 14.03]}, {"time": 1.3333, "value": 16.3}]}, "Spike7": {"rotate": [{"value": 14.37, "curve": [0.025, 14.37, 0.075, 6.42]}, {"time": 0.1, "value": 6.42, "curve": [0.125, 6.42, 0.175, 14.37]}, {"time": 0.2, "value": 14.37, "curve": [0.225, 14.37, 0.275, 6.42]}, {"time": 0.3, "value": 6.42, "curve": [0.325, 6.42, 0.375, 14.37]}, {"time": 0.4, "value": 14.37, "curve": [0.417, 14.37, 0.45, 6.42]}, {"time": 0.4667, "value": 6.42, "curve": [0.492, 6.42, 0.542, 14.37]}, {"time": 0.5667, "value": 14.37, "curve": [0.592, 14.37, 0.642, 6.42]}, {"time": 0.6667, "value": 6.42, "curve": [0.692, 6.42, 0.742, 14.37]}, {"time": 0.7667, "value": 14.37, "curve": [0.792, 14.37, 0.842, 6.42]}, {"time": 0.8667, "value": 6.42, "curve": [0.892, 6.42, 0.942, 14.37]}, {"time": 0.9667, "value": 14.37, "curve": [0.992, 14.37, 1.042, 6.42]}, {"time": 1.0667, "value": 6.42, "curve": [1.092, 6.42, 1.142, 14.37]}, {"time": 1.1667, "value": 14.37, "curve": [1.183, 14.37, 1.217, 6.42]}, {"time": 1.2333, "value": 6.42, "curve": [1.258, 6.42, 1.308, 14.37]}, {"time": 1.3333, "value": 14.37}]}, "Spike8": {"rotate": [{"value": -9.22, "curve": [0.013, -11.2, 0.025, -13.19]}, {"time": 0.0333, "value": -13.19, "curve": [0.058, -13.19, 0.108, -5.25]}, {"time": 0.1333, "value": -5.25, "curve": [0.158, -5.25, 0.208, -13.19]}, {"time": 0.2333, "value": -13.19, "curve": [0.258, -13.19, 0.308, -5.25]}, {"time": 0.3333, "value": -5.25, "curve": [0.358, -5.25, 0.408, -13.19]}, {"time": 0.4333, "value": -13.19, "curve": [0.458, -13.19, 0.508, -5.25]}, {"time": 0.5333, "value": -5.25, "curve": [0.55, -5.25, 0.583, -13.19]}, {"time": 0.6, "value": -13.19, "curve": [0.625, -13.19, 0.675, -5.25]}, {"time": 0.7, "value": -5.25, "curve": [0.725, -5.25, 0.775, -13.19]}, {"time": 0.8, "value": -13.19, "curve": [0.825, -13.19, 0.875, -5.25]}, {"time": 0.9, "value": -5.25, "curve": [0.925, -5.25, 0.975, -13.19]}, {"time": 1, "value": -13.19, "curve": [1.025, -13.19, 1.075, -5.25]}, {"time": 1.1, "value": -5.25, "curve": [1.125, -5.25, 1.175, -13.19]}, {"time": 1.2, "value": -13.19, "curve": [1.225, -13.19, 1.275, -5.25]}, {"time": 1.3, "value": -5.25, "curve": [1.308, -5.25, 1.321, -7.23]}, {"time": 1.3333, "value": -9.22}]}, "Spike9": {"rotate": [{"value": -4.45, "curve": [0.025, -4.45, 0.075, -12.39]}, {"time": 0.1, "value": -12.39, "curve": [0.125, -12.39, 0.175, -4.45]}, {"time": 0.2, "value": -4.45, "curve": [0.225, -4.45, 0.275, -12.39]}, {"time": 0.3, "value": -12.39, "curve": [0.325, -12.39, 0.375, -4.45]}, {"time": 0.4, "value": -4.45, "curve": [0.417, -4.45, 0.45, -12.39]}, {"time": 0.4667, "value": -12.39, "curve": [0.492, -12.39, 0.542, -4.45]}, {"time": 0.5667, "value": -4.45, "curve": [0.592, -4.45, 0.642, -12.39]}, {"time": 0.6667, "value": -12.39, "curve": [0.692, -12.39, 0.742, -4.45]}, {"time": 0.7667, "value": -4.45, "curve": [0.792, -4.45, 0.842, -12.39]}, {"time": 0.8667, "value": -12.39, "curve": [0.892, -12.39, 0.942, -4.45]}, {"time": 0.9667, "value": -4.45, "curve": [0.992, -4.45, 1.042, -12.39]}, {"time": 1.0667, "value": -12.39, "curve": [1.092, -12.39, 1.142, -4.45]}, {"time": 1.1667, "value": -4.45, "curve": [1.183, -4.45, 1.217, -12.39]}, {"time": 1.2333, "value": -12.39, "curve": [1.258, -12.39, 1.308, -4.45]}, {"time": 1.3333, "value": -4.45}]}, "DangleHandle": {"rotate": [{"value": 6.7, "curve": [0.043, 3.66, 0.088, 0.04]}, {"time": 0.1333, "value": -3.58, "curve": [0.258, -13.51, 0.383, -23.45]}, {"time": 0.4667, "value": -23.45, "curve": [0.633, -23.45, 0.967, 16.3]}, {"time": 1.1333, "value": 16.3, "curve": [1.187, 16.3, 1.257, 12.3]}, {"time": 1.3333, "value": 6.7}], "translate": [{"x": -3.96, "y": 2.8, "curve": [0.125, -27.88, 0.25, -51.8, 0.125, 1.82, 0.25, 0.85]}, {"time": 0.3333, "x": -51.8, "y": 0.85, "curve": [0.5, -51.8, 0.833, 43.87, 0.5, 0.85, 0.833, 4.74]}, {"time": 1, "x": 43.87, "y": 4.74, "curve": [1.083, 43.87, 1.208, 19.95, 1.083, 4.74, 1.208, 3.77]}, {"time": 1.3333, "x": -3.96, "y": 2.8}], "scale": [{"y": 1.723, "curve": [0.167, 1, 0.5, 1, 0.167, 1.723, 0.5, 0.313]}, {"time": 0.6667, "y": 0.313, "curve": [0.833, 1, 1.167, 1, 0.833, 0.313, 1.167, 1.723]}, {"time": 1.3333, "y": 1.723}]}, "Tentacles": {"scale": [{"x": 0.699}, {"time": 0.6667, "x": 1.199}, {"time": 1.3333, "x": 0.699}, {"time": 2, "x": 1.199}, {"time": 2.6667, "x": 0.699}]}, "Mouth": {"translate": [{"x": -0.72, "y": -42.85}]}, "Mouth8": {"rotate": [{"value": -82.52, "curve": [0.1, -82.52, 0.3, 55.9]}, {"time": 0.4, "value": 55.9, "curve": [0.592, 55.9, 0.975, -82.52]}, {"time": 1.1667, "value": -82.52}]}, "Mouth5": {"rotate": [{"value": 78.81, "curve": [0.1, 78.81, 0.3, -48.27]}, {"time": 0.4, "value": -48.27, "curve": [0.592, -48.27, 0.975, 107.9]}, {"time": 1.1667, "value": 107.9}]}, "Mouth4": {"rotate": [{"value": 66.64, "curve": [0.1, 66.64, 0.3, -55.86]}, {"time": 0.4, "value": -55.86, "curve": [0.592, -55.86, 0.975, 86.12]}, {"time": 1.1667, "value": 86.12}]}, "Mouth7": {"rotate": [{"value": -92.16, "curve": [0.1, -92.16, 0.3, 48.3]}, {"time": 0.4, "value": 48.3, "curve": [0.592, 48.3, 0.975, -100.21]}, {"time": 1.1667, "value": -100.21}]}, "Mouth3": {"rotate": [{"value": 57.71, "curve": [0.1, 57.71, 0.3, -88.97]}, {"time": 0.4, "value": -88.97, "curve": [0.592, -88.97, 0.975, 57.71]}, {"time": 1.1667, "value": 57.71}]}, "Mouth6": {"rotate": [{"value": -76.45, "curve": [0.1, -76.45, 0.3, 62.51]}, {"time": 0.4, "value": 62.51, "curve": [0.592, 62.51, 0.975, -76.45]}, {"time": 1.1667, "value": -76.45}]}, "Head": {"translate": [{"x": -5.2, "y": -0.09}, {"time": 0.0667, "x": 1.78, "y": 0.03}, {"time": 0.1333, "x": -5.2, "y": -0.09}, {"time": 0.2, "x": 1.78, "y": 0.03}, {"time": 0.2667, "x": -5.2, "y": -0.09}, {"time": 0.3333, "x": 1.78, "y": 0.03}, {"time": 0.4, "x": -5.2, "y": -0.09}, {"time": 0.4667, "x": 1.78, "y": 0.03}, {"time": 0.5333, "x": -5.2, "y": -0.09}, {"time": 0.6, "x": 1.78, "y": 0.03}, {"time": 0.6667, "x": -5.2, "y": -0.09}, {"time": 0.7333, "x": 1.78, "y": 0.03}, {"time": 0.8, "x": -5.2, "y": -0.09}, {"time": 0.8667, "x": 1.78, "y": 0.03}, {"time": 0.9333, "x": -5.2, "y": -0.09}, {"time": 1, "x": 1.78, "y": 0.03}, {"time": 1.0667, "x": -5.2, "y": -0.09}, {"time": 1.1333, "x": 1.78, "y": 0.03}, {"time": 1.2, "x": -5.2, "y": -0.09}, {"time": 1.2667, "x": 1.78, "y": 0.03}, {"time": 1.3333, "x": -5.2, "y": -0.09}, {"time": 1.4, "x": 1.78, "y": 0.03}, {"time": 1.4667, "x": -5.2, "y": -0.09}, {"time": 1.5333, "x": 1.78, "y": 0.03}, {"time": 1.6, "x": -5.2, "y": -0.09}, {"time": 1.6667, "x": 1.78, "y": 0.03}, {"time": 1.7333, "x": -5.2, "y": -0.09}, {"time": 1.8, "x": 1.78, "y": 0.03, "curve": "stepped"}, {"time": 1.8667, "x": 1.78, "y": 0.03}, {"time": 1.9333, "x": -5.2, "y": -0.09}, {"time": 2, "x": 1.78, "y": 0.03}, {"time": 2.0667, "x": -5.2, "y": -0.09}, {"time": 2.1333, "x": 1.78, "y": 0.03}, {"time": 2.2, "x": -5.2, "y": -0.09}, {"time": 2.2667, "x": 1.78, "y": 0.03}, {"time": 2.3333, "x": -5.2, "y": -0.09}, {"time": 2.4, "x": 1.78, "y": 0.03}, {"time": 2.4667, "x": -5.2, "y": -0.09}, {"time": 2.5333, "x": 1.78, "y": 0.03}, {"time": 2.6, "x": -5.2, "y": -0.09}, {"time": 2.6667, "x": 1.78, "y": 0.03}], "scale": [{"x": 0.746, "y": 1.272, "curve": [0.042, 0.746, 0.125, 1.153, 0.042, 1.272, 0.125, 0.891]}, {"time": 0.1667, "x": 1.153, "y": 0.891, "curve": [0.308, 1.153, 0.592, 0.805, 0.308, 0.891, 0.592, 1.243]}, {"time": 0.7333, "x": 0.805, "y": 1.243}]}, "Tentacle3_Btm4": {"rotate": [{"value": 51.6, "curve": [0.146, 27.35, 0.353, -41.36]}, {"time": 0.4667, "value": -41.36, "curve": [0.658, -41.36, 1.042, 62.45]}, {"time": 1.2333, "value": 62.45, "curve": [1.262, 62.45, 1.296, 58.35]}, {"time": 1.3333, "value": 51.6, "curve": [1.48, 27.35, 1.687, -41.36]}, {"time": 1.8, "value": -41.36, "curve": [1.992, -41.36, 2.375, 62.45]}, {"time": 2.5667, "value": 62.45, "curve": [2.596, 62.45, 2.629, 58.35]}, {"time": 2.6667, "value": 51.6}]}, "Tentacle1": {"rotate": [{"value": -49.78, "curve": [0.192, -49.78, 0.575, 9.39]}, {"time": 0.7667, "value": 9.39, "curve": [0.908, 9.39, 1.192, -49.78]}, {"time": 1.3333, "value": -49.78, "curve": [1.525, -49.78, 1.908, 9.39]}, {"time": 2.1, "value": 9.39, "curve": [2.242, 9.39, 2.525, -49.78]}, {"time": 2.6667, "value": -49.78}]}, "Tentacle3_Mid4": {"rotate": [{"value": 40.71, "curve": [0.137, 14.47, 0.303, -30.13]}, {"time": 0.4, "value": -30.13, "curve": [0.592, -30.13, 0.975, 62.45]}, {"time": 1.1667, "value": 62.45, "curve": [1.212, 62.45, 1.27, 53.43]}, {"time": 1.3333, "value": 40.71, "curve": [1.471, 14.47, 1.636, -30.13]}, {"time": 1.7333, "value": -30.13, "curve": [1.925, -30.13, 2.308, 62.45]}, {"time": 2.5, "value": 62.45, "curve": [2.545, 62.45, 2.603, 53.43]}, {"time": 2.6667, "value": 40.71}]}, "Tentacle3": {"rotate": [{"value": -36.75, "curve": [0.192, -36.75, 0.575, 6.68]}, {"time": 0.7667, "value": 6.68, "curve": [0.908, 6.68, 1.192, -36.75]}, {"time": 1.3333, "value": -36.75, "curve": [1.525, -36.75, 1.908, 6.68]}, {"time": 2.1, "value": 6.68, "curve": [2.242, 6.68, 2.525, -36.75]}, {"time": 2.6667, "value": -36.75}]}, "Tentacle3_Mid": {"rotate": [{"value": 51.86, "curve": [0.03, 48.48, 0.064, 42.9]}, {"time": 0.1, "value": 36.14, "curve": [0.237, 11.69, 0.403, -29.86]}, {"time": 0.5, "value": -29.86, "curve": [0.692, -29.86, 1.075, 56.4]}, {"time": 1.2667, "value": 56.4, "curve": [1.287, 56.4, 1.309, 54.76]}, {"time": 1.3333, "value": 51.86, "curve": [1.364, 48.48, 1.398, 42.9]}, {"time": 1.4333, "value": 36.14, "curve": [1.571, 11.69, 1.736, -29.86]}, {"time": 1.8333, "value": -29.86, "curve": [2.025, -29.86, 2.408, 56.4]}, {"time": 2.6, "value": 56.4, "curve": [2.62, 56.4, 2.642, 54.76]}, {"time": 2.6667, "value": 51.86}]}, "Tentacle5": {"rotate": [{"value": -49.78, "curve": [0.192, -49.78, 0.575, 9.39]}, {"time": 0.7667, "value": 9.39, "curve": [0.908, 9.39, 1.192, -49.78]}, {"time": 1.3333, "value": -49.78, "curve": [1.525, -49.78, 1.908, 9.39]}, {"time": 2.1, "value": 9.39, "curve": [2.242, 9.39, 2.525, -49.78]}, {"time": 2.6667, "value": -49.78}]}, "Tentacle3_Btm": {"rotate": [{"value": 73.54, "curve": [0.029, 73.54, 0.063, 69.14]}, {"time": 0.1, "value": 61.89, "curve": [0.246, 35.84, 0.453, -37.96]}, {"time": 0.5667, "value": -37.96, "curve": [0.758, -37.96, 1.142, 73.54]}, {"time": 1.3333, "value": 73.54, "curve": [1.362, 73.54, 1.396, 69.14]}, {"time": 1.4333, "value": 61.89, "curve": [1.58, 35.84, 1.787, -37.96]}, {"time": 1.9, "value": -37.96, "curve": [2.092, -37.96, 2.475, 73.54]}, {"time": 2.6667, "value": 73.54}]}, "Tentacle3_Mid2": {"rotate": [{"value": 52.81, "curve": [0.021, 49.53, 0.044, 45.39]}, {"time": 0.0667, "value": 40.71, "curve": [0.204, 14.47, 0.37, -30.13]}, {"time": 0.4667, "value": -30.13, "curve": [0.658, -30.13, 1.042, 62.45]}, {"time": 1.2333, "value": 62.45, "curve": [1.262, 62.45, 1.296, 58.81]}, {"time": 1.3333, "value": 52.81, "curve": [1.354, 49.53, 1.377, 45.39]}, {"time": 1.4, "value": 40.71, "curve": [1.537, 14.47, 1.703, -30.13]}, {"time": 1.8, "value": -30.13, "curve": [1.992, -30.13, 2.375, 62.45]}, {"time": 2.5667, "value": 62.45, "curve": [2.596, 62.45, 2.629, 58.81]}, {"time": 2.6667, "value": 52.81}]}, "Tentacle3_Btm2": {"rotate": [{"value": 60.88, "curve": [0.02, 59.21, 0.043, 55.98]}, {"time": 0.0667, "value": 51.6, "curve": [0.213, 27.35, 0.42, -41.36]}, {"time": 0.5333, "value": -41.36, "curve": [0.725, -41.36, 1.108, 62.45]}, {"time": 1.3, "value": 62.45, "curve": [1.311, 62.45, 1.322, 61.9]}, {"time": 1.3333, "value": 60.88, "curve": [1.354, 59.21, 1.376, 55.98]}, {"time": 1.4, "value": 51.6, "curve": [1.546, 27.35, 1.753, -41.36]}, {"time": 1.8667, "value": -41.36, "curve": [2.058, -41.36, 2.442, 62.45]}, {"time": 2.6333, "value": 62.45, "curve": [2.644, 62.45, 2.655, 61.9]}, {"time": 2.6667, "value": 60.88}]}, "Tentacle2_Btm2": {"rotate": [{"value": 55.68, "curve": [0.148, 34.28, 0.378, -56.67]}, {"time": 0.5, "value": -56.67, "curve": [0.692, -56.67, 1.075, 62.45]}, {"time": 1.2667, "value": 62.45, "curve": [1.287, 62.45, 1.309, 60]}, {"time": 1.3333, "value": 55.68, "curve": [1.481, 34.28, 1.711, -56.67]}, {"time": 1.8333, "value": -56.67, "curve": [2.025, -56.67, 2.408, 62.45]}, {"time": 2.6, "value": 62.45, "curve": [2.62, 62.45, 2.642, 60]}, {"time": 2.6667, "value": 55.68}]}, "Tentacle2_Mid2": {"rotate": [{"value": 26.96, "curve": [0.121, 1.18, 0.251, -30.13]}, {"time": 0.3333, "value": -30.13, "curve": [0.525, -30.13, 0.908, 62.45]}, {"time": 1.1, "value": 62.45, "curve": [1.16, 62.45, 1.244, 46.14]}, {"time": 1.3333, "value": 26.96, "curve": [1.455, 1.18, 1.585, -30.13]}, {"time": 1.6667, "value": -30.13, "curve": [1.858, -30.13, 2.242, 62.45]}, {"time": 2.4333, "value": 62.45, "curve": [2.493, 62.45, 2.578, 46.14]}, {"time": 2.6667, "value": 26.96}]}, "Tentacle4": {"rotate": [{"value": 26.28, "curve": [0.192, 26.28, 0.575, -10.68]}, {"time": 0.7667, "value": -10.68, "curve": [0.908, -10.68, 1.192, 26.28]}, {"time": 1.3333, "value": 26.28, "curve": [1.525, 26.28, 1.908, -10.68]}, {"time": 2.1, "value": -10.68, "curve": [2.242, -10.68, 2.525, 26.28]}, {"time": 2.6667, "value": 26.28}]}, "Tentacle2_Btm": {"rotate": [{"value": 47.08, "curve": [0.148, 25.7, 0.378, -65.15]}, {"time": 0.5, "value": -65.15, "curve": [0.692, -65.15, 1.075, 53.84]}, {"time": 1.2667, "value": 53.84, "curve": [1.287, 53.84, 1.309, 51.4]}, {"time": 1.3333, "value": 47.08, "curve": [1.481, 25.7, 1.711, -65.15]}, {"time": 1.8333, "value": -65.15, "curve": [2.025, -65.15, 2.408, 53.84]}, {"time": 2.6, "value": 53.84, "curve": [2.62, 53.84, 2.642, 51.4]}, {"time": 2.6667, "value": 47.08}]}, "Tentacle3_Mid3": {"rotate": [{"value": 36.14, "curve": [0.137, 11.69, 0.303, -29.86]}, {"time": 0.4, "value": -29.86, "curve": [0.592, -29.86, 0.975, 56.4]}, {"time": 1.1667, "value": 56.4, "curve": [1.212, 56.4, 1.27, 48]}, {"time": 1.3333, "value": 36.14, "curve": [1.471, 11.69, 1.636, -29.86]}, {"time": 1.7333, "value": -29.86, "curve": [1.925, -29.86, 2.308, 56.4]}, {"time": 2.5, "value": 56.4, "curve": [2.545, 56.4, 2.603, 48]}, {"time": 2.6667, "value": 36.14}]}, "Tentacle2_Mid": {"rotate": [{"value": 16.89, "curve": [0.121, -7.13, 0.251, -36.3]}, {"time": 0.3333, "value": -36.3, "curve": [0.525, -36.3, 0.908, 49.96]}, {"time": 1.1, "value": 49.96, "curve": [1.16, 49.96, 1.244, 34.77]}, {"time": 1.3333, "value": 16.89, "curve": [1.455, -7.13, 1.585, -36.3]}, {"time": 1.6667, "value": -36.3, "curve": [1.858, -36.3, 2.242, 49.96]}, {"time": 2.4333, "value": 49.96, "curve": [2.493, 49.96, 2.578, 34.77]}, {"time": 2.6667, "value": 16.89}]}, "Tentacle6": {"rotate": [{"value": 36.14, "curve": [0.192, 36.14, 0.575, -10.68]}, {"time": 0.7667, "value": -10.68, "curve": [0.908, -10.68, 1.192, 36.14]}, {"time": 1.3333, "value": 36.14, "curve": [1.525, 36.14, 1.908, -10.68]}, {"time": 2.1, "value": -10.68, "curve": [2.242, -10.68, 2.525, 36.14]}, {"time": 2.6667, "value": 36.14}]}, "Tentacle3_Btm3": {"rotate": [{"value": 61.89, "curve": [0.146, 35.84, 0.353, -37.96]}, {"time": 0.4667, "value": -37.96, "curve": [0.658, -37.96, 1.042, 73.54]}, {"time": 1.2333, "value": 73.54, "curve": [1.262, 73.54, 1.296, 69.14]}, {"time": 1.3333, "value": 61.89, "curve": [1.48, 35.84, 1.687, -37.96]}, {"time": 1.8, "value": -37.96, "curve": [1.992, -37.96, 2.375, 73.54]}, {"time": 2.5667, "value": 73.54, "curve": [2.596, 73.54, 2.629, 69.14]}, {"time": 2.6667, "value": 61.89}]}, "Tentacle2": {"rotate": [{"value": 36.14, "curve": [0.192, 36.14, 0.575, -10.68]}, {"time": 0.7667, "value": -10.68, "curve": [0.908, -10.68, 1.192, 36.14]}, {"time": 1.3333, "value": 36.14, "curve": [1.525, 36.14, 1.908, -10.68]}, {"time": 2.1, "value": -10.68, "curve": [2.242, -10.68, 2.525, 36.14]}, {"time": 2.6667, "value": 36.14}]}, "root": {"scale": [{}, {"time": 0.0667, "x": 1.026, "y": 0.983}, {"time": 0.1333}, {"time": 0.2, "x": 1.026, "y": 0.983}, {"time": 0.2667}, {"time": 0.3333, "x": 1.026, "y": 0.983}, {"time": 0.4}, {"time": 0.4667, "x": 1.026, "y": 0.983}, {"time": 0.5333}, {"time": 0.6, "x": 1.026, "y": 0.983}, {"time": 0.6667}, {"time": 0.7333, "x": 1.026, "y": 0.983}, {"time": 0.8}, {"time": 0.8667, "x": 1.026, "y": 0.983}, {"time": 0.9333}, {"time": 1, "x": 1.026, "y": 0.983}, {"time": 1.0667}, {"time": 1.1333, "x": 1.026, "y": 0.983}, {"time": 1.2}, {"time": 1.2667, "x": 1.026, "y": 0.983}, {"time": 1.3333}, {"time": 1.4, "x": 1.026, "y": 0.983}, {"time": 1.4667}, {"time": 1.5333, "x": 1.026, "y": 0.983}, {"time": 1.6}, {"time": 1.6667, "x": 1.026, "y": 0.983}, {"time": 1.7333, "curve": "stepped"}, {"time": 1.8}, {"time": 1.8667, "x": 1.026, "y": 0.983}, {"time": 1.9333}, {"time": 2, "x": 1.026, "y": 0.983}, {"time": 2.0667}, {"time": 2.1333, "x": 1.026, "y": 0.983}, {"time": 2.2}, {"time": 2.2667, "x": 1.026, "y": 0.983}, {"time": 2.3333}, {"time": 2.4, "x": 1.026, "y": 0.983}, {"time": 2.4667}, {"time": 2.5333, "x": 1.026, "y": 0.983}, {"time": 2.6}, {"time": 2.6667, "x": 1.026, "y": 0.983}]}}}, "attack": {"bones": {"FACE": {"translate": [{"x": 0.84, "y": 4.16, "curve": [0.017, -23.65, 0.036, -44.45, 0.017, 3.67, 0.036, 3.25]}, {"time": 0.0667, "x": -45.76, "y": 3.22, "curve": [0.133, -45.76, 0.267, 18.32, 0.133, 3.22, 0.267, 6.16]}, {"time": 0.3333, "x": 18.32, "y": 6.16, "curve": [0.425, 18.32, 0.608, 12.06, 0.425, 6.16, 0.608, 4.16]}, {"time": 0.7, "x": 12.06, "y": 4.16, "curve": [0.842, 12.06, 1.125, -21.84, 0.842, 4.16, 1.125, 4.16]}, {"time": 1.2667, "x": -21.84, "y": 4.16, "curve": [1.464, -21.84, 1.552, -5.93, 1.464, 4.16, 1.552, 4.16]}, {"time": 1.6667, "x": 7.05, "y": 4.16}], "scale": [{}, {"time": 0.0667, "x": 1.219, "y": 1.219}, {"time": 0.2}]}, "MAIN": {"translate": [{"y": -33.8, "curve": [0, 0, 0.25, 0, 0, -1.14, 0.25, 16.22]}, {"time": 0.5, "y": 17.53, "curve": [0.732, 0, 0.75, 0, 0.732, 17.53, 0.75, -89.61]}, {"time": 1, "y": -92.41, "curve": [1.31, 0, 1.333, 0, 1.31, -92.41, 1.333, 14.73]}, {"time": 1.6667, "y": 17.53}], "scale": [{"x": 1.509, "y": 0.663, "curve": [0, 0.959, 0.05, 0.76, 0, 1.189, 0.05, 1.379]}, {"time": 0.0667, "x": 0.76, "y": 1.379, "curve": [0.108, 0.76, 0.192, 1.267, 0.108, 1.379, 0.192, 0.835]}, {"time": 0.2333, "x": 1.267, "y": 0.835, "curve": [0.3, 1.267, 0.433, 0.937, 0.3, 0.835, 0.433, 1.05]}, {"time": 0.5, "x": 0.937, "y": 1.05, "curve": [0.625, 0.937, 0.875, 0.987, 0.625, 1.05, 0.875, 1.006]}, {"time": 1, "x": 0.987, "y": 1.006, "curve": [1.125, 1.013, 1.25, 1.038, 1.125, 0.984, 1.25, 0.962]}, {"time": 1.3333, "x": 1.038, "y": 0.962, "curve": [1.417, 1.038, 1.542, 1.013, 1.417, 0.962, 1.542, 0.984]}, {"time": 1.6667, "x": 0.987, "y": 1.006}]}, "Spike5": {"rotate": [{"value": -43.86, "curve": [0.025, 24.37, 0.2, 24.37]}, {"time": 0.2667, "value": 24.37, "curve": [0.333, 24.37, 0.467, -26.01]}, {"time": 0.5333, "value": -26.01, "curve": [0.617, -26.01, 0.783, 12.93]}, {"time": 0.8667, "value": 12.93, "curve": [0.942, 12.93, 1.092, -8.26]}, {"time": 1.1667, "value": -8.26, "curve": [1.23, -10.3, 1.287, -11.66]}, {"time": 1.3333, "value": -11.66, "curve": [1.414, -11.66, 1.556, -1.65]}, {"time": 1.6667, "value": 3.35}]}, "Spike4": {"rotate": [{"value": -45.19, "curve": [0.022, 23.45, 0.175, 23.45]}, {"time": 0.2333, "value": 23.45, "curve": [0.3, 23.45, 0.433, -27.61]}, {"time": 0.5, "value": -27.61, "curve": [0.583, -27.61, 0.75, 11.47]}, {"time": 0.8333, "value": 11.47, "curve": [0.908, 11.47, 1.058, -9.26]}, {"time": 1.1333, "value": -9.26, "curve": [1.183, -10.73, 1.229, -11.66]}, {"time": 1.2667, "value": -11.66, "curve": [1.364, -11.66, 1.539, -0.22]}, {"time": 1.6667, "value": 4.35}]}, "Spike3": {"rotate": [{"value": -46.31, "curve": [0.019, 18.03, 0.15, 18.03]}, {"time": 0.2, "value": 18.03, "curve": [0.267, 18.03, 0.4, -27.12]}, {"time": 0.4667, "value": -27.12, "curve": [0.55, -27.12, 0.717, 11.11]}, {"time": 0.8, "value": 11.11, "curve": [0.875, 11.11, 1.025, -10.13]}, {"time": 1.1, "value": -10.13, "curve": [1.243, -6.23, 1.453, 6.75]}, {"time": 1.5667, "value": 6.75, "curve": [1.596, 6.75, 1.63, 6.18]}, {"time": 1.6667, "value": 5.22}]}, "Spike2": {"rotate": [{"value": -40.82, "curve": [0.016, 25.68, 0.125, 25.68]}, {"time": 0.1667, "value": 25.68, "curve": [0.233, 25.68, 0.367, -21.95]}, {"time": 0.4333, "value": -21.95, "curve": [0.517, -21.95, 0.683, 16.45]}, {"time": 0.7667, "value": 16.45, "curve": [0.842, 16.45, 0.992, -4.89]}, {"time": 1.0667, "value": -4.89, "curve": [1.188, 0.28, 1.318, 6.75]}, {"time": 1.4, "value": 6.75, "curve": [1.469, 6.75, 1.565, 3.67]}, {"time": 1.6667, "value": -0.02}]}, "Spike1": {"rotate": [{"value": -39.2, "curve": [0.01, 27.66, 0.075, 27.66]}, {"time": 0.1, "value": 27.66, "curve": [0.167, 27.66, 0.3, -20.49]}, {"time": 0.3667, "value": -20.49, "curve": [0.45, -20.49, 0.617, 17.99]}, {"time": 0.7, "value": 17.99, "curve": [0.775, 17.99, 0.925, -2.45]}, {"time": 1, "value": -2.45, "curve": [1.125, 2.15, 1.25, 6.75]}, {"time": 1.3333, "value": 6.75, "curve": [1.417, 6.75, 1.542, 2.12]}, {"time": 1.6667, "value": -2.5}]}, "Spike10": {"rotate": [{"value": 34.26, "curve": [0.01, -29.25, 0.075, -29.25]}, {"time": 0.1, "value": -29.25, "curve": [0.167, -29.25, 0.3, 17.23]}, {"time": 0.3667, "value": 17.23, "curve": [0.45, 17.23, 0.617, -24.45]}, {"time": 0.7, "value": -24.45, "curve": [0.775, -24.45, 0.925, -2.18]}, {"time": 1, "value": -2.18, "curve": [1.076, -4.25, 1.146, -5.73]}, {"time": 1.2, "value": -5.73, "curve": [1.313, -5.73, 1.506, 1.22]}, {"time": 1.6667, "value": 5.4}]}, "Spike6": {"rotate": [{"value": 39.38, "curve": [0.016, -22.77, 0.125, -22.77]}, {"time": 0.1667, "value": -22.77, "curve": [0.233, -22.77, 0.367, 21.76]}, {"time": 0.4333, "value": 21.76, "curve": [0.517, 21.76, 0.683, -19.61]}, {"time": 0.7667, "value": -19.61, "curve": [0.842, -19.61, 0.992, 3.55]}, {"time": 1.0667, "value": 3.55, "curve": [1.188, -0.57, 1.318, -5.73]}, {"time": 1.4, "value": -5.73, "curve": [1.469, -5.73, 1.565, -3.28]}, {"time": 1.6667, "value": -0.33}]}, "Spike7": {"rotate": [{"value": 43.76, "curve": [0.019, -16.22, 0.15, -16.22]}, {"time": 0.2, "value": -16.22, "curve": [0.267, -16.22, 0.4, 26]}, {"time": 0.4667, "value": 26, "curve": [0.55, 26, 0.717, -15.29]}, {"time": 0.8, "value": -15.29, "curve": [0.875, -15.29, 1.025, 7.74]}, {"time": 1.1, "value": 7.74, "curve": [1.243, 4.63, 1.453, -5.73]}, {"time": 1.5667, "value": -5.73, "curve": [1.596, -5.73, 1.63, -5.28]}, {"time": 1.6667, "value": -4.52}]}, "Spike8": {"rotate": [{"value": 42.87, "curve": [0.022, -21.41, 0.175, -21.41]}, {"time": 0.2333, "value": -21.41, "curve": [0.3, -21.41, 0.433, 26.26]}, {"time": 0.5, "value": 26.26, "curve": [0.583, 26.26, 0.75, -15.65]}, {"time": 0.8333, "value": -15.65, "curve": [0.908, -15.65, 1.058, 7.05]}, {"time": 1.1333, "value": 7.05, "curve": [1.183, 8.22, 1.229, 8.96]}, {"time": 1.2667, "value": 8.96, "curve": [1.364, 8.96, 1.539, -0.17]}, {"time": 1.6667, "value": -3.82}]}, "Spike9": {"rotate": [{"value": 41.81, "curve": [0.025, -22.06, 0.2, -22.06]}, {"time": 0.2667, "value": -22.06, "curve": [0.333, -22.06, 0.467, 24.98]}, {"time": 0.5333, "value": 24.98, "curve": [0.617, 24.98, 0.783, -16.81]}, {"time": 0.8667, "value": -16.81, "curve": [0.942, -16.81, 1.092, 6.25]}, {"time": 1.1667, "value": 6.25, "curve": [1.23, 7.87, 1.287, 8.96]}, {"time": 1.3333, "value": 8.96, "curve": [1.414, 8.96, 1.556, 0.97]}, {"time": 1.6667, "value": -3.02}]}, "Tentacle5": {"rotate": [{"value": 6.03, "curve": [0.024, 8.17, 0.047, 9.39]}, {"time": 0.0667, "value": 9.39, "curve": [0.175, 9.39, 0.392, -49.78]}, {"time": 0.5, "value": -49.78, "curve": [0.622, -49.78, 0.852, -5.1]}, {"time": 1, "value": 5.41, "curve": [1.037, 7.94, 1.07, 9.39]}, {"time": 1.1, "value": 9.39, "curve": [1.242, 9.39, 1.525, -49.78]}, {"time": 1.6667, "value": -49.78}]}, "Tentacle3_Mid": {"rotate": [{"value": -3.27, "curve": [0.13, 21.42, 0.277, 56.4]}, {"time": 0.3667, "value": 56.4, "curve": [0.403, 56.4, 0.449, 48]}, {"time": 0.5, "value": 36.14, "curve": [0.603, 11.69, 0.727, -29.86]}, {"time": 0.8, "value": -29.86, "curve": [0.852, -29.86, 0.924, -18.51]}, {"time": 1, "value": -3.82, "curve": [1.212, 20.88, 1.454, 56.4]}, {"time": 1.6, "value": 56.4, "curve": [1.62, 56.4, 1.642, 54.76]}, {"time": 1.6667, "value": 51.86}]}, "Tentacle3_Btm": {"rotate": [{"value": -19.18, "curve": [0.143, 10.49, 0.328, 73.54]}, {"time": 0.4333, "value": 73.54, "curve": [0.453, 73.54, 0.475, 69.14]}, {"time": 0.5, "value": 61.89, "curve": [0.615, 35.84, 0.778, -37.96]}, {"time": 0.8667, "value": -37.96, "curve": [0.904, -37.96, 0.95, -29.31]}, {"time": 1, "value": -16.08, "curve": [1.224, 14.65, 1.505, 73.54]}, {"time": 1.6667, "value": 73.54}]}, "Tentacle6": {"rotate": [{"value": -8.02, "curve": [0.024, -9.72, 0.047, -10.68]}, {"time": 0.0667, "value": -10.68, "curve": [0.175, -10.68, 0.392, 36.14]}, {"time": 0.5, "value": 36.14, "curve": [0.622, 36.14, 0.852, 0.78]}, {"time": 1, "value": -7.54, "curve": [1.037, -9.53, 1.07, -10.68]}, {"time": 1.1, "value": -10.68, "curve": [1.242, -10.68, 1.525, 36.14]}, {"time": 1.6667, "value": 36.14}]}, "Tentacle3_Mid2": {"rotate": [{"value": -1.59, "curve": [0.13, 24.91, 0.277, 62.45]}, {"time": 0.3667, "value": 62.45, "curve": [0.403, 62.45, 0.449, 53.43]}, {"time": 0.5, "value": 40.71, "curve": [0.603, 14.47, 0.727, -30.13]}, {"time": 0.8, "value": -30.13, "curve": [0.852, -30.13, 0.924, -17.95]}, {"time": 1, "value": -2.18, "curve": [1.201, 24.33, 1.429, 62.45]}, {"time": 1.5667, "value": 62.45, "curve": [1.596, 62.45, 1.629, 58.81]}, {"time": 1.6667, "value": 52.81}]}, "Tentacle3_Btm2": {"rotate": [{"value": -23.87, "curve": [0.143, 3.75, 0.328, 62.45]}, {"time": 0.4333, "value": 62.45, "curve": [0.453, 62.45, 0.475, 58.35]}, {"time": 0.5, "value": 51.6, "curve": [0.615, 27.35, 0.778, -41.36]}, {"time": 0.8667, "value": -41.36, "curve": [0.904, -41.36, 0.95, -33.3]}, {"time": 1, "value": -20.98, "curve": [1.213, 7.62, 1.48, 62.45]}, {"time": 1.6333, "value": 62.45, "curve": [1.644, 62.45, 1.655, 61.9]}, {"time": 1.6667, "value": 60.88}]}, "Tentacle2_Btm2": {"rotate": [{"value": -38.29, "curve": [0.152, -7.3, 0.353, 62.45]}, {"time": 0.4667, "value": 62.45, "curve": [0.477, 62.45, 0.488, 60]}, {"time": 0.5, "value": 55.68, "curve": [0.608, 34.28, 0.777, -56.67]}, {"time": 0.8667, "value": -56.67, "curve": [0.904, -56.67, 0.95, -49.72]}, {"time": 1, "value": -38.92, "curve": [1.195, -8.2, 1.454, 62.45]}, {"time": 1.6, "value": 62.45, "curve": [1.62, 62.45, 1.642, 60]}, {"time": 1.6667, "value": 55.68}]}, "Tentacle2_Mid2": {"rotate": [{"value": 5.37, "curve": [0.121, 31.15, 0.251, 62.45]}, {"time": 0.3333, "value": 62.45, "curve": [0.376, 62.45, 0.437, 46.14]}, {"time": 0.5, "value": 26.96, "curve": [0.597, 1.18, 0.701, -30.13]}, {"time": 0.7667, "value": -30.13, "curve": [0.827, -30.13, 0.911, -12.53]}, {"time": 1, "value": 8.18, "curve": [1.159, 33.42, 1.327, 62.45]}, {"time": 1.4333, "value": 62.45, "curve": [1.493, 62.45, 1.578, 46.14]}, {"time": 1.6667, "value": 26.96}]}, "Tentacle4": {"rotate": [{"value": -8.58, "curve": [0.024, -9.92, 0.047, -10.68]}, {"time": 0.0667, "value": -10.68, "curve": [0.175, -10.68, 0.392, 26.28]}, {"time": 0.5, "value": 26.28, "curve": [0.622, 26.28, 0.852, -1.63]}, {"time": 1, "value": -8.2, "curve": [1.037, -9.77, 1.07, -10.68]}, {"time": 1.1, "value": -10.68, "curve": [1.242, -10.68, 1.525, 26.28]}, {"time": 1.6667, "value": 26.28}]}, "Tentacle2_Btm": {"rotate": [{"value": -46.79, "curve": [0.152, -15.83, 0.353, 53.84]}, {"time": 0.4667, "value": 53.84, "curve": [0.477, 53.84, 0.488, 51.4]}, {"time": 0.5, "value": 47.08, "curve": [0.608, 25.7, 0.777, -65.15]}, {"time": 0.8667, "value": -65.15, "curve": [0.904, -65.15, 0.95, -58.21]}, {"time": 1, "value": -47.42, "curve": [1.195, -16.73, 1.454, 53.84]}, {"time": 1.6, "value": 53.84, "curve": [1.62, 53.84, 1.642, 51.4]}, {"time": 1.6667, "value": 47.08}]}, "Tentacle2_Mid": {"rotate": [{"value": -3.22, "curve": [0.121, 20.8, 0.251, 49.96]}, {"time": 0.3333, "value": 49.96, "curve": [0.376, 49.96, 0.437, 34.77]}, {"time": 0.5, "value": 16.89, "curve": [0.597, -7.13, 0.701, -36.3]}, {"time": 0.7667, "value": -36.3, "curve": [0.827, -36.3, 0.911, -19.9]}, {"time": 1, "value": -0.6, "curve": [1.159, 22.92, 1.327, 49.96]}, {"time": 1.4333, "value": 49.96, "curve": [1.493, 49.96, 1.578, 34.77]}, {"time": 1.6667, "value": 16.89}]}, "Tentacle3": {"rotate": [{"value": 4.21, "curve": [0.024, 5.78, 0.047, 6.68]}, {"time": 0.0667, "value": 6.68, "curve": [0.175, 6.68, 0.392, -36.75]}, {"time": 0.5, "value": -36.75, "curve": [0.622, -36.75, 0.852, -3.96]}, {"time": 1, "value": 3.76, "curve": [1.037, 5.61, 1.07, 6.68]}, {"time": 1.1, "value": 6.68, "curve": [1.242, 6.68, 1.525, -36.75]}, {"time": 1.6667, "value": -36.75}]}, "DangleHandle": {"rotate": [{"value": -15.4, "curve": [0.124, -4.37, 0.278, 16.3]}, {"time": 0.3667, "value": 16.3, "curve": [0.403, 16.3, 0.449, 12.3]}, {"time": 0.5, "value": 6.7, "curve": [0.532, 3.66, 0.566, 0.04]}, {"time": 0.6, "value": -3.58, "curve": [0.7, -13.51, 0.8, -23.45]}, {"time": 0.8667, "value": -23.45, "curve": [0.903, -23.45, 0.949, -19.55]}, {"time": 1, "value": -13.82, "curve": [1.161, -2.53, 1.353, 16.3]}, {"time": 1.4667, "value": 16.3, "curve": [1.52, 16.3, 1.59, 12.3]}, {"time": 1.6667, "value": 6.7}], "translate": [{"x": -8.18, "y": 2.62, "curve": [0.099, 16.95, 0.201, 43.87, 0.099, 3.65, 0.201, 4.74]}, {"time": 0.2667, "x": 43.87, "y": 4.74, "curve": [0.325, 43.87, 0.413, 19.95, 0.325, 4.74, 0.413, 3.77]}, {"time": 0.5, "x": -3.96, "y": 2.8, "curve": [0.6, -27.88, 0.7, -51.8, 0.6, 1.82, 0.7, 0.85]}, {"time": 0.7667, "x": -51.8, "y": 0.85, "curve": [0.826, -51.8, 0.912, -28.65, 0.826, 0.85, 0.912, 1.79]}, {"time": 1, "x": -3.96, "y": 2.8, "curve": [1.125, 19.95, 1.25, 43.87, 1.125, 3.77, 1.25, 4.74]}, {"time": 1.3333, "x": 43.87, "y": 4.74, "curve": [1.417, 43.87, 1.542, 19.95, 1.417, 4.74, 1.542, 3.77]}, {"time": 1.6667, "x": -3.96, "y": 2.8}], "scale": [{"y": 0.313, "curve": [0.125, 1, 0.375, 1, 0.125, 0.313, 0.375, 1.723]}, {"time": 0.5, "y": 1.723, "curve": [0.625, 1, 0.875, 1, 0.625, 1.723, 0.875, 0.313]}, {"time": 1, "y": 0.313, "curve": [1.167, 1, 1.5, 1, 1.167, 0.313, 1.5, 1.723]}, {"time": 1.6667, "y": 1.723}]}, "Tentacles": {"scale": [{"x": 1.199}, {"time": 0.5, "x": 0.699}, {"time": 1, "x": 1.199}, {"time": 1.6667, "x": 0.699}]}, "Mouth": {"translate": [{}, {"time": 0.0667, "x": 8.27, "y": 49.14, "curve": [0.175, 8.27, 0.392, 0, 0.175, 49.14, 0.392, -10.97]}, {"time": 0.5, "y": -10.97, "curve": [0.625, 0, 0.875, 0, 0.625, -10.97, 0.875, 4.02]}, {"time": 1, "y": 4.02, "curve": [1.167, 0, 1.5, 0, 1.167, 4.02, 1.5, -10.97]}, {"time": 1.6667, "y": -10.97}], "scale": [{"curve": [0.001, 1, 0.05, 1, 0.001, 2.216, 0.05, 2.599]}, {"time": 0.0667, "y": 2.599, "curve": [0.175, 1, 0.392, 1, 0.175, 2.599, 0.392, 1]}, {"time": 0.5}]}, "Mouth8": {"rotate": [{"value": -32.57, "curve": [0.025, -38.95, 0.049, -43.41]}, {"time": 0.0667, "value": -43.41, "curve": [0.133, -43.41, 0.267, 4.89]}, {"time": 0.3333, "value": 4.89, "curve": [0.374, 4.89, 0.443, -17.99]}, {"time": 0.5, "value": -31.71, "curve": [0.525, -38.54, 0.549, -43.41]}, {"time": 0.5667, "value": -43.41, "curve": [0.633, -43.41, 0.767, 4.89]}, {"time": 0.8333, "value": 4.89, "curve": [0.874, 4.89, 0.943, -18.4]}, {"time": 1, "value": -31.71, "curve": [1.038, -38.54, 1.073, -43.41]}, {"time": 1.1, "value": -43.41, "curve": [1.183, -43.41, 1.35, 4.89]}, {"time": 1.4333, "value": 4.89, "curve": [1.49, 4.89, 1.586, -17.99]}, {"time": 1.6667, "value": -31.71}]}, "Mouth5": {"rotate": [{"value": 20.26, "curve": [0.061, 15.29, 0.151, -2.12]}, {"time": 0.2, "value": -2.12, "curve": [0.267, -2.12, 0.4, 22.13]}, {"time": 0.4667, "value": 22.13, "curve": [0.476, 22.13, 0.488, 20.92]}, {"time": 0.5, "value": 18.98, "curve": [0.564, 12.95, 0.651, -2.12]}, {"time": 0.7, "value": -2.12, "curve": [0.767, -2.12, 0.9, 22.13]}, {"time": 0.9667, "value": 22.13, "curve": [0.977, 22.13, 0.988, 20.97]}, {"time": 1, "value": 18.98, "curve": [1.085, 12.95, 1.202, -2.12]}, {"time": 1.2667, "value": -2.12, "curve": [1.35, -2.12, 1.517, 22.13]}, {"time": 1.6, "value": 22.13, "curve": [1.619, 22.13, 1.642, 20.92]}, {"time": 1.6667, "value": 18.98}]}, "Mouth4": {"rotate": [{"value": 7.72, "curve": [0.038, 2.47, 0.074, -2.12]}, {"time": 0.1, "value": -2.12, "curve": [0.167, -2.12, 0.3, 22.13]}, {"time": 0.3667, "value": 22.13, "curve": [0.399, 22.13, 0.452, 13.62]}, {"time": 0.5, "value": 6.8, "curve": [0.538, 1.94, 0.574, -2.12]}, {"time": 0.6, "value": -2.12, "curve": [0.667, -2.12, 0.8, 22.13]}, {"time": 0.8667, "value": 22.13, "curve": [0.899, 22.13, 0.951, 13.88]}, {"time": 1, "value": 6.8, "curve": [1.051, 1.94, 1.099, -2.12]}, {"time": 1.1333, "value": -2.12, "curve": [1.217, -2.12, 1.383, 22.13]}, {"time": 1.4667, "value": 22.13, "curve": [1.516, 22.13, 1.594, 13.62]}, {"time": 1.6667, "value": 6.8}]}, "Mouth7": {"rotate": [{"value": -40.38, "curve": [0.069, -31.32, 0.176, 4.89]}, {"time": 0.2333, "value": 4.89, "curve": [0.292, 4.89, 0.408, -43.41]}, {"time": 0.4667, "value": -43.41, "curve": [0.477, -43.41, 0.488, -42.68]}, {"time": 0.5, "value": -41.36, "curve": [0.567, -33.65, 0.676, 4.89]}, {"time": 0.7333, "value": 4.89, "curve": [0.792, 4.89, 0.908, -43.41]}, {"time": 0.9667, "value": -43.41, "curve": [0.977, -43.41, 0.988, -42.66]}, {"time": 1, "value": -41.36, "curve": [1.087, -33.65, 1.226, 4.89]}, {"time": 1.3, "value": 4.89, "curve": [1.383, 4.89, 1.55, -43.41]}, {"time": 1.6333, "value": -43.41, "curve": [1.643, -43.41, 1.655, -42.68]}, {"time": 1.6667, "value": -41.36}]}, "Mouth3": {"rotate": [{"value": -2.12, "curve": [0.067, -2.12, 0.2, 22.13]}, {"time": 0.2667, "value": 22.13, "curve": [0.325, 22.13, 0.442, -2.12]}, {"time": 0.5, "value": -2.12, "curve": [0.567, -2.12, 0.7, 22.13]}, {"time": 0.7667, "value": 22.13, "curve": [0.825, 22.13, 0.942, -2.12]}, {"time": 1, "value": -2.12, "curve": [1.083, -2.12, 1.25, 22.13]}, {"time": 1.3333, "value": 22.13, "curve": [1.417, 22.13, 1.583, -2.12]}, {"time": 1.6667, "value": -2.12}]}, "Mouth6": {"rotate": [{"value": -27.19, "curve": [0.06, -13.44, 0.126, 4.89]}, {"time": 0.1667, "value": 4.89, "curve": [0.225, 4.89, 0.342, -43.41]}, {"time": 0.4, "value": -43.41, "curve": [0.426, -43.41, 0.462, -35.33]}, {"time": 0.5, "value": -25.64, "curve": [0.56, -12.07, 0.626, 4.89]}, {"time": 0.6667, "value": 4.89, "curve": [0.725, 4.89, 0.842, -43.41]}, {"time": 0.9, "value": -43.41, "curve": [0.926, -43.41, 0.962, -35.51]}, {"time": 1, "value": -25.64, "curve": [1.073, -12.07, 1.151, 4.89]}, {"time": 1.2, "value": 4.89, "curve": [1.283, 4.89, 1.45, -43.41]}, {"time": 1.5333, "value": -43.41, "curve": [1.568, -43.41, 1.616, -35.33]}, {"time": 1.6667, "value": -25.64}]}, "Mouth2": {"scale": [{}, {"time": 0.0667, "x": 1.013, "y": 1.013, "curve": [0.068, 1.003, 0.117, 1, 0.068, 1.935, 0.117, 2.226]}, {"time": 0.1333, "y": 2.226, "curve": [0.225, 1, 0.408, 1.013, 0.225, 2.226, 0.408, 1.013]}, {"time": 0.5, "x": 1.013, "y": 1.013}, {"time": 1}]}, "Tentacle3_Btm3": {"rotate": [{"value": -19.18, "curve": [0.143, 10.49, 0.328, 73.54]}, {"time": 0.4333, "value": 73.54, "curve": [0.453, 73.54, 0.475, 69.14]}, {"time": 0.5, "value": 61.89, "curve": [0.615, 35.84, 0.778, -37.96]}, {"time": 0.8667, "value": -37.96, "curve": [0.904, -37.96, 0.95, -29.31]}, {"time": 1, "value": -16.08, "curve": [1.19, 14.65, 1.429, 73.54]}, {"time": 1.5667, "value": 73.54, "curve": [1.596, 73.54, 1.629, 69.14]}, {"time": 1.6667, "value": 61.89}]}, "Tentacle3_Mid3": {"rotate": [{"value": -3.27, "curve": [0.13, 21.42, 0.277, 56.4]}, {"time": 0.3667, "value": 56.4, "curve": [0.403, 56.4, 0.449, 48]}, {"time": 0.5, "value": 36.14, "curve": [0.603, 11.69, 0.727, -29.86]}, {"time": 0.8, "value": -29.86, "curve": [0.852, -29.86, 0.924, -18.51]}, {"time": 1, "value": -3.82, "curve": [1.177, 20.88, 1.378, 56.4]}, {"time": 1.5, "value": 56.4, "curve": [1.545, 56.4, 1.603, 48]}, {"time": 1.6667, "value": 36.14}]}, "Tentacle1": {"rotate": [{"value": 6.03, "curve": [0.024, 8.17, 0.047, 9.39]}, {"time": 0.0667, "value": 9.39, "curve": [0.175, 9.39, 0.392, -49.78]}, {"time": 0.5, "value": -49.78, "curve": [0.622, -49.78, 0.852, -5.1]}, {"time": 1, "value": 5.41, "curve": [1.037, 7.94, 1.07, 9.39]}, {"time": 1.1, "value": 9.39, "curve": [1.242, 9.39, 1.525, -49.78]}, {"time": 1.6667, "value": -49.78}]}, "Tentacle3_Btm4": {"rotate": [{"value": -23.87, "curve": [0.143, 3.75, 0.328, 62.45]}, {"time": 0.4333, "value": 62.45, "curve": [0.453, 62.45, 0.475, 58.35]}, {"time": 0.5, "value": 51.6, "curve": [0.615, 27.35, 0.778, -41.36]}, {"time": 0.8667, "value": -41.36, "curve": [0.904, -41.36, 0.95, -33.3]}, {"time": 1, "value": -20.98, "curve": [1.19, 7.62, 1.429, 62.45]}, {"time": 1.5667, "value": 62.45, "curve": [1.596, 62.45, 1.629, 58.35]}, {"time": 1.6667, "value": 51.6}]}, "Tentacle3_Mid4": {"rotate": [{"value": -1.59, "curve": [0.13, 24.91, 0.277, 62.45]}, {"time": 0.3667, "value": 62.45, "curve": [0.403, 62.45, 0.449, 53.43]}, {"time": 0.5, "value": 40.71, "curve": [0.603, 14.47, 0.727, -30.13]}, {"time": 0.8, "value": -30.13, "curve": [0.852, -30.13, 0.924, -17.95]}, {"time": 1, "value": -2.18, "curve": [1.177, 24.33, 1.378, 62.45]}, {"time": 1.5, "value": 62.45, "curve": [1.545, 62.45, 1.603, 53.43]}, {"time": 1.6667, "value": 40.71}]}, "Tentacle2": {"rotate": [{"value": -8.02, "curve": [0.024, -9.72, 0.047, -10.68]}, {"time": 0.0667, "value": -10.68, "curve": [0.175, -10.68, 0.392, 36.14]}, {"time": 0.5, "value": 36.14, "curve": [0.622, 36.14, 0.852, 0.78]}, {"time": 1, "value": -7.54, "curve": [1.037, -9.53, 1.07, -10.68]}, {"time": 1.1, "value": -10.68, "curve": [1.242, -10.68, 1.525, 36.14]}, {"time": 1.6667, "value": 36.14}]}}, "drawOrder": [{"offsets": [{"slot": "images/Eye", "offset": 2}, {"slot": "images/Eye2", "offset": 2}]}]}, "attack-charge": {"slots": {"Eyes": {"attachment": [{"name": "EyesCharge"}]}, "images/Eye3": {"attachment": [{"time": 1}]}, "images/SpikerBossSpike1": {"attachment": [{"name": "images/SpikerBossSpike2"}]}, "images/SpikerBossSpike2": {"attachment": [{"name": "images/SpikerBossSpike1"}]}, "images/SpikerBossSpike3": {"attachment": [{"name": "images/SpikerBossSpike1"}]}, "images/SpikerBossSpike4": {"attachment": [{"name": "images/SpikerBossSpike1"}]}, "images/SpikerBossSpike5": {"attachment": [{"name": "images/SpikerBossSpike2"}]}, "images/SpikerBossSpike6": {"attachment": [{"name": "images/SpikerBossSpike2"}]}, "images/SpikerBossSpike7": {"attachment": [{"name": "images/SpikerBossSpike1"}]}, "images/SpikerBossSpike8": {"attachment": [{"name": "images/SpikerBossSpike1"}]}, "ScuttleEye3": {"attachment": [{"time": 1, "name": "<PERSON><PERSON><PERSON>_Eye_Closed"}]}}, "bones": {"FACE": {"translate": [{"x": 0.84, "y": 4.16, "curve": [0.008, -23.65, 0.018, -44.45, 0.008, 3.67, 0.018, 3.25]}, {"time": 0.0333, "x": -45.76, "y": 3.22, "curve": [0.058, -45.76, 0.108, 18.32, 0.058, 3.22, 0.108, 6.16]}, {"time": 0.1333, "x": 18.32, "y": 6.16, "curve": [0.208, 18.32, 0.358, 12.06, 0.208, 6.16, 0.358, 4.16]}, {"time": 0.4333, "x": 12.06, "y": 4.16, "curve": [0.508, 12.06, 0.658, -21.84, 0.508, 4.16, 0.658, 4.16]}, {"time": 0.7333, "x": -21.84, "y": 4.16, "curve": [0.865, -21.84, 0.923, -5.93, 0.865, 4.16, 0.923, 4.16]}, {"time": 1, "x": 7.05, "y": 4.16, "curve": [1.033, 9.68, 1.072, 11.92, 1.033, 4.16, 1.072, 4.16]}, {"time": 1.1333, "x": 12.06, "y": 4.16, "curve": [1.288, 12.06, 1.3, -3.76, 1.288, 4.16, 1.3, 4.16]}, {"time": 1.4667, "x": -4.17, "y": 4.16, "curve": [1.565, -4.17, 1.609, 2.01, 1.565, 4.16, 1.609, 4.16]}, {"time": 1.6667, "x": 7.05, "y": 4.16}], "scale": [{}, {"time": 0.0333, "x": 1.219, "y": 1.219}, {"time": 0.1333}]}, "ScuttleEyeball3": {"translate": [{"time": 1, "y": 0.19, "curve": [1.051, 0, 1.099, 0, 1.051, 1.38, 1.099, 2.38]}, {"time": 1.1333, "y": 2.38, "curve": [1.217, 0, 1.383, 0, 1.217, 2.38, 1.383, -3.57]}, {"time": 1.4667, "y": -3.57, "curve": [1.516, 0, 1.594, 0, 1.516, -3.57, 1.594, -1.48]}, {"time": 1.6667, "y": 0.19}]}, "SpikerSpike1": {"scale": [{"x": 0.076, "curve": [0.006, 1.325, 0.025, 1.552, 0.006, 1, 0.025, 1]}, {"time": 0.0333, "x": 1.552, "curve": [0.042, 1.552, 0.058, 0.793, 0.042, 1, 0.058, 1]}, {"time": 0.0667, "x": 0.793, "curve": [0.1, 0.793, 0.167, 1.223, 0.1, 1, 0.167, 1]}, {"time": 0.2, "x": 1.223}, {"time": 0.3333}]}, "SpikerSpike2": {"scale": [{"x": 0.076, "curve": [0.006, 1.325, 0.025, 1.552, 0.006, 1, 0.025, 1]}, {"time": 0.0333, "x": 1.552, "curve": [0.042, 1.552, 0.058, 0.793, 0.042, 1, 0.058, 1]}, {"time": 0.0667, "x": 0.793, "curve": [0.1, 0.793, 0.167, 1.223, 0.1, 1, 0.167, 1]}, {"time": 0.2, "x": 1.223}, {"time": 0.3333}]}, "SpikerSpike3": {"scale": [{"x": 0.076, "curve": [0.006, 1.325, 0.025, 1.552, 0.006, 1, 0.025, 1]}, {"time": 0.0333, "x": 1.552, "curve": [0.042, 1.552, 0.058, 0.793, 0.042, 1, 0.058, 1]}, {"time": 0.0667, "x": 0.793, "curve": [0.1, 0.793, 0.167, 1.223, 0.1, 1, 0.167, 1]}, {"time": 0.2, "x": 1.223}, {"time": 0.3333}]}, "SpikerSpike4": {"scale": [{"x": 0.076, "curve": [0.006, 1.325, 0.025, 1.552, 0.006, 1, 0.025, 1]}, {"time": 0.0333, "x": 1.552, "curve": [0.042, 1.552, 0.058, 0.793, 0.042, 1, 0.058, 1]}, {"time": 0.0667, "x": 0.793, "curve": [0.1, 0.793, 0.167, 1.223, 0.1, 1, 0.167, 1]}, {"time": 0.2, "x": 1.223}, {"time": 0.3333}]}, "SpikerSpike5": {"scale": [{"x": 0.076, "curve": [0.006, 1.325, 0.025, 1.552, 0.006, 1, 0.025, 1]}, {"time": 0.0333, "x": 1.552, "curve": [0.042, 1.552, 0.058, 0.793, 0.042, 1, 0.058, 1]}, {"time": 0.0667, "x": 0.793, "curve": [0.1, 0.793, 0.167, 1.223, 0.1, 1, 0.167, 1]}, {"time": 0.2, "x": 1.223}, {"time": 0.3333}]}, "SpikerSpike6": {"scale": [{"x": 0.076, "curve": [0.006, 1.325, 0.025, 1.552, 0.006, 1, 0.025, 1]}, {"time": 0.0333, "x": 1.552, "curve": [0.042, 1.552, 0.058, 0.793, 0.042, 1, 0.058, 1]}, {"time": 0.0667, "x": 0.793, "curve": [0.1, 0.793, 0.167, 1.223, 0.1, 1, 0.167, 1]}, {"time": 0.2, "x": 1.223}, {"time": 0.3333}]}, "SpikerSpike7": {"scale": [{"x": 0.076, "curve": [0.006, 1.325, 0.025, 1.552, 0.006, 1, 0.025, 1]}, {"time": 0.0333, "x": 1.552, "curve": [0.042, 1.552, 0.058, 0.793, 0.042, 1, 0.058, 1]}, {"time": 0.0667, "x": 0.793, "curve": [0.1, 0.793, 0.167, 1.223, 0.1, 1, 0.167, 1]}, {"time": 0.2, "x": 1.223}, {"time": 0.3333}]}, "SpikerSpike8": {"scale": [{"x": 0.076, "curve": [0.006, 1.325, 0.025, 1.552, 0.006, 1, 0.025, 1]}, {"time": 0.0333, "x": 1.552, "curve": [0.042, 1.552, 0.058, 0.793, 0.042, 1, 0.058, 1]}, {"time": 0.0667, "x": 0.793, "curve": [0.1, 0.793, 0.167, 1.223, 0.1, 1, 0.167, 1]}, {"time": 0.2, "x": 1.223}, {"time": 0.3333}]}, "MAIN": {"translate": [{"y": -33.8, "curve": [0, 0, 0.15, 0, 0, -1.14, 0.15, 16.22]}, {"time": 0.3, "y": 17.53, "curve": [0.439, 0, 0.45, 0, 0.439, 17.53, 0.45, -89.61]}, {"time": 0.6, "y": -92.41, "curve": [0.786, 0, 0.8, 0, 0.786, -92.41, 0.8, 14.73]}, {"time": 1, "y": 17.53, "curve": [1.155, 0, 1.167, 0, 1.155, 17.53, 1.167, -32.5]}, {"time": 1.3333, "y": -33.8, "curve": [1.488, 0, 1.5, 0, 1.488, -33.8, 1.5, 16.22]}, {"time": 1.6667, "y": 17.53}], "scale": [{"x": 1.509, "y": 0.663, "curve": [0, 0.959, 0.025, 0.76, 0, 1.189, 0.025, 1.379]}, {"time": 0.0333, "x": 0.76, "y": 1.379, "curve": [0.058, 0.76, 0.108, 1.267, 0.058, 1.379, 0.108, 0.835]}, {"time": 0.1333, "x": 1.267, "y": 0.835, "curve": [0.175, 1.267, 0.258, 0.937, 0.175, 0.835, 0.258, 1.05]}, {"time": 0.3, "x": 0.937, "y": 1.05, "curve": [0.375, 0.937, 0.525, 0.987, 0.375, 1.05, 0.525, 1.006]}, {"time": 0.6, "x": 0.987, "y": 1.006, "curve": [0.675, 1.013, 0.75, 1.038, 0.675, 0.984, 0.75, 0.962]}, {"time": 0.8, "x": 1.038, "y": 0.962, "curve": [0.85, 1.038, 0.925, 1.013, 0.85, 0.962, 0.925, 0.984]}, {"time": 1, "x": 0.987, "y": 1.006, "curve": [1.063, 0.962, 1.125, 0.937, 1.063, 1.028, 1.125, 1.05]}, {"time": 1.1667, "x": 0.937, "y": 1.05, "curve": [1.25, 0.937, 1.417, 1.038, 1.25, 1.05, 1.417, 0.962]}, {"time": 1.5, "x": 1.038, "y": 0.962, "curve": [1.542, 1.038, 1.604, 1.013, 1.542, 0.962, 1.604, 0.984]}, {"time": 1.6667, "x": 0.987, "y": 1.006}]}, "Spike5": {"rotate": [{"value": -43.86, "curve": [0.013, 24.37, 0.1, 24.37]}, {"time": 0.1333, "value": 24.37, "curve": [0.183, 24.37, 0.283, -26.01]}, {"time": 0.3333, "value": -26.01, "curve": [0.383, -26.01, 0.483, 12.93]}, {"time": 0.5333, "value": 12.93, "curve": [0.575, 12.93, 0.658, -8.26]}, {"time": 0.7, "value": -8.26, "curve": [0.738, -10.3, 0.772, -11.66]}, {"time": 0.8, "value": -11.66, "curve": [0.848, -11.66, 0.933, -1.65]}, {"time": 1, "value": 3.35, "curve": [1.038, 5.39, 1.072, 6.75]}, {"time": 1.1, "value": 6.75, "curve": [1.183, 6.75, 1.35, -11.66]}, {"time": 1.4333, "value": -11.66, "curve": [1.49, -11.66, 1.589, -1.65]}, {"time": 1.6667, "value": 3.35}]}, "Spike4": {"rotate": [{"value": -45.19, "curve": [0.013, 23.45, 0.1, 23.45]}, {"time": 0.1333, "value": 23.45, "curve": [0.175, 23.45, 0.258, -27.61]}, {"time": 0.3, "value": -27.61, "curve": [0.35, -27.61, 0.45, 11.47]}, {"time": 0.5, "value": 11.47, "curve": [0.542, 11.47, 0.625, -9.26]}, {"time": 0.6667, "value": -9.26, "curve": [0.692, -10.73, 0.714, -11.66]}, {"time": 0.7333, "value": -11.66, "curve": [0.798, -11.66, 0.915, -0.22]}, {"time": 1, "value": 4.35, "curve": [1.025, 5.82, 1.048, 6.75]}, {"time": 1.0667, "value": 6.75, "curve": [1.15, 6.75, 1.317, -11.66]}, {"time": 1.4, "value": -11.66, "curve": [1.465, -11.66, 1.581, -0.22]}, {"time": 1.6667, "value": 4.35}]}, "Spike3": {"rotate": [{"value": -46.31, "curve": [0.013, 18.03, 0.1, 18.03]}, {"time": 0.1333, "value": 18.03, "curve": [0.167, 18.03, 0.233, -27.12]}, {"time": 0.2667, "value": -27.12, "curve": [0.317, -27.12, 0.417, 11.11]}, {"time": 0.4667, "value": 11.11, "curve": [0.508, 11.11, 0.592, -10.13]}, {"time": 0.6333, "value": -10.13, "curve": [0.725, -6.23, 0.86, 6.75]}, {"time": 0.9333, "value": 6.75, "curve": [0.953, 6.75, 0.975, 6.18]}, {"time": 1, "value": 5.22, "curve": [1.092, 1.32, 1.227, -11.66]}, {"time": 1.3, "value": -11.66, "curve": [1.383, -11.66, 1.55, 6.75]}, {"time": 1.6333, "value": 6.75, "curve": [1.643, 6.75, 1.654, 6.18]}, {"time": 1.6667, "value": 5.22}]}, "Spike2": {"rotate": [{"value": -40.82, "curve": [0.01, 25.68, 0.075, 25.68]}, {"time": 0.1, "value": 25.68, "curve": [0.133, 25.68, 0.2, -21.95]}, {"time": 0.2333, "value": -21.95, "curve": [0.283, -21.95, 0.383, 16.45]}, {"time": 0.4333, "value": 16.45, "curve": [0.483, 16.45, 0.583, -4.89]}, {"time": 0.6333, "value": -4.89, "curve": [0.706, 0.28, 0.784, 6.75]}, {"time": 0.8333, "value": 6.75, "curve": [0.876, 6.75, 0.936, 3.67]}, {"time": 1, "value": -0.02, "curve": [1.073, -5.19, 1.151, -11.66]}, {"time": 1.2, "value": -11.66, "curve": [1.283, -11.66, 1.45, 6.75]}, {"time": 1.5333, "value": 6.75, "curve": [1.568, 6.75, 1.616, 3.67]}, {"time": 1.6667, "value": -0.02}]}, "Spike1": {"rotate": [{"value": -39.2, "curve": [0.003, 27.66, 0.025, 27.66]}, {"time": 0.0333, "value": 27.66, "curve": [0.083, 27.66, 0.183, -20.49]}, {"time": 0.2333, "value": -20.49, "curve": [0.283, -20.49, 0.383, 17.99]}, {"time": 0.4333, "value": 17.99, "curve": [0.475, 17.99, 0.558, -2.45]}, {"time": 0.6, "value": -2.45, "curve": [0.675, 2.15, 0.75, 6.75]}, {"time": 0.8, "value": 6.75, "curve": [0.85, 6.75, 0.925, 2.12]}, {"time": 1, "value": -2.5, "curve": [1.023, -4.16, 1.045, -5.81]}, {"time": 1.0667, "value": -7.2, "curve": [1.105, -9.8, 1.14, -11.66]}, {"time": 1.1667, "value": -11.66, "curve": [1.25, -11.66, 1.417, 6.75]}, {"time": 1.5, "value": 6.75, "curve": [1.542, 6.75, 1.604, 2.12]}, {"time": 1.6667, "value": -2.5}]}, "Spike10": {"rotate": [{"value": 34.26, "curve": [0.003, -29.25, 0.025, -29.25]}, {"time": 0.0333, "value": -29.25, "curve": [0.083, -29.25, 0.183, 17.23]}, {"time": 0.2333, "value": 17.23, "curve": [0.283, 17.23, 0.383, -24.45]}, {"time": 0.4333, "value": -24.45, "curve": [0.475, -24.45, 0.558, -2.18]}, {"time": 0.6, "value": -2.18, "curve": [0.651, -4.25, 0.697, -5.73]}, {"time": 0.7333, "value": -5.73, "curve": [0.798, -5.73, 0.908, 1.22]}, {"time": 1, "value": 5.4, "curve": [1.038, 7.48, 1.073, 8.96]}, {"time": 1.1, "value": 8.96, "curve": [1.183, 8.96, 1.35, -5.73]}, {"time": 1.4333, "value": -5.73, "curve": [1.49, -5.73, 1.586, 1.22]}, {"time": 1.6667, "value": 5.4}]}, "Spike6": {"rotate": [{"value": 39.38, "curve": [0.01, -22.77, 0.075, -22.77]}, {"time": 0.1, "value": -22.77, "curve": [0.133, -22.77, 0.2, 21.76]}, {"time": 0.2333, "value": 21.76, "curve": [0.283, 21.76, 0.383, -19.61]}, {"time": 0.4333, "value": -19.61, "curve": [0.483, -19.61, 0.583, 3.55]}, {"time": 0.6333, "value": 3.55, "curve": [0.706, -0.57, 0.784, -5.73]}, {"time": 0.8333, "value": -5.73, "curve": [0.876, -5.73, 0.936, -3.28]}, {"time": 1, "value": -0.33, "curve": [1.073, 3.8, 1.151, 8.96]}, {"time": 1.2, "value": 8.96, "curve": [1.283, 8.96, 1.45, -5.73]}, {"time": 1.5333, "value": -5.73, "curve": [1.568, -5.73, 1.616, -3.28]}, {"time": 1.6667, "value": -0.33}]}, "Spike7": {"rotate": [{"value": 43.76, "curve": [0.013, -16.22, 0.1, -16.22]}, {"time": 0.1333, "value": -16.22, "curve": [0.167, -16.22, 0.233, 26]}, {"time": 0.2667, "value": 26, "curve": [0.317, 26, 0.417, -15.29]}, {"time": 0.4667, "value": -15.29, "curve": [0.508, -15.29, 0.592, 7.74]}, {"time": 0.6333, "value": 7.74, "curve": [0.725, 4.63, 0.86, -5.73]}, {"time": 0.9333, "value": -5.73, "curve": [0.953, -5.73, 0.975, -5.28]}, {"time": 1, "value": -4.52, "curve": [1.092, -1.41, 1.227, 8.96]}, {"time": 1.3, "value": 8.96, "curve": [1.383, 8.96, 1.55, -5.73]}, {"time": 1.6333, "value": -5.73, "curve": [1.643, -5.73, 1.654, -5.28]}, {"time": 1.6667, "value": -4.52}]}, "Spike8": {"rotate": [{"value": 42.87, "curve": [0.013, -21.41, 0.1, -21.41]}, {"time": 0.1333, "value": -21.41, "curve": [0.175, -21.41, 0.258, 26.26]}, {"time": 0.3, "value": 26.26, "curve": [0.35, 26.26, 0.45, -15.65]}, {"time": 0.5, "value": -15.65, "curve": [0.542, -15.65, 0.625, 7.05]}, {"time": 0.6667, "value": 7.05, "curve": [0.692, 8.22, 0.714, 8.96]}, {"time": 0.7333, "value": 8.96, "curve": [0.798, 8.96, 0.915, -0.17]}, {"time": 1, "value": -3.82, "curve": [1.025, -5, 1.048, -5.74]}, {"time": 1.0667, "value": -5.74, "curve": [1.15, -5.74, 1.317, 8.96]}, {"time": 1.4, "value": 8.96, "curve": [1.465, 8.96, 1.581, -0.17]}, {"time": 1.6667, "value": -3.82}]}, "Spike9": {"rotate": [{"value": 41.81, "curve": [0.013, -22.06, 0.1, -22.06]}, {"time": 0.1333, "value": -22.06, "curve": [0.183, -22.06, 0.283, 24.98]}, {"time": 0.3333, "value": 24.98, "curve": [0.383, 24.98, 0.483, -16.81]}, {"time": 0.5333, "value": -16.81, "curve": [0.575, -16.81, 0.658, 6.25]}, {"time": 0.7, "value": 6.25, "curve": [0.738, 7.87, 0.772, 8.96]}, {"time": 0.8, "value": 8.96, "curve": [0.848, 8.96, 0.933, 0.97]}, {"time": 1, "value": -3.02, "curve": [1.038, -4.65, 1.072, -5.73]}, {"time": 1.1, "value": -5.73, "curve": [1.183, -5.73, 1.35, 8.96]}, {"time": 1.4333, "value": 8.96, "curve": [1.49, 8.96, 1.589, 0.97]}, {"time": 1.6667, "value": -3.02}]}, "Tentacle5": {"rotate": [{"value": 6.03, "curve": [0.012, 8.17, 0.023, 9.39]}, {"time": 0.0333, "value": 9.39, "curve": [0.1, 9.39, 0.233, -49.78]}, {"time": 0.3, "value": -49.78, "curve": [0.373, -49.78, 0.511, -5.1]}, {"time": 0.6, "value": 5.41, "curve": [0.612, 7.94, 0.623, 9.39]}, {"time": 0.6333, "value": 9.39, "curve": [0.725, 9.39, 0.908, -49.78]}, {"time": 1, "value": -49.78, "curve": [1.1, -49.78, 1.3, 9.39]}, {"time": 1.4, "value": 9.39, "curve": [1.467, 9.39, 1.6, -49.78]}, {"time": 1.6667, "value": -49.78}]}, "Tentacle3_Mid": {"rotate": [{"value": -3.27, "curve": [0.083, 21.42, 0.176, 56.4]}, {"time": 0.2333, "value": 56.4, "curve": [0.251, 56.4, 0.275, 48]}, {"time": 0.3, "value": 36.14, "curve": [0.357, 11.69, 0.426, -29.86]}, {"time": 0.4667, "value": -29.86, "curve": [0.502, -29.86, 0.549, -18.51]}, {"time": 0.6, "value": -3.82, "curve": [0.718, 20.88, 0.852, 56.4]}, {"time": 0.9333, "value": 56.4, "curve": [0.953, 56.4, 0.976, 54.76]}, {"time": 1, "value": 51.86, "curve": [1.02, 48.48, 1.043, 42.9]}, {"time": 1.0667, "value": 36.14, "curve": [1.135, 11.69, 1.218, -29.86]}, {"time": 1.2667, "value": -29.86, "curve": [1.358, -29.86, 1.542, 56.4]}, {"time": 1.6333, "value": 56.4, "curve": [1.643, 56.4, 1.655, 54.76]}, {"time": 1.6667, "value": 51.86}]}, "Tentacle3_Btm": {"rotate": [{"value": -19.18, "curve": [0.077, 10.49, 0.177, 73.54]}, {"time": 0.2333, "value": 73.54, "curve": [0.253, 73.54, 0.275, 69.14]}, {"time": 0.3, "value": 61.89, "curve": [0.373, 35.84, 0.477, -37.96]}, {"time": 0.5333, "value": -37.96, "curve": [0.552, -37.96, 0.575, -29.31]}, {"time": 0.6, "value": -16.08, "curve": [0.734, 14.65, 0.903, 73.54]}, {"time": 1, "value": 73.54, "curve": [1.019, 73.54, 1.042, 69.14]}, {"time": 1.0667, "value": 61.89, "curve": [1.14, 35.84, 1.243, -37.96]}, {"time": 1.3, "value": -37.96, "curve": [1.392, -37.96, 1.575, 73.54]}, {"time": 1.6667, "value": 73.54}]}, "Tentacle6": {"rotate": [{"value": -8.02, "curve": [0.012, -9.72, 0.023, -10.68]}, {"time": 0.0333, "value": -10.68, "curve": [0.1, -10.68, 0.233, 36.14]}, {"time": 0.3, "value": 36.14, "curve": [0.373, 36.14, 0.511, 0.78]}, {"time": 0.6, "value": -7.54, "curve": [0.612, -9.53, 0.623, -10.68]}, {"time": 0.6333, "value": -10.68, "curve": [0.725, -10.68, 0.908, 36.14]}, {"time": 1, "value": 36.14, "curve": [1.1, 36.14, 1.3, -10.68]}, {"time": 1.4, "value": -10.68, "curve": [1.467, -10.68, 1.6, 36.14]}, {"time": 1.6667, "value": 36.14}]}, "Tentacle3_Mid2": {"rotate": [{"value": -1.59, "curve": [0.083, 24.91, 0.176, 62.45]}, {"time": 0.2333, "value": 62.45, "curve": [0.251, 62.45, 0.275, 53.43]}, {"time": 0.3, "value": 40.71, "curve": [0.357, 14.47, 0.426, -30.13]}, {"time": 0.4667, "value": -30.13, "curve": [0.502, -30.13, 0.549, -17.95]}, {"time": 0.6, "value": -2.18, "curve": [0.718, 24.33, 0.852, 62.45]}, {"time": 0.9333, "value": 62.45, "curve": [0.953, 62.45, 0.975, 58.81]}, {"time": 1, "value": 52.81, "curve": [1.011, 49.53, 1.022, 45.39]}, {"time": 1.0333, "value": 40.71, "curve": [1.102, 14.47, 1.185, -30.13]}, {"time": 1.2333, "value": -30.13, "curve": [1.333, -30.13, 1.533, 62.45]}, {"time": 1.6333, "value": 62.45, "curve": [1.643, 62.45, 1.654, 58.81]}, {"time": 1.6667, "value": 52.81}]}, "Tentacle3_Btm2": {"rotate": [{"value": -23.87, "curve": [0.077, 3.75, 0.177, 62.45]}, {"time": 0.2333, "value": 62.45, "curve": [0.253, 62.45, 0.275, 58.35]}, {"time": 0.3, "value": 51.6, "curve": [0.373, 27.35, 0.477, -41.36]}, {"time": 0.5333, "value": -41.36, "curve": [0.552, -41.36, 0.575, -33.3]}, {"time": 0.6, "value": -20.98, "curve": [0.723, 7.62, 0.878, 62.45]}, {"time": 0.9667, "value": 62.45, "curve": [0.977, 62.45, 0.988, 61.9]}, {"time": 1, "value": 60.88, "curve": [1.01, 59.21, 1.021, 55.98]}, {"time": 1.0333, "value": 51.6, "curve": [1.107, 27.35, 1.21, -41.36]}, {"time": 1.2667, "value": -41.36, "curve": [1.367, -41.36, 1.567, 60.88]}, {"time": 1.6667, "value": 60.88}]}, "Tentacle2_Btm2": {"rotate": [{"value": -38.29, "curve": [0.087, -7.3, 0.202, 62.45]}, {"time": 0.2667, "value": 62.45, "curve": [0.277, 62.45, 0.288, 60]}, {"time": 0.3, "value": 55.68, "curve": [0.369, 34.28, 0.476, -56.67]}, {"time": 0.5333, "value": -56.67, "curve": [0.552, -56.67, 0.575, -49.72]}, {"time": 0.6, "value": -38.92, "curve": [0.708, -8.2, 0.852, 62.45]}, {"time": 0.9333, "value": 62.45, "curve": [0.953, 62.45, 0.976, 60]}, {"time": 1, "value": 55.68, "curve": [1.079, 34.28, 1.201, -56.67]}, {"time": 1.2667, "value": -56.67, "curve": [1.358, -56.67, 1.542, 62.45]}, {"time": 1.6333, "value": 62.45, "curve": [1.643, 62.45, 1.655, 60]}, {"time": 1.6667, "value": 55.68}]}, "Tentacle2_Mid2": {"rotate": [{"value": 5.37, "curve": [0.073, 31.15, 0.151, 62.45]}, {"time": 0.2, "value": 62.45, "curve": [0.226, 62.45, 0.262, 46.14]}, {"time": 0.3, "value": 26.96, "curve": [0.349, 1.18, 0.401, -30.13]}, {"time": 0.4333, "value": -30.13, "curve": [0.476, -30.13, 0.537, -12.53]}, {"time": 0.6, "value": 8.18, "curve": [0.686, 33.42, 0.776, 62.45]}, {"time": 0.8333, "value": 62.45, "curve": [0.876, 62.45, 0.937, 46.14]}, {"time": 1, "value": 26.96, "curve": [1.061, 1.18, 1.126, -30.13]}, {"time": 1.1667, "value": -30.13, "curve": [1.267, -30.13, 1.467, 62.45]}, {"time": 1.5667, "value": 62.45, "curve": [1.592, 62.45, 1.629, 46.14]}, {"time": 1.6667, "value": 26.96}]}, "Tentacle4": {"rotate": [{"value": -8.58, "curve": [0.012, -9.92, 0.023, -10.68]}, {"time": 0.0333, "value": -10.68, "curve": [0.1, -10.68, 0.233, 26.28]}, {"time": 0.3, "value": 26.28, "curve": [0.373, 26.28, 0.511, -1.63]}, {"time": 0.6, "value": -8.2, "curve": [0.612, -9.77, 0.623, -10.68]}, {"time": 0.6333, "value": -10.68, "curve": [0.725, -10.68, 0.908, 26.28]}, {"time": 1, "value": 26.28, "curve": [1.1, 26.28, 1.3, -10.68]}, {"time": 1.4, "value": -10.68, "curve": [1.467, -10.68, 1.6, 26.28]}, {"time": 1.6667, "value": 26.28}]}, "Tentacle2_Btm": {"rotate": [{"value": -46.79, "curve": [0.087, -15.83, 0.202, 53.84]}, {"time": 0.2667, "value": 53.84, "curve": [0.277, 53.84, 0.288, 51.4]}, {"time": 0.3, "value": 47.08, "curve": [0.369, 25.7, 0.476, -65.15]}, {"time": 0.5333, "value": -65.15, "curve": [0.552, -65.15, 0.575, -58.21]}, {"time": 0.6, "value": -47.42, "curve": [0.708, -16.73, 0.852, 53.84]}, {"time": 0.9333, "value": 53.84, "curve": [0.953, 53.84, 0.976, 51.4]}, {"time": 1, "value": 47.08, "curve": [1.079, 25.7, 1.201, -65.15]}, {"time": 1.2667, "value": -65.15, "curve": [1.358, -65.15, 1.542, 53.84]}, {"time": 1.6333, "value": 53.84, "curve": [1.643, 53.84, 1.655, 51.4]}, {"time": 1.6667, "value": 47.08}]}, "Tentacle2_Mid": {"rotate": [{"value": -3.22, "curve": [0.073, 20.8, 0.151, 49.96]}, {"time": 0.2, "value": 49.96, "curve": [0.226, 49.96, 0.262, 34.77]}, {"time": 0.3, "value": 16.89, "curve": [0.349, -7.13, 0.401, -36.3]}, {"time": 0.4333, "value": -36.3, "curve": [0.476, -36.3, 0.537, -19.9]}, {"time": 0.6, "value": -0.6, "curve": [0.686, 22.92, 0.776, 49.96]}, {"time": 0.8333, "value": 49.96, "curve": [0.876, 49.96, 0.937, 34.77]}, {"time": 1, "value": 16.89, "curve": [1.061, -7.13, 1.126, -36.3]}, {"time": 1.1667, "value": -36.3, "curve": [1.267, -36.3, 1.467, 49.96]}, {"time": 1.5667, "value": 49.96, "curve": [1.592, 49.96, 1.629, 34.77]}, {"time": 1.6667, "value": 16.89}]}, "Tentacle3": {"rotate": [{"value": 4.21, "curve": [0.012, 5.78, 0.023, 6.68]}, {"time": 0.0333, "value": 6.68, "curve": [0.1, 6.68, 0.233, -36.75]}, {"time": 0.3, "value": -36.75, "curve": [0.373, -36.75, 0.511, -3.96]}, {"time": 0.6, "value": 3.76, "curve": [0.612, 5.61, 0.623, 6.68]}, {"time": 0.6333, "value": 6.68, "curve": [0.725, 6.68, 0.908, -36.75]}, {"time": 1, "value": -36.75, "curve": [1.1, -36.75, 1.3, 6.68]}, {"time": 1.4, "value": 6.68, "curve": [1.467, 6.68, 1.6, -36.75]}, {"time": 1.6667, "value": -36.75}]}, "DangleHandle": {"rotate": [{"value": -15.4, "curve": [0.079, -4.37, 0.177, 16.3]}, {"time": 0.2333, "value": 16.3, "curve": [0.251, 16.3, 0.275, 12.3]}, {"time": 0.3, "value": 6.7, "curve": [0.311, 3.66, 0.322, 0.04]}, {"time": 0.3333, "value": -3.58, "curve": [0.408, -13.51, 0.483, -23.45]}, {"time": 0.5333, "value": -23.45, "curve": [0.552, -23.45, 0.575, -19.55]}, {"time": 0.6, "value": -13.82, "curve": [0.692, -2.53, 0.802, 16.3]}, {"time": 0.8667, "value": 16.3, "curve": [0.903, 16.3, 0.949, 12.3]}, {"time": 1, "value": 6.7, "curve": [1.021, 3.66, 1.044, 0.04]}, {"time": 1.0667, "value": -3.58, "curve": [1.129, -13.51, 1.192, -23.45]}, {"time": 1.2333, "value": -23.45, "curve": [1.317, -23.45, 1.483, 16.3]}, {"time": 1.5667, "value": 16.3, "curve": [1.594, 16.3, 1.628, 12.3]}, {"time": 1.6667, "value": 6.7}], "translate": [{"x": -8.18, "y": 2.62, "curve": [0.05, 16.95, 0.1, 43.87, 0.05, 3.65, 0.1, 4.74]}, {"time": 0.1333, "x": 43.87, "y": 4.74, "curve": [0.175, 43.87, 0.238, 19.95, 0.175, 4.74, 0.238, 3.77]}, {"time": 0.3, "x": -3.96, "y": 2.8, "curve": [0.35, -27.88, 0.4, -51.8, 0.35, 1.82, 0.4, 0.85]}, {"time": 0.4333, "x": -51.8, "y": 0.85, "curve": [0.475, -51.8, 0.537, -28.65, 0.475, 0.85, 0.537, 1.79]}, {"time": 0.6, "x": -3.96, "y": 2.8, "curve": [0.675, 19.95, 0.75, 43.87, 0.675, 3.77, 0.75, 4.74]}, {"time": 0.8, "x": 43.87, "y": 4.74, "curve": [0.85, 43.87, 0.925, 19.95, 0.85, 4.74, 0.925, 3.77]}, {"time": 1, "x": -3.96, "y": 2.8, "curve": [1.063, -27.88, 1.125, -51.8, 1.063, 1.82, 1.125, 0.85]}, {"time": 1.1667, "x": -51.8, "y": 0.85, "curve": [1.25, -51.8, 1.417, 43.87, 1.25, 0.85, 1.417, 4.74]}, {"time": 1.5, "x": 43.87, "y": 4.74, "curve": [1.542, 43.87, 1.604, 19.95, 1.542, 4.74, 1.604, 3.77]}, {"time": 1.6667, "x": -3.96, "y": 2.8}], "scale": [{"y": 0.313, "curve": [0.075, 1, 0.225, 1, 0.075, 0.313, 0.225, 1.723]}, {"time": 0.3, "y": 1.723, "curve": [0.375, 1, 0.525, 1, 0.375, 1.723, 0.525, 0.313]}, {"time": 0.6, "y": 0.313, "curve": [0.7, 1, 0.9, 1, 0.7, 0.313, 0.9, 1.723]}, {"time": 1, "y": 1.723, "curve": [1.083, 1, 1.25, 1, 1.083, 1.723, 1.25, 0.313]}, {"time": 1.3333, "y": 0.313, "curve": [1.417, 1, 1.583, 1, 1.417, 0.313, 1.583, 1.723]}, {"time": 1.6667, "y": 1.723}]}, "Tentacles": {"translate": [{"time": 0.6}, {"time": 1.5333, "x": 0.52}], "scale": [{"x": 1.199}, {"time": 0.3, "x": 0.699}, {"time": 0.6, "x": 1.199}, {"time": 1, "x": 0.699}, {"time": 1.3333, "x": 1.199}, {"time": 1.6667, "x": 0.699}]}, "Mouth3": {"rotate": [{"value": -2.12, "curve": [0.033, -2.12, 0.1, 22.13]}, {"time": 0.1333, "value": 22.13, "curve": [0.175, 22.13, 0.258, -2.12]}, {"time": 0.3, "value": -2.12, "curve": [0.333, -2.12, 0.4, 22.13]}, {"time": 0.4333, "value": 22.13, "curve": [0.475, 22.13, 0.558, -2.12]}, {"time": 0.6, "value": -2.12, "curve": [0.65, -2.12, 0.75, 22.13]}, {"time": 0.8, "value": 22.13, "curve": [0.85, 22.13, 0.95, -2.12]}, {"time": 1, "value": -2.12, "curve": [1.042, -2.12, 1.125, 22.13]}, {"time": 1.1667, "value": 22.13, "curve": [1.208, 22.13, 1.292, -2.12]}, {"time": 1.3333, "value": -2.12, "curve": [1.375, -2.12, 1.458, 22.13]}, {"time": 1.5, "value": 22.13, "curve": [1.542, 22.13, 1.625, -2.12]}, {"time": 1.6667, "value": -2.12}]}, "Mouth6": {"rotate": [{"value": -27.19, "curve": [0.036, -13.44, 0.076, 4.89]}, {"time": 0.1, "value": 4.89, "curve": [0.133, 4.89, 0.2, -43.41]}, {"time": 0.2333, "value": -43.41, "curve": [0.251, -43.41, 0.275, -35.33]}, {"time": 0.3, "value": -25.64, "curve": [0.336, -12.07, 0.375, 4.89]}, {"time": 0.4, "value": 4.89, "curve": [0.433, 4.89, 0.5, -43.41]}, {"time": 0.5333, "value": -43.41, "curve": [0.551, -43.41, 0.575, -35.51]}, {"time": 0.6, "value": -25.64, "curve": [0.648, -12.07, 0.701, 4.89]}, {"time": 0.7333, "value": 4.89, "curve": [0.783, 4.89, 0.883, -43.41]}, {"time": 0.9333, "value": -43.41, "curve": [0.951, -43.41, 0.975, -35.33]}, {"time": 1, "value": -25.64, "curve": [1.036, -12.07, 1.075, 4.89]}, {"time": 1.1, "value": 4.89, "curve": [1.142, 4.89, 1.225, -43.41]}, {"time": 1.2667, "value": -43.41, "curve": [1.308, -43.41, 1.392, 4.89]}, {"time": 1.4333, "value": 4.89, "curve": [1.475, 4.89, 1.558, -43.41]}, {"time": 1.6, "value": -43.41, "curve": [1.617, -43.41, 1.641, -35.33]}, {"time": 1.6667, "value": -25.64}]}, "Tentacle3_Btm3": {"rotate": [{"value": -19.18, "curve": [0.077, 10.49, 0.177, 73.54]}, {"time": 0.2333, "value": 73.54, "curve": [0.253, 73.54, 0.275, 69.14]}, {"time": 0.3, "value": 61.89, "curve": [0.373, 35.84, 0.477, -37.96]}, {"time": 0.5333, "value": -37.96, "curve": [0.552, -37.96, 0.575, -29.31]}, {"time": 0.6, "value": -16.08, "curve": [0.712, 14.65, 0.853, 73.54]}, {"time": 0.9333, "value": 73.54, "curve": [0.953, 73.54, 0.975, 69.14]}, {"time": 1, "value": 61.89, "curve": [1.073, 35.84, 1.177, -37.96]}, {"time": 1.2333, "value": -37.96, "curve": [1.333, -37.96, 1.533, 73.54]}, {"time": 1.6333, "value": 73.54, "curve": [1.643, 73.54, 1.654, 69.14]}, {"time": 1.6667, "value": 61.89}]}, "Tentacle3_Mid3": {"rotate": [{"value": -3.27, "curve": [0.083, 21.42, 0.176, 56.4]}, {"time": 0.2333, "value": 56.4, "curve": [0.251, 56.4, 0.275, 48]}, {"time": 0.3, "value": 36.14, "curve": [0.357, 11.69, 0.426, -29.86]}, {"time": 0.4667, "value": -29.86, "curve": [0.502, -29.86, 0.549, -18.51]}, {"time": 0.6, "value": -3.82, "curve": [0.706, 20.88, 0.827, 56.4]}, {"time": 0.9, "value": 56.4, "curve": [0.927, 56.4, 0.962, 48]}, {"time": 1, "value": 36.14, "curve": [1.069, 11.69, 1.151, -29.86]}, {"time": 1.2, "value": -29.86, "curve": [1.3, -29.86, 1.5, 56.4]}, {"time": 1.6, "value": 56.4, "curve": [1.618, 56.4, 1.641, 48]}, {"time": 1.6667, "value": 36.14}]}, "Tentacle1": {"rotate": [{"value": 6.03, "curve": [0.012, 8.17, 0.023, 9.39]}, {"time": 0.0333, "value": 9.39, "curve": [0.1, 9.39, 0.233, -49.78]}, {"time": 0.3, "value": -49.78, "curve": [0.373, -49.78, 0.511, -5.1]}, {"time": 0.6, "value": 5.41, "curve": [0.612, 7.94, 0.623, 9.39]}, {"time": 0.6333, "value": 9.39, "curve": [0.725, 9.39, 0.908, -49.78]}, {"time": 1, "value": -49.78, "curve": [1.1, -49.78, 1.3, 9.39]}, {"time": 1.4, "value": 9.39, "curve": [1.467, 9.39, 1.6, -49.78]}, {"time": 1.6667, "value": -49.78}]}, "Tentacle3_Btm4": {"rotate": [{"value": -23.87, "curve": [0.077, 3.75, 0.177, 62.45]}, {"time": 0.2333, "value": 62.45, "curve": [0.253, 62.45, 0.275, 58.35]}, {"time": 0.3, "value": 51.6, "curve": [0.373, 27.35, 0.477, -41.36]}, {"time": 0.5333, "value": -41.36, "curve": [0.552, -41.36, 0.575, -33.3]}, {"time": 0.6, "value": -20.98, "curve": [0.712, 7.62, 0.853, 62.45]}, {"time": 0.9333, "value": 62.45, "curve": [0.953, 62.45, 0.975, 58.35]}, {"time": 1, "value": 51.6, "curve": [1.073, 27.35, 1.177, -41.36]}, {"time": 1.2333, "value": -41.36, "curve": [1.333, -41.36, 1.533, 62.45]}, {"time": 1.6333, "value": 62.45, "curve": [1.643, 62.45, 1.654, 58.35]}, {"time": 1.6667, "value": 51.6}]}, "Tentacle3_Mid4": {"rotate": [{"value": -1.59, "curve": [0.083, 24.91, 0.176, 62.45]}, {"time": 0.2333, "value": 62.45, "curve": [0.251, 62.45, 0.275, 53.43]}, {"time": 0.3, "value": 40.71, "curve": [0.357, 14.47, 0.426, -30.13]}, {"time": 0.4667, "value": -30.13, "curve": [0.502, -30.13, 0.549, -17.95]}, {"time": 0.6, "value": -2.18, "curve": [0.706, 24.33, 0.827, 62.45]}, {"time": 0.9, "value": 62.45, "curve": [0.927, 62.45, 0.962, 53.43]}, {"time": 1, "value": 40.71, "curve": [1.069, 14.47, 1.151, -30.13]}, {"time": 1.2, "value": -30.13, "curve": [1.3, -30.13, 1.5, 62.45]}, {"time": 1.6, "value": 62.45, "curve": [1.618, 62.45, 1.641, 53.43]}, {"time": 1.6667, "value": 40.71}]}, "Tentacle2": {"rotate": [{"value": -8.02, "curve": [0.012, -9.72, 0.023, -10.68]}, {"time": 0.0333, "value": -10.68, "curve": [0.1, -10.68, 0.233, 36.14]}, {"time": 0.3, "value": 36.14, "curve": [0.373, 36.14, 0.511, 0.78]}, {"time": 0.6, "value": -7.54, "curve": [0.612, -9.53, 0.623, -10.68]}, {"time": 0.6333, "value": -10.68, "curve": [0.725, -10.68, 0.908, 36.14]}, {"time": 1, "value": 36.14, "curve": [1.1, 36.14, 1.3, -10.68]}, {"time": 1.4, "value": -10.68, "curve": [1.467, -10.68, 1.6, 36.14]}, {"time": 1.6667, "value": 36.14}]}, "Mouth2": {"scale": [{}, {"time": 0.3, "x": 1.013, "y": 1.013}, {"time": 0.6}]}, "Mouth7": {"rotate": [{"value": -40.38, "curve": [0.017, -40.38, 0.05, 6.58]}, {"time": 0.0667, "value": 6.58, "curve": [0.117, 6.58, 0.217, -43.41]}, {"time": 0.2667, "value": -43.41, "curve": [0.277, -43.41, 0.288, -42.68]}, {"time": 0.3, "value": -41.36, "curve": [0.339, -33.65, 0.401, 4.89]}, {"time": 0.4333, "value": 4.89, "curve": [0.467, 4.89, 0.533, -43.41]}, {"time": 0.5667, "value": -43.41, "curve": [0.577, -43.41, 0.588, -42.66]}, {"time": 0.6, "value": -41.36, "curve": [0.648, -33.65, 0.726, 4.89]}, {"time": 0.7667, "value": 4.89, "curve": [0.817, 4.89, 0.917, -43.41]}, {"time": 0.9667, "value": -43.41, "curve": [0.977, -43.41, 0.988, -42.68]}, {"time": 1, "value": -41.36, "curve": [1.048, -33.65, 1.126, 4.89]}, {"time": 1.1667, "value": 4.89, "curve": [1.208, 4.89, 1.292, -43.41]}, {"time": 1.3333, "value": -43.41, "curve": [1.375, -43.41, 1.458, 4.89]}, {"time": 1.5, "value": 4.89, "curve": [1.542, 4.89, 1.625, -41.36]}, {"time": 1.6667, "value": -41.36}]}, "Mouth5": {"rotate": [{"value": 20.26, "curve": [0.02, 13.9, 0.043, 2.98]}, {"time": 0.0667, "value": -9.36, "curve": [0.139, 4.39, 0.218, 22.13]}, {"time": 0.2667, "value": 22.13, "curve": [0.276, 22.13, 0.288, 20.92]}, {"time": 0.3, "value": 18.98, "curve": [0.343, 12.95, 0.401, -2.12]}, {"time": 0.4333, "value": -2.12, "curve": [0.467, -2.12, 0.533, 22.13]}, {"time": 0.5667, "value": 22.13, "curve": [0.577, 22.13, 0.588, 20.97]}, {"time": 0.6, "value": 18.98, "curve": [0.643, 12.95, 0.701, -2.12]}, {"time": 0.7333, "value": -2.12, "curve": [0.783, -2.12, 0.883, 22.13]}, {"time": 0.9333, "value": 22.13, "curve": [0.952, 22.13, 0.975, 20.92]}, {"time": 1, "value": 18.98, "curve": [1.043, 12.95, 1.101, -2.12]}, {"time": 1.1333, "value": -2.12, "curve": [1.175, -2.12, 1.258, 22.13]}, {"time": 1.3, "value": 22.13, "curve": [1.342, 22.13, 1.425, -2.12]}, {"time": 1.4667, "value": -2.12, "curve": [1.508, -2.12, 1.592, 22.13]}, {"time": 1.6333, "value": 22.13, "curve": [1.643, 22.13, 1.654, 20.92]}, {"time": 1.6667, "value": 18.98}]}, "Mouth": {"translate": [{}, {"time": 0.3, "y": -10.97, "curve": [0.375, 0, 0.525, 0, 0.375, -10.97, 0.525, 4.02]}, {"time": 0.6, "y": 4.02, "curve": [0.7, 0, 0.9, 0, 0.7, 4.02, 0.9, -10.97]}, {"time": 1, "y": -10.97, "curve": [1.083, 0, 1.25, 0, 1.083, -10.97, 1.25, 4.02]}, {"time": 1.3333, "y": 4.02, "curve": [1.417, 0, 1.583, 0, 1.417, 4.02, 1.583, -10.97]}, {"time": 1.6667, "y": -10.97}]}, "Mouth8": {"rotate": [{"value": -32.57, "curve": [0.017, -32.57, 0.05, -0.45]}, {"time": 0.0667, "value": -0.45, "curve": [0.125, -0.45, 0.242, -31.71]}, {"time": 0.3, "value": -31.71, "curve": [0.313, -38.54, 0.324, -43.41]}, {"time": 0.3333, "value": -43.41, "curve": [0.375, -43.41, 0.458, 4.89]}, {"time": 0.5, "value": 4.89, "curve": [0.524, 4.89, 0.566, -18.4]}, {"time": 0.6, "value": -31.71, "curve": [0.613, -38.54, 0.624, -43.41]}, {"time": 0.6333, "value": -43.41, "curve": [0.683, -43.41, 0.783, 4.89]}, {"time": 0.8333, "value": 4.89, "curve": [0.874, 4.89, 0.943, -17.99]}, {"time": 1, "value": -31.71, "curve": [1.025, -38.54, 1.049, -43.41]}, {"time": 1.0667, "value": -43.41, "curve": [1.108, -43.41, 1.192, 4.89]}, {"time": 1.2333, "value": 4.89, "curve": [1.275, 4.89, 1.358, -43.41]}, {"time": 1.4, "value": -43.41, "curve": [1.442, -43.41, 1.525, 4.89]}, {"time": 1.5667, "value": 4.89, "curve": [1.591, 4.89, 1.632, -17.99]}, {"time": 1.6667, "value": -31.71}]}, "Mouth4": {"rotate": [{"value": 7.72, "curve": [0.017, 7.72, 0.05, -21.65]}, {"time": 0.0667, "value": -21.65, "curve": [0.125, -21.65, 0.242, 6.8]}, {"time": 0.3, "value": 6.8, "curve": [0.313, 1.94, 0.325, -2.12]}, {"time": 0.3333, "value": -2.12, "curve": [0.383, -2.12, 0.483, 22.13]}, {"time": 0.5333, "value": 22.13, "curve": [0.55, 22.13, 0.576, 13.88]}, {"time": 0.6, "value": 6.8, "curve": [0.625, 1.94, 0.649, -2.12]}, {"time": 0.6667, "value": -2.12, "curve": [0.717, -2.12, 0.817, 22.13]}, {"time": 0.8667, "value": 22.13, "curve": [0.899, 22.13, 0.952, 13.62]}, {"time": 1, "value": 6.8, "curve": [1.025, 1.94, 1.049, -2.12]}, {"time": 1.0667, "value": -2.12, "curve": [1.108, -2.12, 1.192, 22.13]}, {"time": 1.2333, "value": 22.13, "curve": [1.275, 22.13, 1.358, -2.12]}, {"time": 1.4, "value": -2.12, "curve": [1.442, -2.12, 1.525, 22.13]}, {"time": 1.5667, "value": 22.13, "curve": [1.591, 22.13, 1.63, 13.62]}, {"time": 1.6667, "value": 6.8}]}}, "drawOrder": [{"offsets": [{"slot": "Eyes", "offset": 4}]}]}, "attack-shoot": {"slots": {"images/Eye": {"attachment": [{"time": 0.0667, "name": "Eye"}, {"time": 1.1667}]}, "images/Eye2": {"attachment": [{"time": 0.0667, "name": "Eye"}, {"time": 1.1667}]}, "images/Eye3": {"attachment": [{}, {"time": 0.0667, "name": "Eye"}, {"time": 1.1667}]}, "Mouth": {"attachment": [{"name": "Mouth Closed"}, {"time": 0.0667, "name": "Mouth Open"}]}, "MouthBack": {"attachment": [{"name": "Mouth Back Shut"}, {"time": 0.0667, "name": "Mouth Back"}]}, "ScuttleEye1": {"attachment": [{"time": 0.0667, "name": "<PERSON><PERSON><PERSON>_<PERSON>_Back"}, {"time": 1.1667, "name": "<PERSON><PERSON><PERSON>_Eye_Closed"}]}, "ScuttleEye2": {"attachment": [{"time": 0.0667, "name": "<PERSON><PERSON><PERSON>_<PERSON>_Back"}, {"time": 1.1667, "name": "<PERSON><PERSON><PERSON>_Eye_Closed"}]}, "ScuttleEye3": {"attachment": [{"name": "<PERSON><PERSON><PERSON>_Eye_Closed"}, {"time": 0.0667, "name": "<PERSON><PERSON><PERSON>_<PERSON>_Back"}, {"time": 1.1667, "name": "<PERSON><PERSON><PERSON>_Eye_Closed"}]}}, "bones": {"FACE": {"rotate": [{"time": 0.0667, "curve": [0.15, 0, 0.317, -8.22]}, {"time": 0.4, "value": -8.22, "curve": [0.567, -8.22, 0.9, 0]}, {"time": 1.0667}], "translate": [{"x": 0.84, "y": 4.16, "curve": [0.017, -23.65, 0.036, -44.45, 0.017, 3.67, 0.036, 3.25]}, {"time": 0.0667, "x": -45.76, "y": 3.22, "curve": [0.08, 9.08, 0.217, 50.86, 0.08, 4.57, 0.217, 5.6]}, {"time": 0.2667, "x": 50.86, "y": 5.6, "curve": [0.467, 50.86, 0.867, -21.84, 0.467, 5.6, 0.867, 4.16]}, {"time": 1.0667, "x": -21.84, "y": 4.16, "curve": [1.362, -21.84, 1.494, -5.93, 1.362, 4.16, 1.494, 4.16]}, {"time": 1.6667, "x": 7.05, "y": 4.16}], "scale": [{}, {"time": 0.0667, "x": 1.219, "y": 1.219}, {"time": 0.2}]}, "ScuttleEye1": {"scale": [{"time": 0.0667}, {"time": 0.1, "x": 1.015, "y": 0.191, "curve": [0.133, 1.015, 0.2, 0.827, 0.133, 0.191, 0.2, 0.827]}, {"time": 0.2333, "x": 0.827, "y": 0.827, "curve": [0.275, 0.827, 0.358, 1, 0.275, 0.827, 0.358, 1]}, {"time": 0.4, "curve": "stepped"}, {"time": 1.1667, "x": 1.512, "curve": [1.183, 1.512, 1.217, 1.5, 1.183, 1, 1.217, 0.721]}, {"time": 1.2333, "x": 1.5, "y": 0.721, "curve": [1.267, 1.5, 1.333, 0.908, 1.267, 0.721, 1.333, 1.318]}, {"time": 1.3667, "x": 0.908, "y": 1.318, "curve": [1.4, 0.908, 1.467, 1, 1.4, 1.318, 1.467, 1]}, {"time": 1.5}]}, "ScuttleEye2": {"scale": [{"time": 0.0667}, {"time": 0.1, "x": 1.015, "y": 0.191, "curve": [0.133, 1.015, 0.2, 0.827, 0.133, 0.191, 0.2, 0.827]}, {"time": 0.2333, "x": 0.827, "y": 0.827, "curve": [0.275, 0.827, 0.358, 1, 0.275, 0.827, 0.358, 1]}, {"time": 0.4, "curve": "stepped"}, {"time": 1.1667, "x": 1.512, "curve": [1.183, 1.512, 1.217, 1.5, 1.183, 1, 1.217, 0.721]}, {"time": 1.2333, "x": 1.5, "y": 0.721, "curve": [1.267, 1.5, 1.333, 0.908, 1.267, 0.721, 1.333, 1.318]}, {"time": 1.3667, "x": 0.908, "y": 1.318, "curve": [1.4, 0.908, 1.467, 1, 1.4, 1.318, 1.467, 1]}, {"time": 1.5}]}, "ScuttleEye3": {"scale": [{"time": 0.0667}, {"time": 0.1, "x": 1.015, "y": 0.191, "curve": [0.133, 1.015, 0.2, 0.827, 0.133, 0.191, 0.2, 0.827]}, {"time": 0.2333, "x": 0.827, "y": 0.827, "curve": [0.275, 0.827, 0.358, 1, 0.275, 0.827, 0.358, 1]}, {"time": 0.4, "curve": "stepped"}, {"time": 1.1667, "x": 1.512, "curve": [1.183, 1.512, 1.217, 1.5, 1.183, 1, 1.217, 0.721]}, {"time": 1.2333, "x": 1.5, "y": 0.721, "curve": [1.267, 1.5, 1.333, 0.908, 1.267, 0.721, 1.333, 1.318]}, {"time": 1.3667, "x": 0.908, "y": 1.318, "curve": [1.4, 0.908, 1.467, 1, 1.4, 1.318, 1.467, 1]}, {"time": 1.5}]}, "ScuttleEyeball": {"scale": [{"time": 0.0667, "curve": [0.1, 1, 0.167, 1.296, 0.1, 1, 0.167, 1.296]}, {"time": 0.2, "x": 1.296, "y": 1.296, "curve": [0.25, 1.296, 0.35, 1, 0.25, 1.296, 0.35, 1]}, {"time": 0.4}]}, "ScuttleEyeball2": {"scale": [{"time": 0.0667, "curve": [0.1, 1, 0.167, 1.296, 0.1, 1, 0.167, 1.296]}, {"time": 0.2, "x": 1.296, "y": 1.296, "curve": [0.25, 1.296, 0.35, 1, 0.25, 1.296, 0.35, 1]}, {"time": 0.4}]}, "ScuttleEyeball3": {"scale": [{"time": 0.0667, "curve": [0.1, 1, 0.167, 1.296, 0.1, 1, 0.167, 1.296]}, {"time": 0.2, "x": 1.296, "y": 1.296, "curve": [0.25, 1.296, 0.35, 1, 0.25, 1.296, 0.35, 1]}, {"time": 0.4}]}, "MAIN": {"translate": [{"y": -33.8, "curve": [0, 0, 0.25, 0, 0, -1.14, 0.25, 16.22]}, {"time": 0.5, "y": 17.53, "curve": [0.732, 0, 0.75, 0, 0.732, 17.53, 0.75, -89.61]}, {"time": 1, "y": -92.41, "curve": [1.31, 0, 1.333, 0, 1.31, -92.41, 1.333, 14.73]}, {"time": 1.6667, "y": 17.53}], "scale": [{"x": 1.509, "y": 0.663, "curve": [0, 0.959, 0.05, 0.76, 0, 1.189, 0.05, 1.379]}, {"time": 0.0667, "x": 0.76, "y": 1.379, "curve": [0.108, 0.76, 0.192, 1.267, 0.108, 1.379, 0.192, 0.835]}, {"time": 0.2333, "x": 1.267, "y": 0.835, "curve": [0.3, 1.267, 0.433, 0.937, 0.3, 0.835, 0.433, 1.05]}, {"time": 0.5, "x": 0.937, "y": 1.05, "curve": [0.625, 0.937, 0.875, 0.987, 0.625, 1.05, 0.875, 1.006]}, {"time": 1, "x": 0.987, "y": 1.006, "curve": [1.125, 1.013, 1.25, 1.038, 1.125, 0.984, 1.25, 0.962]}, {"time": 1.3333, "x": 1.038, "y": 0.962, "curve": [1.417, 1.038, 1.542, 1.013, 1.417, 0.962, 1.542, 0.984]}, {"time": 1.6667, "x": 0.987, "y": 1.006}]}, "Spike5": {"rotate": [{"value": -43.86, "curve": [0.025, 24.37, 0.2, 24.37]}, {"time": 0.2667, "value": 24.37, "curve": [0.333, 24.37, 0.467, -26.01]}, {"time": 0.5333, "value": -26.01, "curve": [0.617, -26.01, 0.783, 12.93]}, {"time": 0.8667, "value": 12.93, "curve": [0.942, 12.93, 1.092, -8.26]}, {"time": 1.1667, "value": -8.26, "curve": [1.23, -10.3, 1.287, -11.66]}, {"time": 1.3333, "value": -11.66, "curve": [1.414, -11.66, 1.556, -1.65]}, {"time": 1.6667, "value": 3.35}]}, "Spike4": {"rotate": [{"value": -45.19, "curve": [0.022, 23.45, 0.175, 23.45]}, {"time": 0.2333, "value": 23.45, "curve": [0.3, 23.45, 0.433, -27.61]}, {"time": 0.5, "value": -27.61, "curve": [0.583, -27.61, 0.75, 11.47]}, {"time": 0.8333, "value": 11.47, "curve": [0.908, 11.47, 1.058, -9.26]}, {"time": 1.1333, "value": -9.26, "curve": [1.183, -10.73, 1.229, -11.66]}, {"time": 1.2667, "value": -11.66, "curve": [1.364, -11.66, 1.539, -0.22]}, {"time": 1.6667, "value": 4.35}]}, "Spike3": {"rotate": [{"value": 51.91, "curve": [0.041, -26.49, 0.125, -50.12]}, {"time": 0.1667, "value": -50.12, "curve": [0.25, -50.12, 0.417, 41.16]}, {"time": 0.5, "value": 41.16, "curve": [0.625, 41.16, 0.875, -27.86]}, {"time": 1, "value": -27.86, "curve": [1.167, -27.86, 1.5, 5.22]}, {"time": 1.6667, "value": 5.22}]}, "Spike2": {"rotate": [{"value": 46.67, "curve": [0.024, -27.1, 0.075, -49.34]}, {"time": 0.1, "value": -49.34, "curve": [0.183, -49.34, 0.35, 35.92]}, {"time": 0.4333, "value": 35.92, "curve": [0.558, 35.92, 0.808, -35.22]}, {"time": 0.9333, "value": -35.22, "curve": [1.117, -35.22, 1.483, -0.02]}, {"time": 1.6667, "value": -0.02}]}, "Spike1": {"rotate": [{"value": 15.44, "curve": [0.008, -34.47, 0.025, -49.51]}, {"time": 0.0333, "value": -49.51, "curve": [0.117, -49.51, 0.283, 22.4]}, {"time": 0.3667, "value": 22.4, "curve": [0.492, 22.4, 0.742, -19.03]}, {"time": 0.8667, "value": -19.03, "curve": [1.067, -19.03, 1.467, -2.5]}, {"time": 1.6667, "value": -2.5}]}, "Spike10": {"rotate": [{"value": -14.91, "curve": [0.008, 32.12, 0.025, 46.3]}, {"time": 0.0333, "value": 46.3, "curve": [0.117, 46.3, 0.283, -17.17]}, {"time": 0.3667, "value": -17.17, "curve": [0.492, -17.17, 0.742, 27.49]}, {"time": 0.8667, "value": 27.49, "curve": [1.067, 27.49, 1.467, 5.4]}, {"time": 1.6667, "value": 5.4}]}, "Spike6": {"rotate": [{"value": -53.08, "curve": [0.024, 23.56, 0.075, 46.66]}, {"time": 0.1, "value": 46.66, "curve": [0.183, 46.66, 0.35, -33.81]}, {"time": 0.4333, "value": -33.81, "curve": [0.558, -33.81, 0.808, 26.2]}, {"time": 0.9333, "value": 26.2, "curve": [1.117, 26.2, 1.483, -0.33]}, {"time": 1.6667, "value": -0.33}]}, "Spike7": {"rotate": [{"value": -57.26, "curve": [0.041, 23.08, 0.125, 47.29]}, {"time": 0.1667, "value": 47.29, "curve": [0.25, 47.29, 0.417, -37.99]}, {"time": 0.5, "value": -37.99, "curve": [0.625, -37.99, 0.875, 20.04]}, {"time": 1, "value": 20.04, "curve": [1.167, 20.04, 1.5, -4.52]}, {"time": 1.6667, "value": -4.52}]}, "Spike8": {"rotate": [{"value": 42.87, "curve": [0.022, -21.41, 0.175, -21.41]}, {"time": 0.2333, "value": -21.41, "curve": [0.3, -21.41, 0.433, 26.26]}, {"time": 0.5, "value": 26.26, "curve": [0.583, 26.26, 0.75, -15.65]}, {"time": 0.8333, "value": -15.65, "curve": [0.908, -15.65, 1.058, 7.05]}, {"time": 1.1333, "value": 7.05, "curve": [1.183, 8.22, 1.229, 8.96]}, {"time": 1.2667, "value": 8.96, "curve": [1.364, 8.96, 1.539, -0.17]}, {"time": 1.6667, "value": -3.82}]}, "Spike9": {"rotate": [{"value": 41.81, "curve": [0.025, -22.06, 0.2, -22.06]}, {"time": 0.2667, "value": -22.06, "curve": [0.333, -22.06, 0.467, 24.98]}, {"time": 0.5333, "value": 24.98, "curve": [0.617, 24.98, 0.783, -16.81]}, {"time": 0.8667, "value": -16.81, "curve": [0.942, -16.81, 1.092, 6.25]}, {"time": 1.1667, "value": 6.25, "curve": [1.23, 7.87, 1.287, 8.96]}, {"time": 1.3333, "value": 8.96, "curve": [1.414, 8.96, 1.556, 0.97]}, {"time": 1.6667, "value": -3.02}]}, "Tentacle5": {"rotate": [{"value": 6.03, "curve": [0.024, 8.17, 0.047, 9.39]}, {"time": 0.0667, "value": 9.39, "curve": [0.175, 9.39, 0.392, -49.78]}, {"time": 0.5, "value": -49.78, "curve": [0.622, -49.78, 0.852, -5.1]}, {"time": 1, "value": 5.41, "curve": [1.037, 7.94, 1.07, 9.39]}, {"time": 1.1, "value": 9.39, "curve": [1.242, 9.39, 1.525, -49.78]}, {"time": 1.6667, "value": -49.78}]}, "Tentacle3_Mid": {"rotate": [{"value": -3.27, "curve": [0.13, 21.42, 0.277, 56.4]}, {"time": 0.3667, "value": 56.4, "curve": [0.403, 56.4, 0.449, 48]}, {"time": 0.5, "value": 36.14, "curve": [0.603, 11.69, 0.727, -29.86]}, {"time": 0.8, "value": -29.86, "curve": [0.852, -29.86, 0.924, -18.51]}, {"time": 1, "value": -3.82, "curve": [1.212, 20.88, 1.454, 56.4]}, {"time": 1.6, "value": 56.4, "curve": [1.62, 56.4, 1.642, 54.76]}, {"time": 1.6667, "value": 51.86}]}, "Tentacle3_Btm": {"rotate": [{"value": -19.18, "curve": [0.143, 10.49, 0.328, 73.54]}, {"time": 0.4333, "value": 73.54, "curve": [0.453, 73.54, 0.475, 69.14]}, {"time": 0.5, "value": 61.89, "curve": [0.615, 35.84, 0.778, -37.96]}, {"time": 0.8667, "value": -37.96, "curve": [0.904, -37.96, 0.95, -29.31]}, {"time": 1, "value": -16.08, "curve": [1.224, 14.65, 1.505, 73.54]}, {"time": 1.6667, "value": 73.54}]}, "Tentacle6": {"rotate": [{"value": -8.02, "curve": [0.024, -9.72, 0.047, -10.68]}, {"time": 0.0667, "value": -10.68, "curve": [0.175, -10.68, 0.392, 36.14]}, {"time": 0.5, "value": 36.14, "curve": [0.622, 36.14, 0.852, 0.78]}, {"time": 1, "value": -7.54, "curve": [1.037, -9.53, 1.07, -10.68]}, {"time": 1.1, "value": -10.68, "curve": [1.242, -10.68, 1.525, 36.14]}, {"time": 1.6667, "value": 36.14}]}, "Tentacle3_Mid2": {"rotate": [{"value": -1.59, "curve": [0.13, 24.91, 0.277, 62.45]}, {"time": 0.3667, "value": 62.45, "curve": [0.403, 62.45, 0.449, 53.43]}, {"time": 0.5, "value": 40.71, "curve": [0.603, 14.47, 0.727, -30.13]}, {"time": 0.8, "value": -30.13, "curve": [0.852, -30.13, 0.924, -17.95]}, {"time": 1, "value": -2.18, "curve": [1.201, 24.33, 1.429, 62.45]}, {"time": 1.5667, "value": 62.45, "curve": [1.596, 62.45, 1.629, 58.81]}, {"time": 1.6667, "value": 52.81}]}, "Tentacle3_Btm2": {"rotate": [{"value": -23.87, "curve": [0.143, 3.75, 0.328, 62.45]}, {"time": 0.4333, "value": 62.45, "curve": [0.453, 62.45, 0.475, 58.35]}, {"time": 0.5, "value": 51.6, "curve": [0.615, 27.35, 0.778, -41.36]}, {"time": 0.8667, "value": -41.36, "curve": [0.904, -41.36, 0.95, -33.3]}, {"time": 1, "value": -20.98, "curve": [1.213, 7.62, 1.48, 62.45]}, {"time": 1.6333, "value": 62.45, "curve": [1.644, 62.45, 1.655, 61.9]}, {"time": 1.6667, "value": 60.88}]}, "Tentacle2_Btm2": {"rotate": [{"value": -38.29, "curve": [0.152, -7.3, 0.353, 62.45]}, {"time": 0.4667, "value": 62.45, "curve": [0.477, 62.45, 0.488, 60]}, {"time": 0.5, "value": 55.68, "curve": [0.608, 34.28, 0.777, -56.67]}, {"time": 0.8667, "value": -56.67, "curve": [0.904, -56.67, 0.95, -49.72]}, {"time": 1, "value": -38.92, "curve": [1.195, -8.2, 1.454, 62.45]}, {"time": 1.6, "value": 62.45, "curve": [1.62, 62.45, 1.642, 60]}, {"time": 1.6667, "value": 55.68}]}, "Tentacle2_Mid2": {"rotate": [{"value": 5.37, "curve": [0.121, 31.15, 0.251, 62.45]}, {"time": 0.3333, "value": 62.45, "curve": [0.376, 62.45, 0.437, 46.14]}, {"time": 0.5, "value": 26.96, "curve": [0.597, 1.18, 0.701, -30.13]}, {"time": 0.7667, "value": -30.13, "curve": [0.827, -30.13, 0.911, -12.53]}, {"time": 1, "value": 8.18, "curve": [1.159, 33.42, 1.327, 62.45]}, {"time": 1.4333, "value": 62.45, "curve": [1.493, 62.45, 1.578, 46.14]}, {"time": 1.6667, "value": 26.96}]}, "Tentacle4": {"rotate": [{"value": -8.58, "curve": [0.024, -9.92, 0.047, -10.68]}, {"time": 0.0667, "value": -10.68, "curve": [0.175, -10.68, 0.392, 26.28]}, {"time": 0.5, "value": 26.28, "curve": [0.622, 26.28, 0.852, -1.63]}, {"time": 1, "value": -8.2, "curve": [1.037, -9.77, 1.07, -10.68]}, {"time": 1.1, "value": -10.68, "curve": [1.242, -10.68, 1.525, 26.28]}, {"time": 1.6667, "value": 26.28}]}, "Tentacle2_Btm": {"rotate": [{"value": -46.79, "curve": [0.152, -15.83, 0.353, 53.84]}, {"time": 0.4667, "value": 53.84, "curve": [0.477, 53.84, 0.488, 51.4]}, {"time": 0.5, "value": 47.08, "curve": [0.608, 25.7, 0.777, -65.15]}, {"time": 0.8667, "value": -65.15, "curve": [0.904, -65.15, 0.95, -58.21]}, {"time": 1, "value": -47.42, "curve": [1.195, -16.73, 1.454, 53.84]}, {"time": 1.6, "value": 53.84, "curve": [1.62, 53.84, 1.642, 51.4]}, {"time": 1.6667, "value": 47.08}]}, "Tentacle2_Mid": {"rotate": [{"value": -3.22, "curve": [0.121, 20.8, 0.251, 49.96]}, {"time": 0.3333, "value": 49.96, "curve": [0.376, 49.96, 0.437, 34.77]}, {"time": 0.5, "value": 16.89, "curve": [0.597, -7.13, 0.701, -36.3]}, {"time": 0.7667, "value": -36.3, "curve": [0.827, -36.3, 0.911, -19.9]}, {"time": 1, "value": -0.6, "curve": [1.159, 22.92, 1.327, 49.96]}, {"time": 1.4333, "value": 49.96, "curve": [1.493, 49.96, 1.578, 34.77]}, {"time": 1.6667, "value": 16.89}]}, "Tentacle3": {"rotate": [{"value": 4.21, "curve": [0.024, 5.78, 0.047, 6.68]}, {"time": 0.0667, "value": 6.68, "curve": [0.175, 6.68, 0.392, -36.75]}, {"time": 0.5, "value": -36.75, "curve": [0.622, -36.75, 0.852, -3.96]}, {"time": 1, "value": 3.76, "curve": [1.037, 5.61, 1.07, 6.68]}, {"time": 1.1, "value": 6.68, "curve": [1.242, 6.68, 1.525, -36.75]}, {"time": 1.6667, "value": -36.75}]}, "DangleHandle": {"rotate": [{"value": -15.4, "curve": [0.124, -4.37, 0.278, 16.3]}, {"time": 0.3667, "value": 16.3, "curve": [0.403, 16.3, 0.449, 12.3]}, {"time": 0.5, "value": 6.7, "curve": [0.532, 3.66, 0.566, 0.04]}, {"time": 0.6, "value": -3.58, "curve": [0.7, -13.51, 0.8, -23.45]}, {"time": 0.8667, "value": -23.45, "curve": [0.903, -23.45, 0.949, -19.55]}, {"time": 1, "value": -13.82, "curve": [1.161, -2.53, 1.353, 16.3]}, {"time": 1.4667, "value": 16.3, "curve": [1.52, 16.3, 1.59, 12.3]}, {"time": 1.6667, "value": 6.7}], "translate": [{"x": -8.18, "y": 2.62, "curve": [0.099, 16.95, 0.201, 43.87, 0.099, 3.65, 0.201, 4.74]}, {"time": 0.2667, "x": 43.87, "y": 4.74, "curve": [0.325, 43.87, 0.413, 19.95, 0.325, 4.74, 0.413, 3.77]}, {"time": 0.5, "x": -3.96, "y": 2.8, "curve": [0.6, -27.88, 0.7, -51.8, 0.6, 1.82, 0.7, 0.85]}, {"time": 0.7667, "x": -51.8, "y": 0.85, "curve": [0.826, -51.8, 0.912, -28.65, 0.826, 0.85, 0.912, 1.79]}, {"time": 1, "x": -3.96, "y": 2.8, "curve": [1.125, 19.95, 1.25, 43.87, 1.125, 3.77, 1.25, 4.74]}, {"time": 1.3333, "x": 43.87, "y": 4.74, "curve": [1.417, 43.87, 1.542, 19.95, 1.417, 4.74, 1.542, 3.77]}, {"time": 1.6667, "x": -3.96, "y": 2.8}], "scale": [{"y": 0.313, "curve": [0.125, 1, 0.375, 1, 0.125, 0.313, 0.375, 1.723]}, {"time": 0.5, "y": 1.723, "curve": [0.625, 1, 0.875, 1, 0.625, 1.723, 0.875, 0.313]}, {"time": 1, "y": 0.313, "curve": [1.167, 1, 1.5, 1, 1.167, 0.313, 1.5, 1.723]}, {"time": 1.6667, "y": 1.723}]}, "Tentacles": {"scale": [{"x": 1.199}, {"time": 0.5, "x": 0.699}, {"time": 1, "x": 0.851}, {"time": 1.6667, "x": 0.699}]}, "Mouth": {"translate": [{"x": -0.72, "y": -42.85, "curve": "stepped"}, {"time": 0.0667, "x": -0.72, "y": -42.85, "curve": [0.083, -0.72, 0.117, 0.39, 0.083, -42.85, 0.117, 20.48]}, {"time": 0.1333, "x": 0.39, "y": 20.48, "curve": [0.15, 0.39, 0.183, 0.11, 0.15, 20.48, 0.183, 0.69]}, {"time": 0.2, "x": 0.11, "y": 0.69, "curve": [0.217, 0.11, 0.25, 0.39, 0.217, 0.69, 0.25, 20.48]}, {"time": 0.2667, "x": 0.39, "y": 20.48, "curve": [0.283, 0.39, 0.317, 0.11, 0.283, 20.48, 0.317, 0.69]}, {"time": 0.3333, "x": 0.11, "y": 0.69, "curve": [0.35, 0.11, 0.383, 0.39, 0.35, 0.69, 0.383, 20.48]}, {"time": 0.4, "x": 0.39, "y": 20.48, "curve": [0.417, 0.39, 0.45, 0.11, 0.417, 20.48, 0.45, 0.69]}, {"time": 0.4667, "x": 0.11, "y": 0.69, "curve": [0.516, 0.08, 0.779, 0.05, 0.516, -15.06, 0.779, -29.86]}, {"time": 0.9333, "x": 0.05, "y": -33.81, "curve": [0.958, -0.19, 0.981, -0.33, 0.958, -34.53, 0.981, -34.96]}, {"time": 1, "x": -0.37, "y": -35.08, "curve": [1.167, -0.37, 1.5, 0, 1.167, -35.08, 1.5, -10.97]}, {"time": 1.6667, "y": -10.97}]}, "Mouth8": {"rotate": [{"value": -32.57, "curve": [0.025, -38.95, 0.049, -43.41]}, {"time": 0.0667, "value": -43.41, "curve": [0.133, -43.41, 0.267, 4.89]}, {"time": 0.3333, "value": 4.89, "curve": [0.374, 4.89, 0.443, -17.99]}, {"time": 0.5, "value": -31.71, "curve": [0.525, -38.54, 0.549, -43.41]}, {"time": 0.5667, "value": -43.41, "curve": [0.633, -43.41, 0.767, 4.89]}, {"time": 0.8333, "value": 4.89, "curve": [0.874, 4.89, 0.943, -18.4]}, {"time": 1, "value": -31.71, "curve": [1.038, -38.54, 1.073, -43.41]}, {"time": 1.1, "value": -43.41, "curve": [1.183, -43.41, 1.35, 4.89]}, {"time": 1.4333, "value": 4.89, "curve": [1.49, 4.89, 1.586, -17.99]}, {"time": 1.6667, "value": -31.71}]}, "Mouth5": {"rotate": [{"value": 20.26, "curve": [0.061, 15.29, 0.151, -2.12]}, {"time": 0.2, "value": -2.12, "curve": [0.267, -2.12, 0.4, 22.13]}, {"time": 0.4667, "value": 22.13, "curve": [0.476, 22.13, 0.488, 20.92]}, {"time": 0.5, "value": 18.98, "curve": [0.564, 12.95, 0.651, -2.12]}, {"time": 0.7, "value": -2.12, "curve": [0.767, -2.12, 0.9, 22.13]}, {"time": 0.9667, "value": 22.13, "curve": [0.977, 22.13, 0.988, 20.97]}, {"time": 1, "value": 18.98, "curve": [1.085, 12.95, 1.202, -2.12]}, {"time": 1.2667, "value": -2.12, "curve": [1.35, -2.12, 1.517, 22.13]}, {"time": 1.6, "value": 22.13, "curve": [1.619, 22.13, 1.642, 20.92]}, {"time": 1.6667, "value": 18.98}]}, "Mouth4": {"rotate": [{"value": 7.72, "curve": [0.038, 2.47, 0.074, -2.12]}, {"time": 0.1, "value": -2.12, "curve": [0.167, -2.12, 0.3, 22.13]}, {"time": 0.3667, "value": 22.13, "curve": [0.399, 22.13, 0.452, 13.62]}, {"time": 0.5, "value": 6.8, "curve": [0.538, 1.94, 0.574, -2.12]}, {"time": 0.6, "value": -2.12, "curve": [0.667, -2.12, 0.8, 22.13]}, {"time": 0.8667, "value": 22.13, "curve": [0.899, 22.13, 0.951, 13.88]}, {"time": 1, "value": 6.8, "curve": [1.051, 1.94, 1.099, -2.12]}, {"time": 1.1333, "value": -2.12, "curve": [1.217, -2.12, 1.383, 22.13]}, {"time": 1.4667, "value": 22.13, "curve": [1.516, 22.13, 1.594, 13.62]}, {"time": 1.6667, "value": 6.8}]}, "Mouth7": {"rotate": [{"value": -40.38, "curve": [0.069, -31.32, 0.176, 4.89]}, {"time": 0.2333, "value": 4.89, "curve": [0.292, 4.89, 0.408, -43.41]}, {"time": 0.4667, "value": -43.41, "curve": [0.477, -43.41, 0.488, -42.68]}, {"time": 0.5, "value": -41.36, "curve": [0.567, -33.65, 0.676, 4.89]}, {"time": 0.7333, "value": 4.89, "curve": [0.792, 4.89, 0.908, -43.41]}, {"time": 0.9667, "value": -43.41, "curve": [0.977, -43.41, 0.988, -42.66]}, {"time": 1, "value": -41.36, "curve": [1.087, -33.65, 1.226, 4.89]}, {"time": 1.3, "value": 4.89, "curve": [1.383, 4.89, 1.55, -43.41]}, {"time": 1.6333, "value": -43.41, "curve": [1.643, -43.41, 1.655, -42.68]}, {"time": 1.6667, "value": -41.36}]}, "Mouth3": {"rotate": [{"value": -2.12, "curve": [0.067, -2.12, 0.2, 22.13]}, {"time": 0.2667, "value": 22.13, "curve": [0.325, 22.13, 0.442, -2.12]}, {"time": 0.5, "value": -2.12, "curve": [0.567, -2.12, 0.7, 22.13]}, {"time": 0.7667, "value": 22.13, "curve": [0.825, 22.13, 0.942, -2.12]}, {"time": 1, "value": -2.12, "curve": [1.083, -2.12, 1.25, 22.13]}, {"time": 1.3333, "value": 22.13, "curve": [1.417, 22.13, 1.583, -2.12]}, {"time": 1.6667, "value": -2.12}]}, "Mouth6": {"rotate": [{"value": -27.19, "curve": [0.06, -13.44, 0.126, 4.89]}, {"time": 0.1667, "value": 4.89, "curve": [0.225, 4.89, 0.342, -43.41]}, {"time": 0.4, "value": -43.41, "curve": [0.426, -43.41, 0.462, -35.33]}, {"time": 0.5, "value": -25.64, "curve": [0.56, -12.07, 0.626, 4.89]}, {"time": 0.6667, "value": 4.89, "curve": [0.725, 4.89, 0.842, -43.41]}, {"time": 0.9, "value": -43.41, "curve": [0.926, -43.41, 0.962, -35.51]}, {"time": 1, "value": -25.64, "curve": [1.073, -12.07, 1.151, 4.89]}, {"time": 1.2, "value": 4.89, "curve": [1.283, 4.89, 1.45, -43.41]}, {"time": 1.5333, "value": -43.41, "curve": [1.568, -43.41, 1.616, -35.33]}, {"time": 1.6667, "value": -25.64}]}, "Mouth2": {"scale": [{}, {"time": 0.5, "x": 1.013, "y": 1.013}, {"time": 1}]}, "Head": {"scale": [{"time": 0.1, "curve": [0.133, 1, 0.2, 1.361, 0.133, 1, 0.2, 0.818]}, {"time": 0.2333, "x": 1.361, "y": 0.818, "curve": [0.308, 1.361, 0.458, 0.874, 0.308, 0.818, 0.458, 1.183]}, {"time": 0.5333, "x": 0.874, "y": 1.183, "curve": [0.617, 0.874, 0.783, 1, 0.617, 1.183, 0.783, 1]}, {"time": 0.8667}]}, "Tentacle3_Btm3": {"rotate": [{"value": -19.18, "curve": [0.143, 10.49, 0.328, 73.54]}, {"time": 0.4333, "value": 73.54, "curve": [0.453, 73.54, 0.475, 69.14]}, {"time": 0.5, "value": 61.89, "curve": [0.615, 35.84, 0.778, -37.96]}, {"time": 0.8667, "value": -37.96, "curve": [0.904, -37.96, 0.95, -29.31]}, {"time": 1, "value": -16.08, "curve": [1.19, 14.65, 1.429, 73.54]}, {"time": 1.5667, "value": 73.54, "curve": [1.596, 73.54, 1.629, 69.14]}, {"time": 1.6667, "value": 61.89}]}, "Tentacle3_Mid3": {"rotate": [{"value": -3.27, "curve": [0.13, 21.42, 0.277, 56.4]}, {"time": 0.3667, "value": 56.4, "curve": [0.403, 56.4, 0.449, 48]}, {"time": 0.5, "value": 36.14, "curve": [0.603, 11.69, 0.727, -29.86]}, {"time": 0.8, "value": -29.86, "curve": [0.852, -29.86, 0.924, -18.51]}, {"time": 1, "value": -3.82, "curve": [1.177, 20.88, 1.378, 56.4]}, {"time": 1.5, "value": 56.4, "curve": [1.545, 56.4, 1.603, 48]}, {"time": 1.6667, "value": 36.14}]}, "Tentacle1": {"rotate": [{"value": 6.03, "curve": [0.024, 8.17, 0.047, 9.39]}, {"time": 0.0667, "value": 9.39, "curve": [0.175, 9.39, 0.392, -49.78]}, {"time": 0.5, "value": -49.78, "curve": [0.622, -49.78, 0.852, -5.1]}, {"time": 1, "value": 5.41, "curve": [1.037, 7.94, 1.07, 9.39]}, {"time": 1.1, "value": 9.39, "curve": [1.242, 9.39, 1.525, -49.78]}, {"time": 1.6667, "value": -49.78}]}, "Tentacle3_Btm4": {"rotate": [{"value": -23.87, "curve": [0.143, 3.75, 0.328, 62.45]}, {"time": 0.4333, "value": 62.45, "curve": [0.453, 62.45, 0.475, 58.35]}, {"time": 0.5, "value": 51.6, "curve": [0.615, 27.35, 0.778, -41.36]}, {"time": 0.8667, "value": -41.36, "curve": [0.904, -41.36, 0.95, -33.3]}, {"time": 1, "value": -20.98, "curve": [1.19, 7.62, 1.429, 62.45]}, {"time": 1.5667, "value": 62.45, "curve": [1.596, 62.45, 1.629, 58.35]}, {"time": 1.6667, "value": 51.6}]}, "Tentacle3_Mid4": {"rotate": [{"value": -1.59, "curve": [0.13, 24.91, 0.277, 62.45]}, {"time": 0.3667, "value": 62.45, "curve": [0.403, 62.45, 0.449, 53.43]}, {"time": 0.5, "value": 40.71, "curve": [0.603, 14.47, 0.727, -30.13]}, {"time": 0.8, "value": -30.13, "curve": [0.852, -30.13, 0.924, -17.95]}, {"time": 1, "value": -2.18, "curve": [1.177, 24.33, 1.378, 62.45]}, {"time": 1.5, "value": 62.45, "curve": [1.545, 62.45, 1.603, 53.43]}, {"time": 1.6667, "value": 40.71}]}, "Tentacle2": {"rotate": [{"value": -8.02, "curve": [0.024, -9.72, 0.047, -10.68]}, {"time": 0.0667, "value": -10.68, "curve": [0.175, -10.68, 0.392, 36.14]}, {"time": 0.5, "value": 36.14, "curve": [0.622, 36.14, 0.852, 0.78]}, {"time": 1, "value": -7.54, "curve": [1.037, -9.53, 1.07, -10.68]}, {"time": 1.1, "value": -10.68, "curve": [1.242, -10.68, 1.525, 36.14]}, {"time": 1.6667, "value": 36.14}]}}}, "attack-spikes": {"slots": {"images/SpikerBossSpike1": {"attachment": [{"name": "images/SpikerBossSpike2"}]}, "images/SpikerBossSpike2": {"attachment": [{"name": "images/SpikerBossSpike1"}]}, "images/SpikerBossSpike3": {"attachment": [{"name": "images/SpikerBossSpike1"}]}, "images/SpikerBossSpike4": {"attachment": [{"name": "images/SpikerBossSpike1"}]}, "images/SpikerBossSpike5": {"attachment": [{"name": "images/SpikerBossSpike2"}]}, "images/SpikerBossSpike6": {"attachment": [{"name": "images/SpikerBossSpike2"}]}, "images/SpikerBossSpike7": {"attachment": [{"name": "images/SpikerBossSpike1"}]}, "images/SpikerBossSpike8": {"attachment": [{"name": "images/SpikerBossSpike1"}]}}, "bones": {"SpikerSpike1": {"scale": [{"x": 0.076, "curve": [0.006, 1.325, 0.025, 1.552, 0.006, 1, 0.025, 1]}, {"time": 0.0333, "x": 1.552, "curve": [0.05, 1.552, 0.083, 0.793, 0.05, 1, 0.083, 1]}, {"time": 0.1, "x": 0.793, "curve": [0.133, 0.793, 0.2, 1.223, 0.133, 1, 0.2, 1]}, {"time": 0.2333, "x": 1.223}, {"time": 0.4, "curve": "stepped"}, {"time": 1.3, "curve": [1.35, 1, 1.45, 1.13, 1.35, 1, 1.45, 1]}, {"time": 1.5, "x": 1.13, "curve": [1.542, 1.13, 1.656, 0.594, 1.542, 1, 1.656, 1]}, {"time": 1.6667, "x": 0.122}]}, "SpikerSpike2": {"scale": [{"x": 0.076, "curve": [0.006, 1.325, 0.025, 1.552, 0.006, 1, 0.025, 1]}, {"time": 0.0333, "x": 1.552, "curve": [0.05, 1.552, 0.083, 0.793, 0.05, 1, 0.083, 1]}, {"time": 0.1, "x": 0.793, "curve": [0.133, 0.793, 0.2, 1.223, 0.133, 1, 0.2, 1]}, {"time": 0.2333, "x": 1.223}, {"time": 0.4, "curve": "stepped"}, {"time": 1.3, "curve": [1.35, 1, 1.45, 1.13, 1.35, 1, 1.45, 1]}, {"time": 1.5, "x": 1.13, "curve": [1.542, 1.13, 1.656, 0.594, 1.542, 1, 1.656, 1]}, {"time": 1.6667, "x": 0.122}]}, "SpikerSpike3": {"scale": [{"x": 0.076, "curve": [0.006, 1.325, 0.025, 1.552, 0.006, 1, 0.025, 1]}, {"time": 0.0333, "x": 1.552, "curve": [0.05, 1.552, 0.083, 0.793, 0.05, 1, 0.083, 1]}, {"time": 0.1, "x": 0.793, "curve": [0.133, 0.793, 0.2, 1.223, 0.133, 1, 0.2, 1]}, {"time": 0.2333, "x": 1.223}, {"time": 0.4, "curve": "stepped"}, {"time": 1.3, "curve": [1.35, 1, 1.45, 1.13, 1.35, 1, 1.45, 1]}, {"time": 1.5, "x": 1.13, "curve": [1.542, 1.13, 1.656, 0.594, 1.542, 1, 1.656, 1]}, {"time": 1.6667, "x": 0.122}]}, "SpikerSpike4": {"scale": [{"x": 0.076, "curve": [0.006, 1.325, 0.025, 1.552, 0.006, 1, 0.025, 1]}, {"time": 0.0333, "x": 1.552, "curve": [0.05, 1.552, 0.083, 0.793, 0.05, 1, 0.083, 1]}, {"time": 0.1, "x": 0.793, "curve": [0.133, 0.793, 0.2, 1.223, 0.133, 1, 0.2, 1]}, {"time": 0.2333, "x": 1.223}, {"time": 0.4, "curve": "stepped"}, {"time": 1.3, "curve": [1.35, 1, 1.45, 1.13, 1.35, 1, 1.45, 1]}, {"time": 1.5, "x": 1.13, "curve": [1.542, 1.13, 1.656, 0.594, 1.542, 1, 1.656, 1]}, {"time": 1.6667, "x": 0.122}]}, "SpikerSpike5": {"scale": [{"x": 0.076, "curve": [0.006, 1.325, 0.025, 1.552, 0.006, 1, 0.025, 1]}, {"time": 0.0333, "x": 1.552, "curve": [0.05, 1.552, 0.083, 0.793, 0.05, 1, 0.083, 1]}, {"time": 0.1, "x": 0.793, "curve": [0.133, 0.793, 0.2, 1.223, 0.133, 1, 0.2, 1]}, {"time": 0.2333, "x": 1.223}, {"time": 0.4, "curve": "stepped"}, {"time": 1.3, "curve": [1.35, 1, 1.45, 1.13, 1.35, 1, 1.45, 1]}, {"time": 1.5, "x": 1.13, "curve": [1.542, 1.13, 1.656, 0.594, 1.542, 1, 1.656, 1]}, {"time": 1.6667, "x": 0.122}]}, "SpikerSpike6": {"scale": [{"x": 0.076, "curve": [0.006, 1.325, 0.025, 1.552, 0.006, 1, 0.025, 1]}, {"time": 0.0333, "x": 1.552, "curve": [0.05, 1.552, 0.083, 0.793, 0.05, 1, 0.083, 1]}, {"time": 0.1, "x": 0.793, "curve": [0.133, 0.793, 0.2, 1.223, 0.133, 1, 0.2, 1]}, {"time": 0.2333, "x": 1.223}, {"time": 0.4, "curve": "stepped"}, {"time": 1.3, "curve": [1.35, 1, 1.45, 1.13, 1.35, 1, 1.45, 1]}, {"time": 1.5, "x": 1.13, "curve": [1.542, 1.13, 1.656, 0.594, 1.542, 1, 1.656, 1]}, {"time": 1.6667, "x": 0.122}]}, "SpikerSpike7": {"scale": [{"x": 0.076, "curve": [0.006, 1.325, 0.025, 1.552, 0.006, 1, 0.025, 1]}, {"time": 0.0333, "x": 1.552, "curve": [0.05, 1.552, 0.083, 0.793, 0.05, 1, 0.083, 1]}, {"time": 0.1, "x": 0.793, "curve": [0.133, 0.793, 0.2, 1.223, 0.133, 1, 0.2, 1]}, {"time": 0.2333, "x": 1.223}, {"time": 0.4, "curve": "stepped"}, {"time": 1.3, "curve": [1.35, 1, 1.45, 1.13, 1.35, 1, 1.45, 1]}, {"time": 1.5, "x": 1.13, "curve": [1.542, 1.13, 1.656, 0.594, 1.542, 1, 1.656, 1]}, {"time": 1.6667, "x": 0.122}]}, "SpikerSpike8": {"scale": [{"x": 0.076, "curve": [0.006, 1.325, 0.025, 1.552, 0.006, 1, 0.025, 1]}, {"time": 0.0333, "x": 1.552, "curve": [0.05, 1.552, 0.083, 0.793, 0.05, 1, 0.083, 1]}, {"time": 0.1, "x": 0.793, "curve": [0.133, 0.793, 0.2, 1.223, 0.133, 1, 0.2, 1]}, {"time": 0.2333, "x": 1.223}, {"time": 0.4, "curve": "stepped"}, {"time": 1.3, "curve": [1.35, 1, 1.45, 1.13, 1.35, 1, 1.45, 1]}, {"time": 1.5, "x": 1.13, "curve": [1.542, 1.13, 1.656, 0.594, 1.542, 1, 1.656, 1]}, {"time": 1.6667, "x": 0.122}]}, "FACE": {"rotate": [{"time": 0.0333, "curve": [0.053, -10.05, 0.133, -15.5]}, {"time": 0.1667, "value": -15.5, "curve": [0.375, -15.5, 0.792, 0]}, {"time": 1}], "translate": [{"x": 0.84, "y": 4.16, "curve": [0.002, -30.09, 0.018, -44.45, 0.002, 3.54, 0.018, 3.25]}, {"time": 0.0333, "x": -45.76, "y": 3.22, "curve": [0.067, -45.76, 0.133, 82.73, 0.067, 3.22, 0.133, 2.07]}, {"time": 0.1667, "x": 82.73, "y": 2.07, "curve": [0.857, 82.73, 0.992, -21.84, 0.857, 2.07, 0.992, 4.16]}, {"time": 1.2667, "x": -21.84, "y": 4.16, "curve": [1.464, -21.84, 1.552, -5.93, 1.464, 4.16, 1.552, 4.16]}, {"time": 1.6667, "x": 7.05, "y": 4.16}], "scale": [{"curve": [0.008, 1, 0.025, 1.219, 0.008, 1, 0.025, 1.219]}, {"time": 0.0333, "x": 1.219, "y": 1.219, "curve": [0.058, 1.219, 0.108, 0.712, 0.058, 1.219, 0.108, 0.712]}, {"time": 0.1333, "x": 0.712, "y": 0.712, "curve": [0.183, 0.712, 0.283, 1.219, 0.183, 0.712, 0.283, 1.219]}, {"time": 0.3333, "x": 1.219, "y": 1.219, "curve": [0.417, 1.219, 0.583, 1, 0.417, 1.219, 0.583, 1]}, {"time": 0.6667}]}, "MAIN": {"translate": [{"y": -33.8, "curve": [0, 0, 0.167, 0, 0, -1.14, 0.167, 16.22]}, {"time": 0.3333, "y": 17.53, "curve": [0.643, 0, 0.667, 0, 0.643, 17.53, 0.667, -89.61]}, {"time": 1, "y": -92.41, "curve": [1.31, 0, 1.333, 0, 1.31, -92.41, 1.333, 14.73]}, {"time": 1.6667, "y": 17.53}], "scale": [{"x": 1.509, "y": 0.663, "curve": [0, 0.959, 0.05, 0.76, 0, 1.189, 0.05, 1.379]}, {"time": 0.0667, "x": 0.76, "y": 1.379, "curve": [0.108, 0.76, 0.192, 1.267, 0.108, 1.379, 0.192, 0.835]}, {"time": 0.2333, "x": 1.267, "y": 0.835, "curve": [0.292, 1.267, 0.408, 0.85, 0.292, 0.835, 0.408, 1.157]}, {"time": 0.4667, "x": 0.85, "y": 1.157, "curve": [0.6, 0.85, 0.867, 1.038, 0.6, 1.157, 0.867, 0.955]}, {"time": 1, "x": 1.038, "y": 0.955, "curve": [1.125, 1.038, 1.25, 1.038, 1.125, 0.959, 1.25, 0.962]}, {"time": 1.3333, "x": 1.038, "y": 0.962, "curve": [1.417, 1.038, 1.542, 1.019, 1.417, 0.962, 1.542, 0.981]}, {"time": 1.6667}]}, "Spike5": {"rotate": [{"value": -43.86, "curve": [0.016, 24.37, 0.125, 24.37]}, {"time": 0.1667, "value": 24.37, "curve": [0.217, 24.37, 0.317, -26.01]}, {"time": 0.3667, "value": -26.01, "curve": [0.425, -26.01, 0.542, 12.93]}, {"time": 0.6, "value": 12.93, "curve": [0.742, 12.93, 1.025, -8.26]}, {"time": 1.1667, "value": -8.26, "curve": [1.23, -10.3, 1.287, -11.66]}, {"time": 1.3333, "value": -11.66, "curve": [1.414, -11.66, 1.556, -1.65]}, {"time": 1.6667, "value": 3.35}]}, "Spike4": {"rotate": [{"value": -45.19, "curve": [0.016, 23.45, 0.125, 23.45]}, {"time": 0.1667, "value": 23.45, "curve": [0.208, 23.45, 0.292, -27.61]}, {"time": 0.3333, "value": -27.61, "curve": [0.392, -27.61, 0.508, 11.47]}, {"time": 0.5667, "value": 11.47, "curve": [0.708, 11.47, 0.992, -9.26]}, {"time": 1.1333, "value": -9.26, "curve": [1.183, -10.73, 1.229, -11.66]}, {"time": 1.2667, "value": -11.66, "curve": [1.364, -11.66, 1.539, -0.22]}, {"time": 1.6667, "value": 4.35}]}, "Spike3": {"rotate": [{"value": -46.31, "curve": [0.013, 18.03, 0.1, 18.03]}, {"time": 0.1333, "value": 18.03, "curve": [0.183, 18.03, 0.283, -27.12]}, {"time": 0.3333, "value": -27.12, "curve": [0.383, -27.12, 0.483, 11.11]}, {"time": 0.5333, "value": 11.11, "curve": [0.675, 11.11, 0.958, -10.13]}, {"time": 1.1, "value": -10.13, "curve": [1.243, -6.23, 1.453, 6.75]}, {"time": 1.5667, "value": 6.75, "curve": [1.596, 6.75, 1.63, 6.18]}, {"time": 1.6667, "value": 5.22}]}, "Spike2": {"rotate": [{"value": -40.82, "curve": [0.01, 25.68, 0.075, 25.68]}, {"time": 0.1, "value": 25.68, "curve": [0.15, 25.68, 0.25, -21.95]}, {"time": 0.3, "value": -21.95, "curve": [0.358, -21.95, 0.475, 16.45]}, {"time": 0.5333, "value": 16.45, "curve": [0.667, 16.45, 0.933, -4.89]}, {"time": 1.0667, "value": -4.89, "curve": [1.188, 0.28, 1.318, 6.75]}, {"time": 1.4, "value": 6.75, "curve": [1.469, 6.75, 1.565, 3.67]}, {"time": 1.6667, "value": -0.02}]}, "Spike1": {"rotate": [{"value": -39.2, "curve": [0.006, 27.66, 0.05, 27.66]}, {"time": 0.0667, "value": 27.66, "curve": [0.108, 27.66, 0.192, -20.49]}, {"time": 0.2333, "value": -20.49, "curve": [0.292, -20.49, 0.408, 17.99]}, {"time": 0.4667, "value": 17.99, "curve": [0.6, 17.99, 0.867, -2.45]}, {"time": 1, "value": -2.45, "curve": [1.125, 2.15, 1.25, 6.75]}, {"time": 1.3333, "value": 6.75, "curve": [1.417, 6.75, 1.542, 2.12]}, {"time": 1.6667, "value": -2.5}]}, "Spike10": {"rotate": [{"value": 34.26, "curve": [0.006, -29.25, 0.05, -29.25]}, {"time": 0.0667, "value": -29.25, "curve": [0.108, -29.25, 0.192, 17.23]}, {"time": 0.2333, "value": 17.23, "curve": [0.292, 17.23, 0.408, -24.45]}, {"time": 0.4667, "value": -24.45, "curve": [0.6, -24.45, 0.867, -2.18]}, {"time": 1, "value": -2.18, "curve": [1.076, -4.25, 1.146, -5.73]}, {"time": 1.2, "value": -5.73, "curve": [1.313, -5.73, 1.506, 1.22]}, {"time": 1.6667, "value": 5.4}]}, "Spike6": {"rotate": [{"value": 39.38, "curve": [0.01, -22.77, 0.075, -22.77]}, {"time": 0.1, "value": -22.77, "curve": [0.15, -22.77, 0.25, 21.76]}, {"time": 0.3, "value": 21.76, "curve": [0.358, 21.76, 0.475, -19.61]}, {"time": 0.5333, "value": -19.61, "curve": [0.667, -19.61, 0.933, 3.55]}, {"time": 1.0667, "value": 3.55, "curve": [1.188, -0.57, 1.318, -5.73]}, {"time": 1.4, "value": -5.73, "curve": [1.469, -5.73, 1.565, -3.28]}, {"time": 1.6667, "value": -0.33}]}, "Spike7": {"rotate": [{"value": 43.76, "curve": [0.013, -16.22, 0.1, -16.22]}, {"time": 0.1333, "value": -16.22, "curve": [0.183, -16.22, 0.283, 26]}, {"time": 0.3333, "value": 26, "curve": [0.383, 26, 0.483, -15.29]}, {"time": 0.5333, "value": -15.29, "curve": [0.675, -15.29, 0.958, 7.74]}, {"time": 1.1, "value": 7.74, "curve": [1.243, 4.63, 1.453, -5.73]}, {"time": 1.5667, "value": -5.73, "curve": [1.596, -5.73, 1.63, -5.28]}, {"time": 1.6667, "value": -4.52}]}, "Spike8": {"rotate": [{"value": 42.87, "curve": [0.016, -21.41, 0.125, -21.41]}, {"time": 0.1667, "value": -21.41, "curve": [0.208, -21.41, 0.292, 26.26]}, {"time": 0.3333, "value": 26.26, "curve": [0.392, 26.26, 0.508, -15.65]}, {"time": 0.5667, "value": -15.65, "curve": [0.708, -15.65, 0.992, 7.05]}, {"time": 1.1333, "value": 7.05, "curve": [1.183, 8.22, 1.229, 8.96]}, {"time": 1.2667, "value": 8.96, "curve": [1.364, 8.96, 1.539, -0.17]}, {"time": 1.6667, "value": -3.82}]}, "Spike9": {"rotate": [{"value": 41.81, "curve": [0.016, -22.06, 0.125, -22.06]}, {"time": 0.1667, "value": -22.06, "curve": [0.217, -22.06, 0.317, 24.98]}, {"time": 0.3667, "value": 24.98, "curve": [0.425, 24.98, 0.542, -16.81]}, {"time": 0.6, "value": -16.81, "curve": [0.742, -16.81, 1.025, 6.25]}, {"time": 1.1667, "value": 6.25, "curve": [1.23, 7.87, 1.287, 8.96]}, {"time": 1.3333, "value": 8.96, "curve": [1.414, 8.96, 1.556, 0.97]}, {"time": 1.6667, "value": -3.02}]}, "Tentacle5": {"rotate": [{"value": 6.03, "curve": [0.012, 8.17, 0.023, 9.39]}, {"time": 0.0333, "value": 9.39, "curve": [0.108, 9.39, 0.258, -49.78]}, {"time": 0.3333, "value": -49.78, "curve": [0.496, -49.78, 0.803, -5.1]}, {"time": 1, "value": 5.41, "curve": [1.037, 7.94, 1.07, 9.39]}, {"time": 1.1, "value": 9.39, "curve": [1.242, 9.39, 1.525, -49.78]}, {"time": 1.6667, "value": -49.78}]}, "Tentacle3_Mid": {"rotate": [{"value": -3.27, "curve": [0.083, 21.42, 0.176, 56.4]}, {"time": 0.2333, "value": 56.4, "curve": [0.26, 56.4, 0.295, 48]}, {"time": 0.3333, "value": 36.14, "curve": [0.402, 11.69, 0.485, -29.86]}, {"time": 0.5333, "value": -29.86, "curve": [0.656, -29.86, 0.822, -18.51]}, {"time": 1, "value": -3.82, "curve": [1.212, 20.88, 1.454, 56.4]}, {"time": 1.6, "value": 56.4, "curve": [1.62, 56.4, 1.642, 54.76]}, {"time": 1.6667, "value": 51.86}]}, "Tentacle3_Btm": {"rotate": [{"value": -19.18, "curve": [0.099, 10.49, 0.227, 73.54]}, {"time": 0.3, "value": 73.54, "curve": [0.31, 73.54, 0.321, 69.14]}, {"time": 0.3333, "value": 61.89, "curve": [0.417, 35.84, 0.535, -37.96]}, {"time": 0.6, "value": -37.96, "curve": [0.711, -37.96, 0.849, -29.31]}, {"time": 1, "value": -16.08, "curve": [1.224, 14.65, 1.505, 73.54]}, {"time": 1.6667, "value": 73.54}]}, "Tentacle6": {"rotate": [{"value": -8.02, "curve": [0.012, -9.72, 0.023, -10.68]}, {"time": 0.0333, "value": -10.68, "curve": [0.108, -10.68, 0.258, 36.14]}, {"time": 0.3333, "value": 36.14, "curve": [0.496, 36.14, 0.803, 0.78]}, {"time": 1, "value": -7.54, "curve": [1.037, -9.53, 1.07, -10.68]}, {"time": 1.1, "value": -10.68, "curve": [1.242, -10.68, 1.525, 36.14]}, {"time": 1.6667, "value": 36.14}]}, "Tentacle3_Mid2": {"rotate": [{"value": -1.59, "curve": [0.083, 24.91, 0.176, 62.45]}, {"time": 0.2333, "value": 62.45, "curve": [0.26, 62.45, 0.295, 53.43]}, {"time": 0.3333, "value": 40.71, "curve": [0.402, 14.47, 0.485, -30.13]}, {"time": 0.5333, "value": -30.13, "curve": [0.656, -30.13, 0.822, -17.95]}, {"time": 1, "value": -2.18, "curve": [1.201, 24.33, 1.429, 62.45]}, {"time": 1.5667, "value": 62.45, "curve": [1.596, 62.45, 1.629, 58.81]}, {"time": 1.6667, "value": 52.81}]}, "Tentacle3_Btm2": {"rotate": [{"value": -23.87, "curve": [0.099, 3.75, 0.227, 62.45]}, {"time": 0.3, "value": 62.45, "curve": [0.31, 62.45, 0.321, 58.35]}, {"time": 0.3333, "value": 51.6, "curve": [0.417, 27.35, 0.535, -41.36]}, {"time": 0.6, "value": -41.36, "curve": [0.711, -41.36, 0.849, -33.3]}, {"time": 1, "value": -20.98, "curve": [1.213, 7.62, 1.48, 62.45]}, {"time": 1.6333, "value": 62.45, "curve": [1.644, 62.45, 1.655, 61.9]}, {"time": 1.6667, "value": 60.88}]}, "Tentacle2_Btm2": {"rotate": [{"value": -38.29, "curve": [0.109, -9.38, 0.252, 55.68]}, {"time": 0.3333, "value": 55.68, "curve": [0.412, 34.28, 0.535, -56.67]}, {"time": 0.6, "value": -56.67, "curve": [0.712, -56.67, 0.849, -49.72]}, {"time": 1, "value": -38.92, "curve": [1.195, -8.2, 1.454, 62.45]}, {"time": 1.6, "value": 62.45, "curve": [1.62, 62.45, 1.642, 60]}, {"time": 1.6667, "value": 55.68}]}, "Tentacle2_Mid2": {"rotate": [{"value": 5.37, "curve": [0.085, 31.15, 0.176, 62.45]}, {"time": 0.2333, "value": 62.45, "curve": [0.259, 62.45, 0.295, 46.14]}, {"time": 0.3333, "value": 26.96, "curve": [0.406, 1.18, 0.484, -30.13]}, {"time": 0.5333, "value": -30.13, "curve": [0.653, -30.13, 0.822, -12.53]}, {"time": 1, "value": 8.18, "curve": [1.159, 33.42, 1.327, 62.45]}, {"time": 1.4333, "value": 62.45, "curve": [1.493, 62.45, 1.578, 46.14]}, {"time": 1.6667, "value": 26.96}]}, "Tentacle4": {"rotate": [{"value": -8.58, "curve": [0.012, -9.92, 0.023, -10.68]}, {"time": 0.0333, "value": -10.68, "curve": [0.108, -10.68, 0.258, 26.28]}, {"time": 0.3333, "value": 26.28, "curve": [0.496, 26.28, 0.803, -1.63]}, {"time": 1, "value": -8.2, "curve": [1.037, -9.77, 1.07, -10.68]}, {"time": 1.1, "value": -10.68, "curve": [1.242, -10.68, 1.525, 26.28]}, {"time": 1.6667, "value": 26.28}]}, "Tentacle2_Btm": {"rotate": [{"value": -46.79, "curve": [0.109, -17.91, 0.252, 47.08]}, {"time": 0.3333, "value": 47.08, "curve": [0.412, 25.7, 0.535, -65.15]}, {"time": 0.6, "value": -65.15, "curve": [0.712, -65.15, 0.849, -58.21]}, {"time": 1, "value": -47.42, "curve": [1.195, -16.73, 1.454, 53.84]}, {"time": 1.6, "value": 53.84, "curve": [1.62, 53.84, 1.642, 51.4]}, {"time": 1.6667, "value": 47.08}]}, "Tentacle2_Mid": {"rotate": [{"value": -3.22, "curve": [0.085, 20.8, 0.176, 49.96]}, {"time": 0.2333, "value": 49.96, "curve": [0.259, 49.96, 0.295, 34.77]}, {"time": 0.3333, "value": 16.89, "curve": [0.406, -7.13, 0.484, -36.3]}, {"time": 0.5333, "value": -36.3, "curve": [0.653, -36.3, 0.822, -19.9]}, {"time": 1, "value": -0.6, "curve": [1.159, 22.92, 1.327, 49.96]}, {"time": 1.4333, "value": 49.96, "curve": [1.493, 49.96, 1.578, 34.77]}, {"time": 1.6667, "value": 16.89}]}, "Tentacle3": {"rotate": [{"value": 4.21, "curve": [0.012, 5.78, 0.023, 6.68]}, {"time": 0.0333, "value": 6.68, "curve": [0.108, 6.68, 0.258, -36.75]}, {"time": 0.3333, "value": -36.75, "curve": [0.496, -36.75, 0.803, -3.96]}, {"time": 1, "value": 3.76, "curve": [1.037, 5.61, 1.07, 6.68]}, {"time": 1.1, "value": 6.68, "curve": [1.242, 6.68, 1.525, -36.75]}, {"time": 1.6667, "value": -36.75}]}, "DangleHandle": {"rotate": [{"value": -15.4, "curve": [0.079, -4.37, 0.177, 16.3]}, {"time": 0.2333, "value": 16.3, "curve": [0.26, 16.3, 0.295, 12.3]}, {"time": 0.3333, "value": 6.7, "curve": [0.355, 3.66, 0.377, 0.04]}, {"time": 0.4, "value": -3.58, "curve": [0.475, -13.51, 0.55, -23.45]}, {"time": 0.6, "value": -23.45, "curve": [0.709, -23.45, 0.848, -19.55]}, {"time": 1, "value": -13.82, "curve": [1.161, -2.53, 1.353, 16.3]}, {"time": 1.4667, "value": 16.3, "curve": [1.52, 16.3, 1.59, 12.3]}, {"time": 1.6667, "value": 6.7}], "translate": [{"x": -8.18, "y": 2.62, "curve": [0.062, 16.95, 0.125, 43.87, 0.062, 3.65, 0.125, 4.74]}, {"time": 0.1667, "x": 43.87, "y": 4.74, "curve": [0.208, 43.87, 0.271, 19.95, 0.208, 4.74, 0.271, 3.77]}, {"time": 0.3333, "x": -3.96, "y": 2.8, "curve": [0.408, -27.88, 0.483, -51.8, 0.408, 1.82, 0.483, 0.85]}, {"time": 0.5333, "x": -51.8, "y": 0.85, "curve": [0.651, -51.8, 0.824, -28.65, 0.651, 0.85, 0.824, 1.79]}, {"time": 1, "x": -3.96, "y": 2.8, "curve": [1.125, 19.95, 1.25, 43.87, 1.125, 3.77, 1.25, 4.74]}, {"time": 1.3333, "x": 43.87, "y": 4.74, "curve": [1.417, 43.87, 1.542, 19.95, 1.417, 4.74, 1.542, 3.77]}, {"time": 1.6667, "x": -3.96, "y": 2.8}], "scale": [{"y": 0.313, "curve": [0.083, 1, 0.25, 1, 0.083, 0.313, 0.25, 1.723]}, {"time": 0.3333, "y": 1.723, "curve": [0.5, 1, 0.833, 1, 0.5, 1.723, 0.833, 0.313]}, {"time": 1, "y": 0.313, "curve": [1.167, 1, 1.5, 1, 1.167, 0.313, 1.5, 1.723]}, {"time": 1.6667, "y": 1.723}]}, "Tentacles": {"scale": [{"x": 1.199}, {"time": 0.3333, "x": 0.699}, {"time": 1, "x": 1.199}, {"time": 1.6667, "x": 0.699}]}, "Mouth3": {"rotate": [{"value": -2.12, "curve": [0.022, -8.08, 0.225, -9.86]}, {"time": 0.3, "value": -9.86, "curve": [0.408, -9.86, 0.412, 34.14]}, {"time": 0.7333, "value": 34.14, "curve": [1.011, 34.14, 1.325, 20.87]}, {"time": 1.6667, "value": -2.12}]}, "Mouth6": {"rotate": [{"value": -27.19, "curve": "stepped"}, {"time": 0.7333, "value": -27.19, "curve": [1.068, -26.53, 1.439, -25.64]}, {"time": 1.6667, "value": -25.64}]}, "Tentacle3_Btm3": {"rotate": [{"value": -19.18, "curve": [0.099, 10.49, 0.227, 73.54]}, {"time": 0.3, "value": 73.54, "curve": [0.31, 73.54, 0.321, 69.14]}, {"time": 0.3333, "value": 61.89, "curve": [0.417, 35.84, 0.535, -37.96]}, {"time": 0.6, "value": -37.96, "curve": [0.711, -37.96, 0.849, -29.31]}, {"time": 1, "value": -16.08, "curve": [1.19, 14.65, 1.429, 73.54]}, {"time": 1.5667, "value": 73.54, "curve": [1.596, 73.54, 1.629, 69.14]}, {"time": 1.6667, "value": 61.89}]}, "Tentacle3_Mid3": {"rotate": [{"value": -3.27, "curve": [0.083, 21.42, 0.176, 56.4]}, {"time": 0.2333, "value": 56.4, "curve": [0.26, 56.4, 0.295, 48]}, {"time": 0.3333, "value": 36.14, "curve": [0.402, 11.69, 0.485, -29.86]}, {"time": 0.5333, "value": -29.86, "curve": [0.656, -29.86, 0.822, -18.51]}, {"time": 1, "value": -3.82, "curve": [1.177, 20.88, 1.378, 56.4]}, {"time": 1.5, "value": 56.4, "curve": [1.545, 56.4, 1.603, 48]}, {"time": 1.6667, "value": 36.14}]}, "Tentacle1": {"rotate": [{"value": 6.03, "curve": [0.012, 8.17, 0.023, 9.39]}, {"time": 0.0333, "value": 9.39, "curve": [0.108, 9.39, 0.258, -49.78]}, {"time": 0.3333, "value": -49.78, "curve": [0.496, -49.78, 0.803, -5.1]}, {"time": 1, "value": 5.41, "curve": [1.037, 7.94, 1.07, 9.39]}, {"time": 1.1, "value": 9.39, "curve": [1.242, 9.39, 1.525, -49.78]}, {"time": 1.6667, "value": -49.78}]}, "Tentacle3_Btm4": {"rotate": [{"value": -23.87, "curve": [0.099, 3.75, 0.227, 62.45]}, {"time": 0.3, "value": 62.45, "curve": [0.31, 62.45, 0.321, 58.35]}, {"time": 0.3333, "value": 51.6, "curve": [0.417, 27.35, 0.535, -41.36]}, {"time": 0.6, "value": -41.36, "curve": [0.711, -41.36, 0.849, -33.3]}, {"time": 1, "value": -20.98, "curve": [1.19, 7.62, 1.429, 62.45]}, {"time": 1.5667, "value": 62.45, "curve": [1.596, 62.45, 1.629, 58.35]}, {"time": 1.6667, "value": 51.6}]}, "Tentacle3_Mid4": {"rotate": [{"value": -1.59, "curve": [0.083, 24.91, 0.176, 62.45]}, {"time": 0.2333, "value": 62.45, "curve": [0.26, 62.45, 0.295, 53.43]}, {"time": 0.3333, "value": 40.71, "curve": [0.402, 14.47, 0.485, -30.13]}, {"time": 0.5333, "value": -30.13, "curve": [0.656, -30.13, 0.822, -17.95]}, {"time": 1, "value": -2.18, "curve": [1.177, 24.33, 1.378, 62.45]}, {"time": 1.5, "value": 62.45, "curve": [1.545, 62.45, 1.603, 53.43]}, {"time": 1.6667, "value": 40.71}]}, "Tentacle2": {"rotate": [{"value": -8.02, "curve": [0.012, -9.72, 0.023, -10.68]}, {"time": 0.0333, "value": -10.68, "curve": [0.108, -10.68, 0.258, 36.14]}, {"time": 0.3333, "value": 36.14, "curve": [0.496, 36.14, 0.803, 0.78]}, {"time": 1, "value": -7.54, "curve": [1.037, -9.53, 1.07, -10.68]}, {"time": 1.1, "value": -10.68, "curve": [1.242, -10.68, 1.525, 36.14]}, {"time": 1.6667, "value": 36.14}]}, "Mouth2": {"scale": [{}, {"time": 0.3333, "x": 1.013, "y": 1.013}, {"time": 1}]}, "Mouth7": {"rotate": [{"value": -40.38, "curve": [0.022, 24.75, 0.225, 44.21]}, {"time": 0.3, "value": 44.21, "curve": [0.408, 44.21, 0.412, -23.91]}, {"time": 0.7333, "value": -23.91, "curve": [0.875, -23.91, 1.158, 12.96]}, {"time": 1.3, "value": 12.96, "curve": [1.392, 12.96, 1.575, -41.36]}, {"time": 1.6667, "value": -41.36}]}, "Mouth5": {"rotate": [{"value": 20.26, "curve": [0.022, -16.38, 0.225, -27.33]}, {"time": 0.3, "value": -27.33, "curve": [0.408, -27.33, 0.412, 67.22]}, {"time": 0.7333, "value": 67.22, "curve": [0.875, 67.22, 1.158, 6.31]}, {"time": 1.3, "value": 6.31, "curve": [1.392, 6.31, 1.575, 18.98]}, {"time": 1.6667, "value": 18.98}]}, "Mouth": {"translate": [{}, {"time": 0.3333, "y": -10.97, "curve": [0.5, 0, 0.833, 0, 0.5, -10.97, 0.833, 4.02]}, {"time": 1, "y": 4.02, "curve": [1.167, 0, 1.5, 0, 1.167, 4.02, 1.5, -10.97]}, {"time": 1.6667, "y": -10.97}]}, "Mouth8": {"rotate": [{"value": -32.57, "curve": [0.022, 28.55, 0.225, 46.82]}, {"time": 0.3, "value": 46.82, "curve": [0.408, 46.82, 0.412, -80.21]}, {"time": 0.7333, "value": -80.21, "curve": [0.875, -80.21, 1.158, -1.04]}, {"time": 1.3, "value": -1.04, "curve": [1.392, -1.04, 1.575, -31.71]}, {"time": 1.6667, "value": -31.71}]}, "Mouth4": {"rotate": [{"value": 7.72, "curve": [0.022, -19.61, 0.225, -27.78]}, {"time": 0.3, "value": -27.78, "curve": [0.408, -27.78, 0.412, 43.97]}, {"time": 0.7333, "value": 43.97, "curve": [0.875, 43.97, 1.158, -13.85]}, {"time": 1.3, "value": -13.85, "curve": [1.392, -13.85, 1.575, 6.8]}, {"time": 1.6667, "value": 6.8}]}}, "drawOrder": [{"offsets": [{"slot": "Eyes", "offset": 4}]}]}, "attack-spikes2": {"bones": {"FACE": {"translate": [{"x": 0.84, "y": 4.16, "curve": [0.017, -23.65, 0.036, -44.45, 0.017, 3.67, 0.036, 3.25]}, {"time": 0.0667, "x": -45.76, "y": 3.22, "curve": [0.133, -45.76, 0.267, 18.32, 0.133, 3.22, 0.267, 6.16]}, {"time": 0.3333, "x": 18.32, "y": 6.16, "curve": [0.425, 18.32, 0.608, 12.06, 0.425, 6.16, 0.608, 4.16]}, {"time": 0.7, "x": 12.06, "y": 4.16, "curve": [0.842, 12.06, 1.125, -21.84, 0.842, 4.16, 1.125, 4.16]}, {"time": 1.2667, "x": -21.84, "y": 4.16, "curve": [1.464, -21.84, 1.552, -5.93, 1.464, 4.16, 1.552, 4.16]}, {"time": 1.6667, "x": 7.05, "y": 4.16}], "scale": [{}, {"time": 0.0667, "x": 1.219, "y": 1.219}, {"time": 0.2}]}, "MAIN": {"translate": [{"y": -33.8, "curve": [0, 0, 0.023, 0, 0, -53.67, 0.023, -72.29]}, {"time": 0.0667, "y": -89.66, "curve": [0.115, 0, 0.307, 0, 0.115, -21.75, 0.307, 14.42]}, {"time": 0.5, "y": 17.53, "curve": [0.732, 0, 0.75, 0, 0.732, 17.53, 0.75, -89.61]}, {"time": 1, "y": -92.41, "curve": [1.31, 0, 1.333, 0, 1.31, -92.41, 1.333, 14.73]}, {"time": 1.6667, "y": 17.53}], "scale": [{"x": 0.76, "y": 1.379, "curve": [0.017, 0.76, 0.05, 1.509, 0.017, 1.379, 0.05, 0.663]}, {"time": 0.0667, "x": 1.509, "y": 0.663, "curve": [0.067, 1.332, 0.192, 1.267, 0.067, 0.789, 0.192, 0.835]}, {"time": 0.2333, "x": 1.267, "y": 0.835, "curve": [0.3, 1.267, 0.433, 0.937, 0.3, 0.835, 0.433, 1.05]}, {"time": 0.5, "x": 0.937, "y": 1.05, "curve": [0.625, 0.937, 0.875, 0.987, 0.625, 1.05, 0.875, 1.006]}, {"time": 1, "x": 0.987, "y": 1.006, "curve": [1.125, 1.013, 1.25, 1.038, 1.125, 0.984, 1.25, 0.962]}, {"time": 1.3333, "x": 1.038, "y": 0.962, "curve": [1.417, 1.038, 1.542, 1.013, 1.417, 0.962, 1.542, 0.984]}, {"time": 1.6667, "x": 0.987, "y": 1.006}]}, "Spike5": {"rotate": [{"value": 1.02, "curve": [0, -11.61, 0.05, -17.33]}, {"time": 0.0667, "value": -17.33, "curve": [0.125, -17.33, 0.242, 29.38]}, {"time": 0.3, "value": 29.38, "curve": [0.405, 22.8, 0.527, 12.93]}, {"time": 0.6, "value": 12.93, "curve": [0.742, 12.93, 1.025, -8.26]}, {"time": 1.1667, "value": -8.26, "curve": [1.23, -10.3, 1.287, -11.66]}, {"time": 1.3333, "value": -11.66, "curve": [1.414, -11.66, 1.556, -1.65]}, {"time": 1.6667, "value": 3.35}], "translate": [{"x": 14.75, "y": 0.87, "curve": "stepped"}, {"time": 0.0667, "x": 14.75, "y": 0.87, "curve": [0.267, 14.75, 0.8, 6.63, 0.267, 0.87, 0.8, 0.39]}, {"time": 0.8667}], "scale": [{"x": 0.45, "y": 1.101, "curve": [0, 1.237, 0.05, 1.594, 0, 1.394, 0.05, 1.526]}, {"time": 0.0667, "x": 1.594, "y": 1.526, "curve": [0.092, 1.594, 0.158, 1.129, 0.092, 1.526, 0.158, 1.236]}, {"time": 0.1667, "x": 0.75, "curve": [0.192, 0.75, 0.242, 1, 0.192, 1, 0.242, 1]}, {"time": 0.2667}]}, "Spike4": {"rotate": [{"value": -3.82, "curve": [0, -16.45, 0.05, -22.16]}, {"time": 0.0667, "value": -22.16, "curve": [0.125, -22.16, 0.242, 25.26]}, {"time": 0.3, "value": 25.26, "curve": [0.405, 19.32, 0.527, 10.4]}, {"time": 0.6, "value": 10.4, "curve": [0.756, 6.83, 1.003, -9.26]}, {"time": 1.1333, "value": -9.26, "curve": [1.183, -10.73, 1.229, -11.66]}, {"time": 1.2667, "value": -11.66, "curve": [1.364, -11.66, 1.539, -0.22]}, {"time": 1.6667, "value": 4.35}], "translate": [{"x": 14.73, "y": -0.21, "curve": "stepped"}, {"time": 0.0667, "x": 14.73, "y": -0.21, "curve": [0.267, 14.73, 0.8, 6.62, 0.267, -0.21, 0.8, -0.09]}, {"time": 0.8667}], "scale": [{"x": 0.45, "y": 1.101, "curve": [0, 1.237, 0.05, 1.594, 0, 1.394, 0.05, 1.526]}, {"time": 0.0667, "x": 1.594, "y": 1.526, "curve": [0.092, 1.594, 0.158, 1.129, 0.092, 1.526, 0.158, 1.236]}, {"time": 0.1667, "x": 0.75, "curve": [0.192, 0.75, 0.242, 1, 0.192, 1, 0.242, 1]}, {"time": 0.2667}]}, "Spike3": {"rotate": [{"value": -8.73, "curve": [0, -29.82, 0.05, -39.37]}, {"time": 0.0667, "value": -39.37, "curve": [0.125, -39.37, 0.242, 19.6]}, {"time": 0.3, "value": 19.6, "curve": [0.405, 14.9, 0.527, 7.83]}, {"time": 0.6, "value": 7.83, "curve": [0.763, 2.31, 0.979, -10.13]}, {"time": 1.1, "value": -10.13, "curve": [1.243, -6.23, 1.453, 6.75]}, {"time": 1.5667, "value": 6.75, "curve": [1.596, 6.75, 1.63, 6.18]}, {"time": 1.6667, "value": 5.22}], "translate": [{"x": 13.76, "y": 16.02, "curve": "stepped"}, {"time": 0.0667, "x": 13.76, "y": 16.02, "curve": [0.267, 13.76, 0.8, 6.18, 0.267, 16.02, 0.8, 7.2]}, {"time": 0.8667}], "scale": [{"x": 0.45, "y": 1.101, "curve": [0, 1.237, 0.05, 1.594, 0, 1.394, 0.05, 1.526]}, {"time": 0.0667, "x": 1.594, "y": 1.526, "curve": [0.092, 1.594, 0.158, 1.129, 0.092, 1.526, 0.158, 1.236]}, {"time": 0.1667, "x": 0.75, "curve": [0.192, 0.75, 0.242, 1, 0.192, 1, 0.242, 1]}, {"time": 0.2667}]}, "Spike2": {"rotate": [{"value": -25.78, "curve": [0, -38.41, 0.05, -44.12]}, {"time": 0.0667, "value": -44.12, "curve": [0.125, -44.12, 0.242, 11.57]}, {"time": 0.3, "value": 11.57, "curve": [0.405, 11.1, 0.527, 10.39]}, {"time": 0.6, "value": 10.39, "curve": [0.764, 4.28, 0.953, -4.89]}, {"time": 1.0667, "value": -4.89, "curve": [1.188, 0.28, 1.318, 6.75]}, {"time": 1.4, "value": 6.75, "curve": [1.469, 6.75, 1.565, 3.67]}, {"time": 1.6667, "value": -0.02}], "translate": [{"x": 8.47, "y": 19.34, "curve": "stepped"}, {"time": 0.0667, "x": 8.47, "y": 19.34, "curve": [0.267, 8.47, 0.8, 3.81, 0.267, 19.34, 0.8, 8.69]}, {"time": 0.8667}], "scale": [{"x": 0.45, "y": 1.101, "curve": [0, 1.237, 0.05, 1.594, 0, 1.394, 0.05, 1.526]}, {"time": 0.0667, "x": 1.594, "y": 1.526, "curve": [0.092, 1.594, 0.158, 1.129, 0.092, 1.526, 0.158, 1.236]}, {"time": 0.1667, "x": 0.75, "curve": [0.192, 0.75, 0.242, 1, 0.192, 1, 0.242, 1]}, {"time": 0.2667}]}, "Spike1": {"rotate": [{"value": -19.11, "curve": [0, -27.46, 0.05, -31.23]}, {"time": 0.0667, "value": -31.23, "curve": [0.125, -31.23, 0.242, 16.3]}, {"time": 0.3, "value": 16.3, "curve": [0.405, 12.29, 0.527, 6.27]}, {"time": 0.6, "value": 6.27, "curve": [0.752, 1.68, 0.898, -2.45]}, {"time": 1, "value": -2.45, "curve": [1.125, 2.15, 1.25, 6.75]}, {"time": 1.3333, "value": 6.75, "curve": [1.417, 6.75, 1.542, 2.12]}, {"time": 1.6667, "value": -2.5}], "scale": [{"x": 0.45, "y": 1.101, "curve": [0, 1.237, 0.05, 1.594, 0, 1.394, 0.05, 1.526]}, {"time": 0.0667, "x": 1.594, "y": 1.526, "curve": [0.092, 1.594, 0.158, 1.129, 0.092, 1.526, 0.158, 1.236]}, {"time": 0.1667, "x": 0.75, "curve": [0.192, 0.75, 0.242, 1, 0.192, 1, 0.242, 1]}, {"time": 0.2667}]}, "Spike10": {"rotate": [{"value": 13.02, "curve": [0, 28.95, 0.05, 36.16]}, {"time": 0.0667, "value": 36.16, "curve": [0.125, 36.16, 0.242, -18.53]}, {"time": 0.3, "value": -18.53, "curve": [0.405, -15.79, 0.527, -11.68]}, {"time": 0.6, "value": -11.68, "curve": [0.752, -6.68, 0.898, -2.18]}, {"time": 1, "value": -2.18, "curve": [1.076, -4.25, 1.146, -5.73]}, {"time": 1.2, "value": -5.73, "curve": [1.313, -5.73, 1.506, 1.22]}, {"time": 1.6667, "value": 5.4}], "scale": [{"x": 0.45, "y": 1.101, "curve": [0, 1.237, 0.05, 1.594, 0, 1.394, 0.05, 1.526]}, {"time": 0.0667, "x": 1.594, "y": 1.526, "curve": [0.092, 1.594, 0.158, 1.129, 0.092, 1.526, 0.158, 1.236]}, {"time": 0.1667, "x": 0.75, "curve": [0.192, 0.75, 0.242, 1, 0.192, 1, 0.242, 1]}, {"time": 0.2667}]}, "Spike6": {"rotate": [{"value": 26.44, "curve": [0, 47.44, 0.05, 56.95]}, {"time": 0.0667, "value": 56.95, "curve": [0.125, 56.95, 0.242, -7.87]}, {"time": 0.3, "value": -7.87, "curve": [0.405, -9.93, 0.527, -13.03]}, {"time": 0.6, "value": -13.03, "curve": [0.764, -6.4, 0.953, 3.55]}, {"time": 1.0667, "value": 3.55, "curve": [1.188, -0.57, 1.318, -5.73]}, {"time": 1.4, "value": -5.73, "curve": [1.469, -5.73, 1.565, -3.28]}, {"time": 1.6667, "value": -0.33}], "translate": [{"x": -5.58, "y": -9.65, "curve": "stepped"}, {"time": 0.0667, "x": -5.58, "y": -9.65, "curve": [0.267, -5.58, 0.8, -2.5, 0.267, -9.65, 0.8, -4.34]}, {"time": 0.8667}], "scale": [{"x": 0.45, "y": 1.101, "curve": [0, 1.237, 0.05, 1.594, 0, 1.394, 0.05, 1.526]}, {"time": 0.0667, "x": 1.594, "y": 1.526, "curve": [0.092, 1.594, 0.158, 1.129, 0.092, 1.526, 0.158, 1.236]}, {"time": 0.1667, "x": 0.75, "curve": [0.192, 0.75, 0.242, 1, 0.192, 1, 0.242, 1]}, {"time": 0.2667}]}, "Spike7": {"rotate": [{"value": 6.75, "curve": [0, 37.66, 0.05, 51.65]}, {"time": 0.0667, "value": 51.65, "curve": [0.125, 51.65, 0.242, -17.82]}, {"time": 0.3, "value": -17.82, "curve": [0.405, -15.39, 0.527, -11.74]}, {"time": 0.6, "value": -11.74, "curve": [0.763, -5.75, 0.979, 7.74]}, {"time": 1.1, "value": 7.74, "curve": [1.243, 4.63, 1.453, -5.73]}, {"time": 1.5667, "value": -5.73, "curve": [1.596, -5.73, 1.63, -5.28]}, {"time": 1.6667, "value": -4.52}], "translate": [{"x": 7.74, "y": -13.08, "curve": "stepped"}, {"time": 0.0667, "x": 7.74, "y": -13.08, "curve": [0.267, 7.74, 0.8, 3.48, 0.267, -13.08, 0.8, -5.88]}, {"time": 0.8667}], "scale": [{"x": 0.45, "y": 1.101, "curve": [0, 1.237, 0.05, 1.594, 0, 1.394, 0.05, 1.526]}, {"time": 0.0667, "x": 1.594, "y": 1.526, "curve": [0.092, 1.594, 0.158, 1.129, 0.092, 1.526, 0.158, 1.236]}, {"time": 0.1667, "x": 0.75, "curve": [0.192, 0.75, 0.242, 1, 0.192, 1, 0.242, 1]}, {"time": 0.2667}]}, "Spike8": {"rotate": [{"value": -2.56, "curve": [0, 18.44, 0.05, 27.94]}, {"time": 0.0667, "value": 27.94, "curve": [0.125, 27.94, 0.242, -26.14]}, {"time": 0.3, "value": -26.14, "curve": [0.405, -21.47, 0.527, -14.48]}, {"time": 0.6, "value": -14.48, "curve": [0.756, -10.56, 1.003, 7.05]}, {"time": 1.1333, "value": 7.05, "curve": [1.183, 8.22, 1.229, 8.96]}, {"time": 1.2667, "value": 8.96, "curve": [1.364, 8.96, 1.539, -0.17]}, {"time": 1.6667, "value": -3.82}], "translate": [{"x": 13.39, "y": -0.19, "curve": "stepped"}, {"time": 0.0667, "x": 13.39, "y": -0.19, "curve": [0.267, 13.39, 0.8, 6.02, 0.267, -0.19, 0.8, -0.09]}, {"time": 0.8667}], "scale": [{"x": 0.45, "y": 1.101, "curve": [0, 1.237, 0.05, 1.594, 0, 1.394, 0.05, 1.526]}, {"time": 0.0667, "x": 1.594, "y": 1.526, "curve": [0.092, 1.594, 0.158, 1.129, 0.092, 1.526, 0.158, 1.236]}, {"time": 0.1667, "x": 0.75, "curve": [0.192, 0.75, 0.242, 1, 0.192, 1, 0.242, 1]}, {"time": 0.2667}]}, "Spike9": {"rotate": [{"value": -9.45, "curve": [0, 11.55, 0.05, 21.05]}, {"time": 0.0667, "value": 21.05, "curve": [0.125, 21.05, 0.242, -31.45]}, {"time": 0.3, "value": -31.45, "curve": [0.405, -25.6, 0.527, -16.81]}, {"time": 0.6, "value": -16.81, "curve": [0.742, -16.81, 1.025, 6.25]}, {"time": 1.1667, "value": 6.25, "curve": [1.23, 7.87, 1.287, 8.96]}, {"time": 1.3333, "value": 8.96, "curve": [1.414, 8.96, 1.556, 0.97]}, {"time": 1.6667, "value": -3.02}], "scale": [{"x": 0.45, "y": 1.101, "curve": [0, 1.237, 0.05, 1.594, 0, 1.394, 0.05, 1.526]}, {"time": 0.0667, "x": 1.594, "y": 1.526, "curve": [0.092, 1.594, 0.158, 1.129, 0.092, 1.526, 0.158, 1.236]}, {"time": 0.1667, "x": 0.75, "curve": [0.192, 0.75, 0.242, 1, 0.192, 1, 0.242, 1]}, {"time": 0.2667}]}, "Tentacle5": {"rotate": [{"value": 6.03, "curve": [0.024, 8.17, 0.047, 9.39]}, {"time": 0.0667, "value": 9.39, "curve": [0.175, 9.39, 0.392, -49.78]}, {"time": 0.5, "value": -49.78, "curve": [0.622, -49.78, 0.852, -5.1]}, {"time": 1, "value": 5.41, "curve": [1.037, 7.94, 1.07, 9.39]}, {"time": 1.1, "value": 9.39, "curve": [1.242, 9.39, 1.525, -49.78]}, {"time": 1.6667, "value": -49.78}]}, "Tentacle3_Mid": {"rotate": [{"value": -3.27, "curve": [0.13, 21.42, 0.277, 56.4]}, {"time": 0.3667, "value": 56.4, "curve": [0.403, 56.4, 0.449, 48]}, {"time": 0.5, "value": 36.14, "curve": [0.603, 11.69, 0.727, -29.86]}, {"time": 0.8, "value": -29.86, "curve": [0.852, -29.86, 0.924, -18.51]}, {"time": 1, "value": -3.82, "curve": [1.212, 20.88, 1.454, 56.4]}, {"time": 1.6, "value": 56.4, "curve": [1.62, 56.4, 1.642, 54.76]}, {"time": 1.6667, "value": 51.86}]}, "Tentacle3_Btm": {"rotate": [{"value": -19.18, "curve": [0.143, 10.49, 0.328, 73.54]}, {"time": 0.4333, "value": 73.54, "curve": [0.453, 73.54, 0.475, 69.14]}, {"time": 0.5, "value": 61.89, "curve": [0.615, 35.84, 0.778, -37.96]}, {"time": 0.8667, "value": -37.96, "curve": [0.904, -37.96, 0.95, -29.31]}, {"time": 1, "value": -16.08, "curve": [1.224, 14.65, 1.505, 73.54]}, {"time": 1.6667, "value": 73.54}]}, "Tentacle6": {"rotate": [{"value": -8.02, "curve": [0.024, -9.72, 0.047, -10.68]}, {"time": 0.0667, "value": -10.68, "curve": [0.175, -10.68, 0.392, 36.14]}, {"time": 0.5, "value": 36.14, "curve": [0.622, 36.14, 0.852, 0.78]}, {"time": 1, "value": -7.54, "curve": [1.037, -9.53, 1.07, -10.68]}, {"time": 1.1, "value": -10.68, "curve": [1.242, -10.68, 1.525, 36.14]}, {"time": 1.6667, "value": 36.14}]}, "Tentacle3_Mid2": {"rotate": [{"value": -1.59, "curve": [0.13, 24.91, 0.277, 62.45]}, {"time": 0.3667, "value": 62.45, "curve": [0.403, 62.45, 0.449, 53.43]}, {"time": 0.5, "value": 40.71, "curve": [0.603, 14.47, 0.727, -30.13]}, {"time": 0.8, "value": -30.13, "curve": [0.852, -30.13, 0.924, -17.95]}, {"time": 1, "value": -2.18, "curve": [1.201, 24.33, 1.429, 62.45]}, {"time": 1.5667, "value": 62.45, "curve": [1.596, 62.45, 1.629, 58.81]}, {"time": 1.6667, "value": 52.81}]}, "Tentacle3_Btm2": {"rotate": [{"value": -23.87, "curve": [0.143, 3.75, 0.328, 62.45]}, {"time": 0.4333, "value": 62.45, "curve": [0.453, 62.45, 0.475, 58.35]}, {"time": 0.5, "value": 51.6, "curve": [0.615, 27.35, 0.778, -41.36]}, {"time": 0.8667, "value": -41.36, "curve": [0.904, -41.36, 0.95, -33.3]}, {"time": 1, "value": -20.98, "curve": [1.213, 7.62, 1.48, 62.45]}, {"time": 1.6333, "value": 62.45, "curve": [1.644, 62.45, 1.655, 61.9]}, {"time": 1.6667, "value": 60.88}]}, "Tentacle2_Btm2": {"rotate": [{"value": -38.29, "curve": [0.152, -7.3, 0.353, 62.45]}, {"time": 0.4667, "value": 62.45, "curve": [0.477, 62.45, 0.488, 60]}, {"time": 0.5, "value": 55.68, "curve": [0.608, 34.28, 0.777, -56.67]}, {"time": 0.8667, "value": -56.67, "curve": [0.904, -56.67, 0.95, -49.72]}, {"time": 1, "value": -38.92, "curve": [1.195, -8.2, 1.454, 62.45]}, {"time": 1.6, "value": 62.45, "curve": [1.62, 62.45, 1.642, 60]}, {"time": 1.6667, "value": 55.68}]}, "Tentacle2_Mid2": {"rotate": [{"value": 5.37, "curve": [0.121, 31.15, 0.251, 62.45]}, {"time": 0.3333, "value": 62.45, "curve": [0.376, 62.45, 0.437, 46.14]}, {"time": 0.5, "value": 26.96, "curve": [0.597, 1.18, 0.701, -30.13]}, {"time": 0.7667, "value": -30.13, "curve": [0.827, -30.13, 0.911, -12.53]}, {"time": 1, "value": 8.18, "curve": [1.159, 33.42, 1.327, 62.45]}, {"time": 1.4333, "value": 62.45, "curve": [1.493, 62.45, 1.578, 46.14]}, {"time": 1.6667, "value": 26.96}]}, "Tentacle4": {"rotate": [{"value": -8.58, "curve": [0.024, -9.92, 0.047, -10.68]}, {"time": 0.0667, "value": -10.68, "curve": [0.175, -10.68, 0.392, 26.28]}, {"time": 0.5, "value": 26.28, "curve": [0.622, 26.28, 0.852, -1.63]}, {"time": 1, "value": -8.2, "curve": [1.037, -9.77, 1.07, -10.68]}, {"time": 1.1, "value": -10.68, "curve": [1.242, -10.68, 1.525, 26.28]}, {"time": 1.6667, "value": 26.28}]}, "Tentacle2_Btm": {"rotate": [{"value": -46.79, "curve": [0.152, -15.83, 0.353, 53.84]}, {"time": 0.4667, "value": 53.84, "curve": [0.477, 53.84, 0.488, 51.4]}, {"time": 0.5, "value": 47.08, "curve": [0.608, 25.7, 0.777, -65.15]}, {"time": 0.8667, "value": -65.15, "curve": [0.904, -65.15, 0.95, -58.21]}, {"time": 1, "value": -47.42, "curve": [1.195, -16.73, 1.454, 53.84]}, {"time": 1.6, "value": 53.84, "curve": [1.62, 53.84, 1.642, 51.4]}, {"time": 1.6667, "value": 47.08}]}, "Tentacle2_Mid": {"rotate": [{"value": -3.22, "curve": [0.121, 20.8, 0.251, 49.96]}, {"time": 0.3333, "value": 49.96, "curve": [0.376, 49.96, 0.437, 34.77]}, {"time": 0.5, "value": 16.89, "curve": [0.597, -7.13, 0.701, -36.3]}, {"time": 0.7667, "value": -36.3, "curve": [0.827, -36.3, 0.911, -19.9]}, {"time": 1, "value": -0.6, "curve": [1.159, 22.92, 1.327, 49.96]}, {"time": 1.4333, "value": 49.96, "curve": [1.493, 49.96, 1.578, 34.77]}, {"time": 1.6667, "value": 16.89}]}, "Tentacle3": {"rotate": [{"value": 4.21, "curve": [0.024, 5.78, 0.047, 6.68]}, {"time": 0.0667, "value": 6.68, "curve": [0.175, 6.68, 0.392, -36.75]}, {"time": 0.5, "value": -36.75, "curve": [0.622, -36.75, 0.852, -3.96]}, {"time": 1, "value": 3.76, "curve": [1.037, 5.61, 1.07, 6.68]}, {"time": 1.1, "value": 6.68, "curve": [1.242, 6.68, 1.525, -36.75]}, {"time": 1.6667, "value": -36.75}]}, "DangleHandle": {"rotate": [{"value": -15.4, "curve": [0.124, -4.37, 0.278, 16.3]}, {"time": 0.3667, "value": 16.3, "curve": [0.403, 16.3, 0.449, 12.3]}, {"time": 0.5, "value": 6.7, "curve": [0.532, 3.66, 0.566, 0.04]}, {"time": 0.6, "value": -3.58, "curve": [0.7, -13.51, 0.8, -23.45]}, {"time": 0.8667, "value": -23.45, "curve": [0.903, -23.45, 0.949, -19.55]}, {"time": 1, "value": -13.82, "curve": [1.161, -2.53, 1.353, 16.3]}, {"time": 1.4667, "value": 16.3, "curve": [1.52, 16.3, 1.59, 12.3]}, {"time": 1.6667, "value": 6.7}], "translate": [{"x": -8.18, "y": 2.62, "curve": [0.099, 16.95, 0.201, 43.87, 0.099, 3.65, 0.201, 4.74]}, {"time": 0.2667, "x": 43.87, "y": 4.74, "curve": [0.325, 43.87, 0.413, 19.95, 0.325, 4.74, 0.413, 3.77]}, {"time": 0.5, "x": -3.96, "y": 2.8, "curve": [0.6, -27.88, 0.7, -51.8, 0.6, 1.82, 0.7, 0.85]}, {"time": 0.7667, "x": -51.8, "y": 0.85, "curve": [0.826, -51.8, 0.912, -28.65, 0.826, 0.85, 0.912, 1.79]}, {"time": 1, "x": -3.96, "y": 2.8, "curve": [1.125, 19.95, 1.25, 43.87, 1.125, 3.77, 1.25, 4.74]}, {"time": 1.3333, "x": 43.87, "y": 4.74, "curve": [1.417, 43.87, 1.542, 19.95, 1.417, 4.74, 1.542, 3.77]}, {"time": 1.6667, "x": -3.96, "y": 2.8}], "scale": [{"y": 0.313, "curve": [0.125, 1, 0.375, 1, 0.125, 0.313, 0.375, 1.723]}, {"time": 0.5, "y": 1.723, "curve": [0.625, 1, 0.875, 1, 0.625, 1.723, 0.875, 0.313]}, {"time": 1, "y": 0.313, "curve": [1.167, 1, 1.5, 1, 1.167, 0.313, 1.5, 1.723]}, {"time": 1.6667, "y": 1.723}]}, "Tentacles": {"scale": [{"x": 1.199}, {"time": 0.5, "x": 0.699}, {"time": 1, "x": 1.199}, {"time": 1.6667, "x": 0.699}]}, "Mouth": {"translate": [{}, {"time": 0.5, "y": -10.97, "curve": [0.625, 0, 0.875, 0, 0.625, -10.97, 0.875, 4.02]}, {"time": 1, "y": 4.02, "curve": [1.167, 0, 1.5, 0, 1.167, 4.02, 1.5, -10.97]}, {"time": 1.6667, "y": -10.97}]}, "Mouth8": {"rotate": [{"value": -32.57, "curve": [0.033, -32.57, 0.1, -0.45]}, {"time": 0.1333, "value": -0.45, "curve": [0.225, -0.45, 0.408, -31.71]}, {"time": 0.5, "value": -31.71, "curve": [0.525, -38.54, 0.549, -43.41]}, {"time": 0.5667, "value": -43.41, "curve": [0.633, -43.41, 0.767, 4.89]}, {"time": 0.8333, "value": 4.89, "curve": [0.874, 4.89, 0.943, -18.4]}, {"time": 1, "value": -31.71, "curve": [1.038, -38.54, 1.073, -43.41]}, {"time": 1.1, "value": -43.41, "curve": [1.183, -43.41, 1.35, 4.89]}, {"time": 1.4333, "value": 4.89, "curve": [1.49, 4.89, 1.586, -17.99]}, {"time": 1.6667, "value": -31.71}]}, "Mouth5": {"rotate": [{"value": 20.26, "curve": [0.04, 13.9, 0.086, 2.98]}, {"time": 0.1333, "value": -9.36, "curve": [0.254, 4.39, 0.385, 22.13]}, {"time": 0.4667, "value": 22.13, "curve": [0.476, 22.13, 0.488, 20.92]}, {"time": 0.5, "value": 18.98, "curve": [0.564, 12.95, 0.651, -2.12]}, {"time": 0.7, "value": -2.12, "curve": [0.767, -2.12, 0.9, 22.13]}, {"time": 0.9667, "value": 22.13, "curve": [0.977, 22.13, 0.988, 20.97]}, {"time": 1, "value": 18.98, "curve": [1.085, 12.95, 1.202, -2.12]}, {"time": 1.2667, "value": -2.12, "curve": [1.35, -2.12, 1.517, 22.13]}, {"time": 1.6, "value": 22.13, "curve": [1.619, 22.13, 1.642, 20.92]}, {"time": 1.6667, "value": 18.98}]}, "Mouth4": {"rotate": [{"value": 7.72, "curve": [0.033, 7.72, 0.1, -21.65]}, {"time": 0.1333, "value": -21.65, "curve": [0.225, -21.65, 0.408, 6.8]}, {"time": 0.5, "value": 6.8, "curve": [0.538, 1.94, 0.574, -2.12]}, {"time": 0.6, "value": -2.12, "curve": [0.667, -2.12, 0.8, 22.13]}, {"time": 0.8667, "value": 22.13, "curve": [0.899, 22.13, 0.951, 13.88]}, {"time": 1, "value": 6.8, "curve": [1.051, 1.94, 1.099, -2.12]}, {"time": 1.1333, "value": -2.12, "curve": [1.217, -2.12, 1.383, 22.13]}, {"time": 1.4667, "value": 22.13, "curve": [1.516, 22.13, 1.594, 13.62]}, {"time": 1.6667, "value": 6.8}]}, "Mouth7": {"rotate": [{"value": -40.38, "curve": [0.033, -40.38, 0.1, 6.58]}, {"time": 0.1333, "value": 6.58, "curve": [0.217, 6.58, 0.383, -43.41]}, {"time": 0.4667, "value": -43.41, "curve": [0.477, -43.41, 0.488, -42.68]}, {"time": 0.5, "value": -41.36, "curve": [0.567, -33.65, 0.676, 4.89]}, {"time": 0.7333, "value": 4.89, "curve": [0.792, 4.89, 0.908, -43.41]}, {"time": 0.9667, "value": -43.41, "curve": [0.977, -43.41, 0.988, -42.66]}, {"time": 1, "value": -41.36, "curve": [1.087, -33.65, 1.226, 4.89]}, {"time": 1.3, "value": 4.89, "curve": [1.383, 4.89, 1.55, -43.41]}, {"time": 1.6333, "value": -43.41, "curve": [1.643, -43.41, 1.655, -42.68]}, {"time": 1.6667, "value": -41.36}]}, "Mouth3": {"rotate": [{"value": -2.12, "curve": [0.067, -2.12, 0.2, 22.13]}, {"time": 0.2667, "value": 22.13, "curve": [0.325, 22.13, 0.442, -2.12]}, {"time": 0.5, "value": -2.12, "curve": [0.567, -2.12, 0.7, 22.13]}, {"time": 0.7667, "value": 22.13, "curve": [0.825, 22.13, 0.942, -2.12]}, {"time": 1, "value": -2.12, "curve": [1.083, -2.12, 1.25, 22.13]}, {"time": 1.3333, "value": 22.13, "curve": [1.417, 22.13, 1.583, -2.12]}, {"time": 1.6667, "value": -2.12}]}, "Mouth6": {"rotate": [{"value": -27.19, "curve": [0.06, -13.44, 0.126, 4.89]}, {"time": 0.1667, "value": 4.89, "curve": [0.225, 4.89, 0.342, -43.41]}, {"time": 0.4, "value": -43.41, "curve": [0.426, -43.41, 0.462, -35.33]}, {"time": 0.5, "value": -25.64, "curve": [0.56, -12.07, 0.626, 4.89]}, {"time": 0.6667, "value": 4.89, "curve": [0.725, 4.89, 0.842, -43.41]}, {"time": 0.9, "value": -43.41, "curve": [0.926, -43.41, 0.962, -35.51]}, {"time": 1, "value": -25.64, "curve": [1.073, -12.07, 1.151, 4.89]}, {"time": 1.2, "value": 4.89, "curve": [1.283, 4.89, 1.45, -43.41]}, {"time": 1.5333, "value": -43.41, "curve": [1.568, -43.41, 1.616, -35.33]}, {"time": 1.6667, "value": -25.64}]}, "Mouth2": {"scale": [{}, {"time": 0.5, "x": 1.013, "y": 1.013}, {"time": 1}]}, "Tentacle3_Btm3": {"rotate": [{"value": -19.18, "curve": [0.143, 10.49, 0.328, 73.54]}, {"time": 0.4333, "value": 73.54, "curve": [0.453, 73.54, 0.475, 69.14]}, {"time": 0.5, "value": 61.89, "curve": [0.615, 35.84, 0.778, -37.96]}, {"time": 0.8667, "value": -37.96, "curve": [0.904, -37.96, 0.95, -29.31]}, {"time": 1, "value": -16.08, "curve": [1.19, 14.65, 1.429, 73.54]}, {"time": 1.5667, "value": 73.54, "curve": [1.596, 73.54, 1.629, 69.14]}, {"time": 1.6667, "value": 61.89}]}, "Tentacle3_Mid3": {"rotate": [{"value": -3.27, "curve": [0.13, 21.42, 0.277, 56.4]}, {"time": 0.3667, "value": 56.4, "curve": [0.403, 56.4, 0.449, 48]}, {"time": 0.5, "value": 36.14, "curve": [0.603, 11.69, 0.727, -29.86]}, {"time": 0.8, "value": -29.86, "curve": [0.852, -29.86, 0.924, -18.51]}, {"time": 1, "value": -3.82, "curve": [1.177, 20.88, 1.378, 56.4]}, {"time": 1.5, "value": 56.4, "curve": [1.545, 56.4, 1.603, 48]}, {"time": 1.6667, "value": 36.14}]}, "Tentacle1": {"rotate": [{"value": 6.03, "curve": [0.024, 8.17, 0.047, 9.39]}, {"time": 0.0667, "value": 9.39, "curve": [0.175, 9.39, 0.392, -49.78]}, {"time": 0.5, "value": -49.78, "curve": [0.622, -49.78, 0.852, -5.1]}, {"time": 1, "value": 5.41, "curve": [1.037, 7.94, 1.07, 9.39]}, {"time": 1.1, "value": 9.39, "curve": [1.242, 9.39, 1.525, -49.78]}, {"time": 1.6667, "value": -49.78}]}, "Tentacle3_Btm4": {"rotate": [{"value": -23.87, "curve": [0.143, 3.75, 0.328, 62.45]}, {"time": 0.4333, "value": 62.45, "curve": [0.453, 62.45, 0.475, 58.35]}, {"time": 0.5, "value": 51.6, "curve": [0.615, 27.35, 0.778, -41.36]}, {"time": 0.8667, "value": -41.36, "curve": [0.904, -41.36, 0.95, -33.3]}, {"time": 1, "value": -20.98, "curve": [1.19, 7.62, 1.429, 62.45]}, {"time": 1.5667, "value": 62.45, "curve": [1.596, 62.45, 1.629, 58.35]}, {"time": 1.6667, "value": 51.6}]}, "Tentacle3_Mid4": {"rotate": [{"value": -1.59, "curve": [0.13, 24.91, 0.277, 62.45]}, {"time": 0.3667, "value": 62.45, "curve": [0.403, 62.45, 0.449, 53.43]}, {"time": 0.5, "value": 40.71, "curve": [0.603, 14.47, 0.727, -30.13]}, {"time": 0.8, "value": -30.13, "curve": [0.852, -30.13, 0.924, -17.95]}, {"time": 1, "value": -2.18, "curve": [1.177, 24.33, 1.378, 62.45]}, {"time": 1.5, "value": 62.45, "curve": [1.545, 62.45, 1.603, 53.43]}, {"time": 1.6667, "value": 40.71}]}, "Tentacle2": {"rotate": [{"value": -8.02, "curve": [0.024, -9.72, 0.047, -10.68]}, {"time": 0.0667, "value": -10.68, "curve": [0.175, -10.68, 0.392, 36.14]}, {"time": 0.5, "value": 36.14, "curve": [0.622, 36.14, 0.852, 0.78]}, {"time": 1, "value": -7.54, "curve": [1.037, -9.53, 1.07, -10.68]}, {"time": 1.1, "value": -10.68, "curve": [1.242, -10.68, 1.525, 36.14]}, {"time": 1.6667, "value": 36.14}]}, "ScuttleEyeball": {"rotate": [{"time": 0.0667, "value": 23.14}, {"time": 0.4, "value": -23.43}]}}}, "hit-wall": {"slots": {"Eyes": {"attachment": [{"name": "EyesClosed"}, {"time": 1.4333, "name": "Eyes"}]}}, "bones": {"FACE": {"translate": [{"x": 0.84, "y": 4.16, "curve": [0.017, 0.84, 0.05, 83.22, 0.017, 4.16, 0.05, 36.64]}, {"time": 0.0667, "x": 83.22, "y": 36.64, "curve": [0.092, 83.22, 0.142, 31.26, 0.092, 36.64, 0.142, -58.08]}, {"time": 0.1667, "x": 31.26, "y": -58.08, "curve": [0.208, 31.26, 0.292, 38.66, 0.208, -58.08, 0.292, 39.05]}, {"time": 0.3333, "x": 38.66, "y": 39.05, "curve": [0.383, 38.66, 0.483, 80.97, 0.383, 39.05, 0.483, -18.55]}, {"time": 0.5333, "x": 80.97, "y": -18.55, "curve": [0.591, 80.97, 0.659, 28.46, 0.591, -18.55, 0.659, -12.07]}, {"time": 0.7333, "x": -59.07, "y": -1.28, "curve": [0.783, -59.07, 0.883, -33.32, 0.783, -1.28, 0.883, -1.28]}, {"time": 0.9333, "x": -33.32, "y": -1.28, "curve": [1.008, -33.32, 1.158, -59.07, 1.008, -1.28, 1.158, -1.28]}, {"time": 1.2333, "x": -59.07, "y": -1.28, "curve": [1.317, -59.07, 1.483, 37.16, 1.317, -1.28, 1.483, 4.16]}, {"time": 1.5667, "x": 37.16, "y": 4.16, "curve": [1.675, 37.16, 1.892, 7.05, 1.675, 4.16, 1.892, 4.16]}, {"time": 2, "x": 7.05, "y": 4.16}], "scale": [{}, {"time": 0.0667, "x": 1.219, "y": 1.219}, {"time": 0.2}]}, "Tentacles": {"translate": [{"time": 1.1667}, {"time": 1.3667, "x": -1.53, "y": -42.47}, {"time": 1.5667}], "scale": [{"x": 1.199}, {"time": 0.1667, "x": 0.699}, {"time": 0.6667, "x": 0.803, "curve": "stepped"}, {"time": 1.1333, "x": 0.803}, {"time": 1.3667, "x": 0.959, "y": 0.739}, {"time": 1.5667, "x": 0.699}]}, "Mouth": {"translate": [{}, {"time": 0.0667, "x": 8.27, "y": 49.14, "curve": [0.183, 8.27, 0.417, 0, 0.183, 49.14, 0.417, -10.97]}, {"time": 0.5333, "y": -10.97, "curve": [0.692, 0, 1.008, 0, 0.692, -10.97, 1.008, 4.02]}, {"time": 1.1667, "y": 4.02, "curve": [1.375, 0, 1.792, 0, 1.375, 4.02, 1.792, -10.97]}, {"time": 2, "y": -10.97}], "scale": [{"curve": [0.001, 1, 0.05, 1, 0.001, 2.216, 0.05, 2.599]}, {"time": 0.0667, "y": 2.599, "curve": [0.183, 1, 0.417, 1, 0.183, 2.599, 0.417, 1]}, {"time": 0.5333}]}, "Tentacle6": {"rotate": [{"value": -8.02, "curve": [0.012, 20.17, 0.023, 36.14]}, {"time": 0.0333, "value": 36.14, "curve": [0.122, 36.14, 0.273, 33.96]}, {"time": 0.4, "value": 32.58, "curve": [0.495, 19.2, 0.586, 8.09]}, {"time": 0.6667, "value": 1.63, "curve": "stepped"}, {"time": 1.1333, "value": 1.63, "curve": [1.181, -3.69, 1.226, -8.11]}, {"time": 1.2667, "value": -10.68, "curve": [1.45, -10.68, 1.817, 36.14]}, {"time": 2, "value": 36.14}]}, "Mouth3": {"rotate": [{"value": -2.12, "curve": "stepped"}, {"time": 0.5333, "value": -2.12, "curve": [0.6, -2.12, 0.733, -58.91]}, {"time": 0.8, "value": -58.91, "curve": [0.857, -58.91, 0.925, -51.28]}, {"time": 1, "value": -39.18, "curve": [1.247, -44.96, 1.581, -58.91]}, {"time": 1.7667, "value": -58.91, "curve": [1.825, -58.91, 1.942, -2.12]}, {"time": 2, "value": -2.12}]}, "Tentacle2": {"rotate": [{"value": -8.02, "curve": [0.012, 20.17, 0.023, 36.14]}, {"time": 0.0333, "value": 36.14, "curve": [0.122, 36.14, 0.273, 33.96]}, {"time": 0.4, "value": 32.58, "curve": [0.495, 19.2, 0.586, 8.09]}, {"time": 0.6667, "value": 1.63, "curve": "stepped"}, {"time": 1.1333, "value": 1.63, "curve": [1.181, -3.69, 1.226, -8.11]}, {"time": 1.2667, "value": -10.68, "curve": [1.45, -10.68, 1.817, 36.14]}, {"time": 2, "value": 36.14}]}, "Spike5": {"rotate": [{"value": -43.86, "curve": [0.025, 24.37, 0.2, 24.37]}, {"time": 0.2667, "value": 24.37}]}, "Tentacle2_Mid": {"rotate": [{"value": -3.22, "curve": [0.012, 5.86, 0.025, 16.89]}, {"time": 0.0333, "value": 16.89, "curve": [0.082, -7.13, 0.134, -36.3]}, {"time": 0.1667, "value": -36.3, "curve": [0.231, -36.3, 0.312, -44.79]}, {"time": 0.4, "value": -57.58, "curve": [0.485, -46.82, 0.575, -33.65]}, {"time": 0.6667, "value": -19.64, "curve": "stepped"}, {"time": 1.1333, "value": -19.64, "curve": [1.176, -10.02, 1.221, 1.75]}, {"time": 1.2667, "value": 14.27, "curve": [1.457, 33.21, 1.639, 49.96]}, {"time": 1.7667, "value": 49.96, "curve": [1.827, 49.96, 1.911, 34.77]}, {"time": 2, "value": 16.89}]}, "Tentacle3": {"rotate": [{"value": 4.21, "curve": [0.012, -21.93, 0.023, -36.75]}, {"time": 0.0333, "value": -36.75, "curve": [0.122, -36.75, 0.273, -31.53]}, {"time": 0.4, "value": -28.25, "curve": [0.495, -18.08, 0.586, -9.64]}, {"time": 0.6667, "value": -4.74, "curve": "stepped"}, {"time": 1.1333, "value": -4.74, "curve": [1.181, 0.19, 1.226, 4.3]}, {"time": 1.2667, "value": 6.68, "curve": [1.45, 6.68, 1.817, -36.75]}, {"time": 2, "value": -36.75}]}, "Tentacle5": {"rotate": [{"value": 6.03, "curve": [0.012, -29.6, 0.023, -49.78]}, {"time": 0.0333, "value": -49.78, "curve": [0.122, -49.78, 0.273, -37.43]}, {"time": 0.4, "value": -29.68, "curve": [0.495, -17.9, 0.586, -8.11]}, {"time": 0.6667, "value": -2.43, "curve": "stepped"}, {"time": 1.1333, "value": -2.43, "curve": [1.181, 2.68, 1.226, 6.92]}, {"time": 1.2667, "value": 9.39, "curve": [1.45, 9.39, 1.817, -49.78]}, {"time": 2, "value": -49.78}]}, "Mouth8": {"rotate": [{"value": -32.57, "curve": [0.003, -95.75, 0.1, -130.63]}, {"time": 0.1333, "value": -130.63, "curve": "stepped"}, {"time": 0.5333, "value": -130.63, "curve": [0.628, -18.5, 0.717, 41.87]}, {"time": 0.8, "value": 41.87, "curve": [0.858, 41.87, 0.975, -7.9]}, {"time": 1.0333, "value": -7.9, "curve": [1.167, -7.9, 1.433, 41.87]}, {"time": 1.5667, "value": 41.87, "curve": [1.675, 41.87, 1.892, -31.71]}, {"time": 2, "value": -31.71}]}, "Tentacle2_Mid2": {"rotate": [{"value": 5.37, "curve": [0.012, 15.12, 0.025, 26.96]}, {"time": 0.0333, "value": 26.96, "curve": [0.082, 1.18, 0.134, -30.13]}, {"time": 0.1667, "value": -30.13, "curve": [0.231, -30.13, 0.312, -37]}, {"time": 0.4, "value": -47.34, "curve": [0.485, -40.22, 0.575, -31.51]}, {"time": 0.6667, "value": -22.25, "curve": "stepped"}, {"time": 1.1333, "value": -22.25, "curve": [1.176, -9.09, 1.221, 7.01]}, {"time": 1.2667, "value": 24.14, "curve": [1.457, 44.47, 1.639, 62.45]}, {"time": 1.7667, "value": 62.45, "curve": [1.827, 62.45, 1.911, 46.14]}, {"time": 2, "value": 26.96}]}, "Spike9": {"rotate": [{"value": 41.81, "curve": [0.025, -22.06, 0.2, -22.06]}, {"time": 0.2667, "value": -22.06}]}, "Spike10": {"rotate": [{"value": 34.26, "curve": [0.01, -29.25, 0.075, -29.25]}, {"time": 0.1, "value": -29.25, "curve": [0.192, -29.25, 0.375, 17.23]}, {"time": 0.4667, "value": 17.23, "curve": [0.517, 17.23, 0.617, -37.55]}, {"time": 0.6667, "value": -37.55, "curve": [0.717, -37.55, 0.817, 26.87]}, {"time": 0.8667, "value": 26.87, "curve": [0.925, 26.87, 1.042, -2.19]}, {"time": 1.1, "value": -2.19}, {"time": 1.4, "value": 19.39}, {"time": 2, "value": 5.4}]}, "Tentacle3_Mid": {"rotate": [{"value": -3.27, "curve": [0.012, 13.03, 0.025, 36.14]}, {"time": 0.0333, "value": 36.14, "curve": [0.091, 11.69, 0.16, -29.86]}, {"time": 0.2, "value": -29.86, "curve": [0.258, -29.86, 0.326, -40.86]}, {"time": 0.4, "value": -58.98, "curve": [0.483, -52.2, 0.573, -42.75]}, {"time": 0.6667, "value": -32.1, "curve": "stepped"}, {"time": 1.1333, "value": -32.1, "curve": [1.175, -24.96, 1.22, -15.03]}, {"time": 1.2667, "value": -3.82, "curve": [1.503, 20.88, 1.771, 56.4]}, {"time": 1.9333, "value": 56.4, "curve": [1.953, 56.4, 1.976, 54.76]}, {"time": 2, "value": 51.86}]}, "Tentacle3_Mid2": {"rotate": [{"value": -1.59, "curve": [0.012, 15.91, 0.025, 40.71]}, {"time": 0.0333, "value": 40.71, "curve": [0.091, 14.47, 0.16, -30.13]}, {"time": 0.2, "value": -30.13, "curve": [0.258, -30.13, 0.326, -39.81]}, {"time": 0.4, "value": -55.75, "curve": [0.483, -48.61, 0.573, -38.67]}, {"time": 0.6667, "value": -27.45, "curve": "stepped"}, {"time": 1.1333, "value": -27.45, "curve": [1.175, -19.8, 1.22, -9.15]}, {"time": 1.2667, "value": 2.86, "curve": [1.496, 29.02, 1.745, 62.45]}, {"time": 1.9, "value": 62.45, "curve": [1.929, 62.45, 1.963, 58.81]}, {"time": 2, "value": 52.81}]}, "DangleHandle": {"rotate": [{"value": -15.4, "curve": [0.124, -4.37, 0.278, 16.3]}, {"time": 0.3667, "value": 16.3, "curve": [0.411, 16.3, 0.47, 12.3]}, {"time": 0.5333, "value": 6.7, "curve": [0.576, 3.66, 0.621, 0.04]}, {"time": 0.6667, "value": -3.58, "curve": [0.792, -13.51, 0.917, -23.45]}, {"time": 1, "value": -23.45, "curve": [1.046, -23.45, 1.103, -19.55]}, {"time": 1.1667, "value": -13.82, "curve": [1.373, -2.53, 1.621, 16.3]}, {"time": 1.7667, "value": 16.3, "curve": [1.829, 16.3, 1.911, 12.3]}, {"time": 2, "value": 6.7}], "translate": [{"x": -8.18, "y": 2.62, "curve": [0.099, 16.95, 0.201, 43.87, 0.099, 3.65, 0.201, 4.74]}, {"time": 0.2667, "x": 43.87, "y": 4.74, "curve": [0.333, 43.87, 0.433, 19.95, 0.333, 4.74, 0.433, 3.77]}, {"time": 0.5333, "x": -3.96, "y": 2.8, "curve": [0.658, -27.88, 0.783, -51.8, 0.658, 1.82, 0.783, 0.85]}, {"time": 0.8667, "x": -51.8, "y": 0.85, "curve": [0.942, -51.8, 1.053, -28.65, 0.942, 0.85, 1.053, 1.79]}, {"time": 1.1667, "x": -3.96, "y": 2.8, "curve": [1.329, 19.95, 1.492, 43.87, 1.329, 3.77, 1.492, 4.74]}, {"time": 1.6, "x": 43.87, "y": 4.74, "curve": [1.7, 43.87, 1.85, 19.95, 1.7, 4.74, 1.85, 3.77]}, {"time": 2, "x": -3.96, "y": 2.8}], "scale": [{"y": 0.313, "curve": [0.133, 1, 0.4, 1, 0.133, 0.313, 0.4, 1.723]}, {"time": 0.5333, "y": 1.723, "curve": [0.692, 1, 1.008, 1, 0.692, 1.723, 1.008, 0.313]}, {"time": 1.1667, "y": 0.313, "curve": [1.375, 1, 1.792, 1, 1.375, 0.313, 1.792, 1.723]}, {"time": 2, "y": 1.723}]}, "Mouth4": {"rotate": [{"value": 7.72, "curve": [0.003, 45.8, 0.1, 66.83]}, {"time": 0.1333, "value": 66.83, "curve": "stepped"}, {"time": 0.6333, "value": 66.83, "curve": [0.73, 3.02, 0.819, -32.72]}, {"time": 0.9, "value": -32.72, "curve": [0.958, -32.72, 1.075, 8.07]}, {"time": 1.1333, "value": 8.07, "curve": [1.267, 8.07, 1.533, -32.72]}, {"time": 1.6667, "value": -32.72, "curve": [1.75, -32.72, 1.917, 6.8]}, {"time": 2, "value": 6.8}]}, "Tentacle3_Mid4": {"rotate": [{"value": -1.59, "curve": [0.012, 15.91, 0.025, 40.71]}, {"time": 0.0333, "value": 40.71, "curve": [0.091, 14.47, 0.16, -30.13]}, {"time": 0.2, "value": -30.13, "curve": [0.258, -30.13, 0.326, -39.81]}, {"time": 0.4, "value": -55.75, "curve": [0.483, -43.97, 0.573, -27.56]}, {"time": 0.6667, "value": -9.06, "curve": "stepped"}, {"time": 1.1333, "value": -9.06, "curve": [1.175, -3.37, 1.22, 4.56]}, {"time": 1.2667, "value": 13.5, "curve": [1.478, 37.43, 1.692, 62.45]}, {"time": 1.8333, "value": 62.45, "curve": [1.878, 62.45, 1.936, 53.43]}, {"time": 2, "value": 40.71}]}, "Spike1": {"rotate": [{"value": -39.2, "curve": [0.01, 27.66, 0.075, 27.66]}, {"time": 0.1, "value": 27.66, "curve": [0.192, 27.66, 0.375, -20.49]}, {"time": 0.4667, "value": -20.49, "curve": [0.517, -20.49, 0.617, 22.03]}, {"time": 0.6667, "value": 22.03, "curve": [0.717, 22.03, 0.817, -46.34]}, {"time": 0.8667, "value": -46.34, "curve": [0.925, -46.34, 1.042, 4.65]}, {"time": 1.1, "value": 4.65}, {"time": 1.4, "value": -25.3}, {"time": 2, "value": -2.5}]}, "Tentacle3_Mid3": {"rotate": [{"value": -3.27, "curve": [0.012, 13.03, 0.025, 36.14]}, {"time": 0.0333, "value": 36.14, "curve": [0.091, 11.69, 0.16, -29.86]}, {"time": 0.2, "value": -29.86, "curve": [0.258, -29.86, 0.326, -40.86]}, {"time": 0.4, "value": -58.98, "curve": [0.483, -45.79, 0.573, -27.42]}, {"time": 0.6667, "value": -6.69, "curve": "stepped"}, {"time": 1.1333, "value": -6.69, "curve": [1.175, -2.28, 1.22, 3.86]}, {"time": 1.2667, "value": 10.79, "curve": [1.478, 33.09, 1.692, 56.4]}, {"time": 1.8333, "value": 56.4, "curve": [1.878, 56.4, 1.936, 48]}, {"time": 2, "value": 36.14}]}, "Mouth6": {"rotate": [{"value": -27.19, "curve": [0.003, -26.19, 0.1, -25.64]}, {"time": 0.1333, "value": -25.64, "curve": "stepped"}, {"time": 0.5333, "value": -25.64, "curve": [0.6, -25.64, 0.733, 44.64]}, {"time": 0.8, "value": 44.64, "curve": "stepped"}, {"time": 1.7667, "value": 44.64, "curve": [1.825, 44.64, 1.942, -25.64]}, {"time": 2, "value": -25.64}]}, "Tentacle3_Btm2": {"rotate": [{"value": -23.87, "curve": [0.011, 0.28, 0.025, 51.6]}, {"time": 0.0333, "value": 51.6, "curve": [0.148, 27.35, 0.311, -41.36]}, {"time": 0.4, "value": -41.36, "curve": [0.45, -41.36, 0.55, -77.29]}, {"time": 0.6, "value": -77.29, "curve": [0.642, -77.29, 0.758, -55.51]}, {"time": 0.7667, "value": -41.15, "curve": [0.874, -41.17, 0.999, -41.25]}, {"time": 1.1333, "value": -41.36, "curve": [1.17, -41.36, 1.216, -31.19]}, {"time": 1.2667, "value": -15.65, "curve": [1.509, 13.9, 1.797, 62.45]}, {"time": 1.9667, "value": 62.45, "curve": [1.977, 62.45, 1.988, 61.9]}, {"time": 2, "value": 60.88}]}, "Tentacle3_Btm4": {"rotate": [{"value": -23.87, "curve": [0.011, 0.28, 0.025, 51.6]}, {"time": 0.0333, "value": 51.6, "curve": [0.148, 27.35, 0.311, -41.36]}, {"time": 0.4, "value": -41.36, "curve": [0.45, -41.36, 0.55, -84.55]}, {"time": 0.6, "value": -84.55, "curve": [0.642, -84.55, 0.758, -63.82]}, {"time": 0.7667, "value": -50.14, "curve": [0.874, -50.17, 0.999, -50.25]}, {"time": 1.1333, "value": -50.35, "curve": [1.17, -50.35, 1.216, -32.17]}, {"time": 1.2667, "value": -4.36, "curve": [1.496, 24.97, 1.745, 62.45]}, {"time": 1.9, "value": 62.45, "curve": [1.929, 62.45, 1.963, 58.35]}, {"time": 2, "value": 51.6}]}, "Spike2": {"rotate": [{"value": -40.82, "curve": [0.016, 25.68, 0.125, 25.68]}, {"time": 0.1667, "value": 25.68}]}, "Spike6": {"rotate": [{"value": 39.38, "curve": [0.016, -22.77, 0.125, -22.77]}, {"time": 0.1667, "value": -22.77}]}, "Tentacle3_Btm": {"rotate": [{"value": -19.18, "curve": [0.011, 6.76, 0.025, 61.89]}, {"time": 0.0333, "value": 61.89, "curve": [0.148, 35.84, 0.311, -37.96]}, {"time": 0.4, "value": -37.96, "curve": [0.45, -37.96, 0.55, -72.93]}, {"time": 0.6, "value": -72.93, "curve": [0.642, -72.93, 0.758, -51.74]}, {"time": 0.7667, "value": -37.77, "curve": [0.874, -37.79, 0.999, -37.86]}, {"time": 1.1333, "value": -37.96, "curve": [1.17, -37.96, 1.216, -29.31]}, {"time": 1.2667, "value": -16.08, "curve": [1.513, 14.65, 1.822, 73.54]}, {"time": 2, "value": 73.54}]}, "Tentacle3_Btm3": {"rotate": [{"value": -19.18, "curve": [0.011, 6.76, 0.025, 61.89]}, {"time": 0.0333, "value": 61.89, "curve": [0.148, 35.84, 0.311, -37.96]}, {"time": 0.4, "value": -37.96, "curve": [0.45, -37.96, 0.55, -86.01]}, {"time": 0.6, "value": -86.01, "curve": [0.642, -86.01, 0.758, -66.71]}, {"time": 0.7667, "value": -53.98, "curve": [0.874, -54, 0.999, -54.07]}, {"time": 1.1333, "value": -54.18, "curve": [1.17, -54.18, 1.216, -32.06]}, {"time": 1.2667, "value": 1.77, "curve": [1.496, 33.28, 1.745, 73.54]}, {"time": 1.9, "value": 73.54, "curve": [1.929, 73.54, 1.963, 69.14]}, {"time": 2, "value": 61.89}]}, "Tentacle2_Btm": {"rotate": [{"value": -46.79, "curve": [0.011, -17.91, 0.025, 47.08]}, {"time": 0.0333, "value": 47.08, "curve": [0.142, 25.7, 0.31, -65.15]}, {"time": 0.4, "value": -65.15, "curve": [0.45, -65.15, 0.55, -100.12]}, {"time": 0.6, "value": -100.12, "curve": [0.642, -100.12, 0.758, -78.93]}, {"time": 0.7667, "value": -64.95, "curve": [0.874, -64.97, 0.999, -65.05]}, {"time": 1.1333, "value": -65.15, "curve": [1.171, -65.15, 1.216, -51.09]}, {"time": 1.2667, "value": -29.23, "curve": [1.503, 4.85, 1.771, 53.84]}, {"time": 1.9333, "value": 53.84, "curve": [1.953, 53.84, 1.976, 51.4]}, {"time": 2, "value": 47.08}]}, "Mouth5": {"rotate": [{"value": 20.26, "curve": [0.003, 70.32, 0.1, 97.94]}, {"time": 0.1333, "value": 97.94, "curve": "stepped"}, {"time": 0.5333, "value": 97.94, "curve": [0.634, 25.54, 0.725, -20.54]}, {"time": 0.8, "value": -20.54, "curve": [0.858, -20.54, 0.975, 20.24]}, {"time": 1.0333, "value": 20.24, "curve": [1.167, 20.24, 1.433, -20.54]}, {"time": 1.5667, "value": -20.54, "curve": [1.675, -20.54, 1.892, 18.98]}, {"time": 2, "value": 18.98}]}, "Spike3": {"rotate": [{"value": -46.31, "curve": [0.019, 18.03, 0.15, 18.03]}, {"time": 0.2, "value": 18.03}]}, "Spike7": {"rotate": [{"value": 43.76, "curve": [0.019, -16.22, 0.15, -16.22]}, {"time": 0.2, "value": -16.22}]}, "Tentacle2_Btm2": {"rotate": [{"value": -38.29, "curve": [0.011, -9.38, 0.025, 55.68]}, {"time": 0.0333, "value": 55.68, "curve": [0.142, 34.28, 0.31, -56.67]}, {"time": 0.4, "value": -56.67, "curve": [0.45, -56.67, 0.55, -92.61]}, {"time": 0.6, "value": -92.61, "curve": [0.642, -92.61, 0.758, -70.83]}, {"time": 0.7667, "value": -56.46, "curve": [0.874, -56.49, 0.999, -56.56]}, {"time": 1.1333, "value": -56.67, "curve": [1.171, -56.67, 1.216, -42.6]}, {"time": 1.2667, "value": -20.71, "curve": [1.503, 13.41, 1.771, 62.45]}, {"time": 1.9333, "value": 62.45, "curve": [1.953, 62.45, 1.976, 60]}, {"time": 2, "value": 55.68}]}, "Mouth7": {"rotate": [{"value": -40.38, "curve": [0.003, -69.46, 0.1, -85.51]}, {"time": 0.1333, "value": -85.51, "curve": "stepped"}, {"time": 0.6, "value": -85.51, "curve": [0.667, -85.51, 0.8, 38.19]}, {"time": 0.8667, "value": 38.19, "curve": [0.925, 38.19, 1.042, -11.57]}, {"time": 1.1, "value": -11.57, "curve": [1.233, -11.57, 1.5, 38.19]}, {"time": 1.6333, "value": 38.19, "curve": [1.725, 38.19, 1.908, -41.36]}, {"time": 2, "value": -41.36}]}, "Spike4": {"rotate": [{"value": -45.19, "curve": [0.022, 23.45, 0.175, 23.45]}, {"time": 0.2333, "value": 23.45}]}, "Spike8": {"rotate": [{"value": 42.87, "curve": [0.022, -21.41, 0.175, -21.41]}, {"time": 0.2333, "value": -21.41}]}, "Tentacle4": {"rotate": [{"value": -8.58, "curve": [0.012, 13.67, 0.023, 26.28]}, {"time": 0.0333, "value": 26.28, "curve": [0.122, 26.28, 0.273, 28.56]}, {"time": 0.4, "value": 29.99, "curve": [0.495, 16.61, 0.586, 5.49]}, {"time": 0.6667, "value": -0.96, "curve": "stepped"}, {"time": 1.1333, "value": -0.96, "curve": [1.181, -5.16, 1.226, -8.65]}, {"time": 1.2667, "value": -10.68, "curve": [1.45, -10.68, 1.817, 26.28]}, {"time": 2, "value": 26.28}]}, "Tentacle1": {"rotate": [{"value": 6.03, "curve": [0.012, -29.6, 0.023, -49.78]}, {"time": 0.0333, "value": -49.78, "curve": [0.122, -49.78, 0.273, -37.43]}, {"time": 0.4, "value": -29.68, "curve": [0.495, -19.51, 0.586, -11.07]}, {"time": 0.6667, "value": -6.17, "curve": "stepped"}, {"time": 1.1333, "value": -6.17, "curve": [1.181, 0.56, 1.226, 6.14]}, {"time": 1.2667, "value": 9.39, "curve": [1.45, 9.39, 1.817, -49.78]}, {"time": 2, "value": -49.78}]}, "MAIN": {"rotate": [{"curve": [0.017, 0, 0.05, 18.19]}, {"time": 0.0667, "value": 18.19, "curve": [0.1, 18.19, 0.167, -17.65]}, {"time": 0.2, "value": -17.65, "curve": [0.258, -17.65, 0.375, 8.72]}, {"time": 0.4333, "value": 8.72, "curve": [0.475, 8.72, 0.558, 0]}, {"time": 0.6}], "translate": [{"time": 0.0333, "y": -33.8, "curve": [0.033, 3.66, 0.133, 5.61, 0.033, -34.08, 0.133, -34.22]}, {"time": 0.2333, "x": 5.75, "y": -34.23, "curve": [0.373, 5.75, 0.533, 2.63, 0.373, -34.23, 0.533, -82.31]}, {"time": 0.5333, "y": -122.67, "curve": "stepped"}, {"time": 0.7667, "y": -122.67, "curve": [0.906, 0, 0.917, 0, 0.906, -122.67, 0.917, -120.62]}, {"time": 1.0667, "y": -120.57, "curve": [1.33, 0, 1.35, 0, 1.33, -120.57, 1.35, 78.26]}, {"time": 1.6333, "y": 83.45, "curve": [1.725, 0, 1.908, 0, 1.725, 83.45, 1.908, 17.53]}, {"time": 2, "y": 17.53}], "scale": [{"x": 1.509, "y": 0.663, "curve": [0, 0.959, 0.05, 0.76, 0, 1.189, 0.05, 1.379]}, {"time": 0.0667, "x": 0.76, "y": 1.379, "curve": [0.108, 0.76, 0.192, 1.267, 0.108, 1.379, 0.192, 0.835]}, {"time": 0.2333, "x": 1.267, "y": 0.835, "curve": [0.308, 1.267, 0.458, 0.937, 0.308, 0.835, 0.458, 1.05]}, {"time": 0.5333, "x": 0.937, "y": 1.05, "curve": [0.692, 0.937, 1.008, 0.987, 0.692, 1.05, 1.008, 1.006]}, {"time": 1.1667, "x": 0.987, "y": 1.006, "curve": [1.329, 1.013, 1.492, 1.038, 1.329, 0.984, 1.492, 0.962]}, {"time": 1.6, "x": 1.038, "y": 0.962, "curve": [1.7, 1.038, 1.85, 1.013, 1.7, 0.962, 1.85, 0.984]}, {"time": 2, "x": 0.987, "y": 1.006}]}, "Mouth2": {"scale": [{}, {"time": 0.0667, "x": 1.013, "y": 1.013, "curve": [0.068, 1.003, 0.117, 1, 0.068, 1.935, 0.117, 2.226]}, {"time": 0.1333, "y": 2.226, "curve": [0.233, 1, 0.433, 1.013, 0.233, 2.226, 0.433, 1.013]}, {"time": 0.5333, "x": 1.013, "y": 1.013}, {"time": 1.1667}]}, "root": {"scale": [{"time": 0.6, "curve": [0.65, 1, 0.75, 1.273, 0.65, 1, 0.75, 0.793]}, {"time": 0.8, "x": 1.273, "y": 0.793, "curve": [0.85, 1.273, 0.95, 0.921, 0.85, 0.793, 0.95, 1.108]}, {"time": 1, "x": 0.921, "y": 1.108, "curve": [1.083, 0.921, 1.25, 1.115, 1.083, 1.108, 1.25, 0.899]}, {"time": 1.3333, "x": 1.115, "y": 0.899, "curve": [1.458, 1.115, 1.708, 1, 1.458, 0.899, 1.708, 1]}, {"time": 1.8333}]}, "Head": {"translate": [{"time": 0.6667}, {"time": 0.8, "x": 0.41, "y": -22.88, "curve": "stepped"}, {"time": 1.0667, "x": 0.41, "y": -22.88, "curve": [1.167, 0.41, 1.367, 0, 1.167, -22.88, 1.367, 0]}, {"time": 1.4667}]}, "SpikerSpike1": {"scale": [{"x": 1.13, "curve": [0.017, 1.13, 0.062, 0.594, 0.017, 1, 0.062, 1]}, {"time": 0.0667, "x": 0.122}]}, "SpikerSpike8": {"scale": [{"x": 1.13, "curve": [0.017, 1.13, 0.062, 0.594, 0.017, 1, 0.062, 1]}, {"time": 0.0667, "x": 0.122}]}, "SpikerSpike2": {"scale": [{"x": 1.13, "curve": [0.017, 1.13, 0.062, 0.594, 0.017, 1, 0.062, 1]}, {"time": 0.0667, "x": 0.122}]}, "SpikerSpike7": {"scale": [{"x": 1.13, "curve": [0.017, 1.13, 0.062, 0.594, 0.017, 1, 0.062, 1]}, {"time": 0.0667, "x": 0.122}]}, "SpikerSpike3": {"scale": [{"x": 1.13, "curve": [0.017, 1.13, 0.062, 0.594, 0.017, 1, 0.062, 1]}, {"time": 0.0667, "x": 0.122}]}, "SpikerSpike4": {"scale": [{"x": 1.13, "curve": [0.017, 1.13, 0.062, 0.594, 0.017, 1, 0.062, 1]}, {"time": 0.0667, "x": 0.122}]}, "SpikerSpike5": {"scale": [{"x": 1.13, "curve": [0.017, 1.13, 0.062, 0.594, 0.017, 1, 0.062, 1]}, {"time": 0.0667, "x": 0.122}]}, "SpikerSpike6": {"scale": [{"x": 1.13, "curve": [0.017, 1.13, 0.062, 0.594, 0.017, 1, 0.062, 1]}, {"time": 0.0667, "x": 0.122}]}}, "drawOrder": [{"offsets": [{"slot": "images/Eye", "offset": 2}, {"slot": "images/Eye2", "offset": 2}]}]}, "hit-wall2": {"slots": {"Eyes": {"attachment": [{"name": "EyesClosed"}, {"time": 1, "name": "Eyes"}]}}, "bones": {"FACE": {"translate": [{"x": 0.84, "y": 4.16, "curve": [0.017, 0.84, 0.05, -43.79, 0.017, 4.16, 0.05, 61.87]}, {"time": 0.0667, "x": -43.79, "y": 61.87, "curve": [0.092, -43.79, 0.142, -14.31, 0.092, 61.87, 0.142, -54.6]}, {"time": 0.1667, "x": -14.31, "y": -54.6, "curve": [0.208, -14.31, 0.292, 18.99, 0.208, -54.6, 0.292, 43.61]}, {"time": 0.3333, "x": 18.99, "y": 43.61, "curve": [0.392, 18.99, 0.508, 11.5, 0.392, 43.61, 0.508, -26.98]}, {"time": 0.5667, "x": 11.5, "y": -26.98, "curve": [0.642, 11.5, 0.792, -7.97, 0.642, -26.98, 0.792, 4.27]}, {"time": 0.8667, "x": -7.97, "y": 4.27, "curve": [0.967, -7.97, 1.167, -21.84, 0.967, 4.27, 1.167, 4.16]}, {"time": 1.2667, "x": -21.84, "y": 4.16, "curve": [1.464, -21.84, 1.552, -5.93, 1.464, 4.16, 1.552, 4.16]}, {"time": 1.6667, "x": 7.05, "y": 4.16}], "scale": [{}, {"time": 0.0667, "x": 1.219, "y": 1.219}, {"time": 0.2}]}, "MAIN": {"rotate": [{"curve": [0.017, 0, 0.05, 18.19]}, {"time": 0.0667, "value": 18.19, "curve": [0.1, 18.19, 0.167, -17.65]}, {"time": 0.2, "value": -17.65, "curve": [0.25, -17.65, 0.35, 8.72]}, {"time": 0.4, "value": 8.72, "curve": [0.467, 8.72, 0.6, 0]}, {"time": 0.6667}], "translate": [{"y": -33.8, "curve": [0, 0, 0.25, 0, 0, -1.14, 0.25, 16.22]}, {"time": 0.5, "y": 17.53, "curve": [0.732, 0, 0.75, 0, 0.732, 17.53, 0.75, -89.61]}, {"time": 1, "y": -92.41, "curve": [1.31, 0, 1.333, 0, 1.31, -92.41, 1.333, 14.73]}, {"time": 1.6667, "y": 17.53}], "scale": [{"x": 1.509, "y": 0.663, "curve": [0, 0.959, 0.05, 0.76, 0, 1.189, 0.05, 1.379]}, {"time": 0.0667, "x": 0.76, "y": 1.379, "curve": [0.108, 0.76, 0.192, 1.267, 0.108, 1.379, 0.192, 0.835]}, {"time": 0.2333, "x": 1.267, "y": 0.835, "curve": [0.3, 1.267, 0.433, 0.937, 0.3, 0.835, 0.433, 1.05]}, {"time": 0.5, "x": 0.937, "y": 1.05, "curve": [0.625, 0.937, 0.875, 0.987, 0.625, 1.05, 0.875, 1.006]}, {"time": 1, "x": 0.987, "y": 1.006, "curve": [1.125, 1.013, 1.25, 1.038, 1.125, 0.984, 1.25, 0.962]}, {"time": 1.3333, "x": 1.038, "y": 0.962, "curve": [1.417, 1.038, 1.542, 1.013, 1.417, 0.962, 1.542, 0.984]}, {"time": 1.6667, "x": 0.987, "y": 1.006}]}, "Spike5": {"rotate": [{"value": -43.86, "curve": [0.025, 24.37, 0.2, 24.37]}, {"time": 0.2667, "value": 24.37, "curve": [0.333, 24.37, 0.467, -26.01]}, {"time": 0.5333, "value": -26.01, "curve": [0.617, -26.01, 0.783, 12.93]}, {"time": 0.8667, "value": 12.93, "curve": [0.942, 12.93, 1.092, -8.26]}, {"time": 1.1667, "value": -8.26, "curve": [1.23, -10.3, 1.287, -11.66]}, {"time": 1.3333, "value": -11.66, "curve": [1.414, -11.66, 1.556, -1.65]}, {"time": 1.6667, "value": 3.35}]}, "Spike4": {"rotate": [{"value": -45.19, "curve": [0.022, 23.45, 0.175, 23.45]}, {"time": 0.2333, "value": 23.45, "curve": [0.3, 23.45, 0.433, -27.61]}, {"time": 0.5, "value": -27.61, "curve": [0.583, -27.61, 0.75, 11.47]}, {"time": 0.8333, "value": 11.47, "curve": [0.908, 11.47, 1.058, -9.26]}, {"time": 1.1333, "value": -9.26, "curve": [1.183, -10.73, 1.229, -11.66]}, {"time": 1.2667, "value": -11.66, "curve": [1.364, -11.66, 1.539, -0.22]}, {"time": 1.6667, "value": 4.35}]}, "Spike3": {"rotate": [{"value": -46.31, "curve": [0.019, 18.03, 0.15, 18.03]}, {"time": 0.2, "value": 18.03, "curve": [0.267, 18.03, 0.4, -27.12]}, {"time": 0.4667, "value": -27.12, "curve": [0.55, -27.12, 0.717, 11.11]}, {"time": 0.8, "value": 11.11, "curve": [0.875, 11.11, 1.025, -10.13]}, {"time": 1.1, "value": -10.13, "curve": [1.243, -6.23, 1.453, 6.75]}, {"time": 1.5667, "value": 6.75, "curve": [1.596, 6.75, 1.63, 6.18]}, {"time": 1.6667, "value": 5.22}]}, "Spike2": {"rotate": [{"value": -40.82, "curve": [0.016, 25.68, 0.125, 25.68]}, {"time": 0.1667, "value": 25.68, "curve": [0.233, 25.68, 0.367, -21.95]}, {"time": 0.4333, "value": -21.95, "curve": [0.517, -21.95, 0.683, 16.45]}, {"time": 0.7667, "value": 16.45, "curve": [0.842, 16.45, 0.992, -4.89]}, {"time": 1.0667, "value": -4.89, "curve": [1.188, 0.28, 1.318, 6.75]}, {"time": 1.4, "value": 6.75, "curve": [1.469, 6.75, 1.565, 3.67]}, {"time": 1.6667, "value": -0.02}]}, "Spike1": {"rotate": [{"value": -39.2, "curve": [0.01, 27.66, 0.075, 27.66]}, {"time": 0.1, "value": 27.66, "curve": [0.167, 27.66, 0.3, -20.49]}, {"time": 0.3667, "value": -20.49, "curve": [0.45, -20.49, 0.617, 17.99]}, {"time": 0.7, "value": 17.99, "curve": [0.775, 17.99, 0.925, -2.45]}, {"time": 1, "value": -2.45, "curve": [1.125, 2.15, 1.25, 6.75]}, {"time": 1.3333, "value": 6.75, "curve": [1.417, 6.75, 1.542, 2.12]}, {"time": 1.6667, "value": -2.5}]}, "Spike10": {"rotate": [{"value": 34.26, "curve": [0.01, -29.25, 0.075, -29.25]}, {"time": 0.1, "value": -29.25, "curve": [0.167, -29.25, 0.3, 17.23]}, {"time": 0.3667, "value": 17.23, "curve": [0.45, 17.23, 0.617, -24.45]}, {"time": 0.7, "value": -24.45, "curve": [0.775, -24.45, 0.925, -2.18]}, {"time": 1, "value": -2.18, "curve": [1.076, -4.25, 1.146, -5.73]}, {"time": 1.2, "value": -5.73, "curve": [1.313, -5.73, 1.506, 1.22]}, {"time": 1.6667, "value": 5.4}]}, "Spike6": {"rotate": [{"value": 39.38, "curve": [0.016, -22.77, 0.125, -22.77]}, {"time": 0.1667, "value": -22.77, "curve": [0.233, -22.77, 0.367, 21.76]}, {"time": 0.4333, "value": 21.76, "curve": [0.517, 21.76, 0.683, -19.61]}, {"time": 0.7667, "value": -19.61, "curve": [0.842, -19.61, 0.992, 3.55]}, {"time": 1.0667, "value": 3.55, "curve": [1.188, -0.57, 1.318, -5.73]}, {"time": 1.4, "value": -5.73, "curve": [1.469, -5.73, 1.565, -3.28]}, {"time": 1.6667, "value": -0.33}]}, "Spike7": {"rotate": [{"value": 43.76, "curve": [0.019, -16.22, 0.15, -16.22]}, {"time": 0.2, "value": -16.22, "curve": [0.267, -16.22, 0.4, 26]}, {"time": 0.4667, "value": 26, "curve": [0.55, 26, 0.717, -15.29]}, {"time": 0.8, "value": -15.29, "curve": [0.875, -15.29, 1.025, 7.74]}, {"time": 1.1, "value": 7.74, "curve": [1.243, 4.63, 1.453, -5.73]}, {"time": 1.5667, "value": -5.73, "curve": [1.596, -5.73, 1.63, -5.28]}, {"time": 1.6667, "value": -4.52}]}, "Spike8": {"rotate": [{"value": 42.87, "curve": [0.022, -21.41, 0.175, -21.41]}, {"time": 0.2333, "value": -21.41, "curve": [0.3, -21.41, 0.433, 26.26]}, {"time": 0.5, "value": 26.26, "curve": [0.583, 26.26, 0.75, -15.65]}, {"time": 0.8333, "value": -15.65, "curve": [0.908, -15.65, 1.058, 7.05]}, {"time": 1.1333, "value": 7.05, "curve": [1.183, 8.22, 1.229, 8.96]}, {"time": 1.2667, "value": 8.96, "curve": [1.364, 8.96, 1.539, -0.17]}, {"time": 1.6667, "value": -3.82}]}, "Spike9": {"rotate": [{"value": 41.81, "curve": [0.025, -22.06, 0.2, -22.06]}, {"time": 0.2667, "value": -22.06, "curve": [0.333, -22.06, 0.467, 24.98]}, {"time": 0.5333, "value": 24.98, "curve": [0.617, 24.98, 0.783, -16.81]}, {"time": 0.8667, "value": -16.81, "curve": [0.942, -16.81, 1.092, 6.25]}, {"time": 1.1667, "value": 6.25, "curve": [1.23, 7.87, 1.287, 8.96]}, {"time": 1.3333, "value": 8.96, "curve": [1.414, 8.96, 1.556, 0.97]}, {"time": 1.6667, "value": -3.02}]}, "Tentacle5": {"rotate": [{"value": 6.03, "curve": [0.024, 8.17, 0.047, 9.39]}, {"time": 0.0667, "value": 9.39, "curve": [0.175, 9.39, 0.392, -49.78]}, {"time": 0.5, "value": -49.78, "curve": [0.622, -49.78, 0.852, -5.1]}, {"time": 1, "value": 5.41, "curve": [1.037, 7.94, 1.07, 9.39]}, {"time": 1.1, "value": 9.39, "curve": [1.242, 9.39, 1.525, -49.78]}, {"time": 1.6667, "value": -49.78}]}, "Tentacle3_Mid": {"rotate": [{"value": -3.27, "curve": [0.13, 21.42, 0.277, 56.4]}, {"time": 0.3667, "value": 56.4, "curve": [0.403, 56.4, 0.449, 48]}, {"time": 0.5, "value": 36.14, "curve": [0.603, 11.69, 0.727, -29.86]}, {"time": 0.8, "value": -29.86, "curve": [0.852, -29.86, 0.924, -18.51]}, {"time": 1, "value": -3.82, "curve": [1.212, 20.88, 1.454, 56.4]}, {"time": 1.6, "value": 56.4, "curve": [1.62, 56.4, 1.642, 54.76]}, {"time": 1.6667, "value": 51.86}]}, "Tentacle3_Btm": {"rotate": [{"value": -19.18, "curve": [0.143, 10.49, 0.328, 73.54]}, {"time": 0.4333, "value": 73.54, "curve": [0.453, 73.54, 0.475, 69.14]}, {"time": 0.5, "value": 61.89, "curve": [0.615, 35.84, 0.778, -37.96]}, {"time": 0.8667, "value": -37.96, "curve": [0.904, -37.96, 0.95, -29.31]}, {"time": 1, "value": -16.08, "curve": [1.224, 14.65, 1.505, 73.54]}, {"time": 1.6667, "value": 73.54}]}, "Tentacle6": {"rotate": [{"value": -8.02, "curve": [0.024, -9.72, 0.047, -10.68]}, {"time": 0.0667, "value": -10.68, "curve": [0.175, -10.68, 0.392, 36.14]}, {"time": 0.5, "value": 36.14, "curve": [0.622, 36.14, 0.852, 0.78]}, {"time": 1, "value": -7.54, "curve": [1.037, -9.53, 1.07, -10.68]}, {"time": 1.1, "value": -10.68, "curve": [1.242, -10.68, 1.525, 36.14]}, {"time": 1.6667, "value": 36.14}]}, "Tentacle3_Mid2": {"rotate": [{"value": -1.59, "curve": [0.13, 24.91, 0.277, 62.45]}, {"time": 0.3667, "value": 62.45, "curve": [0.403, 62.45, 0.449, 53.43]}, {"time": 0.5, "value": 40.71, "curve": [0.603, 14.47, 0.727, -30.13]}, {"time": 0.8, "value": -30.13, "curve": [0.852, -30.13, 0.924, -17.95]}, {"time": 1, "value": -2.18, "curve": [1.201, 24.33, 1.429, 62.45]}, {"time": 1.5667, "value": 62.45, "curve": [1.596, 62.45, 1.629, 58.81]}, {"time": 1.6667, "value": 52.81}]}, "Tentacle3_Btm2": {"rotate": [{"value": -23.87, "curve": [0.143, 3.75, 0.328, 62.45]}, {"time": 0.4333, "value": 62.45, "curve": [0.453, 62.45, 0.475, 58.35]}, {"time": 0.5, "value": 51.6, "curve": [0.615, 27.35, 0.778, -41.36]}, {"time": 0.8667, "value": -41.36, "curve": [0.904, -41.36, 0.95, -33.3]}, {"time": 1, "value": -20.98, "curve": [1.213, 7.62, 1.48, 62.45]}, {"time": 1.6333, "value": 62.45, "curve": [1.644, 62.45, 1.655, 61.9]}, {"time": 1.6667, "value": 60.88}]}, "Tentacle2_Btm2": {"rotate": [{"value": -38.29, "curve": [0.152, -7.3, 0.353, 62.45]}, {"time": 0.4667, "value": 62.45, "curve": [0.477, 62.45, 0.488, 60]}, {"time": 0.5, "value": 55.68, "curve": [0.608, 34.28, 0.777, -56.67]}, {"time": 0.8667, "value": -56.67, "curve": [0.904, -56.67, 0.95, -49.72]}, {"time": 1, "value": -38.92, "curve": [1.195, -8.2, 1.454, 62.45]}, {"time": 1.6, "value": 62.45, "curve": [1.62, 62.45, 1.642, 60]}, {"time": 1.6667, "value": 55.68}]}, "Tentacle2_Mid2": {"rotate": [{"value": 5.37, "curve": [0.121, 31.15, 0.251, 62.45]}, {"time": 0.3333, "value": 62.45, "curve": [0.376, 62.45, 0.437, 46.14]}, {"time": 0.5, "value": 26.96, "curve": [0.597, 1.18, 0.701, -30.13]}, {"time": 0.7667, "value": -30.13, "curve": [0.827, -30.13, 0.911, -12.53]}, {"time": 1, "value": 8.18, "curve": [1.159, 33.42, 1.327, 62.45]}, {"time": 1.4333, "value": 62.45, "curve": [1.493, 62.45, 1.578, 46.14]}, {"time": 1.6667, "value": 26.96}]}, "Tentacle4": {"rotate": [{"value": -8.58, "curve": [0.024, -9.92, 0.047, -10.68]}, {"time": 0.0667, "value": -10.68, "curve": [0.175, -10.68, 0.392, 26.28]}, {"time": 0.5, "value": 26.28, "curve": [0.622, 26.28, 0.852, -1.63]}, {"time": 1, "value": -8.2, "curve": [1.037, -9.77, 1.07, -10.68]}, {"time": 1.1, "value": -10.68, "curve": [1.242, -10.68, 1.525, 26.28]}, {"time": 1.6667, "value": 26.28}]}, "Tentacle2_Btm": {"rotate": [{"value": -46.79, "curve": [0.152, -15.83, 0.353, 53.84]}, {"time": 0.4667, "value": 53.84, "curve": [0.477, 53.84, 0.488, 51.4]}, {"time": 0.5, "value": 47.08, "curve": [0.608, 25.7, 0.777, -65.15]}, {"time": 0.8667, "value": -65.15, "curve": [0.904, -65.15, 0.95, -58.21]}, {"time": 1, "value": -47.42, "curve": [1.195, -16.73, 1.454, 53.84]}, {"time": 1.6, "value": 53.84, "curve": [1.62, 53.84, 1.642, 51.4]}, {"time": 1.6667, "value": 47.08}]}, "Tentacle2_Mid": {"rotate": [{"value": -3.22, "curve": [0.121, 20.8, 0.251, 49.96]}, {"time": 0.3333, "value": 49.96, "curve": [0.376, 49.96, 0.437, 34.77]}, {"time": 0.5, "value": 16.89, "curve": [0.597, -7.13, 0.701, -36.3]}, {"time": 0.7667, "value": -36.3, "curve": [0.827, -36.3, 0.911, -19.9]}, {"time": 1, "value": -0.6, "curve": [1.159, 22.92, 1.327, 49.96]}, {"time": 1.4333, "value": 49.96, "curve": [1.493, 49.96, 1.578, 34.77]}, {"time": 1.6667, "value": 16.89}]}, "Tentacle3": {"rotate": [{"value": 4.21, "curve": [0.024, 5.78, 0.047, 6.68]}, {"time": 0.0667, "value": 6.68, "curve": [0.175, 6.68, 0.392, -36.75]}, {"time": 0.5, "value": -36.75, "curve": [0.622, -36.75, 0.852, -3.96]}, {"time": 1, "value": 3.76, "curve": [1.037, 5.61, 1.07, 6.68]}, {"time": 1.1, "value": 6.68, "curve": [1.242, 6.68, 1.525, -36.75]}, {"time": 1.6667, "value": -36.75}]}, "DangleHandle": {"rotate": [{"value": -15.4, "curve": [0.124, -4.37, 0.278, 16.3]}, {"time": 0.3667, "value": 16.3, "curve": [0.403, 16.3, 0.449, 12.3]}, {"time": 0.5, "value": 6.7, "curve": [0.532, 3.66, 0.566, 0.04]}, {"time": 0.6, "value": -3.58, "curve": [0.7, -13.51, 0.8, -23.45]}, {"time": 0.8667, "value": -23.45, "curve": [0.903, -23.45, 0.949, -19.55]}, {"time": 1, "value": -13.82, "curve": [1.161, -2.53, 1.353, 16.3]}, {"time": 1.4667, "value": 16.3, "curve": [1.52, 16.3, 1.59, 12.3]}, {"time": 1.6667, "value": 6.7}], "translate": [{"x": -8.18, "y": 2.62, "curve": [0.099, 16.95, 0.201, 43.87, 0.099, 3.65, 0.201, 4.74]}, {"time": 0.2667, "x": 43.87, "y": 4.74, "curve": [0.325, 43.87, 0.413, 19.95, 0.325, 4.74, 0.413, 3.77]}, {"time": 0.5, "x": -3.96, "y": 2.8, "curve": [0.6, -27.88, 0.7, -51.8, 0.6, 1.82, 0.7, 0.85]}, {"time": 0.7667, "x": -51.8, "y": 0.85, "curve": [0.826, -51.8, 0.912, -28.65, 0.826, 0.85, 0.912, 1.79]}, {"time": 1, "x": -3.96, "y": 2.8, "curve": [1.125, 19.95, 1.25, 43.87, 1.125, 3.77, 1.25, 4.74]}, {"time": 1.3333, "x": 43.87, "y": 4.74, "curve": [1.417, 43.87, 1.542, 19.95, 1.417, 4.74, 1.542, 3.77]}, {"time": 1.6667, "x": -3.96, "y": 2.8}], "scale": [{"y": 0.313, "curve": [0.125, 1, 0.375, 1, 0.125, 0.313, 0.375, 1.723]}, {"time": 0.5, "y": 1.723, "curve": [0.625, 1, 0.875, 1, 0.625, 1.723, 0.875, 0.313]}, {"time": 1, "y": 0.313, "curve": [1.167, 1, 1.5, 1, 1.167, 0.313, 1.5, 1.723]}, {"time": 1.6667, "y": 1.723}]}, "Tentacles": {"scale": [{"x": 1.199}, {"time": 0.5, "x": 0.699}, {"time": 1, "x": 1.199}, {"time": 1.6667, "x": 0.699}]}, "Mouth": {"translate": [{}, {"time": 0.0667, "x": 8.27, "y": 49.14, "curve": [0.175, 8.27, 0.392, 0, 0.175, 49.14, 0.392, -10.97]}, {"time": 0.5, "y": -10.97, "curve": [0.625, 0, 0.875, 0, 0.625, -10.97, 0.875, 4.02]}, {"time": 1, "y": 4.02, "curve": [1.167, 0, 1.5, 0, 1.167, 4.02, 1.5, -10.97]}, {"time": 1.6667, "y": -10.97}], "scale": [{"curve": [0.001, 1, 0.05, 1, 0.001, 2.216, 0.05, 2.599]}, {"time": 0.0667, "y": 2.599, "curve": [0.175, 1, 0.392, 1, 0.175, 2.599, 0.392, 1]}, {"time": 0.5}]}, "Mouth8": {"rotate": [{"value": -32.57, "curve": [0.025, -38.95, 0.049, -43.41]}, {"time": 0.0667, "value": -43.41, "curve": [0.133, -43.41, 0.267, 4.89]}, {"time": 0.3333, "value": 4.89, "curve": [0.374, 4.89, 0.443, -17.99]}, {"time": 0.5, "value": -31.71, "curve": [0.525, -38.54, 0.549, -43.41]}, {"time": 0.5667, "value": -43.41, "curve": [0.633, -43.41, 0.767, 4.89]}, {"time": 0.8333, "value": 4.89, "curve": [0.874, 4.89, 0.943, -18.4]}, {"time": 1, "value": -31.71, "curve": [1.038, -38.54, 1.073, -43.41]}, {"time": 1.1, "value": -43.41, "curve": [1.183, -43.41, 1.35, 4.89]}, {"time": 1.4333, "value": 4.89, "curve": [1.49, 4.89, 1.586, -17.99]}, {"time": 1.6667, "value": -31.71}]}, "Mouth5": {"rotate": [{"value": 20.26, "curve": [0.061, 15.29, 0.151, -2.12]}, {"time": 0.2, "value": -2.12, "curve": [0.267, -2.12, 0.4, 22.13]}, {"time": 0.4667, "value": 22.13, "curve": [0.476, 22.13, 0.488, 20.92]}, {"time": 0.5, "value": 18.98, "curve": [0.564, 12.95, 0.651, -2.12]}, {"time": 0.7, "value": -2.12, "curve": [0.767, -2.12, 0.9, 22.13]}, {"time": 0.9667, "value": 22.13, "curve": [0.977, 22.13, 0.988, 20.97]}, {"time": 1, "value": 18.98, "curve": [1.085, 12.95, 1.202, -2.12]}, {"time": 1.2667, "value": -2.12, "curve": [1.35, -2.12, 1.517, 22.13]}, {"time": 1.6, "value": 22.13, "curve": [1.619, 22.13, 1.642, 20.92]}, {"time": 1.6667, "value": 18.98}]}, "Mouth4": {"rotate": [{"value": 7.72, "curve": [0.038, 2.47, 0.074, -2.12]}, {"time": 0.1, "value": -2.12, "curve": [0.167, -2.12, 0.3, 22.13]}, {"time": 0.3667, "value": 22.13, "curve": [0.399, 22.13, 0.452, 13.62]}, {"time": 0.5, "value": 6.8, "curve": [0.538, 1.94, 0.574, -2.12]}, {"time": 0.6, "value": -2.12, "curve": [0.667, -2.12, 0.8, 22.13]}, {"time": 0.8667, "value": 22.13, "curve": [0.899, 22.13, 0.951, 13.88]}, {"time": 1, "value": 6.8, "curve": [1.051, 1.94, 1.099, -2.12]}, {"time": 1.1333, "value": -2.12, "curve": [1.217, -2.12, 1.383, 22.13]}, {"time": 1.4667, "value": 22.13, "curve": [1.516, 22.13, 1.594, 13.62]}, {"time": 1.6667, "value": 6.8}]}, "Mouth7": {"rotate": [{"value": -40.38, "curve": [0.069, -31.32, 0.176, 4.89]}, {"time": 0.2333, "value": 4.89, "curve": [0.292, 4.89, 0.408, -43.41]}, {"time": 0.4667, "value": -43.41, "curve": [0.477, -43.41, 0.488, -42.68]}, {"time": 0.5, "value": -41.36, "curve": [0.567, -33.65, 0.676, 4.89]}, {"time": 0.7333, "value": 4.89, "curve": [0.792, 4.89, 0.908, -43.41]}, {"time": 0.9667, "value": -43.41, "curve": [0.977, -43.41, 0.988, -42.66]}, {"time": 1, "value": -41.36, "curve": [1.087, -33.65, 1.226, 4.89]}, {"time": 1.3, "value": 4.89, "curve": [1.383, 4.89, 1.55, -43.41]}, {"time": 1.6333, "value": -43.41, "curve": [1.643, -43.41, 1.655, -42.68]}, {"time": 1.6667, "value": -41.36}]}, "Mouth3": {"rotate": [{"value": -2.12, "curve": [0.067, -2.12, 0.2, 22.13]}, {"time": 0.2667, "value": 22.13, "curve": [0.325, 22.13, 0.442, -2.12]}, {"time": 0.5, "value": -2.12, "curve": [0.567, -2.12, 0.7, 22.13]}, {"time": 0.7667, "value": 22.13, "curve": [0.825, 22.13, 0.942, -2.12]}, {"time": 1, "value": -2.12, "curve": [1.083, -2.12, 1.25, 22.13]}, {"time": 1.3333, "value": 22.13, "curve": [1.417, 22.13, 1.583, -2.12]}, {"time": 1.6667, "value": -2.12}]}, "Mouth6": {"rotate": [{"value": -27.19, "curve": [0.06, -13.44, 0.126, 4.89]}, {"time": 0.1667, "value": 4.89, "curve": [0.225, 4.89, 0.342, -43.41]}, {"time": 0.4, "value": -43.41, "curve": [0.426, -43.41, 0.462, -35.33]}, {"time": 0.5, "value": -25.64, "curve": [0.56, -12.07, 0.626, 4.89]}, {"time": 0.6667, "value": 4.89, "curve": [0.725, 4.89, 0.842, -43.41]}, {"time": 0.9, "value": -43.41, "curve": [0.926, -43.41, 0.962, -35.51]}, {"time": 1, "value": -25.64, "curve": [1.073, -12.07, 1.151, 4.89]}, {"time": 1.2, "value": 4.89, "curve": [1.283, 4.89, 1.45, -43.41]}, {"time": 1.5333, "value": -43.41, "curve": [1.568, -43.41, 1.616, -35.33]}, {"time": 1.6667, "value": -25.64}]}, "Mouth2": {"scale": [{}, {"time": 0.0667, "x": 1.013, "y": 1.013, "curve": [0.068, 1.003, 0.117, 1, 0.068, 1.935, 0.117, 2.226]}, {"time": 0.1333, "y": 2.226, "curve": [0.225, 1, 0.408, 1.013, 0.225, 2.226, 0.408, 1.013]}, {"time": 0.5, "x": 1.013, "y": 1.013}, {"time": 1}]}, "Tentacle3_Btm3": {"rotate": [{"value": -19.18, "curve": [0.143, 10.49, 0.328, 73.54]}, {"time": 0.4333, "value": 73.54, "curve": [0.453, 73.54, 0.475, 69.14]}, {"time": 0.5, "value": 61.89, "curve": [0.615, 35.84, 0.778, -37.96]}, {"time": 0.8667, "value": -37.96, "curve": [0.904, -37.96, 0.95, -29.31]}, {"time": 1, "value": -16.08, "curve": [1.19, 14.65, 1.429, 73.54]}, {"time": 1.5667, "value": 73.54, "curve": [1.596, 73.54, 1.629, 69.14]}, {"time": 1.6667, "value": 61.89}]}, "Tentacle3_Mid3": {"rotate": [{"value": -3.27, "curve": [0.13, 21.42, 0.277, 56.4]}, {"time": 0.3667, "value": 56.4, "curve": [0.403, 56.4, 0.449, 48]}, {"time": 0.5, "value": 36.14, "curve": [0.603, 11.69, 0.727, -29.86]}, {"time": 0.8, "value": -29.86, "curve": [0.852, -29.86, 0.924, -18.51]}, {"time": 1, "value": -3.82, "curve": [1.177, 20.88, 1.378, 56.4]}, {"time": 1.5, "value": 56.4, "curve": [1.545, 56.4, 1.603, 48]}, {"time": 1.6667, "value": 36.14}]}, "Tentacle1": {"rotate": [{"value": 6.03, "curve": [0.024, 8.17, 0.047, 9.39]}, {"time": 0.0667, "value": 9.39, "curve": [0.175, 9.39, 0.392, -49.78]}, {"time": 0.5, "value": -49.78, "curve": [0.622, -49.78, 0.852, -5.1]}, {"time": 1, "value": 5.41, "curve": [1.037, 7.94, 1.07, 9.39]}, {"time": 1.1, "value": 9.39, "curve": [1.242, 9.39, 1.525, -49.78]}, {"time": 1.6667, "value": -49.78}]}, "Tentacle3_Btm4": {"rotate": [{"value": -23.87, "curve": [0.143, 3.75, 0.328, 62.45]}, {"time": 0.4333, "value": 62.45, "curve": [0.453, 62.45, 0.475, 58.35]}, {"time": 0.5, "value": 51.6, "curve": [0.615, 27.35, 0.778, -41.36]}, {"time": 0.8667, "value": -41.36, "curve": [0.904, -41.36, 0.95, -33.3]}, {"time": 1, "value": -20.98, "curve": [1.19, 7.62, 1.429, 62.45]}, {"time": 1.5667, "value": 62.45, "curve": [1.596, 62.45, 1.629, 58.35]}, {"time": 1.6667, "value": 51.6}]}, "Tentacle3_Mid4": {"rotate": [{"value": -1.59, "curve": [0.13, 24.91, 0.277, 62.45]}, {"time": 0.3667, "value": 62.45, "curve": [0.403, 62.45, 0.449, 53.43]}, {"time": 0.5, "value": 40.71, "curve": [0.603, 14.47, 0.727, -30.13]}, {"time": 0.8, "value": -30.13, "curve": [0.852, -30.13, 0.924, -17.95]}, {"time": 1, "value": -2.18, "curve": [1.177, 24.33, 1.378, 62.45]}, {"time": 1.5, "value": 62.45, "curve": [1.545, 62.45, 1.603, 53.43]}, {"time": 1.6667, "value": 40.71}]}, "Tentacle2": {"rotate": [{"value": -8.02, "curve": [0.024, -9.72, 0.047, -10.68]}, {"time": 0.0667, "value": -10.68, "curve": [0.175, -10.68, 0.392, 36.14]}, {"time": 0.5, "value": 36.14, "curve": [0.622, 36.14, 0.852, 0.78]}, {"time": 1, "value": -7.54, "curve": [1.037, -9.53, 1.07, -10.68]}, {"time": 1.1, "value": -10.68, "curve": [1.242, -10.68, 1.525, 36.14]}, {"time": 1.6667, "value": 36.14}]}}, "drawOrder": [{"offsets": [{"slot": "images/Eye", "offset": 2}, {"slot": "images/Eye2", "offset": 2}]}]}, "intro": {"slots": {"Eyes": {"attachment": [{"time": 0.4, "name": "EyesClosed"}, {"time": 1.5, "name": "Eyes"}]}, "images/Eye": {"attachment": [{"time": 0.4, "name": "Eye"}, {"time": 1.7}]}, "images/Eye2": {"attachment": [{"time": 0.4, "name": "Eye"}, {"time": 1.7}]}, "images/Eye3": {"attachment": [{}, {"time": 0.4, "name": "Eye"}, {"time": 1.7}]}, "Mouth": {"attachment": [{"time": 0.1333, "name": "Mouth Closed"}, {"time": 0.3333, "name": "Mouth Open"}, {"time": 1.7, "name": "Mouth Closed"}]}, "MouthBack": {"attachment": [{"time": 0.1333, "name": "Mouth Back Shut"}, {"time": 0.3333, "name": "Mouth Back"}, {"time": 1.7, "name": "Mouth Back Shut"}, {"time": 1.9333, "name": "Mouth Back"}]}, "ScuttleEye1": {"attachment": [{"time": 0.4, "name": "<PERSON><PERSON><PERSON>_<PERSON>_Back"}, {"time": 1.7, "name": "<PERSON><PERSON><PERSON>_Eye_Closed"}]}, "ScuttleEye2": {"attachment": [{"time": 0.4, "name": "<PERSON><PERSON><PERSON>_<PERSON>_Back"}, {"time": 1.7, "name": "<PERSON><PERSON><PERSON>_Eye_Closed"}]}, "ScuttleEye3": {"attachment": [{"name": "<PERSON><PERSON><PERSON>_Eye_Closed"}, {"time": 0.4, "name": "<PERSON><PERSON><PERSON>_<PERSON>_Back"}, {"time": 1.7, "name": "<PERSON><PERSON><PERSON>_Eye_Closed"}]}}, "bones": {"FACE": {"rotate": [{"time": 0.3333, "curve": [0.408, 0, 0.558, 4.2]}, {"time": 0.6333, "value": 4.2, "curve": [0.717, 4.2, 0.883, -8.79]}, {"time": 0.9667, "value": -8.79, "curve": [1.083, -8.79, 1.317, 5.89]}, {"time": 1.4333, "value": 5.89, "curve": [1.5, 5.89, 1.633, 6.93]}, {"time": 1.7, "value": 6.93, "curve": [1.758, 6.93, 1.875, 0]}, {"time": 1.9333}], "translate": [{"x": 7.05, "y": 4.16, "curve": [0.083, 7.05, 0.157, 53.06, 0.083, 4.16, 0.157, 3.8]}, {"time": 0.3333, "x": 53.06, "y": 3.8, "curve": [0.395, 53.06, 0.4, -0.48, 0.395, 3.8, 0.4, 3.24]}, {"time": 0.4667, "x": -1.88, "y": 3.23, "curve": [0.631, -1.88, 0.704, -5.56, 0.631, 3.23, 0.704, -11.74]}, {"time": 0.8, "x": -8.55, "y": -23.94, "curve": [0.9, 1.93, 1.016, 10.84, 0.9, 7.85, 1.016, 34.86]}, {"time": 1.2, "x": 11.4, "y": 36.56, "curve": [1.401, 11.4, 1.417, -2.63, 1.401, 36.56, 1.417, -10]}, {"time": 1.6333, "x": -3, "y": -11.21, "curve": [1.725, -3, 1.908, 7.05, 1.725, -11.21, 1.908, 4.16]}, {"time": 2, "x": 7.05, "y": 4.16}]}, "ScuttleEye1": {"scale": [{"time": 0.4, "x": 0.917, "y": 0.917, "curve": "stepped"}, {"time": 1.7}]}, "ScuttleEye2": {"scale": [{"time": 0.4, "x": 0.917, "y": 0.917, "curve": "stepped"}, {"time": 1.7}]}, "ScuttleEye3": {"scale": [{"time": 0.4, "x": 0.917, "y": 0.917, "curve": "stepped"}, {"time": 1.7}]}, "ScuttleEyeball3": {"translate": [{"y": 0.19, "curve": [0.05, 0, 0.15, 0, 0.05, 0.19, 0.15, 2.38]}, {"time": 0.2, "y": 2.38, "curve": [0.325, 0, 0.575, 0, 0.325, 2.38, 0.575, -3.57]}, {"time": 0.7, "y": -3.57, "curve": [0.774, 0, 0.891, 0, 0.774, -3.57, 0.891, -1.48]}, {"time": 1, "y": 0.19, "curve": [1.076, 0, 1.148, 0, 1.076, 1.38, 1.148, 2.38]}, {"time": 1.2, "y": 2.38, "curve": [1.325, 0, 1.575, 0, 1.325, 2.38, 1.575, -3.57]}, {"time": 1.7, "y": -3.57, "curve": [1.774, 0, 1.891, 0, 1.774, -3.57, 1.891, -1.48]}, {"time": 2, "y": 0.19}]}, "MAIN": {"translate": [{"y": 17.53, "curve": [0.083, 0, 0.25, 0, 0.083, 17.53, 0.25, -93.12]}, {"time": 0.3333, "y": -93.12, "curve": [0.426, 0, 0.433, 0, 0.426, -93.12, 0.433, 14.71]}, {"time": 0.5333, "y": 17.53, "curve": [0.982, 0, 1.017, 0, 0.982, 17.53, 1.017, 29.84]}, {"time": 1.5, "y": 30.16, "curve": [1.558, 0, 1.675, 0, 1.558, 30.16, 1.675, -77.91]}, {"time": 1.7333, "y": -77.91, "curve": [1.8, 0, 1.933, 0, 1.8, -77.91, 1.933, 17.53]}, {"time": 2, "y": 17.53}], "scale": [{"x": 0.987, "y": 1.006, "curve": [0.025, 0.987, 0.075, 1.145, 0.025, 1.006, 0.075, 0.94]}, {"time": 0.1, "x": 1.145, "y": 0.94, "curve": [0.158, 1.145, 0.275, 0.929, 0.158, 0.94, 0.275, 1.086]}, {"time": 0.3333, "x": 0.929, "y": 1.086, "curve": [0.383, 0.929, 0.483, 0.987, 0.383, 1.086, 0.483, 1.006]}, {"time": 0.5333, "x": 0.987, "y": 1.006, "curve": [0.575, 0.987, 0.658, 0.937, 0.575, 1.006, 0.658, 1.05]}, {"time": 0.7, "x": 0.937, "y": 1.05, "curve": [0.767, 0.937, 0.9, 1, 0.767, 1.05, 0.9, 1]}, {"time": 0.9667, "curve": [1.585, 1, 1.511, 0.987, 1.585, 1, 1.511, 1.006]}, {"time": 2, "x": 0.987, "y": 1.006}]}, "Spike5": {"rotate": [{"value": 3.35, "curve": [0.092, 3.35, 0.275, 38.65]}, {"time": 0.3667, "value": 38.65, "curve": [0.4, 38.65, 0.467, -10.74]}, {"time": 0.5, "value": -10.74, "curve": [0.517, -10.74, 0.55, -15.89]}, {"time": 0.5667, "value": -15.89, "curve": [0.583, -15.89, 0.617, -10.74]}, {"time": 0.6333, "value": -10.74, "curve": [0.65, -10.74, 0.683, -15.89]}, {"time": 0.7, "value": -15.89, "curve": [0.717, -15.89, 0.75, -10.74]}, {"time": 0.7667, "value": -10.74, "curve": [0.783, -10.74, 0.817, -15.89]}, {"time": 0.8333, "value": -15.89, "curve": [0.85, -15.89, 0.883, -10.74]}, {"time": 0.9, "value": -10.74, "curve": [0.917, -10.74, 0.95, -15.89]}, {"time": 0.9667, "value": -15.89, "curve": [0.983, -15.89, 1.017, -10.74]}, {"time": 1.0333, "value": -10.74, "curve": [1.05, -10.74, 1.083, -15.89]}, {"time": 1.1, "value": -15.89, "curve": [1.117, -15.89, 1.15, -10.74]}, {"time": 1.1667, "value": -10.74, "curve": [1.183, -10.74, 1.217, -15.89]}, {"time": 1.2333, "value": -15.89, "curve": [1.25, -15.89, 1.283, -10.74]}, {"time": 1.3, "value": -10.74, "curve": [1.317, -10.74, 1.35, -15.89]}, {"time": 1.3667, "value": -15.89, "curve": [1.383, -15.89, 1.417, -10.74]}, {"time": 1.4333, "value": -10.74, "curve": [1.45, -10.74, 1.483, -15.89]}, {"time": 1.5, "value": -15.89, "curve": [1.533, -15.89, 1.6, -11.66]}, {"time": 1.6333, "value": -11.66, "curve": [1.725, -11.66, 1.908, 3.35]}, {"time": 2, "value": 3.35}]}, "Spike4": {"rotate": [{"value": 4.35, "curve": [0.092, 4.35, 0.275, 45.6]}, {"time": 0.3667, "value": 45.6, "curve": [0.4, 45.6, 0.467, -9.74]}, {"time": 0.5, "value": -9.74, "curve": [0.517, -9.74, 0.55, -14.81]}, {"time": 0.5667, "value": -14.81, "curve": [0.583, -14.81, 0.617, -9.74]}, {"time": 0.6333, "value": -9.74, "curve": [0.65, -9.74, 0.683, -14.81]}, {"time": 0.7, "value": -14.81, "curve": [0.717, -14.81, 0.75, -9.74]}, {"time": 0.7667, "value": -9.74, "curve": [0.783, -9.74, 0.817, -14.81]}, {"time": 0.8333, "value": -14.81, "curve": [0.85, -14.81, 0.883, -9.74]}, {"time": 0.9, "value": -9.74, "curve": [0.917, -9.74, 0.95, -14.81]}, {"time": 0.9667, "value": -14.81, "curve": [0.983, -14.81, 1.017, -9.74]}, {"time": 1.0333, "value": -9.74, "curve": [1.05, -9.74, 1.083, -14.81]}, {"time": 1.1, "value": -14.81, "curve": [1.117, -14.81, 1.15, -9.74]}, {"time": 1.1667, "value": -9.74, "curve": [1.183, -9.74, 1.217, -14.81]}, {"time": 1.2333, "value": -14.81, "curve": [1.25, -14.81, 1.283, -9.74]}, {"time": 1.3, "value": -9.74, "curve": [1.317, -9.74, 1.35, -14.81]}, {"time": 1.3667, "value": -14.81, "curve": [1.383, -14.81, 1.417, -9.74]}, {"time": 1.4333, "value": -9.74, "curve": [1.45, -9.74, 1.483, -14.81]}, {"time": 1.5, "value": -14.81, "curve": [1.525, -14.81, 1.575, -11.66]}, {"time": 1.6, "value": -11.66, "curve": [1.7, -11.66, 1.9, 4.35]}, {"time": 2, "value": 4.35}]}, "Spike3": {"rotate": [{"value": 5.22, "curve": [0.092, 5.22, 0.275, 35.28]}, {"time": 0.3667, "value": 35.28, "curve": [0.4, 35.28, 0.467, -8.87]}, {"time": 0.5, "value": -8.87, "curve": [0.517, -8.87, 0.55, -17.04]}, {"time": 0.5667, "value": -17.04, "curve": [0.583, -17.04, 0.617, -8.87]}, {"time": 0.6333, "value": -8.87, "curve": [0.65, -8.87, 0.683, -17.04]}, {"time": 0.7, "value": -17.04, "curve": [0.717, -17.04, 0.75, -8.87]}, {"time": 0.7667, "value": -8.87, "curve": [0.783, -8.87, 0.817, -17.04]}, {"time": 0.8333, "value": -17.04, "curve": [0.85, -17.04, 0.883, -8.87]}, {"time": 0.9, "value": -8.87, "curve": [0.917, -8.87, 0.95, -17.04]}, {"time": 0.9667, "value": -17.04, "curve": [0.983, -17.04, 1.017, -8.87]}, {"time": 1.0333, "value": -8.87, "curve": [1.05, -8.87, 1.083, -17.04]}, {"time": 1.1, "value": -17.04, "curve": [1.117, -17.04, 1.15, -8.87]}, {"time": 1.1667, "value": -8.87, "curve": [1.183, -8.87, 1.217, -17.04]}, {"time": 1.2333, "value": -17.04, "curve": [1.25, -17.04, 1.283, -8.87]}, {"time": 1.3, "value": -8.87, "curve": [1.317, -8.87, 1.35, -17.04]}, {"time": 1.3667, "value": -17.04, "curve": [1.383, -17.04, 1.417, -8.87]}, {"time": 1.4333, "value": -8.87, "curve": [1.45, -8.87, 1.483, -17.04]}, {"time": 1.5, "value": -17.04, "curve": [1.608, -17.04, 1.825, 6.75]}, {"time": 1.9333, "value": 6.75, "curve": [1.95, 6.75, 1.983, 5.22]}, {"time": 2, "value": 5.22}]}, "Spike2": {"rotate": [{"value": -0.02, "curve": [0.092, -0.02, 0.275, 30.04]}, {"time": 0.3667, "value": 30.04, "curve": [0.4, 30.04, 0.467, -14.11]}, {"time": 0.5, "value": -14.11, "curve": [0.517, -14.11, 0.55, -21.85]}, {"time": 0.5667, "value": -21.85, "curve": [0.583, -21.85, 0.617, -14.11]}, {"time": 0.6333, "value": -14.11, "curve": [0.65, -14.11, 0.683, -21.85]}, {"time": 0.7, "value": -21.85, "curve": [0.717, -21.85, 0.75, -14.11]}, {"time": 0.7667, "value": -14.11, "curve": [0.783, -14.11, 0.817, -21.85]}, {"time": 0.8333, "value": -21.85, "curve": [0.85, -21.85, 0.883, -14.11]}, {"time": 0.9, "value": -14.11, "curve": [0.917, -14.11, 0.95, -21.85]}, {"time": 0.9667, "value": -21.85, "curve": [0.983, -21.85, 1.017, -14.11]}, {"time": 1.0333, "value": -14.11, "curve": [1.05, -14.11, 1.083, -21.85]}, {"time": 1.1, "value": -21.85, "curve": [1.117, -21.85, 1.15, -14.11]}, {"time": 1.1667, "value": -14.11, "curve": [1.183, -14.11, 1.217, -21.85]}, {"time": 1.2333, "value": -21.85, "curve": [1.25, -21.85, 1.283, -14.11]}, {"time": 1.3, "value": -14.11, "curve": [1.317, -14.11, 1.35, -21.85]}, {"time": 1.3667, "value": -21.85, "curve": [1.383, -21.85, 1.417, -14.11]}, {"time": 1.4333, "value": -14.11, "curve": [1.45, -14.11, 1.483, -21.85]}, {"time": 1.5, "value": -21.85, "curve": [1.575, -21.85, 1.725, 6.75]}, {"time": 1.8, "value": 6.75, "curve": [1.85, 6.75, 1.95, -0.02]}, {"time": 2, "value": -0.02}]}, "Spike1": {"rotate": [{"value": -2.5, "curve": [0.092, -2.5, 0.275, 27.56]}, {"time": 0.3667, "value": 27.56, "curve": [0.4, 27.56, 0.467, -16.59]}, {"time": 0.5, "value": -16.59, "curve": [0.517, -16.59, 0.55, -23.5]}, {"time": 0.5667, "value": -23.5, "curve": [0.583, -23.5, 0.617, -16.59]}, {"time": 0.6333, "value": -16.59, "curve": [0.65, -16.59, 0.683, -23.5]}, {"time": 0.7, "value": -23.5, "curve": [0.717, -23.5, 0.75, -16.59]}, {"time": 0.7667, "value": -16.59, "curve": [0.783, -16.59, 0.817, -23.5]}, {"time": 0.8333, "value": -23.5, "curve": [0.85, -23.5, 0.883, -16.59]}, {"time": 0.9, "value": -16.59, "curve": [0.917, -16.59, 0.95, -23.5]}, {"time": 0.9667, "value": -23.5, "curve": [0.983, -23.5, 1.017, -16.59]}, {"time": 1.0333, "value": -16.59, "curve": [1.05, -16.59, 1.083, -23.5]}, {"time": 1.1, "value": -23.5, "curve": [1.117, -23.5, 1.15, -16.59]}, {"time": 1.1667, "value": -16.59, "curve": [1.183, -16.59, 1.217, -23.5]}, {"time": 1.2333, "value": -23.5, "curve": [1.25, -23.5, 1.283, -16.59]}, {"time": 1.3, "value": -16.59, "curve": [1.317, -16.59, 1.35, -23.5]}, {"time": 1.3667, "value": -23.5, "curve": [1.383, -23.5, 1.417, -16.59]}, {"time": 1.4333, "value": -16.59, "curve": [1.45, -16.59, 1.483, -23.5]}, {"time": 1.5, "value": -23.5, "curve": [1.583, -23.5, 1.75, 6.75]}, {"time": 1.8333, "value": 6.75, "curve": [1.875, 6.75, 1.958, -2.5]}, {"time": 2, "value": -2.5}]}, "Spike10": {"rotate": [{"value": 5.4, "curve": [0.092, 5.4, 0.275, -24.28]}, {"time": 0.3667, "value": -24.28, "curve": [0.4, -24.28, 0.467, 21.39]}, {"time": 0.5, "value": 21.39, "curve": [0.517, 21.39, 0.55, 26.71]}, {"time": 0.5667, "value": 26.71, "curve": [0.583, 26.71, 0.617, 21.39]}, {"time": 0.6333, "value": 21.39, "curve": [0.65, 21.39, 0.683, 26.71]}, {"time": 0.7, "value": 26.71, "curve": [0.717, 26.71, 0.75, 21.39]}, {"time": 0.7667, "value": 21.39, "curve": [0.783, 21.39, 0.817, 26.71]}, {"time": 0.8333, "value": 26.71, "curve": [0.85, 26.71, 0.883, 21.39]}, {"time": 0.9, "value": 21.39, "curve": [0.917, 21.39, 0.95, 26.71]}, {"time": 0.9667, "value": 26.71, "curve": [0.983, 26.71, 1.017, 21.39]}, {"time": 1.0333, "value": 21.39, "curve": [1.05, 21.39, 1.083, 26.71]}, {"time": 1.1, "value": 26.71, "curve": [1.117, 26.71, 1.15, 21.39]}, {"time": 1.1667, "value": 21.39, "curve": [1.183, 21.39, 1.217, 26.71]}, {"time": 1.2333, "value": 26.71, "curve": [1.25, 26.71, 1.283, 21.39]}, {"time": 1.3, "value": 21.39, "curve": [1.317, 21.39, 1.35, 26.71]}, {"time": 1.3667, "value": 26.71, "curve": [1.383, 26.71, 1.417, 21.39]}, {"time": 1.4333, "value": 21.39, "curve": [1.45, 21.39, 1.483, 26.71]}, {"time": 1.5, "value": 26.71, "curve": [1.558, 26.71, 1.675, -5.73]}, {"time": 1.7333, "value": -5.73, "curve": [1.8, -5.73, 1.933, 5.4]}, {"time": 2, "value": 5.4}]}, "Spike6": {"rotate": [{"value": -0.33, "curve": [0.092, -0.33, 0.275, -30.01]}, {"time": 0.3667, "value": -30.01, "curve": [0.4, -30.01, 0.467, 15.66]}, {"time": 0.5, "value": 15.66, "curve": [0.517, 15.66, 0.55, 22.15]}, {"time": 0.5667, "value": 22.15, "curve": [0.583, 22.15, 0.617, 15.66]}, {"time": 0.6333, "value": 15.66, "curve": [0.65, 15.66, 0.683, 22.15]}, {"time": 0.7, "value": 22.15, "curve": [0.717, 22.15, 0.75, 15.66]}, {"time": 0.7667, "value": 15.66, "curve": [0.783, 15.66, 0.817, 22.15]}, {"time": 0.8333, "value": 22.15, "curve": [0.85, 22.15, 0.883, 15.66]}, {"time": 0.9, "value": 15.66, "curve": [0.917, 15.66, 0.95, 22.15]}, {"time": 0.9667, "value": 22.15, "curve": [0.983, 22.15, 1.017, 15.66]}, {"time": 1.0333, "value": 15.66, "curve": [1.05, 15.66, 1.083, 22.15]}, {"time": 1.1, "value": 22.15, "curve": [1.117, 22.15, 1.15, 15.66]}, {"time": 1.1667, "value": 15.66, "curve": [1.183, 15.66, 1.217, 22.15]}, {"time": 1.2333, "value": 22.15, "curve": [1.25, 22.15, 1.283, 15.66]}, {"time": 1.3, "value": 15.66, "curve": [1.317, 15.66, 1.35, 22.15]}, {"time": 1.3667, "value": 22.15, "curve": [1.383, 22.15, 1.417, 15.66]}, {"time": 1.4333, "value": 15.66, "curve": [1.45, 15.66, 1.483, 22.15]}, {"time": 1.5, "value": 22.15, "curve": [1.575, 22.15, 1.725, -5.73]}, {"time": 1.8, "value": -5.73, "curve": [1.85, -5.73, 1.95, -0.33]}, {"time": 2, "value": -0.33}]}, "Spike7": {"rotate": [{"value": -4.52, "curve": [0.092, -4.52, 0.275, -34.2]}, {"time": 0.3667, "value": -34.2, "curve": [0.4, -34.2, 0.467, 11.48]}, {"time": 0.5, "value": 11.48, "curve": [0.517, 11.48, 0.55, 18.5]}, {"time": 0.5667, "value": 18.5, "curve": [0.583, 18.5, 0.617, 11.48]}, {"time": 0.6333, "value": 11.48, "curve": [0.65, 11.48, 0.683, 18.5]}, {"time": 0.7, "value": 18.5, "curve": [0.717, 18.5, 0.75, 11.48]}, {"time": 0.7667, "value": 11.48, "curve": [0.783, 11.48, 0.817, 18.5]}, {"time": 0.8333, "value": 18.5, "curve": [0.85, 18.5, 0.883, 11.48]}, {"time": 0.9, "value": 11.48, "curve": [0.917, 11.48, 0.95, 18.5]}, {"time": 0.9667, "value": 18.5, "curve": [0.983, 18.5, 1.017, 11.48]}, {"time": 1.0333, "value": 11.48, "curve": [1.05, 11.48, 1.083, 18.5]}, {"time": 1.1, "value": 18.5, "curve": [1.117, 18.5, 1.15, 11.48]}, {"time": 1.1667, "value": 11.48, "curve": [1.183, 11.48, 1.217, 18.5]}, {"time": 1.2333, "value": 18.5, "curve": [1.25, 18.5, 1.283, 11.48]}, {"time": 1.3, "value": 11.48, "curve": [1.317, 11.48, 1.35, 18.5]}, {"time": 1.3667, "value": 18.5, "curve": [1.383, 18.5, 1.417, 11.48]}, {"time": 1.4333, "value": 11.48, "curve": [1.45, 11.48, 1.483, 18.5]}, {"time": 1.5, "value": 18.5, "curve": [1.608, 18.5, 1.825, -5.73]}, {"time": 1.9333, "value": -5.73, "curve": [1.95, -5.73, 1.983, -4.52]}, {"time": 2, "value": -4.52}]}, "Spike8": {"rotate": [{"value": -3.82, "curve": [0.092, -3.82, 0.275, -46.28]}, {"time": 0.3667, "value": -46.28, "curve": [0.4, -46.28, 0.467, 12.17]}, {"time": 0.5, "value": 12.17, "curve": [0.517, 12.17, 0.55, 16.18]}, {"time": 0.5667, "value": 16.18, "curve": [0.583, 16.18, 0.617, 12.17]}, {"time": 0.6333, "value": 12.17, "curve": [0.65, 12.17, 0.683, 16.18]}, {"time": 0.7, "value": 16.18, "curve": [0.717, 16.18, 0.75, 12.17]}, {"time": 0.7667, "value": 12.17, "curve": [0.783, 12.17, 0.817, 16.18]}, {"time": 0.8333, "value": 16.18, "curve": [0.85, 16.18, 0.883, 12.17]}, {"time": 0.9, "value": 12.17, "curve": [0.917, 12.17, 0.95, 16.18]}, {"time": 0.9667, "value": 16.18, "curve": [0.983, 16.18, 1.017, 12.17]}, {"time": 1.0333, "value": 12.17, "curve": [1.05, 12.17, 1.083, 16.18]}, {"time": 1.1, "value": 16.18, "curve": [1.117, 16.18, 1.15, 12.17]}, {"time": 1.1667, "value": 12.17, "curve": [1.183, 12.17, 1.217, 16.18]}, {"time": 1.2333, "value": 16.18, "curve": [1.25, 16.18, 1.283, 12.17]}, {"time": 1.3, "value": 12.17, "curve": [1.317, 12.17, 1.35, 16.18]}, {"time": 1.3667, "value": 16.18, "curve": [1.383, 16.18, 1.417, 12.17]}, {"time": 1.4333, "value": 12.17, "curve": [1.45, 12.17, 1.483, 16.18]}, {"time": 1.5, "value": 16.18, "curve": [1.525, 16.18, 1.575, 8.96]}, {"time": 1.6, "value": 8.96, "curve": [1.7, 8.96, 1.9, -3.82]}, {"time": 2, "value": -3.82}]}, "Spike9": {"rotate": [{"value": -3.02, "curve": [0.092, -3.02, 0.275, -42.45]}, {"time": 0.3667, "value": -42.45, "curve": [0.4, -42.45, 0.467, 12.97]}, {"time": 0.5, "value": 12.97, "curve": [0.517, 12.97, 0.55, 17.11]}, {"time": 0.5667, "value": 17.11, "curve": [0.583, 17.11, 0.617, 12.97]}, {"time": 0.6333, "value": 12.97, "curve": [0.65, 12.97, 0.683, 17.11]}, {"time": 0.7, "value": 17.11, "curve": [0.717, 17.11, 0.75, 12.97]}, {"time": 0.7667, "value": 12.97, "curve": [0.783, 12.97, 0.817, 17.11]}, {"time": 0.8333, "value": 17.11, "curve": [0.85, 17.11, 0.883, 12.97]}, {"time": 0.9, "value": 12.97, "curve": [0.917, 12.97, 0.95, 17.11]}, {"time": 0.9667, "value": 17.11, "curve": [0.983, 17.11, 1.017, 12.97]}, {"time": 1.0333, "value": 12.97, "curve": [1.05, 12.97, 1.083, 17.11]}, {"time": 1.1, "value": 17.11, "curve": [1.117, 17.11, 1.15, 12.97]}, {"time": 1.1667, "value": 12.97, "curve": [1.183, 12.97, 1.217, 17.11]}, {"time": 1.2333, "value": 17.11, "curve": [1.25, 17.11, 1.283, 12.97]}, {"time": 1.3, "value": 12.97, "curve": [1.317, 12.97, 1.35, 17.11]}, {"time": 1.3667, "value": 17.11, "curve": [1.383, 17.11, 1.417, 12.97]}, {"time": 1.4333, "value": 12.97, "curve": [1.45, 12.97, 1.483, 17.11]}, {"time": 1.5, "value": 17.11, "curve": [1.533, 17.11, 1.6, 8.96]}, {"time": 1.6333, "value": 8.96, "curve": [1.725, 8.96, 1.908, -3.02]}, {"time": 2, "value": -3.02}]}, "Tentacle5": {"rotate": [{"value": -49.78, "curve": [0.083, -49.78, 0.25, -55.12]}, {"time": 0.3333, "value": -55.12, "curve": [0.383, -55.12, 0.483, -5.44]}, {"time": 0.5333, "value": -5.44, "curve": [0.589, -2.66, 1.208, -1.29]}, {"time": 1.4333, "value": -1.29, "curve": [1.559, 4.17, 1.683, 9.39]}, {"time": 1.7667, "value": 9.39, "curve": [1.825, 9.39, 1.942, -49.78]}, {"time": 2, "value": -49.78}]}, "Tentacle3_Mid": {"rotate": [{"value": 51.86, "curve": [0.083, 51.86, 0.25, -17.38]}, {"time": 0.3333, "value": -17.38, "curve": [0.375, -17.38, 0.458, 19.89]}, {"time": 0.5, "value": 19.89, "curve": [0.517, 19.89, 0.55, -7.93]}, {"time": 0.5667, "value": -7.93, "curve": [0.583, -7.93, 0.617, 19.89]}, {"time": 0.6333, "value": 19.89, "curve": [0.65, 19.89, 0.683, -7.93]}, {"time": 0.7, "value": -7.93, "curve": [0.717, -7.93, 0.75, 19.89]}, {"time": 0.7667, "value": 19.89, "curve": [0.783, 19.89, 0.817, -7.93]}, {"time": 0.8333, "value": -7.93, "curve": [0.85, -7.93, 0.883, 19.89]}, {"time": 0.9, "value": 19.89, "curve": [0.917, 19.89, 0.95, -7.93]}, {"time": 0.9667, "value": -7.93, "curve": [0.983, -7.93, 1.017, 19.89]}, {"time": 1.0333, "value": 19.89, "curve": [1.05, 19.89, 1.083, -7.93]}, {"time": 1.1, "value": -7.93, "curve": [1.117, -7.93, 1.15, 19.89]}, {"time": 1.1667, "value": 19.89, "curve": [1.183, 19.89, 1.217, -7.93]}, {"time": 1.2333, "value": -7.93, "curve": [1.25, -7.93, 1.283, 19.89]}, {"time": 1.3, "value": 19.89, "curve": [1.317, 19.89, 1.35, -7.93]}, {"time": 1.3667, "value": -7.93, "curve": [1.383, -7.93, 1.417, 19.89]}, {"time": 1.4333, "value": 19.89, "curve": [1.483, 19.89, 1.583, -29.86]}, {"time": 1.6333, "value": -29.86, "curve": [1.717, -29.86, 1.883, 56.4]}, {"time": 1.9667, "value": 56.4, "curve": [1.977, 56.4, 1.988, 54.76]}, {"time": 2, "value": 51.86}]}, "Tentacle3_Btm": {"rotate": [{"value": 73.54, "curve": [0.083, 73.54, 0.25, -13.2]}, {"time": 0.3333, "value": -13.2, "curve": [0.383, -13.2, 0.483, 29.37]}, {"time": 0.5333, "value": 29.37, "curve": [0.55, 29.37, 0.583, 4.76]}, {"time": 0.6, "value": 4.76, "curve": [0.617, 4.76, 0.65, 29.37]}, {"time": 0.6667, "value": 29.37, "curve": [0.683, 29.37, 0.717, 4.76]}, {"time": 0.7333, "value": 4.76, "curve": [0.75, 4.76, 0.783, 29.37]}, {"time": 0.8, "value": 29.37, "curve": [0.817, 29.37, 0.85, 4.76]}, {"time": 0.8667, "value": 4.76, "curve": [0.883, 4.76, 0.917, 29.37]}, {"time": 0.9333, "value": 29.37, "curve": [0.95, 29.37, 0.983, 4.76]}, {"time": 1, "value": 4.76, "curve": [1.017, 4.76, 1.05, 29.37]}, {"time": 1.0667, "value": 29.37, "curve": [1.083, 29.37, 1.117, 4.76]}, {"time": 1.1333, "value": 4.76, "curve": [1.15, 4.76, 1.183, 29.37]}, {"time": 1.2, "value": 29.37, "curve": [1.217, 29.37, 1.25, 4.76]}, {"time": 1.2667, "value": 4.76, "curve": [1.283, 4.76, 1.317, 29.37]}, {"time": 1.3333, "value": 29.37, "curve": [1.35, 29.37, 1.383, 4.76]}, {"time": 1.4, "value": 4.76, "curve": [1.417, 4.76, 1.45, 29.37]}, {"time": 1.4667, "value": 29.37, "curve": [1.517, 29.37, 1.617, -37.96]}, {"time": 1.6667, "value": -37.96, "curve": [1.75, -37.96, 1.917, 73.54]}, {"time": 2, "value": 73.54}]}, "Tentacle6": {"rotate": [{"value": 36.14, "curve": [0.083, 36.14, 0.25, 40.76]}, {"time": 0.3333, "value": 40.76, "curve": [0.383, 40.76, 0.483, -2.6]}, {"time": 0.5333, "value": -2.6, "curve": [0.589, 2.97, 1.208, 5.72]}, {"time": 1.4333, "value": 5.72, "curve": [1.559, -2.67, 1.683, -10.68]}, {"time": 1.7667, "value": -10.68, "curve": [1.825, -10.68, 1.942, 36.14]}, {"time": 2, "value": 36.14}]}, "Tentacle3_Mid2": {"rotate": [{"value": 52.81, "curve": [0.083, 52.81, 0.25, -27.59]}, {"time": 0.3333, "value": -27.59, "curve": [0.375, -27.59, 0.458, 19.77]}, {"time": 0.5, "value": 19.77, "curve": [0.517, 19.77, 0.55, -5.64]}, {"time": 0.5667, "value": -5.64, "curve": [0.583, -5.64, 0.617, 19.77]}, {"time": 0.6333, "value": 19.77, "curve": [0.65, 19.77, 0.683, -5.64]}, {"time": 0.7, "value": -5.64, "curve": [0.717, -5.64, 0.75, 19.77]}, {"time": 0.7667, "value": 19.77, "curve": [0.783, 19.77, 0.817, -5.64]}, {"time": 0.8333, "value": -5.64, "curve": [0.85, -5.64, 0.883, 19.77]}, {"time": 0.9, "value": 19.77, "curve": [0.917, 19.77, 0.95, -5.64]}, {"time": 0.9667, "value": -5.64, "curve": [0.983, -5.64, 1.017, 19.77]}, {"time": 1.0333, "value": 19.77, "curve": [1.05, 19.77, 1.083, -5.64]}, {"time": 1.1, "value": -5.64, "curve": [1.117, -5.64, 1.15, 19.77]}, {"time": 1.1667, "value": 19.77, "curve": [1.183, 19.77, 1.217, -5.64]}, {"time": 1.2333, "value": -5.64, "curve": [1.25, -5.64, 1.283, 19.77]}, {"time": 1.3, "value": 19.77, "curve": [1.317, 19.77, 1.35, -5.64]}, {"time": 1.3667, "value": -5.64, "curve": [1.383, -5.64, 1.417, 19.77]}, {"time": 1.4333, "value": 19.77, "curve": [1.483, 19.77, 1.583, -30.13]}, {"time": 1.6333, "value": -30.13, "curve": [1.708, -30.13, 1.858, 62.45]}, {"time": 1.9333, "value": 62.45, "curve": [1.953, 62.45, 1.975, 58.81]}, {"time": 2, "value": 52.81}]}, "Tentacle3_Btm2": {"rotate": [{"value": 60.88, "curve": [0.083, 60.88, 0.25, -20.87]}, {"time": 0.3333, "value": -20.87, "curve": [0.383, -20.87, 0.483, 22.48]}, {"time": 0.5333, "value": 22.48, "curve": [0.55, 22.48, 0.583, -2.93]}, {"time": 0.6, "value": -2.93, "curve": [0.617, -2.93, 0.65, 22.48]}, {"time": 0.6667, "value": 22.48, "curve": [0.683, 22.48, 0.717, -2.93]}, {"time": 0.7333, "value": -2.93, "curve": [0.75, -2.93, 0.783, 22.48]}, {"time": 0.8, "value": 22.48, "curve": [0.817, 22.48, 0.85, -2.93]}, {"time": 0.8667, "value": -2.93, "curve": [0.883, -2.93, 0.917, 22.48]}, {"time": 0.9333, "value": 22.48, "curve": [0.95, 22.48, 0.983, -2.93]}, {"time": 1, "value": -2.93, "curve": [1.017, -2.93, 1.05, 22.48]}, {"time": 1.0667, "value": 22.48, "curve": [1.083, 22.48, 1.117, -2.93]}, {"time": 1.1333, "value": -2.93, "curve": [1.15, -2.93, 1.183, 22.48]}, {"time": 1.2, "value": 22.48, "curve": [1.217, 22.48, 1.25, -2.93]}, {"time": 1.2667, "value": -2.93, "curve": [1.283, -2.93, 1.317, 22.48]}, {"time": 1.3333, "value": 22.48, "curve": [1.35, 22.48, 1.383, -2.93]}, {"time": 1.4, "value": -2.93, "curve": [1.417, -2.93, 1.45, 22.48]}, {"time": 1.4667, "value": 22.48, "curve": [1.517, 22.48, 1.617, -41.36]}, {"time": 1.6667, "value": -41.36, "curve": [1.742, -41.36, 1.892, 62.45]}, {"time": 1.9667, "value": 62.45, "curve": [1.977, 62.45, 1.988, 61.9]}, {"time": 2, "value": 60.88}]}, "Tentacle2_Btm2": {"rotate": [{"value": 55.68, "curve": [0.083, 55.68, 0.25, -25.19]}, {"time": 0.3333, "value": -25.19, "curve": [0.383, -25.19, 0.483, 19.6]}, {"time": 0.5333, "value": 19.6, "curve": [0.55, 19.6, 0.583, 1.91]}, {"time": 0.6, "value": 1.91, "curve": [0.617, 1.91, 0.65, 19.6]}, {"time": 0.6667, "value": 19.6, "curve": [0.683, 19.6, 0.717, 1.91]}, {"time": 0.7333, "value": 1.91, "curve": [0.75, 1.91, 0.783, 19.6]}, {"time": 0.8, "value": 19.6, "curve": [0.817, 19.6, 0.85, 1.91]}, {"time": 0.8667, "value": 1.91, "curve": [0.883, 1.91, 0.917, 19.6]}, {"time": 0.9333, "value": 19.6, "curve": [0.95, 19.6, 0.983, 1.91]}, {"time": 1, "value": 1.91, "curve": [1.017, 1.91, 1.05, 19.6]}, {"time": 1.0667, "value": 19.6, "curve": [1.083, 19.6, 1.117, 1.91]}, {"time": 1.1333, "value": 1.91, "curve": [1.15, 1.91, 1.183, 19.6]}, {"time": 1.2, "value": 19.6, "curve": [1.217, 19.6, 1.25, 1.91]}, {"time": 1.2667, "value": 1.91, "curve": [1.283, 1.91, 1.317, 19.6]}, {"time": 1.3333, "value": 19.6, "curve": [1.35, 19.6, 1.383, 1.91]}, {"time": 1.4, "value": 1.91, "curve": [1.417, 1.91, 1.45, 19.6]}, {"time": 1.4667, "value": 19.6, "curve": [1.508, 19.6, 1.592, -56.67]}, {"time": 1.6333, "value": -56.67, "curve": [1.717, -56.67, 1.883, 62.45]}, {"time": 1.9667, "value": 62.45, "curve": [1.977, 62.45, 1.988, 60]}, {"time": 2, "value": 55.68}]}, "Tentacle2_Mid2": {"rotate": [{"value": 26.96, "curve": [0.083, 26.96, 0.25, -46.04]}, {"time": 0.3333, "value": -46.04, "curve": [0.375, -46.04, 0.458, 13.37]}, {"time": 0.5, "value": 13.37, "curve": [0.517, 13.37, 0.55, -4.32]}, {"time": 0.5667, "value": -4.32, "curve": [0.583, -4.32, 0.617, 13.37]}, {"time": 0.6333, "value": 13.37, "curve": [0.65, 13.37, 0.683, -4.32]}, {"time": 0.7, "value": -4.32, "curve": [0.717, -4.32, 0.75, 13.37]}, {"time": 0.7667, "value": 13.37, "curve": [0.783, 13.37, 0.817, -4.32]}, {"time": 0.8333, "value": -4.32, "curve": [0.85, -4.32, 0.883, 13.37]}, {"time": 0.9, "value": 13.37, "curve": [0.917, 13.37, 0.95, -4.32]}, {"time": 0.9667, "value": -4.32, "curve": [0.983, -4.32, 1.017, 13.37]}, {"time": 1.0333, "value": 13.37, "curve": [1.05, 13.37, 1.083, -4.32]}, {"time": 1.1, "value": -4.32, "curve": [1.117, -4.32, 1.15, 13.37]}, {"time": 1.1667, "value": 13.37, "curve": [1.183, 13.37, 1.217, -4.32]}, {"time": 1.2333, "value": -4.32, "curve": [1.25, -4.32, 1.283, 13.37]}, {"time": 1.3, "value": 13.37, "curve": [1.317, 13.37, 1.35, -4.32]}, {"time": 1.3667, "value": -4.32, "curve": [1.383, -4.32, 1.417, 13.37]}, {"time": 1.4333, "value": 13.37, "curve": [1.467, 13.37, 1.533, -30.13]}, {"time": 1.5667, "value": -30.13, "curve": [1.65, -30.13, 1.817, 62.45]}, {"time": 1.9, "value": 62.45, "curve": [1.926, 62.45, 1.962, 46.14]}, {"time": 2, "value": 26.96}]}, "Tentacle4": {"rotate": [{"value": 26.28, "curve": [0.083, 26.28, 0.25, 17.6]}, {"time": 0.3333, "value": 17.6, "curve": [0.383, 17.6, 0.483, -0.05]}, {"time": 0.5333, "value": -0.05, "curve": [0.589, 1.1, 1.208, 1.67]}, {"time": 1.4333, "value": 1.67, "curve": [1.559, -4.65, 1.683, -10.68]}, {"time": 1.7667, "value": -10.68, "curve": [1.825, -10.68, 1.942, 26.28]}, {"time": 2, "value": 26.28}]}, "Tentacle2_Btm": {"rotate": [{"value": 47.08, "curve": [0.083, 47.08, 0.25, -21.72]}, {"time": 0.3333, "value": -21.72, "curve": [0.383, -21.72, 0.483, 17.31]}, {"time": 0.5333, "value": 17.31, "curve": [0.55, 17.31, 0.583, -5.18]}, {"time": 0.6, "value": -5.18, "curve": [0.617, -5.18, 0.65, 17.31]}, {"time": 0.6667, "value": 17.31, "curve": [0.683, 17.31, 0.717, -5.18]}, {"time": 0.7333, "value": -5.18, "curve": [0.75, -5.18, 0.783, 17.31]}, {"time": 0.8, "value": 17.31, "curve": [0.817, 17.31, 0.85, -5.18]}, {"time": 0.8667, "value": -5.18, "curve": [0.883, -5.18, 0.917, 17.31]}, {"time": 0.9333, "value": 17.31, "curve": [0.95, 17.31, 0.983, -5.18]}, {"time": 1, "value": -5.18, "curve": [1.017, -5.18, 1.05, 17.31]}, {"time": 1.0667, "value": 17.31, "curve": [1.083, 17.31, 1.117, -5.18]}, {"time": 1.1333, "value": -5.18, "curve": [1.15, -5.18, 1.183, 17.31]}, {"time": 1.2, "value": 17.31, "curve": [1.217, 17.31, 1.25, -5.18]}, {"time": 1.2667, "value": -5.18, "curve": [1.283, -5.18, 1.317, 17.31]}, {"time": 1.3333, "value": 17.31, "curve": [1.35, 17.31, 1.383, -5.18]}, {"time": 1.4, "value": -5.18, "curve": [1.417, -5.18, 1.45, 17.31]}, {"time": 1.4667, "value": 17.31, "curve": [1.508, 17.31, 1.592, -65.15]}, {"time": 1.6333, "value": -65.15, "curve": [1.717, -65.15, 1.883, 53.84]}, {"time": 1.9667, "value": 53.84, "curve": [1.977, 53.84, 1.988, 51.4]}, {"time": 2, "value": 47.08}]}, "Tentacle2_Mid": {"rotate": [{"value": 16.89, "curve": [0.083, 16.89, 0.25, -44.63]}, {"time": 0.3333, "value": -44.63, "curve": [0.375, -44.63, 0.458, 7.54]}, {"time": 0.5, "value": 7.54, "curve": [0.517, 7.54, 0.55, -8.22]}, {"time": 0.5667, "value": -8.22, "curve": [0.583, -8.22, 0.617, 7.54]}, {"time": 0.6333, "value": 7.54, "curve": [0.65, 7.54, 0.683, -8.22]}, {"time": 0.7, "value": -8.22, "curve": [0.717, -8.22, 0.75, 7.54]}, {"time": 0.7667, "value": 7.54, "curve": [0.783, 7.54, 0.817, -8.22]}, {"time": 0.8333, "value": -8.22, "curve": [0.85, -8.22, 0.883, 7.54]}, {"time": 0.9, "value": 7.54, "curve": [0.917, 7.54, 0.95, -8.22]}, {"time": 0.9667, "value": -8.22, "curve": [0.983, -8.22, 1.017, 7.54]}, {"time": 1.0333, "value": 7.54, "curve": [1.05, 7.54, 1.083, -8.22]}, {"time": 1.1, "value": -8.22, "curve": [1.117, -8.22, 1.15, 7.54]}, {"time": 1.1667, "value": 7.54, "curve": [1.183, 7.54, 1.217, -8.22]}, {"time": 1.2333, "value": -8.22, "curve": [1.25, -8.22, 1.283, 7.54]}, {"time": 1.3, "value": 7.54, "curve": [1.317, 7.54, 1.35, -8.22]}, {"time": 1.3667, "value": -8.22, "curve": [1.383, -8.22, 1.417, 7.54]}, {"time": 1.4333, "value": 7.54, "curve": [1.467, 7.54, 1.533, -36.3]}, {"time": 1.5667, "value": -36.3, "curve": [1.65, -36.3, 1.817, 49.96]}, {"time": 1.9, "value": 49.96, "curve": [1.926, 49.96, 1.962, 34.77]}, {"time": 2, "value": 16.89}]}, "Tentacle3": {"rotate": [{"value": -36.75, "curve": [0.083, -36.75, 0.25, -19.94]}, {"time": 0.3333, "value": -19.94, "curve": [0.383, -19.94, 0.483, -3.33]}, {"time": 0.5333, "value": -3.33, "curve": [0.589, 3.34, 1.208, 6.63]}, {"time": 1.4333, "value": 6.63, "curve": [1.559, 6.65, 1.683, 6.68]}, {"time": 1.7667, "value": 6.68, "curve": [1.825, 6.68, 1.942, -36.75]}, {"time": 2, "value": -36.75}]}, "DangleHandle": {"rotate": [{"value": 6.7, "curve": [0.025, 6.7, 0.075, -3.58]}, {"time": 0.1, "value": -3.58, "curve": [0.2, -13.51, 0.3, -23.45]}, {"time": 0.3667, "value": -23.45, "curve": [0.492, -23.45, 0.742, 16.3]}, {"time": 0.8667, "value": 16.3, "curve": [0.903, 16.3, 0.949, 12.3]}, {"time": 1, "value": 6.7, "curve": [1.032, 3.66, 1.066, 0.04]}, {"time": 1.1, "value": -3.58, "curve": [1.2, -13.51, 1.3, -23.45]}, {"time": 1.3667, "value": -23.45, "curve": [1.492, -23.45, 1.742, 16.3]}, {"time": 1.8667, "value": 16.3, "curve": [1.903, 16.3, 1.949, 12.3]}, {"time": 2, "value": 6.7}], "translate": [{"x": -3.96, "y": 2.8, "curve": [0.067, -3.96, 0.2, -51.8, 0.067, 2.8, 0.2, 0.85]}, {"time": 0.2667, "x": -51.8, "y": 0.85, "curve": [0.392, -51.8, 0.642, 43.87, 0.392, 0.85, 0.642, 4.74]}, {"time": 0.7667, "x": 43.87, "y": 4.74, "curve": [0.825, 43.87, 0.913, 19.95, 0.825, 4.74, 0.913, 3.77]}, {"time": 1, "x": -3.96, "y": 2.8, "curve": [1.1, -27.88, 1.2, -51.8, 1.1, 1.82, 1.2, 0.85]}, {"time": 1.2667, "x": -51.8, "y": 0.85, "curve": [1.392, -51.8, 1.642, 43.87, 1.392, 0.85, 1.642, 4.74]}, {"time": 1.7667, "x": 43.87, "y": 4.74, "curve": [1.825, 43.87, 1.913, 19.95, 1.825, 4.74, 1.913, 3.77]}, {"time": 2, "x": -3.96, "y": 2.8}], "scale": [{"y": 1.723, "curve": [0.125, 1, 0.375, 1, 0.125, 1.723, 0.375, 0.313]}, {"time": 0.5, "y": 0.313, "curve": [0.625, 1, 0.875, 1, 0.625, 0.313, 0.875, 1.723]}, {"time": 1, "y": 1.723, "curve": [1.125, 1, 1.375, 1, 1.125, 1.723, 1.375, 0.313]}, {"time": 1.5, "y": 0.313, "curve": [1.625, 1, 1.875, 1, 1.625, 0.313, 1.875, 1.723]}, {"time": 2, "y": 1.723}]}, "Tentacles": {"translate": [{"time": 0.8, "x": 0.52}], "scale": [{"x": 0.699, "curve": [0.125, 0.699, 0.375, 1.199, 0.125, 1, 0.375, 1]}, {"time": 0.5, "x": 1.199}, {"time": 1, "x": 1.116}, {"time": 1.5, "x": 1.199}, {"time": 2, "x": 0.699}]}, "Mouth": {"translate": [{"y": -10.97, "curve": [0.033, 0, 0.1, 0, 0.033, -10.97, 0.1, -14.21]}, {"time": 0.1333, "y": -14.21, "curve": "stepped"}, {"time": 0.3333, "y": -14.21, "curve": [0.333, 0, 0.458, 0, 0.333, 15.88, 0.458, 31.82]}, {"time": 0.5, "y": 31.82, "curve": [0.517, 0, 0.55, 0, 0.517, 31.82, 0.55, 20.89]}, {"time": 0.5667, "y": 20.89, "curve": [0.583, 0, 0.617, 0, 0.583, 20.89, 0.617, 31.82]}, {"time": 0.6333, "y": 31.82, "curve": [0.65, 0, 0.683, 0, 0.65, 31.82, 0.683, 20.89]}, {"time": 0.7, "y": 20.89, "curve": [0.717, 0, 0.75, 0, 0.717, 20.89, 0.75, 31.82]}, {"time": 0.7667, "y": 31.82, "curve": [0.783, 0, 0.817, 0, 0.783, 31.82, 0.817, 20.89]}, {"time": 0.8333, "y": 20.89, "curve": [0.85, 0, 0.883, 0, 0.85, 20.89, 0.883, 31.82]}, {"time": 0.9, "y": 31.82, "curve": [0.917, 0, 0.95, 0, 0.917, 31.82, 0.95, 20.89]}, {"time": 0.9667, "y": 20.89, "curve": [0.983, 0, 1.017, 0, 0.983, 20.89, 1.017, 31.82]}, {"time": 1.0333, "y": 31.82, "curve": [1.05, 0, 1.083, 0, 1.05, 31.82, 1.083, 20.89]}, {"time": 1.1, "y": 20.89, "curve": [1.117, 0, 1.15, 0, 1.117, 20.89, 1.15, 31.82]}, {"time": 1.1667, "y": 31.82, "curve": [1.183, 0, 1.217, 0, 1.183, 31.82, 1.217, 20.89]}, {"time": 1.2333, "y": 20.89, "curve": [1.25, 0, 1.283, 0, 1.25, 20.89, 1.283, 31.82]}, {"time": 1.3, "y": 31.82, "curve": [1.317, 0, 1.35, 0, 1.317, 31.82, 1.35, 20.89]}, {"time": 1.3667, "y": 20.89, "curve": [1.4, 0, 1.467, 0, 1.4, 20.89, 1.467, 31.82]}, {"time": 1.5, "y": 31.82, "curve": [1.567, 0, 1.7, 0, 1.567, 31.82, 1.7, -28.96]}, {"time": 1.7667, "y": -28.96, "curve": [1.825, 0, 1.942, 0, 1.825, -28.96, 1.942, -10.97]}, {"time": 2, "y": -10.97}]}, "Mouth8": {"rotate": [{"value": -31.71, "curve": [0.075, -31.71, 0.225, -66.43]}, {"time": 0.3, "value": -66.43, "curve": [0.342, -66.43, 0.425, 41.87]}, {"time": 0.4667, "value": 41.87, "curve": [0.492, 41.87, 0.542, 60.86]}, {"time": 0.5667, "value": 60.86, "curve": [0.592, 60.86, 0.642, 41.87]}, {"time": 0.6667, "value": 41.87, "curve": [0.692, 41.87, 0.742, 60.86]}, {"time": 0.7667, "value": 60.86, "curve": [0.792, 60.86, 0.842, 41.87]}, {"time": 0.8667, "value": 41.87, "curve": [0.892, 41.87, 0.942, 60.86]}, {"time": 0.9667, "value": 60.86, "curve": [0.992, 60.86, 1.042, 41.87]}, {"time": 1.0667, "value": 41.87, "curve": [1.092, 41.87, 1.142, 60.86]}, {"time": 1.1667, "value": 60.86, "curve": [1.192, 60.86, 1.242, 46]}, {"time": 1.2667, "value": 46, "curve": [1.283, 46, 1.317, 60.86]}, {"time": 1.3333, "value": 60.86, "curve": [1.417, 60.86, 1.583, -66.43]}, {"time": 1.6667, "value": -66.43, "curve": [1.75, -66.43, 1.917, -31.71]}, {"time": 2, "value": -31.71}]}, "Mouth5": {"rotate": [{"value": 18.98, "curve": [0.075, 18.98, 0.225, 71.09]}, {"time": 0.3, "value": 71.09, "curve": [0.358, 71.09, 0.475, -20.54]}, {"time": 0.5333, "value": -20.54, "curve": [0.558, -20.54, 0.608, -39.23]}, {"time": 0.6333, "value": -39.23, "curve": [0.658, -39.23, 0.708, -20.54]}, {"time": 0.7333, "value": -20.54, "curve": [0.758, -20.54, 0.808, -39.23]}, {"time": 0.8333, "value": -39.23, "curve": [0.858, -39.23, 0.908, -20.54]}, {"time": 0.9333, "value": -20.54, "curve": [0.958, -20.54, 1.008, -39.23]}, {"time": 1.0333, "value": -39.23, "curve": [1.058, -39.23, 1.108, -20.54]}, {"time": 1.1333, "value": -20.54, "curve": [1.158, -20.54, 1.208, -39.23]}, {"time": 1.2333, "value": -39.23, "curve": [1.258, -39.23, 1.308, -20.54]}, {"time": 1.3333, "value": -20.54, "curve": [1.417, -20.54, 1.583, 71.09]}, {"time": 1.6667, "value": 71.09, "curve": [1.75, 71.09, 1.917, 18.98]}, {"time": 2, "value": 18.98}]}, "Mouth4": {"rotate": [{"value": 6.8, "curve": [0.075, 6.8, 0.225, 43.03]}, {"time": 0.3, "value": 43.03, "curve": [0.342, 43.03, 0.425, -32.72]}, {"time": 0.4667, "value": -32.72, "curve": [0.492, -32.72, 0.542, -51.41]}, {"time": 0.5667, "value": -51.41, "curve": [0.592, -51.41, 0.642, -32.72]}, {"time": 0.6667, "value": -32.72, "curve": [0.692, -32.72, 0.742, -51.41]}, {"time": 0.7667, "value": -51.41, "curve": [0.792, -51.41, 0.842, -32.72]}, {"time": 0.8667, "value": -32.72, "curve": [0.892, -32.72, 0.942, -51.41]}, {"time": 0.9667, "value": -51.41, "curve": [0.992, -51.41, 1.042, -32.72]}, {"time": 1.0667, "value": -32.72, "curve": [1.092, -32.72, 1.142, -51.41]}, {"time": 1.1667, "value": -51.41, "curve": [1.192, -51.41, 1.242, -32.72]}, {"time": 1.2667, "value": -32.72, "curve": "stepped"}, {"time": 1.3333, "value": -32.72, "curve": [1.417, -32.72, 1.583, 43.03]}, {"time": 1.6667, "value": 43.03, "curve": [1.75, 43.03, 1.917, 6.8]}, {"time": 2, "value": 6.8}]}, "Mouth7": {"rotate": [{"value": -41.36, "curve": [0.075, -41.36, 0.225, -76.07]}, {"time": 0.3, "value": -76.07, "curve": [0.35, -76.07, 0.45, 38.19]}, {"time": 0.5, "value": 38.19, "curve": [0.525, 38.19, 0.575, 57.18]}, {"time": 0.6, "value": 57.18, "curve": [0.625, 57.18, 0.675, 38.19]}, {"time": 0.7, "value": 38.19, "curve": [0.725, 38.19, 0.775, 57.18]}, {"time": 0.8, "value": 57.18, "curve": [0.825, 57.18, 0.875, 38.19]}, {"time": 0.9, "value": 38.19, "curve": [0.925, 38.19, 0.975, 57.18]}, {"time": 1, "value": 57.18, "curve": [1.025, 57.18, 1.075, 38.19]}, {"time": 1.1, "value": 38.19, "curve": [1.125, 38.19, 1.175, 57.18]}, {"time": 1.2, "value": 57.18, "curve": [1.225, 57.18, 1.275, 42.32]}, {"time": 1.3, "value": 42.32, "curve": [1.317, 42.32, 1.35, 57.18]}, {"time": 1.3667, "value": 57.18, "curve": [1.442, 57.18, 1.592, -76.07]}, {"time": 1.6667, "value": -76.07, "curve": [1.75, -76.07, 1.917, -41.36]}, {"time": 2, "value": -41.36}]}, "Mouth3": {"rotate": [{"value": -2.12, "curve": "stepped"}, {"time": 0.3, "value": -2.12, "curve": [0.35, -2.12, 0.45, -58.91]}, {"time": 0.5, "value": -58.91, "curve": [0.525, -58.91, 0.575, -77.61]}, {"time": 0.6, "value": -77.61, "curve": [0.625, -77.61, 0.675, -58.91]}, {"time": 0.7, "value": -58.91, "curve": [0.725, -58.91, 0.775, -77.61]}, {"time": 0.8, "value": -77.61, "curve": [0.825, -77.61, 0.875, -58.91]}, {"time": 0.9, "value": -58.91, "curve": [0.925, -58.91, 0.975, -77.61]}, {"time": 1, "value": -77.61, "curve": [1.025, -77.61, 1.075, -58.91]}, {"time": 1.1, "value": -58.91, "curve": [1.125, -58.91, 1.175, -77.61]}, {"time": 1.2, "value": -77.61, "curve": [1.225, -77.61, 1.275, -58.91]}, {"time": 1.3, "value": -58.91, "curve": "stepped"}, {"time": 1.3333, "value": -58.91, "curve": [1.417, -58.91, 1.583, -2.12]}, {"time": 1.6667, "value": -2.12}]}, "Mouth6": {"rotate": [{"value": -25.64, "curve": "stepped"}, {"time": 0.3, "value": -25.64, "curve": [0.358, -25.64, 0.475, 44.64]}, {"time": 0.5333, "value": 44.64, "curve": [0.558, 44.64, 0.608, 63.63]}, {"time": 0.6333, "value": 63.63, "curve": [0.658, 63.63, 0.708, 44.64]}, {"time": 0.7333, "value": 44.64, "curve": [0.758, 44.64, 0.808, 63.63]}, {"time": 0.8333, "value": 63.63, "curve": [0.858, 63.63, 0.908, 44.64]}, {"time": 0.9333, "value": 44.64, "curve": [0.958, 44.64, 1.008, 63.63]}, {"time": 1.0333, "value": 63.63, "curve": [1.058, 63.63, 1.108, 44.64]}, {"time": 1.1333, "value": 44.64, "curve": [1.158, 44.64, 1.208, 63.63]}, {"time": 1.2333, "value": 63.63, "curve": [1.258, 63.63, 1.308, 48.77]}, {"time": 1.3333, "value": 48.77, "curve": [1.35, 48.77, 1.383, 63.63]}, {"time": 1.4, "value": 63.63, "curve": [1.467, 63.63, 1.6, -25.64]}, {"time": 1.6667, "value": -25.64}]}, "Tentacle3_Btm3": {"rotate": [{"value": 61.89, "curve": [0.083, 61.89, 0.25, -5.87]}, {"time": 0.3333, "value": -5.87, "curve": [0.383, -5.87, 0.483, 34.8]}, {"time": 0.5333, "value": 34.8, "curve": [0.55, 34.8, 0.583, -7.06]}, {"time": 0.6, "value": -7.06, "curve": [0.617, -7.06, 0.65, 34.8]}, {"time": 0.6667, "value": 34.8, "curve": [0.683, 34.8, 0.717, -7.06]}, {"time": 0.7333, "value": -7.06, "curve": [0.75, -7.06, 0.783, 34.8]}, {"time": 0.8, "value": 34.8, "curve": [0.817, 34.8, 0.85, -7.06]}, {"time": 0.8667, "value": -7.06, "curve": [0.883, -7.06, 0.917, 34.8]}, {"time": 0.9333, "value": 34.8, "curve": [0.95, 34.8, 0.983, -7.06]}, {"time": 1, "value": -7.06, "curve": [1.017, -7.06, 1.05, 34.8]}, {"time": 1.0667, "value": 34.8, "curve": [1.083, 34.8, 1.117, -7.06]}, {"time": 1.1333, "value": -7.06, "curve": [1.15, -7.06, 1.183, 34.8]}, {"time": 1.2, "value": 34.8, "curve": [1.217, 34.8, 1.25, -7.06]}, {"time": 1.2667, "value": -7.06, "curve": [1.283, -7.06, 1.317, 34.8]}, {"time": 1.3333, "value": 34.8, "curve": [1.35, 34.8, 1.383, -7.06]}, {"time": 1.4, "value": -7.06, "curve": [1.417, -7.06, 1.45, 34.8]}, {"time": 1.4667, "value": 34.8, "curve": [1.508, 34.8, 1.592, -37.96]}, {"time": 1.6333, "value": -37.96, "curve": [1.708, -37.96, 1.858, 73.54]}, {"time": 1.9333, "value": 73.54, "curve": [1.953, 73.54, 1.975, 69.14]}, {"time": 2, "value": 61.89}]}, "Tentacle3_Mid3": {"rotate": [{"value": 36.14, "curve": [0.083, 36.14, 0.25, -29.21]}, {"time": 0.3333, "value": -29.21, "curve": [0.375, -29.21, 0.458, 16.36]}, {"time": 0.5, "value": 16.36, "curve": [0.517, 16.36, 0.55, 0.56]}, {"time": 0.5667, "value": 0.56, "curve": [0.583, 0.56, 0.617, 16.36]}, {"time": 0.6333, "value": 16.36, "curve": [0.65, 16.36, 0.683, 0.56]}, {"time": 0.7, "value": 0.56, "curve": [0.717, 0.56, 0.75, 16.36]}, {"time": 0.7667, "value": 16.36, "curve": [0.783, 16.36, 0.817, 0.56]}, {"time": 0.8333, "value": 0.56, "curve": [0.85, 0.56, 0.883, 16.36]}, {"time": 0.9, "value": 16.36, "curve": [0.917, 16.36, 0.95, 0.56]}, {"time": 0.9667, "value": 0.56, "curve": [0.983, 0.56, 1.017, 16.36]}, {"time": 1.0333, "value": 16.36, "curve": [1.05, 16.36, 1.083, 0.56]}, {"time": 1.1, "value": 0.56, "curve": [1.117, 0.56, 1.15, 16.36]}, {"time": 1.1667, "value": 16.36, "curve": [1.183, 16.36, 1.217, 0.56]}, {"time": 1.2333, "value": 0.56, "curve": [1.25, 0.56, 1.283, 16.36]}, {"time": 1.3, "value": 16.36, "curve": [1.317, 16.36, 1.35, 0.56]}, {"time": 1.3667, "value": 0.56, "curve": [1.383, 0.56, 1.417, 16.36]}, {"time": 1.4333, "value": 16.36, "curve": [1.475, 16.36, 1.558, -29.86]}, {"time": 1.6, "value": -29.86, "curve": [1.683, -29.86, 1.85, 56.4]}, {"time": 1.9333, "value": 56.4, "curve": [1.951, 56.4, 1.975, 48]}, {"time": 2, "value": 36.14}]}, "Tentacle1": {"rotate": [{"value": -49.78, "curve": [0.083, -49.78, 0.25, -38.56]}, {"time": 0.3333, "value": -38.56, "curve": [0.383, -38.56, 0.483, -2.92]}, {"time": 0.5333, "value": -2.92, "curve": [0.589, -1.04, 1.208, -0.11]}, {"time": 1.4333, "value": -0.11, "curve": [1.559, 4.75, 1.683, 9.39]}, {"time": 1.7667, "value": 9.39, "curve": [1.825, 9.39, 1.942, -49.78]}, {"time": 2, "value": -49.78}]}, "Tentacle3_Btm4": {"rotate": [{"value": 51.6, "curve": [0.083, 51.6, 0.25, -28.38]}, {"time": 0.3333, "value": -28.38, "curve": [0.383, -28.38, 0.483, 19.36]}, {"time": 0.5333, "value": 19.36, "curve": [0.55, 19.36, 0.583, -2.65]}, {"time": 0.6, "value": -2.65, "curve": [0.617, -2.65, 0.65, 19.36]}, {"time": 0.6667, "value": 19.36, "curve": [0.683, 19.36, 0.717, -2.65]}, {"time": 0.7333, "value": -2.65, "curve": [0.75, -2.65, 0.783, 19.36]}, {"time": 0.8, "value": 19.36, "curve": [0.817, 19.36, 0.85, -2.65]}, {"time": 0.8667, "value": -2.65, "curve": [0.883, -2.65, 0.917, 19.36]}, {"time": 0.9333, "value": 19.36, "curve": [0.95, 19.36, 0.983, -2.65]}, {"time": 1, "value": -2.65, "curve": [1.017, -2.65, 1.05, 19.36]}, {"time": 1.0667, "value": 19.36, "curve": [1.083, 19.36, 1.117, -2.65]}, {"time": 1.1333, "value": -2.65, "curve": [1.15, -2.65, 1.183, 19.36]}, {"time": 1.2, "value": 19.36, "curve": [1.217, 19.36, 1.25, -2.65]}, {"time": 1.2667, "value": -2.65, "curve": [1.283, -2.65, 1.317, 19.36]}, {"time": 1.3333, "value": 19.36, "curve": [1.35, 19.36, 1.383, -2.65]}, {"time": 1.4, "value": -2.65, "curve": [1.417, -2.65, 1.45, 19.36]}, {"time": 1.4667, "value": 19.36, "curve": [1.508, 19.36, 1.592, -41.36]}, {"time": 1.6333, "value": -41.36, "curve": [1.708, -41.36, 1.858, 62.45]}, {"time": 1.9333, "value": 62.45, "curve": [1.953, 62.45, 1.975, 58.35]}, {"time": 2, "value": 51.6}]}, "Tentacle3_Mid4": {"rotate": [{"value": 40.71, "curve": [0.083, 40.71, 0.25, -36.39]}, {"time": 0.3333, "value": -36.39, "curve": [0.375, -36.39, 0.458, 17.41]}, {"time": 0.5, "value": 17.41, "curve": [0.517, 17.41, 0.55, -4.59]}, {"time": 0.5667, "value": -4.59, "curve": [0.583, -4.59, 0.617, 17.41]}, {"time": 0.6333, "value": 17.41, "curve": [0.65, 17.41, 0.683, -4.59]}, {"time": 0.7, "value": -4.59, "curve": [0.717, -4.59, 0.75, 17.41]}, {"time": 0.7667, "value": 17.41, "curve": [0.783, 17.41, 0.817, -4.59]}, {"time": 0.8333, "value": -4.59, "curve": [0.85, -4.59, 0.883, 17.41]}, {"time": 0.9, "value": 17.41, "curve": [0.917, 17.41, 0.95, -4.59]}, {"time": 0.9667, "value": -4.59, "curve": [0.983, -4.59, 1.017, 17.41]}, {"time": 1.0333, "value": 17.41, "curve": [1.05, 17.41, 1.083, -4.59]}, {"time": 1.1, "value": -4.59, "curve": [1.117, -4.59, 1.15, 17.41]}, {"time": 1.1667, "value": 17.41, "curve": [1.183, 17.41, 1.217, -4.59]}, {"time": 1.2333, "value": -4.59, "curve": [1.25, -4.59, 1.283, 17.41]}, {"time": 1.3, "value": 17.41, "curve": [1.317, 17.41, 1.35, -4.59]}, {"time": 1.3667, "value": -4.59, "curve": [1.383, -4.59, 1.417, 17.41]}, {"time": 1.4333, "value": 17.41, "curve": [1.475, 17.41, 1.558, -30.13]}, {"time": 1.6, "value": -30.13, "curve": [1.683, -30.13, 1.85, 62.45]}, {"time": 1.9333, "value": 62.45, "curve": [1.951, 62.45, 1.975, 53.43]}, {"time": 2, "value": 40.71}]}, "Tentacle2": {"rotate": [{"value": 36.14, "curve": [0.083, 36.14, 0.25, 25.15]}, {"time": 0.3333, "value": 25.15, "curve": [0.383, 25.15, 0.483, -3]}, {"time": 0.5333, "value": -3, "curve": [0.589, 2.72, 1.208, 5.53]}, {"time": 1.4333, "value": 5.53, "curve": [1.559, -2.76, 1.683, -10.68]}, {"time": 1.7667, "value": -10.68, "curve": [1.825, -10.68, 1.942, 36.14]}, {"time": 2, "value": 36.14}]}, "Head": {"rotate": [{"curve": [0.083, 0, 0.25, -3.38]}, {"time": 0.3333, "value": -3.38, "curve": [0.417, -3.38, 0.583, 4.87]}, {"time": 0.6667, "value": 4.87, "curve": [0.758, 4.87, 0.942, -7.99]}, {"time": 1.0333, "value": -7.99, "curve": [1.192, -7.99, 1.508, 0]}, {"time": 1.6667}], "scale": [{"curve": [0.025, 1, 0.075, 0.844, 0.025, 1, 0.075, 1.141]}, {"time": 0.1, "x": 0.844, "y": 1.141, "curve": [0.158, 0.844, 0.275, 1.145, 0.158, 1.141, 0.275, 0.936]}, {"time": 0.3333, "x": 1.145, "y": 0.936, "curve": [0.383, 1.145, 0.483, 0.988, 0.383, 0.936, 0.483, 1.151]}, {"time": 0.5333, "x": 0.988, "y": 1.151, "curve": [0.567, 0.988, 0.633, 1.176, 0.567, 1.151, 0.633, 0.886]}, {"time": 0.6667, "x": 1.176, "y": 0.886, "curve": [0.683, 1.176, 0.717, 1.115, 0.683, 0.886, 0.717, 0.98]}, {"time": 0.7333, "x": 1.115, "y": 0.98, "curve": [0.75, 1.115, 0.783, 1.176, 0.75, 0.98, 0.783, 0.886]}, {"time": 0.8, "x": 1.176, "y": 0.886, "curve": [0.817, 1.176, 0.85, 1.115, 0.817, 0.886, 0.85, 0.98]}, {"time": 0.8667, "x": 1.115, "y": 0.98, "curve": [0.883, 1.115, 0.917, 1.176, 0.883, 0.98, 0.917, 0.886]}, {"time": 0.9333, "x": 1.176, "y": 0.886, "curve": [0.95, 1.176, 0.983, 1.115, 0.95, 0.886, 0.983, 0.98]}, {"time": 1, "x": 1.115, "y": 0.98, "curve": [1.017, 1.115, 1.05, 1.176, 1.017, 0.98, 1.05, 0.886]}, {"time": 1.0667, "x": 1.176, "y": 0.886, "curve": [1.083, 1.176, 1.117, 1.115, 1.083, 0.886, 1.117, 0.98]}, {"time": 1.1333, "x": 1.115, "y": 0.98, "curve": [1.15, 1.115, 1.183, 1.176, 1.15, 0.98, 1.183, 0.886]}, {"time": 1.2, "x": 1.176, "y": 0.886, "curve": [1.217, 1.176, 1.25, 1.115, 1.217, 0.886, 1.25, 0.98]}, {"time": 1.2667, "x": 1.115, "y": 0.98, "curve": [1.283, 1.115, 1.317, 1.176, 1.283, 0.98, 1.317, 0.886]}, {"time": 1.3333, "x": 1.176, "y": 0.886, "curve": [1.417, 1.176, 1.583, 0.844, 1.417, 0.886, 1.583, 1.141]}, {"time": 1.6667, "x": 0.844, "y": 1.141, "curve": [1.75, 0.844, 1.917, 1, 1.75, 1.141, 1.917, 1]}, {"time": 2}]}}}, "teleport": {"slots": {"MASK": {"attachment": [{"name": "MASK"}]}, "TeleportEffect": {"attachment": [{"time": 0.1333, "name": "images/Teleport_1"}, {"time": 0.2, "name": "images/Teleport_2"}, {"time": 0.2667, "name": "images/Teleport_3"}, {"time": 0.3333, "name": "images/Teleport_4"}, {"time": 0.4333}, {"time": 0.5667, "name": "images/Teleport_1"}, {"time": 0.6333, "name": "images/Teleport_2"}, {"time": 0.7, "name": "images/Teleport_3"}, {"time": 0.7333, "name": "images/Teleport_4"}, {"time": 0.7667}]}}, "bones": {"FACE": {"translate": [{"x": 0.84, "y": 4.16, "curve": [0.017, -23.65, 0.036, -44.45, 0.017, 3.67, 0.036, 3.25]}, {"time": 0.0667, "x": -45.76, "y": 3.22, "curve": [0.175, -45.76, 0.392, 18.32, 0.175, 3.22, 0.392, 6.16]}, {"time": 0.5, "x": 18.32, "y": 6.16, "curve": [0.55, 18.32, 0.65, 12.06, 0.55, 6.16, 0.65, 4.16]}, {"time": 0.7, "x": 12.06, "y": 4.16, "curve": [0.758, 12.06, 0.875, -21.84, 0.758, 4.16, 0.875, 4.16]}, {"time": 0.9333, "x": -21.84, "y": 4.16, "curve": [1.015, -21.84, 1.052, -5.93, 1.015, 4.16, 1.052, 4.16]}, {"time": 1.1, "x": 7.05, "y": 4.16}], "scale": [{}, {"time": 0.0667, "x": 1.219, "y": 1.219}, {"time": 0.2}]}, "MAIN": {"translate": [{"y": -33.8, "curve": [0, 0, 0.011, 0, 0, -27.27, 0.011, -20.95]}, {"time": 0.0333, "y": -14.83, "curve": [0.083, 0, 0.233, 0, 0.083, -14.83, 0.233, -379.43]}, {"time": 0.2333, "y": -879.18, "curve": "stepped"}, {"time": 0.5, "y": -879.18, "curve": [0.524, 0, 0.675, 0, 0.524, 22.21, 0.675, 203.27]}, {"time": 0.7333, "y": 203.27, "curve": [0.904, 0, 0.917, 0, 0.904, 203.27, 0.917, 22.26]}, {"time": 1.1, "y": 17.53}], "scale": [{"x": 1.509, "y": 0.663, "curve": [0, 0.959, 0.05, 0.76, 0, 1.189, 0.05, 1.379]}, {"time": 0.0667, "x": 0.76, "y": 1.379, "curve": [0.175, 0.76, 0.392, 0.656, 0.175, 1.379, 0.392, 1.217]}, {"time": 0.5, "x": 0.656, "y": 1.217, "curve": [0.583, 0.656, 0.75, 0.987, 0.583, 1.217, 0.75, 1.006]}, {"time": 0.8333, "x": 0.987, "y": 1.006, "curve": [0.883, 1.013, 0.933, 1.038, 0.883, 0.984, 0.933, 0.962]}, {"time": 0.9667, "x": 1.038, "y": 0.962, "curve": [1, 1.038, 1.05, 1.013, 1, 0.962, 1.05, 0.984]}, {"time": 1.1, "x": 0.987, "y": 1.006}]}, "Spike5": {"rotate": [{"value": -43.86, "curve": [0.025, 24.37, 0.2, 24.37]}, {"time": 0.2667, "value": 24.37, "curve": [0.333, 24.37, 0.467, -26.01]}, {"time": 0.5333, "value": -26.01, "curve": [0.592, -26.01, 0.708, 12.93]}, {"time": 0.7667, "value": 12.93, "curve": [0.8, 12.93, 0.867, -8.26]}, {"time": 0.9, "value": -8.26, "curve": [0.925, -10.3, 0.948, -11.66]}, {"time": 0.9667, "value": -11.66, "curve": [0.999, -11.66, 1.056, -1.65]}, {"time": 1.1, "value": 3.35}]}, "Spike4": {"rotate": [{"value": -45.19, "curve": [0.022, 23.45, 0.175, 23.45]}, {"time": 0.2333, "value": 23.45, "curve": [0.3, 23.45, 0.433, -27.61]}, {"time": 0.5, "value": -27.61, "curve": [0.558, -27.61, 0.675, 11.47]}, {"time": 0.7333, "value": 11.47, "curve": [0.767, 11.47, 0.833, -9.26]}, {"time": 0.8667, "value": -9.26, "curve": [0.892, -10.73, 0.914, -11.66]}, {"time": 0.9333, "value": -11.66, "curve": [0.974, -11.66, 1.047, -0.22]}, {"time": 1.1, "value": 4.35}]}, "Spike3": {"rotate": [{"value": -46.31, "curve": [0.019, 18.03, 0.15, 18.03]}, {"time": 0.2, "value": 18.03, "curve": [0.267, 18.03, 0.4, -27.12]}, {"time": 0.4667, "value": -27.12, "curve": [0.533, -27.12, 0.667, 11.11]}, {"time": 0.7333, "value": 11.11, "curve": [0.767, 11.11, 0.833, -10.13]}, {"time": 0.8667, "value": -10.13, "curve": [0.896, -13.14, 0.932, -19.66]}, {"time": 0.9667, "value": -25.43, "curve": [1.005, -8.22, 1.041, 6.75]}, {"time": 1.0667, "value": 6.75, "curve": [1.076, 6.75, 1.088, 6.18]}, {"time": 1.1, "value": 5.22}]}, "Spike2": {"rotate": [{"value": -40.82, "curve": [0.016, 25.68, 0.125, 25.68]}, {"time": 0.1667, "value": 25.68, "curve": [0.233, 25.68, 0.367, -21.95]}, {"time": 0.4333, "value": -21.95, "curve": [0.508, -21.95, 0.658, 16.45]}, {"time": 0.7333, "value": 16.45, "curve": [0.758, 16.45, 0.808, -4.89]}, {"time": 0.8333, "value": -4.89, "curve": [0.882, -10.7, 0.934, -17.97]}, {"time": 0.9667, "value": -17.97, "curve": [1.001, -17.97, 1.049, -9.81]}, {"time": 1.1, "value": -0.02}]}, "Spike1": {"rotate": [{"value": -39.2, "curve": [0.01, 27.66, 0.075, 27.66]}, {"time": 0.1, "value": 27.66, "curve": [0.167, 27.66, 0.3, -20.49]}, {"time": 0.3667, "value": -20.49, "curve": [0.45, -20.49, 0.617, 17.99]}, {"time": 0.7, "value": 17.99, "curve": [0.733, 17.99, 0.8, -2.45]}, {"time": 0.8333, "value": -2.45, "curve": [0.883, -10.21, 0.933, -17.97]}, {"time": 0.9667, "value": -17.97, "curve": [1, -17.97, 1.05, -10.24]}, {"time": 1.1, "value": -2.5}]}, "Spike10": {"rotate": [{"value": 34.26, "curve": [0.01, -29.25, 0.075, -29.25]}, {"time": 0.1, "value": -29.25, "curve": [0.167, -29.25, 0.3, 17.23]}, {"time": 0.3667, "value": 17.23, "curve": [0.45, 17.23, 0.617, -24.45]}, {"time": 0.7, "value": -24.45, "curve": [0.733, -24.45, 0.8, -2.18]}, {"time": 0.8333, "value": -2.18, "curve": [0.884, 11.35, 0.931, 21.02]}, {"time": 0.9667, "value": 21.02, "curve": [0.999, 21.02, 1.054, 11.26]}, {"time": 1.1, "value": 5.4}]}, "Spike6": {"rotate": [{"value": 39.38, "curve": [0.016, -22.77, 0.125, -22.77]}, {"time": 0.1667, "value": -22.77, "curve": [0.233, -22.77, 0.367, 21.76]}, {"time": 0.4333, "value": 21.76, "curve": [0.508, 21.76, 0.658, -19.61]}, {"time": 0.7333, "value": -19.61, "curve": [0.758, -19.61, 0.808, 3.55]}, {"time": 0.8333, "value": 3.55, "curve": [0.882, 11.31, 0.934, 21.02]}, {"time": 0.9667, "value": 21.02, "curve": [1.001, 21.02, 1.049, 11.31]}, {"time": 1.1, "value": -0.33}]}, "Spike7": {"rotate": [{"value": 43.76, "curve": [0.019, -16.22, 0.15, -16.22]}, {"time": 0.2, "value": -16.22, "curve": [0.267, -16.22, 0.4, 26]}, {"time": 0.4667, "value": 26, "curve": [0.533, 26, 0.667, -15.29]}, {"time": 0.7333, "value": -15.29, "curve": [0.767, -15.29, 0.833, 7.74]}, {"time": 0.8667, "value": 7.74, "curve": [0.896, 11.52, 0.932, 19.72]}, {"time": 0.9667, "value": 26.97, "curve": [1.005, 9.48, 1.041, -5.73]}, {"time": 1.0667, "value": -5.73, "curve": [1.076, -5.73, 1.088, -5.28]}, {"time": 1.1, "value": -4.52}]}, "Spike8": {"rotate": [{"value": 42.87, "curve": [0.022, -21.41, 0.175, -21.41]}, {"time": 0.2333, "value": -21.41, "curve": [0.3, -21.41, 0.433, 26.26]}, {"time": 0.5, "value": 26.26, "curve": [0.558, 26.26, 0.675, -15.65]}, {"time": 0.7333, "value": -15.65, "curve": [0.767, -15.65, 0.833, 7.05]}, {"time": 0.8667, "value": 7.05, "curve": [0.892, 8.22, 0.914, 8.96]}, {"time": 0.9333, "value": 8.96, "curve": [0.974, 8.96, 1.047, -0.17]}, {"time": 1.1, "value": -3.82}]}, "Spike9": {"rotate": [{"value": 41.81, "curve": [0.025, -22.06, 0.2, -22.06]}, {"time": 0.2667, "value": -22.06, "curve": [0.333, -22.06, 0.467, -16.81]}, {"time": 0.5333, "value": -16.81, "curve": [0.558, -16.81, 0.608, 6.25]}, {"time": 0.6333, "value": 6.25, "curve": [0.76, 7.87, 0.875, 8.96]}, {"time": 0.9667, "value": 8.96, "curve": [0.999, 8.96, 1.056, 0.97]}, {"time": 1.1, "value": -3.02}]}, "Tentacle5": {"rotate": [{"value": 6.03, "curve": [0.024, 8.17, 0.047, 9.39]}, {"time": 0.0667, "value": 9.39, "curve": [0.175, 9.39, 0.392, -49.78]}, {"time": 0.5, "value": -49.78, "curve": [0.524, -49.78, 0.57, -5.1]}, {"time": 0.6, "value": 5.41, "curve": [0.612, 7.94, 0.623, 9.39]}, {"time": 0.6333, "value": 9.39, "curve": [0.75, 9.39, 0.983, -49.78]}, {"time": 1.1, "value": -49.78}]}, "Tentacle3_Mid": {"rotate": [{"value": -3.27, "curve": [0.13, 21.42, 0.277, 56.4]}, {"time": 0.3667, "value": 56.4, "curve": [0.403, 56.4, 0.449, 48]}, {"time": 0.5, "value": 36.14, "curve": [0.511, 11.69, 0.525, -29.86]}, {"time": 0.5333, "value": -29.86, "curve": [0.551, -29.86, 0.575, -18.51]}, {"time": 0.6, "value": -3.82, "curve": [0.765, 20.88, 0.953, 56.4]}, {"time": 1.0667, "value": 56.4, "curve": [1.077, 56.4, 1.088, 54.76]}, {"time": 1.1, "value": 51.86}]}, "Tentacle3_Btm": {"rotate": [{"value": -19.18, "curve": [0.143, 10.49, 0.328, 73.54]}, {"time": 0.4333, "value": 73.54, "curve": [0.453, 73.54, 0.475, 69.14]}, {"time": 0.5, "value": 61.89, "curve": [0.51, 35.84, 0.525, -37.96]}, {"time": 0.5333, "value": -37.96, "curve": [0.552, -37.96, 0.575, -29.31]}, {"time": 0.6, "value": -16.08, "curve": [0.768, 14.65, 0.979, 73.54]}, {"time": 1.1, "value": 73.54}]}, "Tentacle6": {"rotate": [{"value": -8.02, "curve": [0.024, -9.72, 0.047, -10.68]}, {"time": 0.0667, "value": -10.68, "curve": [0.175, -10.68, 0.392, 36.14]}, {"time": 0.5, "value": 36.14, "curve": [0.524, 36.14, 0.57, 0.78]}, {"time": 0.6, "value": -7.54, "curve": [0.612, -9.53, 0.623, -10.68]}, {"time": 0.6333, "value": -10.68, "curve": [0.75, -10.68, 0.983, 36.14]}, {"time": 1.1, "value": 36.14}]}, "Tentacle3_Mid2": {"rotate": [{"value": -1.59, "curve": [0.13, 24.91, 0.277, 62.45]}, {"time": 0.3667, "value": 62.45, "curve": [0.403, 62.45, 0.449, 53.43]}, {"time": 0.5, "value": 40.71, "curve": [0.511, 14.47, 0.525, -30.13]}, {"time": 0.5333, "value": -30.13, "curve": [0.551, -30.13, 0.575, -17.95]}, {"time": 0.6, "value": -2.18, "curve": [0.765, 24.33, 0.953, 62.45]}, {"time": 1.0667, "value": 62.45, "curve": [1.076, 62.45, 1.088, 58.81]}, {"time": 1.1, "value": 52.81}]}, "Tentacle3_Btm2": {"rotate": [{"value": -23.87, "curve": [0.143, 3.75, 0.328, 62.45]}, {"time": 0.4333, "value": 62.45, "curve": [0.453, 62.45, 0.475, 58.35]}, {"time": 0.5, "value": 51.6, "curve": [0.51, 27.35, 0.525, -41.36]}, {"time": 0.5333, "value": -41.36, "curve": [0.552, -41.36, 0.575, -33.3]}, {"time": 0.6, "value": -20.98, "curve": [0.757, 7.62, 0.954, 62.45]}, {"time": 1.0667, "value": 62.45, "curve": [1.077, 62.45, 1.088, 61.9]}, {"time": 1.1, "value": 60.88}]}, "Tentacle2_Btm2": {"rotate": [{"value": -38.29, "curve": [0.152, -7.3, 0.353, 62.45]}, {"time": 0.4667, "value": 62.45, "curve": [0.477, 62.45, 0.488, 60]}, {"time": 0.5, "value": 55.68, "curve": [0.51, 34.28, 0.525, -56.67]}, {"time": 0.5333, "value": -56.67, "curve": [0.552, -56.67, 0.575, -49.72]}, {"time": 0.6, "value": -38.92, "curve": [0.752, -8.2, 0.953, 62.45]}, {"time": 1.0667, "value": 62.45, "curve": [1.077, 62.45, 1.088, 60]}, {"time": 1.1, "value": 55.68}]}, "Tentacle2_Mid2": {"rotate": [{"value": 5.37, "curve": [0.121, 31.15, 0.251, 62.45]}, {"time": 0.3333, "value": 62.45, "curve": [0.376, 62.45, 0.437, 19.91]}, {"time": 0.5, "value": -30.13, "curve": [0.526, -30.13, 0.562, -12.53]}, {"time": 0.6, "value": 8.18, "curve": [0.747, 33.42, 0.901, 62.45]}, {"time": 1, "value": 62.45, "curve": [1.026, 62.45, 1.062, 46.14]}, {"time": 1.1, "value": 26.96}]}, "Tentacle4": {"rotate": [{"value": -8.58, "curve": [0.024, -9.92, 0.047, -10.68]}, {"time": 0.0667, "value": -10.68, "curve": [0.175, -10.68, 0.392, 26.28]}, {"time": 0.5, "value": 26.28, "curve": [0.524, 26.28, 0.57, -1.63]}, {"time": 0.6, "value": -8.2, "curve": [0.612, -9.77, 0.623, -10.68]}, {"time": 0.6333, "value": -10.68, "curve": [0.75, -10.68, 0.983, 26.28]}, {"time": 1.1, "value": 26.28}]}, "Tentacle2_Btm": {"rotate": [{"value": -46.79, "curve": [0.152, -15.83, 0.353, 53.84]}, {"time": 0.4667, "value": 53.84, "curve": [0.477, 53.84, 0.488, 51.4]}, {"time": 0.5, "value": 47.08, "curve": [0.51, 25.7, 0.525, -65.15]}, {"time": 0.5333, "value": -65.15, "curve": [0.552, -65.15, 0.575, -58.21]}, {"time": 0.6, "value": -47.42, "curve": [0.752, -16.73, 0.953, 53.84]}, {"time": 1.0667, "value": 53.84, "curve": [1.077, 53.84, 1.088, 51.4]}, {"time": 1.1, "value": 47.08}]}, "Tentacle2_Mid": {"rotate": [{"value": -3.22, "curve": [0.121, 20.8, 0.251, 49.96]}, {"time": 0.3333, "value": 49.96, "curve": [0.376, 49.96, 0.437, 10.33]}, {"time": 0.5, "value": -36.3, "curve": [0.526, -36.3, 0.562, -19.9]}, {"time": 0.6, "value": -0.6, "curve": [0.747, 22.92, 0.901, 49.96]}, {"time": 1, "value": 49.96, "curve": [1.026, 49.96, 1.062, 34.77]}, {"time": 1.1, "value": 16.89}]}, "Tentacle3": {"rotate": [{"value": 4.21, "curve": [0.024, 5.78, 0.047, 6.68]}, {"time": 0.0667, "value": 6.68, "curve": [0.175, 6.68, 0.392, -36.75]}, {"time": 0.5, "value": -36.75, "curve": [0.524, -36.75, 0.57, -3.96]}, {"time": 0.6, "value": 3.76, "curve": [0.612, 5.61, 0.623, 6.68]}, {"time": 0.6333, "value": 6.68, "curve": [0.75, 6.68, 0.983, -36.75]}, {"time": 1.1, "value": -36.75}]}, "DangleHandle": {"rotate": [{"value": -15.4, "curve": [0.124, -4.37, 0.278, 16.3]}, {"time": 0.3667, "value": 16.3, "curve": [0.403, 16.3, 0.449, 12.3]}, {"time": 0.5, "value": 6.7, "curve": [0.532, 3.66, 0.566, 0.04]}, {"time": 0.6, "value": -3.58, "curve": [0.663, -13.51, 0.725, -23.45]}, {"time": 0.7667, "value": -23.45, "curve": [0.785, -23.45, 0.808, -19.55]}, {"time": 0.8333, "value": -13.82, "curve": [0.891, -2.53, 0.96, 16.3]}, {"time": 1, "value": 16.3, "curve": [1.027, 16.3, 1.062, 12.3]}, {"time": 1.1, "value": 6.7}], "translate": [{"x": -8.18, "y": 2.62, "curve": [0.099, 16.95, 0.201, 43.87, 0.099, 3.65, 0.201, 4.74]}, {"time": 0.2667, "x": 43.87, "y": 4.74, "curve": [0.325, 43.87, 0.413, 19.95, 0.325, 4.74, 0.413, 3.77]}, {"time": 0.5, "x": -3.96, "y": 2.8, "curve": [0.588, -27.88, 0.675, -51.8, 0.588, 1.82, 0.675, 0.85]}, {"time": 0.7333, "x": -51.8, "y": 0.85, "curve": [0.759, -51.8, 0.796, -28.65, 0.759, 0.85, 0.796, 1.79]}, {"time": 0.8333, "x": -3.96, "y": 2.8, "curve": [0.883, 19.95, 0.933, 43.87, 0.883, 3.77, 0.933, 4.74]}, {"time": 0.9667, "x": 43.87, "y": 4.74, "curve": [1, 43.87, 1.05, 19.95, 1, 4.74, 1.05, 3.77]}, {"time": 1.1, "x": -3.96, "y": 2.8}], "scale": [{"y": 0.313, "curve": [0.125, 1, 0.375, 1, 0.125, 0.313, 0.375, 1.723]}, {"time": 0.5, "y": 1.723, "curve": [0.583, 1, 0.75, 1, 0.583, 1.723, 0.75, 0.313]}, {"time": 0.8333, "y": 0.313, "curve": [0.9, 1, 1.033, 1, 0.9, 0.313, 1.033, 1.723]}, {"time": 1.1, "y": 1.723}]}, "Tentacles": {"scale": [{"x": 1.199}, {"time": 0.5, "x": 0.699}, {"time": 0.8333, "x": 1.199}, {"time": 1.1, "x": 0.699}]}, "Mouth8": {"rotate": [{"value": -32.57, "curve": [0.025, -38.95, 0.049, -43.41]}, {"time": 0.0667, "value": -43.41, "curve": [0.133, -43.41, 0.267, 4.89]}, {"time": 0.3333, "value": 4.89, "curve": [0.374, 4.89, 0.443, -17.99]}, {"time": 0.5, "value": -31.71, "curve": [0.525, -38.54, 0.549, -43.41]}, {"time": 0.5667, "value": -43.41, "curve": [0.608, -43.41, 0.692, 4.89]}, {"time": 0.7333, "value": 4.89, "curve": [0.758, 4.89, 0.799, -18.4]}, {"time": 0.8333, "value": -31.71, "curve": [0.846, -38.54, 0.858, -43.41]}, {"time": 0.8667, "value": -43.41, "curve": [0.9, -43.41, 0.967, 4.89]}, {"time": 1, "value": 4.89, "curve": [1.024, 4.89, 1.066, -17.99]}, {"time": 1.1, "value": -31.71}]}, "Mouth5": {"rotate": [{"value": 20.26, "curve": [0.061, 15.29, 0.151, -2.12]}, {"time": 0.2, "value": -2.12, "curve": [0.267, -2.12, 0.4, 22.13]}, {"time": 0.4667, "value": 22.13, "curve": [0.476, 22.13, 0.488, 20.92]}, {"time": 0.5, "value": 18.98, "curve": [0.564, 12.95, 0.651, -2.12]}, {"time": 0.7, "value": -2.12, "curve": [0.725, -2.12, 0.775, 22.13]}, {"time": 0.8, "value": 22.13, "curve": [0.81, 22.13, 0.821, 20.97]}, {"time": 0.8333, "value": 18.98, "curve": [0.865, 12.95, 0.909, -2.12]}, {"time": 0.9333, "value": -2.12, "curve": [0.967, -2.12, 1.033, 22.13]}, {"time": 1.0667, "value": 22.13, "curve": [1.076, 22.13, 1.088, 20.92]}, {"time": 1.1, "value": 18.98}]}, "Mouth4": {"rotate": [{"value": 7.72, "curve": [0.038, 2.47, 0.074, -2.12]}, {"time": 0.1, "value": -2.12, "curve": [0.167, -2.12, 0.3, 22.13]}, {"time": 0.3667, "value": 22.13, "curve": [0.399, 22.13, 0.452, 13.62]}, {"time": 0.5, "value": 6.8, "curve": [0.538, 1.94, 0.574, -2.12]}, {"time": 0.6, "value": -2.12, "curve": [0.642, -2.12, 0.725, 22.13]}, {"time": 0.7667, "value": 22.13, "curve": [0.783, 22.13, 0.809, 13.88]}, {"time": 0.8333, "value": 6.8, "curve": [0.846, 1.94, 0.858, -2.12]}, {"time": 0.8667, "value": -2.12, "curve": [0.9, -2.12, 0.967, 22.13]}, {"time": 1, "value": 22.13, "curve": [1.025, 22.13, 1.064, 13.62]}, {"time": 1.1, "value": 6.8}]}, "Mouth7": {"rotate": [{"value": -40.38, "curve": [0.069, -31.32, 0.176, 4.89]}, {"time": 0.2333, "value": 4.89, "curve": [0.292, 4.89, 0.408, -43.41]}, {"time": 0.4667, "value": -43.41, "curve": [0.477, -43.41, 0.488, -42.68]}, {"time": 0.5, "value": -41.36, "curve": [0.558, -33.65, 0.651, 4.89]}, {"time": 0.7, "value": 4.89, "curve": [0.725, 4.89, 0.775, -43.41]}, {"time": 0.8, "value": -43.41, "curve": [0.81, -43.41, 0.821, -42.66]}, {"time": 0.8333, "value": -41.36, "curve": [0.862, -33.65, 0.909, 4.89]}, {"time": 0.9333, "value": 4.89, "curve": [0.967, 4.89, 1.033, -43.41]}, {"time": 1.0667, "value": -43.41, "curve": [1.077, -43.41, 1.088, -42.68]}, {"time": 1.1, "value": -41.36}]}, "Mouth3": {"rotate": [{"value": -2.12, "curve": [0.067, -2.12, 0.2, 22.13]}, {"time": 0.2667, "value": 22.13, "curve": [0.325, 22.13, 0.442, -2.12]}, {"time": 0.5, "value": -2.12, "curve": [0.558, -2.12, 0.675, 22.13]}, {"time": 0.7333, "value": 22.13, "curve": [0.758, 22.13, 0.808, -2.12]}, {"time": 0.8333, "value": -2.12, "curve": [0.867, -2.12, 0.933, 22.13]}, {"time": 0.9667, "value": 22.13, "curve": [1, 22.13, 1.067, -2.12]}, {"time": 1.1, "value": -2.12}]}, "Mouth6": {"rotate": [{"value": -27.19, "curve": [0.06, -13.44, 0.126, 4.89]}, {"time": 0.1667, "value": 4.89, "curve": [0.225, 4.89, 0.342, -43.41]}, {"time": 0.4, "value": -43.41, "curve": [0.426, -43.41, 0.462, -35.33]}, {"time": 0.5, "value": -25.64, "curve": [0.56, -12.07, 0.626, 4.89]}, {"time": 0.6667, "value": 4.89, "curve": [0.7, 4.89, 0.767, -43.41]}, {"time": 0.8, "value": -43.41, "curve": [0.809, -43.41, 0.821, -35.51]}, {"time": 0.8333, "value": -25.64, "curve": [0.858, -12.07, 0.884, 4.89]}, {"time": 0.9, "value": 4.89, "curve": [0.933, 4.89, 1, -43.41]}, {"time": 1.0333, "value": -43.41, "curve": [1.051, -43.41, 1.075, -35.33]}, {"time": 1.1, "value": -25.64}]}, "Mouth2": {"scale": [{}, {"time": 0.0667, "x": 1.013, "y": 1.013, "curve": [0.068, 1.003, 0.117, 1, 0.068, 1.935, 0.117, 2.226]}, {"time": 0.1333, "y": 2.226, "curve": [0.225, 1, 0.408, 1.013, 0.225, 2.226, 0.408, 1.013]}, {"time": 0.5, "x": 1.013, "y": 1.013}, {"time": 0.8333}]}, "Tentacle3_Btm3": {"rotate": [{"value": -19.18, "curve": [0.143, 10.49, 0.328, 73.54]}, {"time": 0.4333, "value": 73.54, "curve": [0.453, 73.54, 0.475, 69.14]}, {"time": 0.5, "value": 61.89, "curve": [0.51, 35.84, 0.525, -37.96]}, {"time": 0.5333, "value": -37.96, "curve": [0.552, -37.96, 0.575, -29.31]}, {"time": 0.6, "value": -16.08, "curve": [0.757, 14.65, 0.954, 73.54]}, {"time": 1.0667, "value": 73.54, "curve": [1.076, 73.54, 1.088, 69.14]}, {"time": 1.1, "value": 61.89}]}, "Tentacle3_Mid3": {"rotate": [{"value": -3.27, "curve": [0.13, 21.42, 0.277, 56.4]}, {"time": 0.3667, "value": 56.4, "curve": [0.403, 56.4, 0.449, 48]}, {"time": 0.5, "value": 36.14, "curve": [0.511, 11.69, 0.525, -29.86]}, {"time": 0.5333, "value": -29.86, "curve": [0.551, -29.86, 0.575, -18.51]}, {"time": 0.6, "value": -3.82, "curve": [0.742, 20.88, 0.903, 56.4]}, {"time": 1, "value": 56.4, "curve": [1.027, 56.4, 1.062, 48]}, {"time": 1.1, "value": 36.14}]}, "Tentacle1": {"rotate": [{"value": 6.03, "curve": [0.024, 8.17, 0.047, 9.39]}, {"time": 0.0667, "value": 9.39, "curve": [0.175, 9.39, 0.392, -49.78]}, {"time": 0.5, "value": -49.78, "curve": [0.524, -49.78, 0.57, -5.1]}, {"time": 0.6, "value": 5.41, "curve": [0.612, 7.94, 0.623, 9.39]}, {"time": 0.6333, "value": 9.39, "curve": [0.75, 9.39, 0.983, -49.78]}, {"time": 1.1, "value": -49.78}]}, "Tentacle3_Btm4": {"rotate": [{"value": -23.87, "curve": [0.143, 3.75, 0.328, 62.45]}, {"time": 0.4333, "value": 62.45, "curve": [0.453, 62.45, 0.475, 58.35]}, {"time": 0.5, "value": 51.6, "curve": [0.51, 27.35, 0.525, -41.36]}, {"time": 0.5333, "value": -41.36, "curve": [0.552, -41.36, 0.575, -33.3]}, {"time": 0.6, "value": -20.98, "curve": [0.757, 7.62, 0.954, 62.45]}, {"time": 1.0667, "value": 62.45, "curve": [1.076, 62.45, 1.088, 58.35]}, {"time": 1.1, "value": 51.6}]}, "Tentacle3_Mid4": {"rotate": [{"value": -1.59, "curve": [0.13, 24.91, 0.277, 62.45]}, {"time": 0.3667, "value": 62.45, "curve": [0.403, 62.45, 0.449, 53.43]}, {"time": 0.5, "value": 40.71, "curve": [0.511, 14.47, 0.525, -30.13]}, {"time": 0.5333, "value": -30.13, "curve": [0.551, -30.13, 0.575, -17.95]}, {"time": 0.6, "value": -2.18, "curve": [0.742, 24.33, 0.903, 62.45]}, {"time": 1, "value": 62.45, "curve": [1.027, 62.45, 1.062, 53.43]}, {"time": 1.1, "value": 40.71}]}, "Tentacle2": {"rotate": [{"value": -8.02, "curve": [0.024, -9.72, 0.047, -10.68]}, {"time": 0.0667, "value": -10.68, "curve": [0.175, -10.68, 0.392, 36.14]}, {"time": 0.5, "value": 36.14, "curve": [0.524, 36.14, 0.57, 0.78]}, {"time": 0.6, "value": -7.54, "curve": [0.612, -9.53, 0.623, -10.68]}, {"time": 0.6333, "value": -10.68, "curve": [0.75, -10.68, 0.983, 36.14]}, {"time": 1.1, "value": 36.14}]}}, "drawOrder": [{"offsets": [{"slot": "Eyes", "offset": -2}, {"slot": "images/Eye", "offset": 2}, {"slot": "images/Eye2", "offset": 2}]}], "events": [{"time": 0.4667, "name": "teleport"}]}}}