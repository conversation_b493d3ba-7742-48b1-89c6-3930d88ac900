{"skeleton": {"hash": "AAVlM3uJh3s", "spine": "4.1.23-beta", "x": -809.16, "y": -73.54, "width": 1287.65, "height": 1039.44, "images": "./images/", "audio": ""}, "bones": [{"name": "root"}, {"name": "hip", "parent": "root", "rotation": 8.96, "x": -122.52, "y": 392.65, "color": "fbff00ff"}, {"name": "torso1", "parent": "hip", "length": 126.26, "rotation": -4.98, "x": 30.04, "y": -0.4, "color": "eaff00ff"}, {"name": "saddle", "parent": "torso1", "length": 50.92, "rotation": 91.8, "x": 25.31, "y": 70.65, "color": "ff7300ff"}, {"name": "spineboy-hip", "parent": "saddle", "length": 0.53, "rotation": 90.02, "x": 81.88, "y": 2.69, "color": "e8ff00ff"}, {"name": "spineboy-torso", "parent": "spineboy-hip", "length": 122.45, "rotation": -75.86, "x": 1.05, "y": -2.11, "color": "e8ff00ff"}, {"name": "torso2", "parent": "torso1", "length": 121.2, "rotation": 39.85, "x": 126.26, "y": -0.38, "color": "e9ff00ff"}, {"name": "neck", "parent": "torso2", "length": 70.6, "rotation": 41.38, "x": 121.2, "y": 0.35, "color": "eaff00ff"}, {"name": "head", "parent": "neck", "length": 105.51, "rotation": 9.83, "x": 70.6, "y": 0.04, "color": "eaff00ff"}, {"name": "horn-back", "parent": "head", "length": 73.78, "rotation": 44.32, "x": 104.76, "y": -242.01, "color": "e07800ff"}, {"name": "spineboy-back-arm-target", "parent": "horn-back", "rotation": -133.55, "x": 232.68, "y": 245.85, "transform": "noScale", "color": "ff3f00ff"}, {"name": "back-arm", "parent": "spineboy-torso", "length": 67.21, "rotation": -120.9, "x": 96.33, "y": -38.47, "color": "e07800ff"}, {"name": "back-bracer", "parent": "back-arm", "length": 43.69, "rotation": 17.48, "x": 67.22, "y": -0.32, "color": "e07800ff"}, {"name": "back-arm1", "parent": "torso2", "length": 109.56, "rotation": -124.72, "x": 83.68, "y": -83.24, "color": "e07800ff"}, {"name": "back-arm2", "parent": "back-arm1", "length": 85.8, "rotation": 123.56, "x": 109.57, "y": -0.01, "color": "e07800ff"}, {"name": "back-foot-target", "parent": "root", "x": 33.44, "y": 30.82, "color": "ff3f00ff"}, {"name": "back-leg-target", "parent": "back-foot-target", "x": -127.51, "y": 75.99, "color": "ff3f00ff"}, {"name": "back-leg1", "parent": "hip", "length": 226.28, "rotation": -54.76, "x": 55.19, "y": -71.25, "color": "e07800ff"}, {"name": "back-leg2", "parent": "back-leg1", "length": 172.59, "rotation": -92.25, "x": 226.32, "y": 0.23, "color": "e07800ff"}, {"name": "back-leg3", "parent": "back-leg2", "length": 103.05, "rotation": 82.82, "x": 172.32, "y": 2.21, "color": "e07800ff"}, {"name": "back-foot1", "parent": "back-leg3", "length": 84.51, "rotation": 75.43, "x": 102.38, "y": -0.03, "color": "e07800ff"}, {"name": "back-foot2", "parent": "back-foot1", "length": 102.31, "rotation": -6.14, "x": 84.5, "y": -0.35, "transform": "noRotationOrReflection", "color": "e07800ff"}, {"name": "back-hand", "parent": "back-arm2", "length": 45.81, "rotation": -76.28, "x": 85.8, "y": 0.11, "color": "e07800ff"}, {"name": "back-hand2", "parent": "spineboy-back-arm-target", "length": 41.98, "rotation": 27.78, "x": -0.03, "y": 0.05, "transform": "noRotationOrReflection", "color": "e07800ff"}, {"name": "spineboy-back-foot-target", "parent": "saddle", "x": -30.44, "y": -100.08, "color": "ff3f00ff"}, {"name": "back-thigh", "parent": "spineboy-hip", "length": 71.16, "rotation": 160.75, "x": -9.57, "y": 2.32, "color": "e07800ff"}, {"name": "back-knee", "parent": "back-thigh", "length": 97.17, "rotation": -54.98, "x": 71.16, "y": -0.28, "color": "e07800ff"}, {"name": "neck2", "parent": "spineboy-torso", "length": 32.05, "rotation": -45.23, "x": 113.44, "y": -15.22, "color": "e8ff00ff"}, {"name": "head2", "parent": "neck2", "length": 167.19, "rotation": 11.66, "x": 25.68, "y": -0.77, "transform": "noScale", "color": "e7ff00ff"}, {"name": "bone", "parent": "head2", "length": 39.92, "rotation": -35.23, "x": 166.09, "y": -79.27, "color": "e7ff00ff"}, {"name": "bone2", "parent": "bone", "length": 47.42, "rotation": 51.8, "x": 39.92, "color": "e7ff00ff"}, {"name": "bone3", "parent": "head2", "length": 45.41, "rotation": -12.34, "x": 179.8, "y": -20.91, "color": "e7ff00ff"}, {"name": "bone4", "parent": "bone3", "length": 43.31, "rotation": 42.01, "x": 44.64, "y": 0.3, "color": "e7ff00ff"}, {"name": "bone5", "parent": "bone4", "length": 44.88, "rotation": 48.8, "x": 43.31, "color": "e7ff00ff"}, {"name": "horn-front", "parent": "head", "length": 87.48, "rotation": 49.36, "x": 87.97, "y": -235.15, "color": "15ff00ff"}, {"name": "spineboy-front-arm-target", "parent": "horn-front", "rotation": -138.6, "x": 294.58, "y": 234.18, "color": "ff3f00ff"}, {"name": "front-arm", "parent": "spineboy-torso", "length": 74.52, "rotation": -118.17, "x": 101.38, "y": 9.79, "color": "14ff00ff"}, {"name": "front-bracer", "parent": "front-arm", "length": 39.85, "rotation": 20.31, "x": 74.52, "y": -0.42, "color": "14ff00ff"}, {"name": "front-arm1", "parent": "torso2", "length": 109.99, "rotation": 224.54, "x": 73, "y": -72.46, "color": "15ff00ff"}, {"name": "front-arm2", "parent": "front-arm1", "length": 86.33, "rotation": 105.24, "x": 109.99, "y": 0.2, "color": "15ff00ff"}, {"name": "front-foot-target", "parent": "root", "rotation": -6.96, "x": -45.8, "y": -28.67, "color": "ff3f00ff"}, {"name": "front-leg-target", "parent": "front-foot-target", "x": -106.06, "y": 115.58, "color": "ff3f00ff"}, {"name": "front-leg1", "parent": "hip", "length": 251.75, "rotation": -51.51, "x": 27.36, "y": -28.28, "color": "15ff00ff"}, {"name": "front-leg2", "parent": "front-leg1", "length": 208.55, "rotation": 261.94, "x": 251.04, "y": 0.16, "color": "15ff00ff"}, {"name": "front-leg3", "parent": "front-leg2", "length": 118.18, "rotation": 85.46, "x": 208.5, "y": -1.64, "color": "15ff00ff"}, {"name": "front-foot1", "parent": "front-leg3", "length": 57.79, "rotation": 54.46, "x": 118.2, "y": -0.79, "color": "15ff00ff"}, {"name": "front-foot2", "parent": "front-foot1", "length": 56.19, "rotation": -2.16, "x": 57.79, "y": -0.02, "scaleX": 0.731, "scaleY": 0.823, "transform": "onlyTranslation", "color": "15ff00ff"}, {"name": "front-foot3", "parent": "front-foot2", "length": 129.88, "rotation": -2.7, "x": 49.71, "y": 20.66, "scaleX": 1.155, "color": "15ff00ff"}, {"name": "front-hand", "parent": "front-arm2", "length": 47.56, "rotation": -56.83, "x": 86.33, "y": 0.06, "color": "15ff00ff"}, {"name": "front-hand2", "parent": "front-bracer", "length": 58.19, "rotation": 17.31, "x": 42.72, "y": -2.77, "scaleX": 1.0004, "scaleY": 1.0004, "transform": "onlyTranslation", "color": "14ff00ff"}, {"name": "spineboy-front-foot-target", "parent": "saddle", "x": -50.71, "y": -96.93, "color": "ff3f00ff"}, {"name": "front-thigh", "parent": "spineboy-hip", "length": 77.79, "rotation": 163.34, "x": 15.52, "y": 17.02, "color": "14ff00ff"}, {"name": "lower-leg", "parent": "front-thigh", "length": 111.5, "rotation": -49.62, "x": 77.93, "y": -0.11, "color": "14ff00ff"}, {"name": "gun", "parent": "spineboy-hip", "length": 181.35, "rotation": 107.12, "x": 16.86, "y": -7.89, "scaleX": 0.816, "scaleY": 0.816, "color": "ffffffff"}, {"name": "jaw", "parent": "head", "length": 203.76, "rotation": -129.6, "x": 49.11, "y": -68.46, "color": "ffff00ff"}, {"name": "jaw-inside", "parent": "jaw", "x": 94.7, "y": 33.64, "color": "ffff00ff"}, {"name": "saddle-strap-back", "parent": "saddle", "length": 74.6, "rotation": 151.14, "x": -33.34, "y": 87.33, "color": "ff7300ff"}, {"name": "saddle-strap-front", "parent": "saddle", "length": 154.29, "rotation": -148.12, "x": -27.36, "y": -73.39, "color": "ff7300ff"}, {"name": "stirrup", "parent": "saddle", "length": 78.17, "rotation": -68.86, "x": -81.94, "y": -103.38, "color": "ff3f00ff"}, {"name": "stirrup-strap1", "parent": "saddle", "length": 43.7, "rotation": -135, "x": -20.38, "y": -29.37, "color": "ff7300ff"}, {"name": "stirrup-strap2", "parent": "stirrup-strap1", "length": 51.62, "rotation": 9.39, "x": 43.71, "color": "ff7300ff"}, {"name": "tail1", "parent": "hip", "length": 81.26, "rotation": 153.61, "x": -20.87, "y": 6.87, "color": "eaff00ff"}, {"name": "tail2", "parent": "tail1", "length": 81.26, "rotation": 10.42, "x": 81.26, "color": "eaff00ff"}, {"name": "tail3", "parent": "tail2", "length": 65.01, "rotation": 12.18, "x": 81.26, "color": "eaff00ff"}, {"name": "tail4", "parent": "tail3", "length": 65.01, "x": 65.01, "color": "eaff00ff"}, {"name": "tail5", "parent": "tail4", "length": 70.53, "rotation": 4.36, "x": 65.01, "color": "eaff00ff"}, {"name": "tail6", "parent": "tail5", "length": 70.53, "x": 70.53, "color": "eaff00ff"}, {"name": "tail7", "parent": "tail6", "length": 63.13, "rotation": 2.35, "x": 70.53, "color": "eaff00ff"}, {"name": "tail8", "parent": "tail7", "length": 54.46, "rotation": 0.97, "x": 63.13, "color": "eaff00ff"}, {"name": "tail9", "parent": "tail8", "length": 49.21, "rotation": -1.29, "x": 54.46, "color": "eaff00ff"}, {"name": "tail10", "parent": "tail9", "length": 45.53, "rotation": 0.36, "x": 49.21, "color": "eaff00ff"}, {"name": "tongue1", "parent": "head", "length": 55.12, "rotation": -129.04, "x": 20.82, "y": -104.75, "color": "ffff00ff"}, {"name": "tongue2", "parent": "tongue1", "length": 44.67, "rotation": 8.93, "x": 55.6, "y": 0.93, "color": "fff200ff"}, {"name": "tongue3", "parent": "tongue2", "length": 43.65, "rotation": 12.86, "x": 44.27, "y": -0.21, "color": "fff200ff"}, {"name": "head-control", "parent": "head", "rotation": -95.04, "x": 42.32, "y": -220.33, "color": "219517ff"}, {"name": "leg-control", "parent": "front-leg1", "rotation": 53.14, "x": 172.97, "y": 4.16, "color": "1d8020ff"}], "slots": [{"name": "back-hand", "bone": "back-hand2", "attachment": "back-hand"}, {"name": "back-arm", "bone": "back-arm", "attachment": "back-arm"}, {"name": "back-bracer", "bone": "back-bracer", "attachment": "back-bracer"}, {"name": "back-knee", "bone": "back-knee", "attachment": "back-knee"}, {"name": "raptor-jaw-inside", "bone": "jaw-inside", "color": "646464ff", "attachment": "raptor-jaw2"}, {"name": "raptor-mouth-inside", "bone": "jaw", "attachment": "raptor-mouth-inside"}, {"name": "raptow-jaw-tooth", "bone": "jaw", "attachment": "raptor-jaw-tooth"}, {"name": "raptor-horn-back", "bone": "horn-back", "attachment": "raptor-horn-back"}, {"name": "raptor-tongue", "bone": "tongue1", "attachment": "raptor-tongue"}, {"name": "raptor-hindleg-back", "bone": "back-leg1", "attachment": "raptor-hindleg-back"}, {"name": "raptor-back-arm", "bone": "back-arm1", "attachment": "raptor-back-arm"}, {"name": "back-thigh", "bone": "back-thigh", "attachment": "back-thigh"}, {"name": "raptor-body", "bone": "torso1", "attachment": "raptor-body"}, {"name": "raptor-saddle-strap-front", "bone": "saddle-strap-front", "attachment": "raptor-saddle-strap-front"}, {"name": "raptor-saddle-strap-back", "bone": "saddle-strap-back", "attachment": "raptor-saddle-strap-back"}, {"name": "raptor-saddle", "bone": "saddle", "attachment": "raptor-saddle-w-shadow"}, {"name": "raptor-jaw", "bone": "jaw", "attachment": "raptor-jaw"}, {"name": "raptor-front-arm", "bone": "front-arm1", "attachment": "raptor-front-arm"}, {"name": "raptor-front-leg", "bone": "front-leg1", "attachment": "raptor-front-leg"}, {"name": "neck", "bone": "neck2", "attachment": "neck"}, {"name": "spineboy-torso", "bone": "spineboy-torso", "attachment": "torso"}, {"name": "head", "bone": "head2", "attachment": "head"}, {"name": "eyes-open", "bone": "head2", "attachment": "eyes-open"}, {"name": "mouth-smile", "bone": "head2", "attachment": "mouth-smile"}, {"name": "visor", "bone": "head2", "attachment": "visor"}, {"name": "raptor-horn", "bone": "horn-front", "attachment": "raptor-horn"}, {"name": "front-thigh", "bone": "front-thigh", "attachment": "front-thigh"}, {"name": "stirrup-back", "bone": "stirrup", "attachment": "stirrup-back"}, {"name": "lower-leg", "bone": "lower-leg", "attachment": "lower-leg"}, {"name": "stirrup-strap", "bone": "stirrup", "attachment": "stirrup-strap"}, {"name": "stirrup-front", "bone": "stirrup", "attachment": "stirrup-front"}, {"name": "gun", "bone": "gun", "attachment": "gun-nohand"}, {"name": "front-arm", "bone": "front-arm", "attachment": "front-arm"}, {"name": "front-bracer", "bone": "front-bracer", "attachment": "front-bracer"}, {"name": "front-hand", "bone": "front-hand2", "attachment": "front-hand"}, {"name": "tail-shadow", "bone": "torso1", "color": "00000000"}], "ik": [{"name": "back-foot-ik", "order": 3, "bones": ["back-leg3", "back-foot1"], "target": "back-foot-target"}, {"name": "back-leg-ik", "order": 2, "bones": ["back-leg1", "back-leg2"], "target": "back-leg-target", "bendPositive": false}, {"name": "front-foot-ik", "order": 1, "bones": ["front-leg3", "front-foot1"], "target": "front-foot-target"}, {"name": "front-leg-ik", "bones": ["front-leg1", "front-leg2"], "target": "front-leg-target", "bendPositive": false}, {"name": "spineboy-back-arm-ik", "order": 8, "bones": ["back-arm", "back-bracer"], "target": "spineboy-back-arm-target", "bendPositive": false}, {"name": "spineboy-back-leg-ik", "order": 5, "bones": ["back-thigh", "back-knee"], "target": "spineboy-back-foot-target", "bendPositive": false}, {"name": "spineboy-front-arm-ik", "order": 7, "bones": ["front-arm", "front-bracer"], "target": "spineboy-front-arm-target"}, {"name": "spineboy-front-leg-ik", "order": 4, "bones": ["front-thigh", "lower-leg"], "target": "spineboy-front-foot-target", "bendPositive": false}, {"name": "stirrup", "order": 6, "bones": ["stirrup-strap1", "stirrup-strap2"], "target": "stirrup", "bendPositive": false}], "skins": [{"name": "default", "attachments": {"back-arm": {"back-arm": {"x": 28.57, "y": -12.03, "rotation": 16.76, "width": 91, "height": 49}}, "back-bracer": {"back-bracer": {"x": 13.2, "y": -4.28, "rotation": -0.73, "width": 77, "height": 55}}, "back-hand": {"back-hand": {"x": 18.61, "y": 4.24, "rotation": -10.99, "width": 72, "height": 68}}, "back-knee": {"back-knee": {"x": 45.77, "y": 20.47, "rotation": 74.23, "width": 97, "height": 134}}, "back-thigh": {"back-thigh": {"x": 37.85, "y": -4.37, "rotation": 19.25, "width": 78, "height": 47}}, "eyes-open": {"eyes-open": {"x": 93.24, "y": -25.45, "rotation": -70.58, "width": 93, "height": 89}}, "front-arm": {"front-arm": {"x": 33.68, "y": -1.53, "rotation": 14.02, "width": 96, "height": 51}}, "front-bracer": {"front-bracer": {"x": 11.68, "y": -1.37, "rotation": -6.28, "width": 81, "height": 58}}, "front-hand": {"front-hand": {"x": 35.7, "y": 7.84, "rotation": -13.97, "width": 82, "height": 75}, "front-open-hand": {"x": 42.55, "y": 4.62, "rotation": 62.19, "width": 86, "height": 87}, "gun": {"x": 98.91, "y": 22.98, "rotation": 56.35, "width": 213, "height": 206}}, "front-thigh": {"front-thigh": {"x": 45.7, "y": -3.1, "rotation": 16.66, "width": 114, "height": 58}}, "gun": {"gun-nohand": {"x": 54.65, "y": -24.93, "rotation": 55.2, "width": 210, "height": 203}}, "head": {"head": {"type": "mesh", "uvs": [0.73461, 0.04542, 0.88414, 0.17033, 0.88955, 0.31976, 0.91126, 0.27463, 0.9461, 0.20217, 1, 0.29892, 1, 0.34554, 1, 0.4508, 0.91249, 0.51206, 0.84514, 0.51207, 0.8209, 0.59663, 0.77915, 0.67257, 0.73605, 0.75464, 0.83571, 0.73994, 0.84784, 0.84528, 0.7549, 0.93101, 0.63773, 1, 0.39394, 1, 0.14747, 0.82935, 0, 0.59419, 0, 0.36645, 0.09623, 0.20353, 0.21474, 0.14594, 0.45179, 0.15693, 0.51509, 0.1263, 0.507, 0.07853, 0.42079, 0, 0.56221, 0, 0.19055, 0.39949, 0.27942, 0.31373, 0.79396, 0.479, 0.76029, 0.85997, 0.53421, 0.16964, 0.53207, 0.04286, 0.61949, 0.08784, 0.70424, 0.16685, 0.69053, 0.432, 0.85592, 0.37861, 0.45844, 0.34997, 0.48658, 0.30193, 0.66307, 0.35065, 0.58439, 0.39448, 0.70468, 0.26242, 0.51985, 0.21924], "triangles": [23, 24, 32, 32, 24, 34, 35, 34, 0, 34, 25, 33, 34, 24, 25, 33, 27, 34, 34, 27, 0, 25, 26, 33, 33, 26, 27, 32, 35, 42, 35, 0, 1, 32, 34, 35, 36, 40, 37, 40, 42, 37, 37, 42, 2, 40, 43, 42, 2, 42, 1, 43, 32, 42, 42, 35, 1, 7, 37, 6, 37, 2, 6, 6, 2, 5, 2, 3, 5, 3, 4, 5, 8, 37, 7, 10, 36, 30, 10, 11, 36, 15, 16, 31, 18, 12, 17, 38, 18, 28, 12, 31, 16, 12, 41, 36, 41, 12, 18, 41, 18, 38, 38, 28, 29, 15, 31, 14, 12, 16, 17, 14, 31, 13, 31, 12, 13, 18, 19, 28, 12, 36, 11, 19, 20, 28, 29, 28, 21, 38, 39, 41, 28, 20, 21, 39, 43, 40, 38, 29, 39, 21, 22, 29, 29, 22, 39, 22, 23, 39, 39, 23, 43, 43, 23, 32, 41, 39, 40, 10, 30, 9, 41, 40, 36, 8, 9, 37, 9, 30, 37, 30, 36, 37], "vertices": [2, 32, 58.33, -14.31, 0.30205, 33, -0.88, -20.72, 0.69795, 3, 30, 69.21, 19.04, 0.07711, 31, 87.24, -25.34, 0.8077, 32, 14.49, -47.57, 0.11518, 3, 29, 43.19, 28.99, 0.10855, 30, 24.81, 15.35, 0.61823, 31, 50.15, -50.03, 0.27322, 1, 30, 38.53, 10.15, 1, 1, 30, 60.57, 1.79, 1, 1, 30, 32.5, -14.23, 1, 1, 30, 18.62, -14.92, 1, 2, 29, 45.01, -20.18, 0.76042, 30, -12.71, -16.48, 0.23958, 1, 28, 166.12, -105.42, 1, 1, 28, 160.05, -88.21, 1, 1, 28, 134.1, -90.39, 1, 1, 28, 108.99, -87.24, 1, 1, 28, 82.04, -84.36, 1, 1, 28, 95.16, -108.38, 1, 1, 28, 66.64, -121.91, 1, 1, 28, 34.17, -106.65, 1, 1, 28, 4.23, -83.54, 1, 1, 28, -17.74, -21.23, 1, 1, 28, 8.01, 58.67, 1, 1, 28, 60.82, 119.66, 1, 1, 28, 124.82, 142.22, 1, 1, 28, 179.28, 133.77, 1, 1, 28, 206.14, 109.19, 1, 3, 28, 224.42, 47.51, 0.55599, 32, 39.26, 67.02, 0.19527, 33, 47.76, 47.19, 0.24873, 3, 28, 238.73, 34.37, 0.20521, 32, 45.19, 48.52, 0.20866, 33, 37.74, 30.54, 0.58612, 2, 32, 59.59, 48.14, 0.05508, 33, 46.95, 19.46, 0.94492, 1, 33, 79.02, 11.41, 1, 1, 33, 46.15, -8.3, 1, 1, 28, 132.71, 90.25, 1, 1, 28, 164.81, 76.03, 1, 1, 28, 164.73, -71.85, 1, 1, 28, 54.62, -100.99, 1, 3, 28, 228.27, 25.19, 0.29316, 32, 31.56, 45.72, 0.3088, 33, 26.66, 38.96, 0.39805, 1, 33, 46.59, 6.85, 1, 2, 32, 51.44, 18.64, 0.07922, 33, 19.38, 6.16, 0.92078, 1, 32, 24.18, 0.23, 1, 1, 28, 168.62, -40.76, 1, 3, 29, 25.61, 19.96, 0.50536, 30, 6.84, 23.59, 0.2645, 31, 30.44, -51.51, 0.23014, 1, 28, 170.76, 26.69, 1, 4, 28, 186.8, 24.26, 0.75057, 31, -2.82, 45.62, 0.07609, 32, -4.94, 65.43, 0.10488, 33, 17.45, 79.4, 0.06845, 1, 31, 10.01, -2.69, 1, 1, 28, 169.6, -9.91, 1, 1, 31, 38.3, 1.56, 1, 4, 28, 213.04, 23.94, 0.43153, 31, 22.88, 50.92, 0.09832, 32, 17.71, 52.17, 0.26174, 33, 22.39, 53.63, 0.20841], "hull": 28, "edges": [10, 8, 4, 2, 2, 0, 0, 54, 52, 54, 52, 50, 50, 48, 48, 46, 46, 44, 44, 42, 42, 40, 40, 56, 56, 58, 60, 18, 18, 16, 16, 14, 38, 40, 38, 36, 36, 34, 32, 34, 32, 30, 30, 62, 62, 28, 28, 26, 26, 24, 24, 22, 22, 20, 20, 18, 28, 30, 48, 64, 50, 66, 66, 54, 0, 68, 68, 48, 66, 68, 2, 70, 70, 64, 68, 70, 72, 60, 10, 12, 12, 14, 4, 12, 4, 6, 6, 8, 10, 6, 14, 74, 4, 74, 74, 72, 58, 76, 78, 76, 78, 44, 80, 78, 72, 82, 82, 76, 80, 82, 80, 74, 70, 84, 84, 80, 4, 84, 64, 86, 86, 78, 84, 86], "width": 271, "height": 298}}, "lower-leg": {"lower-leg": {"x": 76.2, "y": 22.21, "rotation": 66.28, "width": 146, "height": 195}}, "mouth-smile": {"mouth-grind": {"x": 27.66, "y": -31.33, "rotation": -70.58, "width": 93, "height": 59}, "mouth-smile": {"x": 27.66, "y": -31.33, "rotation": -70.58, "width": 93, "height": 59}}, "neck": {"neck": {"x": 15.1, "y": -1.67, "rotation": -58.92, "width": 36, "height": 41}}, "raptor-back-arm": {"raptor-back-arm": {"type": "mesh", "uvs": [0.38712, 0.29362, 0.31383, 0.46513, 0.29243, 0.51522, 0.32476, 0.49311, 0.57587, 0.32139, 0.63255, 0.28263, 0.71632, 0.34508, 0.94948, 0.51888, 0.94948, 0.60129, 1, 0.65257, 1, 0.90624, 0.95463, 0.99934, 0.88957, 0.83205, 0.80295, 0.99999, 0.75236, 0.75696, 0.6654, 0.71301, 0.62289, 0.63243, 0.58195, 0.65032, 0.22479, 0.80641, 0.07792, 0.73315, 0.07825, 0.6655, 0.07985, 0.34307, 0, 0.29728, 0, 0, 0.32335, 0], "triangles": [6, 15, 16, 5, 6, 16, 5, 16, 4, 6, 7, 15, 16, 17, 4, 8, 15, 7, 14, 15, 8, 12, 14, 8, 12, 8, 9, 12, 9, 10, 11, 12, 10, 13, 14, 12, 17, 3, 4, 19, 20, 2, 18, 19, 2, 18, 2, 3, 18, 3, 17, 21, 22, 23, 24, 21, 23, 0, 21, 24, 1, 21, 0, 1, 20, 21, 2, 20, 1], "vertices": [2, 13, 36.95, 33.31, 0.91667, 14, 68.54, 41.05, 0.08333, 2, 13, 66.02, 20.36, 0.76814, 14, 41.42, 24.4, 0.23186, 2, 13, 74.51, 16.58, 0.64468, 14, 33.5, 19.53, 0.35532, 2, 13, 70.89, 21.97, 0.29072, 14, 40, 19.47, 0.70928, 3, 13, 42.78, 63.9, 0.11484, 14, 90.47, 18.95, 0.60855, 22, -17.2, 9.01, 0.27661, 2, 14, 101.86, 18.84, 0.45956, 22, -14.39, 20.05, 0.54044, 2, 14, 106.48, 2.09, 0.0625, 22, 2.98, 20.56, 0.9375, 1, 22, 51.32, 21.99, 1, 1, 22, 60.41, 11.11, 1, 1, 22, 72.39, 9.62, 1, 1, 22, 100.37, -23.87, 1, 1, 22, 104.96, -40.9, 1, 1, 22, 78.37, -25.62, 1, 1, 22, 86.06, -56.84, 1, 1, 22, 52.92, -30.05, 1, 2, 14, 62.25, -43.93, 0.0625, 22, 37.19, -33.34, 0.9375, 2, 14, 64.89, -28.66, 0.3125, 22, 22.99, -27.14, 0.6875, 2, 14, 57.7, -27.17, 0.30612, 22, 19.84, -33.78, 0.69388, 2, 13, 124.19, 3.84, 0.19395, 14, -5.1, -14.24, 0.80605, 2, 13, 110.78, -19.65, 0.3125, 14, -16.89, 10.11, 0.6875, 2, 13, 99.15, -19.2, 0.51614, 14, -9.94, 19.44, 0.48386, 2, 13, 43.73, -17.04, 0.9375, 14, 23.18, 63.93, 0.0625, 1, 13, 35.41, -29.78, 1, 1, 13, -15.69, -28.02, 1, 1, 13, -13.88, 24.65, 1], "hull": 25, "edges": [44, 46, 44, 42, 38, 36, 32, 30, 30, 28, 28, 26, 24, 22, 18, 16, 16, 14, 46, 48, 38, 4, 6, 4, 6, 36, 42, 40, 40, 38, 4, 2, 2, 0, 40, 2, 10, 32, 36, 34, 34, 32, 10, 8, 8, 6, 34, 8, 14, 12, 12, 10, 12, 30, 18, 20, 22, 20, 26, 24, 48, 0], "width": 163, "height": 172}}, "raptor-body": {"raptor-body": {"type": "mesh", "uvs": [0.88305, 0.02794, 0.91758, 0.05592, 0.9497, 0.09133, 0.97573, 0.13213, 0.99055, 0.17339, 0.99759, 0.22987, 0.99678, 0.27226, 0.99353, 0.31287, 0.9839, 0.38477, 0.97956, 0.35307, 0.96687, 0.38782, 0.96442, 0.34841, 0.94742, 0.38391, 0.94489, 0.33238, 0.9386, 0.34808, 0.93784, 0.32559, 0.92667, 0.34333, 0.92539, 0.31538, 0.91182, 0.34989, 0.90925, 0.28963, 0.89984, 0.27929, 0.87514, 0.33979, 0.86225, 0.40838, 0.87429, 0.45818, 0.84272, 0.50226, 0.81998, 0.59622, 0.81697, 0.68641, 0.81951, 0.7069, 0.78696, 0.82183, 0.74283, 0.91135, 0.68699, 0.97585, 0.6244, 1, 0.58849, 1, 0.51466, 1, 0.49121, 0.8368, 0.4727, 0.78488, 0.44707, 0.74644, 0.42472, 0.72176, 0.3966, 0.70938, 0.37043, 0.69548, 0.34684, 0.68416, 0.32377, 0.6759, 0.29877, 0.66711, 0.26827, 0.65566, 0.24021, 0.64447, 0.2154, 0.63308, 0.18745, 0.62026, 0.163, 0.61056, 0.13948, 0.60123, 0.11931, 0.59392, 0.09945, 0.58672, 0.08097, 0.5824, 0.06076, 0.58225, 0.04257, 0.58149, 0.02388, 0.58253, 0, 0.58455, 0, 0.56298, 0.0209, 0.55046, 0.03794, 0.53974, 0.05634, 0.52692, 0.0746, 0.51306, 0.09507, 0.49881, 0.11728, 0.48334, 0.1375, 0.46924, 0.16277, 0.45174, 0.18782, 0.43439, 0.21308, 0.4189, 0.23873, 0.40318, 0.26735, 0.38578, 0.2969, 0.36723, 0.32579, 0.35014, 0.35725, 0.33294, 0.37992, 0.3207, 0.41103, 0.31696, 0.44145, 0.29137, 0.47584, 0.28483, 0.49453, 0.31832, 0.5288, 0.35569, 0.60144, 0.41756, 0.65116, 0.41078, 0.70843, 0.42446, 0.7118, 0.2925, 0.70946, 0.19468, 0.70683, 0.08348, 0.76023, 0.01941, 0.79301, 0, 0.83875, 0, 0.02258, 0.5679, 0.04005, 0.56141, 0.05877, 0.55312, 0.07877, 0.54786, 0.09747, 0.5401, 0.12059, 0.53086, 0.14233, 0.52663, 0.16642, 0.52304, 0.19163, 0.52137, 0.2177, 0.51241, 0.24501, 0.50218, 0.27577, 0.49505, 0.30141, 0.49242, 0.3286, 0.49077, 0.3541, 0.49188, 0.38137, 0.49347, 0.40824, 0.49495, 0.44136, 0.50946, 0.47122, 0.53169, 0.49886, 0.56568, 0.53162, 0.60054, 0.60671, 0.67484, 0.6857, 0.67243, 0.7506, 0.59437, 0.76886, 0.46557, 0.7773, 0.34161, 0.77355, 0.22842, 0.8056, 0.09401, 0.86736, 0.07427, 0.90484, 0.17059, 0.9096, 0.19933, 0.91959, 0.21397, 0.93193, 0.20183, 0.93608, 0.17463, 0.92873, 0.1403, 0.91672, 0.13455, 0.90667, 0.14854, 0.91663, 0.09795, 0.89868, 0.09514, 0.88034, 0.09404, 0.9309, 0.11529, 0.93998, 0.15741, 0.94213, 0.17477, 0.94345, 0.19647, 0.94192, 0.20763, 0.92187, 0.22801, 0.9048, 0.23489, 0.8899, 0.19847, 0.8874, 0.16914, 0.87831, 0.12122, 0.585, 0.84243, 0.63024, 0.8646, 0.68284, 0.85192, 0.72923, 0.80453, 0.75898, 0.76323, 0.78513, 0.70347, 0.78536, 0.6783, 0.78141, 0.59277, 0.94721, 0.11131, 0.96236, 0.1618, 0.96367, 0.19318, 0.95806, 0.21052, 0.976, 0.16763, 0.98026, 0.22172, 0.98039, 0.26467, 0.97933, 0.31612, 0.96394, 0.25896, 0.95648, 0.31982, 0.9432, 0.24678, 0.93886, 0.28792], "triangles": [134, 135, 116, 107, 34, 35, 106, 107, 35, 106, 35, 36, 105, 106, 36, 78, 107, 77, 77, 107, 106, 76, 77, 106, 76, 106, 105, 76, 105, 75, 116, 135, 123, 134, 116, 117, 123, 122, 121, 123, 121, 120, 116, 123, 120, 130, 119, 120, 119, 117, 120, 126, 123, 136, 122, 125, 124, 125, 122, 126, 155, 20, 133, 20, 21, 112, 19, 20, 156, 133, 118, 132, 131, 132, 118, 122, 124, 127, 133, 113, 134, 131, 130, 148, 155, 131, 148, 130, 129, 147, 129, 128, 146, 17, 18, 19, 129, 146, 147, 1, 124, 125, 136, 114, 126, 16, 17, 15, 154, 17, 156, 2, 127, 124, 155, 148, 150, 145, 127, 2, 146, 128, 3, 148, 147, 150, 2, 124, 1, 151, 156, 153, 152, 154, 156, 13, 154, 11, 147, 149, 4, 11, 12, 13, 11, 154, 152, 4, 149, 3, 8, 9, 7, 111, 22, 23, 22, 112, 21, 112, 81, 113, 81, 82, 113, 113, 82, 114, 82, 83, 114, 83, 84, 114, 84, 85, 114, 114, 86, 115, 114, 85, 86, 115, 0, 1, 115, 86, 0, 10, 11, 9, 11, 152, 9, 9, 152, 7, 7, 152, 6, 6, 152, 151, 6, 151, 5, 5, 151, 150, 150, 4, 5, 150, 147, 4, 146, 3, 149, 145, 2, 3, 151, 153, 150, 152, 156, 151, 126, 114, 115, 135, 113, 114, 147, 146, 149, 1, 125, 115, 145, 3, 128, 13, 15, 154, 153, 155, 150, 13, 14, 15, 15, 17, 154, 20, 112, 113, 125, 126, 115, 148, 130, 147, 156, 155, 153, 127, 145, 128, 20, 113, 133, 127, 128, 121, 17, 19, 156, 135, 114, 136, 132, 131, 155, 120, 128, 129, 155, 133, 132, 131, 118, 119, 118, 117, 119, 156, 20, 155, 131, 119, 130, 133, 117, 118, 130, 120, 129, 122, 123, 126, 117, 116, 120, 121, 122, 127, 120, 121, 128, 113, 135, 134, 135, 136, 123, 133, 134, 117, 24, 111, 23, 111, 112, 22, 111, 80, 112, 112, 80, 81, 110, 107, 78, 30, 139, 29, 139, 140, 29, 29, 140, 28, 140, 108, 109, 140, 141, 28, 28, 141, 27, 27, 141, 142, 140, 109, 141, 141, 109, 142, 142, 26, 27, 142, 143, 26, 142, 109, 143, 26, 143, 25, 109, 110, 143, 110, 144, 143, 143, 144, 25, 109, 108, 110, 110, 79, 80, 79, 110, 78, 25, 144, 24, 110, 111, 144, 110, 80, 111, 144, 111, 24, 108, 34, 107, 30, 31, 138, 138, 31, 32, 138, 32, 137, 32, 33, 137, 33, 34, 137, 138, 139, 30, 138, 137, 139, 139, 137, 140, 108, 140, 137, 137, 34, 108, 110, 108, 107, 37, 105, 36, 104, 105, 37, 104, 37, 38, 38, 103, 104, 105, 104, 75, 103, 73, 104, 104, 73, 74, 75, 104, 74, 38, 39, 103, 39, 102, 103, 40, 102, 39, 101, 102, 40, 73, 103, 72, 101, 71, 102, 72, 103, 102, 72, 102, 71, 101, 40, 41, 100, 101, 41, 100, 41, 42, 100, 42, 99, 69, 100, 99, 71, 101, 70, 69, 70, 100, 101, 100, 70, 42, 43, 99, 99, 43, 98, 43, 44, 98, 98, 44, 97, 98, 97, 67, 99, 98, 68, 68, 98, 67, 99, 68, 69, 44, 45, 97, 97, 45, 96, 45, 46, 96, 96, 46, 95, 96, 95, 65, 97, 96, 66, 96, 65, 66, 97, 66, 67, 46, 47, 95, 95, 47, 94, 47, 48, 94, 94, 48, 93, 94, 93, 63, 95, 94, 64, 94, 63, 64, 95, 64, 65, 48, 49, 93, 93, 49, 92, 92, 49, 91, 49, 50, 91, 91, 61, 92, 93, 92, 62, 92, 61, 62, 93, 62, 63, 50, 90, 91, 51, 89, 90, 50, 51, 90, 51, 52, 89, 89, 59, 90, 59, 60, 90, 90, 60, 91, 60, 61, 91, 55, 87, 54, 55, 56, 87, 54, 87, 53, 52, 88, 89, 87, 88, 53, 52, 53, 88, 56, 57, 87, 87, 57, 88, 57, 58, 88, 88, 58, 89, 58, 59, 89], "vertices": [2, 8, 142.31, -163.1, 0.91085, 74, -65.79, 94.58, 0.08915, 2, 8, 129.87, -206.92, 0.78514, 74, -21.05, 86.04, 0.21486, 2, 8, 113.94, -247.72, 0.77045, 74, 21, 73.75, 0.22955, 2, 8, 95.41, -280.88, 0.81697, 74, 55.65, 58.2, 0.18303, 2, 8, 76.47, -299.85, 0.92186, 74, 76.21, 41, 0.07814, 2, 8, 50.32, -309.1, 0.93376, 74, 87.72, 15.77, 0.06624, 2, 8, 30.6, -308.34, 0.9245, 74, 88.7, -3.95, 0.0755, 2, 8, 11.66, -304.47, 0.91842, 74, 86.51, -23.15, 0.08158, 2, 8, -21.93, -292.74, 0.98449, 74, 77.77, -57.64, 0.01551, 2, 8, -7.26, -287.07, 0.90726, 74, 70.83, -43.53, 0.09274, 2, 8, -23.63, -271.23, 0.89469, 74, 56.49, -61.23, 0.10531, 2, 8, -5.35, -267.89, 0.87252, 74, 51.57, -43.31, 0.12748, 2, 8, -22.13, -246.63, 0.90956, 74, 31.85, -61.9, 0.09044, 2, 8, 1.79, -243.11, 0.79155, 74, 26.25, -38.38, 0.20845, 2, 8, -5.62, -235.26, 0.80291, 74, 19.09, -46.44, 0.19709, 2, 8, 4.82, -234.16, 0.75452, 74, 17.07, -36.14, 0.24548, 2, 8, -3.61, -220.15, 0.7702, 74, 3.85, -45.77, 0.2298, 2, 8, 9.36, -218.37, 0.69443, 74, 0.94, -33, 0.30557, 2, 8, -6.91, -201.42, 0.7716, 74, -14.51, -50.7, 0.2284, 2, 8, 21.07, -197.82, 0.52991, 74, -20.56, -23.15, 0.47009, 2, 8, 25.72, -185.86, 0.48384, 74, -32.88, -19.56, 0.51616, 3, 8, -2.82, -155.01, 0.49549, 54, 99.81, 14.95, 0.1998, 74, -61.14, -50.76, 0.30472, 3, 8, -34.92, -139.14, 0.52773, 54, 107.98, -19.92, 0.45684, 74, -74.17, -84.14, 0.01543, 2, 8, -57.88, -154.65, 0.552, 54, 134.55, -27.77, 0.448, 4, 6, 214.38, -86.5, 0.416, 7, 12.5, -126.76, 0.24294, 8, -78.89, -115.02, 0.17462, 54, 117.33, -69.19, 0.16644, 3, 6, 164.81, -103.2, 0.46938, 7, -35.73, -106.53, 0.34058, 54, 123.6, -121.11, 0.19004, 3, 6, 135.98, -133.89, 0.80096, 7, -77.65, -110.49, 0.11639, 54, 147.79, -155.59, 0.08264, 3, 6, 132.63, -143.37, 0.82428, 7, -86.43, -115.4, 0.10285, 54, 156.4, -160.78, 0.07287, 1, 6, 67.3, -160.11, 1, 2, 2, 226.09, -123.55, 0.23474, 6, -2.28, -158.53, 0.76526, 3, 2, 156.49, -155.76, 0.52831, 6, -76.36, -138.65, 0.37693, 1, 172.42, -169.15, 0.09477, 3, 2, 77.76, -169.48, 0.67731, 6, -145.59, -98.75, 0.09201, 1, 92.8, -175.99, 0.23068, 3, 2, 32.4, -170.91, 0.60686, 61, -141.38, 131.19, 0.07586, 1, 47.48, -173.48, 0.31728, 3, 2, -60.88, -173.87, 0.39257, 61, -55.62, 167.98, 0.26021, 1, -45.7, -168.32, 0.34722, 4, 62, -58.53, 122.52, 0.02965, 2, -92.91, -98.95, 0.15809, 61, 1.54, 109.92, 0.56291, 1, -71.11, -90.91, 0.24934, 5, 63, -87.14, 125.38, 0.01694, 62, -30.38, 104.17, 0.12049, 2, -117.05, -75.56, 0.06243, 61, 32.55, 96.96, 0.67353, 1, -93.12, -65.52, 0.12661, 5, 63, -54.56, 107.85, 0.06533, 62, 5.17, 93.91, 0.35431, 2, -149.99, -58.72, 0.01512, 61, 69.37, 93.3, 0.52081, 1, -124.48, -45.88, 0.04444, 4, 64, -91.18, 96.68, 0.02003, 63, -26.17, 96.68, 0.17282, 62, 35.28, 88.98, 0.56052, 61, 99.87, 93.89, 0.24663, 4, 64, -55.58, 91.31, 0.08871, 63, 9.43, 91.31, 0.37251, 62, 71.21, 91.24, 0.43883, 61, 134.8, 102.61, 0.09995, 5, 65, -80.72, 91.6, 0.01487, 64, -22.44, 85.2, 0.21852, 63, 42.57, 85.2, 0.55905, 62, 104.89, 92.27, 0.18543, 61, 167.74, 109.71, 0.02213, 4, 65, -51.3, 84.41, 0.07099, 64, 7.44, 80.26, 0.46092, 63, 72.45, 80.26, 0.39268, 62, 135.14, 93.74, 0.07541, 4, 65, -22.46, 78.67, 0.2126, 64, 36.63, 76.74, 0.62934, 63, 101.64, 76.74, 0.13917, 62, 164.42, 96.46, 0.01889, 4, 66, -61.72, 72.53, 0.05014, 65, 8.81, 72.53, 0.49668, 64, 68.28, 72.99, 0.41049, 63, 133.29, 72.99, 0.04268, 3, 66, -23.6, 64.7, 0.21233, 65, 46.93, 64.7, 0.64547, 64, 106.89, 68.09, 0.1422, 4, 67, -56.68, 59.57, 0.00497, 66, 11.45, 57.2, 0.56359, 65, 81.98, 57.2, 0.40675, 64, 142.41, 63.26, 0.0247, 4, 68, -88.32, 52.48, 0.00856, 67, -26.06, 50.98, 0.14719, 66, 42.4, 49.87, 0.68541, 65, 112.93, 49.87, 0.15884, 4, 68, -53.99, 42.22, 0.05573, 67, 8.43, 41.3, 0.39231, 66, 77.27, 41.61, 0.51595, 65, 147.8, 41.61, 0.036, 5, 69, -79.08, 32.17, 0.00731, 68, -23.87, 33.95, 0.20888, 67, 38.69, 33.54, 0.55959, 66, 107.81, 35.1, 0.21646, 65, 178.34, 35.1, 0.00775, 5, 69, -49.94, 24.87, 0.05717, 68, 5.1, 25.99, 0.4278, 67, 67.79, 26.07, 0.45737, 66, 137.19, 28.83, 0.05638, 65, 207.72, 28.83, 0.00128, 5, 70, -74, 19.39, 0.00687, 69, -24.91, 18.93, 0.21088, 68, 29.98, 19.49, 0.5528, 67, 92.78, 19.99, 0.22286, 66, 162.41, 23.77, 0.00659, 4, 70, -49.4, 13.39, 0.05569, 69, -0.28, 13.08, 0.44113, 68, 54.48, 13.08, 0.43894, 67, 117.38, 14, 0.06424, 4, 70, -26.39, 8.9, 0.21474, 69, 22.77, 8.73, 0.56103, 68, 77.42, 8.22, 0.21699, 67, 140.4, 9.52, 0.00724, 3, 70, -1, 6.11, 0.49366, 69, 48.17, 6.1, 0.44664, 68, 102.76, 5.01, 0.05971, 3, 70, 21.83, 3.3, 0.77042, 69, 71.02, 3.44, 0.22113, 68, 125.54, 1.84, 0.00845, 2, 70, 45.36, 1.27, 0.92172, 69, 94.56, 1.55, 0.07828, 2, 70, 75.48, -1.02, 0.98437, 69, 124.7, -0.54, 0.01563, 2, 70, 74.41, -10.99, 0.98712, 69, 123.69, -10.52, 0.01288, 2, 70, 47.53, -13.96, 0.9286, 69, 96.83, -13.66, 0.0714, 3, 70, 25.58, -16.62, 0.78026, 69, 74.89, -16.46, 0.21218, 68, 128.96, -18.15, 0.00756, 4, 70, 1.81, -20.07, 0.50456, 69, 51.15, -20.06, 0.43841, 68, 105.14, -21.21, 0.05555, 67, 168.62, -19.43, 0.00148, 4, 70, -21.82, -24.02, 0.2241, 69, 27.54, -24.16, 0.55698, 68, 81.45, -24.77, 0.20577, 67, 144.99, -23.39, 0.01316, 5, 70, -48.25, -27.85, 0.06037, 69, 1.14, -28.15, 0.44261, 68, 54.96, -28.17, 0.41953, 67, 118.56, -27.24, 0.07662, 66, 190.11, -22.36, 0.00087, 5, 70, -76.93, -32.01, 0.00843, 69, -27.52, -32.49, 0.21461, 68, 26.21, -31.86, 0.52853, 67, 89.88, -31.41, 0.23794, 66, 161.62, -27.71, 0.01048, 5, 69, -53.61, -36.45, 0.05984, 68, 0.04, -35.22, 0.4064, 67, 63.76, -35.22, 0.46847, 66, 135.68, -32.58, 0.06423, 65, 206.21, -32.58, 0.00106, 5, 69, -86.21, -41.34, 0.00821, 68, -32.66, -39.38, 0.19546, 67, 31.14, -39.93, 0.57299, 66, 103.28, -38.62, 0.21339, 65, 173.81, -38.62, 0.00994, 4, 68, -65.07, -43.5, 0.05047, 67, -1.2, -44.59, 0.45101, 66, 71.16, -44.6, 0.43746, 65, 141.69, -44.6, 0.06106, 4, 68, -97.64, -46.72, 0.00724, 67, -33.71, -48.36, 0.20423, 66, 38.83, -49.7, 0.58773, 65, 109.36, -49.7, 0.2008, 4, 67, -66.72, -52.2, 0.05217, 66, 6, -54.89, 0.45622, 65, 76.53, -54.89, 0.46325, 64, 145.49, -48.91, 0.02835, 3, 66, -30.62, -60.6, 0.17334, 65, 39.91, -60.6, 0.683, 64, 109.41, -57.39, 0.14366, 4, 66, -68.46, -66.77, 0.0295, 65, 2.07, -66.77, 0.50408, 64, 72.15, -66.42, 0.40417, 63, 137.16, -66.42, 0.06225, 4, 65, -34.88, -72.32, 0.18739, 64, 35.73, -74.76, 0.58778, 63, 100.73, -74.76, 0.2056, 62, 195.5, -51.82, 0.01923, 4, 65, -75.09, -77.7, 0.04676, 64, -3.96, -83.19, 0.39617, 63, 61.05, -83.19, 0.48167, 62, 158.49, -68.43, 0.07539, 3, 64, -32.54, -89.19, 0.14427, 63, 32.47, -89.19, 0.63908, 62, 131.82, -80.33, 0.21665, 4, 64, -71.85, -91.35, 0.04785, 63, -6.84, -91.35, 0.40108, 62, 93.85, -90.74, 0.50635, 61, 189.98, -72.27, 0.04472, 3, 63, -45.16, -103.67, 0.17783, 62, 58.99, -110.86, 0.68949, 61, 159.33, -98.37, 0.13269, 4, 63, -88.59, -107.18, 0.06196, 62, 17.28, -123.46, 0.5382, 2, -120.43, 156.97, 0.01597, 61, 120.58, -118.3, 0.38387, 4, 63, -112.39, -91.86, 0.01664, 62, -9.21, -113.51, 0.31712, 2, -96.32, 142.15, 0.06918, 61, 92.73, -113.3, 0.59706, 3, 62, -55.3, -106.16, 0.18947, 2, -52.49, 126.15, 0.211, 61, 46.08, -114.41, 0.59953, 3, 2, 40.2, 100.3, 0.70054, 6, -1.56, 132.44, 0.1247, 61, -49.65, -124.17, 0.17476, 2, 2, 102.91, 105.44, 0.48476, 6, 49.88, 96.2, 0.51524, 2, 6, 102.97, 46.59, 0.55391, 7, 16.89, 46.74, 0.44609, 2, 7, 77.99, 53.82, 0.34839, 8, 16.46, 51.73, 0.65161, 1, 8, 61.91, 55.28, 1, 1, 8, 113.57, 59.28, 1, 2, 8, 144.24, -7.82, 0.89352, 74, -220.64, 82.87, 0.10648, 2, 8, 153.81, -49.13, 0.88, 74, -180.33, 96.03, 0.12, 2, 8, 154.57, -106.94, 0.91167, 74, -122.81, 101.86, 0.08833, 2, 70, 46.28, -5.67, 0.88974, 69, 95.52, -5.38, 0.11026, 3, 70, 24, -6.32, 0.78319, 69, 73.25, -6.17, 0.21288, 68, 127.55, -7.82, 0.00393, 3, 70, 0.06, -7.63, 0.4984, 69, 49.31, -7.63, 0.45067, 68, 103.59, -8.74, 0.05093, 4, 70, -25.34, -7.36, 0.21021, 69, 23.92, -7.52, 0.57916, 68, 78.21, -8.06, 0.20281, 67, 141.46, -6.74, 0.00782, 4, 70, -49.23, -8.43, 0.05008, 69, 0.04, -8.74, 0.44936, 68, 54.3, -8.74, 0.44109, 67, 117.57, -7.82, 0.05946, 5, 70, -78.74, -9.59, 0.00411, 69, -29.46, -10.08, 0.20704, 68, 24.78, -9.42, 0.55388, 67, 88.06, -9, 0.22906, 66, 158.89, -5.38, 0.0059, 4, 69, -57, -9.28, 0.05054, 68, -2.74, -7.99, 0.42931, 67, 60.53, -8.04, 0.46828, 66, 131.34, -5.55, 0.05187, 5, 69, -87.47, -7.89, 0.00416, 68, -33.16, -5.91, 0.19066, 67, 30.07, -6.47, 0.59281, 66, 100.84, -5.23, 0.20446, 65, 171.37, -5.23, 0.00791, 4, 68, -64.88, -2.76, 0.04806, 67, -1.7, -3.86, 0.44622, 66, 68.99, -3.93, 0.45724, 65, 139.52, -3.93, 0.04848, 4, 68, -98.1, -2.85, 0.00302, 67, -34.91, -4.51, 0.16897, 66, 35.83, -5.93, 0.6491, 65, 106.36, -5.93, 0.17891, 3, 67, -69.74, -5.58, 0.03488, 66, 1.08, -8.43, 0.4896, 65, 71.61, -8.43, 0.47552, 4, 67, -108.75, -4.75, 0.00424, 66, -37.94, -9.2, 0.16984, 65, 32.59, -9.2, 0.69137, 64, 98.21, -6.7, 0.13455, 2, 65, 0.17, -8.31, 0.52543, 64, 65.82, -8.27, 0.47457, 3, 65, -34.17, -6.83, 0.18477, 64, 31.46, -9.41, 0.62696, 63, 96.47, -9.41, 0.18827, 4, 65, -66.3, -4.22, 0.01263, 64, -0.78, -9.24, 0.46677, 63, 64.23, -9.24, 0.48853, 62, 146, 4.52, 0.03206, 3, 64, -35.25, -8.87, 0.22735, 63, 29.76, -8.87, 0.65577, 62, 112.23, -2.39, 0.11689, 4, 64, -69.22, -8.55, 0.02776, 63, -4.21, -8.55, 0.41215, 62, 78.95, -9.25, 0.52977, 61, 160.59, 5.18, 0.03032, 3, 63, -46.14, -2.26, 0.12876, 62, 36.63, -11.94, 0.70822, 61, 119.45, -5.12, 0.16302, 3, 63, -84, 7.67, 0.04548, 62, -2.46, -10.23, 0.46291, 61, 80.69, -10.51, 0.4916, 4, 62, -40.04, -2.56, 0.20066, 2, -87.22, 27.36, 0.04102, 61, 42.35, -9.75, 0.69947, 1, -54.47, 34.43, 0.05884, 4, 62, -84, 4.08, 0.06346, 2, -45.32, 12.47, 0.1335, 61, -2.09, -11.18, 0.66178, 1, -14.03, 15.96, 0.14126, 1, 2, 50.64, -19.06, 1, 2, 2, 150.39, -14.78, 0.14978, 6, 9.3, -26.52, 0.85022, 2, 6, 96.28, -48.49, 0.94283, 7, -50.98, -20.17, 0.05717, 4, 6, 151.36, -15.54, 0.1938, 7, 12.13, -31.86, 0.65264, 54, 35.04, -116.47, 0.10585, 74, -188.88, -122.43, 0.0477, 4, 7, 70.75, -31.76, 0.42196, 8, -5.28, -31.36, 0.19392, 54, 5.95, -65.58, 0.22877, 74, -184.09, -64, 0.15534, 3, 8, 47.28, -25.92, 0.60156, 54, -31.67, -28.46, 0.26709, 74, -194.09, -12.11, 0.13136, 3, 8, 110.31, -65.62, 0.66779, 54, -41.11, 45.43, 0.13197, 74, -160.06, 54.14, 0.20024, 2, 8, 120.51, -143.55, 0.72576, 74, -83.35, 71.14, 0.27424, 2, 8, 76.35, -191.51, 0.55039, 74, -31.7, 31.36, 0.44961, 2, 8, 63.06, -197.7, 0.57574, 74, -24.37, 18.67, 0.42426, 2, 8, 56.42, -210.42, 0.59162, 74, -11.11, 13.17, 0.40838, 2, 8, 62.27, -225.94, 0.54866, 74, 3.83, 20.37, 0.45134, 2, 8, 74.99, -231.02, 0.55882, 74, 7.78, 33.48, 0.44118, 2, 8, 90.83, -221.52, 0.5372, 74, -3.08, 48.42, 0.4628, 2, 8, 93.3, -206.31, 0.54037, 74, -18.44, 49.55, 0.45963, 2, 8, 86.63, -193.69, 0.56061, 74, -30.43, 41.8, 0.43939, 2, 8, 110.32, -205.97, 0.55507, 74, -20.28, 66.47, 0.44493, 2, 8, 111.33, -183.27, 0.52695, 74, -42.98, 65.49, 0.47305, 2, 8, 111.53, -160.07, 0.56797, 74, -66.1, 63.65, 0.43203, 2, 8, 102.49, -224.1, 0.57026, 74, -1.52, 60.27, 0.42974, 2, 8, 83.06, -235.84, 0.54683, 74, 11.87, 41.94, 0.45317, 2, 8, 75.02, -238.67, 0.5345, 74, 15.4, 34.18, 0.4655, 2, 8, 64.95, -240.47, 0.52774, 74, 18.07, 24.31, 0.47226, 2, 8, 59.74, -238.59, 0.52019, 74, 16.66, 18.96, 0.47981, 2, 8, 49.93, -213.38, 0.51178, 74, -7.59, 6.97, 0.48822, 2, 8, 46.45, -191.85, 0.53448, 74, -28.74, 1.61, 0.46552, 2, 8, 63.13, -172.79, 0.45609, 74, -49.18, 16.56, 0.54391, 2, 8, 76.73, -169.46, 0.47244, 74, -53.69, 29.81, 0.52756, 2, 8, 98.86, -157.68, 0.53514, 74, -67.37, 50.82, 0.46486, 3, 2, 25.68, -97.82, 0.63735, 61, -108.45, 65.59, 0.06216, 1, 47.13, -100.08, 0.30049, 3, 2, 83.15, -106.32, 0.72603, 6, -100.98, -53.7, 0.09811, 1, 103.65, -113.53, 0.17585, 3, 2, 149.42, -98.32, 0.50192, 6, -44.98, -90.03, 0.42892, 1, 170.36, -111.32, 0.06917, 2, 2, 207.34, -74.44, 0.18186, 6, 14.79, -108.81, 0.81814, 2, 2, 244.31, -54.06, 0.05649, 6, 56.23, -116.86, 0.94351, 2, 6, 99.39, -115.34, 0.92692, 7, -92.84, -72.39, 0.07308, 3, 6, 106.83, -106.29, 0.84981, 7, -81.28, -70.52, 0.08915, 54, 114.85, -178.52, 0.06104, 4, 6, 127.4, -71.89, 0.69313, 7, -43.11, -58.31, 0.19154, 54, 85.35, -151.38, 0.10631, 74, -167.16, -179.69, 0.00902, 2, 8, 104.61, -244.7, 0.66844, 74, 18.8, 64.19, 0.33156, 2, 8, 81.39, -264.16, 0.67294, 74, 40.23, 42.76, 0.32706, 2, 8, 66.82, -266.01, 0.65456, 74, 43.35, 28.41, 0.34544, 2, 8, 58.66, -259.02, 0.64393, 74, 37.11, 19.68, 0.35607, 2, 8, 78.9, -281.43, 0.75776, 74, 57.66, 41.81, 0.24224, 2, 8, 53.83, -287.14, 0.77384, 74, 65.54, 17.33, 0.22616, 2, 8, 33.86, -287.57, 0.80091, 74, 67.72, -2.53, 0.19909, 2, 8, 9.92, -286.55, 0.89717, 74, 68.81, -26.47, 0.10283, 2, 8, 36.24, -266.75, 0.70023, 74, 46.78, -1.99, 0.29977, 2, 8, 7.82, -257.69, 0.78734, 74, 40.25, -31.09, 0.21266, 2, 8, 41.56, -240.46, 0.5741, 74, 20.12, 1.01, 0.4259, 2, 8, 22.36, -235.22, 0.62581, 74, 16.58, -18.58, 0.37419], "hull": 87, "edges": [110, 108, 108, 106, 106, 104, 104, 102, 102, 100, 88, 86, 86, 84, 84, 82, 82, 80, 80, 78, 78, 76, 76, 74, 74, 72, 72, 70, 70, 68, 68, 66, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 96, 98, 98, 100, 92, 94, 94, 96, 88, 90, 90, 92, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 150, 152, 152, 154, 122, 124, 124, 126, 174, 176, 176, 178, 178, 180, 180, 182, 182, 184, 184, 186, 186, 188, 188, 190, 190, 192, 192, 194, 194, 196, 196, 198, 198, 200, 200, 202, 202, 204, 130, 190, 132, 192, 128, 188, 126, 186, 124, 184, 122, 182, 120, 180, 118, 178, 116, 176, 90, 192, 92, 190, 94, 188, 96, 186, 98, 184, 100, 182, 102, 180, 104, 178, 106, 176, 134, 194, 136, 196, 88, 194, 86, 196, 198, 84, 82, 200, 202, 80, 78, 204, 206, 76, 74, 208, 72, 210, 212, 70, 68, 214, 198, 138, 140, 200, 142, 202, 144, 204, 146, 206, 208, 148, 150, 210, 152, 212, 154, 214, 64, 66, 62, 64, 62, 60, 60, 58, 58, 56, 56, 54, 54, 52, 52, 50, 50, 48, 48, 46, 46, 44, 44, 42, 42, 40, 40, 38, 38, 36, 36, 34, 34, 32, 32, 30, 30, 28, 28, 26, 26, 24, 24, 22, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 0, 0, 172, 170, 172, 170, 168, 168, 166, 166, 164, 164, 162, 162, 160, 160, 158, 158, 156, 156, 154, 232, 234, 234, 236, 236, 238, 238, 240, 240, 242, 242, 244, 244, 246, 246, 232, 248, 250, 250, 252, 248, 254, 254, 256, 256, 258, 258, 260, 260, 262, 262, 264, 264, 266, 266, 268, 268, 270, 270, 272], "width": 1264, "height": 465}}, "raptor-front-arm": {"raptor-front-arm": {"type": "mesh", "uvs": [0.39563, 0.1396, 0.38771, 0.30213, 0.31231, 0.41784, 0.27287, 0.47836, 0.33389, 0.4507, 0.5488, 0.35329, 0.64093, 0.31153, 0.73024, 0.3653, 1, 0.5277, 1, 0.86607, 0.93243, 1, 0.86176, 0.80967, 0.75576, 0.99765, 0.71748, 1, 0.70276, 0.77443, 0.62032, 0.73448, 0.58793, 0.64519, 0.53561, 0.6582, 0.13449, 0.75798, 0, 0.69219, 0.01846, 0.56358, 0.05499, 0.30918, 0, 0.27863, 0, 0.12423, 0, 0, 0.19596, 0, 0.40243, 0, 0.24536, 0.19241, 0.21679, 0.0811], "triangles": [6, 7, 16, 6, 16, 5, 15, 16, 7, 7, 14, 15, 8, 14, 7, 11, 14, 8, 11, 8, 9, 12, 14, 11, 13, 14, 12, 10, 11, 9, 17, 4, 5, 16, 17, 5, 18, 19, 3, 18, 3, 4, 18, 4, 17, 0, 28, 26, 23, 25, 28, 23, 24, 25, 27, 28, 0, 27, 23, 28, 1, 27, 0, 21, 22, 27, 21, 27, 1, 2, 21, 1, 2, 20, 21, 3, 20, 2, 19, 20, 3, 27, 22, 23, 28, 25, 26], "vertices": [2, 38, 9.24, 26.77, 0.808, 6, 85.19, -98.03, 0.192, 1, 38, 35.87, 35.63, 1, 2, 38, 61.98, 28.62, 0.84641, 39, 40.04, 38.85, 0.15359, 2, 38, 77.67, 27.28, 0.34921, 39, 34.62, 24.06, 0.65079, 3, 38, 73.77, 39.05, 0.10938, 39, 47.01, 24.74, 0.78124, 48, -42.17, -19.42, 0.10938, 2, 39, 86.98, 31.25, 0.65079, 48, -25.75, 17.61, 0.34921, 2, 39, 103.84, 34.49, 0.34375, 48, -19.24, 33.5, 0.65625, 2, 39, 114.05, 19.51, 0.10938, 48, -1.12, 33.84, 0.89062, 1, 48, 53.62, 34.88, 1, 1, 48, 96.03, -19.16, 1, 1, 48, 104.2, -47.32, 1, 1, 48, 71.34, -23.98, 1, 1, 48, 81.39, -64.61, 1, 1, 48, 76.81, -68.82, 1, 1, 48, 46.66, -34.26, 1, 2, 39, 73.14, -45.77, 0.10938, 48, 31.14, -36.12, 0.89062, 2, 39, 73.98, -26.91, 0.34375, 48, 15.82, -25.1, 0.65625, 2, 39, 65.11, -26.69, 0.65079, 48, 10.78, -32.4, 0.34921, 3, 38, 134.76, 4.51, 0.10938, 39, -2.35, -25.03, 0.78124, 48, -27.52, -87.96, 0.10938, 2, 38, 121.45, -16.1, 0.34921, 39, -18.74, -6.77, 0.65079, 2, 38, 96.94, -14.98, 0.84641, 39, -11.21, 16.58, 0.15359, 1, 38, 45.47, -17.43, 1, 1, 38, 47.64, -32.91, 1, 2, 38, 12.11, -34.27, 0.536, 6, 40.33, -56.53, 0.464, 2, 38, -2.57, -46.21, 0.536, 6, 42.42, -37.73, 0.464, 2, 38, -7.4, -14.83, 0.472, 6, 67.87, -56.7, 0.528, 2, 38, -12.49, 18.22, 0.664, 6, 94.68, -76.69, 0.336, 1, 38, 18.79, 4.26, 1, 2, 38, 0.95, -1.4, 0.512, 6, 71.34, -72.13, 0.488], "hull": 27, "edges": [38, 36, 32, 30, 30, 28, 28, 26, 24, 26, 24, 22, 22, 20, 20, 18, 18, 16, 44, 42, 38, 6, 38, 40, 40, 42, 6, 4, 4, 2, 40, 4, 8, 6, 36, 8, 32, 12, 42, 2, 52, 0, 0, 2, 16, 14, 14, 12, 30, 14, 36, 34, 34, 32, 12, 10, 10, 8, 34, 10, 48, 50, 50, 52, 44, 46, 46, 48, 50, 56, 56, 54], "width": 162, "height": 203}}, "raptor-front-leg": {"raptor-front-leg": {"type": "mesh", "uvs": [0.55117, 0.17818, 0.6279, 0.36027, 0.66711, 0.4533, 0.6488, 0.51528, 0.53554, 0.56894, 0.32335, 0.66947, 0.28674, 0.72087, 0.32539, 0.80401, 0.36258, 0.80144, 0.42056, 0.79744, 0.61015, 0.78436, 0.73352, 0.81335, 0.84813, 0.84029, 1, 0.93855, 0.732, 0.92345, 0.62439, 0.91738, 0.72813, 1, 0.58574, 1, 0.47086, 0.98249, 0.36708, 0.96668, 0.26307, 0.95082, 0.16267, 0.93552, 0.03859, 0.72238, 0, 0.66947, 0.0374, 0.62999, 0.1647, 0.49563, 0.23732, 0.45681, 0.2702, 0.43923, 0.28064, 0.43365, 0.223, 0.40571, 0.12565, 0.35851, 0, 0.2976, 0, 0.1524, 0, 0, 0.32132, 0, 0.32222, 0.22778, 0.44931, 0.38031, 0.47664, 0.44362, 0.4615, 0.47375, 0.35106, 0.53247, 0.20091, 0.65257, 0.18528, 0.72148, 0.25222, 0.86314, 0.30942, 0.88124, 0.55694, 0.89613, 0.55858, 0.89208, 0.47493, 0.8534, 0.6059, 0.91526, 0.39706, 0.8913, 0.1323, 0.09352, 0.36997, 0.45346, 0.37163, 0.43828, 0.32516, 0.39424, 0.2376, 0.34426, 0.34066, 0.47415, 0.51677, 0.90503, 0.07821, 0.26333, 0.05796, 0.13086, 0.09601, 0.05963, 0.29303, 0.03825], "triangles": [14, 12, 13, 14, 11, 12, 14, 15, 11, 11, 15, 10, 55, 44, 47, 44, 45, 47, 10, 15, 45, 15, 47, 45, 55, 46, 44, 44, 46, 45, 45, 46, 10, 46, 9, 10, 48, 9, 46, 48, 8, 9, 16, 17, 15, 17, 47, 15, 18, 55, 17, 17, 55, 47, 19, 48, 18, 18, 48, 55, 20, 43, 19, 19, 43, 48, 48, 46, 55, 48, 43, 8, 21, 42, 20, 20, 42, 43, 21, 41, 42, 21, 22, 41, 43, 7, 8, 43, 42, 7, 42, 6, 7, 42, 41, 6, 23, 24, 22, 22, 24, 41, 41, 40, 6, 41, 24, 40, 6, 40, 5, 5, 39, 4, 5, 40, 39, 39, 26, 54, 39, 40, 26, 24, 25, 40, 40, 25, 26, 39, 38, 4, 4, 38, 3, 39, 50, 38, 39, 54, 50, 38, 37, 3, 3, 37, 2, 26, 27, 54, 54, 27, 50, 50, 51, 38, 38, 51, 37, 27, 28, 50, 50, 28, 51, 56, 57, 49, 32, 33, 57, 57, 58, 49, 57, 33, 58, 49, 58, 59, 59, 58, 34, 34, 58, 33, 59, 34, 0, 32, 57, 56, 37, 1, 2, 51, 36, 37, 37, 36, 1, 28, 52, 51, 51, 52, 36, 28, 29, 52, 29, 53, 52, 29, 30, 53, 36, 52, 35, 52, 53, 35, 36, 0, 1, 36, 35, 0, 31, 56, 30, 30, 56, 53, 53, 56, 35, 31, 32, 56, 56, 49, 35, 35, 59, 0, 49, 59, 35], "vertices": [2, 42, 128.03, 88.47, 0.85041, 1, 158.83, -71.92, 0.14959, 2, 42, 219.55, 53.15, 0.77988, 43, -48.05, -38.59, 0.22012, 2, 42, 266.31, 35.11, 0.53545, 43, -36.73, 10.22, 0.46455, 2, 42, 286.89, 9.8, 0.35167, 43, -14.56, 34.15, 0.64833, 2, 42, 281.55, -41.24, 0.09228, 43, 36.71, 36, 0.90772, 3, 42, 271.54, -136.86, 0.05787, 43, 132.77, 39.48, 0.71426, 44, 35, 78.76, 0.22788, 3, 43, 158.22, 55.17, 0.5308, 44, 52.66, 54.64, 0.38143, 45, 7.02, 85.54, 0.08776, 4, 43, 167.14, 99.49, 0.22977, 44, 97.55, 49.25, 0.37788, 45, 28.72, 45.88, 0.15198, 46, -21.26, 49.99, 0.24037, 4, 44, 102.57, 62.61, 0.26558, 45, 42.51, 49.56, 0.17568, 46, -7.07, 51.4, 0.22874, 47, -58.17, 28.03, 0.33001, 4, 44, 109.72, 83.4, 0.11934, 45, 64.09, 55.24, 0.13984, 46, 15.13, 53.52, 0.16668, 47, -36.1, 31.19, 0.57414, 1, 47, 35.81, 41.81, 1, 1, 47, 83.66, 29.43, 1, 1, 47, 128.11, 17.93, 1, 1, 47, 188.73, -29.42, 1, 2, 46, 145.37, -10.99, 0.34248, 47, 84.02, -27.11, 0.65752, 2, 46, 93.3, -7.6, 0.48, 47, 44.87, -26.18, 0.52, 2, 46, 133.18, -49.83, 0.776, 47, 86.69, -66.48, 0.224, 2, 46, 78.79, -50.15, 0.768, 47, 32.38, -69.36, 0.232, 2, 46, 35.36, -41.46, 0.88989, 47, -9.88, -62.73, 0.11011, 1, 46, -4.92, -33.56, 1, 3, 44, 155.05, -5.14, 0.35918, 45, 17.88, -32.51, 0.30633, 46, -44.62, -25.61, 0.3345, 4, 43, 254.98, 126.28, 0.10155, 44, 131.22, -36.21, 0.54212, 45, -21.25, -31.18, 0.20873, 46, -83.02, -17.98, 0.1476, 3, 43, 240.34, 7.81, 0.25587, 44, 11.94, -30.99, 0.61615, 45, -86.32, 68.91, 0.12798, 2, 43, 239.27, -23.1, 0.45486, 44, -18.96, -32.37, 0.54514, 3, 42, 187.65, -209.74, 0.09777, 43, 216.67, -33.36, 0.58893, 44, -30.98, -10.65, 0.3133, 2, 42, 163.86, -128.68, 0.19603, 43, 139.75, -68.26, 0.80397, 3, 42, 165.75, -94.49, 0.3178, 43, 105.59, -71.26, 0.67648, 75, -80.8, -39.34, 0.00571, 3, 42, 166.4, -79.07, 0.45961, 43, 90.23, -72.77, 0.53468, 75, -67.92, -34.74, 0.00571, 3, 42, 166.49, -74.17, 0.53171, 43, 85.43, -73.29, 0.45686, 75, -64.13, -33.63, 0.01143, 3, 42, 141.54, -82.47, 0.7272, 43, 97.13, -96.82, 0.26709, 75, -86.02, -21.18, 0.00571, 3, 42, 99.76, -97.08, 0.84471, 43, 117.34, -136.23, 0.14529, 75, -123.07, 1.78, 0.01, 2, 42, 45.01, -114.56, 0.83615, 1, -51.09, -135.29, 0.16385, 2, 42, -16.21, -74.77, 0.53, 1, -42.95, -58.39, 0.47, 1, 1, -52.66, 17.56, 1, 1, 1, 70.07, 18.78, 1, 2, 42, 93.55, 4.14, 0.84985, 75, -47.66, 63.53, 0.15015, 3, 42, 185.14, -6.67, 0.69958, 43, 15.99, -64.28, 0.22749, 75, 0.73, -14.59, 0.07292, 3, 42, 217.11, -18.75, 0.50337, 43, 23.47, -30.93, 0.48663, 75, 9.65, -46.32, 0.01, 2, 42, 225.64, -32.92, 0.32528, 43, 36.31, -20.51, 0.67472, 3, 42, 223, -84.74, 0.2007, 43, 87.97, -15.86, 0.79322, 75, -43.91, -82.01, 0.00607, 3, 42, 235.62, -168.07, 0.08091, 43, 168.7, 8.29, 0.57148, 44, 6.75, 40.47, 0.34761, 3, 43, 191.8, 35.81, 0.32545, 44, 36.01, 19.63, 0.57243, 45, -31.15, 78.74, 0.10211, 4, 43, 206.64, 111.54, 0.10808, 44, 112.69, 10.83, 0.52068, 45, 6.26, 11.23, 0.23518, 46, -49.03, 19.43, 0.13606, 3, 44, 130.61, 26.42, 0.35068, 45, 29.36, 5.72, 0.28241, 46, -27.13, 10.26, 0.36691, 2, 46, 67.47, 3.17, 0.384, 47, 18.56, -16.63, 0.616, 1, 47, 19.07, -14.52, 1, 2, 46, 36.01, 24.95, 0.384, 47, -13.89, 3.64, 0.616, 2, 46, 86.23, -6.55, 0.488, 47, 37.76, -25.46, 0.512, 4, 44, 151.19, 56, 0.22879, 45, 65.44, 5.56, 0.18425, 46, 8.45, 4.28, 0.45492, 47, 0, 0, 0.13205, 3, 42, -9.28, -17.51, 0.21934, 1, 7.72, -30.86, 0.74243, 75, -126.22, 130.87, 0.03823, 3, 42, 195.91, -53.82, 0.42127, 43, 61.12, -47.06, 0.57302, 75, -30.92, -46.02, 0.00571, 3, 42, 190.1, -48.45, 0.52927, 43, 56.62, -53.56, 0.46502, 75, -29.84, -39.6, 0.00571, 3, 42, 161.27, -48.26, 0.74345, 43, 60.44, -82.13, 0.18733, 75, -47.3, -19.14, 0.06922, 3, 42, 120.38, -58.54, 0.78619, 43, 76.31, -121.19, 0.13381, 75, -79.81, 7.32, 0.08, 3, 42, 197.37, -69.23, 0.33416, 43, 76.18, -43.47, 0.66185, 75, -43.15, -54, 0.00398, 4, 44, 167.22, 97.41, 0.10303, 45, 97.38, 0.84, 0.08297, 46, 54.09, -2.79, 0.51764, 47, 4.74, -23.22, 0.29636, 3, 42, 49.5, -83.17, 0.65468, 1, -17.26, -114.16, 0.26246, 75, -142.18, 45.76, 0.08286, 3, 42, -9.83, -51.31, 0.41164, 1, -21.43, -46.95, 0.57122, 75, -153.07, 111.17, 0.01714, 2, 42, -31.44, -20.43, 0.27617, 1, -6.57, -12.31, 0.72383, 3, 42, 0.92, 47.46, 0.40628, 1, 68.18, -4.06, 0.57468, 75, -69.72, 165.13, 0.01904], "hull": 35, "edges": [46, 44, 44, 42, 32, 34, 32, 30, 26, 24, 14, 12, 12, 10, 6, 4, 66, 68, 0, 68, 46, 48, 48, 50, 40, 42, 16, 14, 58, 56, 4, 2, 2, 0, 10, 8, 8, 6, 78, 80, 80, 82, 82, 84, 84, 86, 86, 96, 16, 18, 18, 20, 38, 40, 62, 64, 64, 66, 100, 102, 102, 104, 58, 60, 60, 62, 106, 104, 54, 56, 50, 52, 52, 54, 108, 100, 78, 76, 76, 74, 72, 74, 72, 70, 70, 98, 92, 90, 56, 102, 100, 54, 52, 108, 58, 104, 60, 106, 76, 6, 74, 4, 72, 2, 78, 8, 92, 20, 92, 88, 88, 94, 90, 30, 94, 30, 26, 28, 28, 30, 20, 22, 22, 24, 28, 22, 34, 36, 36, 38, 94, 110, 110, 96, 36, 110, 110, 88, 60, 112, 112, 114, 114, 116, 116, 118, 118, 0], "width": 382, "height": 514}}, "raptor-hindleg-back": {"raptor-hindleg-back": {"type": "mesh", "uvs": [0.45041, 0.09352, 0.56934, 0.23361, 0.65294, 0.47297, 0.66354, 0.50822, 0.63175, 0.54255, 0.32384, 0.69723, 0.30069, 0.73876, 0.27934, 0.77704, 0.30417, 0.83513, 0.31059, 0.85014, 0.34101, 0.85047, 0.45165, 0.85164, 0.59556, 0.81882, 0.91177, 0.92548, 1, 1, 0.56337, 0.96427, 0.4835, 0.98261, 0.29879, 0.98027, 0.22808, 0.98389, 0.15998, 0.98738, 0.15424, 0.95547, 0.13895, 0.87048, 0.07371, 0.78726, 0, 0.753, 0, 0.7049, 0, 0.671, 0.11876, 0.64653, 0.16535, 0.5266, 0.28496, 0.47398, 0.29011, 0.45774, 0.29427, 0.4446, 0.20635, 0.40396, 0.06129, 0.33691, 0, 0.25247, 0, 0, 0.30793, 0, 0.276, 0.20262, 0.40398, 0.31122, 0.48439, 0.45964, 0.48318, 0.48384, 0.47029, 0.51062, 0.22698, 0.67328, 0.17142, 0.7242, 0.17122, 0.78242, 0.22996, 0.89469, 0.24677, 0.90829, 0.28672, 0.9146, 0.46583, 0.91414], "triangles": [15, 13, 14, 16, 47, 15, 15, 12, 13, 15, 47, 12, 18, 46, 17, 18, 45, 46, 17, 47, 16, 17, 46, 47, 47, 10, 11, 47, 46, 10, 47, 11, 12, 45, 18, 19, 44, 45, 20, 20, 45, 19, 20, 21, 44, 46, 9, 10, 46, 45, 9, 45, 44, 9, 21, 43, 44, 44, 8, 9, 44, 7, 8, 44, 43, 7, 21, 22, 43, 43, 22, 42, 43, 42, 7, 22, 23, 24, 24, 42, 22, 7, 42, 6, 42, 41, 6, 6, 41, 5, 24, 26, 42, 42, 26, 41, 24, 25, 26, 5, 40, 4, 5, 41, 40, 41, 28, 40, 26, 27, 41, 41, 27, 28, 40, 39, 4, 28, 29, 40, 40, 29, 39, 4, 39, 3, 39, 2, 3, 29, 30, 39, 39, 38, 2, 39, 30, 38, 38, 1, 2, 30, 37, 38, 38, 37, 1, 30, 31, 37, 31, 36, 37, 31, 32, 36, 32, 33, 36, 37, 0, 1, 37, 36, 0, 33, 34, 36, 36, 35, 0, 36, 34, 35], "vertices": [1, 17, 53.94, 69.16, 1, 1, 17, 126.23, 67.31, 1, 2, 17, 226.42, 31.14, 0.9375, 18, -30.88, -1.11, 0.0625, 2, 17, 240.84, 25.33, 0.7, 18, -25.65, 13.52, 0.3, 2, 17, 246.67, 8.06, 0.3, 18, -8.61, 20.02, 0.7, 3, 17, 240.82, -115.25, 0.0625, 18, 114.81, 19.01, 0.875, 19, 9.48, 59.16, 0.0625, 2, 18, 131.07, 29.69, 0.7, 19, 22.12, 44.36, 0.3, 2, 18, 146.07, 39.54, 0.3, 19, 33.76, 30.71, 0.7, 3, 18, 152.6, 65.01, 0.12567, 19, 59.85, 27.41, 0.75203, 20, 15.86, 48.05, 0.1223, 2, 19, 66.6, 26.56, 0.82916, 20, 16.73, 41.31, 0.17084, 3, 19, 71.2, 35.76, 0.64716, 20, 26.79, 39.17, 0.1317, 21, -67.33, 18.96, 0.22114, 3, 19, 87.93, 69.21, 0.0625, 20, 63.37, 31.39, 0.675, 21, -30.18, 23.3, 0.2625, 2, 20, 113.82, 35.72, 0.10381, 21, 16.23, 43.56, 0.89619, 1, 21, 128.14, 12.02, 1, 1, 21, 161.85, -15.82, 1, 1, 21, 13.52, -19.72, 1, 2, 20, 62.98, -25.82, 0.7, 21, -12.23, -31.02, 0.3, 3, 19, 115.12, -1.34, 0.08333, 20, 1.94, -12.66, 0.83333, 21, -74.27, -38.11, 0.08333, 2, 19, 106.11, -23.53, 0.3, 20, -21.81, -9.53, 0.7, 2, 19, 97.44, -44.91, 0.7, 20, -44.67, -6.51, 0.3, 2, 19, 84.26, -40.69, 0.9375, 20, -43.91, 7.3, 0.0625, 1, 19, 49.19, -29.47, 1, 2, 18, 206.75, 5.37, 0.13333, 19, 7.44, -33.78, 0.86667, 2, 18, 219.64, -20.52, 0.36111, 19, -16.64, -49.81, 0.63889, 2, 18, 208.41, -37.83, 0.72083, 19, -35.22, -40.82, 0.27917, 2, 18, 200.49, -50.03, 0.91667, 19, -48.31, -34.49, 0.08333, 1, 18, 161.11, -36.98, 1, 2, 17, 150.1, -116.77, 0.08333, 18, 119.88, -71.55, 0.91667, 2, 17, 154.99, -70.72, 0.42846, 18, 73.68, -68.48, 0.57154, 2, 17, 150.31, -65.27, 0.35605, 18, 68.43, -73.37, 0.64395, 2, 17, 146.52, -60.87, 0.59148, 18, 64.18, -77.33, 0.40852, 2, 17, 115.13, -75.09, 0.8446, 18, 79.61, -108.13, 0.1554, 1, 17, 63.33, -98.54, 1, 1, 17, 21.78, -94.56, 1, 1, 17, -66.69, -32.05, 1, 1, 17, -6.63, 52.97, 1, 1, 17, 58.15, -6.01, 1, 1, 17, 121.17, 2.44, 1, 1, 17, 188.87, -12.1, 1, 2, 17, 197.12, -18.43, 0.7, 18, 19.79, -28.44, 0.3, 2, 17, 203.99, -28.62, 0.3, 18, 29.7, -21.18, 0.7, 1, 18, 136.67, -7.43, 1, 2, 18, 164.32, 0.67, 0.7, 19, -2.53, 7.74, 0.3, 2, 18, 177.98, 21.58, 0.25, 19, 19.92, -3.2, 0.75, 1, 19, 71.94, -6.3, 1, 2, 19, 79.66, -3.72, 0.7, 20, -9.29, 21.05, 0.3, 2, 19, 87.98, 7.26, 0.3125, 20, 3.43, 15.76, 0.6875, 2, 20, 62.84, 4.16, 0.72917, 21, -21.96, -2.67, 0.27083], "hull": 36, "edges": [66, 68, 66, 64, 56, 54, 54, 52, 52, 50, 46, 44, 44, 42, 34, 32, 32, 30, 30, 28, 28, 26, 26, 24, 24, 22, 10, 8, 8, 6, 6, 4, 4, 2, 2, 0, 68, 70, 0, 70, 46, 48, 48, 50, 14, 12, 12, 10, 60, 58, 58, 56, 42, 40, 40, 38, 18, 16, 16, 14, 22, 20, 20, 18, 38, 36, 36, 34, 60, 62, 62, 64, 68, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 16, 88, 86, 88, 18, 90, 90, 38, 88, 90, 20, 92, 92, 36, 90, 92, 92, 94, 94, 22, 94, 32, 30, 24, 88, 40, 86, 14, 84, 12, 82, 10, 82, 52, 48, 84, 44, 86, 78, 6, 4, 76, 80, 8, 80, 56, 58, 78, 76, 60], "width": 338, "height": 429}}, "raptor-horn": {"raptor-horn": {"type": "mesh", "uvs": [0.23202, 0, 0.36456, 0.3051, 0.37967, 0.28578, 0.42983, 0.41504, 0.48255, 0.4592, 0.49181, 0.37558, 0.54262, 0.43364, 0.62744, 0.22373, 0.72685, 0.20157, 0.71155, 0.10296, 0.7437, 0.12629, 0.87154, 0.32694, 0.92655, 0.58847, 0.95146, 0.58291, 1, 0.79797, 0.99855, 0.91608, 0.95668, 0.9066, 0.89548, 0.84052, 0.85745, 0.71568, 0.81176, 0.71081, 0.79146, 0.64162, 0.7146, 0.66948, 0.70308, 0.72109, 0.66405, 0.91955, 0.57139, 1, 0.51265, 1, 0.40749, 0.94178, 0.34499, 0.80186, 0.24959, 0.49467, 0.23945, 0.4213, 0.15768, 0.37748, 0.10301, 0.43856, 0.0716, 0.54182, 0.0635, 0.72333, 0.0968, 0.78581, 0.19705, 0.70312, 0.20849, 0.70702, 0.13611, 0.87184, 0.0794, 0.95675, 0.03745, 0.92784, 0, 0.72707, 0, 0.49239, 0.01821, 0.37577, 0.08924, 0.16005, 0.2217, 0.08591, 0.21231, 0.02037, 0.21551, 0, 0.93395, 0.7495, 0.84078, 0.47214, 0.76078, 0.46484, 0.71616, 0.58482, 0.55373, 0.75879, 0.45392, 0.65487, 0.35185, 0.54621], "triangles": [17, 18, 47, 0, 45, 46, 44, 45, 0, 8, 9, 10, 1, 44, 0, 8, 10, 11, 30, 43, 44, 29, 30, 44, 31, 42, 43, 1, 29, 44, 30, 31, 43, 4, 5, 6, 49, 8, 11, 48, 49, 11, 28, 29, 1, 32, 42, 31, 41, 42, 32, 53, 28, 1, 12, 48, 11, 49, 50, 7, 49, 7, 8, 6, 7, 50, 20, 49, 48, 50, 49, 20, 52, 3, 4, 6, 50, 51, 21, 50, 20, 18, 19, 20, 48, 18, 20, 48, 47, 18, 21, 22, 50, 32, 40, 41, 33, 40, 32, 47, 48, 12, 22, 51, 50, 4, 6, 51, 52, 4, 51, 47, 13, 14, 53, 27, 28, 1, 2, 3, 53, 1, 3, 37, 34, 35, 37, 35, 36, 16, 47, 14, 17, 47, 16, 23, 51, 22, 39, 40, 33, 39, 33, 34, 38, 39, 34, 53, 52, 27, 52, 53, 3, 25, 26, 52, 26, 27, 52, 37, 38, 34, 51, 25, 52, 24, 25, 51, 23, 24, 51, 47, 12, 13, 15, 16, 14], "vertices": [1, 34, 281.61, 81.74, 1, 1, 34, 213.35, 84.72, 1, 1, 34, 211.41, 78.74, 1, 2, 34, 184.08, 81.47, 0.99749, 74, -201.91, 110.55, 0.00251, 1, 34, 165.27, 73.64, 1, 1, 34, 171.82, 61.6, 1, 2, 34, 152.01, 55.86, 0.99978, 74, -160.92, 112.71, 0.00022, 2, 34, 152.05, 10.45, 0.99, 74, -134.51, 149.65, 0.01, 2, 34, 127.93, -16.63, 0.98888, 74, -99.14, 157.63, 0.01112, 2, 34, 142.65, -24.37, 0.97775, 74, -106.6, 172.5, 0.02225, 2, 34, 131.57, -29.57, 0.9788, 74, -94.56, 170.26, 0.0212, 2, 34, 75.83, -37.63, 0.97359, 74, -44.55, 144.38, 0.02641, 2, 34, 32.94, -20.65, 0.93, 74, -19.57, 105.6, 0.07, 2, 34, 26.9, -27.43, 0.9, 74, -10.71, 107.6, 0.1, 2, 34, -9.24, -14.28, 0.86, 74, 11.02, 75.86, 0.14, 2, 34, -21.6, -0.12, 0.78, 74, 12.83, 57.16, 0.22, 2, 34, -9.42, 9.08, 0.84, 74, -2.43, 56.76, 0.16, 2, 34, 14.03, 16.44, 0.88122, 74, -25.78, 64.43, 0.11878, 2, 34, 37.64, 11.23, 0.93, 74, -41.95, 82.41, 0.07, 2, 34, 50.35, 21.92, 0.94, 74, -58.5, 81.12, 0.06, 2, 34, 63.22, 18.84, 0.95957, 74, -67.18, 91.12, 0.04043, 2, 34, 80.71, 41.03, 0.99714, 74, -94.31, 83.25, 0.00286, 1, 34, 78.21, 49.9, 1, 1, 34, 67.2, 82.69, 1, 1, 34, 83.22, 114.91, 1, 1, 34, 98.88, 129.38, 1, 1, 34, 133.2, 148.49, 1, 1, 34, 164.96, 147.54, 1, 1, 34, 223.55, 135.17, 1, 1, 34, 234.17, 129.11, 1, 1, 34, 260.7, 144.13, 1, 1, 34, 268.68, 164.73, 1, 1, 34, 265.91, 184.53, 1, 1, 34, 248.48, 207.72, 1, 1, 34, 232.86, 206.82, 1, 1, 34, 215.06, 172.46, 1, 1, 34, 211.59, 170.1, 1, 1, 34, 213.1, 207.18, 1, 1, 34, 219.05, 231.06, 1, 1, 34, 233.36, 238.02, 1, 1, 34, 265.01, 223.8, 1, 1, 34, 290.33, 196.4, 1, 1, 34, 298.06, 178.29, 1, 1, 34, 302.4, 135.6, 1, 1, 34, 275.09, 94.31, 1, 1, 34, 284.66, 88.97, 1, 1, 34, 286.01, 85.81, 1, 2, 34, 13.6, -3.66, 0.93, 74, -13.73, 80.52, 0.07, 1, 34, 68.37, -13.1, 1, 2, 34, 90.48, 5.75, 0.995, 74, -81.72, 117.62, 0.005, 2, 34, 89.43, 30.76, 0.995, 74, -95.42, 96.68, 0.005, 1, 34, 113.96, 91.09, 1, 1, 34, 151.78, 103.55, 1, 1, 34, 190.72, 116, 1], "hull": 47, "edges": [0, 92, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 30, 32, 32, 34, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 28, 30, 16, 18, 34, 36, 58, 60, 96, 94, 96, 98, 98, 100], "width": 363, "height": 159}}, "raptor-horn-back": {"raptor-horn-back": {"x": 121.43, "y": 83.01, "rotation": -132.22, "width": 351, "height": 153}}, "raptor-jaw": {"raptor-jaw": {"type": "mesh", "uvs": [0.43611, 0.10281, 0.50457, 0.26446, 0.59673, 0.37777, 0.69416, 0.49754, 0.79771, 0.54917, 0.91149, 0.59812, 1, 0.63619, 0.99305, 0.85625, 0.67606, 1, 0.39521, 1, 0.19457, 0.89404, 0.2161, 0.6497, 0, 0.46112, 0, 0, 0.26125, 1e-05, 0.19457, 0.29385, 0.60678, 0.81243, 0.42896, 0.88938, 0.86006, 0.80271, 0.64788, 0.93008, 0.58349, 0.62419, 0.41196, 0.69752, 0.46153, 0.51921, 0.35989, 0.3664, 0.32564, 0.54238], "triangles": [15, 14, 0, 1, 23, 0, 12, 13, 15, 15, 13, 14, 8, 18, 7, 7, 18, 6, 18, 5, 6, 8, 19, 18, 17, 21, 16, 23, 15, 0, 24, 15, 23, 21, 20, 16, 21, 22, 20, 21, 24, 22, 24, 23, 22, 2, 22, 23, 20, 22, 3, 22, 2, 3, 2, 23, 1, 11, 24, 21, 4, 16, 20, 16, 4, 18, 17, 16, 19, 19, 16, 18, 18, 4, 5, 4, 20, 3, 9, 10, 17, 8, 9, 19, 9, 17, 19, 10, 21, 17, 10, 11, 21, 11, 12, 24, 12, 15, 24], "vertices": [2, 54, 28.47, 75.44, 0.40489, 74, -91.22, 43.02, 0.59511, 2, 54, 66.98, 65.83, 0.54061, 74, -64.96, 13.27, 0.45939, 2, 54, 98.09, 68.86, 0.67457, 74, -37.62, -1.88, 0.32543, 2, 54, 132.32, 71.81, 0.77891, 74, -7.76, -18.87, 0.22109, 2, 54, 163.31, 76.98, 0.78694, 74, 20.7, -32.2, 0.21306, 2, 54, 190.52, 90.03, 0.78631, 74, 50.51, -36.88, 0.21369, 2, 54, 210.32, 100.44, 0.86507, 74, 72.72, -39.54, 0.13493, 1, 54, 238.9, 67.81, 1, 2, 54, 234.83, 1.64, 0.98898, 74, 36.86, -134.8, 0.01102, 2, 54, 173.67, -58.3, 0.98603, 74, -47.5, -149.48, 0.01397, 1, 54, 125.49, -79.1, 1, 2, 54, 87.8, -40.51, 0.77481, 74, -108.13, -86.11, 0.22519, 3, 54, -5.59, -78.2, 0.30353, 8, -21.32, -18.84, 0.63716, 74, -202.55, -73.18, 0.05931, 2, 8, 106.45, -6.22, 0.98571, 74, -224.49, 48.7, 0.01429, 2, 8, 95.42, -85.63, 0.62475, 74, -144.43, 44.69, 0.37525, 2, 54, 24.89, 6.25, 0.63522, 74, -133.42, -11.92, 0.36478, 2, 54, 177.48, 5.6, 0.7446, 74, -8.12, -99.01, 0.2554, 2, 54, 160.25, -36.54, 0.86286, 74, -46.21, -123.95, 0.13714, 2, 54, 216.48, 51.69, 0.84606, 74, 50.14, -83.17, 0.15394, 2, 54, 213.42, 2.58, 0.92571, 74, 19.76, -121.89, 0.07429, 2, 54, 138.62, 31.33, 0.63037, 74, -25.53, -55.78, 0.36963, 2, 54, 124.75, -11.2, 0.73167, 74, -61.07, -82.94, 0.26833, 2, 54, 102.54, 22.8, 0.5705, 74, -60.08, -42.34, 0.4295, 2, 54, 61.9, 25.79, 0.54075, 74, -91.85, -16.83, 0.45925, 2, 54, 86.18, -5.32, 0.63768, 74, -89.5, -56.22, 0.36232], "hull": 15, "edges": [24, 26, 24, 22, 22, 20, 20, 18, 18, 16, 8, 6, 2, 0, 26, 28, 0, 28, 26, 30, 24, 30, 30, 0, 14, 16, 14, 12, 8, 32, 32, 34, 8, 10, 10, 12, 2, 4, 4, 6], "width": 252, "height": 275}}, "raptor-jaw-inside": {"raptor-jaw2": {"type": "mesh", "path": "raptor-jaw", "uvs": [0.43611, 0.10281, 0.50457, 0.26446, 0.69416, 0.49754, 0.79771, 0.54917, 1, 0.63619, 0.99305, 0.85625, 0.67606, 1, 0.39521, 1, 0.19457, 0.89404, 0.2161, 0.6497, 0, 0.46112, 0, 0, 0.26125, 1e-05, 0.19457, 0.29385, 0.60678, 0.81243, 0.42896, 0.88938], "triangles": [13, 11, 12, 10, 11, 13, 13, 12, 0, 13, 0, 1, 9, 13, 1, 9, 1, 2, 10, 13, 9, 14, 9, 2, 14, 2, 3, 5, 3, 4, 14, 3, 5, 15, 9, 14, 8, 9, 15, 7, 8, 15, 6, 14, 5, 15, 14, 6, 7, 15, 6], "vertices": [2, 54, 28.9, 96.24, 0.84, 74, -73.48, 56.29, 0.16, 2, 54, 65.84, 86.82, 0.84002, 74, -48.4, 27.58, 0.15998, 2, 54, 125.41, 92.66, 0.88641, 74, 3.97, -1.4, 0.11359, 2, 54, 151.38, 98.09, 0.83356, 74, 28.44, -11.66, 0.16644, 2, 54, 191.91, 121, 0.85174, 74, 74.81, -15.78, 0.14826, 2, 54, 227.3, 89.29, 0.83919, 74, 85.97, -61.97, 0.16081, 2, 54, 223.4, 25.16, 0.94641, 74, 46.37, -112.58, 0.05359, 2, 54, 176.27, -33.76, 0.77848, 74, -25.86, -134.36, 0.22152, 2, 54, 132.75, -53.77, 0.70055, 74, -73.05, -126.15, 0.29945, 2, 54, 94.17, -16.26, 0.68436, 74, -83.54, -73.38, 0.31564, 3, 54, 19.52, -38.44, 0.28887, 8, -3.76, -62.46, 0.60639, 74, -154.63, -56.39, 0.10474, 1, 8, 98.59, -46.15, 1, 2, 8, 110.02, -102.65, 0.84736, 74, -123.17, 57.1, 0.15264, 2, 54, 31.25, 29.22, 0.82334, 74, -109.57, -0.23, 0.17666, 2, 54, 171.54, 28.72, 0.86269, 74, 5.69, -80.23, 0.13731, 2, 54, 161.87, -12.64, 0.71096, 74, -25.74, -108.8, 0.28904], "hull": 13, "edges": [20, 22, 20, 18, 18, 16, 16, 14, 14, 12, 6, 4, 4, 2, 2, 0, 22, 24, 0, 24, 22, 26, 20, 26, 26, 0, 10, 12, 6, 8, 10, 8, 6, 28, 28, 30], "width": 252, "height": 275}}, "raptor-mouth-inside": {"raptor-mouth-inside": {"type": "mesh", "uvs": [1, 0.29017, 0.37217, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 0, 1, 3, 3, 4, 0], "vertices": [1, 8, 26.56, -211.68, 1, 1, 54, 130.45, -7.83, 1, 1, 54, 109.72, -24.21, 1, 1, 8, 47.22, -139.7, 1, 1, 8, 50.33, -210.63, 1], "hull": 5, "edges": [4, 6, 6, 8, 2, 4, 0, 8, 2, 0], "width": 71, "height": 82}}, "raptor-saddle": {"raptor-saddle-w-shadow": {"type": "mesh", "uvs": [0.28517, 0.09749, 0.26891, 0.14719, 0.32431, 0.28893, 0.45069, 0.52793, 0.56076, 0.56219, 0.69936, 0.53502, 0.71567, 0.44878, 0.83797, 0.36373, 0.91271, 0.34719, 1, 0.53622, 1, 0.61771, 0.93479, 0.82943, 0.87524, 0.96013, 0.74099, 1, 0.28984, 0.9496, 0.12982, 0.85304, 0.10295, 0.69443, 0.10449, 0.63657, 0.20499, 0.6452, 0.0954, 0.41741, 0.00133, 0.37841, 0, 0.27026, 0.11186, 0, 0.17021, 0, 0.24413, 0, 0.46313, 0.92332, 0.56755, 0.84415, 0.94056, 0.67906, 0.9263, 0.43106, 0.2137, 0.18682, 0.18239, 0.28963, 0.21653, 0.33824, 0.32307, 0.44535, 0.38606, 0.52911, 0.39069, 0.55971, 0.36568, 0.6032, 0.38235, 0.62414, 0.43979, 0.69174, 0.53891, 0.71429, 0.62321, 0.7159, 0.70381, 0.69254, 0.74827, 0.66355, 0.78996, 0.62087, 0.80571, 0.56933, 0.79737, 0.54033, 0.75661, 0.51215, 0.72789, 0.51537, 0.20634, 0.08376, 0.17577, 0.12886, 0.13686, 0.18765, 0.11185, 0.28751, 0.17762, 0.36321, 0.26192, 0.46066, 0.30546, 0.50012, 0.31565, 0.55488, 0.81026, 0.7038, 0.86992, 0.65976, 0.89927, 0.54517, 0.84925, 0.47993, 0.81868, 0.43161], "triangles": [47, 23, 24, 47, 24, 0, 47, 22, 23, 1, 47, 0, 48, 47, 1, 29, 48, 1, 48, 49, 22, 47, 48, 22, 49, 48, 29, 21, 22, 49, 50, 21, 49, 29, 1, 2, 30, 49, 29, 30, 29, 2, 50, 49, 30, 31, 30, 2, 51, 50, 30, 51, 30, 31, 20, 21, 50, 19, 20, 50, 19, 50, 51, 8, 9, 28, 7, 8, 28, 59, 7, 28, 32, 31, 2, 2, 3, 32, 7, 59, 6, 52, 31, 32, 51, 31, 52, 58, 59, 28, 53, 52, 32, 45, 6, 59, 45, 59, 58, 46, 6, 45, 33, 53, 32, 3, 33, 32, 46, 5, 6, 44, 45, 58, 57, 58, 28, 57, 28, 9, 44, 58, 57, 54, 53, 33, 34, 33, 3, 54, 33, 34, 43, 44, 57, 35, 54, 34, 57, 9, 10, 46, 44, 43, 44, 46, 45, 36, 35, 34, 52, 19, 51, 19, 52, 18, 54, 52, 53, 54, 18, 52, 56, 43, 57, 27, 56, 57, 42, 43, 56, 46, 42, 5, 43, 42, 46, 41, 5, 42, 10, 27, 57, 3, 36, 34, 37, 3, 4, 37, 36, 3, 40, 5, 41, 4, 5, 40, 16, 17, 18, 55, 42, 56, 41, 42, 55, 38, 37, 4, 39, 38, 4, 40, 39, 4, 27, 55, 56, 11, 55, 27, 11, 27, 10, 26, 38, 39, 15, 16, 18, 26, 25, 37, 26, 37, 38, 14, 18, 37, 35, 18, 54, 36, 18, 35, 37, 18, 36, 14, 37, 25, 15, 18, 14, 12, 55, 11, 55, 13, 40, 55, 40, 41, 13, 55, 12, 26, 39, 40, 13, 26, 40, 25, 26, 13, 14, 25, 13], "vertices": [262.59, 79.92, 244.74, 92.82, 188.83, 69.76, 114.07, 26.79, 102.07, -9.38, 113.32, -54.32, 145.78, -58.87, 178.6, -97.98, 185.38, -122.19, 120.06, -152.19, 84.63, -153.03, 15.94, -134.16, -24.77, -117.84, -45.38, -70.46, -59.12, 75.16, -24.15, 128.17, 35.11, 138.33, 56.81, 138.33, 54.35, 105.5, 138.9, 143.23, 152.8, 174.24, 193.34, 175.62, 295.51, 141.56, 295.96, 122.54, 296.53, 98.45, -47.94, 18.91, -17.46, -14.42, 67.83, -136.04, 154.04, -127.36, 226.26, 106.71, 187.47, 116.01, 169.51, 104.45, 130.18, 68.79, 99.26, 47.52, 87.82, 45.74, 71.33, 53.5, 63.61, 47.89, 52.57, 28.9, 44.88, -3.61, 44.93, -31.1, 54.3, -57.16, 65.51, -71.39, 81.83, -84.6, 101.28, -89.28, 112.08, -86.31, 122.33, -72.77, 120.91, -63.44, 264.84, 110.02, 247.7, 119.59, 225.36, 131.75, 187.73, 139.02, 159.85, 116.91, 123.97, 88.58, 109.51, 74.04, 89.06, 70.23, 41.99, -86.15, 68.62, -111.21, 111.05, -119.56, 135.12, -102.68, 153, -92.29], "hull": 25, "edges": [44, 42, 40, 42, 40, 38, 38, 36, 36, 34, 34, 32, 32, 30, 30, 28, 28, 50, 50, 52, 52, 26, 26, 24, 24, 22, 22, 54, 54, 20, 20, 18, 18, 56, 56, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 0, 0, 48, 26, 28, 20, 22, 16, 18, 2, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 10, 44, 46, 46, 48, 46, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 36, 110, 112, 112, 114, 114, 116, 116, 118, 118, 14], "width": 324, "height": 341}}, "raptor-saddle-strap-back": {"raptor-saddle-strap-back": {"x": 78.45, "y": -4.2, "rotation": 120.24, "width": 108, "height": 148}}, "raptor-saddle-strap-front": {"raptor-saddle-strap-front": {"x": 128.83, "y": -4.72, "rotation": 61.29, "width": 114, "height": 189}}, "raptor-tongue": {"raptor-tongue": {"type": "mesh", "uvs": [0.35242, 0.21561, 0.4794, 0.44246, 0.62072, 0.61177, 0.80563, 0.75374, 1, 0.90297, 1, 1, 0.8971, 1, 0.72055, 0.92255, 0.50668, 0.82872, 0.30402, 0.70725, 0.10537, 0.57889, 0, 0.50622, 0, 0, 0.26225, 0], "triangles": [7, 8, 3, 6, 7, 3, 4, 6, 3, 6, 4, 5, 8, 7, 6, 9, 1, 2, 8, 9, 2, 9, 10, 1, 8, 2, 3, 0, 11, 12, 0, 12, 13, 10, 11, 0, 1, 10, 0], "vertices": [2, 71, 3.64, 27.05, 0.6875, 72, -47.27, 33.88, 0.3125, 3, 71, 39.1, 19.46, 0.3125, 72, -13.42, 20.87, 0.625, 73, -51.54, 33.38, 0.0625, 3, 71, 71.56, 19.03, 0.0625, 72, 18.59, 15.4, 0.625, 73, -21.56, 20.92, 0.3125, 2, 72, 55.03, 16.86, 0.3125, 73, 14.29, 14.24, 0.6875, 2, 72, 93.34, 18.4, 0.08333, 73, 51.98, 7.21, 0.91667, 1, 73, 56.09, -4.51, 1, 2, 72, 85.07, -1.49, 0.08333, 73, 39.49, -10.33, 0.91667, 2, 72, 54.23, -9.18, 0.3125, 73, 7.71, -10.97, 0.6875, 3, 71, 75.14, -14.72, 0.0625, 72, 16.87, -18.5, 0.625, 73, -30.77, -11.74, 0.3125, 3, 71, 38.8, -25.81, 0.3125, 72, -20.75, -23.8, 0.625, 73, -68.63, -8.54, 0.0625, 2, 71, 2.4, -35.78, 0.6875, 72, -58.25, -27.99, 0.3125, 2, 71, -17.29, -40.63, 0.91667, 72, -78.46, -29.72, 0.08333, 1, 71, -59.92, 8.19, 1, 2, 71, -26.14, 37.69, 0.91667, 72, -75.02, 49.02, 0.08333], "hull": 14, "edges": [22, 24, 10, 12, 10, 8, 24, 26, 16, 4, 18, 16, 2, 4, 18, 2, 22, 20, 0, 26, 20, 0, 0, 2, 12, 14, 14, 16, 4, 6, 6, 8, 14, 6, 20, 18], "width": 171, "height": 128}}, "raptow-jaw-tooth": {"raptor-jaw-tooth": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 54, 275.87, 107.8, 1, 2, 54, 235.58, 46.93, 0.94857, 74, -85.57, -171.76, 0.05143, 2, 54, 155.53, 99.92, 0.94, 74, -56.06, -80.4, 0.06, 1, 54, 195.82, 160.79, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 73, "height": 96}}, "spineboy-torso": {"torso": {"x": 55.88, "y": 4.87, "rotation": -104.14, "width": 108, "height": 182}}, "stirrup-back": {"stirrup-back": {"x": 53.2, "y": 31.34, "rotation": -21.13, "width": 87, "height": 69}}, "stirrup-front": {"stirrup-front": {"x": 36.14, "y": 20.39, "rotation": -21.13, "width": 89, "height": 100}}, "stirrup-strap": {"stirrup-strap": {"type": "mesh", "uvs": [0.36823, 0.27894, 0.45738, 0.38897, 0.54452, 0.49652, 0.67872, 0.59135, 0.81977, 0.69102, 1, 0.77344, 1, 1, 0.77957, 1, 0.6373, 0.8163, 0.53364, 0.72349, 0.40534, 0.60861, 0.30886, 0.52535, 0.2105, 0.44048, 0, 0.26245, 0, 0, 0.30637, 0, 0.20242, 0.23001], "triangles": [2, 10, 1, 9, 10, 2, 9, 2, 3, 8, 9, 3, 8, 3, 4, 7, 8, 4, 7, 4, 5, 7, 5, 6, 16, 14, 15, 13, 14, 16, 16, 15, 0, 12, 16, 0, 12, 0, 1, 13, 16, 12, 11, 12, 1, 10, 11, 1], "vertices": [2, 59, 24.72, 8.04, 0.80345, 60, -17.42, 11.02, 0.19655, 2, 59, 37.95, 8.04, 0.59979, 60, -4.37, 8.87, 0.40021, 2, 59, 50.88, 8.05, 0.36895, 60, 8.39, 6.77, 0.63105, 2, 59, 65.92, 12.27, 0.17748, 60, 23.92, 8.48, 0.82252, 2, 59, 81.73, 16.71, 0.05943, 60, 40.24, 10.28, 0.94057, 2, 59, 98.83, 25.04, 0.0121, 60, 58.47, 15.72, 0.9879, 2, 59, 114.44, 11.58, 0.00191, 60, 71.67, -0.11, 0.99809, 2, 59, 100.47, -4.61, 0.01818, 60, 55.25, -13.81, 0.98182, 2, 59, 78.8, -4.14, 0.07488, 60, 33.95, -9.81, 0.92512, 2, 59, 65.83, -6.24, 0.2028, 60, 20.81, -9.77, 0.7972, 2, 59, 49.79, -8.84, 0.39972, 60, 4.56, -9.71, 0.60028, 2, 59, 37.94, -10.97, 0.62658, 60, -7.48, -9.89, 0.37342, 2, 59, 25.86, -13.15, 0.82035, 60, -19.76, -10.07, 0.17965, 2, 59, 0.25, -18.03, 0.95289, 60, -45.82, -10.7, 0.04711, 2, 59, -17.84, -2.43, 0.9771, 60, -61.11, 7.64, 0.0229, 2, 59, 1.58, 20.07, 0.94775, 60, -38.29, 26.68, 0.05225, 2, 59, 10.84, -1.24, 0.9771, 60, -32.63, 4.14, 0.0229], "hull": 16, "edges": [28, 30, 30, 0, 12, 10, 8, 10, 12, 14, 14, 16, 26, 28, 24, 26, 26, 32, 32, 30, 20, 22, 22, 24, 0, 2, 2, 4, 4, 6, 6, 8, 16, 18, 18, 20], "width": 97, "height": 91}}, "tail-shadow": {"raptor-tail-shadow": {"type": "mesh", "uvs": [1, 0.50387, 0.89276, 1, 0.82069, 0.96993, 0.72927, 0.92231, 0.64083, 0.87624, 0.54988, 0.83667, 0.47106, 0.80022, 0.40123, 0.7783, 0.32238, 0.75321, 0.25301, 0.73107, 0.20375, 0.71883, 0.11753, 0.71414, 0, 0.72519, 0, 0.66338, 0.10358, 0.57282, 0.18201, 0.5128, 0.23534, 0.47512, 0.30555, 0.4281, 0.37968, 0.37769, 0.44858, 0.3281, 0.51987, 0.2798, 0.61007, 0.21367, 0.70725, 0.14608, 0.80109, 0.08082, 0.90134, 0], "triangles": [10, 11, 14, 13, 14, 11, 10, 14, 15, 12, 13, 11, 9, 10, 15, 8, 9, 16, 9, 15, 16, 8, 16, 17, 7, 8, 17, 6, 7, 18, 7, 17, 18, 6, 18, 19, 5, 6, 19, 4, 20, 21, 4, 5, 20, 5, 19, 20, 2, 22, 23, 3, 21, 22, 4, 21, 3, 3, 22, 2, 23, 24, 0, 23, 0, 2, 1, 2, 0], "vertices": [1, 65, -0.16, 6.41, 1, 2, 65, 42.4, 61.67, 0.7548, 66, -28.13, 61.67, 0.2452, 2, 65, 69.28, 56.16, 0.53679, 66, -1.25, 56.16, 0.46321, 3, 65, 103.42, 48.48, 0.13235, 66, 32.89, 48.48, 0.82952, 67, -35.63, 49.98, 0.03813, 3, 65, 136.1, 39.06, 0.00439, 66, 65.57, 39.06, 0.62467, 67, -3.36, 39.23, 0.37094, 3, 66, 99.5, 32, 0.0995, 67, 30.26, 30.79, 0.87982, 68, -32.35, 31.34, 0.02068, 3, 66, 129.1, 26.76, 0.00046, 67, 59.61, 24.34, 0.57172, 68, -3.11, 24.4, 0.42782, 2, 67, 85.42, 18.44, 0.04275, 68, 22.59, 18.06, 0.95725, 2, 68, 51.63, 10.96, 0.64526, 69, -3.07, 10.89, 0.35474, 2, 68, 77.16, 4.61, 0.00246, 69, 22.59, 5.12, 0.99754, 2, 69, 40.97, 2.02, 0.84959, 70, -8.23, 2.08, 0.15041, 1, 70, 23.84, -2.64, 1, 1, 70, 68.09, -5.25, 1, 1, 70, 68.64, -7.05, 1, 1, 70, 29.23, -12.51, 1, 2, 69, 48.26, -18.17, 0.57427, 70, -1.07, -18.16, 0.42573, 1, 69, 27.9, -20.81, 1, 2, 68, 55.03, -24.11, 0.40024, 69, 1.11, -24.1, 0.59976, 3, 67, 90.24, -26.6, 0.00715, 68, 26.65, -27.06, 0.98709, 69, -27.19, -27.68, 0.00576, 2, 67, 63.89, -30.1, 0.5083, 68, 0.25, -30.11, 0.4917, 3, 66, 108.32, -33.03, 0.01005, 67, 36.41, -34.55, 0.9784, 68, -27.3, -34.09, 0.01155, 2, 66, 74.22, -38.09, 0.50429, 67, 2.13, -38.21, 0.49571, 3, 65, 107.88, -44.01, 0.04245, 66, 37.35, -44.01, 0.94684, 67, -34.96, -42.61, 0.01071, 2, 65, 72.14, -50.49, 0.52154, 66, 1.61, -50.49, 0.47846, 2, 65, 33.89, -58.82, 0.93522, 66, -36.64, -58.82, 0.06478], "hull": 25, "edges": [20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 20, 30, 32, 18, 20, 32, 18, 34, 32, 16, 18, 34, 16, 14, 36, 16, 14, 34, 36, 38, 36, 12, 14, 38, 12, 40, 38, 10, 12, 40, 10, 2, 4, 46, 4, 42, 8, 8, 10, 40, 42, 46, 44, 44, 42, 4, 6, 6, 8, 44, 6, 2, 0, 0, 48, 46, 48], "width": 377, "height": 126}}, "visor": {"visor": {"x": 99.13, "y": 6.51, "rotation": -70.58, "width": 261, "height": 168}}}}], "events": {"footstep": {}}, "animations": {"gun-grab": {"slots": {"front-hand": {"attachment": [{"name": "front-open-hand"}, {"time": 0.2333, "name": "gun"}]}, "gun": {"attachment": [{"time": 0.2333}]}}, "bones": {"front-hand2": {"rotate": [{"curve": [0.033, 0, 0.1, 12.34]}, {"time": 0.1333, "value": 12.34, "curve": [0.158, 12.34, 0.208, -89.55]}, {"time": 0.2333, "value": -89.55, "curve": [0.269, -89.03, 0.299, -89.03]}, {"time": 0.3333, "value": -79.79, "curve": [0.397, -62.87, 0.583, -10.18]}, {"time": 0.6667, "value": -10.18}], "scale": [{"curve": [0.058, 1, 0.175, 0.938, 0.058, 1, 0.175, 0.938]}, {"time": 0.2333, "x": 0.938, "y": 0.938, "curve": [0.342, 0.938, 0.558, 1, 0.342, 0.938, 0.558, 1]}, {"time": 0.6667}]}, "front-arm": {"rotate": [{"curve": [0.025, 0, 0.082, -21.08]}, {"time": 0.1, "value": -32, "curve": [0.15, -62.93, 0.213, -120.29]}, {"time": 0.2333, "value": -136.89, "curve": [0.29, -183.72, 0.308, -204.81]}, {"time": 0.3333, "value": -204.81, "curve": [0.383, -204.81, 0.479, -143.9]}, {"time": 0.5333, "value": -113.86, "curve": [0.563, -97.44, 0.633, -56.75]}, {"time": 0.6667, "value": -56.75}], "translate": [{"curve": [0.058, 0, 0.173, 4.7, 0.058, 0, 0.175, -2.66]}, {"time": 0.2333, "x": 5.85, "y": -2.66, "curve": [0.258, 6.3, 0.308, 6.84, 0.258, -2.66, 0.308, 4.8]}, {"time": 0.3333, "x": 6.84, "y": 4.8, "curve": [0.417, 6.84, 0.583, 0, 0.417, 4.8, 0.583, 0]}, {"time": 0.6667}]}, "front-bracer": {"rotate": [{"curve": [0.058, 0, 0.218, 76.7]}, {"time": 0.2333, "value": 86.02, "curve": [0.267, 106.51, 0.317, 114.95]}, {"time": 0.3333, "value": 114.95, "curve": [0.383, 114.95, 0.515, 89.58]}, {"time": 0.5333, "value": 81.86, "curve": [0.574, 64.66, 0.633, 34.74]}, {"time": 0.6667, "value": 34.74}]}}, "ik": {"spineboy-front-arm-ik": [{"mix": 0}]}}, "gun-holster": {"slots": {"front-hand": {"attachment": [{"name": "gun"}, {"time": 0.3, "name": "front-open-hand"}, {"time": 0.6667, "name": "front-hand"}]}, "gun": {"attachment": [{}, {"time": 0.3, "name": "gun-nohand"}]}}, "bones": {"front-hand2": {"rotate": [{"value": -10.18, "curve": [0.042, -10.18, 0.132, -79.17]}, {"time": 0.1667, "value": -84.76, "curve": [0.204, -90.76, 0.267, -89.52]}, {"time": 0.3, "value": -89.52, "curve": [0.342, -89.52, 0.411, -56.54]}, {"time": 0.4667, "value": -35.36, "curve": [0.507, -19.8, 0.617, 0.18]}, {"time": 0.6667, "value": 0.18}], "translate": [{"curve": [0.017, 0, 0.05, -1.82, 0.017, 0, 0.05, 0.11]}, {"time": 0.0667, "x": -1.82, "y": 0.11}], "scale": [{"curve": [0.075, 1, 0.225, 0.888, 0.075, 1, 0.225, 0.888]}, {"time": 0.3, "x": 0.888, "y": 0.888}]}, "front-arm": {"rotate": [{"value": -56.75, "curve": [0.042, -56.75, 0.104, -197.53]}, {"time": 0.1667, "value": -197.88, "curve": [0.23, -198.59, 0.267, -143.09]}, {"time": 0.3, "value": -143.09, "curve": [0.342, -143.09, 0.425, -159.79]}, {"time": 0.4667, "value": -159.79, "curve": [0.517, -159.79, 0.617, -25.24]}, {"time": 0.6667, "value": -25.24}]}, "front-bracer": {"rotate": [{"value": 34.74, "curve": [0.042, 34.74, 0.138, 83.37]}, {"time": 0.1667, "value": 90.01, "curve": [0.195, 96.76, 0.369, 112.84]}, {"time": 0.4, "value": 114.44, "curve": [0.422, 115.57, 0.45, 116.1]}, {"time": 0.4667, "value": 116.1}]}}, "ik": {"spineboy-front-arm-ik": [{"mix": 0, "curve": "stepped"}, {"time": 0.4667, "mix": 0}, {"time": 0.6667, "mix": 0.996}]}}, "jump": {"slots": {"mouth-smile": {"attachment": [{"time": 0.1333, "name": "mouth-grind"}, {"time": 0.9, "name": "mouth-smile"}]}}, "bones": {"front-foot-target": {"rotate": [{"time": 0.3, "curve": [0.325, 0, 0.393, -4.59]}, {"time": 0.4, "value": -6.98, "curve": [0.421, -14.42, 0.45, -69.67]}, {"time": 0.4667, "value": -69.67, "curve": [0.483, -69.67, 0.509, -21.2]}, {"time": 0.5333, "value": -12.81, "curve": [0.562, -2.84, 0.633, 5.74]}, {"time": 0.6667, "value": 5.74, "curve": [0.742, 5.74, 0.892, 0]}, {"time": 0.9667}], "translate": [{"x": -90.53, "y": 47.55, "curve": [0.015, -146.88, 0.075, -246.15, 0.036, 37.03, 0.075, 33.45]}, {"time": 0.1, "x": -246.15, "y": 33.45, "curve": [0.15, -246.15, 0.375, -246.15, 0.15, 33.45, 0.349, 33.63]}, {"time": 0.4, "x": -246.15, "y": 33.45, "curve": [0.413, -243.99, 0.447, -223.12, 0.413, 35.27, 0.46, 361.9]}, {"time": 0.4667, "x": -179.6, "y": 397.56, "curve": [0.484, -140.35, 0.517, 16.95, 0.477, 456.62, 0.496, 549.31]}, {"time": 0.5333, "x": 73.03, "y": 636.97, "curve": [0.557, 157.46, 0.606, 251.39, 0.56, 699.46, 0.633, 735.98]}, {"time": 0.6667, "x": 251.39, "y": 737.16, "curve": [0.796, 249.04, 0.947, 141, 0.81, 742.2, 0.947, 234.23]}, {"time": 0.9667, "x": 95.94, "y": 36.5}]}, "hip": {"rotate": [{"value": -4.48, "curve": [0.049, -9.72, 0.1, -13.95]}, {"time": 0.1333, "value": -13.95, "curve": [0.2, -13.95, 0.361, 5.8]}, {"time": 0.4, "value": 12.46, "curve": [0.438, 19.02, 0.513, 31.53]}, {"time": 0.6667, "value": 31.43, "curve": [0.821, 31.91, 0.951, 2.18]}, {"time": 0.9667, "value": -4.26, "curve": [0.982, -10.63, 1.013, -18.69]}, {"time": 1.0667, "value": -18.59, "curve": [1.108, -18.52, 1.292, 3.45]}, {"time": 1.3333, "value": 3.45, "curve": [1.367, 3.45, 1.445, 2.35]}, {"time": 1.5333, "value": -4.48}], "translate": [{"x": -47.56, "y": 48.49, "curve": [0.043, -69.43, 0.1, -79.91, 0.033, 48.49, 0.111, -39.5]}, {"time": 0.1333, "x": -79.91, "y": -55.85, "curve": [0.166, -79.91, 0.225, -76.38, 0.162, -76.85, 0.217, -91.17]}, {"time": 0.2667, "x": -52.47, "y": -92.76, "curve": [0.312, -26.39, 0.38, 56.36, 0.317, -94.39, 0.359, -58.96]}, {"time": 0.4, "x": 82.2, "y": 39.51, "curve": [0.426, 116.54, 0.532, 203.27, 0.43, 112.89, 0.475, 649.69]}, {"time": 0.6667, "x": 205.52, "y": 649.79, "curve": [0.775, 207.34, 0.857, 58.95, 0.879, 646.83, 0.926, 440.06]}, {"time": 0.9667, "x": 59.67, "y": 161.1, "curve": [1.026, 60.06, 1.041, 72.9, 0.982, 53.87, 1.004, -91.4]}, {"time": 1.0667, "x": 91.3, "y": -91.6, "curve": [1.083, 103.05, 1.246, 238.62, 1.207, -92.04, 1.234, 76.13]}, {"time": 1.3333, "x": 238.12, "y": 75.68, "curve": [1.408, 237.69, 1.5, 213.2, 1.41, 75.33, 1.497, 49.77]}, {"time": 1.5333, "x": 213.2, "y": 49.77}]}, "back-foot-target": {"rotate": [{"time": 0.3, "curve": [0.325, 0, 0.386, -31.84]}, {"time": 0.4, "value": -41.64, "curve": [0.42, -55.3, 0.458, -86.03]}, {"time": 0.4667, "value": -86.03, "curve": [0.475, -86.03, 0.515, -62.63]}, {"time": 0.5333, "value": -57.97, "curve": [0.645, -29.13, 1.025, -7.79]}, {"time": 1.0333, "value": -7.79}], "translate": [{"x": 99.37, "curve": "stepped"}, {"time": 0.3, "x": 99.37, "curve": [0.352, 97.71, 0.349, 85.15, 0.4, 0, 0.3, 0]}, {"time": 0.4, "x": 83.35, "curve": [0.412, 83.24, 0.424, 87.02, 0.411, 0.2, 0.415, -7.91]}, {"time": 0.4333, "x": 92.07, "y": -9.73, "curve": [0.451, 100.78, 0.463, 124.21, 0.449, 27.5, 0.462, 103.35]}, {"time": 0.4667, "x": 132.33, "y": 119.67, "curve": [0.476, 153.34, 0.517, 239.65, 0.476, 151.52, 0.518, 382.69]}, {"time": 0.5333, "x": 267.51, "y": 435.87, "curve": [0.553, 302.61, 0.632, 352.21, 0.557, 517.14, 0.645, 683.92]}, {"time": 0.6667, "x": 352.52, "y": 702.46, "curve": [0.747, 353.24, 0.797, 342.85, 0.745, 768.64, 0.789, 768.68]}, {"time": 0.8333, "x": 322.4, "y": 717.67, "curve": [0.866, 303.67, 0.932, 224.25, 0.865, 681.89, 0.936, 422.05]}, {"time": 0.9667, "x": 220.5, "y": 293.73, "curve": [0.989, 218.13, 1.009, 314.6, 0.987, 209.37, 1.024, 79.62]}, {"time": 1.0333, "x": 318.98}]}, "front-leg-target": {"translate": [{"curve": [0.025, 0, 0.075, -33.09, 0.025, 0, 0.075, -31.34]}, {"time": 0.1, "x": -33.09, "y": -31.34, "curve": [0.175, -33.09, 0.325, 140.91, 0.175, -31.34, 0.325, 51.55]}, {"time": 0.4, "x": 140.91, "y": 51.55, "curve": [0.434, 140.58, 0.421, 10.15, 0.435, 50.46, 0.45, 16]}, {"time": 0.4667, "x": -11.12, "y": 4.78, "curve": [0.501, -25.03, 0.586, -45.12, 0.511, -24.72, 0.56, -38.69]}, {"time": 0.6667, "x": -46.38, "y": -40.57, "curve": [0.74, -46.38, 0.923, -1.75, 0.74, -40.57, 0.896, 22.3]}, {"time": 0.9667, "x": -1.72, "y": 20.96, "curve": [0.993, -1.71, 0.993, -37.51, 0.995, 20.41, 0.954, -37.81]}, {"time": 1.1, "x": -38.27, "y": -35.93, "curve": [1.181, -38.85, 1.252, 44.94, 1.184, -34.84, 1.252, 5.48]}, {"time": 1.3333, "x": 44.94, "y": 5.48, "curve": [1.383, 44.94, 1.452, 0, 1.383, 5.48, 1.452, 0]}, {"time": 1.5333}]}, "back-leg-target": {"translate": [{"curve": [0.025, 0, 0.075, -35.37, 0.025, 0, 0.075, -16.42]}, {"time": 0.1, "x": -35.37, "y": -16.42, "curve": [0.141, -35.37, 0.205, -14.12, 0.141, -16.42, 0.214, 4.84]}, {"time": 0.2667, "x": -4.49, "y": 4.24, "curve": [0.317, 3.38, 0.366, 12.04, 0.319, 3.65, 0.375, -33.42]}, {"time": 0.4, "x": 12.04, "y": -42.73, "curve": [0.417, 12.04, 0.45, -46.17, 0.413, -47.43, 0.454, -76.29]}, {"time": 0.4667, "x": -46.17, "y": -81.12, "curve": [0.475, -46.17, 0.525, -38.36, 0.499, -93.24, 0.525, -96.11]}, {"time": 0.5333, "x": -38.36, "y": -96.11, "curve": [0.567, -38.36, 0.633, -55.58, 0.567, -96.11, 0.643, -67.83]}, {"time": 0.6667, "x": -55.58, "y": -63.06, "curve": [0.75, -55.58, 0.907, -56.88, 0.736, -49.33, 0.921, -52.06]}, {"time": 1, "x": -43, "y": -42.05, "curve": [1.076, -31.56, 1.101, -19.95, 1.084, -31.37, 1.125, -4.64]}, {"time": 1.1333, "x": -12.99, "y": -3.97, "curve": [1.198, 0.98, 1.233, 0, 1.173, -0.72, 1.233, 0]}, {"time": 1.2667}]}, "tail1": {"rotate": [{"curve": [0.033, -0.73, 0.182, -1.37]}, {"time": 0.2333, "value": -0.68, "curve": [0.324, 0.55, 0.378, 4.7]}, {"time": 0.4, "value": 6.15, "curve": [0.449, 9.36, 0.523, 12.03]}, {"time": 0.5667, "value": 12.05, "curve": [0.704, 12.09, 0.764, -9.79]}, {"time": 0.9333, "value": -9.74, "curve": [0.984, -9.73, 1.054, -9.25]}, {"time": 1.1, "value": -7.09, "curve": [1.173, -3.67, 1.279, 7.71]}, {"time": 1.3333, "value": 7.67, "curve": [1.407, 7.63, 1.491, 0]}, {"time": 1.5333}]}, "tail3": {"rotate": [{"curve": [0.143, -0.06, 0.212, -21.95]}, {"time": 0.2333, "value": -24.08, "curve": [0.258, -26.54, 0.283, -26.63]}, {"time": 0.3, "value": -26.63, "curve": [0.325, -26.63, 0.365, -22.41]}, {"time": 0.4, "value": -17.42, "curve": [0.463, -8.36, 0.658, 0.68]}, {"time": 0.7667, "value": 0.99, "curve": [0.839, 1.2, 0.911, -6.88]}, {"time": 0.9333, "value": -7.95, "curve": [0.999, -11.08, 1.101, -12.03]}, {"time": 1.1667, "value": -11.94, "curve": [1.233, -11.85, 1.317, -8.44]}, {"time": 1.3333, "value": -7.62, "curve": [1.4, -4.31, 1.483, 0]}, {"time": 1.5333}]}, "torso2": {"rotate": [{"curve": [0.076, 2.18, 0.15, 3.87]}, {"time": 0.2, "value": 3.87, "curve": [0.25, 3.87, 0.317, -14.55]}, {"time": 0.3667, "value": -14.55, "curve": [0.433, -14.55, 0.549, -7.29]}, {"time": 0.6667, "value": -0.64, "curve": [0.725, 2.66, 0.883, 10.9]}, {"time": 0.9667, "value": 10.9, "curve": [1.095, 10.9, 1.185, -6.18]}, {"time": 1.2667, "value": -6.04, "curve": [1.38, -5.86, 1.471, -2.78]}, {"time": 1.5333}], "translate": [{"curve": [0.05, 0, 0.105, 6.29, 0.05, 0, 0.15, -22.92]}, {"time": 0.2, "x": 9.67, "y": -22.92, "curve": [0.417, 17.38, 0.775, 24.08, 0.392, -22.92, 0.806, 3.87]}, {"time": 0.9667, "x": 24.08, "y": 4.46, "curve": [1.012, 24.08, 1.071, 23.76, 1.022, 4.66, 1.077, -8.33]}, {"time": 1.1333, "x": 20.46, "y": -8.46, "curve": [1.221, 16.02, 1.317, 10.46, 1.21, -8.64, 1.352, 2.35]}, {"time": 1.4, "x": 5.93, "y": 2.27, "curve": [1.451, 3.19, 1.497, 0, 1.468, 2.16, 1.494, 0]}, {"time": 1.5333}]}, "front-arm1": {"rotate": [{"curve": [0.067, 0, 0.2, 51.21]}, {"time": 0.2667, "value": 51.21, "curve": [0.325, 51.21, 0.442, -38.7]}, {"time": 0.5, "value": -38.7, "curve": [0.567, -38.7, 0.706, 24.96]}, {"time": 0.7667, "value": 38.01, "curve": [0.854, 56.01, 0.911, 62.19]}, {"time": 1, "value": 62.19, "curve": [1.084, 62.19, 1.192, -14.43]}, {"time": 1.2333, "value": -14.43, "curve": [1.292, -14.43, 1.408, 0]}, {"time": 1.4667}]}, "neck": {"rotate": [{"curve": [0.053, 0, 0.169, -1.43]}, {"time": 0.2, "value": -2.08, "curve": [0.272, -3.58, 0.329, -4.44]}, {"time": 0.4, "value": -4.48, "curve": [0.473, -4.51, 0.616, -2.46]}, {"time": 0.6667, "value": -1.01, "curve": [0.728, 0.75, 0.881, 5.85]}, {"time": 0.9667, "value": 5.85, "curve": [1.04, 5.86, 1.17, -1.69]}, {"time": 1.2667, "value": -1.79, "curve": [1.317, -1.84, 1.483, 0]}, {"time": 1.5333}], "translate": [{"curve": [0.042, -1.88, 0.137, -2.9, 0.058, 3.23, 0.133, 7.83]}, {"time": 0.2, "x": -2.93, "y": 7.91, "curve": [0.262, -2.97, 0.337, 1.35, 0.262, 7.98, 0.333, -17.63]}, {"time": 0.4, "x": 5.6, "y": -17.63, "curve": [0.501, 12.45, 0.612, 22.88, 0.467, -17.63, 0.619, -5.42]}, {"time": 0.6667, "x": 25.24, "y": -2.9, "curve": [0.752, 28.94, 0.851, 31.66, 0.775, 2.84, 0.883, 5.36]}, {"time": 0.9667, "x": 31.78, "y": 5.36, "curve": [1.083, 31.89, 1.209, 25.93, 1.068, 5.52, 1.169, -13.52]}, {"time": 1.2667, "x": 22.31, "y": -13.24, "curve": [1.338, 17.8, 1.432, 11.29, 1.345, -13.01, 1.467, 0]}, {"time": 1.5333}]}, "back-arm1": {"rotate": [{"curve": [0.033, 0, 0.1, 41.83]}, {"time": 0.1333, "value": 41.83, "curve": [0.233, 41.83, 0.433, -19.76]}, {"time": 0.5333, "value": -19.76, "curve": [0.617, -19.76, 0.813, 7.86]}, {"time": 0.8667, "value": 16.31, "curve": [0.922, 25.06, 0.992, 39.62]}, {"time": 1.0333, "value": 39.62, "curve": [1.067, 39.62, 1.134, 36.98]}, {"time": 1.1667, "value": 21.98, "curve": [1.184, 13.73, 1.242, -14.43]}, {"time": 1.2667, "value": -14.43, "curve": [1.317, -14.43, 1.417, 0]}, {"time": 1.4667}]}, "spineboy-hip": {"translate": [{"curve": [0.033, 0, 0.071, 12.38, 0.033, 0, 0.099, 6.02]}, {"time": 0.1333, "x": 18.47, "y": 5.61, "curve": [0.183, 23.25, 0.285, 23.09, 0.199, 4.82, 0.308, -11.17]}, {"time": 0.3333, "x": 24.3, "y": -23.55, "curve": [0.439, 26.95, 0.553, 40.03, 0.394, -53.54, 0.573, -55.54]}, {"time": 0.6667, "x": 51.3, "y": -55.2, "curve": [0.741, 58.64, 0.905, 68.29, 0.853, -54.53, 0.939, -42.26]}, {"time": 1, "x": 67.68, "y": -37.87, "curve": [1.071, 67.22, 1.216, 14.12, 1.055, -33.92, 1.171, -3.34]}, {"time": 1.2667, "x": 10.39, "y": -1.83, "curve": [1.376, 2.35, 1.467, 0, 1.338, -0.69, 1.467, 0]}, {"time": 1.5333}]}, "tail5": {"rotate": [{"value": 6.72, "curve": [0.018, 6.72, 0.077, 8.48]}, {"time": 0.1333, "value": 5.57, "curve": [0.214, 1.41, 0.249, -14.58]}, {"time": 0.3, "value": -14.58, "curve": [0.341, -14.52, 0.36, -12.87]}, {"time": 0.4, "value": -10.37, "curve": [0.466, -6.22, 0.481, 6.11]}, {"time": 0.7667, "value": 11.47, "curve": [0.831, 12.69, 0.857, 12.88]}, {"time": 0.9333, "value": 12.89, "curve": [1.073, 12.92, 1.137, -5.02]}, {"time": 1.1667, "value": -10.52, "curve": [1.189, -14.81, 1.242, -16.26]}, {"time": 1.2667, "value": -16.26, "curve": [1.292, -16.26, 1.344, -10.57]}, {"time": 1.3667, "value": -7.39, "curve": [1.387, -4.51, 1.468, -0.3]}, {"time": 1.5333}]}, "front-arm2": {"rotate": [{"curve": [0.033, 0, 0.1, 18.42]}, {"time": 0.1333, "value": 18.42, "curve": [0.225, 18.42, 0.408, -58.26]}, {"time": 0.5, "value": -58.26, "curve": [0.567, -58.26, 0.702, -38.97]}, {"time": 0.7667, "value": -16.61, "curve": [0.821, 2.07, 0.967, 12.73]}, {"time": 1.0667, "value": 13.08, "curve": [1.108, 13.23, 1.192, -56.15]}, {"time": 1.2333, "value": -56.15, "curve": [1.292, -56.15, 1.356, -0.71]}, {"time": 1.4667}]}, "gun": {"rotate": [{}, {"time": 0.1333, "value": 15.28}, {"time": 0.4, "value": -53.41}, {"time": 0.7667, "value": -63.35}, {"time": 1.0667, "value": -29.92}, {"time": 1.3, "value": 7.24}, {"time": 1.4, "value": -3.7}, {"time": 1.4667}]}, "head": {"rotate": [{"curve": [0.035, -0.03, 0.069, 8.8]}, {"time": 0.1, "value": 9.41, "curve": [0.141, 10.24, 0.189, 4.37]}, {"time": 0.2, "value": 3.23, "curve": [0.224, 0.67, 0.369, -14.75]}, {"time": 0.4, "value": -19.24, "curve": [0.441, -25.21, 0.498, -33.84]}, {"time": 0.5333, "value": -33.74, "curve": [0.581, -33.61, 0.614, -28.7]}, {"time": 0.6667, "value": -28.63, "curve": [0.73, -28.55, 0.809, -29.54]}, {"time": 0.9, "value": -29.94, "curve": [0.948, -30.15, 0.967, -4.31]}, {"time": 1, "value": -3.74, "curve": [1.032, -3.18, 1.04, -9.87]}, {"time": 1.0667, "value": -9.83, "curve": [1.094, -9.79, 1.157, 0.42]}, {"time": 1.2, "value": 0.36, "curve": [1.237, 0.31, 1.249, -5.16]}, {"time": 1.2667, "value": -5.16, "curve": [1.292, -5.16, 1.351, 3.76]}, {"time": 1.4, "value": 3.9, "curve": [1.44, 4.01, 1.509, 0]}, {"time": 1.5333}], "translate": [{"curve": [0.05, 0, 0.15, 1.7, 0.05, 0, 0.15, -35.74]}, {"time": 0.2, "x": 2.52, "y": -35.74, "curve": [0.264, 3.57, 0.342, 10.41, 0.258, -35.74, 0.342, -18.19]}, {"time": 0.4, "x": 10.41, "y": -18.19, "curve": [0.458, 10.41, 0.608, 26.32, 0.458, -18.19, 0.608, -20.79]}, {"time": 0.6667, "x": 26.32, "y": -20.79, "curve": [0.75, 26.32, 0.917, 19.04, 0.75, -20.79, 0.917, 1.7]}, {"time": 1, "x": 19.04, "y": 1.7}], "scale": [{"curve": [0.05, 1, 0.15, 0.985, 0.05, 1, 0.15, 1.049]}, {"time": 0.2, "x": 0.985, "y": 1.049, "curve": [0.233, 0.985, 0.3, 1, 0.233, 1.049, 0.3, 1]}, {"time": 0.3333, "curve": [0.375, 1, 0.458, 1.066, 0.375, 1, 0.458, 0.945]}, {"time": 0.5, "x": 1.066, "y": 0.945, "curve": [0.603, 1.066, 0.7, 0.987, 0.639, 0.945, 0.817, 1.049]}, {"time": 0.8667, "x": 0.985, "y": 1.049, "curve": [0.878, 0.985, 0.958, 1.066, 0.917, 1.049, 0.958, 0.945]}, {"time": 1, "x": 1.066, "y": 0.945, "curve": [1.045, 1.065, 1.061, 0.986, 1.05, 0.944, 1.058, 1.048]}, {"time": 1.1, "x": 0.985, "y": 1.049, "curve": [1.189, 0.983, 1.363, 1, 1.119, 1.048, 1.363, 1]}, {"time": 1.5333}]}, "back-arm2": {"rotate": [{"curve": [0.033, 0, 0.124, -25.32]}, {"time": 0.1333, "value": -28.29, "curve": [0.178, -42.54, 0.408, -75.93]}, {"time": 0.5, "value": -75.93, "curve": [0.567, -75.93, 0.722, -61.84]}, {"time": 0.7667, "value": -57.21, "curve": [0.834, -50.18, 0.992, -38.17]}, {"time": 1.0667, "value": -38.17, "curve": [1.108, -38.17, 1.177, -49.68]}, {"time": 1.2667, "value": -49.36, "curve": [1.349, -49.07, 1.475, 0]}, {"time": 1.5333}]}, "spineboy-torso": {"rotate": [{"value": -4.78, "curve": [0.033, -4.78, 0.088, -8.83]}, {"time": 0.1333, "value": -13.36, "curve": [0.179, -17.9, 0.242, -26.07]}, {"time": 0.2667, "value": -31.39, "curve": [0.31, -40.56, 0.382, -52.73]}, {"time": 0.4667, "value": -52.79, "curve": [0.574, -52.87, 0.615, -44.38]}, {"time": 0.6667, "value": -36.05, "curve": [0.71, -29.14, 0.917, 9.18]}, {"time": 1, "value": 9.18, "curve": [1.058, 9.18, 1.163, -29.03]}, {"time": 1.2333, "value": -29.37, "curve": [1.275, -29.37, 1.377, -9.74]}, {"time": 1.4, "value": -7.98, "curve": [1.443, -4.61, 1.517, -1.69]}, {"time": 1.5333, "value": -1.69}]}, "tail7": {"rotate": [{"value": 6.72, "curve": [0.049, 6.72, 0.193, 20.19]}, {"time": 0.2333, "value": 20.19, "curve": [0.25, 20.19, 0.325, -13.06]}, {"time": 0.4, "value": -13.06, "curve": [0.433, -13.06, 0.484, -9.84]}, {"time": 0.5333, "value": -5.93, "curve": [0.586, -1.74, 0.822, 16.83]}, {"time": 0.9333, "value": 16.85, "curve": [0.992, 16.86, 1.035, 12.25]}, {"time": 1.0667, "value": 7.16, "curve": [1.129, -2.72, 1.166, -16.97]}, {"time": 1.2, "value": -16.51, "curve": [1.273, -16.51, 1.319, -13.06]}, {"time": 1.3667, "value": -8.95, "curve": [1.421, -4.2, 1.508, 0]}, {"time": 1.5333}]}, "front-foot2": {"rotate": [{"time": 0.4, "curve": [0.403, -10.05, 0.416, -43.77]}, {"time": 0.4333, "value": -53.55, "curve": [0.469, -73.71, 0.522, -67.11]}, {"time": 0.5333, "value": -67.11, "curve": [0.592, -68.53, 0.613, 20.61]}, {"time": 0.9333, "value": 24.85, "curve": [0.958, 24.85, 0.989, 20.15]}, {"time": 1}]}, "front-hand": {"rotate": [{"curve": [0.076, 0, 0.185, -8.93]}, {"time": 0.3, "value": -8.94, "curve": [0.333, -8.94, 0.372, 38.13]}, {"time": 0.4333, "value": 38.11, "curve": [0.516, 38.08, 0.653, -22.01]}, {"time": 0.6667, "value": -27.75, "curve": [0.693, -38.81, 0.75, -52.48]}, {"time": 0.8, "value": -52.38, "curve": [0.896, -52.18, 1.052, 10.93]}, {"time": 1.0667, "value": 16.08, "curve": [1.088, 23.55, 1.152, 45.76]}, {"time": 1.2, "value": 46.03, "curve": [1.252, 46.32, 1.27, -12.74]}, {"time": 1.3, "value": -12.68, "curve": [1.373, -12.53, 1.424, 0]}, {"time": 1.4667}]}, "jaw": {"rotate": [{"value": 0.83}, {"time": 0.1333, "value": 6.62}, {"time": 0.3333, "value": -3.98}, {"time": 0.4667, "value": -14.79, "curve": [0.731, -14.76, 0.792, 3.22]}, {"time": 0.9, "value": 3.22}, {"time": 0.9333, "value": -3.14}, {"time": 1.0667, "value": 12.08}, {"time": 1.2333, "value": -8.41}, {"time": 1.5333, "value": 0.83}], "translate": [{"x": -10.21, "y": 13.96, "curve": [0.041, -10.21, 0.086, -23.53, 0.041, 13.96, 0.086, -6.69]}, {"time": 0.1333, "x": -23.53, "y": -6.69, "curve": [0.233, -23.53, 0.347, -43.29, 0.233, -6.69, 0.347, 2.9]}, {"time": 0.4667, "x": -42.09, "y": 4.75, "curve": [0.607, -40.69, 0.757, -33.92, 0.607, 6.92, 0.757, -13.55]}, {"time": 0.9, "x": -28.39, "y": -12.1, "curve": [1.016, -24, 1.13, -30.31, 1.016, -10.94, 1.13, 3.19]}, {"time": 1.2333, "x": -26.95, "y": 9.17, "curve": [1.347, -23.32, 1.449, -10.21, 1.347, 15.64, 1.449, 13.96]}, {"time": 1.5333, "x": -10.21, "y": 13.96}]}, "back-foot2": {"rotate": [{"time": 0.4333, "curve": [0.442, 0, 0.454, -46.1]}, {"time": 0.4667, "value": -60.96, "curve": [0.475, -70.47, 0.517, -76.49]}, {"time": 0.5333, "value": -76.49, "curve": [0.567, -76.49, 0.643, -68.96]}, {"time": 0.6667, "value": -63.79, "curve": [0.712, -53.71, 0.866, -1.7]}, {"time": 0.9333, "value": 16.34, "curve": [0.944, 19.2, 0.983, 24.85]}, {"time": 1, "value": 24.85, "curve": [1.017, 24.85, 1.067, 11.07]}, {"time": 1.0667}]}, "back-hand": {"rotate": [{"curve": [0.046, 0, 0.017, 36.31]}, {"time": 0.1667, "value": 36.26, "curve": [0.3, 36.22, 0.535, 24.33]}, {"time": 0.6667, "value": 24.22, "curve": [0.769, 24.27, 0.907, 26.83]}, {"time": 0.9667, "value": 31.87, "curve": [1.024, 36.68, 1.148, 55.22]}, {"time": 1.2, "value": 55.2, "curve": [1.296, 55.16, 1.469, 0]}, {"time": 1.5333}]}, "tail9": {"rotate": [{"value": 6.72, "curve": [0.04, 6.72, 0.185, 21.12]}, {"time": 0.2333, "value": 21.25, "curve": [0.27, 21.35, 0.284, 17.74]}, {"time": 0.3, "value": 14.56, "curve": [0.322, 10.26, 0.375, -13.06]}, {"time": 0.4, "value": -13.06, "curve": [0.433, -13.06, 0.51, -11.64]}, {"time": 0.5333, "value": -7.88, "curve": [0.579, -0.37, 0.741, 21.14]}, {"time": 0.7667, "value": 23.25, "curve": [0.819, 27.63, 0.865, 31.03]}, {"time": 0.9333, "value": 31.09, "curve": [0.975, 31.12, 1.034, 24.27]}, {"time": 1.0667, "value": 16.48, "curve": [1.121, 3.62, 1.146, -4.68]}, {"time": 1.1667, "value": -10.43, "curve": [1.185, -15.54, 1.242, -22.43]}, {"time": 1.2667, "value": -22.43, "curve": [1.292, -22.43, 1.338, -20.95]}, {"time": 1.3667, "value": -17.59, "curve": [1.412, -12.34, 1.508, 0]}, {"time": 1.5333}]}, "front-foot3": {"rotate": [{"time": 0.4, "curve": [0.415, 0.16, 0.423, -3.39]}, {"time": 0.4333, "value": -8.31, "curve": [0.438, -10.62, 0.486, -19.18]}, {"time": 0.5333, "value": -19.17, "curve": [0.581, -19.16, 0.637, -15.86]}, {"time": 0.6667, "value": -13.75, "curve": [0.743, -8.29, 0.858, 10.78]}, {"time": 0.9333, "value": 10.78, "curve": [0.958, 10.78, 1.022, 10.58]}, {"time": 1.0333, "value": 0.34}]}, "head2": {"rotate": [{"value": 18.08, "curve": [0.033, 18.08, 0.108, 24.73]}, {"time": 0.1333, "value": 25.81, "curve": [0.206, 28.87, 0.258, 29.66]}, {"time": 0.3, "value": 29.66, "curve": [0.325, 29.66, 0.387, 27.38]}, {"time": 0.4, "value": 26.31, "curve": [0.488, 19.31, 0.606, 9.53]}, {"time": 0.6333, "value": 7.4, "curve": [0.681, 3.72, 0.761, -1.99]}, {"time": 0.8333, "value": -1.98, "curve": [0.888, -1.97, 0.944, 1.29]}, {"time": 0.9667, "value": 3.21, "curve": [1.005, 6.46, 1.019, 15.11]}, {"time": 1.0667, "value": 15.13, "curve": [1.125, 15.15, 1.204, 13.96]}, {"time": 1.2333, "value": 12.87, "curve": [1.271, 11.48, 1.275, 9.98]}, {"time": 1.3333, "value": 7.77, "curve": [1.394, 5.46, 1.393, 5.9]}, {"time": 1.4333, "value": 5.86, "curve": [1.481, 5.81, 1.517, 8.93]}, {"time": 1.5333, "value": 8.93}], "scale": [{"time": 0.9, "curve": [0.928, 1, 0.963, 1.056, 0.928, 1, 0.963, 0.978]}, {"time": 1, "x": 1.056, "y": 0.978, "curve": [1.052, 1.056, 1.11, 0.951, 1.052, 0.978, 1.11, 0.997]}, {"time": 1.1667, "x": 0.942, "y": 1.001, "curve": [1.267, 0.926, 1.367, 1, 1.267, 1.008, 1.367, 1]}, {"time": 1.4333}]}, "neck2": {"rotate": [{"value": -0.77, "curve": [0.033, -0.77, 0.1, 15.96]}, {"time": 0.1333, "value": 15.96, "curve": [0.175, 15.96, 0.269, 11.97]}, {"time": 0.3, "value": 10.07, "curve": [0.34, 7.56, 0.375, 4.11]}, {"time": 0.4, "value": 4.05, "curve": [0.468, 3.91, 0.575, 8.43]}, {"time": 0.6333, "value": 8.48, "curve": [0.73, 8.58, 0.883, -2.77]}, {"time": 0.9667, "value": -2.77, "curve": [0.992, -2.77, 1.047, 3.79]}, {"time": 1.0667, "value": 5.45, "curve": [1.094, 7.73, 1.192, 10.76]}, {"time": 1.2333, "value": 10.76, "curve": [1.258, 10.76, 1.316, 7.65]}, {"time": 1.3667, "value": 7.6, "curve": [1.417, 7.56, 1.5, 12.24]}, {"time": 1.5333, "value": 12.24}]}, "spineboy-front-arm-target": {"translate": [{"x": -0.43, "y": -9.01}, {"time": 0.5}, {"time": 0.9667, "x": -6.56, "y": 0.7}, {"time": 1.0667, "x": 12.25, "y": -29.51}]}, "front-hand2": {"rotate": [{}, {"time": 0.1333, "value": -22.27}, {"time": 0.2667, "value": -16.91}, {"time": 0.4333, "value": -2.22}, {"time": 0.6667, "value": -6.95}]}, "stirrup": {"rotate": [{}, {"time": 0.3, "value": -13.39, "curve": "stepped"}, {"time": 0.9667, "value": -13.39}, {"time": 1.3333}]}, "spineboy-front-foot-target": {"translate": [{"time": 0.2667}, {"time": 0.4333, "x": 19.72, "y": -2.18}, {"time": 0.5333, "x": 19.39, "y": -3.07}, {"time": 0.6667, "x": -3.87, "y": 6.01}, {"time": 1.0667, "x": -10.92, "y": 4.87}, {"time": 1.4667}]}, "tail2": {"rotate": [{"curve": [0.031, 0, 0.085, -0.3]}, {"time": 0.1333, "value": -0.88, "curve": [0.22, -1.88, 0.255, -7.43]}, {"time": 0.3, "value": -7.43, "curve": [0.325, -7.43, 0.366, -7.05]}, {"time": 0.4, "value": -5.89, "curve": [0.434, -4.73, 0.506, 1.38]}, {"time": 0.5333, "value": 1.75, "curve": [0.582, 2.41, 0.637, 3.11]}, {"time": 0.7333, "value": 3.14, "curve": [0.814, 3.16, 0.903, 0.42]}, {"time": 0.9333, "value": -0.83, "curve": [0.959, -1.87, 1.106, -7.13]}, {"time": 1.1667, "value": -7.07, "curve": [1.206, -7.03, 1.308, 4.72]}, {"time": 1.4, "value": 4.89, "curve": [1.452, 4.98, 1.501, -6.46]}, {"time": 1.5333, "value": -6.46}]}, "tail4": {"rotate": [{"value": 6.72, "curve": [0.018, 6.72, 0.072, 8.45]}, {"time": 0.1333, "value": 8.08, "curve": [0.257, 7.33, 0.161, -3.27]}, {"time": 0.3, "value": -3.31, "curve": [0.455, -3.35, 0.599, 7.47]}, {"time": 0.7667, "value": 11.95, "curve": [0.804, 12.94, 0.861, 14.26]}, {"time": 0.9333, "value": 14.35, "curve": [0.963, 14.39, 0.996, 14.41]}, {"time": 1.0333, "value": 6.04, "curve": [1.072, -2.56, 1.116, -11.42]}, {"time": 1.1667, "value": -11.4, "curve": [1.238, -11.4, 1.318, -6.95]}, {"time": 1.3333, "value": -5.65, "curve": [1.364, -3.08, 1.508, 3.65]}, {"time": 1.5333, "value": 3.65}]}, "tail6": {"rotate": [{"value": 6.72, "curve": [0.017, 6.72, 0.079, 14.92]}, {"time": 0.1333, "value": 14.56, "curve": [0.186, 14.23, 0.228, 8.64]}, {"time": 0.2333, "value": 6.59, "curve": [0.245, 1.79, 0.303, -11.91]}, {"time": 0.4, "value": -11.91, "curve": [0.474, -11.91, 0.702, 8.29]}, {"time": 0.7667, "value": 12.95, "curve": [0.803, 15.57, 0.845, 18.42]}, {"time": 0.9333, "value": 18.64, "curve": [1.043, 18.9, 1.08, -11.4]}, {"time": 1.1667, "value": -11.61, "curve": [1.236, -11.44, 1.29, -10.99]}, {"time": 1.3333, "value": -8.88, "curve": [1.423, -4.58, 1.472, 2.97]}, {"time": 1.5333, "value": 3.69}]}, "tail8": {"rotate": [{"value": 6.72, "curve": [0.018, 6.72, 0.179, 17.14]}, {"time": 0.2333, "value": 17.13, "curve": [0.257, 17.2, 0.288, 11.35]}, {"time": 0.3, "value": 9.01, "curve": [0.318, 5.47, 0.375, -14.47]}, {"time": 0.4, "value": -14.47, "curve": [0.433, -14.47, 0.492, -12.37]}, {"time": 0.5333, "value": -9.76, "curve": [0.575, -6.73, 0.732, 7.06]}, {"time": 0.7667, "value": 9.18, "curve": [0.809, 11.79, 0.85, 14.28]}, {"time": 0.9333, "value": 14.4, "curve": [0.98, 14.46, 1.027, 12.95]}, {"time": 1.0667, "value": 6.47, "curve": [1.112, -0.74, 1.148, -13.08]}, {"time": 1.1667, "value": -14.91, "curve": [1.215, -19.6, 1.259, -23.09]}, {"time": 1.3, "value": -23.17, "curve": [1.376, -23.24, 1.508, -11.89]}, {"time": 1.5333, "value": -11.89}]}, "tail10": {"rotate": [{"value": 6.72, "curve": [0.038, 6.72, 0.199, 10.82]}, {"time": 0.2333, "value": 10.93, "curve": [0.257, 11, 0.276, 11.07]}, {"time": 0.3, "value": 8.71, "curve": [0.335, 5.33, 0.37, -8.93]}, {"time": 0.4, "value": -12.6, "curve": [0.425, -15.68, 0.5, -18.92]}, {"time": 0.5333, "value": -18.92, "curve": [0.549, -18.92, 0.651, 2.65]}, {"time": 0.7, "value": 9.59, "curve": [0.725, 13.09, 0.791, 16.42]}, {"time": 0.8333, "value": 16.82, "curve": [0.956, 17.98, 1.051, 10.26]}, {"time": 1.0667, "value": 7.38, "curve": [1.098, 1.71, 1.13, -23.72]}, {"time": 1.1667, "value": -26.24, "curve": [1.199, -28.46, 1.242, -29.22]}, {"time": 1.2667, "value": -29.22, "curve": [1.292, -29.22, 1.353, -18.67]}, {"time": 1.3667, "value": -17.84, "curve": [1.408, -15.28, 1.492, -11.19]}, {"time": 1.5333, "value": -11.19}]}, "saddle-strap-back": {"rotate": [{"value": -13.92, "curve": [0.119, -14.07, 0.185, -17.29]}, {"time": 0.2667, "value": -17.26, "curve": [0.326, -17.24, 0.467, 1.36]}, {"time": 0.5333, "value": 1.36, "curve": [0.583, 1.36, 0.686, 0.58]}, {"time": 0.7333, "value": -2.1, "curve": [0.773, -4.34, 0.883, -27.93]}, {"time": 0.9333, "value": -27.93, "curve": [1, -27.93, 1.128, -14.6]}, {"time": 1.2, "value": -13.79, "curve": [1.305, -12.61, 1.352, -12.27]}, {"time": 1.4333, "value": -12.07, "curve": [1.48, -11.96, 1.508, -14.43]}, {"time": 1.5333, "value": -14.43}]}, "back-leg1": {"translate": [{}, {"time": 0.4667, "x": -12.67, "y": -22.45}, {"time": 0.9}]}, "bone3": {"rotate": [{"curve": [0.025, 0, 0.075, -6.29]}, {"time": 0.1, "value": -6.29, "curve": [0.142, -6.29, 0.225, 14.19]}, {"time": 0.2667, "value": 14.19, "curve": [0.283, 14.19, 0.317, 5.37]}, {"time": 0.3333, "value": 5.37, "curve": [0.375, 5.37, 0.458, 17.56]}, {"time": 0.5, "value": 17.56, "curve": [0.567, 17.56, 0.745, 7.05]}, {"time": 0.7667, "value": 5.59, "curve": [0.792, 3.95, 0.944, -7.5]}, {"time": 0.9667, "value": -8.54, "curve": [0.99, -9.6, 1.017, -10.01]}, {"time": 1.0333, "value": -10.01, "curve": [1.067, -10.01, 1.152, 0.34]}, {"time": 1.1667, "value": 1.84, "curve": [1.172, 2.4, 1.217, 5.06]}, {"time": 1.2667, "value": 5.88, "curve": [1.28, 6.11, 1.317, 6.21]}, {"time": 1.3333, "value": 6.21, "curve": [1.35, 6.21, 1.395, 1.51]}, {"time": 1.4, "value": 1.14, "curve": [1.427, -0.79, 1.475, -2.77]}, {"time": 1.5, "value": -2.77}]}, "bone4": {"rotate": [{"curve": [0.025, 0, 0.075, -6.29]}, {"time": 0.1, "value": -6.29, "curve": [0.142, -6.29, 0.225, 14.19]}, {"time": 0.2667, "value": 14.19, "curve": [0.283, 14.19, 0.324, 11.19]}, {"time": 0.3333, "value": 10.52, "curve": [0.388, 6.71, 0.471, 1.06]}, {"time": 0.5, "value": -0.4, "curve": [0.573, -4, 0.7, -7.16]}, {"time": 0.7667, "value": -7.16, "curve": [0.817, -7.16, 0.917, -7.61]}, {"time": 0.9667, "value": -7.61, "curve": [0.983, -7.61, 1.023, -5.42]}, {"time": 1.0333, "value": -4.76, "curve": [1.088, -1.16, 1.133, 13.06]}, {"time": 1.1667, "value": 13.06, "curve": [1.192, 13.06, 1.261, 3.4]}, {"time": 1.2667, "value": 2.21, "curve": [1.285, -1.5, 1.317, -12.63]}, {"time": 1.3333, "value": -12.63, "curve": [1.35, -12.63, 1.383, 16.65]}, {"time": 1.4, "value": 16.65, "curve": [1.425, 16.65, 1.475, -2.77]}, {"time": 1.5, "value": -2.77}]}, "bone5": {"rotate": [{"curve": [0.025, 0, 0.075, -6.29]}, {"time": 0.1, "value": -6.29, "curve": [0.142, -6.29, 0.225, 14.19]}, {"time": 0.2667, "value": 14.19, "curve": [0.283, 14.19, 0.321, 9.81]}, {"time": 0.3333, "value": 9.24, "curve": [0.398, 6.26, 0.459, 4.3]}, {"time": 0.5, "value": 3.82, "curve": [0.575, 2.94, 0.7, 1.96]}, {"time": 0.7667, "value": 1.96, "curve": [0.817, 1.96, 0.91, -3.96]}, {"time": 0.9667, "value": -7.61, "curve": [0.977, -8.28, 1.017, -10.01]}, {"time": 1.0333, "value": -10.01, "curve": [1.067, -10.01, 1.133, 6.48]}, {"time": 1.1667, "value": 6.48, "curve": [1.192, 6.48, 1.254, 3.31]}, {"time": 1.2667, "value": 2.21, "curve": [1.294, -0.11, 1.317, -11.57]}, {"time": 1.3333, "value": -11.57, "curve": [1.358, -11.57, 1.408, 5.16]}, {"time": 1.4333, "value": 5.16, "curve": [1.458, 5.16, 1.508, -2.77]}, {"time": 1.5333, "value": -2.77}]}, "bone": {"rotate": [{"curve": [0.025, 0, 0.075, -6.29]}, {"time": 0.1, "value": -6.29, "curve": [0.142, -6.29, 0.225, 14.19]}, {"time": 0.2667, "value": 14.19, "curve": [0.283, 14.19, 0.325, 10.28]}, {"time": 0.3333, "value": 9.74, "curve": [0.388, 6.32, 0.46, 4.23]}, {"time": 0.5, "value": 3.82, "curve": [0.562, 3.19, 0.7, 2.05]}, {"time": 0.7667, "value": 1.96, "curve": [0.825, 1.88, 0.917, 4.62]}, {"time": 0.9667, "value": 4.62, "curve": [0.983, 4.62, 1.023, 1.2]}, {"time": 1.0333, "value": 0.71, "curve": [1.081, -1.59, 1.133, -2.71]}, {"time": 1.1667, "value": -2.71, "curve": [1.192, -2.71, 1.246, -1.91]}, {"time": 1.2667, "value": -0.49, "curve": [1.294, 1.34, 1.298, 3.19]}, {"time": 1.3333, "value": 5.49, "curve": [1.342, 6.03, 1.383, 7.29]}, {"time": 1.4, "value": 7.29, "curve": [1.425, 7.29, 1.475, -2.77]}, {"time": 1.5, "value": -2.77}]}, "bone2": {"rotate": [{"curve": [0.025, 0, 0.075, -6.29]}, {"time": 0.1, "value": -6.29, "curve": [0.142, -6.29, 0.225, 14.19]}, {"time": 0.2667, "value": 14.19, "curve": [0.283, 14.19, 0.324, 10.89]}, {"time": 0.3333, "value": 10.09, "curve": [0.392, 5.25, 0.471, -0.35]}, {"time": 0.5, "value": -2.13, "curve": [0.562, -5.98, 0.7, -10.52]}, {"time": 0.7667, "value": -10.52, "curve": [0.817, -10.52, 0.929, -8.78]}, {"time": 0.9667, "value": -6.83, "curve": [0.998, -5.22, 1.027, -2.36]}, {"time": 1.0333, "value": -1.45, "curve": [1.073, 4.26, 1.133, 18.91]}, {"time": 1.1667, "value": 18.91, "curve": [1.192, 18.91, 1.26, 3.63]}, {"time": 1.2667, "value": 2.21, "curve": [1.283, -1.14, 1.317, -7.27]}, {"time": 1.3333, "value": -7.27, "curve": [1.35, -7.27, 1.383, 10.01]}, {"time": 1.4, "value": 10.01, "curve": [1.425, 10.01, 1.475, -2.77]}, {"time": 1.5, "value": -2.77}]}, "head-control": {"translate": [{"curve": [0.102, 0, 0.227, -26.08, 0.05, 0, 0.262, 97.74]}, {"time": 0.4333, "x": -26.1, "y": 97.3, "curve": [0.663, -26.12, 0.832, -16.98, 0.613, 96.83, 0.892, 22.95]}, {"time": 0.9333, "x": -14.02, "y": 11.08, "curve": [0.986, -12.5, 1.035, -10.47, 0.957, 4.24, 1.058, -31.97]}, {"time": 1.1, "x": -8.27, "y": -31.97, "curve": [1.163, -6.13, 1.265, -3.68, 1.158, -31.97, 1.275, 24.45]}, {"time": 1.3333, "x": -2.27, "y": 24.45, "curve": [1.447, 0.09, 1.483, 0, 1.383, 24.45, 1.483, 0]}, {"time": 1.5333}]}, "horn-front": {"translate": [{"curve": [0.108, 0, 0.325, -6.39, 0.108, 0, 0.325, 23.95]}, {"time": 0.4333, "x": -6.39, "y": 23.95, "curve": [0.558, -6.39, 0.808, 3.02, 0.558, 23.95, 0.808, -1.11]}, {"time": 0.9333, "x": 3.02, "y": -1.11, "curve": [0.975, 3.02, 1.058, 4.31, 0.975, -1.11, 1.058, -10.04]}, {"time": 1.1, "x": 4.31, "y": -10.04, "curve": [1.158, 4.31, 1.275, 3.72, 1.158, -10.04, 1.275, 6.17]}, {"time": 1.3333, "x": 3.72, "y": 6.17, "curve": [1.383, 3.72, 1.483, 0, 1.383, 6.17, 1.483, 0]}, {"time": 1.5333}]}, "horn-back": {"translate": [{"curve": [0.108, 0, 0.325, -3.27, 0.108, 0, 0.325, -12.12]}, {"time": 0.4333, "x": -3.27, "y": -12.12, "curve": [0.558, -3.27, 0.808, 0, 0.558, -12.12, 0.808, 0]}, {"time": 0.9333, "curve": [0.975, 0, 1.058, -2.83, 0.975, 0, 1.058, 17.37]}, {"time": 1.1, "x": -2.83, "y": 17.37, "curve": [1.158, -2.83, 1.275, 0.49, 1.158, 17.37, 1.275, 6.93]}, {"time": 1.3333, "x": 0.49, "y": 6.93, "curve": [1.383, 0.49, 1.483, 0, 1.383, 6.93, 1.483, 0]}, {"time": 1.5333}]}, "torso1": {"rotate": [{"curve": [0.118, 0.13, 0.135, 1.55]}, {"time": 0.2, "value": 3.04, "curve": [0.243, 4.04, 0.325, 5.4]}, {"time": 0.3667, "value": 5.4, "curve": [0.467, 5.4, 0.567, -16.54]}, {"time": 0.6667, "value": -16.54, "curve": [0.804, -16.54, 0.878, 9.25]}, {"time": 0.9667, "value": 9.37, "curve": [1.053, 9.48, 1.131, -2.06]}, {"time": 1.2, "value": -1.95, "curve": [1.292, -1.8, 1.304, 2.68]}, {"time": 1.3667, "value": 2.86, "curve": [1.429, 3.03, 1.472, -0.13]}, {"time": 1.5333}]}, "saddle": {"translate": [{"x": 19.28, "y": -10.71}]}, "tongue1": {"rotate": [{"curve": [0.1, 0, 0.3, 9.37]}, {"time": 0.4, "value": 9.37, "curve": [0.5, 9.37, 0.7, 17.48]}, {"time": 0.8, "value": 17.48, "curve": [0.867, 17.48, 1, 22.44]}, {"time": 1.0667, "value": 22.44, "curve": [1.117, 22.44, 1.217, -7.3]}, {"time": 1.2667, "value": -7.3, "curve": [1.333, -7.3, 1.467, 0]}, {"time": 1.5333}]}, "tongue2": {"rotate": [{"curve": [0.083, 0, 0.25, -16.67]}, {"time": 0.3333, "value": -16.67, "curve": [0.383, -16.67, 0.483, -20.4]}, {"time": 0.5333, "value": -20.4, "curve": [0.592, -20.4, 0.708, -5.9]}, {"time": 0.7667, "value": -5.9}]}, "tongue3": {"rotate": [{"curve": [0.083, 0, 0.25, -16.67]}, {"time": 0.3333, "value": -16.67, "curve": [0.383, -16.67, 0.483, -32.41]}, {"time": 0.5333, "value": -32.41, "curve": [0.592, -32.41, 0.708, -4.64]}, {"time": 0.7667, "value": -4.64, "curve": [0.817, -4.64, 0.917, 9.34]}, {"time": 0.9667, "value": 9.34}]}, "saddle-strap-front": {"rotate": [{"curve": [0.068, 0, 0.147, 0.59]}, {"time": 0.2333, "value": 0.26, "curve": [0.286, 0.06, 0.364, -2.89]}, {"time": 0.4, "value": -4.33, "curve": [0.432, -5.6, 0.489, -7.01]}, {"time": 0.5333, "value": -7.19, "curve": [0.622, -7.54, 0.748, -0.51]}, {"time": 0.8, "value": 1.72, "curve": [0.877, 5.01, 0.931, 5.58]}, {"time": 1, "value": 5.62, "curve": [1.04, 5.64, 1.114, -0.98]}, {"time": 1.1667, "value": -2.42, "curve": [1.307, -6.12, 1.433, -6.47]}, {"time": 1.5333, "value": -6.47}]}, "leg-control": {"translate": [{"curve": [0.05, 0, 0.106, 0, 0.05, 0, 0.106, 100.22]}, {"time": 0.1667, "y": 100.22, "curve": [0.199, 0, 0.234, 0, 0.199, 100.22, 0.242, -19.3]}, {"time": 0.2667, "y": -25.61, "curve": [0.331, 0, 0.398, 0, 0.321, -39.52, 0.398, -43.58]}, {"time": 0.4667, "y": -43.47, "curve": [0.522, 0, 0.578, 0, 0.522, -43.39, 0.598, 11.65]}, {"time": 0.6333, "y": 23.7, "curve": [0.745, 0, 0.858, 0, 0.729, 55.73, 0.858, 75.57]}, {"time": 0.9667, "y": 71.98, "curve": [0.989, 0, 1.012, 0, 0.989, 71.25, 1.012, -46.63]}, {"time": 1.0333, "y": -48.46, "curve": [1.091, 0, 1.147, 0, 1.091, -53.23, 1.126, 120.27]}, {"time": 1.2, "y": 118.83, "curve": [1.234, 0, 1.268, 0, 1.237, 118.12, 1.251, -67.36]}, {"time": 1.3, "y": -66.77, "curve": [1.335, 0, 1.369, 0, 1.333, -66.38, 1.368, 44.6]}, {"time": 1.4, "y": 40.56, "curve": [1.449, 0, 1.493, 0, 1.421, 37.96, 1.493, 0]}, {"time": 1.5333}]}, "front-arm": {"translate": [{}, {"time": 0.4, "x": -5.67, "y": -4.08}, {"time": 0.5333, "y": -4.23}, {"time": 0.9, "x": -12.75, "y": 12.28}, {"time": 1.2333, "x": -4.44, "y": -5.6}, {"time": 1.5333}], "scale": [{}, {"time": 0.4, "x": 1.057}, {"time": 0.5333, "x": 1.118}, {"time": 0.6667}, {"time": 1.3, "x": 1.105}, {"time": 1.5333}]}}, "ik": {"front-leg-ik": [{"softness": 37.3, "bendPositive": false}, {"time": 0.4333, "softness": 46.5, "bendPositive": false}, {"time": 0.8, "softness": 15.1, "bendPositive": false}], "spineboy-back-arm-ik": [{}], "spineboy-front-arm-ik": [{"softness": 15, "stretch": true}]}, "drawOrder": [{"offsets": [{"slot": "raptor-horn", "offset": 4}, {"slot": "front-thigh", "offset": -5}]}]}, "roar": {"slots": {"mouth-smile": {"attachment": [{"time": 0.6333, "name": "mouth-grind"}, {"time": 1.5667, "name": "mouth-smile"}]}}, "bones": {"hip": {"rotate": [{"curve": [0.025, 0, 0.061, 2.9]}, {"time": 0.1, "value": 2.95, "curve": [0.15, 3.01, 0.192, -10.89]}, {"time": 0.2, "value": -14.12, "curve": [0.224, -23.48, 0.238, -33.2]}, {"time": 0.3, "value": -33.4, "curve": [0.401, -33.71, 0.493, -9.73]}, {"time": 0.5667, "value": -6.44, "curve": [0.589, -5.43, 0.683, -0.43]}, {"time": 0.7, "value": -0.43, "curve": [0.739, -0.43, 0.778, -6.76]}, {"time": 0.8333, "value": -6.78, "curve": [0.962, -6.83, 1.164, -5.78]}, {"time": 1.4333, "value": -5.93, "curve": [1.531, -5.99, 1.535, -17.42]}, {"time": 1.7, "value": -17.43, "curve": [1.808, -17.5, 1.816, 3.25]}, {"time": 1.9333, "value": 3.28, "curve": [1.988, 3.3, 2.083, 0]}, {"time": 2.1333}], "translate": [{"y": 0.49, "curve": [0.012, -0.48, 0.055, 34.36, 0.033, 15.91, 0.072, 19.98]}, {"time": 0.1, "x": 34.57, "y": 19.97, "curve": [0.3, 33.35, 0.261, -337.47, 0.27, 21.25, 0.435, -81.31]}, {"time": 0.4667, "x": -339.74, "y": -103.88, "curve": [0.525, -333.53, 0.54, -266.9, 0.518, -141.21, 0.548, -146.45]}, {"time": 0.5667, "x": -207.1, "y": -146.24, "curve": [0.577, -182.59, 0.605, 44.93, 0.613, -146.67, 0.629, -42.46]}, {"time": 0.7, "x": 44.98, "y": -16.55, "curve": [0.729, 44.99, 0.752, 42.93, 0.722, -8.54, 0.75, -6.52]}, {"time": 0.7667, "x": 41.57, "y": -6.52, "curve": [0.8, 38.2, 0.817, 35.92, 0.783, -6.52, 0.817, -13.34]}, {"time": 0.8333, "x": 35.92, "y": -13.34, "curve": [0.85, 35.92, 0.861, 39.13, 0.85, -13.34, 0.883, -6.52]}, {"time": 0.9, "x": 41.57, "y": -6.52, "curve": [0.945, 44.44, 1.276, 55.19, 0.992, -6.52, 1.275, -29.36]}, {"time": 1.3667, "x": 48.94, "y": -29.36, "curve": [1.579, 34.34, 1.622, -126.54, 1.558, -27.52, 1.659, -0.02]}, {"time": 1.7, "x": -127.64, "y": 26.21, "curve": [1.76, -128.48, 1.796, -115.83, 1.73, 45.34, 1.788, 70.47]}, {"time": 1.8667, "x": -78.95, "y": 70.65, "curve": [1.924, -49.19, 2.036, -1.03, 1.95, 70.79, 2.067, -30.6]}, {"time": 2.1333, "y": 0.49}]}, "torso2": {"rotate": [{"curve": [0.093, 0.6, 0.15, 1.6]}, {"time": 0.2, "value": 1.61, "curve": [0.29, 1.63, 0.346, -1.9]}, {"time": 0.3667, "value": -3.53, "curve": [0.389, -5.3, 0.508, -20.04]}, {"time": 0.5333, "value": -22.32, "curve": [0.559, -24.61, 0.596, -27.57]}, {"time": 0.7, "value": -27.54, "curve": [0.758, -27.53, 0.754, -20.48]}, {"time": 0.8, "value": -20.36, "curve": [0.831, -20.27, 0.851, -21.29]}, {"time": 0.9, "value": -21.25, "curve": [0.963, -21.2, 1.021, -14.89]}, {"time": 1.0667, "value": -14.92, "curve": [1.141, -14.99, 1.167, -18.09]}, {"time": 1.2667, "value": -18.03, "curve": [1.385, -17.94, 1.605, -5.08]}, {"time": 1.6667, "value": -5.02, "curve": [1.714, -4.98, 1.751, -7.53]}, {"time": 1.8, "value": -7.52, "curve": [1.961, -6.98, 2.05, -0.54]}, {"time": 2.1333}], "translate": [{"curve": [0.05, 0, 0.167, 10.5, 0.05, 0, 0.15, 15.96]}, {"time": 0.2, "x": 12.62, "y": 15.96, "curve": [0.264, 16.81, 0.35, 19.16, 0.25, 15.96, 0.372, -12.74]}, {"time": 0.4, "x": 19.16, "y": -15.96, "curve": [0.45, 19.16, 0.565, 14.27, 0.46, -22.88, 0.55, -27.56]}, {"time": 0.6, "x": 13.67, "y": -27.56, "curve": [0.655, 12.74, 0.707, 12.17, 0.642, -27.56, 0.703, -24.23]}, {"time": 0.7667, "x": 12.05, "y": -14.93, "curve": [0.798, 11.99, 0.817, 12.59, 0.811, -8.49, 0.817, 0.87]}, {"time": 0.8333, "x": 13.4, "y": 0.87, "curve": [0.867, 15.08, 0.877, 16.88, 0.858, 0.87, 0.908, -11.59]}, {"time": 0.9333, "x": 18.37, "y": -11.59, "curve": [0.957, 19, 1.011, 19.96, 0.958, -11.59, 1.008, -1.9]}, {"time": 1.0333, "x": 20.16, "y": -1.9, "curve": [1.072, 20.52, 1.162, 20.95, 1.075, -1.9, 1.158, -5.89]}, {"time": 1.2, "x": 21.21, "y": -5.89, "curve": [1.245, 21.53, 1.375, 25.99, 1.258, -5.89, 1.375, 3.45]}, {"time": 1.4333, "x": 25.99, "y": 3.45, "curve": [1.5, 25.99, 1.645, 25.8, 1.5, 3.45, 1.646, 1.31]}, {"time": 1.7, "x": 25.18, "y": 0.95, "curve": [1.784, 24.23, 2.025, 0, 1.816, 0.17, 2.025, 0]}, {"time": 2.1333}], "scale": [{"time": 0.5333, "curve": [0.55, 1, 0.657, 1.09, 0.55, 1, 0.683, 0.966]}, {"time": 0.7, "x": 1.093, "y": 0.966, "curve": [0.772, 1.091, 0.8, 0.988, 0.774, 0.968, 0.8, 1.043]}, {"time": 0.8333, "x": 0.988, "y": 1.043, "curve": [0.867, 0.988, 0.983, 1.002, 0.867, 1.043, 0.991, 0.999]}, {"time": 1.1333}]}, "spineboy-torso": {"rotate": [{"curve": [0.024, -0.09, 0.088, -27.73]}, {"time": 0.1667, "value": -27.92, "curve": [0.193, -27.99, 0.216, -27.44]}, {"time": 0.2333, "value": -27.42, "curve": [0.292, -27.34, 0.4, -41]}, {"time": 0.4333, "value": -47.9, "curve": [0.481, -57.82, 0.578, -76.33]}, {"time": 0.6333, "value": -81.72, "curve": [0.656, -83.88, 0.669, -84.84]}, {"time": 0.7, "value": -84.84, "curve": [0.733, -84.84, 0.8, -60.21]}, {"time": 0.8333, "value": -60.21, "curve": [0.85, -60.21, 0.873, -68.32]}, {"time": 0.9, "value": -68.29, "curve": [0.933, -68.25, 0.958, -64.03]}, {"time": 1, "value": -63.97, "curve": [1.085, -63.84, 1.213, -69.62]}, {"time": 1.2667, "value": -69.47, "curve": [1.42, -69.06, 1.479, -59.79]}, {"time": 1.5, "value": -54.97, "curve": [1.524, -49.41, 1.645, -25.87]}, {"time": 1.7, "value": -18.45, "curve": [1.751, -11.45, 1.878, 0]}, {"time": 2.1333}]}, "head2": {"rotate": [{"value": 12.98, "curve": [0.031, 13.09, 0.051, 13.3]}, {"time": 0.0667, "value": 14.18, "curve": [0.093, 15.64, 0.27, 32.49]}, {"time": 0.3, "value": 33.18, "curve": [0.337, 34.04, 0.379, 21.48]}, {"time": 0.4333, "value": 21.71, "curve": [0.495, 21.97, 0.586, 55.8]}, {"time": 0.6333, "value": 55.8, "curve": [0.713, 55.8, 0.808, 48.07]}, {"time": 0.9, "value": 48.25, "curve": [0.95, 48.35, 0.991, 53.74]}, {"time": 1.0333, "value": 53.37, "curve": [1.058, 53.15, 1.073, 47.29]}, {"time": 1.1, "value": 47.47, "curve": [1.189, 48.06, 1.186, 51.29]}, {"time": 1.2333, "value": 51.02, "curve": [1.265, 50.85, 1.312, 48.13]}, {"time": 1.3333, "value": 45.79, "curve": [1.359, 42.93, 1.443, 33.19]}, {"time": 1.5, "value": 32.96, "curve": [1.555, 32.74, 1.584, 45.98]}, {"time": 1.6333, "value": 45.92, "curve": [1.706, 45.84, 1.747, 24.35]}, {"time": 1.8, "value": 15.58, "curve": [1.858, 6.03, 1.904, 0]}, {"time": 1.9333, "curve": [1.966, 0, 2.019, 17.18]}, {"time": 2.0667, "value": 17.31, "curve": [2.097, 17.39, 2.116, 12.98]}, {"time": 2.1333, "value": 12.98}], "scale": [{"time": 0.4667, "curve": [0.497, 1, 0.531, 0.984, 0.497, 1, 0.531, 1.04]}, {"time": 0.5667, "x": 0.984, "y": 1.04, "curve": [0.618, 0.984, 0.684, 1.043, 0.618, 1.04, 0.668, 0.992]}, {"time": 0.7333, "x": 1.044, "y": 0.99, "curve": [0.775, 1.045, 0.844, 1, 0.767, 0.989, 0.844, 1]}, {"time": 0.9, "curve": "stepped"}, {"time": 1.4333, "curve": [1.475, 1, 1.563, 1.001, 1.475, 1, 1.568, 1.003]}, {"time": 1.6, "x": 1.004, "y": 1.008, "curve": [1.651, 1.008, 1.675, 1.016, 1.637, 1.012, 1.675, 1.055]}, {"time": 1.7, "x": 1.016, "y": 1.055, "curve": [1.758, 1.016, 1.854, 1.008, 1.758, 1.055, 1.881, 1.014]}, {"time": 1.9333, "x": 1.004, "y": 1.008, "curve": [1.98, 1.001, 2.083, 1, 1.993, 1.001, 2.083, 1]}, {"time": 2.1333}]}, "neck2": {"rotate": [{"value": 0.02, "curve": [0.05, 0.02, 0.144, 5.69]}, {"time": 0.2, "value": 9.94, "curve": [0.276, 15.74, 0.525, 37.25]}, {"time": 0.6333, "value": 37.25, "curve": [0.808, 37.25, 1.175, 37.21]}, {"time": 1.3333, "value": 33.93, "curve": [1.543, 29.58, 1.659, 13.32]}, {"time": 1.7, "value": 11.08, "curve": [1.762, 7.68, 1.875, 0]}, {"time": 1.9333}]}, "front-arm1": {"rotate": [{"curve": [0.025, 0, 0.051, -14.24]}, {"time": 0.1, "value": -14.37, "curve": [0.206, -14.64, 0.27, 25.52]}, {"time": 0.3333, "value": 25.6, "curve": [0.466, 25.77, 0.528, -35.52]}, {"time": 0.7, "value": -35.63, "curve": [0.742, -35.65, 0.808, -30.27]}, {"time": 0.8333, "value": -28.76, "curve": [0.956, -21.55, 1.342, -18.16]}, {"time": 1.5, "value": -18.16, "curve": [1.608, -18.16, 1.825, 13.78]}, {"time": 1.9333, "value": 13.78, "curve": [1.983, 13.78, 2.083, 0]}, {"time": 2.1333}]}, "front-arm2": {"rotate": [{"curve": [0.157, 0, 0.166, 22.96]}, {"time": 0.2333, "value": 23.11, "curve": [0.52, 23.77, 0.523, 3.24]}, {"time": 0.5333, "value": -3.34, "curve": [0.561, -21.06, 0.604, -49.19]}, {"time": 0.8, "value": -59.15, "curve": [0.896, -64.05, 1.195, -69.82]}, {"time": 1.3333, "value": -69.51, "curve": [1.407, -69.35, 1.46, -64.94]}, {"time": 1.5, "value": -47.97, "curve": [1.525, -37.26, 1.6, 20.75]}, {"time": 1.6667, "value": 20.78, "curve": [1.801, 20.84, 1.868, -8.99]}, {"time": 1.9333, "value": -8.99, "curve": [1.983, -8.99, 2.083, 0]}, {"time": 2.1333}]}, "front-hand": {"rotate": [{"curve": [0.05, 0, 0.15, 15.17]}, {"time": 0.2, "value": 15.17, "curve": [0.409, 14.83, 0.403, -26.62]}, {"time": 0.5333, "value": -25.8, "curve": [0.62, -25.26, 0.648, 1.88]}, {"time": 0.8, "value": 1.88, "curve": [0.983, 1.88, 1.479, -23.1]}, {"time": 1.5333, "value": -30.97, "curve": [1.574, -36.96, 1.591, -46.26]}, {"time": 1.6333, "value": -46.06, "curve": [1.75, -45.52, 1.762, 18.69]}, {"time": 1.9333, "value": 19.03, "curve": [1.983, 19.13, 2.083, 0]}, {"time": 2.1333}]}, "back-arm2": {"rotate": [{"curve": [0.05, 0, 0.081, -14.57]}, {"time": 0.2, "value": -14.55, "curve": [0.287, -14.54, 0.388, -2.01]}, {"time": 0.4667, "value": -2.44, "curve": [0.53, -2.78, 0.593, -10.56]}, {"time": 0.6, "value": -25.66, "curve": [0.621, -73.18, 0.619, -95.18]}, {"time": 0.7667, "value": -97.24, "curve": [0.847, -98.36, 1.279, -103.19]}, {"time": 1.5, "value": -77.44, "curve": [1.567, -69.6, 1.77, -24.47]}, {"time": 1.9333, "value": -8.99, "curve": [1.959, -6.54, 2.083, 0]}, {"time": 2.1333}]}, "back-hand": {"rotate": [{"curve": [0.05, 0, 0.136, 7.7]}, {"time": 0.2, "value": 19.14, "curve": [0.243, 26.85, 0.237, 49.93]}, {"time": 0.3, "value": 49.98, "curve": [0.416, 50.06, 0.415, 33.42]}, {"time": 0.4667, "value": 33.94, "curve": [0.586, 35.15, 0.557, 78.18]}, {"time": 0.6, "value": 78.77, "curve": [0.624, 79.1, 0.639, 48.7]}, {"time": 0.6667, "value": 37.71, "curve": [0.711, 20.36, 0.804, -1.75]}, {"time": 0.9667, "value": -3, "curve": [1.168, -4.55, 1.332, -1.6]}, {"time": 1.3667, "value": 1.79, "curve": [1.408, 5.92, 1.502, 36.79]}, {"time": 1.5667, "value": 36.4, "curve": [1.685, 35.7, 1.9, 31.04]}, {"time": 1.9333, "value": 27.25, "curve": [1.999, 19.84, 2.083, 0]}, {"time": 2.1333}]}, "back-arm1": {"rotate": [{"curve": [0.042, 0, 0.056, 24.8]}, {"time": 0.1667, "value": 25.18, "curve": [0.301, 25.64, 0.566, 11.56]}, {"time": 0.6333, "value": -5.85, "curve": [0.672, -16, 0.707, -25.63]}, {"time": 0.8, "value": -25.79, "curve": [0.975, -25.79, 1.295, -26.78]}, {"time": 1.5, "value": -12.94, "curve": [1.564, -8.58, 1.825, 13.78]}, {"time": 1.9333, "value": 13.78, "curve": [1.983, 13.78, 2.083, 0]}, {"time": 2.1333}]}, "neck": {"rotate": [{"curve": [0.05, 0, 0.114, -8.5]}, {"time": 0.2, "value": -8.26, "curve": [0.29, -8.01, 0.537, 12.55]}, {"time": 0.6333, "value": 13.44, "curve": [0.766, 14.68, 1.273, 2.13]}, {"time": 1.4667, "value": -3.5, "curve": [1.565, -6.92, 1.656, -11.02]}, {"time": 1.7, "value": -10.74, "curve": [1.788, -10.19, 1.852, 2.74]}, {"time": 1.9333, "value": 2.88, "curve": [1.992, 2.98, 2.083, 0]}, {"time": 2.1333}], "translate": [{"curve": [0.05, 0, 0.15, -20.09, 0.05, 0, 0.174, -0.26]}, {"time": 0.2, "x": -19.98, "y": -1.45, "curve": [0.312, -19.75, 0.463, 34.4, 0.264, -4.43, 0.474, -34.98]}, {"time": 0.6, "x": 34.3, "y": -34.87, "curve": [0.672, 34.26, 0.742, 30.02, 0.638, -34.83, 0.742, -21.32]}, {"time": 0.8, "x": 30.02, "y": -21.32, "curve": [0.842, 30.02, 0.992, 37.66, 0.842, -21.32, 0.992, -41.18]}, {"time": 1.0333, "x": 37.66, "y": -41.18, "curve": [1.083, 37.66, 1.183, 23.6, 1.083, -41.18, 1.183, -0.63]}, {"time": 1.2333, "x": 23.6, "y": -0.63, "curve": [1.292, 23.6, 1.408, 26.3, 1.292, -0.63, 1.408, -26.62]}, {"time": 1.4667, "x": 26.3, "y": -26.62, "curve": [1.581, 26.3, 1.618, 17.21, 1.5, -26.62, 1.675, 3.64]}, {"time": 1.7, "x": 12.18, "y": 3.64, "curve": [1.773, 7.71, 1.87, 4.4, 1.758, 3.64, 1.875, -4.9]}, {"time": 1.9333, "x": 2.83, "y": -4.9, "curve": [2.001, 1.15, 2.083, 0, 1.983, -4.9, 2.083, 0]}, {"time": 2.1333}]}, "head": {"rotate": [{"curve": [0.001, -4.96, 0.05, -6.66]}, {"time": 0.0667, "value": -6.57, "curve": [0.211, -5.81, 0.176, 34.04]}, {"time": 0.2667, "value": 33.65, "curve": [0.335, 33.7, 0.454, 12.93]}, {"time": 0.5, "value": 3.37, "curve": [0.539, -4.68, 0.547, -11.66]}, {"time": 0.6, "value": -12.11, "curve": [0.645, -11.69, 0.602, 31.75]}, {"time": 0.8, "value": 31.51, "curve": [0.85, 31.51, 0.917, 25.3]}, {"time": 0.9667, "value": 25.3, "curve": [1.075, 25.3, 1.392, 27.9]}, {"time": 1.5, "value": 27.9, "curve": [1.55, 27.9, 1.717, -5.14]}, {"time": 1.8, "value": -4.51, "curve": [1.861, -4.19, 1.949, 4.07]}, {"time": 2.0333, "value": 4.18, "curve": [2.087, 4.25, 2.096, 2.99]}, {"time": 2.1333}], "translate": [{"curve": [0.05, 0, 0.15, 22.58, 0.05, 0, 0.154, -41.07]}, {"time": 0.2, "x": 22.75, "y": -41.48, "curve": [0.3, 23.09, 0.336, -13.2, 0.329, -42.63, 0.356, 19.91]}, {"time": 0.5667, "x": -12.04, "y": 21.07, "curve": [0.643, -12.24, 0.681, 19.67, 0.625, 21.26, 0.615, -90.96]}, {"time": 0.7, "x": 20.1, "y": -90.36, "curve": [0.718, 20.5, 0.75, 16.57, 0.738, -90.33, 0.75, -87.92]}, {"time": 0.7667, "x": 16.57, "y": -87.92, "curve": [0.783, 16.57, 0.817, 18.74, 0.783, -87.92, 0.817, -87.42]}, {"time": 0.8333, "x": 18.74, "y": -87.42, "curve": [0.85, 18.74, 0.883, 16.57, 0.85, -87.42, 0.883, -87.92]}, {"time": 0.9, "x": 16.57, "y": -87.92, "curve": [0.917, 16.57, 0.95, 18.74, 0.917, -87.92, 0.95, -87.42]}, {"time": 0.9667, "x": 18.74, "y": -87.42, "curve": [1.075, 18.74, 1.392, 27.31, 1.075, -87.42, 1.392, -84.9]}, {"time": 1.5, "x": 27.31, "y": -84.9, "curve": [1.554, 27.31, 1.605, -22.6, 1.512, -84.9, 1.575, -102.53]}, {"time": 1.7, "x": -22.58, "y": -89.06, "curve": [1.812, -23.81, 1.863, 9.94, 1.841, -71.22, 1.847, 11.93]}, {"time": 1.9667, "x": 10.77, "y": 13.35, "curve": [2.028, 11.26, 2.065, -0.88, 2.032, 14.12, 2.082, 7.52]}, {"time": 2.1333}], "shear": [{"time": 0.6333, "curve": [0.654, 0, 0.67, -0.13, 0.654, 0, 0.676, 0]}, {"time": 0.7, "x": -0.56, "curve": [0.721, -0.86, 0.744, -1.24, 0.721, 0, 0.744, 0]}, {"time": 0.7667, "x": -1.24, "curve": [0.788, -1.24, 0.819, -0.69, 0.788, 0, 0.811, 0]}, {"time": 0.8333, "x": -0.47, "curve": [0.88, 0.25, 0.901, 0.43, 0.866, 0, 0.9, 0]}, {"time": 0.9333, "x": 0.43, "curve": [0.975, 0.44, 1.017, -0.47, 1, 0, 1.068, 0]}, {"time": 1.1333, "x": -0.46, "curve": [1.202, -0.44, 1.282, -0.01, 1.216, 0, 1.462, 0]}, {"time": 1.5}]}, "jaw": {"rotate": [{"value": -1.2, "curve": [0.05, -1.2, 0.158, 0.54]}, {"time": 0.2, "value": 1.83, "curve": [0.273, 4.06, 0.417, 15.63]}, {"time": 0.4667, "value": 14.47, "curve": [0.652, 10.18, 0.592, -68.7]}, {"time": 0.6667, "value": -68.7, "curve": [0.731, -68.52, 0.767, -42.64]}, {"time": 0.8, "value": -42.45, "curve": [0.808, -42.45, 0.801, -43.41]}, {"time": 0.8333, "value": -43.55, "curve": [0.858, -43.55, 0.906, -39.81]}, {"time": 0.9333, "value": -39.73, "curve": [0.956, -39.65, 1.013, -42.1]}, {"time": 1.0333, "value": -42.02, "curve": [1.06, -41.91, 1.083, -35.38]}, {"time": 1.1333, "value": -35.34, "curve": [1.169, -35.32, 1.22, -36.84]}, {"time": 1.2667, "value": -36.74, "curve": [1.372, -36.31, 1.542, 1.5]}, {"time": 1.6, "value": -3.58, "curve": [1.62, -3.98, 1.647, -9.02]}, {"time": 1.7, "value": -8.79, "curve": [1.748, -8.74, 1.823, 3.7]}, {"time": 1.9333, "value": 4.73, "curve": [1.951, 5.04, 1.975, -1.14]}, {"time": 2, "value": -1.34, "curve": [2.048, -1.72, 2.1, -1.2]}, {"time": 2.1333, "value": -1.2}], "translate": [{"x": -3.44, "y": 2.51, "curve": [0.111, -3.74, 0.391, -39.94, 0.155, 4.09, 0.297, -2.98]}, {"time": 0.4667, "x": -56.45, "y": -10.08, "curve": [0.527, -69.24, 0.561, -75.25, 0.577, -15.37, 0.551, -15.25]}, {"time": 0.6, "x": -74.43, "y": -14.69, "curve": [0.634, -74.95, 0.624, -60.95, 0.641, -14.11, 0.65, 23.07]}, {"time": 0.6667, "x": -53.45, "y": 23.07, "curve": [0.759, -37.12, 1.392, -28.49, 0.892, 23.07, 1.375, 19.61]}, {"time": 1.6, "x": -21.33, "y": 16.78, "curve": [1.762, -15.77, 1.825, -8.47, 1.625, 16.47, 1.78, 6.33]}, {"time": 1.8667, "x": -8.47, "y": -3.77, "curve": [1.9, -8.47, 1.967, -15.38, 1.914, -8.24, 1.967, -10.85]}, {"time": 2, "x": -15.38, "y": -10.85, "curve": [2.033, -15.38, 2.1, -3.44, 2.033, -10.85, 2.1, 2.51]}, {"time": 2.1333, "x": -3.44, "y": 2.51}], "scale": [{"time": 0.5667, "curve": [0.594, 1, 0.621, 0.956, 0.601, 1, 0.621, 1.058]}, {"time": 0.6333, "x": 0.956, "y": 1.058, "curve": [0.646, 0.956, 0.639, 0.994, 0.646, 1.058, 0.642, 1.02]}, {"time": 0.7}], "shear": [{"time": 0.2333, "curve": [0.283, 0, 0.361, -0.05, 0.283, 0, 0.405, -0.25]}, {"time": 0.4333, "x": 0.04, "y": -0.47, "curve": [0.498, 0.13, 0.57, 0.57, 0.485, -0.88, 0.533, -2.91]}, {"time": 0.6333, "x": 0.55, "y": -2.91, "curve": [0.702, 0.53, 0.717, 0.33, 0.68, -2.87, 0.748, -2.09]}, {"time": 0.8, "x": 0.2, "y": -1.59, "curve": [0.897, 0.05, 1.025, 0, 0.862, -0.98, 1.025, 0]}, {"time": 1.1333}]}, "tongue1": {"rotate": [{"value": 16.25, "curve": [0.05, 16.25, 0.197, 16.24]}, {"time": 0.2, "value": 15.93, "curve": [0.244, 10.93, 0.33, -0.69]}, {"time": 0.3667, "value": -0.58, "curve": [0.432, -0.37, 0.481, 1.4]}, {"time": 0.5333, "value": 5.31, "curve": [0.541, 5.85, 0.57, 25.11]}, {"time": 0.6, "value": 28.2, "curve": [0.611, 29.32, 0.623, 14.35]}, {"time": 0.6333, "value": 15.32, "curve": [0.661, 17.81, 0.683, 18.19]}, {"time": 0.7, "value": 18.19, "curve": [0.733, 18.19, 0.8, 13.38]}, {"time": 0.8333, "value": 13.38, "curve": [0.867, 13.38, 0.937, 14.84]}, {"time": 0.9667, "value": 15.71, "curve": [1.019, 17.29, 1.045, 19.01]}, {"time": 1.0667, "value": 20.57, "curve": [1.084, 21.85, 1.148, 23.51]}, {"time": 1.1667, "value": 23.29, "curve": [1.22, 22.66, 1.242, 16.58]}, {"time": 1.2667, "value": 16.58, "curve": [1.283, 16.58, 1.327, 19.6]}, {"time": 1.3333, "value": 19.86, "curve": [1.376, 21.73, 1.408, 22.44]}, {"time": 1.4333, "value": 22.44, "curve": [1.458, 22.44, 1.481, 19.06]}, {"time": 1.5333, "value": 19.01, "curve": [1.575, 18.98, 1.656, 22.9]}, {"time": 1.7, "value": 22.93, "curve": [1.776, 22.99, 1.875, 16.25]}, {"time": 1.9333, "value": 16.25}], "translate": [{"x": -22.37, "y": 13.32}], "scale": [{"time": 0.2, "curve": [0.3, 1, 0.5, 1.16, 0.3, 1, 0.5, 1]}, {"time": 0.6, "x": 1.16, "curve": [0.875, 1.16, 1.425, 1, 0.875, 1, 1.425, 1]}, {"time": 1.7}]}, "torso1": {"rotate": [{"curve": [0.029, 0, 0.036, -1.4]}, {"time": 0.1, "value": -1.41, "curve": [0.143, -1.42, 0.287, 10.8]}, {"time": 0.3333, "value": 10.73, "curve": [0.479, 10.51, 0.531, 6.79]}, {"time": 0.6, "curve": [0.624, -2.59, 0.679, -4.75]}, {"time": 0.7, "value": -4.9, "curve": [0.723, -5.06, 0.75, -2.6]}, {"time": 0.7667, "value": -2.6, "curve": [0.783, -2.6, 0.814, -4.72]}, {"time": 0.8333, "value": -4.7, "curve": [0.858, -4.67, 0.857, -1.2]}, {"time": 0.9, "value": -1.13, "curve": [0.926, -1.09, 1, -3.27]}, {"time": 1.0333, "value": -3.27, "curve": [1.061, -3.27, 1.1, 0.25]}, {"time": 1.1333, "value": 0.24, "curve": [1.179, 0.23, 1.199, -1.36]}, {"time": 1.2333, "value": -1.36, "curve": [1.295, -1.36, 1.29, 1.49]}, {"time": 1.3667, "value": 1.47, "curve": [1.409, 1.46, 1.464, -1.37]}, {"time": 1.5, "value": -1.36, "curve": [1.607, -1.3, 1.754, 1.39]}, {"time": 1.9667, "value": 1.36, "curve": [2.008, 1.36, 2.092, 0]}, {"time": 2.1333}]}, "horn-back": {"rotate": [{"time": 0.2}, {"time": 1.0333, "value": 7.62}, {"time": 1.9333}], "translate": [{"time": 1.6667, "curve": [1.708, 0, 1.792, 19.83, 1.708, 0, 1.792, 22.59]}, {"time": 1.8333, "x": 19.83, "y": 22.59, "curve": [1.908, 19.83, 2.058, 0, 1.908, 22.59, 2.058, 0]}, {"time": 2.1333}]}, "horn-front": {"rotate": [{"curve": [0.018, 0, 0.052, 0.73]}, {"time": 0.0667, "value": 1.73, "curve": [0.084, 2.92, 0.1, 4.72]}, {"time": 0.1333, "value": 4.95, "curve": [0.162, 5.16, 0.187, 4.3]}, {"time": 0.2, "value": 3.89, "curve": [0.289, 0.95, 0.477, -6.85]}, {"time": 0.6667, "value": -6.06, "curve": [0.809, -5.49, 0.953, -3.36]}, {"time": 1.0333, "value": -2.66, "curve": [1.255, -0.73, 1.708, 0]}, {"time": 1.9333}], "translate": [{"time": 1.4667}, {"time": 1.9, "x": 5.8, "y": -14.31}, {"time": 2.0333, "x": 7.96, "y": -4.52}, {"time": 2.1333}]}, "front-leg-target": {"rotate": [{"value": -0.31}], "translate": [{"curve": [0.138, 0.68, 0.144, 63.75, 0.092, 0, 0.15, 21.48]}, {"time": 0.2, "x": 63.06, "y": 22.03, "curve": [0.315, 61.65, 0.292, -25.03, 0.315, 23.29, 0.356, -56.94]}, {"time": 0.5, "x": -29.09, "y": -56.75, "curve": [0.601, -31.05, 0.583, 104.34, 0.589, -56.64, 0.583, 47.28]}, {"time": 0.7, "x": 104.34, "y": 47.28}, {"time": 1.3667, "x": 106.2, "y": 47.7, "curve": [1.538, 105.92, 1.633, 88.98, 1.552, 49.6, 1.633, 40.88]}, {"time": 1.7333, "x": 49.5, "y": 28.71, "curve": [1.842, 7.01, 2.025, -0.54, 1.842, 15.61, 2.025, 0]}, {"time": 2.1333}]}, "back-leg-target": {"translate": [{}, {"time": 0.2, "x": 5.58, "y": 4.02}, {"time": 0.4667, "x": -39.15, "y": -10.18, "curve": [0.512, -15.45, 0.563, 1.22, 0.512, 9.94, 0.563, 28.79]}, {"time": 0.6333, "x": 17.09, "y": 42.26}, {"time": 0.7333, "x": 30.54, "y": 59.35}, {"time": 1.4667, "x": 31.18, "y": 60.17, "curve": [1.567, 24.16, 1.607, 12.61, 1.564, 55.67, 1.613, 36.27]}, {"time": 1.6667, "x": -19.04, "y": -0.52}, {"time": 2.1333}]}, "spineboy-hip": {"rotate": [{"curve": [0.025, 0, 0.075, 10.98]}, {"time": 0.1, "value": 10.98, "curve": [0.157, 10.98, 0.238, 8.08]}, {"time": 0.3333, "value": 8.02, "curve": [0.391, 7.98, 0.432, 11.76]}, {"time": 0.4667, "value": 11.66, "curve": [0.491, 11.58, 0.504, 11.4]}, {"time": 0.5333, "value": 7.39, "curve": [0.577, 1.45, 0.627, -5.5]}, {"time": 0.7, "value": -7.47, "curve": [0.821, -10.75, 0.917, -11.16]}, {"time": 1, "value": -11.16, "curve": [1.13, -11.16, 1.359, 3.64]}, {"time": 1.5333, "value": 3.43, "curve": [1.603, 3.89, 1.637, -25.96]}, {"time": 1.7, "value": -25.96, "curve": [1.811, -26.33, 1.881, 9.15]}, {"time": 1.9667, "value": 9.05, "curve": [2.034, 8.97, 2.092, 0]}, {"time": 2.1333}], "translate": [{"curve": [0.025, 0, 0.075, -3.14, 0.025, 0, 0.075, 4.23]}, {"time": 0.1, "x": -2.69, "y": 4.23, "curve": [0.131, -2.14, 0.167, 3.27, 0.125, 4.23, 0.183, -2.98]}, {"time": 0.2, "x": 17.13, "y": -6.44, "curve": [0.214, 22.78, 0.275, 27.63, 0.235, -13.6, 0.274, -24.33]}, {"time": 0.3, "x": 27.63, "y": -24.78, "curve": [0.342, 27.63, 0.425, 23.61, 0.349, -25.62, 0.441, -8.74]}, {"time": 0.4667, "x": 23.61, "y": -8.32, "curve": [0.483, 23.61, 0.508, 20.92, 0.486, -7.99, 0.508, -37.58]}, {"time": 0.5333, "x": 35.42, "y": -44.36, "curve": [0.551, 45.68, 0.608, 70.81, 0.569, -53.86, 0.616, -66.98]}, {"time": 0.6333, "x": 70.81, "y": -72.81, "curve": [0.65, 70.81, 0.679, 65.64, 0.648, -77.73, 0.671, -76.48]}, {"time": 0.7, "x": 62.91, "y": -76.91, "curve": [0.725, 59.54, 0.775, 59.86, 0.73, -77.35, 0.775, -54.3]}, {"time": 0.8, "x": 59.86, "y": -54.3, "curve": [0.825, 59.86, 0.867, 59.93, 0.825, -54.3, 0.865, -55.33]}, {"time": 0.9, "x": 60.79, "y": -56.69, "curve": [0.946, 62, 0.962, 62.29, 0.923, -57.6, 0.951, -57.62]}, {"time": 1, "x": 63.78, "y": -57.73, "curve": [1.038, 65.26, 1.106, 68.28, 1.058, -57.86, 1.1, -57.63]}, {"time": 1.1333, "x": 69.44, "y": -57.41, "curve": [1.217, 72.97, 1.333, 77.13, 1.226, -56.82, 1.316, -55.55]}, {"time": 1.4, "x": 77.13, "y": -53.65, "curve": [1.433, 77.13, 1.467, 73.12, 1.437, -52.82, 1.492, -50.91]}, {"time": 1.5333, "x": 63.41, "y": -49.52, "curve": [1.549, 61.17, 1.597, 46.67, 1.576, -48.09, 1.599, -47.25]}, {"time": 1.6333, "x": 38.86, "y": -45.65, "curve": [1.687, 27.26, 1.722, 20.8, 1.677, -43.62, 1.71, -40.67]}, {"time": 1.7333, "x": 19.46, "y": -38, "curve": [1.803, 11.67, 1.885, 5.59, 1.758, -35.11, 1.888, -14.53]}, {"time": 1.9333, "x": 4.11, "y": -7.78, "curve": [2.006, 1.87, 2.083, 0, 1.96, -3.77, 2.083, 0]}, {"time": 2.1333}]}, "front-hand2": {"rotate": [{}, {"time": 0.2, "value": -21.93}, {"time": 0.3, "value": -23.29}, {"time": 0.4333, "value": -29.8}, {"time": 0.5333, "value": -36.62}, {"time": 0.6333, "value": -40.49}, {"time": 0.7, "value": -38.28}, {"time": 0.7667, "value": -33.33}, {"time": 0.8333, "value": -28.06}, {"time": 1.4333, "value": -27.17}, {"time": 1.5333, "value": -27.62}, {"time": 1.7}]}, "spineboy-front-arm-target": {"translate": [{}, {"time": 0.0667, "x": 1.11, "y": -2.35}, {"time": 0.3, "x": 21.56, "y": -45.67}, {"time": 0.5667, "x": 2.23, "y": -34.98}, {"time": 0.7333, "x": 28.52, "y": -47.75}, {"time": 0.8333, "x": 24.52, "y": -37.92}, {"time": 1.5, "x": 26.24, "y": -38.24}, {"time": 1.7, "x": -9.39, "y": -0.72}, {"time": 1.8667, "x": 1.03, "y": -21.86}, {"time": 2.1333}]}, "gun": {"rotate": [{"curve": [0.025, 0, 0.075, -16.45]}, {"time": 0.1, "value": -16.38, "curve": [0.186, -16.14, 0.268, -2.86]}, {"time": 0.3, "value": -2.83, "curve": [0.342, -2.79, 0.427, -27.51]}, {"time": 0.4333, "value": -29.46, "curve": [0.508, -53.36, 0.613, -60.85]}, {"time": 0.6333, "value": -60.92, "curve": [0.711, -61.15, 0.734, -27.93]}, {"time": 0.7667, "value": -23.27, "curve": [0.785, -20.65, 0.852, -13.39]}, {"time": 0.9, "value": -13.36, "curve": [0.952, -13.33, 1.002, -20.79]}, {"time": 1.0333, "value": -20.87, "curve": [1.206, -21.35, 1.432, -17.1]}, {"time": 1.5, "value": -14.02, "curve": [1.549, -11.78, 1.65, 15.4]}, {"time": 1.7, "value": 15.4, "curve": [1.733, 15.4, 1.8, 9.7]}, {"time": 1.8333, "value": 2.78, "curve": [1.875, -5.94, 1.908, -25.4]}, {"time": 1.9333, "value": -25.23, "curve": [2.015, -24.66, 2.108, 0]}, {"time": 2.1333}], "translate": [{"time": 0.2, "curve": [0.308, 0, 0.525, -18.37, 0.308, 0, 0.525, -9.19]}, {"time": 0.6333, "x": -18.37, "y": -9.19, "curve": [0.9, -18.37, 1.433, 0, 0.9, -9.19, 1.433, 0]}, {"time": 1.7}]}, "tail1": {"rotate": [{"curve": [0.098, 0.13, 0.204, -14.76]}, {"time": 0.2667, "value": -14.69, "curve": [0.453, -14.49, 0.578, 19.55]}, {"time": 0.9333, "value": 19.85, "curve": [1.08, 19.98, 1.328, 16.46]}, {"time": 1.4333, "value": 9.05, "curve": [1.532, 2.13, 1.565, -5.76]}, {"time": 1.7, "value": -5.74, "curve": [1.739, -5.73, 1.771, 1.08]}, {"time": 1.8, "value": 1.06, "curve": [1.853, 1.04, 1.956, -1.66]}, {"time": 2, "value": -1.81, "curve": [2.039, -1.95, 2.08, -0.07]}, {"time": 2.1333}]}, "tail3": {"rotate": [{"value": -4.66, "curve": [0.053, -3.48, 0.21, 0.22]}, {"time": 0.2667, "value": 0.23, "curve": [0.331, 0.24, 0.337, -16.21]}, {"time": 0.4333, "value": -16.22, "curve": [0.521, -16.24, 0.565, -5.72]}, {"time": 0.6667, "value": -5.73, "curve": [0.743, -5.73, 0.805, -9.29]}, {"time": 0.8333, "value": -9.89, "curve": [0.878, -10.85, 0.93, -11.71]}, {"time": 1, "value": -11.85, "curve": [1.032, -11.91, 1.065, -10.91]}, {"time": 1.1, "value": -10.92, "curve": [1.142, -10.93, 1.19, -11.14]}, {"time": 1.2333, "value": -11.21, "curve": [1.268, -11.27, 1.296, -10.85]}, {"time": 1.3333, "value": -10.84, "curve": [1.389, -10.83, 1.422, -12.91]}, {"time": 1.4667, "value": -12.9, "curve": [1.523, -12.89, 1.594, 0.83]}, {"time": 1.6667, "value": 0.91, "curve": [1.722, 0.97, 1.763, -1.01]}, {"time": 1.8, "value": -2.92, "curve": [1.837, -4.84, 1.889, -7.19]}, {"time": 1.9667, "value": -7.21, "curve": [2.039, -7.24, 2.081, -5.7]}, {"time": 2.1333, "value": -4.66}]}, "tail5": {"rotate": [{"value": 5.3, "curve": [0.036, 7.51, 0.233, 22.33]}, {"time": 0.2667, "value": 22.33, "curve": [0.325, 22.33, 0.414, -17.46]}, {"time": 0.5, "value": -17.34, "curve": [0.554, -17.27, 0.622, -8.56]}, {"time": 0.6667, "value": -3.3, "curve": [0.68, -1.76, 0.718, 2.89]}, {"time": 0.8, "value": 2.99, "curve": [0.87, 3.07, 0.878, -6.27]}, {"time": 0.9333, "value": -6.3, "curve": [0.953, -6.31, 1.022, -0.18]}, {"time": 1.0667, "value": -0.19, "curve": [1.141, -0.21, 1.247, -1.67]}, {"time": 1.3333, "value": -1.68, "curve": [1.445, -1.69, 1.533, 6.2]}, {"time": 1.6333, "value": 6.15, "curve": [1.783, 6.09, 1.796, 2.3]}, {"time": 1.9333, "value": 2.27, "curve": [2.022, 2.41, 2.09, 3.96]}, {"time": 2.1333, "value": 5.3}]}, "tail7": {"rotate": [{"value": -10.89, "curve": [0.024, -9.39, 0.083, 2.54]}, {"time": 0.1, "value": 5.38, "curve": [0.12, 8.69, 0.201, 19.39]}, {"time": 0.2667, "value": 19.39, "curve": [0.326, 19.38, 0.373, 10.75]}, {"time": 0.4, "value": 6.64, "curve": [0.425, 2.85, 0.527, -17.3]}, {"time": 0.6, "value": -17.31, "curve": [0.729, -17.34, 0.751, 6.3]}, {"time": 0.8, "value": 6.38, "curve": [0.868, 6.5, 0.914, -11.69]}, {"time": 0.9667, "value": -11.4, "curve": [0.995, -11.24, 1.044, -1.32]}, {"time": 1.1, "value": -1.44, "curve": [1.143, -1.54, 1.18, -10.37]}, {"time": 1.2667, "value": -10.23, "curve": [1.295, -10.19, 1.34, -3.84]}, {"time": 1.4333, "value": -4.1, "curve": [1.553, -4.43, 1.585, -16.61]}, {"time": 1.6333, "value": -16.61, "curve": [1.688, -16.61, 1.696, 23.74]}, {"time": 1.7333, "value": 23.71, "curve": [1.798, 23.66, 1.87, 16.5]}, {"time": 1.9, "value": 10.29, "curve": [1.923, 5.57, 1.978, -13.06]}, {"time": 2.0667, "value": -13.03, "curve": [2.081, -13.06, 2.106, -12.38]}, {"time": 2.1333, "value": -10.89}]}, "tail9": {"rotate": [{"value": -10.89, "curve": [0.033, -10.89, 0.109, -16.1]}, {"time": 0.1667, "value": -15.69, "curve": [0.2, -15.45, 0.263, 24]}, {"time": 0.3, "value": 23.95, "curve": [0.341, 23.91, 0.38, 20.42]}, {"time": 0.4, "value": 13.72, "curve": [0.436, 1.64, 0.469, -22.42]}, {"time": 0.5667, "value": -22.06, "curve": [0.592, -21.97, 0.619, -12.65]}, {"time": 0.6333, "value": -12.97, "curve": [0.649, -13.32, 0.672, -23.19]}, {"time": 0.7333, "value": -23.77, "curve": [0.753, -23.95, 0.784, 18.1]}, {"time": 0.8333, "value": 18.04, "curve": [0.896, 17.97, 0.968, -8.44]}, {"time": 1.0667, "value": -8.3, "curve": [1.102, -8.25, 1.147, 1.32]}, {"time": 1.2, "value": 1.29, "curve": [1.225, 1.28, 1.264, -9.04]}, {"time": 1.3333, "value": -9.07, "curve": [1.359, -9.08, 1.367, -0.8]}, {"time": 1.4, "value": -0.93, "curve": [1.45, -1.14, 1.485, -13.55]}, {"time": 1.5333, "value": -13.67, "curve": [1.589, -13.81, 1.592, -4.95]}, {"time": 1.6333, "value": -4.9, "curve": [1.659, -4.87, 1.672, -6.82]}, {"time": 1.7, "value": -6.85, "curve": [1.732, -6.89, 1.741, 23.58]}, {"time": 1.8, "value": 23.61, "curve": [1.855, 23.65, 1.942, -2.54]}, {"time": 1.9667, "value": -7.74, "curve": [1.979, -10.39, 2.03, -19.23]}, {"time": 2.0667, "value": -19.56, "curve": [2.092, -19.79, 2.116, -10.89]}, {"time": 2.1333, "value": -10.89}]}, "tongue2": {"rotate": [{"curve": [0.042, 0, 0.192, -3.18]}, {"time": 0.2, "value": -3.18, "curve": [0.217, -3.18, 0.25, 52.29]}, {"time": 0.2667, "value": 52.29, "curve": [0.333, 52.29, 0.506, 33.61]}, {"time": 0.5333, "value": 17.71, "curve": [0.545, 10.61, 0.575, -38.38]}, {"time": 0.6, "value": -63.26, "curve": [0.613, -75.71, 0.625, -74.25]}, {"time": 0.6333, "value": -74.25, "curve": [0.65, -74.25, 0.666, -50.57]}, {"time": 0.7, "value": -42.92, "curve": [0.715, -39.42, 0.803, -33.62]}, {"time": 0.8333, "value": -32.97, "curve": [0.884, -31.92, 0.934, -31.56]}, {"time": 0.9667, "value": -31.29, "curve": [1.012, -30.9, 1.051, -29.92]}, {"time": 1.0667, "value": -28.95, "curve": [1.105, -26.65, 1.142, -20.34]}, {"time": 1.1667, "value": -20.34, "curve": [1.192, -20.34, 1.242, -24.26]}, {"time": 1.2667, "value": -24.26, "curve": [1.283, -24.26, 1.317, -19.87]}, {"time": 1.3333, "value": -19.87, "curve": [1.358, -19.87, 1.428, -21.71]}, {"time": 1.4333, "value": -22.11, "curve": [1.481, -25.54, 1.508, -36.17]}, {"time": 1.5333, "value": -36.17, "curve": [1.575, -36.17, 1.648, -17.58]}, {"time": 1.7, "value": -13.32, "curve": [1.748, -9.35, 1.861, -3.44]}, {"time": 1.9333, "value": -1.54, "curve": [1.975, -0.45, 2.083, 0]}, {"time": 2.1333}], "translate": [{"time": 0.2, "curve": [0.283, 0, 0.423, -0.54, 0.283, 0, 0.435, 0.96]}, {"time": 0.5333, "x": 6.19, "y": 1.39, "curve": [0.567, 8.27, 0.633, 45.26, 0.555, 1.49, 0.62, 1.67]}, {"time": 0.6667, "x": 57.07, "y": 1.67, "curve": [0.706, 70.68, 0.76, 94.14, 0.729, 1.67, 0.764, 1.67]}, {"time": 0.8333, "x": 94.09, "y": 1.67, "curve": [1.21, 93.82, 1.098, 7.05, 1.195, 1.67, 1.676, 1.67]}, {"time": 1.9333, "x": 6.19, "y": 1.67, "curve": [1.983, 6.14, 2.083, 0, 1.983, 1.67, 2.083, 0]}, {"time": 2.1333}]}, "tongue3": {"rotate": [{"curve": [0.042, 0, 0.142, -1.29]}, {"time": 0.2, "value": 4.21, "curve": [0.209, 5.09, 0.25, 52.29]}, {"time": 0.2667, "value": 52.29, "curve": [0.333, 52.29, 0.491, 43.05]}, {"time": 0.5333, "value": 17.71, "curve": [0.551, 6.94, 0.577, -41.44]}, {"time": 0.6, "value": -50.22, "curve": [0.613, -55.02, 0.625, -24.1]}, {"time": 0.6333, "value": -24.1, "curve": [0.65, -24.1, 0.683, 52.67]}, {"time": 0.7, "value": 52.67, "curve": [0.733, 52.67, 0.8, -50.91]}, {"time": 0.8333, "value": -50.91, "curve": [0.867, -50.91, 0.933, 36.54]}, {"time": 0.9667, "value": 36.54, "curve": [0.992, 36.54, 1.042, -47.34]}, {"time": 1.0667, "value": -47.34, "curve": [1.092, -47.34, 1.142, 5.53]}, {"time": 1.1667, "value": 5.53, "curve": [1.192, 5.53, 1.242, -19.25]}, {"time": 1.2667, "value": -19.94, "curve": [1.298, -20.82, 1.313, -13.2]}, {"time": 1.3333, "value": -2.03, "curve": [1.368, 17.44, 1.408, 54.04]}, {"time": 1.4333, "value": 54.04, "curve": [1.458, 54.04, 1.519, 43.79]}, {"time": 1.5333, "value": 35.49, "curve": [1.581, 8.1, 1.658, -36.24]}, {"time": 1.7, "value": -36.24, "curve": [1.758, -36.24, 1.875, 13.61]}, {"time": 1.9333, "value": 13.61, "curve": [1.983, 13.61, 2.083, 0]}, {"time": 2.1333}], "translate": [{"time": 0.2, "curve": [0.283, 0, 0.45, 11.48, 0.283, 0, 0.45, 3.7]}, {"time": 0.5333, "x": 11.48, "y": 3.7}, {"time": 2.1333}]}, "saddle-strap-back": {"rotate": [{"value": -2.53, "curve": [0.079, -2.45, 0.302, -25.1]}, {"time": 0.3333, "value": -25.81, "curve": [0.389, -27.07, 0.484, -26.23]}, {"time": 0.5333, "value": -26.79, "curve": [0.561, -27.1, 0.608, -31.99]}, {"time": 0.6333, "value": -31.97, "curve": [0.725, -31.89, 0.763, -10.53]}, {"time": 0.8333, "value": -7.75, "curve": [0.911, -4.71, 1.308, -5.82]}, {"time": 1.4667, "value": -5.82, "curve": [1.541, -5.82, 1.632, -4.15]}, {"time": 1.7, "value": -4.14, "curve": [1.766, -4.13, 1.845, -11.09]}, {"time": 1.9333, "value": -11.09, "curve": [1.983, -11.09, 2.083, -2.53]}, {"time": 2.1333, "value": -2.53}], "translate": [{"curve": [0.083, 0, 0.25, 11.69, 0.083, 0, 0.25, -2.81]}, {"time": 0.3333, "x": 11.69, "y": -2.81, "curve": [0.408, 11.69, 0.558, 0, 0.408, -2.81, 0.558, 0]}, {"time": 0.6333}], "scale": [{"time": 0.6333, "curve": [0.65, 1, 0.666, 1.076, 0.65, 1, 0.683, 1]}, {"time": 0.7, "x": 1.103, "curve": [0.737, 1.132, 0.8, 1.162, 0.733, 1, 0.8, 1]}, {"time": 0.8333, "x": 1.162, "curve": [1.05, 1.162, 1.483, 1, 1.05, 1, 1.483, 1]}, {"time": 1.7}]}, "front-foot-target": {"rotate": [{"curve": [0.074, -0.1, 0.093, -16.93]}, {"time": 0.1, "value": -19.19, "curve": [0.117, -24.39, 0.157, -24.35]}, {"time": 0.2, "value": -24.36, "curve": [0.252, -24.36, 0.311, -22.81]}, {"time": 0.4333, "value": -22.8, "curve": [0.628, -22.79, 0.776, -25.56]}, {"time": 1.2333, "value": -25.57, "curve": [1.378, -25.58, 1.525, 2.24]}, {"time": 1.7333, "value": 2.24, "curve": [1.797, 2.48, 1.732, -17.39]}, {"time": 1.8, "value": -17.18, "curve": [1.923, -16.81, 2.073, 0.17]}, {"time": 2.1333}], "translate": [{"y": 1.27, "curve": [0.027, -0.41, 0.053, -46.34, 0.023, 3.93, 0.05, 71.17]}, {"time": 0.0667, "x": -73.27, "y": 71.17, "curve": [0.088, -115.35, 0.169, -316.46, 0.103, 71.88, 0.17, 60.5]}, {"time": 0.2, "x": -392.1, "y": 9.43, "curve": [0.231, -419.09, 0.231, -422.04, 0.231, 9.43, 0.231, 9.43]}, {"time": 0.2667, "x": -422.04, "y": 9.43, "curve": "stepped"}, {"time": 0.6, "x": -422.04, "y": 9.43, "curve": [0.643, -422.04, 0.688, -403.61, 0.643, 9.43, 0.688, 9.43]}, {"time": 0.7333, "x": -403.61, "y": 9.43, "curve": "stepped"}, {"time": 1.7333, "x": -403.61, "y": 9.43, "curve": [1.769, -403.61, 1.793, -341.64, 1.768, 9.43, 1.783, 111.02]}, {"time": 1.8, "x": -308.05, "y": 111.02, "curve": [1.813, -248.48, 1.9, 0, 1.858, 109.79, 1.928, 38.95]}, {"time": 1.9333, "curve": [1.983, 0, 2.083, 0.76, 1.983, 0, 2.122, 0]}, {"time": 2.1333, "y": 1.27}]}, "front-foot2": {"rotate": [{}, {"time": 0.0333, "value": -17.85}, {"time": 0.0667, "value": -27.51}, {"time": 0.1, "value": -21.45}, {"time": 0.1333, "value": -12.42}, {"time": 0.1667, "value": -1.36}, {"time": 0.2, "value": -17.5}, {"time": 0.6, "value": -17.43}, {"time": 0.7, "value": -21.13, "curve": "stepped"}, {"time": 1.7333, "value": -21.13}, {"time": 1.8, "value": -41.83}, {"time": 1.8667, "value": -1.1}, {"time": 1.9333}]}, "front-foot3": {"rotate": [{}, {"time": 0.0333, "value": 1.2}, {"time": 0.1, "value": 8.93}, {"time": 0.2, "value": 26.93, "curve": [0.217, 26.93, 0.224, 25.26]}, {"time": 0.2333, "value": 16.32, "curve": "stepped"}, {"time": 1.7333, "value": 16.32}, {"time": 1.7667, "value": 24.81}, {"time": 1.8, "value": -12.37}, {"time": 1.8333, "value": -17.66}, {"time": 1.9, "value": 9.39}, {"time": 1.9333, "value": 16.11}, {"time": 2}], "translate": [{"x": -29.67, "curve": "stepped"}, {"time": 1.7333, "x": -29.67}, {"time": 1.8, "x": -34.03, "y": 8.95}, {"time": 2.1333, "x": -29.67}]}, "back-hand2": {"rotate": [{"value": -21.46}, {"time": 0.2, "value": -21.93}, {"time": 0.3, "value": -8.39}, {"time": 0.4333, "value": -5.38}, {"time": 0.5333, "value": -30.38}, {"time": 0.6333, "value": -55.56}, {"time": 0.7, "value": -18.74}, {"time": 0.7667, "value": -20.26}, {"time": 0.8333, "value": -21.46}]}, "saddle": {"rotate": [{"value": -3.18, "curve": [0.042, -3.18, 0.132, -3.3]}, {"time": 0.1667, "value": -3.04, "curve": [0.278, -2.2, 0.517, 8.87]}, {"time": 0.6333, "value": 8.87, "curve": [0.683, 8.87, 0.772, -1.92]}, {"time": 0.8333, "value": -2.37, "curve": [0.9, -2.86, 1.258, -3.18]}, {"time": 1.4, "value": -3.18, "curve": [1.458, -3.18, 1.575, -7.06]}, {"time": 1.6333, "value": -7.06, "curve": [1.715, -7.06, 1.86, -0.12]}, {"time": 1.9667, "value": -0.07, "curve": [2.05, -0.03, 2.09, -3.18]}, {"time": 2.1333, "value": -3.18}], "translate": [{"x": 12.68, "y": -2.67, "curve": [0.042, 12.68, 0.125, 22.76, 0.042, -2.67, 0.125, -3.36]}, {"time": 0.1667, "x": 22.76, "y": -3.36, "curve": [0.283, 22.76, 0.517, 9.26, 0.283, -3.36, 0.517, -0.42]}, {"time": 0.6333, "x": 9.26, "y": -0.42, "curve": [0.658, 9.26, 0.708, 12.68, 0.658, -0.42, 0.708, -2.67]}, {"time": 0.7333, "x": 12.68, "y": -2.67}]}, "saddle-strap-front": {"rotate": [{"value": -0.37, "curve": [0.067, -0.37, 0.2, -0.95]}, {"time": 0.2667, "value": -0.95, "curve": [0.325, -0.95, 0.466, -15.05]}, {"time": 0.5, "value": -17.95, "curve": [0.567, -23.58, 0.6, -27.12]}, {"time": 0.6333, "value": -27.12, "curve": [0.658, -27.12, 0.688, -23.12]}, {"time": 0.7333, "value": -18.56, "curve": [0.75, -16.89, 0.803, -13.87]}, {"time": 0.8333, "value": -12.02, "curve": [0.866, -10.04, 0.97, -6.28]}, {"time": 1.0667, "value": -6.13, "curve": [1.133, -6.02, 1.204, -10.67]}, {"time": 1.2667, "value": -10.07, "curve": [1.394, -8.87, 1.514, -1.13]}, {"time": 1.6, "value": -1.13, "curve": [1.675, -1.13, 1.825, -6.56]}, {"time": 1.9, "value": -6.68, "curve": [1.952, -6.76, 1.982, -6.39]}, {"time": 2, "value": -5.97, "curve": [2.033, -5.22, 2.1, -0.37]}, {"time": 2.1333, "value": -0.37}], "translate": [{"curve": [0.125, 0, 0.375, -17.92, 0.125, 0, 0.375, -1.75]}, {"time": 0.5, "x": -17.92, "y": -1.75, "curve": [0.589, -17.92, 0.707, -7.06, 0.589, -1.75, 0.707, -4.51]}, {"time": 0.8333, "x": -2.9, "y": -6.57, "curve": [1.102, 5.81, 1.414, 11.49, 1.102, -10.87, 1.414, -16.3]}, {"time": 1.6, "x": 11.49, "y": -16.3, "curve": [1.733, 11.49, 2, 0, 1.733, -16.3, 2, 0]}, {"time": 2.1333}]}, "spineboy-back-arm-target": {"translate": [{}, {"time": 0.6333, "x": 56.17, "y": -58.56}, {"time": 0.8333, "x": 34.47, "y": -59.19}, {"time": 2.1333}]}, "tail2": {"rotate": [{"value": -4.33, "curve": [0.027, -1.91, 0.124, 3.51]}, {"time": 0.1667, "value": 6.45, "curve": [0.211, 9.51, 0.278, 10.89]}, {"time": 0.3333, "value": 10.99, "curve": [0.392, 11.09, 0.385, -13.61]}, {"time": 0.5, "value": -13.56, "curve": [0.614, -13.52, 0.636, -5.75]}, {"time": 0.7, "value": -5.76, "curve": [0.787, -5.77, 0.831, -10.17]}, {"time": 0.9667, "value": -10.46, "curve": [1.072, -10.68, 1.279, -8.29]}, {"time": 1.3333, "value": -6.23, "curve": [1.389, -4.11, 1.509, 5.53]}, {"time": 1.5333, "value": 9.75, "curve": [1.551, 12.82, 1.619, 27.3]}, {"time": 1.6667, "value": 27.26, "curve": [1.734, 27.21, 1.763, 9.86]}, {"time": 1.8, "value": 5.3, "curve": [1.836, 0.84, 1.926, -9.62]}, {"time": 2, "value": -9.49, "curve": [2.071, -9.36, 2.091, -7.25]}, {"time": 2.1333, "value": -4.33}], "translate": [{"curve": [0.133, 0, 0.435, 3.44, 0.133, 0, 0.4, 0]}, {"time": 0.5333, "x": 11.88, "curve": [0.618, 19.17, 0.84, 41.3, 0.575, 0, 0.848, 4.02]}, {"time": 0.9667, "x": 41.27, "y": 4.03, "curve": [1.78, 41.12, 1.763, 0.25, 1.213, 4.03, 1.848, 0.08]}, {"time": 2.1333}]}, "tail4": {"rotate": [{"value": 13.37, "curve": [0.033, 13.37, 0.101, 11.83]}, {"time": 0.1333, "value": 11.78, "curve": [0.201, 11.66, 0.233, 16.73]}, {"time": 0.2667, "value": 16.73, "curve": [0.289, 16.73, 0.316, 16.02]}, {"time": 0.3333, "value": 13.79, "curve": [0.391, 6.36, 0.456, -4.32]}, {"time": 0.4667, "value": -5.61, "curve": [0.482, -7.52, 0.499, -10.14]}, {"time": 0.5667, "value": -10.33, "curve": [0.576, -10.36, 0.616, -10.01]}, {"time": 0.6667, "value": -8.03, "curve": [0.781, -3.75, 0.935, 3.38]}, {"time": 1, "value": 4.13, "curve": [1.033, 4.52, 1.063, 4.54]}, {"time": 1.1333, "value": 4.55, "curve": [1.176, 4.56, 1.221, 4.12]}, {"time": 1.2667, "value": 4.18, "curve": [1.349, 4.28, 1.417, 4.99]}, {"time": 1.4333, "value": 5.83, "curve": [1.466, 7.48, 1.557, 19.49]}, {"time": 1.6333, "value": 19.57, "curve": [1.775, 19.71, 1.857, 7.44]}, {"time": 1.9667, "value": 7.46, "curve": [2.045, 7.48, 2.092, 13.37]}, {"time": 2.1333, "value": 13.37}]}, "tail6": {"rotate": [{"value": 5.3, "curve": [0.026, 7.49, 0.15, 12.9]}, {"time": 0.2, "value": 17.27, "curve": [0.251, 21.71, 0.291, 24.53]}, {"time": 0.3333, "value": 24.59, "curve": [0.403, 24.69, 0.376, -22.01]}, {"time": 0.5333, "value": -21.87, "curve": [0.601, -21.81, 0.697, -13.33]}, {"time": 0.7333, "value": -10.11, "curve": [0.748, -8.78, 0.863, 4.36]}, {"time": 0.9333, "value": 4.22, "curve": [0.974, 4.13, 1.063, 1.22]}, {"time": 1.1, "value": 1.32, "curve": [1.145, 1.44, 1.196, 6.12]}, {"time": 1.2333, "value": 6.08, "curve": [1.279, 6.03, 1.324, -4.69]}, {"time": 1.4333, "value": -5.05, "curve": [1.505, -5.28, 1.608, -1.45]}, {"time": 1.6333, "value": 0.1, "curve": [1.647, 0.95, 1.722, 20.39]}, {"time": 1.8, "value": 20.34, "curve": [1.888, 20.3, 1.878, -3.6]}, {"time": 2, "value": -3.58, "curve": [2.055, -3.58, 2.121, 3.57]}, {"time": 2.1333, "value": 5.3}]}, "tail8": {"rotate": [{"value": -10.89, "curve": [0.081, -11.05, 0.147, 13.08]}, {"time": 0.1667, "value": 15.43, "curve": [0.186, 17.83, 0.268, 21.77]}, {"time": 0.3333, "value": 21.83, "curve": [0.472, 21.96, 0.454, -21.07]}, {"time": 0.5, "value": -21.25, "curve": [0.544, -20.58, 0.551, -10.78]}, {"time": 0.6, "value": -10.88, "curve": [0.618, -10.92, 0.651, -17.9]}, {"time": 0.6667, "value": -17.69, "curve": [0.694, -17.33, 0.795, 1.24]}, {"time": 0.8333, "value": 3.93, "curve": [0.858, 5.72, 0.886, 7.58]}, {"time": 0.9333, "value": 7.55, "curve": [0.982, 7.53, 0.948, -9.49]}, {"time": 1.0333, "value": -9.59, "curve": [1.083, -9.64, 1.1, -4.47]}, {"time": 1.1333, "value": -4.45, "curve": [1.195, -4.4, 1.198, -11.22]}, {"time": 1.2667, "value": -11.14, "curve": [1.314, -11.08, 1.359, -8.01]}, {"time": 1.4667, "value": -8.41, "curve": [1.523, -8.63, 1.603, -26.32]}, {"time": 1.6667, "value": -26.16, "curve": [1.702, -26.06, 1.729, 19.38]}, {"time": 1.8, "value": 19.14, "curve": [1.862, 18.93, 1.956, -1.69]}, {"time": 1.9667, "value": -3.18, "curve": [2.006, -8.48, 2.092, -10.76]}, {"time": 2.1333, "value": -10.89}]}, "tail10": {"rotate": [{"value": -10.89, "curve": [0.033, -10.89, 0.111, -30.73]}, {"time": 0.1667, "value": -30.9, "curve": [0.229, -31.08, 0.335, 27.4]}, {"time": 0.4, "value": 27.6, "curve": [0.439, 27.72, 0.524, -27.93]}, {"time": 0.5667, "value": -27.88, "curve": [0.62, -27.81, 0.664, -24.25]}, {"time": 0.7, "value": -20.93, "curve": [0.734, -17.79, 0.814, 5.46]}, {"time": 0.8333, "value": 13.09, "curve": [0.842, 16.62, 0.87, 28.27]}, {"time": 0.9, "value": 28.51, "curve": [0.947, 28.9, 0.985, -19.51]}, {"time": 1, "value": -22.12, "curve": [1.01, -23.84, 1.038, -31.09]}, {"time": 1.0667, "value": -31.14, "curve": [1.103, -31.22, 1.133, 19.55]}, {"time": 1.1667, "value": 19.35, "curve": [1.201, 19.15, 1.213, -14.84]}, {"time": 1.3, "value": -14.94, "curve": [1.347, -14.99, 1.37, 11.37]}, {"time": 1.4, "value": 11.31, "curve": [1.432, 11.24, 1.523, -27.59]}, {"time": 1.5333, "value": -31.27, "curve": [1.565, -42.53, 1.588, -42.01]}, {"time": 1.6, "value": -42.01, "curve": [1.64, -41.99, 1.753, -25.24]}, {"time": 1.7667, "value": -18.25, "curve": [1.778, -12.25, 1.82, 14.24]}, {"time": 1.9, "value": 14.82, "curve": [1.929, 14.81, 2.041, -14.3]}, {"time": 2.0667, "value": -14.15, "curve": [2.096, -13.98, 2.11, -10.93]}, {"time": 2.1333, "value": -10.89}]}, "jaw-inside": {"rotate": [{}, {"time": 0.7667, "value": 13.77}, {"time": 2.1333}], "translate": [{}, {"time": 0.6, "x": -4.3, "y": 4.48}, {"time": 0.7667, "x": -23.59, "y": -9.32}, {"time": 2.1333}], "scale": [{"time": 0.6}, {"time": 0.7667, "x": 1.119}, {"time": 2.1333}]}, "bone": {"rotate": [{"curve": [0.042, 0, 0.125, 6.63]}, {"time": 0.1667, "value": 6.63, "curve": [0.192, 6.63, 0.242, 2.54]}, {"time": 0.2667, "value": 2.54, "curve": [0.308, 2.54, 0.406, 11.71]}, {"time": 0.4333, "value": 12.45, "curve": [0.491, 14, 0.6, 14.87]}, {"time": 0.7, "value": 14.69, "curve": [0.725, 14.64, 0.775, -1.4]}, {"time": 0.8, "value": -1.4, "curve": [0.825, -1.4, 0.887, 5.02]}, {"time": 0.9, "value": 5.61, "curve": [0.936, 7.29, 1, 9.11]}, {"time": 1.0333, "value": 9.11, "curve": [1.058, 9.11, 1.108, 6.88]}, {"time": 1.1333, "value": 6.88, "curve": [1.158, 6.88, 1.208, 10.51]}, {"time": 1.2333, "value": 10.51, "curve": [1.258, 10.51, 1.308, 6.4]}, {"time": 1.3333, "value": 6.4, "curve": [1.358, 6.4, 1.408, 14.34]}, {"time": 1.4333, "value": 14.34, "curve": [1.45, 14.34, 1.496, 0.94]}, {"time": 1.5, "value": 0.49, "curve": [1.525, -2.03, 1.575, -5.85]}, {"time": 1.6, "value": -5.85, "curve": [1.625, -5.85, 1.675, 4.87]}, {"time": 1.7, "value": 4.87, "curve": [1.717, 4.87, 1.75, -1.8]}, {"time": 1.7667, "value": -1.8, "curve": [1.817, -1.8, 1.917, 13.56]}, {"time": 1.9667, "value": 13.56, "curve": [2.008, 13.56, 2.092, 0]}, {"time": 2.1333}]}, "bone2": {"rotate": [{"curve": [0.042, 0, 0.146, -2.69]}, {"time": 0.1667, "value": -4.33, "curve": [0.219, -8.51, 0.262, -16.86]}, {"time": 0.2667, "value": -17.98, "curve": [0.308, -27.29, 0.392, -48.51]}, {"time": 0.4333, "value": -48.51, "curve": [0.5, -48.51, 0.633, 6.49]}, {"time": 0.7, "value": 6.49, "curve": [0.725, 6.49, 0.765, 3.58]}, {"time": 0.8, "value": -1.4, "curve": [0.848, -8.2, 0.875, -10.86]}, {"time": 0.9, "value": -10.86, "curve": [0.933, -10.86, 1, -4.95]}, {"time": 1.0333, "value": -4.95, "curve": [1.058, -4.95, 1.108, -14.48]}, {"time": 1.1333, "value": -14.48, "curve": [1.158, -14.48, 1.208, -10.43]}, {"time": 1.2333, "value": -10.43, "curve": [1.258, -10.43, 1.308, -28.38]}, {"time": 1.3333, "value": -28.38, "curve": [1.358, -28.38, 1.43, -14.4]}, {"time": 1.4333, "value": -13.27, "curve": [1.45, -7.85, 1.483, 0.49]}, {"time": 1.5, "value": 0.49, "curve": [1.525, 0.49, 1.594, -14.84]}, {"time": 1.6, "value": -15.95, "curve": [1.637, -22.9, 1.675, -38.34]}, {"time": 1.7, "value": -38.34, "curve": [1.717, -38.34, 1.749, -24.54]}, {"time": 1.7667, "value": -21.29, "curve": [1.81, -13.5, 1.902, -4.2]}, {"time": 1.9667, "value": 2.36, "curve": [2, 5.78, 2.022, 7.84]}, {"time": 2.0667, "value": 8.12, "curve": [2.1, 8.33, 2.1, 0]}, {"time": 2.1333}]}, "bone3": {"rotate": [{"curve": [0.025, 0, 0.064, -0.37]}, {"time": 0.1, "value": -2.21, "curve": [0.125, -3.49, 0.155, -5.64]}, {"time": 0.1667, "value": -6.06, "curve": [0.208, -7.52, 0.242, -7.08]}, {"time": 0.2667, "value": -7.08, "curve": [0.375, -7.08, 0.558, 14.69]}, {"time": 0.6667, "value": 14.69, "curve": [0.692, 14.69, 0.775, -1.4]}, {"time": 0.8, "value": -1.4, "curve": [0.817, -1.4, 0.883, 3.9]}, {"time": 0.9, "value": 3.9, "curve": [0.939, 3.9, 0.98, 3.03]}, {"time": 1.0333, "value": 2.98, "curve": [1.076, 2.94, 1.149, 3.42]}, {"time": 1.1667, "value": 3.92, "curve": [1.185, 4.42, 1.222, 5.94]}, {"time": 1.2667, "value": 5.9, "curve": [1.299, 5.87, 1.305, 0.43]}, {"time": 1.3667, "value": 0.38, "curve": [1.432, 0.32, 1.437, 2.12]}, {"time": 1.4667, "value": 2.12, "curve": [1.491, 2.12, 1.514, -6.82]}, {"time": 1.5667, "value": -9.65, "curve": [1.582, -10.49, 1.616, -10.48]}, {"time": 1.6333, "value": -10.48, "curve": [1.65, -10.48, 1.725, -3.32]}, {"time": 1.7667, "value": -1.8, "curve": [1.786, -1.1, 1.932, 0.99]}, {"time": 1.9667, "value": 1.81, "curve": [1.989, 2.33, 2.006, 6.83]}, {"time": 2.0333, "value": 6.85, "curve": [2.065, 6.87, 2.109, 0]}, {"time": 2.1333}]}, "bone4": {"rotate": [{"curve": [0.025, 0, 0.064, 0.8]}, {"time": 0.1, "value": -1.04, "curve": [0.125, -2.32, 0.16, -5.18]}, {"time": 0.1667, "value": -5.78, "curve": [0.207, -9.38, 0.242, -15.67]}, {"time": 0.2667, "value": -15.67, "curve": [0.375, -15.67, 0.558, 14.69]}, {"time": 0.6667, "value": 14.69, "curve": [0.692, 14.69, 0.775, -1.4]}, {"time": 0.8, "value": -1.4, "curve": [0.817, -1.4, 0.883, 3.9]}, {"time": 0.9, "value": 3.9, "curve": [0.939, 3.9, 0.984, 2.94]}, {"time": 1.0333, "value": 3, "curve": [1.085, 3.06, 1.133, 3.41]}, {"time": 1.1667, "value": 4.6, "curve": [1.213, 6.21, 1.234, 8.82]}, {"time": 1.2667, "value": 8.27, "curve": [1.302, 7.7, 1.313, 0.66]}, {"time": 1.3667, "value": 0.43, "curve": [1.413, 0.24, 1.437, 3]}, {"time": 1.4667, "value": 3, "curve": [1.491, 3, 1.515, -15.25]}, {"time": 1.5667, "value": -15.03, "curve": [1.601, -14.89, 1.626, -12.84]}, {"time": 1.6333, "value": -12.25, "curve": [1.674, -9, 1.75, -1.8]}, {"time": 1.7667, "value": -1.8, "curve": [1.784, -1.8, 1.804, -12.17]}, {"time": 1.8333, "value": -12.06, "curve": [1.872, -11.93, 1.935, -3.2]}, {"time": 1.9667, "value": 1.81, "curve": [1.986, 4.89, 2.008, 10.05]}, {"time": 2.0333, "value": 9.69, "curve": [2.07, 9.18, 2.088, 0]}, {"time": 2.1333}]}, "bone5": {"rotate": [{"curve": [0.025, 0, 0.062, 28.42]}, {"time": 0.1, "value": 28.6, "curve": [0.147, 28.83, 0.153, 11.26]}, {"time": 0.1667, "value": 7.14, "curve": [0.184, 2.13, 0.242, -3.09]}, {"time": 0.2667, "value": -3.09, "curve": [0.375, -3.09, 0.558, 14.69]}, {"time": 0.6667, "value": 14.69, "curve": [0.692, 14.69, 0.775, -1.4]}, {"time": 0.8, "value": -1.4, "curve": [0.817, -1.4, 0.883, 3.9]}, {"time": 0.9, "value": 3.9, "curve": [0.939, 3.9, 0.985, -10.79]}, {"time": 1.0333, "value": -11.13, "curve": [1.076, -11.41, 1.131, 1.61]}, {"time": 1.1667, "value": 9.66, "curve": [1.176, 11.68, 1.223, 16.67]}, {"time": 1.2667, "value": 17.42, "curve": [1.291, 17.84, 1.325, 1.33]}, {"time": 1.3667, "value": 1.77, "curve": [1.4, 2.12, 1.437, 13.5]}, {"time": 1.4667, "value": 13.5, "curve": [1.491, 13.5, 1.53, 12.52]}, {"time": 1.5667, "value": 6.34, "curve": [1.592, 2.04, 1.625, -8.5]}, {"time": 1.6333, "value": -9.29, "curve": [1.668, -12.54, 1.752, -12.67]}, {"time": 1.7667, "value": -13.33, "curve": [1.796, -14.63, 1.798, -16.98]}, {"time": 1.8333, "value": -16.88, "curve": [1.885, -16.74, 1.93, -6.63]}, {"time": 1.9667, "value": 1.81, "curve": [1.989, 7.11, 2.008, 15.58]}, {"time": 2.0333, "value": 15.21, "curve": [2.07, 14.7, 2.109, 0]}, {"time": 2.1333}]}, "front-leg1": {"rotate": [{"value": -10.53}]}, "spineboy-front-foot-target": {"translate": [{"curve": [0.075, 0, 0.225, 12.33, 0.075, 0, 0.225, 10.25]}, {"time": 0.3, "x": 12.33, "y": 10.25, "curve": [0.758, 12.33, 1.675, 0, 0.758, 10.25, 1.675, 0]}, {"time": 2.1333}]}, "front-arm": {"rotate": [{"value": -14.35, "curve": [0.133, -14.35, 0.4, -19.58]}, {"time": 0.5333, "value": -19.58}], "translate": [{"curve": [0.033, 0, 0.1, -7.25, 0.033, 0, 0.1, -6.08]}, {"time": 0.1333, "x": -7.25, "y": -6.08, "curve": [0.167, -7.25, 0.233, -9.9, 0.167, -6.08, 0.233, -6.36]}, {"time": 0.2667, "x": -9.9, "y": -6.36, "curve": [0.292, -9.9, 0.342, -18.75, 0.292, -6.36, 0.342, -3.94]}, {"time": 0.3667, "x": -18.75, "y": -3.94, "curve": [0.442, -18.75, 0.592, -6.67, 0.442, -3.94, 0.621, -5.2]}, {"time": 0.6667, "x": -6.67, "y": -5.49, "curve": [0.766, -6.67, 0.886, -6.5, 0.906, -7.04, 0.862, -6.67]}, {"time": 1, "x": -6.32, "y": -7.71, "curve": [1.142, -6.09, 1.277, -5.84, 1.153, -8.88, 1.277, -10.04]}, {"time": 1.3667, "x": -5.84, "y": -10.04, "curve": [1.415, -5.84, 1.452, -5.89, 1.415, -10.04, 1.434, -9.81]}, {"time": 1.4667, "x": -5.93, "y": -9.67, "curve": [1.567, -6.22, 1.62, -6.67, 1.51, -9.48, 1.71, -9.28]}, {"time": 1.8, "x": -6.67, "y": -9.26, "curve": [1.883, -6.67, 2.05, 0, 1.883, -9.25, 2.05, 0]}, {"time": 2.1333}], "scale": [{"curve": [0.036, 1, 0.083, 1.024, 0.036, 1, 0.083, 1.001]}, {"time": 0.1333, "x": 1.024, "y": 1.001, "curve": [0.259, 1.024, 0.411, 1, 0.259, 1.001, 0.411, 1]}, {"time": 0.5, "curve": [0.575, 1, 0.592, 1.192, 0.575, 1, 0.592, 1]}, {"time": 0.6667, "x": 1.192, "curve": [0.75, 1.192, 0.858, 1.081, 0.75, 1, 0.858, 1]}, {"time": 0.9333, "x": 1.081, "curve": [1.017, 1.081, 0.917, 1.085, 1.017, 1, 0.917, 1]}, {"time": 1, "x": 1.085, "curve": [1.1, 1.085, 1.267, 1.087, 1.1, 1, 1.267, 1]}, {"time": 1.3667, "x": 1.087, "curve": [1.375, 1.087, 1.418, 1.125, 1.375, 1, 1.418, 1]}, {"time": 1.4667, "x": 1.162, "curve": [1.502, 1.188, 1.676, 1.354, 1.502, 1, 1.676, 1]}, {"time": 1.8, "x": 1.301, "curve": [1.841, 1.284, 2.109, 1, 1.841, 1, 2.109, 1]}, {"time": 2.1333}]}, "stirrup": {"rotate": [{}, {"time": 0.4333, "value": -19.63}, {"time": 0.8333, "value": -15.18}, {"time": 1.3333, "value": -5.96}, {"time": 1.7667, "value": -18.16}, {"time": 2.1333}], "translate": [{}, {"time": 0.4333, "x": -1.45, "y": 16.31, "curve": "stepped"}, {"time": 1, "x": -1.45, "y": 16.31}, {"time": 2.1333}]}, "back-arm": {"rotate": [{"value": 44.83}], "scale": [{"time": 0.4667}, {"time": 0.5667, "x": 1.088}, {"time": 0.6667, "x": 1.311}, {"time": 0.7667, "x": 1.211}, {"time": 1.2, "x": 1.008}, {"time": 1.5667, "x": 1.137}, {"time": 1.8333}]}, "back-bracer": {"rotate": [{"value": -108.62}]}, "back-leg1": {"rotate": [{"value": -8.96}]}, "back-leg2": {"rotate": [{"value": -19.65}]}, "back-leg3": {"rotate": [{"value": 19.47}]}, "back-foot1": {"rotate": [{"value": 1.53}]}, "back-thigh": {"rotate": [{"value": 6.63}]}, "back-knee": {"rotate": [{"value": -8.23}]}, "front-bracer": {"rotate": [{"value": 72.21}]}, "front-leg2": {"rotate": [{"value": -9.63}]}, "front-leg3": {"rotate": [{"value": 3.66}]}, "front-foot1": {"rotate": [{"value": 0.4}]}, "front-thigh": {"rotate": [{"value": 8.81}]}, "lower-leg": {"rotate": [{"value": -12.66}]}, "stirrup-strap1": {"rotate": [{"value": 5.25}]}, "stirrup-strap2": {"rotate": [{"value": -9.39}]}, "head-control": {"translate": [{"curve": [0.054, 0, 0.199, -1.25, 0.072, 1.29, 0.192, -16.81]}, {"time": 0.2333, "x": -7.11, "y": -16.81, "curve": [0.327, -23.29, 0.379, -49.73, 0.317, -16.81, 0.452, 14.65]}, {"time": 0.5667, "x": -48.04, "y": 14.37, "curve": [0.653, -47.8, 0.826, -35.85, 0.674, 14.1, 0.729, -25.53]}, {"time": 0.8333, "x": -35.79, "y": -25.53, "curve": [0.867, -35.49, 0.908, -35.48, 0.858, -25.53, 0.908, -15.45]}, {"time": 0.9333, "x": -35.48, "y": -15.45, "curve": [0.967, -35.48, 1.039, -37.97, 0.967, -15.45, 1.033, -22.04]}, {"time": 1.0667, "x": -39.2, "y": -22.04, "curve": [1.135, -42.29, 1.163, -43.43, 1.1, -22.04, 1.167, -6.45]}, {"time": 1.2, "x": -44.12, "y": -6.45, "curve": [1.24, -44.87, 1.325, -45.15, 1.242, -6.45, 1.287, -13.6]}, {"time": 1.3667, "x": -45.15, "y": -13.61, "curve": [1.417, -45.15, 1.517, -47.61, 1.448, -13.61, 1.509, -1.81]}, {"time": 1.5667, "x": -47.61, "y": -1.61, "curve": [1.617, -47.61, 1.671, -48.3, 1.653, -1.32, 1.756, -23.41]}, {"time": 1.7667, "x": -37.45, "y": -28.41, "curve": [1.791, -34.66, 1.814, -20.75, 1.796, -41.85, 1.804, -47.93]}, {"time": 1.8333, "x": -18.68, "y": -47.78, "curve": [1.92, -9.88, 1.961, -5.23, 1.906, -47.41, 1.952, -34.09]}, {"time": 2, "x": -3.41, "y": -22.62, "curve": [2.045, -1.31, 2.1, 0, 2.013, -19.49, 2.1, 0]}, {"time": 2.1333}]}, "leg-control": {"translate": [{"curve": [0.017, 0, 0.05, 0, 0.017, 0, 0.05, 102.43]}, {"time": 0.0667, "y": 102.43, "curve": [0.1, 0, 0.15, 0, 0.1, 102.43, 0.15, 97.83]}, {"time": 0.2, "y": 93.23, "curve": [0.223, 0, 0.246, 0, 0.223, 91.13, 0.246, -33.33]}, {"time": 0.2667, "y": -34.99, "curve": [0.292, 0, 0.315, 0, 0.292, -36.97, 0.315, 84.03]}, {"time": 0.3333, "y": 84.03, "curve": [0.35, 0, 0.383, 0, 0.35, 84.03, 0.383, 22.16]}, {"time": 0.4, "y": 22.16, "curve": [0.467, 0, 0.6, 11.4, 0.467, 22.16, 0.6, -252.93]}, {"time": 0.6667, "x": 11.4, "y": -252.93, "curve": [0.7, 11.4, 0.767, 11.4, 0.7, -252.93, 0.767, -174.39]}, {"time": 0.8, "x": 11.4, "y": -174.39, "curve": [0.825, 11.4, 0.875, 11.4, 0.825, -174.39, 0.875, -201.79]}, {"time": 0.9, "x": 11.4, "y": -201.79, "curve": [0.925, 11.4, 0.975, 11.4, 0.925, -201.79, 0.975, -174.33]}, {"time": 1, "x": 11.4, "y": -174.33, "curve": [1.033, 11.4, 1.1, 11.4, 1.033, -174.33, 1.1, -221.22]}, {"time": 1.1333, "x": 11.4, "y": -221.22, "curve": [1.167, 11.4, 1.233, 11.4, 1.167, -221.22, 1.233, -167.36]}, {"time": 1.2667, "x": 11.4, "y": -167.36, "curve": [1.308, 11.4, 1.392, 11.4, 1.308, -167.36, 1.392, -214.56]}, {"time": 1.4333, "x": 11.4, "y": -214.56, "curve": [1.508, 11.4, 1.725, 11.4, 1.508, -214.56, 1.725, 48.39]}, {"time": 1.8, "x": 11.4, "y": 48.39, "curve": [1.857, 11.4, 1.953, 6.13, 1.857, 48.39, 1.953, -139.31]}, {"time": 2.0333, "x": 2.76, "y": -153.62, "curve": [2.072, 1.2, 2.107, 0, 2.072, -160.25, 2.107, 0]}, {"time": 2.1333}]}}, "ik": {"front-leg-ik": [{"softness": 73.5, "bendPositive": false}], "spineboy-back-arm-ik": [{"softness": 15.4, "stretch": true}], "spineboy-front-arm-ik": [{"softness": 15, "stretch": true}], "spineboy-front-leg-ik": [{"softness": 19.9, "bendPositive": false}]}, "drawOrder": [{"offsets": [{"slot": "raptor-horn", "offset": 4}, {"slot": "front-thigh", "offset": -5}]}]}, "walk": {"slots": {"raptor-jaw-inside": {"rgba": [{"color": "646464ff"}, {"time": 0.6333, "color": "808080ff"}, {"time": 1.2667, "color": "646464ff"}]}, "tail-shadow": {"rgba": [{"color": "00000000", "curve": [0.158, 0, 0.442, 0, 0.158, 0, 0.442, 0, 0.158, 0, 0.442, 0, 0.158, 0, 0.441, 0.2]}, {"time": 0.6, "color": "00000033", "curve": [0.633, 0, 1.075, 0, 0.633, 0, 1.075, 0, 0.633, 0, 1.075, 0, 0.805, 0.2, 1.075, 0]}, {"time": 1.2333, "color": "00000000"}], "attachment": [{"name": "raptor-tail-shadow"}]}}, "bones": {"front-foot-target": {"rotate": [{"curve": [0.092, 0, 0.27, -32.53]}, {"time": 0.3667, "value": -51.26, "curve": [0.411, -59.91, 0.559, -84.04]}, {"time": 0.6333, "value": -84.29, "curve": [1.183, -83.79, 1.129, 0.25]}, {"time": 1.2667}], "translate": [{"x": 381.92, "y": 36.5}, {"time": 0.6333, "x": -134.72, "y": 37.42}, {"time": 0.7, "x": -171.19, "y": 126.07, "curve": [0.733, -171.19, 0.798, -142.14, 0.74, 182.63, 0.8, 238.01]}, {"time": 0.8333, "x": -70.41, "y": 238.01, "curve": [0.845, -46.13, 1.125, 418.07, 0.983, 235.62, 1.129, 141.43]}, {"time": 1.1667, "x": 418.07, "y": 115.18, "curve": [1.192, 418.07, 1.231, 404.25, 1.202, 89.98, 1.2, 84.96]}, {"time": 1.2667, "x": 381.92, "y": 36.5}]}, "hip": {"rotate": [{"value": -4.78, "curve": [0.033, -4.18, 0.05, -3.99]}, {"time": 0.0667, "value": -3.99, "curve": [0.142, -3.99, 0.292, -12.5]}, {"time": 0.3667, "value": -12.5, "curve": [0.477, -12.56, 0.584, -4.05]}, {"time": 0.7, "value": -3.99, "curve": [0.775, -3.89, 0.925, -12.5]}, {"time": 1, "value": -12.5, "curve": [1.067, -12.5, 1.216, -6.08]}, {"time": 1.2667, "value": -4.78}], "translate": [{"x": 161.93, "y": 34.05, "curve": [0.022, 155.35, 0.047, 146.41, 0.021, 20.48, 0.039, 1.4]}, {"time": 0.0667, "x": 146.25, "y": 1.68, "curve": [0.124, 146.16, 0.29, 209.86, 0.18, 2.76, 0.294, 142.08]}, {"time": 0.4, "x": 209.5, "y": 140.69, "curve": [0.562, 208.96, 0.624, 145.35, 0.537, 141.96, 0.63, 4.69]}, {"time": 0.7, "x": 145.67, "y": 2.06, "curve": [0.745, 145.86, 0.928, 209.19, 0.818, 3.05, 0.95, 138.66]}, {"time": 1.0333, "x": 209.19, "y": 139.3, "curve": [1.1, 209.19, 1.221, 175.02, 1.13, 140.4, 1.235, 58.97]}, {"time": 1.2667, "x": 161.93, "y": 34.05}]}, "back-foot-target": {"rotate": [{"value": -80.75, "curve": [0.198, -90.08, 0.228, -92.2]}, {"time": 0.3667, "value": -92.34, "curve": [0.42, -92.39, 0.517, -45.34]}, {"time": 0.5667, "value": -45.34, "curve": [0.655, -45.71, 1.097, -71.9]}, {"time": 1.2667, "value": -80.75}], "translate": [{"x": -187.61, "y": -15.47, "curve": [0.049, -223.65, 0.093, -245.68, 0.092, -17.15, 0.095, -1.05]}, {"time": 0.1333, "x": -245.95, "y": 31.3, "curve": [0.166, -245.38, 0.315, 6.48, 0.173, 64.21, 0.228, 125.64]}, {"time": 0.3667, "x": 86.5, "y": 124.4, "curve": [0.382, 110.05, 0.518, 358.22, 0.462, 123.17, 0.549, 44.45]}, {"time": 0.5667, "x": 361.92, "y": 29.91, "curve": [0.583, 361.92, 0.579, 373.34, 0.597, 4.36, 0.603, 0.79]}, {"time": 0.6333, "x": 316.72, "y": -26.42}, {"time": 1.2667, "x": -187.61, "y": -15.47}]}, "front-leg1": {"translate": [{"curve": [0.017, 0, 0.3, 0.01, 0.017, 0, 0.293, 13.2]}, {"time": 0.3667, "x": -0.27, "y": 13.9, "curve": [0.46, -0.66, 0.548, -1.22, 0.44, 14.6, 0.57, 15.77]}, {"time": 0.6333, "x": -2.25, "y": 16.95, "curve": [0.705, -3.11, 0.783, -4.75, 0.697, 18.15, 0.783, 31.94]}, {"time": 0.8333, "x": -4.75, "y": 31.94, "curve": [0.875, -4.75, 0.963, -4.5, 0.875, 31.94, 0.985, 27.76]}, {"time": 1, "x": -3.8, "y": 25.55, "curve": [1.07, -2.46, 1.2, 0, 1.033, 20.56, 1.2, 0]}, {"time": 1.2667}]}, "front-leg-target": {"translate": [{"x": -18.05, "y": -2.89, "curve": [0.135, -18.7, 0.286, -40.73, 0.171, -2.56, 0.416, -51.29]}, {"time": 0.5333, "x": -42.2, "y": -52.27, "curve": [0.558, -42.2, 0.568, -44.03, 0.558, -52.27, 0.608, -22.52]}, {"time": 0.6333, "x": -36.87, "y": -22.52, "curve": [0.702, -29.26, 0.783, -26.32, 0.683, -22.52, 0.763, -44.03]}, {"time": 0.8333, "x": -26.32, "y": -72.37, "curve": [0.875, -26.32, 0.939, -42.98, 0.855, -81, 0.916, -109.88]}, {"time": 1, "x": -42.64, "y": -109.99, "curve": [1.176, -42.56, 1.221, -18.42, 1.18, -110.24, 1.217, -3.39]}, {"time": 1.2667, "x": -18.05, "y": -2.89}]}, "back-leg-target": {"rotate": [{"value": 2.39}], "translate": [{"x": -24.54, "y": 15.12, "curve": [0.135, -24.49, 0.417, -40.42, 0.121, 14.56, 0.327, -140.88]}, {"time": 0.5667, "x": -40.21, "y": -141.76, "curve": [0.705, -40.01, 1.147, -24.39, 0.939, -143.13, 1.122, 15.12]}, {"time": 1.2667, "x": -24.54, "y": 15.12}]}, "tail1": {"rotate": [{"value": -1.81, "curve": [0.091, -1.81, 0.208, 12.33]}, {"time": 0.3333, "value": 12.33, "curve": [0.431, 12.33, 0.532, -1.81]}, {"time": 0.6333, "value": -1.81, "curve": [0.735, -1.81, 0.838, 11.62]}, {"time": 0.9333, "value": 11.62, "curve": [1.06, 11.62, 1.176, -1.81]}, {"time": 1.2667, "value": -1.81}]}, "torso1": {"rotate": [{"value": 7.22, "curve": [0.048, 7.93, 0.072, 8.38]}, {"time": 0.1333, "value": 8.39, "curve": [0.202, 8.4, 0.302, 2.44]}, {"time": 0.3667, "value": 2.44, "curve": [0.433, 2.44, 0.588, 6.89]}, {"time": 0.6, "value": 7.22, "curve": [0.618, 7.7, 0.683, 8.43]}, {"time": 0.7333, "value": 8.43, "curve": [0.83, 8.43, 0.91, 2.42]}, {"time": 0.9667, "value": 2.42, "curve": [1.033, 2.42, 1.212, 6.06]}, {"time": 1.2667, "value": 7.22}]}, "saddle": {"rotate": [{"value": -2.52}, {"time": 0.3667, "value": -4.17}, {"time": 0.6333, "value": -3.85}, {"time": 1, "value": -3.1}, {"time": 1.2667, "value": -2.52}], "translate": [{"x": 5.87, "y": -0.06}, {"time": 0.3667, "curve": [0.377, -0.01, 0.417, -0.04, 0.377, 1.66, 0.417, 5.92]}, {"time": 0.4333, "x": -0.04, "y": 5.92, "curve": [0.518, -0.04, 0.603, -6.88, 0.518, 5.92, 0.603, 1.38]}, {"time": 0.6333, "x": -8.81, "y": 0.1}, {"time": 0.7, "x": -7.83, "y": -2.27}, {"time": 1, "curve": [1.01, -0.01, 1.05, -0.04, 1.01, 1.66, 1.05, 5.92]}, {"time": 1.0667, "x": -0.04, "y": 5.92, "curve": [1.151, -0.04, 1.237, 4.57, 1.151, 5.92, 1.237, 1.25]}, {"time": 1.2667, "x": 5.87, "y": -0.06}]}, "torso2": {"rotate": [{"value": -4.19, "curve": [0.092, -4.19, 0.275, -2.54]}, {"time": 0.3667, "value": -2.54, "curve": [0.433, -2.54, 0.567, -4.19]}, {"time": 0.6333, "value": -4.19, "curve": [0.725, -4.19, 0.908, -2.57]}, {"time": 1, "value": -2.57, "curve": [1.067, -2.57, 1.2, -4.19]}, {"time": 1.2667, "value": -4.19}], "translate": [{"curve": [0.087, -1.87, 0.131, -1.57, 0.098, 9.88, 0.131, 12.61]}, {"time": 0.1667, "x": -1.57, "y": 12.61, "curve": [0.246, -1.57, 0.278, 8.5, 0.246, 12.61, 0.258, -10.38]}, {"time": 0.3667, "x": 8.38, "y": -10.24, "curve": [0.453, 8.25, 0.581, 1.81, 0.441, -10.14, 0.598, -3.48]}, {"time": 0.6333, "x": 0.09, "curve": [0.666, -0.98, 0.764, -1.48, 0.667, 3.32, 0.764, 12.61]}, {"time": 0.8, "x": -1.48, "y": 12.61, "curve": [0.88, -1.48, 0.937, 8.5, 0.88, 12.61, 0.907, -10.4]}, {"time": 1.0333, "x": 8.38, "y": -10.24, "curve": [1.12, 8.26, 1.218, 2.68, 1.101, -10.15, 1.195, -5.21]}, {"time": 1.2667}]}, "front-arm1": {"rotate": [{"value": -348.35, "curve": [0.305, -348.39, 0.452, -330.1]}, {"time": 0.6333, "value": -329.45, "curve": [0.927, -329.66, 0.992, -347.92]}, {"time": 1.2667, "value": -348.35}], "translate": [{"x": 8.89, "y": 9.56, "curve": [0.065, 11.51, 0.276, 24.33, 0.111, 9.62, 0.302, 1.67]}, {"time": 0.3667, "x": 24.1, "y": -1.47, "curve": [0.479, 23.82, 0.545, 23.42, 0.424, -4.23, 0.533, -7.49]}, {"time": 0.6333, "x": 20.65, "y": -7.55, "curve": [0.724, 17.83, 0.908, 4.35, 0.725, -7.6, 0.903, -1.56]}, {"time": 1, "x": 4.35, "y": 2.31, "curve": [1.058, 4.35, 1.197, 6.14, 1.052, 4.38, 1.17, 9.44]}, {"time": 1.2667, "x": 8.89, "y": 9.56}]}, "neck": {"rotate": [{"value": -22.13, "curve": [0.092, -22.13, 0.275, -4.12]}, {"time": 0.3667, "value": -4.12, "curve": [0.433, -4.12, 0.567, -22.13]}, {"time": 0.6333, "value": -22.13, "curve": [0.725, -22.13, 0.908, -4.12]}, {"time": 1, "value": -4.12, "curve": [1.067, -4.12, 1.2, -22.13]}, {"time": 1.2667, "value": -22.13}], "translate": [{"x": 19.46, "y": -14.29, "curve": [0.046, 22.71, 0.064, 24.67, 0.027, -20.08, 0.067, -33.27]}, {"time": 0.1, "x": 24.7, "y": -32.71, "curve": [0.121, 24.71, 0.187, 5.34, 0.136, -32.09, 0.207, -18.93]}, {"time": 0.2333, "x": 5.16, "y": -13.23, "curve": [0.287, 4.94, 0.337, 5.54, 0.274, -4.5, 0.332, 3.98]}, {"time": 0.3667, "x": 7.2, "y": 3.98, "curve": [0.426, 10.51, 0.575, 18.6, 0.433, 3.98, 0.6, -8.7]}, {"time": 0.6333, "x": 21.66, "y": -14.29, "curve": [0.668, 23.45, 0.697, 24.53, 0.668, -20.07, 0.697, -32.36]}, {"time": 0.7333, "x": 24.7, "y": -32.71, "curve": [0.788, 24.95, 0.82, 4.99, 0.782, -33.17, 0.829, -21.82]}, {"time": 0.8667, "x": 5.16, "y": -13.23, "curve": [0.921, 5.36, 0.955, 6.02, 0.897, -6.22, 0.965, 3.98]}, {"time": 1, "x": 7.71, "y": 3.98, "curve": [1.046, 9.42, 1.17, 14.8, 1.067, 3.98, 1.227, -5.97]}, {"time": 1.2667, "x": 19.46, "y": -14.29}]}, "back-arm1": {"rotate": [{"value": 36.77, "curve": [0.275, 36.57, 0.464, 23.85]}, {"time": 0.6333, "value": 23.86, "curve": [0.891, 23.91, 1.035, 36.68]}, {"time": 1.2667, "value": 36.77}], "translate": [{"x": -20.37, "y": -12.6, "curve": [0.092, -20.37, 0.567, -30.85, 0.25, -12.16, 0.508, -9.49]}, {"time": 0.6333, "x": -30.85, "y": -9.16, "curve": [0.725, -30.85, 1.143, -20.51, 0.794, -9.16, 1.086, -12.8]}, {"time": 1.2667, "x": -20.37, "y": -12.6}]}, "saddle-strap-back": {"rotate": [{"value": -5.12, "curve": [0.042, -5.12, 0.104, -2.7]}, {"time": 0.1667, "value": 0.89, "curve": [0.241, 5.11, 0.317, 10.59]}, {"time": 0.3667, "value": 10.59, "curve": [0.433, 10.59, 0.567, 0.72]}, {"time": 0.6333, "value": 0.72, "curve": [0.733, 0.72, 0.933, 7.72]}, {"time": 1.0333, "value": 7.72, "curve": [1.092, 7.72, 1.208, -5.12]}, {"time": 1.2667, "value": -5.12}]}, "front-arm2": {"rotate": [{"value": 20.46, "curve": [0.092, 20.46, 0.29, 10.44]}, {"time": 0.3667, "value": 4.66, "curve": [0.493, -4.86, 0.54, -11]}, {"time": 0.6333, "value": -11.14, "curve": [0.725, -11.27, 0.934, 1.22]}, {"time": 1, "value": 6.77, "curve": [1.042, 10.27, 1.15, 20.26]}, {"time": 1.2667, "value": 20.46}]}, "head": {"rotate": [{"value": 21.27, "curve": [0.121, 21.36, 0.118, 5.37]}, {"time": 0.3333, "value": 5.74, "curve": [0.443, 5.92, 0.579, 20.65]}, {"time": 0.6333, "value": 20.71, "curve": [0.761, 20.68, 0.8, 6.02]}, {"time": 1.0333, "value": 5.76, "curve": [1.101, 5.8, 1.208, 21.57]}, {"time": 1.2667, "value": 21.27}], "translate": [{"x": 9.88, "y": -21.53, "curve": [0.066, 2.9, 0.151, -2.12, 0.05, -24.49, 0.139, -28.72]}, {"time": 0.2, "x": -1.68, "y": -28.94, "curve": [0.281, -0.96, 0.392, 24.71, 0.278, -29.21, 0.37, -7.39]}, {"time": 0.4667, "x": 24.76, "y": -6.79, "curve": [0.645, 24.69, 0.668, -1.21, 0.622, -7.06, 0.597, -30.92]}, {"time": 0.8, "x": -1.47, "y": -30.78, "curve": [0.89, -1.64, 0.979, 25.37, 0.877, -30.73, 0.97, -7.01]}, {"time": 1.0667, "x": 25.27, "y": -6.86, "curve": [1.147, 25.19, 1.217, 15.2, 1.141, -6.75, 1.217, -18.56]}, {"time": 1.2667, "x": 9.88, "y": -21.53}]}, "back-arm2": {"rotate": [{"value": -28.13, "curve": [0.32, -28.07, 0.434, 0.42]}, {"time": 0.6333, "value": 0.31, "curve": [0.941, 0.13, 0.968, -27.95]}, {"time": 1.2667, "value": -28.13}]}, "stirrup": {"rotate": [{"value": -17.15}, {"time": 0.3667, "value": -11.14}, {"time": 0.6333, "value": -6.95}, {"time": 1, "value": -11.84}, {"time": 1.2667, "value": -17.15}], "translate": [{"x": 8.98, "y": 4.99}, {"time": 0.3667, "x": 4.85, "y": 1}, {"time": 0.6333, "x": 7.76, "y": -2.99}, {"time": 1, "x": 4.85, "y": 1}, {"time": 1.2667, "x": 8.98, "y": 4.99}]}, "front-foot2": {"rotate": [{"value": 36.9}, {"time": 0.0667, "value": 7.88}, {"time": 0.1333, "value": 4.67}, {"time": 0.5, "value": 6.81, "curve": [0.572, 7.04, 0.605, 6.97]}, {"time": 0.6333, "value": 6.25, "curve": [0.686, 4.19, 0.708, -105.88]}, {"time": 0.8, "value": -104.63, "curve": [0.955, -102.9, 1.188, 34.64]}, {"time": 1.2667, "value": 36.9}]}, "front-hand": {"rotate": [{"value": -60.7, "curve": [0.318, -60.68, 0.442, -6]}, {"time": 0.6333, "value": -6.02, "curve": [0.847, -6.04, 1.076, -60.24]}, {"time": 1.2667, "value": -60.7}]}, "horn-back": {"translate": [{"x": 7.01, "y": 42.92}, {"time": 0.3667, "x": 13.17, "y": 33.39}, {"time": 0.6333, "x": 7.68, "y": 13.94}, {"time": 1, "x": 12.26, "y": 35.06}, {"time": 1.2667, "x": 7.01, "y": 42.92}]}, "jaw": {"rotate": [{"value": -9.46, "curve": [0.088, -9.55, 0.339, -16.63]}, {"time": 0.3667, "value": -17.51, "curve": [0.562, -23.77, 0.8, -23.24]}, {"time": 0.8333, "value": -23.22, "curve": [0.963, -23.18, 1.179, -9.56]}, {"time": 1.2667, "value": -9.46}], "translate": [{"x": -0.34, "y": -2.02, "curve": [0.092, -0.34, 0.275, 0.79, 0.092, -2.02, 0.275, 9.47]}, {"time": 0.3667, "x": 0.79, "y": 9.47, "curve": [0.425, 0.79, 0.575, 0.93, 0.425, 9.47, 0.575, 6.04]}, {"time": 0.6333, "x": 0.93, "y": 6.09, "curve": [0.642, 0.93, 0.783, 0.79, 0.714, 6.16, 0.783, 9.47]}, {"time": 0.8333, "x": 0.79, "y": 9.47, "curve": [0.875, 0.79, 0.958, 0.57, 0.875, 9.47, 0.979, 7.85]}, {"time": 1, "x": 0.57, "y": 7.17, "curve": [1.067, 0.57, 1.2, -0.34, 1.083, 4.51, 1.2, -2.02]}, {"time": 1.2667, "x": -0.34, "y": -2.02}]}, "back-foot2": {"rotate": [{"curve": [0.019, 0, 0.046, 0.68]}, {"time": 0.0667, "value": -2.42, "curve": [0.127, -11.89, 0.157, -75.22]}, {"time": 0.3, "value": -74.35, "curve": [0.372, -74.4, 0.44, -55.45]}, {"time": 0.4667, "value": -38.51, "curve": [0.488, -24.72, 0.512, 30.14]}, {"time": 0.6, "value": 30.17, "curve": [0.631, 30.18, 0.649, 17.16]}, {"time": 0.6667, "value": 2.1, "curve": [0.758, 2.1, 1.2, 0]}, {"time": 1.2667}]}, "back-hand": {"rotate": [{"value": -26.17, "curve": [0.077, -26.23, 0.165, -15.08]}, {"time": 0.2667, "value": -15.07, "curve": [0.348, -15.06, 0.514, -27.32]}, {"time": 0.6333, "value": -27.37, "curve": [0.745, -27.37, 0.844, -14.99]}, {"time": 1, "value": -15.02, "curve": [1.117, -15.05, 1.104, -26.34]}, {"time": 1.2667, "value": -26.17}]}, "tongue1": {"rotate": [{"value": 21.55}, {"time": 0.8, "value": 0.08}, {"time": 1.2667, "value": 21.55}]}, "front-foot3": {"rotate": [{"value": -1.65}, {"time": 0.1, "value": -3.94, "curve": [0.158, -3.94, 0.308, -3.82]}, {"time": 0.3667, "value": -3.82, "curve": [0.433, -3.82, 0.511, -6.36]}, {"time": 0.6333, "value": -6.62, "curve": [0.701, -7.05, 0.687, 24.84]}, {"time": 0.7333, "value": 25.1, "curve": [0.766, 25.1, 0.821, -23.09]}, {"time": 0.8667, "value": -22.96, "curve": [0.975, -22.65, 1.139, 10.93]}, {"time": 1.2, "value": 10.93, "curve": [1.217, 10.93, 1.267, 5.95]}, {"time": 1.2667, "value": -1.65}]}, "tongue2": {"rotate": [{"value": -16.02, "curve": [0.286, -15.88, 0.415, -3.2]}, {"time": 0.6333, "value": -3.21, "curve": [0.988, -3.22, 1.102, -16.12]}, {"time": 1.2667, "value": -16.02}]}, "tongue3": {"rotate": [{"value": -16.02, "curve": [0.027, -12.15, 0.123, 0.49]}, {"time": 0.2333, "value": 0.44, "curve": [0.457, 0.33, 0.862, -36.42]}, {"time": 1.0667, "value": -36.41, "curve": [1.168, -36.41, 1.248, -19.42]}, {"time": 1.2667, "value": -16.02}]}, "neck2": {"rotate": [{"value": 24.69}], "translate": [{"x": -6.84, "y": -1.8, "curve": [0.164, -6.45, 0.256, -6.42, 0.092, -1.8, 0.308, -0.45]}, {"time": 0.4, "x": -6.24, "y": -0.45, "curve": [0.514, -6.09, 0.567, -6.03, 0.467, -0.45, 0.567, -1.81]}, {"time": 0.6333, "x": -6.03, "y": -1.81, "curve": [0.725, -6.03, 0.908, -10.81, 0.725, -1.81, 0.908, -0.35]}, {"time": 1, "x": -10.81, "y": -0.35, "curve": [1.067, -10.81, 1.137, -7.65, 1.067, -0.35, 1.2, -1.8]}, {"time": 1.2667, "x": -6.84, "y": -1.8}]}, "spineboy-hip": {"translate": [{"x": 53.69, "y": -49.21, "curve": [0.118, 53.52, 0.275, 16.15, 0.092, -49.21, 0.275, -32.56]}, {"time": 0.3667, "x": 16.15, "y": -32.56, "curve": [0.433, 16.15, 0.542, 53.47, 0.433, -32.56, 0.567, -49.21]}, {"time": 0.6333, "x": 53.69, "y": -49.21, "curve": [0.742, 53.95, 0.908, 15.73, 0.725, -49.21, 0.908, -29.74]}, {"time": 1, "x": 15.73, "y": -29.74, "curve": [1.067, 15.73, 1.11, 54.45, 1.067, -29.74, 1.2, -49.21]}, {"time": 1.2667, "x": 53.69, "y": -49.21}]}, "spineboy-torso": {"rotate": [{"value": -37.55, "curve": [0.092, -37.55, 0.275, -12.2]}, {"time": 0.3667, "value": -12.2, "curve": [0.498, -12.2, 0.567, -37.66]}, {"time": 0.6333, "value": -37.66, "curve": [0.725, -37.66, 0.908, -12.19]}, {"time": 1, "value": -12.19, "curve": [1.131, -12.19, 1.2, -37.55]}, {"time": 1.2667, "value": -37.55}]}, "front-arm": {"translate": [{"y": -5.28}, {"time": 0.1333, "x": -3.61, "y": -8.36}, {"time": 0.3667, "x": -8.05, "y": -5.53}, {"time": 0.6333}, {"time": 1, "x": -7.45, "y": -1.99}, {"time": 1.2667, "y": -5.28}]}, "gun": {"rotate": [{"value": -11.68, "curve": [0.03, -14.01, 0.177, -31.16]}, {"time": 0.3, "value": -31.16, "curve": [0.368, -31.16, 0.475, -9.46]}, {"time": 0.5333, "value": -9.58, "curve": [0.737, -9.99, 0.77, -31.38]}, {"time": 0.9333, "value": -31.11, "curve": [0.992, -31.11, 1.108, -9.58]}, {"time": 1.1667, "value": -9.58, "curve": [1.192, -9.58, 1.236, -9.68]}, {"time": 1.2667, "value": -11.68}], "translate": [{"curve": [0.042, -0.34, 0.098, -0.45, 0.039, -4.02, 0.098, -6.86]}, {"time": 0.1333, "x": -0.45, "y": -6.86, "curve": [0.194, -0.45, 0.256, 2.36, 0.194, -6.86, 0.245, 6.35]}, {"time": 0.3333, "x": 2.35, "y": 6.16, "curve": [0.439, 2.35, 0.579, 0.46, 0.418, 5.98, 0.6, 2.06]}, {"time": 0.6333, "curve": [0.668, -0.29, 0.731, -0.45, 0.679, -2.83, 0.731, -6.86]}, {"time": 0.7667, "x": -0.45, "y": -6.86, "curve": [0.828, -0.45, 0.89, 2.35, 0.828, -6.86, 0.899, 6.27]}, {"time": 0.9667, "x": 2.35, "y": 6.16, "curve": [1.053, 2.36, 1.213, 0.51, 1.051, 6.03, 1.226, 2.28]}, {"time": 1.2667}]}, "tail2": {"rotate": [{"value": -12.17, "curve": [0.091, -12.17, 0.207, -1.45]}, {"time": 0.3333, "value": -1.45, "curve": [0.441, -1.45, 0.555, -12.17]}, {"time": 0.6667, "value": -12.17, "curve": [0.896, -12.17, 0.874, -1.45]}, {"time": 1, "value": -1.45, "curve": [1.107, -1.45, 1.117, -12.17]}, {"time": 1.2667, "value": -12.17}]}, "tail3": {"rotate": [{"value": -7.15, "curve": [0.083, -7.15, 0.25, 0.58]}, {"time": 0.3333, "value": 0.58, "curve": [0.408, 0.58, 0.558, -7.15]}, {"time": 0.6333, "value": -7.15, "curve": [0.717, -7.15, 0.883, 0.75]}, {"time": 0.9667, "value": 0.75, "curve": [1.042, 0.75, 1.192, -7.15]}, {"time": 1.2667, "value": -7.15}], "translate": [{"curve": [0.158, 0, 0.475, -9.87, 0.158, 0, 0.475, -0.87]}, {"time": 0.6333, "x": -9.87, "y": -0.87, "curve": [0.792, -9.87, 1.108, 0, 0.792, -0.87, 1.108, 0]}, {"time": 1.2667}]}, "tail4": {"rotate": [{"value": -3.83, "curve": [0.013, -4.23, 0.024, -4.53]}, {"time": 0.0333, "value": -4.53, "curve": [0.068, -4.53, 0.116, -3.13]}, {"time": 0.1667, "value": -0.39, "curve": [0.24, 3.5, 0.318, 9.15]}, {"time": 0.3667, "value": 9.15, "curve": [0.442, 9.15, 0.592, -4.53]}, {"time": 0.6667, "value": -4.53, "curve": [0.75, -4.53, 0.917, 9]}, {"time": 1, "value": 9, "curve": [1.065, 9, 1.187, -1.17]}, {"time": 1.2667, "value": -3.83}], "translate": [{"curve": [0.158, 0, 0.475, -4.06, 0.158, 0, 0.475, 0.32]}, {"time": 0.6333, "x": -4.06, "y": 0.32, "curve": [0.792, -4.06, 1.108, 0, 0.792, 0.32, 1.108, 0]}, {"time": 1.2667}]}, "tail5": {"rotate": [{"value": -7.24, "curve": [0.026, -8.78, 0.048, -9.9]}, {"time": 0.0667, "value": -9.9, "curve": [0.093, -9.9, 0.129, -9.01]}, {"time": 0.1667, "value": -6.6, "curve": [0.248, -1.49, 0.343, 7.39]}, {"time": 0.4, "value": 7.39, "curve": [0.475, 7.39, 0.625, -9.9]}, {"time": 0.7, "value": -9.9, "curve": [0.783, -9.9, 0.95, 7.29]}, {"time": 1.0333, "value": 7.29, "curve": [1.09, 7.29, 1.19, -2.46]}, {"time": 1.2667, "value": -7.24}], "translate": [{"curve": [0.158, 0, 0.475, -7.55, 0.158, 0, 0.475, 0.73]}, {"time": 0.6333, "x": -7.55, "y": 0.73, "curve": [0.792, -7.55, 1.108, 0, 0.792, 0.73, 1.108, 0]}, {"time": 1.2667}]}, "tail6": {"rotate": [{"value": -4.22, "curve": [0.038, -7.4, 0.074, -9.9]}, {"time": 0.1, "value": -9.9, "curve": [0.119, -9.9, 0.142, -9.79]}, {"time": 0.1667, "value": -8.22, "curve": [0.255, -2.85, 0.369, 9.89]}, {"time": 0.4333, "value": 9.89, "curve": [0.508, 9.89, 0.658, -9.9]}, {"time": 0.7333, "value": -9.9, "curve": [0.817, -9.9, 0.983, 10.12]}, {"time": 1.0667, "value": 10.12, "curve": [1.115, 10.12, 1.196, 1.7]}, {"time": 1.2667, "value": -4.22}], "translate": [{"curve": [0.158, 0, 0.475, -9.06, 0.158, 0, 0.475, 1.58]}, {"time": 0.6333, "x": -9.06, "y": 1.58, "curve": [0.792, -9.06, 1.108, 0, 0.792, 1.58, 1.108, 0]}, {"time": 1.2667}]}, "tail7": {"rotate": [{"value": -0.76, "curve": [0.051, -5.53, 0.1, -9.9]}, {"time": 0.1333, "value": -9.9, "curve": [0.144, -9.9, 0.155, -10.45]}, {"time": 0.1667, "value": -9.89, "curve": [0.257, -5.87, 0.394, 11.3]}, {"time": 0.4667, "value": 11.3, "curve": [0.542, 11.3, 0.692, -9.9]}, {"time": 0.7667, "value": -9.9, "curve": [0.85, -9.9, 1.017, 11.52]}, {"time": 1.1, "value": 11.52, "curve": [1.141, 11.52, 1.205, 5.07]}, {"time": 1.2667, "value": -0.76}], "translate": [{"curve": [0.158, 0, 0.475, -9.93, 0.158, 0, 0.475, 1.32]}, {"time": 0.6333, "x": -9.93, "y": 1.32, "curve": [0.792, -9.93, 1.108, 0, 0.792, 1.32, 1.108, 0]}, {"time": 1.2667}]}, "tail8": {"rotate": [{"value": 4.71, "curve": [0.062, -2.19, 0.126, -10.79]}, {"time": 0.1667, "value": -10.79, "curve": [0.25, -10.79, 0.417, 16.46]}, {"time": 0.5, "value": 16.46, "curve": [0.575, 16.46, 0.725, -9.9]}, {"time": 0.8, "value": -9.9, "curve": [0.883, -9.9, 1.05, 15.57]}, {"time": 1.1333, "value": 15.57, "curve": [1.167, 15.57, 1.217, 10.43]}, {"time": 1.2667, "value": 4.71}], "translate": [{"curve": [0.158, 0, 0.475, -12.57, 0.158, 0, 0.475, 1.97]}, {"time": 0.6333, "x": -12.57, "y": 1.97, "curve": [0.792, -12.57, 1.108, 0, 0.792, 1.97, 1.108, 0]}, {"time": 1.2667}]}, "tail9": {"rotate": [{"value": 7.95, "curve": [0.072, 1.87, 0.151, -6.79]}, {"time": 0.2, "value": -6.79, "curve": [0.283, -6.79, 0.45, 15.08]}, {"time": 0.5333, "value": 15.08, "curve": [0.608, 15.08, 0.758, -6.79]}, {"time": 0.8333, "value": -6.79, "curve": [0.917, -6.79, 1.083, 13.8]}, {"time": 1.1667, "value": 13.8, "curve": [1.193, 13.8, 1.229, 11.28]}, {"time": 1.2667, "value": 7.95}], "translate": [{"curve": [0.158, 0, 0.475, -15.36, 0.158, 0, 0.475, 2.18]}, {"time": 0.6333, "x": -15.36, "y": 2.18, "curve": [0.792, -15.36, 1.108, 0, 0.792, 2.18, 1.108, 0]}, {"time": 1.2667}]}, "tail10": {"rotate": [{"value": 8.87, "curve": [0.078, 3.96, 0.177, -5.98]}, {"time": 0.2333, "value": -5.98, "curve": [0.317, -5.98, 0.483, 10.93]}, {"time": 0.5667, "value": 10.93, "curve": [0.642, 10.93, 0.792, -5.98]}, {"time": 0.8667, "value": -5.98, "curve": [0.95, -5.98, 1.117, 11.58]}, {"time": 1.2, "value": 11.58, "curve": [1.219, 11.58, 1.242, 10.48]}, {"time": 1.2667, "value": 8.87}], "translate": [{"curve": [0.158, 0, 0.475, -19.3, 0.158, 0, 0.475, 1.9]}, {"time": 0.6333, "x": -19.3, "y": 1.9, "curve": [0.792, -19.3, 1.108, 0, 0.792, 1.9, 1.108, 0]}, {"time": 1.2667}]}, "horn-front": {"translate": [{"x": 3.46, "y": -20.12, "curve": [0.092, 3.46, 0.243, 3.41, 0.092, -20.12, 0.292, -6.08]}, {"time": 0.3667, "x": 3.57, "y": -1.24, "curve": [0.407, 3.63, 0.567, 5.09, 0.484, 6.36, 0.567, 11.01]}, {"time": 0.6333, "x": 5.09, "y": 11.01, "curve": [0.725, 5.09, 0.807, 2.74, 0.725, 11.01, 0.918, -0.08]}, {"time": 1, "x": 2.77, "y": -5.87, "curve": [1.116, 2.79, 1.2, 3.46, 1.049, -9.34, 1.2, -20.12]}, {"time": 1.2667, "x": 3.46, "y": -20.12}]}, "saddle-strap-front": {"rotate": [{"value": 3.65, "curve": [0.091, 3.65, 0.233, 5.42]}, {"time": 0.3667, "value": 5.41, "curve": [0.45, 5.4, 0.566, 3.49]}, {"time": 0.6333, "value": 3.49, "curve": [0.732, 3.49, 0.89, 5.39]}, {"time": 1.0333, "value": 5.38, "curve": [1.112, 5.38, 1.207, 3.65]}, {"time": 1.2667, "value": 3.65}]}, "jaw-inside": {"translate": [{"x": -8.34, "y": -3.22}, {"time": 0.6333, "x": 1.17, "y": -1.6}, {"time": 1.2667, "x": -8.34, "y": -3.22}]}, "bone": {"rotate": [{"value": 2, "curve": [0.031, 3.38, 0.075, 4.99]}, {"time": 0.1, "value": 5.01, "curve": [0.194, 5.05, 0.398, -0.88]}, {"time": 0.4667, "value": -0.83, "curve": [0.538, -0.78, 0.671, 4.88]}, {"time": 0.7333, "value": 4.88, "curve": [0.846, 4.88, 1.032, -0.57]}, {"time": 1.1, "value": -0.57, "curve": [1.142, -0.57, 1.201, 0.04]}, {"time": 1.2667, "value": 2}]}, "bone2": {"rotate": [{"curve": [0.019, -8.93, 0.062, -21.67]}, {"time": 0.1333, "value": -21.67, "curve": [0.23, -21.67, 0.346, 21.39]}, {"time": 0.4667, "value": 21.32, "curve": [0.639, 21.22, 0.626, -21.47]}, {"time": 0.7667, "value": -21.67, "curve": [0.863, -21.81, 1.013, 21.01]}, {"time": 1.1, "value": 21.32, "curve": [1.204, 21.68, 1.245, 9.94]}, {"time": 1.2667}]}, "bone3": {"rotate": [{"curve": [0.083, 0, 0.25, 11.45]}, {"time": 0.3333, "value": 11.45, "curve": [0.407, 11.39, 0.547, 0.26]}, {"time": 0.6333, "value": 0.18, "curve": [0.708, 0.18, 0.858, 11.29]}, {"time": 0.9333, "value": 11.29, "curve": [1.017, 11.29, 1.183, 0]}, {"time": 1.2667}]}, "bone4": {"rotate": [{"curve": [0, -6.15, 0.044, -10.86]}, {"time": 0.1, "value": -11.06, "curve": [0.161, -11.27, 0.267, 10.59]}, {"time": 0.3667, "value": 10.49, "curve": [0.554, 10.32, 0.603, -10.61]}, {"time": 0.7333, "value": -10.98, "curve": [0.782, -10.93, 0.949, 10.3]}, {"time": 1.0333, "value": 10.41, "curve": [1.144, 10.56, 1.255, 4.55]}, {"time": 1.2667}]}, "bone5": {"rotate": [{"value": 6.23, "curve": [0.042, 3.41, 0.105, 3.02]}, {"time": 0.1667, "value": 3.03, "curve": [0.222, 3.04, 0.334, 11.98]}, {"time": 0.4333, "value": 11.93, "curve": [0.561, 11.87, 0.662, 3.23]}, {"time": 0.8, "value": 3.22, "curve": [0.849, 3.21, 1.016, 11.92]}, {"time": 1.1, "value": 12.03, "curve": [1.162, 12.12, 1.223, 9.21]}, {"time": 1.2667, "value": 6.23}]}, "spineboy-front-arm-target": {"translate": [{}, {"time": 0.3667, "x": -12.14, "y": 8.93}, {"time": 0.6333}, {"time": 1, "x": -10.79, "y": 7.94}, {"time": 1.2667}]}, "front-hand2": {"rotate": [{"curve": [0.092, 0, 0.275, 15.62]}, {"time": 0.3667, "value": 15.62, "curve": [0.433, 15.62, 0.567, 0]}, {"time": 0.6333, "curve": [0.733, 0, 0.933, 15.62]}, {"time": 1.0333, "value": 15.62, "curve": [1.092, 15.62, 1.208, 0]}, {"time": 1.2667}]}, "front-bracer": {"rotate": [{"value": 52.99}]}, "head-control": {"translate": [{"y": -55.1, "curve": [0.063, -1.75, 0.253, -5.83, 0.092, -55.1, 0.298, -20]}, {"time": 0.3667, "x": -5.85, "y": -6.98, "curve": [0.437, -5.86, 0.539, -2.89, 0.44, 6.9, 0.567, 28.15]}, {"time": 0.6333, "y": 28.15, "curve": [0.709, 2.32, 0.858, 5.7, 0.708, 28.15, 0.867, 1.15]}, {"time": 0.9333, "x": 5.7, "y": -12.05, "curve": [1.017, 5.7, 1.157, 2.64, 1.02, -29.22, 1.183, -55.1]}, {"time": 1.2667, "y": -55.1}]}, "leg-control": {"translate": [{"curve": [0.021, 0, 0.048, 3.27, 0.021, 0, 0.044, -99.76]}, {"time": 0.0667, "x": 5.16, "y": -99.76, "curve": [0.099, 8.46, 0.145, 12.65, 0.089, -99.76, 0.134, 67.18]}, {"time": 0.1667, "x": 12.65, "y": 68.39, "curve": [0.188, 12.65, 0.251, 7.24, 0.211, 70.03, 0.244, -68.23]}, {"time": 0.2667, "x": 4.95, "y": -68.19, "curve": [0.324, -3.3, 0.337, -4.96, 0.307, -68.11, 0.329, -15.36]}, {"time": 0.3667, "x": -10.49, "y": -14.91, "curve": [0.404, -17.53, 0.441, -25.51, 0.416, -14.33, 0.484, -84.78]}, {"time": 0.5, "x": -25.51, "y": -101.71, "curve": [0.566, -25.51, 0.634, -23.99, 0.543, -148.75, 0.634, -191.81]}, {"time": 0.7, "x": -21.81, "y": -183.43, "curve": [0.915, -14.78, 1.123, 0, 0.915, -156.4, 1.123, 0]}, {"time": 1.2667}]}, "head2": {"rotate": [{"value": 11.97, "curve": [0.021, 10.46, 0.104, 4.49]}, {"time": 0.1667, "value": 4.55, "curve": [0.312, 4.71, 0.419, 14.2]}, {"time": 0.5, "value": 14.11, "curve": [0.671, 13.92, 0.686, 4.54]}, {"time": 0.8, "value": 4.56, "curve": [0.953, 4.59, 1.029, 14.2]}, {"time": 1.1333, "value": 14.13, "curve": [1.186, 14.1, 1.214, 14.03]}, {"time": 1.2667, "value": 11.97}], "scale": [{"curve": [0.011, 0.995, 0.122, 0.985, 0.055, 1.007, 0.104, 1.011]}, {"time": 0.1667, "x": 0.985, "y": 1.012, "curve": [0.256, 0.985, 0.415, 1.014, 0.245, 1.013, 0.419, 0.988]}, {"time": 0.5, "x": 1.013, "y": 0.988, "curve": [0.579, 1.013, 0.693, 0.988, 0.62, 0.988, 0.713, 1.014]}, {"time": 0.8, "x": 0.988, "y": 1.014, "curve": [0.881, 0.988, 1.075, 1.014, 0.908, 1.014, 1.033, 0.988]}, {"time": 1.1333, "x": 1.014, "y": 0.989, "curve": [1.181, 1.014, 1.2, 1.011, 1.164, 0.989, 1.23, 0.995]}, {"time": 1.2667}]}}, "ik": {"back-leg-ik": [{"softness": 66.9, "bendPositive": false}], "spineboy-back-arm-ik": [{}], "spineboy-front-arm-ik": [{"softness": 14.4, "stretch": true}]}, "events": [{"time": 0.6333, "name": "footstep"}, {"time": 1.2667, "name": "footstep"}]}}}