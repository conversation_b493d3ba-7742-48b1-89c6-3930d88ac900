{"skeleton": {"hash": "8C0Q8jcbsuI", "spine": "4.1.23-beta", "x": -402.84, "y": -35.99, "width": 611.87, "height": 526.92, "images": "./images/", "audio": ""}, "bones": [{"name": "root"}, {"name": "hip", "parent": "root", "rotation": 3.16, "x": -68.39, "y": 207.74, "color": "fbff00ff"}, {"name": "torso1", "parent": "hip", "length": 63.13, "rotation": -4.98, "x": 15.02, "y": -0.2, "color": "eaff00ff"}, {"name": "saddle", "parent": "torso1", "length": 25.46, "rotation": 91.8, "x": 2.29, "y": 35.94, "color": "ff7300ff"}, {"name": "spineboy_hip", "parent": "saddle", "length": 0.26, "rotation": 90.02, "x": 40.94, "y": 1.35, "color": "ffffffff"}, {"name": "spineboy_torso", "parent": "spineboy_hip", "length": 61.22, "rotation": -75.86, "x": 0.52, "y": -1.05, "color": "ffffffff"}, {"name": "torso2", "parent": "torso1", "length": 60.6, "rotation": 39.85, "x": 63.13, "y": -0.19, "color": "eaff00ff"}, {"name": "neck", "parent": "torso2", "length": 35.3, "rotation": 41.38, "x": 60.6, "y": 0.17, "color": "eaff00ff"}, {"name": "head", "parent": "neck", "length": 52.76, "rotation": 9.83, "x": 35.3, "y": 0.02, "color": "eaff00ff"}, {"name": "horn_rear", "parent": "head", "length": 36.89, "rotation": 44.32, "x": 49.63, "y": -113.4, "color": "e07800ff"}, {"name": "rear_arm_target", "parent": "horn_rear", "rotation": -133.55, "x": 116.34, "y": 122.93, "color": "ff3f00ff"}, {"name": "back_arm", "parent": "spineboy_torso", "length": 33.6, "rotation": -120.9, "x": 48.17, "y": -19.24, "color": "ffffffff"}, {"name": "back_bracer", "parent": "back_arm", "length": 21.84, "rotation": 17.48, "x": 33.61, "y": -0.16, "color": "ffffffff"}, {"name": "back_hand", "parent": "back_bracer", "length": 20.99, "rotation": 9.21, "x": 21.84, "y": 0.03, "transform": "noRotationOrReflection", "color": "ffffffff"}, {"name": "spineboy_rear_arm_goal", "parent": "saddle", "x": -15.22, "y": -50.04, "color": "ff3f00ff"}, {"name": "back_thigh", "parent": "spineboy_hip", "length": 35.58, "rotation": 160.75, "x": -4.78, "y": 1.16, "color": "ffffffff"}, {"name": "back_knee", "parent": "back_thigh", "length": 48.58, "rotation": -54.98, "x": 35.58, "y": -0.14, "color": "ffffffff"}, {"name": "horn_front", "parent": "head", "length": 43.74, "rotation": 49.36, "x": 41.04, "y": -110.68, "color": "15ff00ff"}, {"name": "front_arm_target", "parent": "horn_front", "rotation": -138.6, "x": 147.29, "y": 117.09, "color": "ff3f00ff"}, {"name": "front_arm", "parent": "spineboy_torso", "length": 37.26, "rotation": -118.17, "x": 50.69, "y": 4.89, "color": "ffffffff"}, {"name": "front_bracer", "parent": "front_arm", "length": 19.92, "rotation": 20.31, "x": 37.26, "y": -0.21, "color": "ffffffff"}, {"name": "front_arm1", "parent": "torso2", "length": 54.99, "rotation": 224.54, "x": 23.18, "y": -42.31, "color": "15ff00ff"}, {"name": "front_arm2", "parent": "front_arm1", "length": 43.17, "rotation": 105.24, "x": 54.99, "y": 0.1, "color": "15ff00ff"}, {"name": "front_foot_goal", "parent": "root", "rotation": -0.95, "x": -22.9, "y": -14.34, "color": "ff3f00ff"}, {"name": "front_leg_goal", "parent": "front_foot_goal", "x": -53.03, "y": 57.79, "color": "ff3f00ff"}, {"name": "front_leg1", "parent": "hip", "length": 125.88, "rotation": -51.51, "x": 13.68, "y": -14.14, "color": "15ff00ff"}, {"name": "front_leg2", "parent": "front_leg1", "length": 104.28, "rotation": 261.94, "x": 125.52, "y": 0.08, "color": "15ff00ff"}, {"name": "front_leg3", "parent": "front_leg2", "length": 59.09, "rotation": 85.46, "x": 104.25, "y": -0.82, "color": "15ff00ff"}, {"name": "front_foot1", "parent": "front_leg3", "length": 28.9, "rotation": 54.46, "x": 59.1, "y": -0.4, "scaleX": 1.127, "color": "15ff00ff"}, {"name": "front_foot2", "parent": "front_foot1", "length": 28.09, "rotation": -0.46, "x": 28.9, "y": -0.01, "scaleX": 0.731, "scaleY": 0.823, "transform": "noRotationOrReflection", "color": "15ff00ff"}, {"name": "front_foot3", "parent": "front_foot2", "length": 64.94, "rotation": -3.17, "x": 24.85, "y": 10.33, "scaleX": 1.155, "transform": "noRotationOrReflection", "color": "15ff00ff"}, {"name": "front_hand", "parent": "front_arm2", "length": 23.78, "rotation": -56.83, "x": 43.17, "y": 0.03, "color": "15ff00ff"}, {"name": "front_hand2", "parent": "front_bracer", "length": 29.09, "rotation": 13.9, "x": 19.99, "y": -0.45, "transform": "noRotationOrReflection", "color": "ffffffff"}, {"name": "spineboy_front_arm_goal", "parent": "saddle", "x": -25.35, "y": -48.47, "color": "ff3f00ff"}, {"name": "front_thigh", "parent": "spineboy_hip", "length": 38.9, "rotation": 163.34, "x": 7.76, "y": 8.51, "color": "ffffffff"}, {"name": "lower_leg", "parent": "front_thigh", "length": 55.75, "rotation": -49.62, "x": 38.97, "y": -0.05, "color": "ffffffff"}, {"name": "gun", "parent": "spineboy_hip", "length": 90.68, "rotation": 107.12, "x": 8.43, "y": -3.94, "scaleX": 0.816, "scaleY": 0.816, "color": "ffffffff"}, {"name": "neck2", "parent": "spineboy_torso", "length": 16.02, "rotation": -45.23, "x": 56.72, "y": -7.61, "color": "ffffffff"}, {"name": "head2", "parent": "neck2", "length": 124.82, "rotation": 11.66, "x": 11.51, "y": 1.74, "color": "ffffffff"}, {"name": "jaw", "parent": "head", "length": 101.88, "rotation": -140.14, "x": 14.69, "y": -20.08, "transform": "noScale", "color": "ffff00ff"}, {"name": "rear_arm1", "parent": "torso2", "length": 54.78, "rotation": -124.72, "x": 28.52, "y": -47.69, "color": "e07800ff"}, {"name": "rear_arm2", "parent": "rear_arm1", "length": 42.9, "rotation": 123.56, "x": 54.78, "color": "e07800ff"}, {"name": "rear_foot_goal", "parent": "root", "x": 16.72, "y": 15.41, "color": "ff3f00ff"}, {"name": "rear_leg_goal", "parent": "rear_foot_goal", "x": -63.76, "y": 37.99, "color": "ff3f00ff"}, {"name": "rear_leg1", "parent": "hip", "length": 113.14, "rotation": -54.76, "x": 27.59, "y": -35.62, "color": "e07800ff"}, {"name": "rear_leg2", "parent": "rear_leg1", "length": 86.29, "rotation": -92.25, "x": 113.16, "y": 0.12, "color": "e07800ff"}, {"name": "rear_leg3", "parent": "rear_leg2", "length": 51.53, "rotation": 82.82, "x": 86.16, "y": 1.11, "color": "e07800ff"}, {"name": "rear_foot1", "parent": "rear_leg3", "length": 42.26, "rotation": 75.43, "x": 51.19, "y": -0.01, "color": "e07800ff"}, {"name": "rear_foot2", "parent": "rear_foot1", "length": 51.15, "rotation": -6.14, "x": 42.25, "y": -0.17, "transform": "noRotationOrReflection", "color": "e07800ff"}, {"name": "rear_hand", "parent": "rear_arm2", "length": 22.91, "rotation": -76.28, "x": 42.9, "y": 0.05, "color": "e07800ff"}, {"name": "saddle_strap_front1", "parent": "saddle", "length": 48.64, "rotation": -148.12, "x": -13.68, "y": -36.69, "color": "ff7300ff"}, {"name": "saddle_strap_front2", "parent": "saddle_strap_front1", "length": 51.37, "rotation": -11.14, "x": 48.65, "y": 0.16, "color": "ff7300ff"}, {"name": "saddle_strap_rear1", "parent": "saddle", "length": 19.31, "rotation": 151.14, "x": -16.67, "y": 43.67, "color": "ff7300ff"}, {"name": "saddle_strap_rear2", "parent": "saddle_strap_rear1", "length": 27.18, "x": 19.32, "y": -0.01, "color": "ff7300ff"}, {"name": "saddle_strap_rear3", "parent": "saddle_strap_rear2", "length": 22.02, "rotation": 3.63, "x": 27.43, "y": 0.1, "color": "ff7300ff"}, {"name": "stirrup", "parent": "saddle", "length": 39.08, "rotation": -68.86, "x": -40.97, "y": -51.69, "color": "ff3f00ff"}, {"name": "stirrup_strap1", "parent": "saddle", "length": 21.85, "rotation": -135, "x": -10.19, "y": -14.69, "color": "ff7300ff"}, {"name": "stirrup_strap2", "parent": "stirrup_strap1", "length": 25.81, "rotation": 9.39, "x": 21.85, "color": "ff7300ff"}, {"name": "tail1", "parent": "hip", "length": 81.26, "rotation": 162.93, "x": -10.44, "y": 3.43, "color": "eaff00ff"}, {"name": "tail2", "parent": "tail1", "length": 65.01, "rotation": 30.31, "x": 81.26, "y": -0.41, "color": "eaff00ff"}, {"name": "tail3", "parent": "tail2", "length": 70.53, "rotation": 6.89, "x": 65.01, "y": 0.05, "color": "eaff00ff"}, {"name": "tail4", "parent": "tail3", "length": 63.13, "rotation": -18.86, "x": 70.53, "y": 0.32, "color": "eaff00ff"}, {"name": "tail5", "parent": "tail4", "length": 45.53, "rotation": -22.35, "x": 63.13, "y": -0.23, "color": "eaff00ff"}, {"name": "tongue1", "parent": "head", "length": 27.56, "rotation": -129.04, "x": 10.41, "y": -52.37, "color": "ffff00ff"}, {"name": "tongue2", "parent": "tongue1", "length": 22.33, "rotation": 8.93, "x": 27.8, "y": 0.47, "color": "fff200ff"}, {"name": "tongue3", "parent": "tongue2", "length": 21.83, "rotation": 12.86, "x": 22.14, "y": -0.1, "color": "fff200ff"}], "slots": [{"name": "back_hand", "bone": "back_hand", "attachment": "back_hand"}, {"name": "back_arm", "bone": "back_arm", "attachment": "back_arm"}, {"name": "back_bracer", "bone": "back_bracer", "attachment": "back_bracer"}, {"name": "back_knee", "bone": "back_knee", "attachment": "back_knee"}, {"name": "raptor_horn_back", "bone": "horn_rear", "attachment": "raptor_horn_back"}, {"name": "raptor_tongue", "bone": "root", "attachment": "raptor_tongue"}, {"name": "raptor_hindleg_back", "bone": "rear_leg1", "attachment": "raptor_hindleg_back"}, {"name": "raptor_arm_back", "bone": "root", "attachment": "raptor_arm_back"}, {"name": "raptor_body", "bone": "torso1", "attachment": "raptor_body"}, {"name": "back_thigh", "bone": "back_thigh", "attachment": "back_thigh"}, {"name": "raptor_saddle_strap_front", "bone": "saddle_strap_front1", "attachment": "raptor_saddle_strap_front"}, {"name": "raptor_saddle_strap_rear", "bone": "saddle_strap_rear1", "attachment": "raptor_saddle_strap_rear"}, {"name": "raptor_saddle_w_shadow", "bone": "saddle", "attachment": "raptor_saddle_w_shadow"}, {"name": "raptor_saddle_noshadow", "bone": "saddle"}, {"name": "raptor_front_arm", "bone": "root", "attachment": "raptor_front_arm"}, {"name": "raptor_front_leg", "bone": "front_leg1", "attachment": "raptor_front_leg"}, {"name": "raptor_jaw", "bone": "jaw", "attachment": "raptor_jaw"}, {"name": "neck", "bone": "neck2", "attachment": "neck"}, {"name": "spineboy_torso", "bone": "spineboy_torso", "attachment": "torso"}, {"name": "head", "bone": "head2", "attachment": "head"}, {"name": "eyes_open", "bone": "head2", "attachment": "eyes_open"}, {"name": "mouth_smile", "bone": "head2", "attachment": "mouth_smile"}, {"name": "visor", "bone": "head2", "attachment": "visor"}, {"name": "raptor_horn", "bone": "horn_front", "attachment": "raptor_horn"}, {"name": "front_thigh", "bone": "front_thigh", "attachment": "front_thigh"}, {"name": "stirrup_back", "bone": "stirrup", "attachment": "stirrup_back"}, {"name": "lower_leg", "bone": "lower_leg", "attachment": "lower_leg"}, {"name": "stirrup_strap", "bone": "stirrup", "attachment": "stirrup_strap"}, {"name": "stirrup_front", "bone": "stirrup", "attachment": "stirrup_front"}, {"name": "gun", "bone": "gun", "attachment": "gun_nohand"}, {"name": "front_arm", "bone": "front_arm", "attachment": "front_arm"}, {"name": "front_bracer", "bone": "front_bracer", "attachment": "front_bracer"}, {"name": "front_hand", "bone": "front_hand2", "attachment": "front_hand"}], "ik": [{"name": "front_arm_goal", "bones": ["front_arm", "front_bracer"], "target": "front_arm_target"}, {"name": "front_foot_goal", "order": 2, "bones": ["front_leg3", "front_foot1"], "target": "front_foot_goal"}, {"name": "front_leg_goal", "order": 1, "bones": ["front_leg1", "front_leg2"], "target": "front_leg_goal", "bendPositive": false}, {"name": "rear_arm_goal", "order": 3, "bones": ["back_arm", "back_bracer"], "target": "rear_arm_target"}, {"name": "rear_foot_goal", "order": 5, "bones": ["rear_leg3", "rear_foot1"], "target": "rear_foot_goal"}, {"name": "rear_leg_goal", "order": 4, "bones": ["rear_leg1", "rear_leg2"], "target": "rear_leg_goal", "bendPositive": false}, {"name": "spineboy_front_leg_goal", "order": 6, "bones": ["front_thigh", "lower_leg"], "target": "spineboy_front_arm_goal", "bendPositive": false}, {"name": "spineboy_rear_leg_goal", "order": 7, "bones": ["back_thigh", "back_knee"], "target": "spineboy_rear_arm_goal", "bendPositive": false}, {"name": "stirrup", "order": 8, "bones": ["stirrup_strap1", "stirrup_strap2"], "target": "stirrup"}], "skins": [{"name": "default", "attachments": {"back_arm": {"back_arm": {"x": 14.86, "y": 1.02, "rotation": 16.76, "width": 46, "height": 29}}, "back_bracer": {"back_bracer": {"x": 6.6, "y": -2.14, "rotation": -0.73, "width": 39, "height": 28}}, "back_hand": {"back_hand": {"x": 9.31, "y": 2.12, "rotation": -10.99, "width": 36, "height": 34}}, "back_knee": {"back_knee": {"x": 22.89, "y": 10.23, "rotation": 74.23, "width": 49, "height": 67}}, "back_thigh": {"back_thigh": {"x": 18.92, "y": -2.18, "rotation": 19.25, "width": 39, "height": 24}}, "eyes_open": {"eyes_open": {"x": 46.62, "y": -12.73, "rotation": -70.58, "width": 47, "height": 45}}, "front_arm": {"front_arm": {"x": 15.69, "y": 2.55, "rotation": 14.02, "width": 48, "height": 30}}, "front_bracer": {"front_bracer": {"x": 5.84, "y": -0.69, "rotation": -6.28, "width": 41, "height": 29}}, "front_hand": {"front_hand": {"x": 17.85, "y": 3.92, "rotation": -13.97, "width": 41, "height": 38}, "front_open_hand": {"x": 21.27, "y": 2.31, "rotation": 62.19, "width": 43, "height": 44}, "gun": {"x": 49.46, "y": 11.49, "rotation": 56.35, "width": 107, "height": 103}}, "front_thigh": {"front_thigh": {"x": 22.85, "y": -1.55, "rotation": 16.66, "width": 57, "height": 29}}, "gun": {"gun_nohand": {"type": "mesh", "uvs": [0.71081, 0.16149, 0.85808, 0.41785, 1, 0.6649, 1, 1, 0.71457, 1, 0.49803, 0.69051, 0.30183, 0.41009, 0, 0.58226, 0, 0.11741, 0.27187, 0.1243, 0.24857, 0, 0.36658, 0, 0.61804, 0, 0.70575, 0.53546, 0.53669, 0.26855], "triangles": [3, 13, 2, 5, 13, 4, 3, 4, 13, 13, 6, 14, 13, 5, 6, 13, 1, 2, 6, 8, 9, 6, 7, 8, 13, 14, 1, 14, 0, 1, 6, 9, 14, 9, 11, 14, 14, 12, 0, 14, 11, 12, 9, 10, 11], "vertices": [11.74, 25.32, 41.94, 23.17, 71.03, 21.09, 98.96, 1.67, 81.85, -22.93, 43.08, -23.67, 7.95, -24.34, 4.21, -60.34, -34.53, -33.41, -17.67, -10.37, -29.42, -5.18, -22.34, 5, -7.28, 26.68, 42.6, 3.22, 10.23, 4.11], "hull": 13, "edges": [14, 12, 6, 8, 6, 4, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 8, 10, 10, 12, 6, 26, 10, 26, 4, 2, 26, 2, 22, 28, 28, 26, 12, 28, 2, 0, 0, 24, 28, 0, 18, 12], "width": 105, "height": 102}}, "head": {"head": {"x": 66.17, "y": 0.6, "rotation": -70.58, "width": 136, "height": 149}}, "lower_leg": {"lower_leg": {"x": 38.1, "y": 11.1, "rotation": 66.28, "width": 73, "height": 98}}, "mouth_smile": {"mouth_smile": {"x": 13.83, "y": -15.66, "rotation": -70.58, "width": 47, "height": 30}}, "neck": {"neck": {"x": 7.55, "y": -0.83, "rotation": -58.92, "width": 18, "height": 21}}, "raptor_arm_back": {"raptor_arm_back": {"type": "mesh", "uvs": [0.38712, 0.29362, 0.31383, 0.46513, 0.29243, 0.51522, 0.32476, 0.49311, 0.57587, 0.32139, 0.63255, 0.28263, 0.71632, 0.34508, 0.94948, 0.51888, 0.94948, 0.60129, 1, 0.65257, 1, 0.90624, 0.95463, 0.99934, 0.88957, 0.83205, 0.80295, 0.99999, 0.75236, 0.75696, 0.6654, 0.71301, 0.62289, 0.63243, 0.58195, 0.65032, 0.22479, 0.80641, 0.07792, 0.73315, 0.07825, 0.6655, 0.07985, 0.34307, 0, 0.29728, 0, 0, 0.32335, 0], "triangles": [13, 14, 12, 11, 12, 10, 12, 9, 10, 12, 8, 9, 12, 14, 8, 14, 15, 8, 8, 15, 7, 16, 17, 4, 6, 7, 15, 5, 16, 4, 5, 6, 16, 6, 15, 16, 18, 3, 17, 18, 2, 3, 18, 19, 2, 19, 20, 2, 17, 3, 4, 2, 20, 1, 1, 20, 21, 1, 21, 0, 0, 21, 24, 24, 21, 23, 21, 22, 23], "vertices": [2, 40, 18.48, 16.66, 0.91667, 41, 34.27, 20.52, 0.08333, 2, 40, 33.01, 10.18, 0.76814, 41, 20.71, 12.2, 0.23186, 2, 40, 37.26, 8.29, 0.64468, 41, 16.75, 9.77, 0.35532, 3, 40, 35.44, 10.98, 0.2767, 41, 20, 9.73, 0.67508, 49, -14.84, -19.95, 0.04822, 3, 40, 21.39, 31.95, 0.11484, 41, 45.24, 9.48, 0.60855, 49, -8.6, 4.51, 0.27661, 2, 41, 50.93, 9.42, 0.45956, 49, -7.2, 10.02, 0.54044, 2, 41, 53.24, 1.04, 0.0625, 49, 1.49, 10.28, 0.9375, 1, 49, 25.66, 10.99, 1, 1, 49, 30.2, 5.55, 1, 1, 49, 36.19, 4.81, 1, 1, 49, 50.19, -11.94, 1, 1, 49, 52.48, -20.45, 1, 1, 49, 39.19, -12.81, 1, 1, 49, 43.03, -28.42, 1, 1, 49, 26.46, -15.02, 1, 2, 41, 31.13, -21.97, 0.0625, 49, 18.59, -16.67, 0.9375, 2, 41, 32.44, -14.33, 0.3125, 49, 11.49, -13.57, 0.6875, 2, 41, 28.85, -13.59, 0.30612, 49, 9.92, -16.89, 0.69388, 2, 40, 62.1, 1.92, 0.19395, 41, -2.55, -7.12, 0.80605, 2, 40, 55.39, -9.82, 0.3125, 41, -8.44, 5.05, 0.6875, 2, 40, 49.58, -9.6, 0.51614, 41, -4.97, 9.72, 0.48386, 2, 40, 21.86, -8.52, 0.9375, 41, 11.59, 31.97, 0.0625, 1, 40, 17.7, -14.89, 1, 1, 40, -7.84, -14.01, 1, 1, 40, -6.94, 12.32, 1], "hull": 25, "edges": [44, 46, 44, 42, 38, 36, 32, 30, 30, 28, 28, 26, 24, 22, 18, 16, 16, 14, 46, 48, 38, 4, 6, 4, 6, 36, 42, 40, 40, 38, 4, 2, 2, 0, 40, 2, 10, 32, 36, 34, 34, 32, 10, 8, 8, 6, 34, 8, 14, 12, 12, 10, 12, 30, 18, 20, 22, 20, 26, 24, 48, 0], "width": 82, "height": 86}}, "raptor_body": {"raptor_body": {"type": "mesh", "uvs": [0.89014, 0.11137, 1, 0.22194, 1, 0.42848, 0.8818, 0.38589, 0.87401, 0.47987, 0.84783, 0.51728, 0.82505, 0.54984, 0.82403, 0.61606, 0.82306, 0.67973, 0.74042, 0.8671, 0.61597, 0.93098, 0.49649, 0.90968, 0.41187, 0.7138, 0.36955, 0.70087, 0.32823, 0.68824, 0.30083, 0.69963, 0.27516, 0.71029, 0.25302, 0.71948, 0.22569, 0.73083, 0.20832, 0.72362, 0.19092, 0.7164, 0.15952, 0.70337, 0.1301, 0.69117, 0.09227, 0.67547, 0.06029, 0.63165, 0.02855, 0.58817, 0, 0.49874, 0.05046, 0.53494, 0.08267, 0.54507, 0.11816, 0.55623, 0.14734, 0.54162, 0.17913, 0.52569, 0.20325, 0.51361, 0.22867, 0.50087, 0.24872, 0.47664, 0.27524, 0.44459, 0.32027, 0.39015, 0.37518, 0.35748, 0.43477, 0.32202, 0.48931, 0.35534, 0.56021, 0.39867, 0.61588, 0.40674, 0.6777, 0.4157, 0.69095, 0.31314, 0.69363, 0.14742, 0.79219, 0.08354, 0.51541, 0.74573, 0.62393, 0.75425, 0.70856, 0.7287, 0.76133, 0.63288, 0.7566, 0.49455, 0.80613, 0.27517, 0.65886, 0.59038, 0.53929, 0.54937, 0.42633, 0.52207, 0.32461, 0.55242, 0.22715, 0.61801, 0.10575, 0.61341, 0.03969, 0.5611, 0.77917, 0.39462, 0.37557, 0.53721, 0.27743, 0.58417, 0.16959, 0.61583, 0.0726, 0.58716, 0.87546, 0.31684, 0.85488, 0.21417, 0.81012, 0.17403, 0.83214, 0.25663, 0.83823, 0.32215, 0.84622, 0.41719, 0.59955, 0.57004, 0.49075, 0.53764, 0.76917, 0.43889, 0.75913, 0.56846, 0.87101, 0.3701, 0.85432, 0.43545, 0.89558, 0.32412, 0.90106, 0.22877, 0.91524, 0.20564, 0.93086, 0.219, 0.93447, 0.25859, 0.91957, 0.2776, 0.90611, 0.26424, 0.9415, 0.25929, 0.93589, 0.21546, 0.91669, 0.19193, 0.89297, 0.22202, 0.90245, 0.28513, 0.92007, 0.281, 0.92144, 0.2962, 0.94856, 0.26431, 0.19894, 0.61694, 0.13974, 0.6147, 0.25159, 0.60156, 0.88779, 0.26675], "triangles": [91, 21, 62, 92, 30, 62, 20, 21, 91, 21, 22, 62, 62, 22, 92, 22, 23, 92, 92, 23, 57, 23, 24, 57, 24, 63, 57, 24, 25, 63, 57, 29, 92, 92, 29, 30, 57, 63, 29, 25, 58, 63, 25, 26, 58, 63, 58, 28, 63, 28, 29, 28, 58, 27, 58, 26, 27, 18, 19, 17, 17, 19, 56, 56, 19, 20, 20, 91, 56, 93, 91, 31, 31, 32, 93, 91, 62, 31, 62, 30, 31, 12, 60, 54, 60, 13, 55, 93, 61, 14, 61, 55, 14, 60, 55, 35, 54, 60, 36, 60, 35, 36, 36, 37, 54, 16, 17, 56, 16, 56, 15, 56, 93, 15, 56, 91, 93, 93, 32, 61, 61, 32, 33, 61, 33, 55, 33, 34, 55, 55, 34, 35, 15, 93, 14, 13, 14, 55, 10, 11, 47, 47, 11, 46, 11, 12, 46, 46, 70, 47, 12, 71, 46, 13, 60, 12, 46, 71, 53, 12, 54, 71, 53, 40, 41, 53, 71, 40, 40, 71, 39, 71, 54, 39, 39, 37, 38, 39, 54, 37, 72, 59, 69, 47, 52, 48, 48, 49, 8, 48, 52, 49, 8, 49, 7, 52, 73, 49, 49, 73, 7, 7, 73, 6, 73, 52, 50, 50, 52, 42, 52, 70, 42, 73, 50, 6, 6, 50, 5, 50, 72, 5, 50, 42, 72, 72, 42, 59, 59, 42, 43, 10, 47, 9, 47, 48, 9, 9, 48, 8, 47, 70, 52, 46, 53, 70, 70, 41, 42, 70, 53, 41, 5, 72, 75, 5, 75, 4, 75, 72, 69, 4, 75, 3, 75, 69, 3, 3, 76, 2, 76, 89, 2, 89, 90, 2, 90, 1, 2, 69, 74, 3, 74, 69, 68, 69, 59, 68, 59, 51, 68, 59, 43, 51, 3, 74, 76, 74, 64, 76, 74, 68, 64, 76, 87, 89, 76, 64, 87, 51, 67, 68, 68, 67, 64, 64, 94, 87, 64, 67, 94, 43, 44, 51, 90, 88, 83, 80, 83, 81, 87, 88, 89, 88, 90, 89, 87, 81, 88, 87, 82, 81, 87, 94, 82, 83, 88, 81, 81, 82, 80, 51, 66, 67, 51, 44, 66, 67, 65, 94, 94, 77, 82, 94, 86, 77, 94, 65, 86, 1, 90, 84, 82, 77, 80, 84, 90, 83, 77, 79, 80, 84, 83, 79, 83, 80, 79, 67, 66, 65, 77, 78, 79, 77, 86, 78, 78, 86, 85, 79, 78, 84, 78, 85, 84, 85, 0, 1, 85, 86, 65, 0, 85, 65, 65, 66, 0, 1, 84, 85, 44, 45, 66, 66, 45, 0], "vertices": [1, 8, 73.75, -72.75, 1, 1, 8, 44.71, -140.81, 1, 1, 8, -14.12, -142.96, 1, 1, 8, -7.29, -97.34, 1, 5, 8, -30.99, -66.13, 0.41198, 2, 181.61, 43.87, 0.02179, 6, 119.19, -42.06, 0.20397, 7, 16.05, -70.43, 0.18915, 39, 64.79, 3.19, 0.17311, 5, 8, -42.72, -50.67, 0.3086, 2, 166.35, 31.85, 0.06905, 6, 99.79, -41.52, 0.29425, 7, 1.85, -57.19, 0.2194, 39, 63.67, -13.32, 0.1087, 5, 8, -52.59, -37.67, 0.21862, 2, 153.54, 21.75, 0.11018, 6, 83.47, -41.07, 0.37282, 7, -10.1, -46.07, 0.24573, 39, 61.54, -32.4, 0.05265, 5, 8, -71.49, -37.67, 0.10716, 2, 153.88, 2.85, 0.18147, 6, 71.63, -55.79, 0.56512, 7, -28.72, -49.29, 0.12045, 39, 77.43, -41.74, 0.02581, 2, 2, 154.35, -15.28, 0.25, 6, 60.38, -70.02, 0.75, 2, 2, 106.97, -71.35, 0.75, 6, -11.92, -82.73, 0.25, 3, 2, 32.22, -93.68, 0.3114, 58, -79.23, 79.17, 0.1038, 1, 42.08, -95.49, 0.5848, 1, 1, -30.74, -89.43, 1, 7, 2, -96.92, -39.19, 0.01006, 58, 59.24, 57.38, 0.07207, 1, -83.46, -33.97, 0.66667, 59, 8.59, 61.24, 0.17087, 60, -50.35, 66.28, 0.06656, 61, -136.27, 19.85, 0.0127, 62, -190.57, -61.99, 0.00106, 7, 2, -123.14, -37.01, 0.00848, 58, 85.21, 61.56, 0.06072, 1, -108.85, -30.67, 0.56148, 59, 33.35, 52.39, 0.19505, 60, -26.55, 55.1, 0.11161, 61, -110.06, 17.66, 0.03559, 62, -165.7, -53.44, 0.02706, 7, 2, -148.73, -34.87, 0.00701, 58, 110.56, 65.66, 0.0502, 1, -133.83, -27.57, 0.46388, 59, 57.54, 43.74, 0.2135, 60, -3.29, 44.2, 0.15182, 61, -84.46, 15.51, 0.05914, 62, -141.41, -45.1, 0.05446, 7, 2, -161.19, -44.85, 0.00572, 58, 122.77, 73.72, 0.04099, 1, -149.43, -31, 0.37833, 59, 73.25, 43.04, 0.22288, 60, 13.34, 41.69, 0.18429, 61, -67.5, 20.67, 0.0837, 62, -128.76, -30.33, 0.08408, 7, 2, -179.62, -42.55, 0.00461, 58, 138.7, 80.55, 0.03306, 1, -164.35, -34.62, 0.30452, 59, 89.36, 43.21, 0.22347, 60, 28.34, 40.65, 0.20919, 61, -53.56, 23.16, 0.10924, 62, -116.22, -25.64, 0.11589, 7, 2, -188.09, -53.92, 0.00362, 58, 147.15, 88.24, 0.02592, 1, -177.01, -37.71, 0.23783, 59, 101.64, 43.26, 0.21876, 60, 41.53, 38.51, 0.2295, 61, -39.78, 26.77, 0.13534, 62, -105.45, -14.15, 0.14903, 7, 2, -208.41, -49.71, 0.00302, 58, 164.91, 94.43, 0.02166, 1, -192.67, -41.58, 0.19674, 59, 119.03, 42.71, 0.19918, 60, 57.83, 37.33, 0.23165, 61, -24.77, 30.3, 0.16136, 62, -92.75, -7.49, 0.18639, 7, 2, -214.01, -58.41, 0.00251, 58, 171.56, 98.07, 0.01806, 1, -203.23, -39.95, 0.16063, 59, 127.67, 39.43, 0.17874, 60, 66.92, 31.59, 0.23028, 61, -13.52, 28.42, 0.18642, 62, -81.79, -2.63, 0.22335, 7, 2, -224.71, -58.12, 0.0021, 58, 182.09, 100.04, 0.0152, 1, -213.87, -38.74, 0.12946, 59, 137.74, 35.81, 0.15814, 60, 76.49, 26.8, 0.22594, 61, -2.91, 26.98, 0.21025, 62, -71.43, 0.06, 0.25892, 7, 2, -249.11, -44.1, 0.00179, 58, 205.76, 98.78, 0.01311, 1, -232.99, -36.29, 0.10272, 59, 156.91, 26.81, 0.1384, 60, 94.03, 17.91, 0.21975, 61, 15.93, 24.65, 0.23252, 62, -53.24, 3.74, 0.29172, 7, 2, -262.15, -56.7, 0.00158, 58, 218.99, 106.52, 0.01187, 1, -251.04, -34.1, 0.07984, 59, 172.87, 22.77, 0.12065, 60, 109.81, 9.65, 0.21311, 61, 34.15, 21.51, 0.25282, 62, -35.06, 9.1, 0.32013, 7, 2, -290.48, -38.4, 0.00149, 58, 247.28, 103.21, 0.01162, 1, -274.17, -31.61, 0.06028, 59, 195.41, 10.65, 0.10604, 60, 130.82, -1.83, 0.20746, 61, 57.28, 18.92, 0.27064, 62, -13.07, 15.17, 0.34249, 7, 2, -310.62, -26.99, 0.00153, 58, 269.58, 96.99, 0.01256, 1, -294.52, -21.09, 0.0436, 59, 211.94, -5.55, 0.09566, 60, 145.73, -19.53, 0.20413, 61, 77.42, 7.5, 0.2853, 62, 9.95, 12.84, 0.35722, 7, 2, -330.61, -15.68, 0.00173, 58, 291.71, 90.82, 0.01498, 1, -314.89, -10.76, 0.02939, 59, 228.35, -21.64, 0.09055, 60, 160.54, -37.1, 0.20423, 61, 97.4, -3.84, 0.29603, 62, 32.81, 10.52, 0.36309, 7, 2, -349.38, 8.82, 0.00213, 58, 315.83, 71.56, 0.01929, 1, -334.67, 12.86, 0.01717, 59, 240.17, -50.15, 0.09165, 60, 169.6, -66.6, 0.20843, 61, 116.15, -28.34, 0.30195, 62, 59.85, -4.35, 0.35937, 7, 2, -318.11, 0.2, 0.00187, 58, 283.4, 72.39, 0.02216, 1, -303.04, 5.61, 0.01209, 59, 212.18, -33.76, 0.09479, 60, 143.29, -47.63, 0.21972, 61, 84.89, -19.7, 0.30023, 62, 27.76, -9.04, 0.34916, 7, 2, -298.35, -1.61, 0.00183, 58, 263.78, 69.39, 0.02792, 1, -283.12, 4.7, 0.00829, 59, 193.54, -26.92, 0.10537, 60, 125.39, -39.06, 0.2343, 61, 65.12, -17.87, 0.29251, 62, 8.94, -15.34, 0.32978, 7, 2, -276.58, -3.6, 0.00202, 58, 242.17, 66.08, 0.03719, 1, -261.17, 3.52, 0.00553, 59, 173.02, -19.39, 0.1236, 60, 105.68, -29.61, 0.2506, 61, 43.35, -15.86, 0.27865, 62, -11.8, -22.27, 0.30241, 7, 2, -258.48, -12.97, 0.00247, 58, 224.59, 62.99, 0.05073, 1, -243.62, 9.16, 0.00359, 59, 155.73, -17.63, 0.14934, 60, 87.94, -28.42, 0.26648, 61, 25.77, -21.57, 0.25871, 62, -26.44, -33.94, 0.26868, 7, 2, -239.94, 7.12, 0.00318, 58, 209.2, 46.86, 0.06938, 1, -224.55, 15.03, 0.00227, 59, 134.86, -20.32, 0.1822, 60, 67.6, -26.92, 0.27949, 61, 6.72, -26.56, 0.23299, 62, -41.01, -46.83, 0.2305, 7, 2, -225.82, 0.17, 0.00417, 58, 195.41, 43.29, 0.09393, 1, -209.95, 19.42, 0.00139, 59, 120.6, -19.91, 0.22145, 60, 52.79, -26.47, 0.28716, 61, -8.12, -31.08, 0.20207, 62, -54.17, -55.62, 0.18983, 7, 2, -210.18, 15.83, 0.00547, 58, 182.4, 31.24, 0.12486, 1, -194.57, 23.94, 0.00082, 59, 103.86, -21.08, 0.26613, 60, 36.67, -24.72, 0.28758, 61, -23.06, -35.24, 0.16678, 62, -64.75, -66.79, 0.14837, 7, 2, -199.55, 14.49, 0.00748, 58, 172.74, 23.76, 0.17174, 1, -182.54, 31.43, 0.00059, 59, 91.18, -25.31, 0.31859, 60, 22.93, -28.32, 0.26575, 61, -35.78, -42.48, 0.12561, 62, -75.43, -76.68, 0.11024, 7, 2, -182.71, 33.4, 0.00957, 58, 159.98, 7.58, 0.2203, 1, -166.65, 41.33, 0.00037, 59, 72.8, -30.98, 0.37152, 60, 4.81, -31.63, 0.24137, 61, -50.53, -52.79, 0.08415, 62, -82.82, -93.92, 0.07273, 7, 2, -156.15, 50.39, 0.01176, 58, 138.29, -15.31, 0.27125, 1, -139.32, 57.81, 0.00018, 59, 42.76, -40.56, 0.42509, 60, -26, -38.31, 0.21337, 61, -77.11, -69.76, 0.04228, 62, -100.31, -120.16, 0.03608, 6, 2, -121.25, 62.21, 0.01402, 58, 107.25, -35.18, 0.32389, 59, 5.99, -42.99, 0.47912, 60, -62.85, -37.24, 0.18281, 61, -112.03, -81.56, 0.00012, 62, -127.5, -145.03, 3e-05, 6, 2, -83.36, 75.04, 0.02469, 58, 73.57, -56.75, 0.57034, 59, -33.92, -45.63, 0.38715, 60, -102.82, -36.08, 0.01756, 61, -149.92, -94.35, 0.00021, 62, -157.02, -172.01, 6e-05, 2, 2, -56.57, 67.93, 0.24192, 58, 45.86, -56.3, 0.75808, 2, 2, -21.07, 58.39, 0.14515, 58, 9.1, -55.59, 0.85485, 1, 2, 22.1, 53.55, 1, 2, 2, 70.05, 48.18, 0.22579, 6, 36.29, 32.71, 0.77421, 4, 8, 11.85, 45.88, 0.4875, 2, 68.85, 84.68, 0.05645, 6, 58.76, 61.51, 0.24355, 7, 39.15, 47.24, 0.2125, 2, 8, 59.09, 46.58, 0.75, 7, 85.58, 55.99, 0.25, 1, 8, 79.48, -12.8, 1, 1, 1, -20.32, -43.01, 1, 3, 2, 33.67, -43.33, 0.33216, 58, -68.51, 29.96, 0.08304, 1, 46.28, -45.31, 0.5848, 2, 2, 85.07, -33.15, 0.75, 6, -4.27, -39.36, 0.25, 2, 2, 115.88, -4.07, 0.4, 6, 38.02, -36.76, 0.6, 5, 8, -38.37, 4.59, 0.08552, 2, 111.03, 35.21, 0.16894, 6, 59.45, -3.5, 0.53731, 7, -3.29, -2, 0.17075, 39, 22.53, -54.01, 0.03748, 1, 8, 25.22, -23.28, 1, 1, 6, -4.94, 10.33, 1, 2, 2, -26.61, 10.27, 0.2, 58, 2.9, -7.55, 0.8, 6, 2, -90.36, 16.11, 0.0849, 58, 66.18, 2.13, 0.55724, 59, -11.99, 9.51, 0.34912, 60, -75.76, 16.73, 0.00851, 61, -142.87, -35.44, 0.00019, 62, -174.34, -115.26, 5e-05, 7, 2, -152.12, 3.98, 0.0403, 58, 123.19, 28.76, 0.26454, 1, -136.32, 11.31, 0.00014, 59, 50.81, 5.33, 0.41137, 60, -13.64, 6.6, 0.19983, 61, -81.11, -23.35, 0.04444, 62, -122.68, -79.3, 0.03938, 7, 2, -209.28, -17.56, 0.01927, 58, 173.49, 63.42, 0.12664, 1, -193.59, -9.46, 0.0008, 59, 111.59, 11.41, 0.27477, 60, 47.44, 6.89, 0.24467, 61, -23.93, -1.86, 0.17261, 62, -79.01, -36.58, 0.16124, 7, 2, -283.24, -20.28, 0.00611, 58, 244.62, 83.89, 0.04146, 1, -267.4, -13.35, 0.00553, 59, 183.76, -4.99, 0.13447, 60, 117.73, -16.29, 0.21639, 61, 50.03, 0.81, 0.28346, 62, -12.41, -4.32, 0.31258, 7, 2, -324.26, -7.6, 0.00312, 58, 287.48, 81.44, 0.02392, 1, -308.86, -2.42, 0.01209, 59, 220.12, -27.81, 0.09895, 60, 151.76, -42.46, 0.20698, 61, 91.04, -11.9, 0.30202, 62, 30.24, 0.57, 0.35292, 3, 8, -9.4, -8.1, 0.2, 6, 87.5, 11.11, 0.2, 7, 27.42, -9.57, 0.6, 6, 2, -121.18, 10.06, 0.04821, 58, 94.63, 15.42, 0.31646, 59, 19.34, 7.42, 0.45752, 60, -44.76, 11.68, 0.17767, 61, -112.06, -29.41, 0.00011, 62, -148.56, -97.32, 3e-05, 7, 2, -179.79, -6.45, 0.03247, 58, 147.54, 45.54, 0.21318, 1, -164.12, 1.45, 0.00029, 59, 80.23, 8.27, 0.3652, 60, 15.93, 6.74, 0.22126, 61, -53.43, -12.95, 0.0887, 62, -101.54, -58.62, 0.0789, 7, 2, -244.35, -18.85, 0.01114, 58, 207.22, 73.13, 0.07359, 1, -228.58, -11.01, 0.00226, 59, 145.8, 3.64, 0.19521, 60, 80.77, -4.1, 0.2345, 61, 11.14, -0.6, 0.23931, 62, -47.44, -21.28, 0.24399, 7, 2, -303.82, -13.92, 0.00442, 58, 266.13, 82.66, 0.03118, 1, -288.17, -7.81, 0.00829, 59, 202.01, -16.43, 0.11329, 60, 134.81, -29.42, 0.20984, 61, 70.61, -5.57, 0.29596, 62, 8.99, -1.87, 0.33703, 1, 8, 13.2, -83.04, 1, 1, 8, 43.6, -53.06, 1, 1, 8, 54.1, -24.82, 1, 2, 8, 30.86, -41.07, 0.50022, 39, 2.21, 26.42, 0.49978, 2, 8, 11.43, -54.71, 0.50022, 39, 25.76, 23.36, 0.49978, 5, 8, -15.15, -59.01, 0.48739, 2, 174.2, 59.56, 0.00694, 6, 123.56, -25.26, 0.065, 7, 30.43, -60.71, 0.06028, 39, 48.29, 8.61, 0.38039, 1, 2, 13.36, 7.4, 1, 2, 2, -53.99, 12.84, 0.24192, 58, 30.09, -3.45, 0.75808, 5, 8, -22.22, -2.44, 0.14928, 2, 117.76, 51.49, 0.07485, 6, 75.06, 4.68, 0.34944, 7, 13.82, -6.18, 0.40983, 39, 17.02, -37.2, 0.01661, 5, 8, -59.37, 2.42, 0.03983, 2, 113.57, 14.24, 0.29239, 6, 47.98, -21.23, 0.5708, 7, -23.62, -7.72, 0.07952, 39, 42.42, -64.75, 0.01746, 2, 8, 2.6, -76.56, 0.87619, 39, 45.49, 35.6, 0.12381, 5, 8, -20.33, -60.61, 0.46542, 2, 175.9, 54.42, 0.01127, 6, 121.57, -30.3, 0.10549, 7, 25.61, -63.17, 0.09782, 39, 52.86, 8.67, 0.32, 1, 8, 11.85, -92.61, 1, 1, 8, 39.82, -87.97, 1, 1, 8, 46.98, -93.79, 1, 1, 8, 43.54, -103.28, 1, 1, 8, 32.1, -108.37, 1, 1, 8, 26.12, -101.85, 1, 1, 8, 29.63, -93.52, 1, 1, 8, 32.13, -111.9, 1, 1, 8, 44.72, -105.71, 1, 1, 8, 51.03, -93.47, 1, 1, 8, 41.56, -83.07, 1, 1, 8, 23.42, -93.21, 1, 1, 8, 25.16, -102.18, 1, 1, 8, 20.85, -103.3, 1, 1, 8, 30.94, -115.49, 1, 7, 2, -224.06, -29.38, 0.01475, 58, 187.49, 71.81, 0.0971, 1, -210.74, -10.16, 0.00137, 59, 128.15, 8.71, 0.23314, 60, 63.72, 1.03, 0.24172, 61, -6.68, -1.52, 0.20842, 62, -64.07, -27.73, 0.20349, 7, 2, -259.77, -34.28, 0.00831, 58, 221.38, 84.1, 0.05534, 1, -246.74, -11.96, 0.00359, 59, 163.6, 2.21, 0.16201, 60, 98.14, -9.66, 0.22527, 61, 29.36, -0.53, 0.2645, 62, -31.12, -13.11, 0.28098, 7, 2, -193.22, -20.67, 0.02473, 58, 159.16, 56.81, 0.16236, 1, -179.26, -4.14, 0.00044, 59, 96.13, 10.07, 0.31904, 60, 32.1, 6.22, 0.24198, 61, -38.28, -6.84, 0.13288, 62, -91.28, -44.66, 0.11857, 2, 8, 28.49, -81.5, 0.8926, 39, 28.77, 56, 0.1074], "hull": 46, "edges": [22, 20, 20, 18, 18, 16, 6, 4, 4, 2, 90, 88, 54, 52, 52, 50, 24, 22, 88, 86, 86, 84, 8, 6, 24, 26, 26, 28, 72, 74, 74, 76, 70, 72, 46, 48, 48, 50, 54, 56, 56, 58, 80, 82, 82, 84, 76, 78, 78, 80, 8, 10, 10, 12, 12, 14, 14, 16, 0, 90, 0, 2, 62, 64, 64, 66, 40, 42, 58, 60, 60, 62, 42, 44, 44, 46, 66, 68, 68, 70, 32, 34, 34, 36, 52, 116, 116, 126, 126, 114, 114, 184, 184, 124, 124, 182, 182, 112, 112, 186, 186, 122, 122, 110, 110, 120, 120, 108, 108, 142, 142, 106, 106, 140, 140, 104, 92, 94, 94, 96, 96, 98, 98, 146, 146, 100, 100, 144, 144, 118, 118, 102, 8, 150, 150, 138, 138, 136, 136, 134, 134, 132, 156, 154, 154, 164, 164, 162, 162, 160, 160, 158, 158, 156, 180, 178, 178, 174, 174, 188, 188, 172, 170, 168, 28, 30, 30, 32, 36, 38, 38, 40], "width": 610, "height": 285}}, "raptor_front_arm": {"raptor_front_arm": {"type": "mesh", "uvs": [0.39563, 0.1396, 0.38771, 0.30213, 0.31231, 0.41784, 0.27287, 0.47836, 0.33389, 0.4507, 0.5488, 0.35329, 0.64093, 0.31153, 0.73024, 0.3653, 1, 0.5277, 1, 0.86607, 0.93243, 1, 0.86176, 0.80967, 0.75576, 0.99765, 0.71748, 1, 0.70276, 0.77443, 0.62032, 0.73448, 0.58793, 0.64519, 0.53561, 0.6582, 0.13449, 0.75798, 0, 0.69219, 0.01846, 0.56358, 0.05499, 0.30918, 0, 0.27863, 0, 0.12423, 0, 0, 0.19596, 0, 0.40243, 0, 0.24536, 0.19241, 0.21679, 0.0811], "triangles": [10, 11, 9, 13, 14, 12, 12, 14, 11, 11, 8, 9, 11, 14, 8, 8, 14, 7, 7, 14, 15, 15, 16, 7, 6, 16, 5, 6, 7, 16, 18, 4, 17, 18, 3, 4, 18, 19, 3, 16, 17, 5, 17, 4, 5, 23, 24, 25, 28, 25, 26, 23, 25, 28, 0, 28, 26, 19, 20, 3, 3, 20, 2, 2, 20, 21, 2, 21, 1, 21, 27, 1, 21, 22, 27, 1, 27, 0, 27, 23, 28, 27, 22, 23, 27, 28, 0], "vertices": [2, 21, 1.53, 15.94, 0.51075, 6, 33.28, -54.74, 0.48925, 1, 21, 17.93, 17.82, 1, 2, 21, 30.47, 13.56, 0.84641, 22, 23.25, 15.56, 0.15359, 3, 21, 37.03, 11.34, 0.34375, 22, 18.25, 10.77, 0.64063, 31, -22.62, -14.98, 0.01563, 3, 21, 33.51, 15.8, 0.10938, 22, 23.83, 11.85, 0.78125, 31, -20.47, -9.72, 0.10938, 3, 21, 21.08, 31.5, 0.01563, 22, 43.49, 15.63, 0.64063, 31, -12.87, 8.81, 0.34375, 2, 22, 51.92, 17.25, 0.34375, 31, -9.62, 16.75, 0.65625, 2, 22, 57.02, 9.76, 0.10938, 31, -0.56, 16.92, 0.89063, 2, 22, 72.43, -12.86, 0.02083, 31, 26.81, 17.44, 0.97917, 1, 31, 48.01, -9.58, 1, 1, 31, 52.1, -23.66, 1, 1, 31, 35.67, -11.99, 1, 1, 31, 40.69, -32.31, 1, 1, 31, 38.4, -34.41, 1, 2, 22, 41.59, -28.86, 0.02083, 31, 23.33, -17.13, 0.97917, 2, 22, 36.57, -22.88, 0.10938, 31, 15.57, -18.06, 0.89063, 2, 22, 36.99, -13.45, 0.34375, 31, 7.91, -12.55, 0.65625, 3, 21, 51.84, 35.15, 0.01563, 22, 32.56, -13.35, 0.64063, 31, 5.39, -16.2, 0.34375, 3, 21, 66.78, 4.57, 0.10938, 22, -1.47, -12.52, 0.78125, 31, -13.93, -44.24, 0.10938, 3, 21, 61.83, -7.22, 0.34375, 22, -9.65, -2.69, 0.64063, 31, -26.61, -45.71, 0.01563, 2, 21, 48.71, -7.72, 0.84641, 22, -4.04, 9.19, 0.15359, 1, 21, 22.74, -8.72, 1, 2, 21, 20.34, -13.59, 0.45035, 6, -0.85, -46.9, 0.54965, 2, 21, -1.37, -14.82, 0.44352, 6, 9.5, -36.47, 0.55648, 1, 6, 16.06, -24.23, 1, 1, 6, 28.78, -33.72, 1, 1, 6, 42.19, -43.71, 1, 2, 21, 8.22, 2.61, 0.71821, 6, 23.16, -50.94, 0.28179, 2, 21, -2.26, 2.66, 0.48851, 6, 26.41, -40.97, 0.51149], "hull": 27, "edges": [38, 36, 32, 30, 30, 28, 28, 26, 24, 26, 24, 22, 22, 20, 20, 18, 18, 16, 44, 42, 38, 6, 38, 40, 40, 42, 6, 4, 4, 2, 40, 4, 8, 6, 36, 8, 32, 12, 42, 2, 52, 0, 0, 2, 16, 14, 14, 12, 30, 14, 36, 34, 34, 32, 12, 10, 10, 8, 34, 10, 48, 50, 50, 52, 44, 46, 46, 48, 50, 56, 56, 54], "width": 81, "height": 102}}, "raptor_front_leg": {"raptor_front_leg": {"type": "mesh", "uvs": [0.55117, 0.17818, 0.6279, 0.36027, 0.66711, 0.4533, 0.6488, 0.51528, 0.53554, 0.56894, 0.32335, 0.66947, 0.28674, 0.72087, 0.32539, 0.80401, 0.36258, 0.80144, 0.42056, 0.79744, 0.61015, 0.78436, 0.84813, 0.84029, 1, 0.93855, 0.62439, 0.91738, 0.72813, 1, 0.58574, 1, 0.36708, 0.96668, 0.26307, 0.95082, 0.16267, 0.93552, 0.03859, 0.72238, 0, 0.66947, 0.0374, 0.62999, 0.1647, 0.49563, 0.23732, 0.45681, 0.2702, 0.43923, 0.28064, 0.43365, 0.223, 0.40571, 0.12565, 0.35851, 0, 0.2976, 0, 0.1524, 0, 0, 0.32132, 0, 0.32222, 0.22778, 0.44931, 0.38031, 0.47664, 0.44362, 0.4615, 0.47375, 0.35106, 0.53247, 0.20091, 0.65257, 0.18528, 0.72148, 0.25222, 0.86314, 0.30942, 0.88124, 0.55694, 0.89613, 0.55858, 0.89208, 0.47493, 0.8534, 0.6059, 0.91526, 0.39706, 0.8913, 0.1323, 0.09352, 0.36997, 0.45346, 0.37163, 0.43828, 0.32516, 0.39424, 0.2376, 0.34426, 0.34066, 0.47415], "triangles": [45, 43, 41, 45, 41, 44, 41, 42, 44, 43, 9, 10, 42, 43, 10, 41, 43, 42, 13, 44, 42, 10, 13, 42, 11, 13, 10, 13, 11, 12, 15, 44, 13, 19, 21, 38, 20, 21, 19, 39, 38, 6, 39, 6, 7, 40, 39, 7, 40, 7, 8, 18, 19, 38, 18, 38, 39, 17, 39, 40, 18, 39, 17, 45, 8, 9, 45, 9, 43, 45, 40, 8, 16, 40, 45, 17, 40, 16, 15, 45, 44, 16, 45, 15, 14, 15, 13, 46, 30, 31, 47, 25, 48, 24, 25, 47, 35, 48, 34, 47, 48, 35, 51, 24, 47, 23, 24, 51, 3, 34, 2, 35, 34, 3, 36, 51, 47, 36, 47, 35, 4, 35, 3, 36, 35, 4, 37, 22, 23, 21, 22, 37, 36, 37, 23, 36, 23, 51, 5, 37, 36, 5, 36, 4, 6, 37, 5, 38, 21, 37, 38, 37, 6, 29, 30, 46, 32, 31, 0, 46, 31, 32, 28, 29, 46, 46, 27, 28, 32, 50, 46, 50, 27, 46, 33, 32, 0, 33, 0, 1, 49, 50, 32, 33, 49, 32, 26, 27, 50, 26, 50, 49, 25, 26, 49, 48, 49, 33, 25, 49, 48, 34, 33, 1, 48, 33, 34, 34, 1, 2], "vertices": [3, 25, 64.01, 44.24, 0.83908, 26, -35.1, -67.07, 0.01332, 1, 79.42, -35.96, 0.1476, 2, 25, 109.78, 26.58, 0.77988, 26, -24.02, -19.3, 0.22012, 3, 25, 133.15, 17.56, 0.53532, 26, -18.36, 5.11, 0.46444, 28, 63.63, 122.73, 0.00025, 4, 25, 143.45, 4.9, 0.35077, 26, -7.28, 17.08, 0.64668, 28, 62.85, 106.44, 0.0023, 29, 50.69, 99.57, 0.00026, 4, 25, 140.77, -20.62, 0.0917, 26, 18.35, 18, 0.90197, 28, 43.83, 89.22, 0.00514, 29, 29.15, 85.64, 0.00119, 5, 25, 135.77, -68.43, 0.05609, 26, 66.39, 19.74, 0.69233, 28, 8.19, 56.97, 0.0224, 29, -11.23, 59.57, 0.00831, 27, 17.5, 39.38, 0.22088, 5, 25, 141.76, -82.13, 0.01988, 26, 79.11, 27.58, 0.50335, 28, 3.51, 42.77, 0.08322, 29, -18.14, 46.32, 0.03185, 27, 26.33, 27.32, 0.3617, 6, 25, 163.08, -89.65, 0.00798, 26, 83.57, 49.74, 0.21327, 28, 14.36, 22.94, 0.14107, 29, -10.63, 25, 0.22312, 27, 48.78, 24.63, 0.35075, 30, -36.15, 12.98, 0.0638, 6, 25, 166.99, -83.68, 0.00242, 26, 77.11, 52.78, 0.07519, 28, 21.25, 24.78, 0.2831, 29, -3.54, 25.7, 0.2694, 27, 51.28, 31.31, 0.22995, 30, -29.08, 14.02, 0.13993, 6, 25, 172.1, -74.85, 0.00049, 26, 67.13, 57.22, 0.01761, 28, 32.04, 27.62, 0.07977, 29, 7.57, 26.76, 0.36293, 27, 54.86, 41.7, 0.11397, 30, -18.05, 15.6, 0.42523, 1, 30, 17.91, 20.91, 1, 1, 30, 64.06, 8.97, 1, 1, 30, 94.36, -14.71, 1, 1, 30, 22.43, -13.09, 1, 1, 29, 66.59, -24.92, 1, 1, 29, 39.4, -25.08, 1, 5, 25, 199.66, -110.01, 0.00022, 26, 97.78, 89.72, 0.01703, 28, 29.17, -16.97, 0.2023, 29, -2.46, -16.78, 0.54324, 27, 89.74, 13.77, 0.2372, 5, 25, 185.21, -122.46, 0.00033, 26, 112.95, 76.25, 0.02513, 28, 8.94, -16.25, 0.29853, 29, -22.31, -12.81, 0.32598, 27, 77.53, -2.57, 0.35003, 5, 25, 170.19, -135.02, 0.00252, 26, 127.49, 63.14, 0.10129, 28, -10.62, -15.59, 0.20821, 29, -41.51, -8.99, 0.14723, 27, 65.61, -18.1, 0.54075, 5, 25, 112.56, -119.47, 0.0153, 26, 120.17, 3.9, 0.24037, 28, -43.16, 34.46, 0.12023, 29, -65.54, 45.65, 0.04529, 27, 5.97, -15.49, 0.57882, 5, 25, 97.32, -116.78, 0.04819, 26, 119.64, -11.55, 0.40428, 28, -52.7, 46.63, 0.04604, 29, -72.99, 59.21, 0.01698, 27, -9.48, -16.18, 0.48451, 5, 25, 93.82, -104.87, 0.09566, 26, 108.33, -16.68, 0.57617, 28, -47.35, 57.83, 0.01789, 29, -65.9, 69.39, 0.00376, 27, -15.49, -5.32, 0.30652, 4, 25, 81.93, -64.34, 0.19533, 26, 69.88, -34.13, 0.8011, 28, -29.17, 95.94, 0.00327, 29, -41.79, 104.07, 0.0003, 4, 25, 82.88, -47.24, 0.31922, 26, 52.79, -35.63, 0.67951, 28, -2.53, 110.36, 0.00117, 29, -28.16, 137.98, 0.0001, 4, 25, 83.2, -39.53, 0.46206, 26, 45.12, -36.38, 0.53752, 28, 2.78, 115.25, 0.0004, 29, -20.31, 143.09, 3e-05, 3, 25, 83.25, -37.08, 0.5378, 26, 42.72, -36.65, 0.46208, 28, -9.99, 115.36, 0.00012, 2, 25, 70.77, -41.24, 0.73138, 26, 48.56, -48.41, 0.26862, 3, 25, 49.88, -48.54, 0.81379, 26, 58.67, -68.11, 0.13997, 1, -1.28, -82.1, 0.04623, 3, 25, 22.5, -57.28, 0.8186, 26, 71.21, -93.94, 0.02099, 1, -25.55, -67.64, 0.16041, 3, 25, -8.1, -37.38, 0.6239, 26, 56.92, -126.54, 0.00952, 1, -21.48, -29.19, 0.36658, 2, 25, -37.37, -9.66, 0.31469, 1, -26.33, 8.78, 0.68531, 2, 25, 0.84, 38.38, 0.25576, 1, 35.03, 9.39, 0.74424, 1, 25, 46.78, 2.07, 1, 2, 25, 92.57, -3.34, 0.75462, 26, 7.99, -32.14, 0.24539, 2, 25, 108.56, -9.37, 0.50845, 26, 11.73, -15.47, 0.49155, 3, 25, 112.82, -16.46, 0.32512, 26, 18.16, -10.26, 0.6744, 28, 25.78, 110.97, 0.00047, 4, 25, 111.5, -42.37, 0.20061, 26, 43.99, -7.93, 0.79288, 28, 7.51, 92.57, 0.00581, 29, -6.14, 94.81, 0.0007, 5, 25, 117.81, -84.04, 0.07777, 26, 84.35, 4.14, 0.54932, 28, -15.6, 57.33, 0.03211, 29, -34.64, 63.78, 0.00667, 27, 3.38, 20.24, 0.33413, 5, 25, 129.82, -97.4, 0.01922, 26, 95.9, 17.91, 0.30498, 28, -15.57, 39.37, 0.09569, 29, -37.52, 46.04, 0.04369, 27, 18, 9.81, 0.53642, 5, 25, 166.27, -110.06, 0.00292, 26, 103.32, 55.77, 0.10777, 28, 3.13, 5.61, 0.23449, 29, -24.51, 9.72, 0.13567, 27, 56.35, 5.41, 0.51915, 4, 26, 96.26, 65.32, 0.03213, 28, 14.68, 2.86, 0.27334, 29, -13.56, 5.13, 0.35512, 27, 65.31, 13.21, 0.33941, 1, 30, 9.28, -8.31, 1, 1, 30, 9.53, -7.26, 1, 6, 25, 190.78, -75.2, 0.00031, 26, 65.36, 75.17, 0.00811, 28, 44.65, 15.21, 0.02559, 29, 18, 12.48, 0.37636, 27, 72.68, 44.77, 0.04102, 30, -6.95, 1.82, 0.54861, 1, 30, 18.88, -12.73, 1, 4, 26, 82.45, 76.78, 0.02264, 28, 32.72, 2.78, 0.19254, 29, 4.22, 2.14, 0.54574, 27, 75.6, 28, 0.23908, 2, 25, -4.64, -8.76, 0.59606, 1, 3.86, -15.43, 0.40394, 3, 25, 97.96, -26.91, 0.42356, 26, 30.56, -23.53, 0.57614, 28, 19.85, 112.61, 0.0003, 3, 25, 95.05, -24.23, 0.53228, 26, 28.31, -26.78, 0.46765, 28, 19.92, 116.56, 7e-05, 2, 25, 80.64, -24.13, 0.79874, 26, 30.22, -41.06, 0.20126, 3, 25, 60.19, -29.27, 0.84851, 26, 38.15, -60.6, 0.14442, 1, 20.52, -80.71, 0.00708, 4, 25, 98.68, -34.62, 0.33487, 26, 38.09, -21.74, 0.66324, 28, 15.18, 106.94, 0.0017, 29, -4.55, 131.21, 0.00018], "hull": 32, "edges": [40, 38, 38, 36, 28, 30, 28, 26, 26, 24, 24, 22, 22, 20, 14, 12, 12, 10, 6, 4, 60, 62, 0, 62, 40, 42, 42, 44, 34, 36, 16, 14, 52, 50, 4, 2, 2, 0, 10, 8, 8, 6, 72, 74, 74, 76, 76, 78, 78, 80, 80, 90, 90, 88, 16, 18, 18, 20, 30, 32, 32, 34, 56, 58, 58, 60, 94, 96, 96, 98, 52, 54, 54, 56, 100, 98, 48, 50, 44, 46, 46, 48, 102, 94, 72, 70, 70, 68, 66, 68, 66, 64, 64, 92, 86, 84, 50, 96, 94, 48, 46, 102, 52, 98, 54, 100, 70, 6, 68, 4, 66, 2, 72, 8, 86, 20, 86, 82, 82, 88, 84, 26, 88, 26], "width": 191, "height": 257}}, "raptor_hindleg_back": {"raptor_hindleg_back": {"type": "mesh", "uvs": [0.45041, 0.09352, 0.56934, 0.23361, 0.65294, 0.47297, 0.66354, 0.50822, 0.63175, 0.54255, 0.32384, 0.69723, 0.30069, 0.73876, 0.27934, 0.77704, 0.30417, 0.83513, 0.31059, 0.85014, 0.34101, 0.85047, 0.45165, 0.85164, 0.59556, 0.81882, 0.91177, 0.92548, 1, 1, 0.56337, 0.96427, 0.4835, 0.98261, 0.29879, 0.98027, 0.22808, 0.98389, 0.15998, 0.98738, 0.15424, 0.95547, 0.13895, 0.87048, 0.07371, 0.78726, 0, 0.753, 0, 0.7049, 0, 0.671, 0.11876, 0.64653, 0.16535, 0.5266, 0.28496, 0.47398, 0.29011, 0.45774, 0.29427, 0.4446, 0.20635, 0.40396, 0.06129, 0.33691, 0, 0.25247, 0, 0, 0.30793, 0, 0.276, 0.20262, 0.40398, 0.31122, 0.48439, 0.45964, 0.48318, 0.48384, 0.47029, 0.51062, 0.22698, 0.67328, 0.17142, 0.7242, 0.17122, 0.78242, 0.22996, 0.89469, 0.24677, 0.90829, 0.28672, 0.9146, 0.46583, 0.91414], "triangles": [15, 13, 14, 16, 47, 15, 15, 12, 13, 15, 47, 12, 18, 46, 17, 18, 45, 46, 17, 47, 16, 17, 46, 47, 47, 10, 11, 47, 46, 10, 47, 11, 12, 45, 18, 19, 44, 45, 20, 20, 45, 19, 20, 21, 44, 46, 9, 10, 46, 45, 9, 45, 44, 9, 21, 43, 44, 44, 8, 9, 44, 7, 8, 44, 43, 7, 21, 22, 43, 43, 22, 42, 43, 42, 7, 22, 23, 24, 24, 42, 22, 7, 42, 6, 42, 41, 6, 6, 41, 5, 24, 26, 42, 42, 26, 41, 24, 25, 26, 5, 40, 4, 5, 41, 40, 41, 28, 40, 26, 27, 41, 41, 27, 28, 40, 39, 4, 28, 29, 40, 40, 29, 39, 4, 39, 3, 39, 2, 3, 29, 30, 39, 39, 38, 2, 39, 30, 38, 38, 1, 2, 30, 37, 38, 38, 37, 1, 30, 31, 37, 31, 36, 37, 31, 32, 36, 32, 33, 36, 37, 0, 1, 37, 36, 0, 33, 34, 36, 36, 35, 0, 36, 34, 35], "vertices": [1, 44, 26.97, 34.58, 1, 1, 44, 63.12, 33.65, 1, 2, 44, 113.21, 15.57, 0.9375, 45, -15.44, -0.56, 0.0625, 2, 44, 120.42, 12.66, 0.7, 45, -12.82, 6.76, 0.3, 2, 44, 123.33, 4.03, 0.3, 45, -4.3, 10.01, 0.7, 3, 44, 120.41, -57.62, 0.0625, 45, 57.4, 9.51, 0.875, 46, 4.74, 29.58, 0.0625, 2, 45, 65.54, 14.85, 0.7, 46, 11.06, 22.18, 0.3, 2, 45, 73.04, 19.77, 0.3, 46, 16.88, 15.35, 0.7, 4, 45, 76.3, 32.51, 0.12438, 46, 29.92, 13.7, 0.74435, 47, 7.93, 24.02, 0.12104, 48, -40.26, 11.94, 0.01022, 4, 45, 77.14, 35.8, 0.05191, 46, 33.3, 13.28, 0.74749, 47, 8.36, 20.66, 0.15402, 48, -38.77, 8.89, 0.04659, 4, 45, 72.87, 38.65, 0.02194, 46, 35.6, 17.88, 0.63296, 47, 13.4, 19.58, 0.12881, 48, -33.67, 9.48, 0.21629, 3, 46, 43.97, 34.6, 0.0625, 47, 31.68, 15.69, 0.675, 48, -15.09, 11.65, 0.2625, 2, 47, 56.91, 17.86, 0.10381, 48, 8.11, 21.78, 0.89619, 1, 48, 64.07, 6.01, 1, 1, 48, 80.93, -7.91, 1, 2, 47, 45.49, -11.68, 0.01381, 48, 6.76, -9.86, 0.98619, 2, 47, 31.49, -12.91, 0.7, 48, -6.11, -15.51, 0.3, 3, 46, 57.56, -0.67, 0.08333, 47, 0.97, -6.33, 0.83333, 48, -37.13, -19.06, 0.08333, 2, 46, 53.06, -11.77, 0.3, 47, -10.9, -4.76, 0.7, 2, 46, 48.72, -22.45, 0.7, 47, -22.33, -3.26, 0.3, 2, 46, 42.13, -20.34, 0.9375, 47, -21.95, 3.65, 0.0625, 1, 46, 24.59, -14.73, 1, 2, 45, 103.38, 2.68, 0.13333, 46, 3.72, -16.89, 0.86667, 2, 45, 109.82, -10.26, 0.36111, 46, -8.32, -24.91, 0.63889, 2, 45, 104.21, -18.92, 0.72083, 46, -17.61, -20.41, 0.27917, 2, 45, 100.25, -25.01, 0.91667, 46, -24.16, -17.25, 0.08333, 1, 45, 80.56, -18.49, 1, 2, 44, 75.05, -58.38, 0.08333, 45, 59.94, -35.78, 0.91667, 2, 44, 77.5, -35.36, 0.42846, 45, 36.84, -34.24, 0.57154, 2, 44, 75.15, -32.63, 0.35605, 45, 34.22, -36.69, 0.64395, 2, 44, 73.26, -30.43, 0.59148, 45, 32.09, -38.67, 0.40852, 2, 44, 57.56, -37.54, 0.8446, 45, 39.81, -54.06, 0.1554, 1, 44, 31.67, -49.27, 1, 1, 44, 10.89, -47.28, 1, 1, 44, -33.35, -16.02, 1, 1, 44, -3.32, 26.49, 1, 1, 44, 29.08, -3.01, 1, 1, 44, 60.58, 1.22, 1, 2, 44, 94.43, -6.05, 0.96, 45, 6.89, -18.47, 0.04, 2, 44, 98.56, -9.22, 0.7, 45, 9.9, -14.22, 0.3, 2, 44, 102, -14.31, 0.3, 45, 14.85, -10.59, 0.7, 3, 44, 106.76, -68.04, 0.04, 45, 68.33, -3.71, 0.92, 46, -7.01, 17.08, 0.04, 2, 45, 82.16, 0.34, 0.7, 46, -1.26, 3.87, 0.3, 2, 45, 88.99, 10.79, 0.25, 46, 9.96, -1.6, 0.75, 3, 45, 93.78, 36.4, 0.04, 46, 35.97, -3.15, 0.92, 47, -6.86, 13.94, 0.04, 2, 46, 39.83, -1.86, 0.7, 47, -4.64, 10.52, 0.3, 3, 46, 43.99, 3.63, 0.3, 47, 1.72, 7.88, 0.66, 48, -40.98, -5.35, 0.04, 3, 46, 57.08, 30.92, 0.04, 47, 31.42, 2.08, 0.7, 48, -10.98, -1.34, 0.26], "hull": 36, "edges": [66, 68, 66, 64, 56, 54, 54, 52, 52, 50, 46, 44, 44, 42, 34, 32, 32, 30, 30, 28, 28, 26, 26, 24, 24, 22, 10, 8, 8, 6, 6, 4, 4, 2, 2, 0, 68, 70, 0, 70, 46, 48, 48, 50, 14, 12, 12, 10, 60, 58, 58, 56, 42, 40, 40, 38, 18, 16, 16, 14, 22, 20, 20, 18, 38, 36, 36, 34, 60, 62, 62, 64, 68, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 16, 88, 86, 88, 18, 90, 90, 38, 88, 90, 20, 92, 92, 36, 90, 92, 92, 94, 94, 22, 94, 32, 30, 24, 88, 40, 86, 14, 84, 12, 82, 10, 82, 52, 48, 84, 44, 86, 78, 6, 4, 76, 80, 8, 80, 56, 58, 78, 76, 60], "width": 169, "height": 215}}, "raptor_horn": {"raptor_horn": {"x": 78.11, "y": 37.06, "rotation": -137.26, "width": 182, "height": 80}}, "raptor_horn_back": {"raptor_horn_back": {"x": 60.72, "y": 41.51, "rotation": -132.22, "width": 176, "height": 77}}, "raptor_jaw": {"raptor_jaw": {"type": "mesh", "uvs": [0.40985, 0.2217, 0.42215, 0.3988, 0.67895, 0.53819, 0.75891, 0.62838, 0.99999, 0.4726, 1, 0.53491, 0.87731, 0.77925, 0.63281, 0.94488, 0.39908, 0.96947, 0.19457, 0.89404, 0.2161, 0.6497, 0, 0.46112, 0, 0, 0.26069, 0, 0.19457, 0.29385], "triangles": [11, 12, 14, 14, 12, 13, 2, 7, 8, 8, 9, 10, 2, 8, 10, 7, 3, 6, 7, 2, 3, 2, 10, 1, 6, 3, 5, 11, 14, 10, 10, 14, 1, 5, 3, 4, 14, 0, 1, 14, 13, 0], "vertices": [1, 39, 14.3, 34.43, 1, 1, 39, 34.83, 19.48, 1, 1, 39, 75.36, 36.44, 1, 1, 39, 93.08, 37.4, 1, 1, 39, 99.88, 79.85, 1, 1, 39, 106.68, 74.08, 1, 1, 39, 121.21, 37.22, 1, 1, 39, 115.16, -6.54, 1, 1, 39, 94.78, -36, 1, 1, 39, 66.38, -52.81, 1, 1, 39, 41.86, -27.69, 1, 2, 39, -0.03, -35.38, 0.32267, 8, -9.16, 6.05, 0.67733, 1, 8, 56.72, 8.48, 1, 1, 8, 58.19, -31.25, 1, 1, 39, 0.93, 2.72, 1], "hull": 14, "edges": [22, 24, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 0, 24, 26, 0, 26, 24, 28, 22, 28, 28, 0], "width": 153, "height": 143}}, "raptor_saddle_noshadow": {"raptor_saddle_noshadow": {"x": 40.42, "y": 5.32, "rotation": -88.65, "width": 163, "height": 188}}, "raptor_saddle_strap_front": {"raptor_saddle_strap_front": {"x": 64.42, "y": -2.36, "rotation": 61.29, "width": 57, "height": 95}}, "raptor_saddle_strap_rear": {"raptor_saddle_strap_rear": {"type": "mesh", "uvs": [0.855, 0.06803, 1, 0.13237, 1, 0.20266, 0.95982, 0.26524, 0.88584, 0.38045, 0.80684, 0.46413, 0.74038, 0.53454, 0.81676, 0.58951, 0.51962, 1, 0.45161, 1, 0.01739, 0.84071, 0, 0.8089, 0.24646, 0.3664, 0.37921, 0.39151, 0.42457, 0.32099, 0.49229, 0.21571, 0.57673, 0.10986, 0.66437, 0, 0.70169, 0, 0.56029, 0.46322, 0.68822, 0.29773, 0.76846, 0.18722, 0.6153, 0.39206], "triangles": [7, 8, 6, 9, 10, 13, 13, 11, 12, 6, 8, 19, 8, 9, 19, 9, 13, 19, 13, 10, 11, 19, 22, 6, 13, 14, 19, 19, 14, 22, 6, 22, 5, 22, 20, 5, 5, 20, 4, 14, 15, 22, 22, 15, 20, 20, 21, 4, 15, 16, 20, 4, 21, 3, 20, 16, 21, 2, 3, 0, 3, 21, 0, 0, 1, 2, 21, 16, 18, 16, 17, 18, 21, 18, 0], "vertices": [1, 52, 1.96, -1.63, 1, 1, 52, 2.13, 7.53, 1, 1, 52, 6.62, 10.15, 1, 2, 52, 11.72, 10.6, 0.7, 53, -7.6, 10.61, 0.3, 3, 52, 20.56, 11.44, 0.3, 53, 1.25, 11.45, 0.6375, 54, -16.92, 12.48, 0.0625, 3, 52, 26.03, 10.86, 0.0625, 53, 6.72, 10.88, 0.6375, 54, -11.49, 11.56, 0.3, 2, 53, 9.19, 10.38, 0.25, 54, -9.05, 10.91, 0.75, 1, 54, -9.38, 16.55, 1, 1, 54, 24.97, 15.78, 1, 1, 54, 26.6, 12.5, 1, 1, 54, 26.56, -13.74, 1, 1, 54, 24.87, -15.64, 1, 1, 54, -10.37, -18.39, 1, 1, 54, -11.91, -11.14, 1, 3, 52, 26.74, -12.31, 0.0625, 53, 7.43, -12.3, 0.575, 54, -12.26, -11.61, 0.3625, 3, 52, 20.73, -13.06, 0.3, 53, 1.4, -13.05, 0.6375, 54, -18.31, -11.98, 0.0625, 2, 52, 12.19, -13.06, 0.7, 53, -7.12, -13.05, 0.3, 1, 52, 2.79, -13.06, 1, 1, 52, 1.77, -11.32, 1, 1, 54, -11.55, -0.02, 1, 3, 52, 20.83, -0.87, 0.3, 53, 1.51, -0.85, 0.66, 54, -17.42, 0.19, 0.04, 2, 52, 11.93, -1.24, 0.7, 53, -7.39, -1.23, 0.3, 3, 52, 26.3, -0.76, 0.04, 53, 6.97, -0.75, 0.62, 54, -11.97, -0.05, 0.34], "hull": 19, "edges": [26, 24, 24, 22, 22, 20, 20, 18, 16, 18, 16, 14, 14, 12, 4, 2, 34, 36, 12, 38, 38, 26, 8, 40, 40, 30, 2, 0, 0, 36, 30, 32, 32, 34, 32, 42, 4, 6, 6, 8, 42, 6, 26, 28, 28, 30, 28, 44, 8, 10, 10, 12, 44, 10], "width": 54, "height": 74}}, "raptor_saddle_w_shadow": {"raptor_saddle_w_shadow": {"x": 40.42, "y": 5.32, "rotation": -88.65, "width": 163, "height": 188}}, "raptor_tongue": {"raptor_tongue": {"type": "mesh", "uvs": [0.35242, 0.21561, 0.4794, 0.44246, 0.62072, 0.61177, 0.80563, 0.75374, 1, 0.90297, 1, 1, 0.8971, 1, 0.72055, 0.92255, 0.50668, 0.82872, 0.30402, 0.70725, 0.10537, 0.57889, 0, 0.50622, 0, 0, 0.26225, 0], "triangles": [7, 8, 3, 6, 7, 3, 4, 6, 3, 6, 4, 5, 8, 7, 6, 9, 1, 2, 8, 9, 2, 9, 10, 1, 8, 2, 3, 0, 11, 12, 0, 12, 13, 10, 11, 0, 1, 10, 0], "vertices": [2, 63, 1.82, 13.52, 0.6875, 64, -23.64, 16.94, 0.3125, 3, 63, 19.55, 9.73, 0.3125, 64, -6.71, 10.44, 0.625, 65, -25.77, 16.69, 0.0625, 3, 63, 35.78, 9.52, 0.0625, 64, 9.3, 7.7, 0.625, 65, -10.78, 10.46, 0.3125, 2, 64, 27.51, 8.43, 0.3125, 65, 7.14, 7.12, 0.6875, 2, 64, 46.67, 9.2, 0.08333, 65, 25.99, 3.61, 0.91667, 1, 65, 28.05, -2.26, 1, 2, 64, 42.53, -0.75, 0.08333, 65, 19.75, -5.16, 0.91667, 2, 64, 27.11, -4.59, 0.3125, 65, 3.86, -5.49, 0.6875, 3, 63, 37.57, -7.36, 0.0625, 64, 8.44, -9.25, 0.625, 65, -15.39, -5.87, 0.3125, 3, 63, 19.4, -12.9, 0.3125, 64, -10.37, -11.9, 0.625, 65, -34.31, -4.27, 0.0625, 2, 63, 1.2, -17.89, 0.6875, 64, -29.12, -13.99, 0.3125, 2, 63, -8.65, -20.32, 0.91667, 64, -39.23, -14.86, 0.08333, 1, 63, -29.96, 4.09, 1, 2, 63, -13.07, 18.84, 0.91667, 64, -37.51, 24.51, 0.08333], "hull": 14, "edges": [22, 24, 10, 12, 10, 8, 24, 26, 16, 4, 18, 16, 2, 4, 18, 2, 22, 20, 0, 26, 20, 0, 0, 2, 12, 14, 14, 16, 4, 6, 6, 8, 14, 6, 20, 18], "width": 86, "height": 64}}, "spineboy_torso": {"torso": {"x": 27.94, "y": 2.43, "rotation": -104.14, "width": 54, "height": 91}}, "stirrup_back": {"stirrup_back": {"x": 26.6, "y": 15.67, "rotation": -21.13, "width": 44, "height": 35}}, "stirrup_front": {"stirrup_front": {"x": 18.07, "y": 10.19, "rotation": -21.13, "width": 45, "height": 50}}, "stirrup_strap": {"stirrup_strap": {"type": "mesh", "uvs": [0.36823, 0.27894, 0.45738, 0.38897, 0.54452, 0.49652, 0.67872, 0.59135, 0.81977, 0.69102, 1, 0.77344, 1, 1, 0.77957, 1, 0.6373, 0.8163, 0.53364, 0.72349, 0.40534, 0.60861, 0.30886, 0.52535, 0.2105, 0.44048, 0, 0.26245, 0, 0, 0.30637, 0, 0.20242, 0.23001], "triangles": [7, 5, 6, 7, 4, 5, 7, 8, 4, 8, 3, 4, 8, 9, 3, 9, 2, 3, 9, 10, 2, 2, 10, 1, 10, 11, 1, 11, 12, 1, 13, 16, 12, 12, 0, 1, 12, 16, 0, 16, 15, 0, 13, 14, 16, 16, 14, 15], "vertices": [2, 56, 12.36, 4.02, 0.80345, 57, -8.71, 5.51, 0.19655, 2, 56, 18.98, 4.02, 0.59979, 57, -2.18, 4.43, 0.40021, 2, 56, 25.44, 4.03, 0.36895, 57, 4.2, 3.38, 0.63105, 2, 56, 32.96, 6.14, 0.17748, 57, 11.96, 4.24, 0.82252, 2, 56, 40.87, 8.35, 0.05943, 57, 20.12, 5.14, 0.94057, 2, 56, 49.42, 12.52, 0.0121, 57, 29.24, 7.86, 0.9879, 2, 56, 57.22, 5.79, 0.00191, 57, 35.83, -0.05, 0.99809, 2, 56, 50.24, -2.31, 0.01818, 57, 27.63, -6.91, 0.98182, 2, 56, 39.4, -2.07, 0.07488, 57, 16.98, -4.91, 0.92512, 2, 56, 32.92, -3.12, 0.2028, 57, 10.4, -4.89, 0.7972, 2, 56, 24.9, -4.42, 0.39972, 57, 2.28, -4.86, 0.60028, 2, 56, 18.97, -5.49, 0.62658, 57, -3.74, -4.95, 0.37342, 2, 56, 12.93, -6.57, 0.82035, 57, -9.88, -5.03, 0.17965, 2, 56, 0.13, -9.02, 0.95289, 57, -22.91, -5.35, 0.04711, 2, 56, -8.92, -1.22, 0.9771, 57, -30.56, 3.82, 0.0229, 2, 56, 0.79, 10.03, 0.94775, 57, -19.15, 13.34, 0.05225, 2, 56, 5.42, -0.62, 0.9771, 57, -16.32, 2.07, 0.0229], "hull": 16, "edges": [28, 30, 30, 0, 12, 10, 8, 10, 12, 14, 14, 16, 26, 28, 24, 26, 26, 32, 32, 30, 20, 22, 22, 24, 0, 2, 2, 4, 4, 6, 6, 8, 16, 18, 18, 20], "width": 49, "height": 46}}, "visor": {"visor": {"x": 49.56, "y": 3.26, "rotation": -70.58, "width": 131, "height": 84}}}}], "events": {"footstep": {}}, "animations": {"debugfade": {"slots": {"raptor_body": {"rgba": [{"color": "0000803f", "curve": "stepped"}, {"time": 1, "color": "0000803f"}]}}}, "gungrab": {"slots": {"front_hand": {"attachment": [{"name": "front_open_hand"}, {"time": 0.1667, "name": "gun"}]}, "gun": {"attachment": [{"time": 0.1667}]}}, "bones": {"front_hand2": {"rotate": [{}, {"time": 0.1333, "value": 12.34}, {"time": 0.1667, "value": -89.55}, {"time": 0.2333, "value": -79.79}, {"time": 0.4667, "value": -10.18}], "scale": [{}, {"time": 0.1667, "x": 0.938, "y": 0.938}, {"time": 0.4667}]}, "front_arm": {"rotate": [{}, {"time": 0.0667, "value": -32}, {"time": 0.1667, "value": -136.89, "curve": [0.183, -136.89, 0.217, -204.81]}, {"time": 0.2333, "value": -204.81, "curve": [0.267, -204.81, 0.333, -113.86]}, {"time": 0.3667, "value": -113.86, "curve": [0.385, -94.44, 0.442, -56.75]}, {"time": 0.4667, "value": -56.75}], "translate": [{}, {"time": 0.1667, "x": 3.25, "y": -1.33}, {"time": 0.2333, "x": 3.42, "y": 2.4}, {"time": 0.4667}]}, "front_bracer": {"rotate": [{}, {"time": 0.1667, "value": 86.02}, {"time": 0.2333, "value": 114.95}, {"time": 0.3667, "value": 81.86, "curve": [0.392, 81.86, 0.442, 34.74]}, {"time": 0.4667, "value": 34.74}]}}, "ik": {"front_arm_goal": [{"mix": 0}]}}, "gunkeep": {"slots": {"front_hand": {"attachment": [{"name": "gun"}, {"time": 0.4, "name": "front_open_hand"}, {"time": 0.5333, "name": "front_hand"}]}, "gun": {"attachment": [{}, {"time": 0.4, "name": "gun_nohand"}]}}, "bones": {"front_hand2": {"rotate": [{"value": -10.18, "curve": "stepped"}, {"time": 0.1, "value": -10.18}, {"time": 0.3333, "value": -79.79}, {"time": 0.4, "value": -89.55}, {"time": 0.4333, "value": 12.34}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.7}], "scale": [{"time": 0.1}, {"time": 0.4, "x": 0.938, "y": 0.938}, {"time": 0.5667}]}, "front_arm": {"rotate": [{"value": -56.75, "curve": "stepped"}, {"time": 0.1, "value": -56.75}, {"time": 0.2, "value": -113.86, "curve": [0.225, -144.78, 0.3, -204.81]}, {"time": 0.3333, "value": -204.81, "curve": [0.35, -204.81, 0.383, -136.89]}, {"time": 0.4, "value": -136.89, "curve": [0.425, -136.89, 0.475, -32]}, {"time": 0.5, "value": -32}, {"time": 0.5667}], "translate": [{"time": 0.1}, {"time": 0.3333, "x": 3.42, "y": 2.4}, {"time": 0.4, "x": 3.25, "y": -1.33}, {"time": 0.5667}]}, "front_bracer": {"rotate": [{"value": 34.74, "curve": "stepped"}, {"time": 0.1, "value": 34.74}, {"time": 0.2, "value": 81.86, "curve": [0.233, 81.86, 0.3, 114.95]}, {"time": 0.3333, "value": 114.95}, {"time": 0.4, "value": 86.02}, {"time": 0.5667}]}}, "ik": {"front_arm_goal": [{"mix": 0, "curve": "stepped"}, {"time": 0.5667}]}}, "Jump": {"bones": {"root": {"translate": [{"time": 0.3, "curve": [0.374, 133.45, 0.599, 615.35, 0.374, 0, 0.599, 0]}, {"time": 0.6667, "x": 741.39}]}, "front_foot_goal": {"rotate": [{"time": 0.2}, {"time": 0.3, "value": -41.64}, {"time": 0.3667, "value": -69.67}, {"time": 0.4333, "value": -12.81}, {"time": 0.5333, "value": 5.74}, {"time": 0.6667}], "translate": [{"time": 0.3}, {"time": 0.3667, "x": -30, "y": 55.55}, {"time": 0.4333, "x": 106.59, "y": 145.62}, {"time": 0.5333, "x": 121.87, "y": 166.3}, {"time": 0.6667}]}, "hip": {"rotate": [{"value": -4.48}, {"time": 0.1, "value": -23.03}, {"time": 0.3, "value": 19.24}, {"time": 0.5333, "value": 20.85}, {"time": 0.6667, "value": -10.76}, {"time": 0.7667, "value": -18.59}, {"time": 0.9333, "value": -3.56}, {"time": 1.0667, "value": -4.48}], "translate": [{"x": -50.33, "y": 24.89}, {"time": 0.1, "x": 4.69, "y": -54.53, "curve": [0.116, 4.69, 0.141, 34.3, 0.116, -54.53, 0.141, -47.7]}, {"time": 0.1667, "x": 75.18, "y": -38.26, "curve": [0.22, 110.99, 0.281, 167.87, 0.22, -19.01, 0.281, 11.55]}, {"time": 0.3, "x": 180.51, "y": 18.34}, {"time": 0.5333, "x": 2.68, "y": 145.46, "curve": [0.641, 2.68, 0.652, -22.59, 0.641, 145.46, 0.652, 62.3]}, {"time": 0.6667, "x": -28.14, "y": 44.04}, {"time": 0.7667, "x": -64.34, "y": -30.47, "curve": [0.808, -64.34, 0.892, -35.25, 0.808, -30.47, 0.892, 24.83]}, {"time": 0.9333, "x": -35.25, "y": 24.83, "curve": [0.967, -35.25, 1.033, -50.33, 0.967, 24.83, 1.033, 24.89]}, {"time": 1.0667, "x": -50.33, "y": 24.89}]}, "rear_foot_goal": {"rotate": [{"time": 0.2}, {"time": 0.3, "value": -41.64}, {"time": 0.3667, "value": -69.67}, {"time": 0.4333, "value": -57.97}, {"time": 0.7, "value": -9.2}, {"time": 0.7333, "value": -7.79}, {"time": 1.0667}], "translate": [{"time": 0.3}, {"time": 0.3667, "x": -65.83, "y": 23.8}, {"time": 0.4333, "x": -8.06, "y": 102.92}, {"time": 0.5333, "x": 30.65, "y": 160.1}, {"time": 0.7333}]}, "front_leg_goal": {"translate": [{"time": 0.3}, {"time": 0.7, "x": -0.2, "y": 12.15}, {"time": 0.7667}]}, "rear_leg_goal": {"translate": [{"time": 0.3}, {"time": 0.7, "x": 3.38, "y": 12.82}, {"time": 0.7667}]}, "tail1": {"rotate": [{}, {"time": 0.1, "value": -11.02}, {"time": 0.3, "value": 0.53}, {"time": 0.4333, "value": 8.64}, {"time": 0.7, "value": -9.74}, {"time": 0.7667, "value": -4.46, "curve": [0.84, -4.46, 0.961, 0]}, {"time": 1.0667}]}, "tail2": {"rotate": [{}, {"time": 0.1, "value": -39.83}, {"time": 0.3, "value": -31.83}, {"time": 0.4333, "value": -7.28}, {"time": 0.5333, "value": 1.28}, {"time": 0.6, "value": -7.23}, {"time": 0.7, "value": -30.66}, {"time": 0.7667, "value": -40.54, "curve": [0.84, -40.54, 0.961, 0]}, {"time": 1.0667}]}, "torso2": {"rotate": [{}, {"time": 0.1, "value": 0.52}, {"time": 0.3, "value": -1.91}, {"time": 0.5333, "value": 1.04}, {"time": 0.7, "value": -3.26}, {"time": 0.7667, "value": 4.82}, {"time": 1.0667}]}, "front_arm1": {"rotate": [{}, {"time": 0.1667, "value": 51.21}, {"time": 0.3, "value": -38.7}, {"time": 0.5333, "value": 62.19, "curve": "stepped"}, {"time": 0.7, "value": 62.19}, {"time": 0.7667, "value": 34.63}, {"time": 0.8333, "value": -14.43}, {"time": 1.0667}]}, "neck": {"rotate": [{}, {"time": 0.1, "value": -8.26}, {"time": 0.3, "value": -1.91}, {"time": 0.5333, "value": 5.44}, {"time": 0.7, "value": 24.02}, {"time": 0.7667, "value": 4.83}, {"time": 0.8667, "value": -1.79}, {"time": 1.0667}], "translate": [{"time": 0.1}, {"time": 0.3, "x": 31.45, "y": -22.12}, {"time": 0.5333, "x": -2.18, "y": 8.65}, {"time": 0.7}, {"time": 0.7667, "x": 11.65, "y": -21.14}, {"time": 0.8667}]}, "rear_arm1": {"rotate": [{}, {"time": 0.1, "value": 51.21}, {"time": 0.3333, "value": -38.7}, {"time": 0.6, "value": 62.19, "curve": "stepped"}, {"time": 0.7333, "value": 62.19}, {"time": 0.8, "value": 34.63}, {"time": 0.8667, "value": -14.43}, {"time": 1.0667}]}, "spineboy_hip": {"translate": [{}, {"time": 0.1, "x": 17.99, "y": -5.91}, {"time": 0.3, "x": 15.56, "y": -25.19}, {"time": 0.5333, "x": 6.05, "y": -4.02}, {"time": 0.7, "x": 20.85, "y": -9.73}, {"time": 0.8667}]}, "tail3": {"rotate": [{}, {"time": 0.1, "value": -8.97}, {"time": 0.3, "value": -18.39}, {"time": 0.4333, "value": 0.9}, {"time": 0.5333, "value": 11.44}, {"time": 0.6, "value": 17.23}, {"time": 0.7, "value": 4.74}, {"time": 0.7667, "value": -20.69, "curve": [0.815, -20.69, 0.896, -20.41]}, {"time": 0.9667, "value": -20.41, "curve": [1.005, -8.78, 1.04, 0]}, {"time": 1.0667}]}, "front_arm2": {"rotate": [{}, {"time": 0.1, "value": 23.11}, {"time": 0.3, "value": -75.93}, {"time": 0.5333, "value": -1.41}, {"time": 0.7667, "value": 26.87}, {"time": 0.8333, "value": -56.15}, {"time": 1.0667}]}, "gun": {"rotate": [{}, {"time": 0.1, "value": 15.28}, {"time": 0.3, "value": -53.41}, {"time": 0.5667, "value": -63.35}, {"time": 0.7667, "value": -29.92}, {"time": 0.9, "value": 7.24}, {"time": 1, "value": -3.7}, {"time": 1.0667}]}, "head": {"rotate": [{}, {"time": 0.1, "value": 9.94}, {"time": 0.3, "value": -3.77}, {"time": 0.5333, "value": -26.64}, {"time": 0.7, "value": -10.23}, {"time": 0.7667, "value": 21.8}, {"time": 0.8667, "value": 15.37}, {"time": 1.0667}]}, "rear_arm2": {"rotate": [{}, {"time": 0.1, "value": 23.11}, {"time": 0.3, "value": -75.93}, {"time": 0.5333, "value": -1.41}, {"time": 0.7667, "value": 26.87}, {"time": 0.8333, "value": -56.15}, {"time": 1.0667}]}, "spineboy_torso": {"rotate": [{}, {"time": 0.1667, "value": -24.94}, {"time": 0.2333, "value": -20.34}, {"time": 0.5333, "value": -11.2}, {"time": 0.7, "value": 10.49}, {"time": 0.8333, "value": -30.21, "curve": [0.875, -30.21, 0.958, 1.35]}, {"time": 1, "value": 1.35}, {"time": 1.0667}]}, "tail4": {"rotate": [{}, {"time": 0.1, "value": 34.12}, {"time": 0.3, "value": -12.25}, {"time": 0.4333, "value": 11.11}, {"time": 0.5333, "value": 25.19}, {"time": 0.6, "value": 32.5}, {"time": 0.7, "value": 24.41}, {"time": 0.7667, "value": 9.91, "curve": [0.815, 9.91, 0.896, -11.72]}, {"time": 0.9667, "value": -11.72, "curve": [1.005, -5.04, 1.04, 0]}, {"time": 1.0667}]}, "front_foot2": {"rotate": [{"time": 0.3}, {"time": 0.3667, "value": -63.6}, {"time": 0.4333, "value": -80.16}, {"time": 0.5333, "value": -17.48}, {"time": 0.6667, "value": 24.85}, {"time": 0.7667}]}, "front_hand": {"rotate": [{}, {"time": 0.5333, "value": -27.75}, {"time": 0.7667, "value": -27.1}, {"time": 1.0667}]}, "jaw": {"rotate": [{"value": 15.57}, {"time": 0.2333, "value": -0.93}, {"time": 0.5, "value": 20.4}, {"time": 0.7, "value": 18.33}, {"time": 0.7667, "value": 5.17}, {"time": 0.8333, "value": 20.34}, {"time": 1.0667, "value": 15.57}]}, "neck2": {"rotate": [{}, {"time": 0.1, "value": 11.08}, {"time": 0.8333, "value": 8.17}, {"time": 1.0667}]}, "rear_foot2": {"rotate": [{"time": 0.3}, {"time": 0.3667, "value": -87.94}, {"time": 0.4333, "value": -126.75}, {"time": 0.5333, "value": -63.79}, {"time": 0.7, "value": 24.85}, {"time": 0.7667}]}, "rear_hand": {"rotate": [{}, {"time": 0.5333, "value": -27.75}, {"time": 0.7667, "value": -27.1}, {"time": 1.0667}]}, "tail5": {"rotate": [{}, {"time": 0.1, "value": 76.87}, {"time": 0.3, "value": -12.25}, {"time": 0.4333, "value": 10.51}, {"time": 0.5333, "value": 24.82}, {"time": 0.6, "value": 32.22}, {"time": 0.7, "value": 24.41}, {"time": 0.7667, "value": 9.91, "curve": [0.815, 9.91, 0.896, -41.67]}, {"time": 0.9667, "value": -41.67, "curve": [1.005, -17.92, 1.04, 0]}, {"time": 1.0667}]}, "front_foot3": {"rotate": [{"time": 0.3}, {"time": 0.3667, "value": -84.17}, {"time": 0.4333, "value": -127.53}, {"time": 0.5333, "value": -52.16}, {"time": 0.6667, "value": 10.78}, {"time": 0.7667}]}, "head2": {"rotate": [{"value": 15.31}, {"time": 0.1, "value": 29.86}, {"time": 0.2, "value": 22.44}, {"time": 0.3, "value": 12.64}, {"time": 0.4667, "value": 24.86}, {"time": 0.5333, "value": 9.29}, {"time": 0.7, "value": 4.78}, {"time": 0.7667, "value": 37.9}, {"time": 0.8333, "value": 18.88, "curve": [0.843, 22.19, 0.958, 22.97]}, {"time": 1, "value": 22.97}, {"time": 1.0667, "value": 15.31}]}}}, "walk": {"bones": {"front_foot_goal": {"rotate": [{}, {"time": 0.2667, "value": -51.26}, {"time": 0.4, "value": -65.18}, {"time": 0.5333, "value": -76.29}, {"time": 0.8, "value": -76.53}, {"time": 1.0667}], "translate": [{"x": 171.64, "y": 18.25}, {"time": 0.2667, "x": 43.26, "y": 18.5}, {"time": 0.5333, "x": -86.68, "y": 18.71}, {"time": 0.6, "x": -34.08, "y": 70.57}, {"time": 0.7333, "x": 45.9, "y": 119}, {"time": 0.8, "x": 77.95, "y": 95.46}, {"time": 0.9667, "x": 151.64, "y": 47.21}, {"time": 1.0667, "x": 171.64, "y": 18.25}]}, "hip": {"rotate": [{"value": -4.78}, {"time": 0.0667, "value": -3.99}, {"time": 0.2667, "value": -12.5}, {"time": 0.5333, "value": -4.78}, {"time": 0.6, "value": -3.99}, {"time": 0.8, "value": -12.5}, {"time": 1.0667, "value": -4.78}], "translate": [{"x": 80.96, "y": 2.45, "curve": [0.018, 81.56, 0.041, 81.59, 0.018, 0.38, 0.041, 0.27]}, {"time": 0.0667, "x": 82.52, "y": -2.99, "curve": [0.116, 82.59, 0.218, 89.4, 0.116, -2.28, 0.218, 68.26]}, {"time": 0.2667, "x": 89.4, "y": 68.26, "curve": [0.333, 89.4, 0.491, 82.57, 0.333, 68.26, 0.491, 14.95]}, {"time": 0.5333, "x": 80.96, "y": 2.45, "curve": [0.551, 81.56, 0.575, 81.59, 0.551, 0.38, 0.575, 0.27]}, {"time": 0.6, "x": 82.52, "y": -2.99, "curve": [0.649, 82.59, 0.752, 89.4, 0.649, -2.28, 0.752, 68.26]}, {"time": 0.8, "x": 89.4, "y": 68.26, "curve": [0.867, 89.4, 1.029, 82.48, 0.867, 68.26, 1.029, 14.3]}, {"time": 1.0667, "x": 80.96, "y": 2.45}]}, "rear_foot_goal": {"rotate": [{"value": -62.73}, {"time": 0.2667, "value": -107.17}, {"time": 0.4667, "value": -40.52}, {"time": 0.8, "value": -97.16}, {"time": 1.0667, "value": -62.73}], "translate": [{"x": -133.35, "y": -7.74}, {"time": 0.1333, "x": -43.94, "y": 62.42}, {"time": 0.2667, "x": 44.18, "y": 67.03}, {"time": 0.3667, "x": 99.19, "y": 45.33}, {"time": 0.4667, "x": 154.1, "y": -13.21}, {"time": 0.6, "x": 83.53, "y": -13.21}, {"time": 1.0667, "x": -133.35, "y": -7.74}]}, "front_leg1": {"rotate": [{"value": 27.08}, {"time": 1.0667, "value": 31.39}], "translate": [{}, {"time": 0.0667, "x": -0.11, "y": 7.6}, {"time": 0.5333, "x": -0.17, "y": 6.08}, {"time": 0.7333, "x": -2.37, "y": 15.97}, {"time": 1.0667}]}, "front_leg_goal": {"translate": [{"x": -9.02, "y": -1.45}, {"time": 0.4333, "x": -21.1, "y": -44.31}, {"time": 0.5333, "x": -13.65, "y": -21.95}, {"time": 0.7333, "x": -0.76, "y": -47.15}, {"time": 0.8, "x": -12.15, "y": -58.21}, {"time": 1, "x": -20.94, "y": -46.65}, {"time": 1.0667, "x": -9.02, "y": -1.45}]}, "rear_leg1": {"rotate": [{"value": -64.85}, {"time": 1.0667, "value": -45.72}]}, "rear_leg_goal": {"translate": [{"x": -1.02, "y": 7.56}, {"time": 0.2667, "x": 8.74, "y": -75.22}, {"time": 0.4667, "x": -20.1, "y": -40.88}, {"time": 0.5333, "x": -15.85, "y": -41.22}, {"time": 0.8, "x": 1.33, "y": -84.61}, {"time": 0.9333, "x": -8.39, "y": -49.15}, {"time": 1.0667, "x": -1.02, "y": 7.56}]}, "tail1": {"rotate": [{"value": 1.31}, {"time": 0.0667, "value": 4.14}, {"time": 0.3333, "value": -5.78}, {"time": 0.6333, "value": 4.14}, {"time": 0.9, "value": -5.78}, {"time": 1.0667, "value": 1.31}]}, "torso1": {"rotate": [{"value": 7.22}, {"time": 0.2667, "value": 4.2}, {"time": 0.5333, "value": 7.22}, {"time": 0.8, "value": 4.2}, {"time": 1.0667, "value": 7.22}]}, "front_leg2": {"rotate": [{"value": 12.72}, {"time": 1.0667, "value": -2.06}]}, "rear_leg2": {"rotate": [{"value": 27.05}, {"time": 1.0667, "value": 9.93}]}, "saddle": {"rotate": [{"value": -2.52}, {"time": 0.2667, "value": -4.17}, {"time": 0.5333, "value": -3.85}, {"time": 0.8, "value": -3.1}, {"time": 1.0667, "value": -2.52}], "translate": [{"time": 0.2667, "curve": [0.277, -0.01, 0.317, -0.02, 0.277, 0.83, 0.317, 2.96]}, {"time": 0.3333, "x": -0.02, "y": 2.96, "curve": [0.418, -0.02, 0.503, 0, 0.418, 2.96, 0.503, 0.65]}, {"time": 0.5333}, {"time": 0.6, "x": -0.1, "y": -1.18}, {"time": 0.8, "curve": [0.81, -0.01, 0.85, -0.02, 0.81, 0.83, 0.85, 2.96]}, {"time": 0.8667, "x": -0.02, "y": 2.96, "curve": [0.951, -0.02, 1.037, 0, 0.951, 2.96, 1.037, 0.65]}, {"time": 1.0667}]}, "tail2": {"rotate": [{"value": -19.16}, {"time": 0.2333, "value": -11.31}, {"time": 0.5, "value": -9.37}, {"time": 0.7667, "value": -11.31}, {"time": 1.0333, "value": -20.28}, {"time": 1.0667, "value": -19.16}], "scale": [{"x": 0.8}, {"time": 0.2333, "x": 0.9}, {"time": 0.5, "x": 0.8}]}, "torso2": {"rotate": [{"value": 8.6}, {"time": 0.2667, "value": 9.53, "curve": [0.333, 9.53, 0.467, 8.01]}, {"time": 0.5333, "value": 8.01}, {"time": 0.8, "value": 5.48, "curve": [0.867, 5.48, 1, 8.6]}, {"time": 1.0667, "value": 8.6}]}, "front_arm1": {"rotate": [{}, {"time": 0.5, "value": -7.83}, {"time": 1.0667}], "translate": [{"x": 10.32, "y": -3.78}, {"time": 0.5, "x": -1.43, "y": 1.66}, {"time": 0.8, "x": 12.05, "y": -0.74}, {"time": 0.9333, "x": 10.86, "y": -1.86}, {"time": 1.0667, "x": 10.32, "y": -3.78}]}, "front_leg3": {"rotate": [{"value": 1.14}]}, "neck": {"rotate": [{"value": 6.5}, {"time": 0.2667, "value": 12.71}, {"time": 0.5333, "value": 6.5}, {"time": 0.8, "value": 12.71}, {"time": 1.0667, "value": 6.5}], "translate": [{"x": 6.3, "y": -15.65}, {"time": 0.2667, "x": -5.42, "y": -36.15, "curve": [0.321, -5.19, 0.496, 4.78, 0.321, -35.74, 0.496, -18.31]}, {"time": 0.5333, "x": 6.3, "y": -15.65}, {"time": 0.8, "x": -5.42, "y": -36.15, "curve": [0.854, -5.19, 1.03, 4.78, 0.854, -35.74, 1.03, -18.31]}, {"time": 1.0667, "x": 6.3, "y": -15.65}]}, "rear_arm1": {"rotate": [{}, {"time": 0.5, "value": 13.72}, {"time": 1.0667}], "translate": [{}, {"time": 0.5, "x": 5.57, "y": -6.7}, {"time": 1.0667}]}, "rear_leg3": {"rotate": [{"value": -23.19}]}, "tail3": {"rotate": [{"value": -12.46}, {"time": 0.2333, "value": 12.66}, {"time": 0.5, "value": -20.8}, {"time": 0.7667, "value": 12.66}, {"time": 1.0333, "value": -16.05}, {"time": 1.0667, "value": -12.46}], "scale": [{"time": 0.2333}, {"time": 0.5, "x": 0.998}, {"time": 1.0667}]}, "front_arm2": {"rotate": [{}, {"time": 0.5, "value": 22.44}, {"time": 1.0667}]}, "front_foot1": {"rotate": [{"value": -41.33}]}, "head": {"rotate": [{"value": -7.36}, {"time": 0.1333, "value": -12.99, "curve": [0.2, -12.99, 0.333, -6.12]}, {"time": 0.4, "value": -6.12}, {"time": 0.5333, "value": -7.36}, {"time": 0.6667, "value": -12.99, "curve": [0.733, -12.99, 0.867, -6.12]}, {"time": 0.9333, "value": -6.12}, {"time": 1.0667, "value": -7.36}], "translate": [{"x": -1.94, "y": -16.43}, {"time": 0.9333, "x": -1.66, "y": -11.41}, {"time": 1.0667, "x": -1.94, "y": -16.43}]}, "rear_arm2": {"rotate": [{}, {"time": 0.5, "value": -30.21}, {"time": 1.0667}]}, "rear_foot1": {"rotate": [{"value": 2.07}]}, "saddle_strap_rear2": {"rotate": [{"value": -4.45}, {"time": 0.1, "value": -2.67}, {"time": 0.3, "value": -0.35}, {"time": 0.4333, "value": -1.71}, {"time": 0.6333, "value": -2.55}, {"time": 0.9, "value": -0.51}, {"time": 1.0667, "value": -4.45}]}, "stirrup": {"rotate": [{}, {"time": 0.2667, "value": -4.96}, {"time": 0.5333}, {"time": 0.8, "value": -4.96}, {"time": 1.0667}], "translate": [{"x": 4.49, "y": 2.49}, {"time": 0.2667, "x": 2.42, "y": 0.5}, {"time": 0.5333, "x": 3.88, "y": -1.5}, {"time": 0.8, "x": 2.42, "y": 0.5}, {"time": 1.0667, "x": 4.49, "y": 2.49}]}, "tail4": {"rotate": [{"value": 10.25}, {"time": 0.2333, "value": 39.48, "curve": [0.3, 39.48, 0.433, 1.33]}, {"time": 0.5, "value": 1.33}, {"time": 0.7667, "value": 39.48, "curve": [0.944, 39.48, 0.967, 6.08]}, {"time": 1.0333, "value": 6.08}, {"time": 1.0667, "value": 10.25}]}, "front_foot2": {"rotate": [{"value": 44.19}, {"time": 0.0667, "value": 7.88}, {"time": 0.1333, "value": 4.67}, {"time": 0.4, "value": 7.59}, {"time": 0.5333, "value": 8.08}, {"time": 0.6667, "value": -67.33}, {"time": 0.7333, "value": -65.24}, {"time": 1, "value": 42.34}, {"time": 1.0667, "value": 44.19}]}, "front_hand": {"rotate": [{"value": 9.49}, {"time": 0.5, "value": -48.61}, {"time": 1.0667, "value": 9.49}]}, "horn_front": {"translate": [{}, {"time": 0.2667, "x": -3.59, "y": -0.69}, {"time": 0.5333}, {"time": 0.8, "x": -3.59, "y": -0.69}, {"time": 1.0667}]}, "horn_rear": {"translate": [{}, {"time": 0.2667, "x": 6.17, "y": 4.58}, {"time": 0.5333}, {"time": 0.8, "x": 6.17, "y": 4.58}, {"time": 1.0667}]}, "jaw": {"rotate": [{"value": 25.56}, {"time": 0.2, "value": 21.27}, {"time": 0.3333, "value": 21.36}, {"time": 0.6667, "value": 15.61}, {"time": 0.8667, "value": 22.97}, {"time": 1.0667, "value": 25.56}]}, "rear_foot2": {"rotate": [{}, {"time": 0.1333, "value": -82.38}, {"time": 0.2667, "value": -110.31}, {"time": 0.4333, "value": 36.22}, {"time": 0.5333, "value": 2.1}, {"time": 1.0667}]}, "rear_hand": {"rotate": [{"value": -28.89}, {"time": 0.5, "value": 12.2}, {"time": 1.0667, "value": -28.89}]}, "saddle_strap_rear3": {"rotate": [{"value": -1.32}, {"time": 0.1, "value": 0.46}, {"time": 0.3, "value": 2.78}, {"time": 0.4333, "value": 1.42}, {"time": 0.6333, "value": 0.58}, {"time": 0.9, "value": 2.62}, {"time": 1.0667, "value": -1.32}]}, "tail5": {"rotate": [{"value": -26.35, "curve": [0.091, -67.98, 0.153, 46.14]}, {"time": 0.2333, "value": 45.42, "curve": [0.338, 84.48, 0.408, -22.6]}, {"time": 0.5, "value": -21.93, "curve": [0.604, -60.99, 0.674, 46.09]}, {"time": 0.7667, "value": 45.42, "curve": [0.871, 76.25, 0.941, -8.26]}, {"time": 1.0333, "value": -7.73}, {"time": 1.0667, "value": -26.35}], "scale": [{"x": 0.765}, {"time": 0.2333}, {"time": 0.5, "x": 0.765}, {"time": 0.7667}, {"time": 1.0667, "x": 0.765}]}, "tongue1": {"rotate": [{}, {"time": 0.3333, "value": 7.55}, {"time": 0.6667, "value": -1.69}, {"time": 0.9333, "value": 8.11}, {"time": 1.0667}]}, "front_foot3": {"rotate": [{"value": 27.6}, {"time": 0.0667, "value": -5.29}, {"time": 0.1333, "value": -3.94}, {"time": 0.2667, "value": -3.82}, {"time": 0.5333, "value": -5.89}, {"time": 0.6, "value": -21.21}, {"time": 0.6667, "value": -73.64}, {"time": 0.7333, "value": -102.82}, {"time": 0.8333, "value": -41.3}, {"time": 1, "value": 27.6}]}, "tongue2": {"rotate": [{}, {"time": 0.3333, "value": 7.55}, {"time": 0.6667, "value": -1.69}, {"time": 0.9333, "value": 8.11}, {"time": 1.0667}]}, "tongue3": {"rotate": [{}, {"time": 0.3333, "value": 7.55}, {"time": 0.6667, "value": -1.69}, {"time": 0.9333, "value": 8.11}, {"time": 1.0667}]}, "head2": {"rotate": [{"value": 38.6}, {"time": 0.2667, "value": 43.19}, {"time": 0.5333, "value": 38.6}, {"time": 0.8, "value": 43.19}, {"time": 1.0667, "value": 38.6}]}, "neck2": {"rotate": [{"value": 9.65}, {"time": 0.2667, "value": 14.71}, {"time": 0.5333, "value": 9.65}, {"time": 0.8, "value": 14.71}, {"time": 1.0667, "value": 9.65}]}, "spineboy_hip": {"translate": [{"x": 16.27, "y": 0.67, "curve": [0.11, 16.27, 0.233, -1.44, 0.11, 0.67, 0.233, 0.37]}, {"time": 0.2667, "x": -6.44, "y": 0.29, "curve": [0.304, -2.35, 0.467, 16.27, 0.304, 0.36, 0.467, 0.67]}, {"time": 0.5333, "x": 16.27, "y": 0.67, "curve": [0.631, 16.27, 0.765, -2.35, 0.631, 0.67, 0.765, 0.36]}, {"time": 0.8, "x": -6.44, "y": 0.29, "curve": [0.844, -2.58, 1, 16.27, 0.844, 0.35, 1, 0.67]}, {"time": 1.0667, "x": 16.27, "y": 0.67}]}, "spineboy_torso": {"rotate": [{"value": -37.93}, {"time": 0.2667, "value": -29.48, "curve": [0.398, -29.48, 0.467, -37.93]}, {"time": 0.5333, "value": -37.93}, {"time": 0.8, "value": -29.48, "curve": [0.931, -29.48, 1, -37.71]}, {"time": 1.0667, "value": -37.71}]}, "front_arm": {"translate": [{}, {"time": 0.3333, "x": -7.22, "y": -5.51}, {"time": 0.5333}, {"time": 0.8, "x": -7.22, "y": -5.51}, {"time": 1.0667}]}, "gun": {"rotate": [{"value": -11.68, "curve": [0.025, -15.23, 0.048, -17.6]}, {"time": 0.0667, "value": -17.6}, {"time": 0.3333, "value": 14.46, "curve": [0.4, 14.46, 0.533, -24.74]}, {"time": 0.6, "value": -24.74, "curve": [0.687, -24.35, 0.791, 14.46]}, {"time": 0.8667, "value": 14.46, "curve": [0.915, 14.46, 1, -3.05]}, {"time": 1.0667, "value": -11.68}], "translate": [{"x": 0.42, "y": -1.91}, {"time": 0.0667}, {"time": 0.3333, "x": 1.69, "y": -7.64}, {"time": 0.6}, {"time": 0.8667, "x": 1.69, "y": -7.64}, {"time": 1.0667, "x": 0.42, "y": -1.91}]}}, "attachments": {"default": {"raptor_body": {"raptor_body": {"deform": [{}, {"time": 0.2667, "offset": 452, "vertices": [9.66226, 6.07712, -8.39342, 7.73739, -0.31512, 11.41041, 9.05756, 6.94628, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.62274, 0.40576, -1.62274, 0.40576, -1.62274, 0.40576, -1.62274, 0.40576, -1.62274, 0.40576, -1.62274, 0.40576, 0, 0, 0, 0, 0, 0, -1.62274, 0.40576, 0, 0, -1.62274, 0.40576]}, {"time": 0.5333}, {"time": 0.8, "offset": 452, "vertices": [9.66226, 6.07712, -8.39342, 7.73739, -0.31512, 11.41041, 9.05756, 6.94628, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.62274, 0.40576, -1.62274, 0.40576, -1.62274, 0.40576, -1.62274, 0.40576, -1.62274, 0.40576, -1.62274, 0.40576, 0, 0, 0, 0, 0, 0, -1.62274, 0.40576, 0, 0, -1.62274, 0.40576]}, {"time": 1.0667}]}}, "raptor_front_leg": {"raptor_front_leg": {"deform": [{"time": 0.2667}, {"time": 0.5333, "offset": 206, "vertices": [-1.11804, 10.97701, -10.52314, 3.3174, 10.77458, -2.3777]}, {"time": 0.6, "offset": 206, "vertices": [3.58981, 7.57179, -8.37008, -0.40027, 7.63262, -3.45871]}, {"time": 0.7333, "offset": 160, "vertices": [-0.41243, 0.36704, -0.00642, -0.55221, -0.13748, 0.53003, -0.64028, 0.19816, 0, 0, 0, 0, 0, 0, 0, 0, 0.7433, -1.29713, 0.49036, 1.41172, -0.05063, -1.49312, 1.47504, -1.05834, 1.3683, -5.24968, 3.06253, 4.47641, -1.44046, -5.17512, -1.30437, -1.14192, 1.71709, -0.23523, -1.14153, -2.38019, 2.54447, 0.70039, -2.29261, -1.30733, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.68984, -0.87758, -1.06615, -0.32834, -1.4312, -0.38682, 1.22995, -0.8266, -1.25796, 0.43206]}, {"time": 0.8}]}}}}, "events": [{"time": 0.0333, "name": "footstep"}, {"time": 0.5333, "name": "footstep"}]}}}