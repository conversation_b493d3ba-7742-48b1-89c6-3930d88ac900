
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using LuBan.Runtime;
using GameFrameX.Config;
using SimpleJSON;

namespace Hotfix.Config
{
    public sealed partial class WeaponConfig : LuBan.Runtime.BeanBase
    {
        public WeaponConfig(int Id, System.Collections.Generic.List<int> Param1, EFireMode EFireMode, int ModelId, int Damage, int FiringRate, int MagazineCapacity, int ReloadTime, int BulletSpeed, int Range, int CriticalDamageMultiplier, int AimDuration, int MovementSpeedMultiplier, int AimMovementSpeedMultiplier, int HipFireSpread, int AimSpread, int VerticalRecoil, int HorizontalRecoil) 
        {
            this.Id = Id;
            this.Param1 = Param1;
            this.EFireMode = EFireMode;
            this.ModelId = ModelId;
            this.Damage = Damage;
            this.FiringRate = FiringRate;
            this.MagazineCapacity = MagazineCapacity;
            this.ReloadTime = ReloadTime;
            this.BulletSpeed = BulletSpeed;
            this.Range = Range;
            this.CriticalDamageMultiplier = CriticalDamageMultiplier;
            this.AimDuration = AimDuration;
            this.MovementSpeedMultiplier = MovementSpeedMultiplier;
            this.AimMovementSpeedMultiplier = AimMovementSpeedMultiplier;
            this.HipFireSpread = HipFireSpread;
            this.AimSpread = AimSpread;
            this.VerticalRecoil = VerticalRecoil;
            this.HorizontalRecoil = HorizontalRecoil;
            PostInit();
        }

        public WeaponConfig(JSONNode _buf)
        {
            { if(!_buf["Id"].IsNumber) { throw new SerializationException(); }  Id = _buf["Id"]; }
            { var __json0 = _buf["Param1"]; if(!__json0.IsArray) { throw new SerializationException(); } Param1 = new System.Collections.Generic.List<int>(__json0.Count); foreach(JSONNode __e0 in __json0.Children) { int __v0;  { if(!__e0.IsNumber) { throw new SerializationException(); }  __v0 = __e0; }  Param1.Add(__v0); }   }
            { if(!_buf["EFireMode"].IsNumber) { throw new SerializationException(); }  EFireMode = (EFireMode)_buf["EFireMode"].AsInt; }
            { if(!_buf["ModelId"].IsNumber) { throw new SerializationException(); }  ModelId = _buf["ModelId"]; }
            { if(!_buf["Damage"].IsNumber) { throw new SerializationException(); }  Damage = _buf["Damage"]; }
            { if(!_buf["FiringRate"].IsNumber) { throw new SerializationException(); }  FiringRate = _buf["FiringRate"]; }
            { if(!_buf["MagazineCapacity"].IsNumber) { throw new SerializationException(); }  MagazineCapacity = _buf["MagazineCapacity"]; }
            { if(!_buf["ReloadTime"].IsNumber) { throw new SerializationException(); }  ReloadTime = _buf["ReloadTime"]; }
            { if(!_buf["BulletSpeed"].IsNumber) { throw new SerializationException(); }  BulletSpeed = _buf["BulletSpeed"]; }
            { if(!_buf["Range"].IsNumber) { throw new SerializationException(); }  Range = _buf["Range"]; }
            { if(!_buf["CriticalDamageMultiplier"].IsNumber) { throw new SerializationException(); }  CriticalDamageMultiplier = _buf["CriticalDamageMultiplier"]; }
            { if(!_buf["AimDuration"].IsNumber) { throw new SerializationException(); }  AimDuration = _buf["AimDuration"]; }
            { if(!_buf["MovementSpeedMultiplier"].IsNumber) { throw new SerializationException(); }  MovementSpeedMultiplier = _buf["MovementSpeedMultiplier"]; }
            { if(!_buf["AimMovementSpeedMultiplier"].IsNumber) { throw new SerializationException(); }  AimMovementSpeedMultiplier = _buf["AimMovementSpeedMultiplier"]; }
            { if(!_buf["HipFireSpread"].IsNumber) { throw new SerializationException(); }  HipFireSpread = _buf["HipFireSpread"]; }
            { if(!_buf["AimSpread"].IsNumber) { throw new SerializationException(); }  AimSpread = _buf["AimSpread"]; }
            { if(!_buf["VerticalRecoil"].IsNumber) { throw new SerializationException(); }  VerticalRecoil = _buf["VerticalRecoil"]; }
            { if(!_buf["HorizontalRecoil"].IsNumber) { throw new SerializationException(); }  HorizontalRecoil = _buf["HorizontalRecoil"]; }

            // Localization Key Begin
            // Localization Key End
            PostInit();
        }

        public static WeaponConfig DeserializeWeaponConfig(JSONNode _buf)
        {
            return new WeaponConfig(_buf);
        }

        /// <summary>
        /// id
        /// </summary>
        public int Id { private set; get; }
        /// <summary>
        /// 参数
        /// </summary>
        public System.Collections.Generic.List<int> Param1 { private set; get; }
        /// <summary>
        /// 发射类型
        /// </summary>
        public EFireMode EFireMode { private set; get; }
        /// <summary>
        /// 穿在身上显示什么模型Id
        /// </summary>
        public int ModelId { private set; get; }
        /// <summary>
        /// 能造成多少伤害(最终伤害 = damage+子弹伤害)
        /// </summary>
        public int Damage { private set; get; }
        /// <summary>
        /// 射速（每秒发射几颗）
        /// </summary>
        public int FiringRate { private set; get; }
        /// <summary>
        /// 弹夹容量
        /// </summary>
        public int MagazineCapacity { private set; get; }
        /// <summary>
        /// 换弹时间
        /// </summary>
        public int ReloadTime { private set; get; }
        /// <summary>
        /// 子弹速度
        /// </summary>
        public int BulletSpeed { private set; get; }
        /// <summary>
        /// 射程
        /// </summary>
        public int Range { private set; get; }
        /// <summary>
        /// 暴击伤害倍率
        /// </summary>
        public int CriticalDamageMultiplier { private set; get; }
        /// <summary>
        /// 瞄准时间
        /// </summary>
        public int AimDuration { private set; get; }
        /// <summary>
        /// 移动速度系数
        /// </summary>
        public int MovementSpeedMultiplier { private set; get; }
        /// <summary>
        /// 瞄准移动系数
        /// </summary>
        public int AimMovementSpeedMultiplier { private set; get; }
        /// <summary>
        /// 腰射散布
        /// </summary>
        public int HipFireSpread { private set; get; }
        /// <summary>
        /// 瞄准散布
        /// </summary>
        public int AimSpread { private set; get; }
        /// <summary>
        /// 垂直后坐力
        /// </summary>
        public int VerticalRecoil { private set; get; }
        /// <summary>
        /// 水平后坐力
        /// </summary>
        public int HorizontalRecoil { private set; get; }
        public const int __ID__ = 127041694;
        public override int GetTypeId() => __ID__;

        public  void ResolveRef(TablesComponent tables)
        {
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
            
        }

        public void TranslateText(System.Func<string, string, string> translator)
        {
        }

        public override string ToString()
        {
            return "{ "
            + "Id:" + Id + ","
            + "Param1:" + StringUtil.CollectionToString(Param1) + ","
            + "EFireMode:" + EFireMode + ","
            + "ModelId:" + ModelId + ","
            + "Damage:" + Damage + ","
            + "FiringRate:" + FiringRate + ","
            + "MagazineCapacity:" + MagazineCapacity + ","
            + "ReloadTime:" + ReloadTime + ","
            + "BulletSpeed:" + BulletSpeed + ","
            + "Range:" + Range + ","
            + "CriticalDamageMultiplier:" + CriticalDamageMultiplier + ","
            + "AimDuration:" + AimDuration + ","
            + "MovementSpeedMultiplier:" + MovementSpeedMultiplier + ","
            + "AimMovementSpeedMultiplier:" + AimMovementSpeedMultiplier + ","
            + "HipFireSpread:" + HipFireSpread + ","
            + "AimSpread:" + AimSpread + ","
            + "VerticalRecoil:" + VerticalRecoil + ","
            + "HorizontalRecoil:" + HorizontalRecoil + ","
            + "}";
        }

        partial void PostInit();
    }
}
