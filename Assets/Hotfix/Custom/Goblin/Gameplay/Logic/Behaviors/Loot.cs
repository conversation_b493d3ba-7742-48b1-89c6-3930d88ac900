using System.Collections.Generic;
using GameFrameX.Runtime;
using Goblin.Gameplay.BehaviorInfos;
using Goblin.Gameplay.BehaviorInfos.Flows;
using Goblin.Gameplay.Logic.BehaviorInfos;
using Goblin.Gameplay.Logic.BehaviorInfos.Collisions;
using Goblin.Gameplay.Logic.Common;
using Goblin.Gameplay.Logic.Common.Defines;
using Goblin.Gameplay.Logic.Core;
using Kowtow.Math;

namespace Goblin.Gameplay.Logic.Behaviors
{
    /// <summary>
    /// 战利品system
    /// </summary>
    public class Loot : Behavior
    {
        protected override void OnTick(FP tick)
        {
            base.OnTick(tick);
            if (false == stage.SeekBehaviorInfos(out List<LootInfo> lootInfos)) return;
            foreach (var lootInfo in lootInfos) Execute(lootInfo);
            lootInfos.Clear();
            ObjectCache.Set(lootInfos);
        }

        /// <summary>
        /// 执行战利品行为
        /// </summary>
        /// <param name="bullet">子弹信息</param>
        private void Execute(LootInfo lootInfo)
        {
            if (stage.SeekBehaviorInfo(stage.sa, out DetectionInfo detectionInfo))
            {
                if (detectionInfo.collisions.TryGetValue(lootInfo.id, out var collisioninfo))
                {
                    if (collisioninfo.Count > 0)
                    {
                        foreach (var actorId in collisioninfo)
                        {
                            if (stage.SeekBehavior(actorId, out Tag tag) && tag.Get(TAG_DEFINE.ACTOR_TYPE, out var val))
                            {
                                if (val == ACTOR_DEFINE.HERO)
                                {
                                    stage.OnLootCollected(actorId,lootInfo);            
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}