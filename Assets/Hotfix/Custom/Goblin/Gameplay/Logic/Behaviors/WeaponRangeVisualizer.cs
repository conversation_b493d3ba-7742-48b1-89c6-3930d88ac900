using System.Collections.Generic;
using GameFrameX.Runtime;
using Goblin.Gameplay.Logic.BehaviorInfos;
using Goblin.Gameplay.Logic.Core;
using Hotfix.Config;
using Kowtow.Math;
using UnityEngine;

namespace Goblin.Gameplay.Logic.Behaviors
{
    /// <summary>
    /// 武器攻击范围可视化组件
    /// </summary>
    public class WeaponRangeVisualizer : Behavior
    {
        /// <summary>
        /// 是否显示攻击范围
        /// </summary>
        public bool showRange = true;
        
        /// <summary>
        /// 攻击范围圆形颜色
        /// </summary>
        public Color rangeColor = new Color(1f, 0f, 0f, 0.3f); // 半透明红色
        
        /// <summary>
        /// 攻击范围边框颜色
        /// </summary>
        public Color rangeBorderColor = Color.red;
        
        /// <summary>
        /// 圆形分段数（越高越圆滑）
        /// </summary>
        public int circleSegments = 64;

        protected override void OnAssemble()
        {
            base.OnAssemble();
            // 注册到 GameManager 的 Gizmos 绘制事件
            GameManager.Instance.AddOnDrawGizmosListener(OnDrawGizmos);
        }

        protected override void OnDisassemble()
        {
            base.OnDisassemble();
            // 这里应该移除监听器，但当前 GameManager 没有提供移除方法
            // 在实际项目中可能需要扩展 GameManager 来支持移除监听器
        }

        /// <summary>
        /// 绘制 Gizmos
        /// </summary>
        private void OnDrawGizmos()
        {
            if (!showRange) return;
            
            // 获取所有武器信息
            if (false == stage.SeekBehaviorInfos(out List<WeaponInfo> weaponInfos)) return;
            
            foreach (var weaponInfo in weaponInfos)
            {
                DrawWeaponRange(weaponInfo);
            }
            
            weaponInfos.Clear();
            ObjectCache.Set(weaponInfos);
        }

        /// <summary>
        /// 绘制单个武器的攻击范围
        /// </summary>
        /// <param name="weaponInfo">武器信息</param>
        private void DrawWeaponRange(WeaponInfo weaponInfo)
        {
            // 获取武器配置
            WeaponConfig weaponConfig = GameApp.Config.GetConfig<TbWeapon>().Get(weaponInfo.weapon);
            if (weaponConfig == null) return;
            
            // 获取武器位置
            if (false == stage.SeekBehaviorInfo<SpatialInfo>(weaponInfo.id, out var spatialInfo)) return;
            
            // 转换位置到 Unity 坐标系
            Vector3 weaponPosition = new Vector3((float)spatialInfo.position.x, (float)spatialInfo.position.y, (float)spatialInfo.position.z);
            float range = weaponConfig.Range;
            
            // 绘制攻击范围圆形
            DrawRangeCircle(weaponPosition, range);
            
            // 绘制当前目标连线（如果有目标）
            if (weaponInfo.target > 0)
            {
                DrawTargetLine(weaponInfo, spatialInfo);
            }
        }

        /// <summary>
        /// 绘制攻击范围圆形
        /// </summary>
        /// <param name="center">圆心位置</param>
        /// <param name="radius">半径</param>
        private void DrawRangeCircle(Vector3 center, float radius)
        {
            // 绘制填充圆形
            Gizmos.color = rangeColor;
            DrawFilledCircle(center, radius);
            
            // 绘制圆形边框
            Gizmos.color = rangeBorderColor;
            DrawWireCircle(center, radius);
        }

        /// <summary>
        /// 绘制填充圆形
        /// </summary>
        /// <param name="center">圆心</param>
        /// <param name="radius">半径</param>
        private void DrawFilledCircle(Vector3 center, float radius)
        {
            // 使用三角形扇形来绘制填充圆形
            Vector3[] vertices = new Vector3[circleSegments + 1];
            vertices[0] = center; // 圆心
            
            for (int i = 0; i <= circleSegments; i++)
            {
                float angle = (float)i / circleSegments * 2f * Mathf.PI;
                vertices[i] = center + new Vector3(Mathf.Cos(angle) * radius, Mathf.Sin(angle) * radius, 0);
            }
            
            // 绘制三角形扇形
            for (int i = 1; i < circleSegments; i++)
            {
                Gizmos.DrawLine(center, vertices[i]);
                Gizmos.DrawLine(vertices[i], vertices[i + 1]);
                Gizmos.DrawLine(vertices[i + 1], center);
            }
        }

        /// <summary>
        /// 绘制圆形边框
        /// </summary>
        /// <param name="center">圆心</param>
        /// <param name="radius">半径</param>
        private void DrawWireCircle(Vector3 center, float radius)
        {
            Vector3 prevPoint = center + new Vector3(radius, 0, 0);
            
            for (int i = 1; i <= circleSegments; i++)
            {
                float angle = (float)i / circleSegments * 2f * Mathf.PI;
                Vector3 currentPoint = center + new Vector3(Mathf.Cos(angle) * radius, Mathf.Sin(angle) * radius, 0);
                Gizmos.DrawLine(prevPoint, currentPoint);
                prevPoint = currentPoint;
            }
        }

        /// <summary>
        /// 绘制到目标的连线
        /// </summary>
        /// <param name="weaponInfo">武器信息</param>
        /// <param name="weaponSpatial">武器空间信息</param>
        private void DrawTargetLine(WeaponInfo weaponInfo, SpatialInfo weaponSpatial)
        {
            if (false == stage.SeekBehaviorInfo<SpatialInfo>(weaponInfo.target, out var targetSpatial)) return;
            
            Vector3 weaponPos = new Vector3((float)weaponSpatial.position.x, (float)weaponSpatial.position.y, (float)weaponSpatial.position.z);
            Vector3 targetPos = new Vector3((float)targetSpatial.position.x, (float)targetSpatial.position.y, (float)targetSpatial.position.z);
            
            // 绘制瞄准线
            Gizmos.color = Color.yellow;
            Gizmos.DrawLine(weaponPos, targetPos);
            
            // 在目标位置绘制一个小圆圈
            Gizmos.color = Color.red;
            DrawWireCircle(targetPos, 0.5f);
        }
    }
}
