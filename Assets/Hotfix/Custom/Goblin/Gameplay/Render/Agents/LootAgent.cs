using System.Collections.Generic;
using System.Threading.Tasks;
using Goblin.Common;
using Goblin.Gameplay.Logic.Common.Defines;
using Goblin.Gameplay.Logic.RIL;
using Goblin.Gameplay.Render.Core;
using Hotfix.Config;
using UnityEngine;

namespace Goblin.Gameplay.Render.Agents
{
    /// <summary>
    /// 模型代理
    /// </summary>
    public class LootAgent : Agent
    {
        /// <summary>
        /// 模型节池
        /// </summary>
        private static GameObject lootPool = new("LootPool");
        static LootAgent()
        {
            lootPool.transform.SetParent(GameObject.Find("Gameplay").transform, false);
            lootPool.transform.localPosition = Vector3.zero;
            lootPool.transform.localScale = Vector3.one;
            lootPool.SetActive(false);
        }
        
        /// <summary>
        /// 模型 ID
        /// </summary>
        public int itemId { get; private set; } = 0;
        /// <summary>
        /// 资源名
        /// </summary>
        public string res { get; private set; }
        /// <summary>
        /// 模型 GameObject
        /// </summary>
        public GameObject go { get; private set; }
        
        public Transform weaponAttachment { get; private set; }
        
        protected override void OnReady()
        {
            RecycleModel();
            itemId = 0;
            res = null;
            go = null;
        }

        protected override void OnReset()
        {
            RecycleModel();
            itemId = 0;
            res = null;
            go = null;
        }
        
        /// <summary>
        /// 回收模型 Go
        /// </summary>
        private void RecycleModel()
        {
            if (null != go)
            {
                ObjectPool.Set(go, $"Loot_GO_KEY_{res}");
                go.transform.SetParent(lootPool.transform, false);
            }
        }

        protected override void OnChase(float tick, float timescale)
        {
            base.OnChase(tick, timescale);
            Load();
        }

        /// <summary>
        /// 加载模型
        /// </summary>
        public async Task Load()
        {
            if (false == world.rilbucket.SeekRIL(actor, out RIL_LOOT ril) || 0 >= ril.itemId)
            {
                RecycleModel();
                return;
            }
            
            if (itemId == ril.itemId) return;
            
            RecycleModel();
            itemId = ril.itemId;
            ItemConfig itemconfig = GameApp.Config.GetConfig<TbItem>().Get(ril.itemId);
            res = "Loot/Loot0";
            go = ObjectPool.Get<GameObject>($"Loot_GO_KEY_{res}");
            if (null == go) go = await world.engine.u3dkit.LoadGameplayPrefabAsync(res);
            var node = world.EnsureAgent<NodeAgent>(actor);
            
            SpriteRenderer sr = go.transform.Find("Model").GetComponent<SpriteRenderer>();
            if (sr != null)
            {
                world.engine.u3dkit.SetSprite(sr,itemconfig.Icon);
            }
            go.transform.SetParent(node.go.transform, false);
            go.transform.localPosition = Vector3.zero;
            go.transform.localRotation = Quaternion.identity;
            go.transform.localScale = Vector3.one;
        }
    }
}